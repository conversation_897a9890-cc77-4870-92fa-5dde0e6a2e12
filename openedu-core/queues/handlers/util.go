package handlers

import (
	"openedu-core/pkg/log"
	"openedu-core/pkg/queue/consumer"
)

func handleError(workerName string, msg consumer.Message, err error) {
	if rErr := msg.Reject(); rErr != nil {
		log.Errorf("%s failed to reject message: %v", workerName, rErr)
	}
	log.Errorf("%s failed to process message: %v", workerName, err)
}

func handleSuccess(workerName string, msg consumer.Message) {
	if err := msg.Ack(); err != nil {
		log.Errorf("%s failed to acknowledge message ID %s: %v", workerName, msg.GetID(), err)
	}
}
