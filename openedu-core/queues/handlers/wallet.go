package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/queue/consumer"
	"openedu-core/services"
)

func SyncWallet(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", SyncWalletWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			if !ok {
				// Channel closed, stop the worker.
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.WalletSyncRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleError(workerName, msg, err)
				continue
			}

			if _, appErr := services.Wallet.Sync(&req); appErr != nil {
				handleError(workerName, msg, appErr)
				continue
			}

			handleSuccess(workerName, msg)
		}
	}
}

func HandleRetrieveWalletDetails(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", HandleRetrieveWalletDetailsWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			if !ok {
				// Channel closed, stop the worker.
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.RetrieveWalletDetailsRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleError(workerName, msg, err)
				continue
			}

			switch req.Type {
			case models.RetrieveWalletDetailsTypeEarnings:
				var retrieveEarningsReq dto.RetrieveWalletEarningsRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleError(workerName, msg, err)
					continue
				}

				if err = json.Unmarshal(b, &retrieveEarningsReq); err != nil {
					handleError(workerName, msg, err)
					continue
				}

				if err := models.Cache.WalletEarning.SetByUser(retrieveEarningsReq.UserID, lo.Map(retrieveEarningsReq.Earnings, func(item *dto.WalletEarningsRequest, index int) interface{} {
					return &models.WalletEarning{
						Address:  item.Address,
						Currency: item.Currency,
						Amount:   item.Amount,
					}
				})); err != nil {
					log.Errorf("WalletService::getEarningsByUser Set wallet earnings cache error: %v", err)
				}

			default:
				appErr := e.NewError400(e.WalletInvalidRetrieveDetailsType, "Invalid retrieve details type: "+string(req.Type))
				handleError(workerName, msg, appErr)
			}

			handleSuccess(workerName, msg)
		}
	}
}

func SyncSponsorWallet(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", SyncSponsorWalletWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			if !ok {
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.SponsorWalletSyncRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleError(workerName, msg, err)
				continue
			}

			if _, appErr := services.Wallet.SyncSponsorWallet(&req); appErr != nil {
				handleError(workerName, msg, appErr)
				continue
			}

			handleSuccess(workerName, msg)
		}
	}
}
