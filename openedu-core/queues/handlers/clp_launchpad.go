package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"openedu-core/dto"
	"openedu-core/pkg/log"
	"openedu-core/pkg/queue/consumer"
	"openedu-core/services"
)

func SyncClpLaunchpadJob(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", SyncClpLaunchpadJobWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			if !ok {
				// Channel closed, stop the worker.
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.SchedulerClpLaunchpadRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleError(workerName, msg, err)
				continue
			}

			if appErr := services.ClpLaunchpad.SyncJobForClpLaunchpad(&req); appErr != nil {
				handleError(workerName, msg, appErr)
				continue
			}

			handleSuccess(workerName, msg)
		}
	}
}
