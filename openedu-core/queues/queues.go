package queues

import (
	"context"
	"openedu-core/pkg/log"
	"openedu-core/pkg/queue/consumer"
	"openedu-core/pkg/setting"
	"openedu-core/queues/handlers"
)

const (
	WalletSyncQueueName      = "wallet_sync_queue"
	WalletSyncWorkerPoolSize = 30

	WalletRetrieveDetailsQueueName      = "wallet_retrieve_get_details_queue"
	WalletRetrieveDetailsWorkerPoolSize = 30

	TransactionSyncQueueName      = "transaction_sync_queue"
	TransactionSyncWorkerPoolSize = 50

	ClpLaunchpadJobSyncQueueName      = "clp_launchpad_job_sync_queue"
	ClpLaunchpadJobSyncWorkerPoolSize = 30
)

var (
	queueConsumer consumer.Consumer
	stopChan      = make(chan struct{})
)

func Consume() error {
	var err error
	prefix := setting.RabbitMQSetting.Prefix
	queueConsumer, err = consumer.NewConsumer(consumer.RabbitMQ)
	if err != nil {
		return err
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	syncWalletMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+WalletSyncQueueName)
	if err != nil {
		return err
	}
	for i := 0; i < WalletSyncWorkerPoolSize; i++ {
		go handlers.SyncWallet(ctx, i, syncWalletMsgChan)
	}

	walletRetrieveDetailsMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+WalletRetrieveDetailsQueueName)
	if err != nil {
		return err
	}
	for i := 0; i < WalletRetrieveDetailsWorkerPoolSize; i++ {
		go handlers.HandleRetrieveWalletDetails(ctx, i, walletRetrieveDetailsMsgChan)
	}

	syncTxMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+TransactionSyncQueueName)
	if err != nil {
		return err
	}
	for i := 0; i < TransactionSyncWorkerPoolSize; i++ {
		go handlers.SyncTransaction(ctx, i, syncTxMsgChan)
	}

	syncClpMsgChan, err := queueConsumer.FetchMessage(ctx, prefix+ClpLaunchpadJobSyncQueueName)
	if err != nil {
		return err
	}
	for i := 0; i < ClpLaunchpadJobSyncWorkerPoolSize; i++ {
		go handlers.SyncClpLaunchpadJob(ctx, i, syncClpMsgChan)
	}
	
	select {
	case <-stopChan:
		return nil
	}
}

// Stop method to gracefully stop the workers and close the consumer
func Stop() error {
	log.Infof("Stopping queue consumers...")
	stopChan <- struct{}{}
	if queueConsumer != nil {
		return queueConsumer.Close()
	}
	return nil
}
