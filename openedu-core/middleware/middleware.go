package middleware

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"strings"

	"github.com/golang-jwt/jwt/v5"
	"github.com/samber/lo"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type headerData struct {
	Token string
	//OrgId    string
	Origin   string
	XReferer string
	ApiKey   string
}

func extractHeaderData(c *gin.Context) *headerData {
	token := ""
	sePayApiKey := ""
	headerApiKey := c.Request.Header.Get(util.HeaderAPIKey)
	authHeader := c.Request.Header.Get("Authorization")
	if authHeader != "" {
		authHeaderParts := strings.Split(authHeader, " ")
		if len(authHeaderParts) == 2 && (authHeaderParts[0] == "Bearer" || authHeaderParts[0] == "bearer") {
			token = authHeaderParts[1]
		}

		if len(authHeaderParts) == 2 && authHeaderParts[0] == "Apikey" {
			sePayApiKey = authHeaderParts[1]
		}
	}

	return &headerData{
		//OrgId:    c.Request.Header.Get(util.HeaderOrgKey),
		Token:    token,
		Origin:   c.Request.Header.Get(util.HeaderOriginKey),
		XReferer: c.Request.Header.Get(util.HeaderXReferrer),
		ApiKey:   lo.If(headerApiKey != "", headerApiKey).Else(sePayApiKey),
	}
}

// extract organization
// Super admin: admin.openedu101.com
// Org management: *.openedu101.com/admin
// Instructor management: *.openedu101.com/instructor
// Leaner dashboard: *.openedu101.com/learner
// Normal page: *.openedu101.com
// Function to extract domain and URI from a given URL
func extractDomainAndURI(rawURL string) (*app.AppDomain, error) {
	// Split the domain and URI
	parts := strings.SplitN(rawURL, "/", 2)
	domain := parts[0]
	uri := "/"
	if len(parts) > 1 {
		uri = "/" + parts[1]
	}

	subdomain := domain
	// Check if the domain ends with the base domain
	if strings.HasSuffix(domain, setting.AppSetting.BaseDomain) {
		// Extract subdomain
		subdomain = strings.TrimSuffix(domain, "."+setting.AppSetting.BaseDomain)
		if domain == setting.AppSetting.BaseDomain {
			subdomain = ""
		}
	}

	return &app.AppDomain{
		Path:      rawURL,
		Domain:    domain,
		Uri:       uri,
		Subdomain: subdomain,
	}, nil
}

func getRoleAccesses(c *gin.Context) ([]*models.RoleAccess, error) {
	method := c.Request.Method
	path := c.FullPath()
	controllerName := c.HandlerName()
	roleAccesses, err := models.Repository.Permission.FindAccesses(method, path, controllerName)
	if err != nil {
		return nil, err
	}

	return roleAccesses, nil
}

// BeforeInterceptor is a middleware function that verify JWT authentication.
func BeforeInterceptor() gin.HandlerFunc {
	return func(c *gin.Context) {
		appG := app.Gin{C: c}
		ctx := c.Request.Context()
		var (
			code          int
			domainInToken string
			user          *models.User
			userRoleOrgs  []*models.UserRoleOrg
			organization  *models.Organization
		)

		code = e.SUCCESS
		headers := extractHeaderData(c)
		if headers.ApiKey != "" {
			if headers.ApiKey == setting.AppSetting.ApiKey {
				c.Request = c.Request.WithContext(ctx)
				c.Next()
				return
			}

			appG.Response401(e.Error_auth_invalid_api_key, "Invalid api key")
			c.Abort()
			return
		}

		if headers.Origin == "" {
			appG.Response401(e.Error_auth_missing_headers_domain, "Origin required")
			c.Abort()
			return
		}

		if headers.XReferer == "" {
			appG.Response403(e.FORBIDDEN, fmt.Sprintf("Header %s required", util.HeaderXReferrer))
			c.Abort()
			return
		}

		domain, eErr := extractDomainAndURI(headers.XReferer)
		if eErr != nil {
			appG.Response403(e.FORBIDDEN, fmt.Sprintf("Invalid %s: %s", util.HeaderXReferrer, eErr.Error()))
			c.Abort()
			return
		}
		// Set request domain
		c.Set(util.ContextDomainInfo, domain)
		ctx = context.WithValue(ctx, util.ContextDomainInfo, domain)

		roleAccesses, perErr := getRoleAccesses(c)
		if perErr != nil {
			appG.Response500(e.Error_auth_invalid_permission, "Get role accesses: "+perErr.Error())
			c.Abort()
			return
		}
		if len(roleAccesses) <= 0 {
			appG.Response403(e.Error_auth_permission_not_found, "Method doesn't exist")
			c.Abort()
			return
		}

		isPublicAccess := false
		for _, p := range roleAccesses {
			if p.RoleID == models.GuestRoleType && p.Allow {
				isPublicAccess = true
				break
			}
		}

		// Get user info from token
		if headers.Token != "" {
			claims, err := services.Auth.ParseAccessToken(headers.Token)
			if err == nil {
				userDB, err := models.Repository.User.FindByID(claims.Sub)
				if err != nil {
					appG.Response401(e.Error_auth_invalid_user, err.Error())
					c.Abort()
					return
				} else {
					urgs, rErr := models.Repository.UserRoleOrg.FindByUserId(userDB.ID)
					if rErr != nil {
						appG.Response401(e.Error_auth_invalid_user_role, err.Error())
						c.Abort()
						return
					}
					user = userDB
					userRoleOrgs = urgs
					domainInToken = claims.Url
					c.Set(util.ContextUserKey, user.Sanitize())
					ctx = context.WithValue(ctx, util.ContextUserKey, user.Sanitize())
				}

				if !claims.OTPVerified {
					log.Debugf("Middleware::BeforeInterceptor:: OTP not verified %s", user.Email)
					appG.Response401(e.Error_auth_otp_not_verified, "OTP not verified")
					c.Abort()
					return
				}

			} else {
				log.Debugf("Parse access token error: %v", err)
				if !isPublicAccess {
					switch {
					case errors.Is(err, jwt.ErrTokenExpired):
						code = e.Error_auth_expired_token
					default:
						code = e.Error_auth_verify_token_fail
					}
					appG.Response401(code, err.Error())
					c.Abort()
					return
				}
			}
		}

		if !setting.IsSysAdminSite(domain.Domain) {
			org, oErr := models.Repository.Organization.FindByDomain(domain.Domain)
			if oErr != nil {
				log.Errorf("Middleware::BeforeInterceptor::Find org failed %v", oErr)
				appG.Response401(e.Error_auth_invalid_org, "Org not found, domain: "+domain.Domain)
				c.Abort()
				return
			}

			models.AppSchema = org.Schema
			c.Set(util.ContextOrgKey, org)
			organization = org
			ctx = context.WithValue(ctx, util.ContextOrgKey, org)
		}

		// Public access
		if isPublicAccess {
			c.Request = c.Request.WithContext(ctx)
			c.Next()
			return
		}

		// Not public request and don't have user credentials
		if user == nil {
			appG.Response403(e.FORBIDDEN, "Method require login")
			c.Abort()
			return
		}

		// Allow token of ORG can access current ORG
		if !setting.IsSysAdminSite(domain.Domain) && setting.AppSetting.BaseDomain != domainInToken {
			appG.Response403(e.Error_auth_domain_miss_match, "Domain and token domain are miss match!")
			c.Abort()
			return
		}

		// Validate user active
		if !user.Active {
			log.Debugf("Middleware::BeforeInterceptor:: user inactive %s", user.Email)
			appG.Response403(e.Auth_user_inactive, "User inactive")
			c.Abort()
			return
		}

		// Validate user bocked
		if user.Blocked {
			log.Debugf("Middleware::BeforeInterceptor:: user blocked %s", user.Email)
			appG.Response403(e.Auth_user_blocked, "User blocked")
			c.Abort()
			return
		}

		// Only allow admin roles can access admin site
		if setting.IsSysAdminSite(domain.Domain) && models.IsSysAdminRoles(userRoleOrgs) {
			for _, userRole := range userRoleOrgs {
				if lo.ContainsBy(roleAccesses, func(p *models.RoleAccess) bool {
					return p.RoleID == userRole.RoleID && p.Allow
				}) {
					c.Next()
					c.Request = c.Request.WithContext(ctx)
					return
				}
			}
			log.Debugf("Middleware::BeforeInterceptor::Permission::List permissions: %s", util.Struct2Json(roleAccesses))
			appG.Response403(e.FORBIDDEN, "Method admin not allow")
			c.Abort()
			return
		}

		// /admin: only display data of current org
		// /instructor: only display instructor data of current org + user
		// /learner: only display user data all org
		// sysadmin, admin, mod có thể access /admin, /instructor, /learner
		if models.IsSysAdminRoles(userRoleOrgs) {
			c.Request = c.Request.WithContext(ctx)
			c.Next()
			return
		}

		if ok, cErr := canAccess(roleAccesses, userRoleOrgs, user.ID, organization.ID); cErr != nil {
			appG.ResponseAppError(cErr)
			c.Abort()
			return
		} else {
			if !ok {
				b, _ := json.Marshal(roleAccesses)
				log.Debugf("Middleware::BeforeInterceptor::Permission::List permission role accesses: %s", string(b))
				appG.Response403(e.FORBIDDEN, "Method not allow")
				c.Abort()
				return
			}
		}
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

func canAccess(permissions []*models.RoleAccess, uros []*models.UserRoleOrg, userId, orgId string) (bool, *e.AppError) {
	// check if current user have a default role with current org
	if !lo.ContainsBy(uros, func(item *models.UserRoleOrg) bool {
		return item.RoleID == models.LearnerRoleType && item.OrgID == orgId
	}) {
		if ur, uErr := services.Auth.AddDefaultRole(userId, orgId); uErr != nil {
			return false, uErr
		} else {
			uros = append(uros, ur)
		}
	}

	for _, uro := range uros {
		if lo.ContainsBy(permissions, func(p *models.RoleAccess) bool {
			return p.RoleID == uro.RoleID && uro.OrgID == orgId && p.Allow
		}) {
			return true, nil
		}
	}
	return false, nil
}

func ExtractAllowedFields() gin.HandlerFunc {
	return func(c *gin.Context) {
		allowedFields := models.GetConfig[[]string](models.FieldAllowInRequest)
		var requestMap map[string]interface{}
		if err := c.ShouldBindBodyWith(&requestMap, binding.JSON); err != nil {
			log.Errorf("Middleware::ExtractAllowedFields Bind and validate JSON failed: %v", err)
			c.Next()
		}

		filteredFields := map[string]interface{}{}
		for _, field := range allowedFields {
			if value, exists := requestMap[field]; exists {
				filteredFields[field] = value
			}
		}
		if len(filteredFields) > 0 {
			c.Set(util.ContextAllowFields, filteredFields)
		}
		c.Next()
	}
}
