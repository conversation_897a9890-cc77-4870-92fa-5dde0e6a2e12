package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"openedu-core/models"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
)

func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get(util.HeaderOriginKey)
		allowedOrigins := models.GetConfig[models.StringArray](models.AllowOriginConfig)
		_, found := lo.Find(allowedOrigins, func(item string) bool {
			return item == origin
		})
		log.Debug("Origin: ", origin, found, " Allow List: ", allowedOrigins)
		if found {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		}
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers",
			"Content-Type, Content-Length, Accept-Encoding, Authorization, accept, origin, Cache-Control, X-referrer, X-api-key, Origin",
		)
		c<PERSON><PERSON>("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
