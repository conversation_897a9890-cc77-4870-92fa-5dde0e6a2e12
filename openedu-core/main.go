package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"openedu-core/api"
	"openedu-core/config"
	_ "openedu-core/docs"
	dtovalidate "openedu-core/dto/validate"
	"openedu-core/models"
	"openedu-core/pkg/ai"
	"openedu-core/pkg/cache"
	"openedu-core/pkg/communication"
	"openedu-core/pkg/email_normalizer"
	"openedu-core/pkg/email_normalizer/rules"
	"openedu-core/pkg/exchange"
	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	"openedu-core/pkg/openedu_scheduler"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/upload"
	"openedu-core/pkg/util"
	"openedu-core/queues"
	"openedu-core/services"
	"os"
	"os/signal"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/samber/lo"
)

func init() {
	setting.Setup()
	cache.SetupCache()
	log.Newlogger(log.ConfigLogger{
		Mode:     "dev",
		Encoding: "console",
		ZapType:  "sugar",
		Level: lo.If(
			setting.ServerSetting.RunMode == util.RunModeRelease,
			util.LogLevelInfo,
		).Else(util.LogLevelDebug),
	})
	models.Setup()
	upload.Setup()
	communication.Setup()
	exchange.Setup()
	openedu_chain.Setup()
	services.Setup()
	util.Validator = validator.New()
	util.Validator.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})
	util.Setup()
	dtovalidate.Setup()
	ai.Setup()
	openedu_scheduler.Setup()
}

// @title						OpenEdu APIs
// @version						1.0
// @description					The API server for OpenEdu project.
//
// @securityDefinitions.apikey	BearerAuth
// @in							header
// @name						Authorization
// @description					Enter the bearer token in the format "Bearer {token}"
func main() {
	gin.SetMode(setting.ServerSetting.RunMode)
	routersInit := api.InitRouter()
	readTimeout := setting.ServerSetting.ReadTimeout
	writeTimeout := setting.ServerSetting.WriteTimeout
	port := setting.ServerSetting.HttpPort
	portStr := os.Getenv("PORT")
	if portStr != "" {
		port, _ = strconv.Atoi(portStr)
	}

	endPoint := fmt.Sprintf("%s:%d", setting.ServerSetting.Host, port)
	maxHeaderBytes := 1 << 20

	server := &http.Server{
		Addr:           endPoint,
		Handler:        routersInit,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}

	models.MigrateDatabase()

	config.InitSystem()
	defer func() {
		if cErr := services.QueueProducer.Close(); cErr != nil {
			log.Errorf("Close queue producer error: %v", cErr)
		}
	}()

	normalizeEmailRules := models.GetConfig[[]*rules.NormalizeEmailRule](models.NormalizeEmailRules)
	for _, rule := range normalizeEmailRules {
		email_normalizer.GetRegistry().Register(rule)
	}

	log.Info("Start http server listening ", endPoint)
	go func() {
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("listen: %s\n", err)
		}
	}()

	log.Info("Start consuming messages on queues")
	go func() {
		if err := queues.Consume(); err != nil {
			log.Fatalf("Consume messages on queues failed: %v", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	log.Info("Shutting down server...")

	if err := queues.Stop(); err != nil {
		log.Fatalf("Stop queues failed: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}
	log.Info("Server exiting")
}
