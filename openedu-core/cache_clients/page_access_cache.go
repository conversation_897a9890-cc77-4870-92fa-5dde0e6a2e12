package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	//PageAccessCacheTTL    = 30 * time.Minute
	PageAccessCacheTTL    = 7 * 24 * time.Hour
	PageAccessClientCache = "all_permission"
)

func (c *PageAccessCache) SetAll(results []interface{}) error {
	key := makeCacheKey(c.Prefix, PageAccessClientCache)
	err := cache.Client.Set(key, results, PageAccessCacheTTL)
	return err
}

func (c *PageAccessCache) GetAll(results []interface{}) error {
	key := makeCacheKey(c.Prefix, PageAccessClientCache)
	return cache.Client.Get(key, results)
}

func (c *PageAccessCache) DeleteAll() error {
	return cache.Client.Flush()
}
