package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	QuizSubmissionCacheTTL = 5 * time.Minute
)

func (c *QuizSubmissionCache) SetByKey(k string, submission interface{}) error {
	key := makeCacheKey(c.Prefix, k)
	err := cache.Client.Set(key, submission, QuizSubmissionCacheTTL)
	return err
}

func (c *QuizSubmissionCache) GetByKey(k string, submission interface{}) error {
	key := makeCacheKey(c.Prefix, k)
	return cache.Client.Get(key, submission)
}

func (c *QuizSubmissionCache) DeleteByKey(k string) error {
	key := makeCacheKey(c.Prefix, k)
	return cache.Client.Delete(key)
}

func (c *QuizSubmissionCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
