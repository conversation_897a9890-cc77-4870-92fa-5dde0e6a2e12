package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	PublishCourseTTL = 8 * time.Hour
)

func (c *PublishCourseCache) SetByKey(key string, pubCourse interface{}) error {
	key = makeCacheKey(c.Prefix, key)
	err := cache.Client.Set(key, pubCourse, PublishCourseTTL)
	return err
}

func (c *PublishCourseCache) GetByKey(key string, pubCourse interface{}) error {
	key = makeCacheKey(c.Prefix, key)
	err := cache.Client.Get(key, &pubCourse)
	if err != nil {
		return err
	}
	return nil
}

func (c *PublishCourseCache) DeleteByKey(key string) error {
	strKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Delete(strKey)
}

func (c *PublishCourseCache) Flush() error {
	return cache.Client.Flush()
}
