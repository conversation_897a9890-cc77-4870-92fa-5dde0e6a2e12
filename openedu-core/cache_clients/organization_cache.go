package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	OrganizationTTL = 10 * time.Minute
)

func (c *OrganizationCache) Set(key string, org interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Set(cacheKey, org, OrganizationTTL)
}

func (c *OrganizationCache) Get(key string, org interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Get(cacheKey, org)
}

func (c *OrganizationCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *OrganizationCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
