package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	ReferralCampaignTTL = 24 * time.Hour
)

func (c *OEReferralCampaignCache) Set(cKey string, campaign interface{}) error {
	key := makeCacheKey(c.Prefix, c<PERSON>ey)
	err := cache.Client.Set(key, campaign, ReferralCampaignTTL)
	return err
}

func (c *OEReferralCampaignCache) Get(cKey string, campaign interface{}) error {
	key := makeCacheKey(c.Prefix, cKey)
	return cache.Client.Get(key, campaign)
}

func (c *OEReferralCampaignCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *OEReferralCampaignCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
