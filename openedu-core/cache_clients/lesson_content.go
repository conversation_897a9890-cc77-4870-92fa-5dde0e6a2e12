package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	LessonContentTTL = 1 * time.Hour
)

func (c *LessonContentCache) SetByKey(key string, contents []interface{}) error {
	key = makeCache<PERSON>ey(c.Prefix, key)
	err := cache.Client.Set(key, contents, LessonContentTTL)
	return err
}

func (c *LessonContentCache) GetByKey(key string, contents *[]interface{}) error {
	key = makeCacheKey(c.Prefix, key)
	err := cache.Client.Get(key, contents)
	if err != nil {
		return err
	}
	return nil
}

func (c *LessonContentCache) DeleteByKey(key string) error {
	strKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Delete(strKey)
}

func (c *LessonContentCache) Flush() error {
	return cache.Client.Flush()
}

func (c *LessonContentCache) DeleteByCourseID(courseID string) error {
	strKey := makeCacheKey(c.Prefix, courseID)
	return cache.Client.DeleteByPrefix(strKey)
}
