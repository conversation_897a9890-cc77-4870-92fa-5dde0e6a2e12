package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	UserTTL = 1 * time.Minute
)

func (c *UserCache) SetUser(id string, user interface{}) error {
	key := makeCacheKey(c.Prefix, id)
	err := cache.Client.Set(key, user, UserTTL)
	return err
}

func (c *UserCache) GetByUserID(userID string, user interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	return cache.Client.Get(key, user)
}

func (c *UserCache) DeleteByUserID(userID string) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Delete(key)
	return err
}

func (c *UserCache) SetByKey(key string, user interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	err := cache.Client.Set(cacheKey, user, UserTTL)
	return err
}

func (c *UserCache) GetBy<PERSON><PERSON>(key string, user interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Get(cacheKey, user)
}

func (c *UserCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *UserCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
