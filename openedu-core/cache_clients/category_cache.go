package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	CategoryListPrefix        = "category_list_"
	AllActiveBlogCategories   = "all_active_blog_categories"
	AllActiveCourseCategories = "all_active_course_categories"
	AllActiveLevels           = "all_active_levels"
	AllCategoriesTTL          = 24 * time.Hour
)

func (c *CategoryCache) GetAllCourseCategories(results *[]interface{}) error {
	key := makeCacheKey(c.Prefix, AllActiveCourseCategories)
	err := cache.Client.Get(key, results)
	if err != nil {
		return err
	}
	return nil
}

func (c *CategoryCache) GetAllBlogCategories(results *[]interface{}) error {
	key := makeCacheKey(c.Prefix, AllActiveBlogCategories)
	err := cache.Client.Get(key, results)
	if err != nil {
		return err
	}
	return nil
}

func (c *CategoryCache) GetAllLevels(results *[]interface{}) error {
	key := makeCacheKey(c.Prefix, AllActiveLevels)
	err := cache.Client.Get(key, results)
	if err != nil {
		return err
	}
	return nil
}

func (c *CategoryCache) SetAllCourseCategories(categories []interface{}) error {
	key := makeCacheKey(c.Prefix, AllActiveCourseCategories)
	err := cache.Client.Set(key, categories, AllCategoriesTTL)
	return err

}

func (c *CategoryCache) SetAllBlogCategories(categories []interface{}) error {
	key := makeCacheKey(c.Prefix, AllActiveBlogCategories)
	err := cache.Client.Set(key, categories, AllCategoriesTTL)
	return err
}

func (c *CategoryCache) SetAllLevels(categories []interface{}) error {
	key := makeCacheKey(c.Prefix, AllActiveLevels)
	err := cache.Client.Set(key, categories, AllCategoriesTTL)
	return err
}

func (c *CategoryCache) SetList(cacheKey string, categories interface{}) error {
	key := makeCacheKey(c.Prefix+CategoryListPrefix, cacheKey)
	err := cache.Client.Set(key, categories, CourseItemTTL)
	return err
}

func (c *CategoryCache) GetList(cacheKey string, categories interface{}) error {
	key := makeCacheKey(c.Prefix+CategoryListPrefix, cacheKey)
	return cache.Client.Get(key, categories)
}

func (c *CategoryCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *CategoryCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
