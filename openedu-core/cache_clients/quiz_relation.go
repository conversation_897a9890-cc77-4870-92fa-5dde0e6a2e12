package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	QuizRelationCacheTTL = 1 * time.Hour
)

func (c *QuizRelationCache) SetByKey(k string, answers interface{}) error {
	key := makeCacheKey(c.Prefix, k)
	err := cache.Client.Set(key, answers, QuizRelationCacheTTL)
	return err
}

func (c *QuizRelationCache) GetByKey(k string, answers interface{}) error {
	key := makeCacheKey(c.Prefix, k)
	return cache.Client.Get(key, answers)
}

func (c *QuizRelationCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *QuizRelationCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
