package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	UserWalletTTL = 5 * time.Minute
)

func (c *WalletCache) SetByUser(userID string, wallets []interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Set(key, wallets, UserWalletTTL)
	return err
}

func (c *WalletCache) GetByUser(userID string, wallets *[]interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Get(key, wallets)
	if err != nil {
		return err
	}
	return nil
}

func (c *WalletCache) Set(ckey string, wallets interface{}) error {
	key := makeCacheKey(c.Prefix, ckey)
	err := cache.Client.Set(key, wallets, UserWalletTTL)
	return err
}

func (c *WalletCache) Get(ckey string, wallets interface{}) error {
	key := makeCacheKey(c.Prefix, ckey)
	err := cache.Client.Get(key, wallets)
	if err != nil {
		return err
	}
	return nil
}

func (c *WalletCache) SetMany(ckey string, wallets []interface{}) error {
	key := makeCacheKey(c.Prefix, ckey)
	err := cache.Client.Set(key, wallets, UserWalletTTL)
	return err
}

func (c *WalletCache) GetMany(ckey string, wallets *[]interface{}) error {
	key := makeCacheKey(c.Prefix, ckey)
	err := cache.Client.Get(key, wallets)
	if err != nil {
		return err
	}
	return nil
}

func (c *WalletCache) DeleteByUserID(userID string) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Delete(key)
	return err
}

func (c *WalletCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *WalletCache) Flush() error {
	return cache.Client.Flush()
}
