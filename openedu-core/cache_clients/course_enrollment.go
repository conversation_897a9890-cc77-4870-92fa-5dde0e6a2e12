package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	CourseEnrollmentTTL   = 5 * time.Minute
	CourseEnrollment1MTTL = 1 * time.Minute
)

func (c *CourseEnrollmentCache) Set(key string, org interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Set(cacheKey, org, CourseEnrollmentTTL)
}

func (c *CourseEnrollmentCache) Get(key string, org interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Get(cacheKey, org)
}

func (c *CourseEnrollmentCache) Set1M(key string, org interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Set(cacheKey, org, CourseEnrollmentTTL)
}

func (c *CourseEnrollmentCache) Get1M(key string, org interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Get(cacheKey, org)
}

func (c *CourseEnrollmentCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *CourseEnrollmentCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}

func (c *CourseEnrollmentCache) SetManyCourseEnrollment(courseEnrollmentsCount map[string]int64) error {
	for key, value := range courseEnrollmentsCount {
		redisKey := makeCacheKey(c.Prefix, key, "count")
		err := cache.Client.Set(redisKey, value, CourseEnrollmentTTL)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *CourseEnrollmentCache) GetManyCourseEnrollment(courseCuids []string) (res map[string]int64, err error) {
	res = make(map[string]int64)

	for _, id := range courseCuids {
		cacheKey := makeCacheKey(c.Prefix, id, "count")
		var count int64
		if getErr := cache.Client.Get(cacheKey, &count); getErr != nil {
			err = getErr
			return
		}

		res[id] = count
	}

	return
}
