package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	WalletEarningTTL = 5 * time.Minute
)

func (c *WalletEarningCache) SetByUser(userID string, walletEarnings []interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Set(key, walletEarnings, WalletEarningTTL)
	return err
}

func (c *WalletEarningCache) GetByUser(userID string, walletEarnings *[]interface{}) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Get(key, walletEarnings)
	if err != nil {
		return err
	}
	return nil
}

func (c *WalletEarningCache) DeleteByUserID(userID string) error {
	key := makeCacheKey(c.Prefix, userID)
	err := cache.Client.Delete(key)
	return err
}

func (c *WalletEarningCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *WalletEarningCache) Flush() error {
	return cache.Client.Flush()
}
