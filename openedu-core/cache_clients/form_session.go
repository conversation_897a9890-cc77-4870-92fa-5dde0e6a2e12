package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	FormSessionCacheTTL     = 30 * time.Minute
	FormSessionSubmittedKey = "user_submitted"
)

func (c *FormSessionCache) SetUserSubmitted(userID string, hasSubmittedByFormUI<PERSON> interface{}) error {
	key := makeCacheKey(c.Prefix+FormSessionSubmittedKey, userID)
	err := cache.Client.Set(key, hasSubmittedByFormUIDs, FormSessionCacheTTL)
	return err
}

func (c *FormSessionCache) GetUserSubmitted(userID string, hasSubmittedByFormUIDs interface{}) error {
	key := makeCacheKey(c.Prefix+FormSessionSubmittedKey, userID)
	return cache.Client.Get(key, hasSubmittedByFormUIDs)
}

func (c *FormSessionCache) DeleteUserSubmitted(userID string) error {
	key := makeCacheKey(c.Prefix+FormSessionSubmittedKey, userID)
	return cache.Client.Delete(key)
}

func (c *FormSessionCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *FormSessionCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
