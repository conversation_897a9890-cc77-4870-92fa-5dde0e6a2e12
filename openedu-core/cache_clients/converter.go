package cache_clients

import (
	"encoding/json"
	"reflect"
)

func mapToStruct(input, output interface{}) error {
	data, err := json.Marshal(input)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, output)
}

func convertValue(input, output interface{}) error {
	if reflect.TypeOf(input) == reflect.TypeOf(output).Elem() {
		reflect.ValueOf(output).Elem().Set(reflect.ValueOf(input))
		return nil
	}

	inputValue := reflect.ValueOf(input)
	if inputValue.Kind() == reflect.Map {
		return mapToStruct(input, output)
	}

	var data []byte
	switch v := input.(type) {
	case string:
		data = []byte(v)
	case []byte:
		data = v
	default:
		var err error
		data, err = json.Marshal(input)
		if err != nil {
			return err
		}
	}

	return json.Unmarshal(data, output)
}
