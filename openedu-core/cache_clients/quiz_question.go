package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	QuizQuestionCacheTTL = 1 * time.Hour
)

func (c *QuizQuestionCache) SetByID(ID string, Questions interface{}) error {
	key := makeCacheKey(c.Prefix, ID)
	err := cache.Client.Set(key, Questions, QuizQuestionCacheTTL)
	return err
}

func (c *QuizQuestionCache) GetByID(ID string, Questions interface{}) error {
	key := makeCacheKey(c.Prefix, ID)
	return cache.Client.Get(key, Questions)
}

func (c *QuizQuestionCache) DeleteByID(quizID string) error {
	key := makeCacheKey(c.Prefix, quizID)
	return cache.Client.Delete(key)
}

func (c *QuizQuestionCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *QuizQuestionCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
