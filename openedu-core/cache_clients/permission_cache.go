package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	AllPermissionsCacheKey = "all-permissions"
	AllPermissionsTTL      = 24 * time.Hour
	PermissionTTL          = 24 * time.Hour
)

func (c *PermissionCache) Set(key string, roleAccesses interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Set(cacheKey, roleAccesses, PermissionTTL)
}

func (c *PermissionCache) Get(key string, roleAccesses interface{}) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Get(cacheKey, roleAccesses)
}

func (c *PermissionCache) SetAll(permissions []interface{}) error {
	key := makeCacheKey(c.Prefix, AllPermissionsCacheKey)
	err := cache.Client.Set(key, permissions, AllPermissionsTTL)
	return err
}

func (c *PermissionCache) FindAll(permissions *[]interface{}) error {
	key := makeCacheKey(c.Prefix, AllPermissionsCacheKey)
	return cache.Client.Get(key, permissions)
}

func (c *PermissionCache) DeleteByKey(key string) error {
	cacheKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Delete(cacheKey)
}

func (c *PermissionCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
