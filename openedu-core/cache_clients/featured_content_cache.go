package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	FeaturedContentTTL      = 1 * time.Hour
	FeaturedContentCacheAll = "all"
)

func (c *FeaturedContentCache) SetByKey(key string, contents []interface{}) error {
	key = makeCacheKey(c.Prefix, key)
	err := cache.Client.Set(key, contents, FeaturedContentTTL)
	return err
}

func (c *FeaturedContentCache) GetByKey(key string, contents *[]interface{}) error {
	key = makeCacheKey(c.Prefix, key)
	err := cache.Client.Get(key, contents)
	if err != nil {
		return err
	}
	return nil
}

func (c *FeaturedContentCache) DeleteByKey(key string) error {
	strKey := makeCacheKey(c.Prefix, key)
	return cache.Client.Delete(strKey)
}

func (c *FeaturedContentCache) SetAll(contents []interface{}) error {
	key := makeCacheKey(c.Prefix, FeaturedContentCacheAll)
	err := cache.Client.Set(key, contents, FeaturedContentTTL)
	return err
}

func (c *FeaturedContentCache) GetAll(contents *[]interface{}) error {
	key := makeCacheKey(c.Prefix, FeaturedContentCacheAll)
	err := cache.Client.Get(key, contents)
	if err != nil {
		return err
	}
	return nil
}

func (c *FeaturedContentCache) DeleteKeyAll() error {
	key := makeCacheKey(c.Prefix, FeaturedContentCacheAll)
	return cache.Client.Delete(key)
}

func (c *FeaturedContentCache) Flush() error {
	return cache.Client.Flush()
}
