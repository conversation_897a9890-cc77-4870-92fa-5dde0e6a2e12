package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	CoursePropsTTL = 1 * time.Hour
)

func (c *CoursePropsCache) Set(key string, datas interface{}) error {
	key = makeCacheKey(c.Prefix, key)
	err := cache.Client.Set(key, datas, CoursePropsTTL)
	return err
}

func (c *CoursePropsCache) Get(key string, contents interface{}) error {
	key = makeCacheKey(c.Prefix, key)
	err := cache.Client.Get(key, contents)
	if err != nil {
		return err
	}
	return nil
}

func (c *CoursePropsCache) Flush() error {
	return cache.Client.Flush()
}
