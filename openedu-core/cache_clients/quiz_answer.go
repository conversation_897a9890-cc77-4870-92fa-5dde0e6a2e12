package cache_clients

import (
	"openedu-core/pkg/cache"
	"time"
)

const (
	QuizAnswerCacheTTL = 10 * time.Minute
)

func (c *QuizAnswerCache) SetBySubmissionID(submissionID string, answers interface{}) error {
	key := makeCacheKey(c.Prefix, submissionID)
	err := cache.Client.Set(key, answers, QuizAnswerCacheTTL)
	return err
}

func (c *QuizAnswerCache) GetBySubmissionID(submissionID string, answers interface{}) error {
	key := makeCacheKey(c.Prefix, submissionID)
	return cache.Client.Get(key, answers)
}

func (c *QuizAnswerCache) DeleteBySubmissionID(quizID string) error {
	key := makeCacheKey(c.Prefix, quizID)
	return cache.Client.Delete(key)
}

func (c *QuizAnswerCache) DeleteByKey(key string) error {
	return cache.Client.Delete(key)
}

func (c *QuizAnswerCache) Flush() error {
	return cache.Client.DeleteByPrefix(c.Prefix)
}
