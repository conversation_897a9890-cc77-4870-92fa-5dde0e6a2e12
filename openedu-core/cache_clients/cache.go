package cache_clients

import (
	"fmt"
)

type Caching struct {
	User               UserCacheIface
	Permission         PermissionCacheIface
	System             SystemConfigCacheIface
	Organization       OrganizationCacheIface
	Category           CategoryCacheIface
	UserRole           UserRoleCacheIface
	Form               FormCacheIface
	FormSession        FormSessionCacheIface
	QuizSubmission     QuizSubmissionCacheIface
	QuizRelation       QuizRelationCacheIface
	PageAccess         PageAccessCacheIface
	Wallet             WalletCacheIface
	Quiz               QuizCacheIface
	QuizAnswer         QuizAnswerCacheIface
	Blog               BlogCacheIface
	CourseEnrollment   CourseEnrollmentIface
	ExchangeRate       ExchangeRateCacheIface
	Transaction        TransactionCacheIface
	WalletEarning      WalletEarningCacheIface
	PricingPlan        PricingPlanCacheIface
	FeaturedContent    FeaturedContentCacheIface
	Course             CourseCacheIface
	OEReferralCode     OEReferralCodeCacheIface
	OEReferralCampaign OEReferralCampaignCacheIface
	PublishCourse      PublishCourseCacheIface
	LessonContent      LessonContentCacheIface
	Subscription       SubscriptionCacheIface
	CourseProps        CoursePropsCacheIface
}

func (c *Caching) Convert(input, output interface{}) error {
	return convertValue(input, output)
}

func makeCacheKey(prefix string, key string, otherKeys ...string) string {
	fullKey := prefix + key
	for _, other := range otherKeys {
		other = "_" + other
		fullKey += other

	}
	return fullKey
}

func (c *Caching) DeleteAll() error {
	var dErr error
	if err := c.User.Flush(); err != nil {
		dErr = fmt.Errorf("delete all user cache: %w", err)
	}
	if err := c.Permission.Flush(); err != nil {
		dErr = fmt.Errorf("delete all permission cache: %w", err)
	}
	if err := c.System.Flush(); err != nil {
		dErr = fmt.Errorf("delete all system cache: %w", err)
	}
	if err := c.Organization.Flush(); err != nil {
		dErr = fmt.Errorf("delete all organization cache: %w", err)
	}
	if err := c.Category.Flush(); err != nil {
		dErr = fmt.Errorf("delete all category cache: %w", err)
	}
	if err := c.UserRole.Flush(); err != nil {
		dErr = fmt.Errorf("delete all user role cache: %w", err)
	}
	if err := c.Form.Flush(); err != nil {
		dErr = fmt.Errorf("delete all form cache: %w", err)
	}
	if err := c.Quiz.Flush(); err != nil {
		dErr = fmt.Errorf("delete all quiz cache: %w", err)
	}
	if err := c.QuizAnswer.Flush(); err != nil {
		dErr = fmt.Errorf("delete all quiz answer cache: %w", err)
	}
	if err := c.QuizRelation.Flush(); err != nil {
		dErr = fmt.Errorf("delete all quiz relation cache: %w", err)
	}
	if err := c.QuizSubmission.Flush(); err != nil {
		dErr = fmt.Errorf("delete all quiz submission cache: %w", err)
	}
	if err := c.Wallet.Flush(); err != nil {
		dErr = fmt.Errorf("delete all wallet cache: %w", err)
	}
	if err := c.Transaction.Flush(); err != nil {
		dErr = fmt.Errorf("delete all transaction cache: %w", err)
	}
	if err := c.ExchangeRate.Flush(); err != nil {
		dErr = fmt.Errorf("delete all exchange rate cache: %w", err)
	}
	if err := c.Blog.Flush(); err != nil {
		dErr = fmt.Errorf("delete all blog cache: %w", err)
	}
	if err := c.WalletEarning.Flush(); err != nil {
		dErr = fmt.Errorf("delete all wallet earnings cache: %w", err)
	}
	if err := c.PricingPlan.Flush(); err != nil {
		dErr = fmt.Errorf("delete all PricingPlan cache: %w", err)
	}
	if err := c.FeaturedContent.Flush(); err != nil {
		dErr = fmt.Errorf("delete all FeaturedContent cache: %w", err)
	}
	if err := c.PublishCourse.Flush(); err != nil {
		dErr = fmt.Errorf("delete all publish course cache: %w", err)
	}
	if err := c.LessonContent.Flush(); err != nil {
		dErr = fmt.Errorf("delete all lesson content cache: %w", err)
	}
	if err := c.OEReferralCode.Flush(); err != nil {
		dErr = fmt.Errorf("delete all OEReferralCode cache: %w", err)
	}
	if err := c.OEReferralCampaign.Flush(); err != nil {
		dErr = fmt.Errorf("delete all OEReferralCampaign cache: %w", err)
	}
	if err := c.Subscription.Flush(); err != nil {
		dErr = fmt.Errorf("delete all Subscription cache: %w", err)
	}
	if err := c.CourseProps.Flush(); err != nil {
		dErr = fmt.Errorf("delete all course props cache: %w", err)
	}
	if err := c.FormSession.Flush(); err != nil {
		dErr = fmt.Errorf("delete all form session cache: %w", err)
	}
	return dErr
}

type CacheModel struct {
	Prefix string
}

type BlogCache struct {
	CacheModel
}

type UserCache struct {
	CacheModel
}

type PermissionCache struct {
	CacheModel
}

type SystemConfigCache struct {
	CacheModel
}

type OrganizationCache struct {
	CacheModel
}

type CategoryCache struct {
	CacheModel
}

type UserRoleCache struct {
	CacheModel
}

type FormCache struct {
	CacheModel
}

type PageAccessCache struct {
	CacheModel
}

type WalletCache struct {
	CacheModel
}

type QuizCache struct {
	CacheModel
}

type CourseEnrollmentCache struct {
	CacheModel
}

type ExchangeRateCache struct {
	CacheModel
}

type TransactionCache struct {
	CacheModel
}

type WalletEarningCache struct {
	CacheModel
}

type PricingPlanCache struct {
	CacheModel
}

type FeaturedContentCache struct {
	CacheModel
}

type CourseCache struct {
	CacheModel
}

type PublishCourseCache struct {
	CacheModel
}

type LessonContentCache struct {
	CacheModel
}

type OEReferralCodeCache struct {
	CacheModel
}

type OEReferralCampaignCache struct {
	CacheModel
}

type SubscriptionCache struct {
	CacheModel
}

type FormSessionCache struct {
	CacheModel
}

type CoursePropsCache struct {
	CacheModel
}

type QuizAnswerCache struct {
	CacheModel
}

type QuizQuestionCache struct {
	CacheModel
}

type QuizSubmissionCache struct {
	CacheModel
}

type QuizRelationCache struct {
	CacheModel
}

type UserCacheIface interface {
	SetUser(id string, user interface{}) error
	GetByUserID(userID string, user interface{}) error
	DeleteByUserID(userID string) error
	SetByKey(key string, user interface{}) error
	GetByKey(key string, user interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type PermissionCacheIface interface {
	Set(key string, roleAccesses interface{}) error
	Get(key string, roleAccesses interface{}) error
	SetAll(permissions []interface{}) error
	FindAll(permissions *[]interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type SystemConfigCacheIface interface {
	Set(systemKey string, system interface{}) error
	Get(cfgKey string, config interface{}) error
	SetAll(cacheKey string, systems []interface{}) error
	GetAll(cacheKey string, systems *[]interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type OrganizationCacheIface interface {
	Set(key string, org interface{}) error
	Get(key string, org interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type CategoryCacheIface interface {
	GetAllCourseCategories(results *[]interface{}) error
	GetAllBlogCategories(results *[]interface{}) error
	GetAllLevels(results *[]interface{}) error

	SetAllCourseCategories(categories []interface{}) error
	SetAllBlogCategories(categories []interface{}) error
	SetAllLevels(categories []interface{}) error

	SetList(cacheKey string, categories interface{}) error
	GetList(cacheKey string, categories interface{}) error

	DeleteByKey(key string) error
	Flush() error
}

type UserRoleCacheIface interface {
	SetUserRole(userID string, roles []interface{}) error
	GetByUserID(userID string, roles *[]interface{}) error
	DeleteByUserID(userID string) error
	DeleteByKey(key string) error
	Flush() error
}

type FormCacheIface interface {
	SetFormByID(id string, form interface{}) error
	SetFormBySlug(slug string, form interface{}) error
	SetFormByKey(key string, form interface{}) error

	GetByFormID(formId string, form interface{}) error
	GetByFormSlug(slug string, form interface{}) error
	GetByKey(key string, form interface{}) error

	DeleteByFormID(id string) error
	DeleteByFormSlug(slug string) error
	DeleteByKey(key string) error
	Flush() error
}

type PageAccessCacheIface interface {
	SetAll(results []interface{}) error
	GetAll(results []interface{}) error
	DeleteAll() error
}

type QuizCacheIface interface {
	SetQuizByID(id string, quiz interface{}) error
	GetByQuizID(quizID string, quiz interface{}) error
	DeleteByQuizID(id string) error
	DeleteByKey(key string) error
	Flush() error
}

type WalletCacheIface interface {
	SetByUser(userID string, wallets []interface{}) error
	GetByUser(userID string, wallets *[]interface{}) error
	Set(ckey string, wallet interface{}) error
	Get(ckey string, wallet interface{}) error
	SetMany(ckey string, wallet []interface{}) error
	GetMany(ckey string, wallet *[]interface{}) error
	DeleteByUserID(userID string) error
	DeleteByKey(key string) error
	Flush() error
}

type BlogCacheIface interface {
	Set(key string, blog interface{}) error
	Get(key string, blog interface{})
	SetManyBlogByCategory(blogByCategory map[string][]interface{}) error
	GetManyBlogByCategory(categoryIDs []string, blogs map[string][]interface{})
	DeleteBlogByManyCategory(categoryIDs []string) error
	DeleteAllBlogByCategory() error
	DeleteByKey(key string) error
	Flush() error
}

type CourseEnrollmentIface interface {
	Set(key string, course interface{}) error
	Get(key string, course interface{}) error
	Set1M(key string, course interface{}) error
	Get1M(key string, course interface{}) error
	DeleteByKey(key string) error
	GetManyCourseEnrollment(courseCuids []string) (res map[string]int64, err error)
	SetManyCourseEnrollment(courseEnrollmentsCount map[string]int64) error
}

type ExchangeRateCacheIface interface {
	SetFiatExchangeRates(exchangeRates interface{}) error
	GetFiatExchangeRates(exchangeRates interface{}) error
	SetCryptoExchangeRates(exchangeRates interface{}) error
	GetCryptoExchangeRates(exchangeRates interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type TransactionCacheIface interface {
	Set(key string, transaction interface{}) error
	Get(key string, transaction interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type WalletEarningCacheIface interface {
	SetByUser(userID string, walletEarnings []interface{}) error
	GetByUser(userID string, walletEarnings *[]interface{}) error
	DeleteByUserID(userID string) error
	DeleteByKey(key string) error
	Flush() error
}

type PricingPlanCacheIface interface {
	SetAll(plans []interface{}) error
	GetAll(plans *[]interface{}) error
	DeleteKeyAll() error
	Flush() error
}

type FeaturedContentCacheIface interface {
	SetAll(plans []interface{}) error
	GetAll(plans *[]interface{}) error
	DeleteByKey(key string) error
	DeleteKeyAll() error
	Flush() error
	SetByKey(key string, contents []interface{}) error
	GetByKey(key string, contents *[]interface{}) error
}

type CourseCacheIface interface {
	Get(courseID string, course interface{}) error
	GetByKey(key string, course interface{}) error
	Set(courseID string, course interface{}) error
	GetListItem(id string, courseItem interface{}) error
	SetListItem(id string, courseItem interface{}) error
	Flush() error
}

type OEReferralCodeCacheIface interface {
	Set(cKey string, code interface{}) error
	Get(cKey string, code interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type OEReferralCampaignCacheIface interface {
	Set(cKey string, campaign interface{}) error
	Get(cKey string, campaign interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type PublishCourseCacheIface interface {
	GetByKey(key string, pubCourse interface{}) error
	SetByKey(key string, pubCourse interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type LessonContentCacheIface interface {
	GetByKey(key string, contents *[]interface{}) error
	SetByKey(key string, contents []interface{}) error
	DeleteByKey(key string) error
	Flush() error
	DeleteByCourseID(courseID string) error
}

type SubscriptionCacheIface interface {
	Get(userID string, pricingPlan interface{}) error
	Set(userID string, pricingPlan interface{}) error
	DeleteByKey(userID string) error
	Flush() error
}

type CoursePropsCacheIface interface {
	Get(string, interface{}) error
	Set(string, interface{}) error
	Flush() error
}

type QuizAnswerCacheIface interface {
	SetBySubmissionID(submissionID string, answers interface{}) error
	GetBySubmissionID(submissionID string, answers interface{}) error
	DeleteBySubmissionID(submissionID string) error
	DeleteByKey(key string) error
	Flush() error
}

type QuizQuestionCacheIface interface {
	SetBySubmissionID(id string, quiz interface{}) error
	GetBySubmissionID(id string, quiz interface{}) error
	DeleteBySubmissionID(id string) error
	DeleteByKey(key string) error
	Flush() error
}

type FormSessionCacheIface interface {
	SetUserSubmitted(userID string, hasSubmittedByFormUIDs interface{}) error
	GetUserSubmitted(userID string, hasSubmittedByFormUIDs interface{}) error
	DeleteUserSubmitted(userID string) error
	DeleteByKey(key string) error
	Flush() error
}

type QuizSubmissionCacheIface interface {
	SetByKey(key string, submission interface{}) error
	GetByKey(key string, submission interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type QuizRelationCacheIface interface {
	SetByKey(key string, submission interface{}) error
	GetByKey(key string, submission interface{}) error
	DeleteByKey(key string) error
	Flush() error
}
