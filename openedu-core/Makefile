start:
	MIGRATE=1 go run main.go
gotest:
	MIGRATE=1 TEST=true go test -v ./test/...
start-dev:
	MIGRATE=1 STAGE=dev go run main.go

start-staging:
	MIGRATE=1 STAGE=staging go run main.go

start-prod:
	STAGE=prod go run main.go
seeds:

	go run scripts/seeds/seeds.go

seeds-dev:
	MIGRATE=1 STAGE=dev scripts/seeds/seeds.go

seeds-staging:
	MIGRATE=1 STAGE=staging scripts/seeds/seeds.go

seeds-prod:
	STAGE=prod scripts/seeds/seeds.go

swagger:
	swag init --parseDependency