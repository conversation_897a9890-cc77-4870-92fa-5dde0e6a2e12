package upload

import (
	"context"
	"fmt"
	"log"
	"openedu-core/pkg/setting"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
)

type ProviderName string

const (
	Local ProviderName = "local"
	S3    ProviderName = "s3"
	Bunny ProviderName = "bunny"

	ConcurrencyLimit = 5
)

var isSupportedUploadProvider = map[ProviderName]bool{
	Local: true,
	S3:    true,
}

type Provider interface {
	UploadFiles(files []*File, pathPrefix string) ([]*UploadedFileInfo, error)
	RemoveFiles(fileInfos []*UploadedFileInfo, pathPrefix string) error
	GetFileData(fileInfo *UploadedFileInfo) ([]byte, error)
}

var DefaultProvider Provider
var PrivateBunnyProvider Provider
var PublicBunnyProvider Provider

// Setup initialize the upload provider instance
func Setup() {
	var err error

	// Bunny Provider is default for video
	providerName := ProviderName(setting.UploadSetting.UploadProvider)
	if !isSupportedUploadProvider[providerName] {
		log.Fatalf("unsupported provider: " + string(providerName))
	}

	DefaultProvider, err = New(providerName)
	if err != nil {
		log.Fatalf("failed to initialize upload provider")
	}

	// Create default Bunny upload provider
	PrivateBunnyProvider, err = New(Bunny)
	if err != nil {
		log.Fatalf("failed to initialize bunny upload provider")
	}

	PublicBunnyProvider = NewBunnyProvider(&BunnyUploadProvider{
		ApiKey:      setting.UploadSetting.UploadPublicBunnyAPIKey,
		ApiURI:      setting.UploadSetting.UploadBunnyApiURI,
		LibraryID:   setting.UploadSetting.UploadPublicBunnyLibraryID,
		IFrameURL:   setting.UploadSetting.UploadBunnyIFrameURL,
		Concurrency: ConcurrencyLimit,
	})
}

func New(providerName ProviderName) (Provider, error) {
	var provider Provider
	switch providerName {
	case Local:
		uploadDirPath := filepath.Join(setting.AppSetting.RuntimeRootPath, setting.UploadSetting.UploadLocalDir)
		provider = newLocalProvider(uploadDirPath, ConcurrencyLimit)

	case S3:
		// Initialize an AWS session
		accessKey := setting.AwsSetting.AccessKey
		secretKey := setting.AwsSetting.SecretKey
		region := setting.AwsSetting.Region
		bucketName := setting.UploadSetting.UploadS3BucketName
		pathPrefix := setting.UploadSetting.UploadS3PathPrefix
		cfg, err := config.LoadDefaultConfig(
			context.Background(),
			config.WithRegion(region),
			config.WithCredentialsProvider(
				credentials.NewStaticCredentialsProvider(accessKey, secretKey, ""),
			),
		)
		if err != nil {
			return nil, fmt.Errorf("Init S3 failed: %v", err)
		}

		provider = newS3Provider(&s3UploadProviderOptions{
			AwsConfig:   cfg,
			BucketName:  bucketName,
			PathPrefix:  pathPrefix,
			Concurrency: ConcurrencyLimit,
		})

	case Bunny:
		// Initialize Bunny provider
		provider = NewBunnyProvider(&BunnyUploadProvider{
			ApiKey:      setting.UploadSetting.UploadPrivateBunnyAPIKey,
			ApiURI:      setting.UploadSetting.UploadBunnyApiURI,
			LibraryID:   setting.UploadSetting.UploadPrivateBunnyLibraryID,
			IFrameURL:   setting.UploadSetting.UploadBunnyIFrameURL,
			Concurrency: ConcurrencyLimit,
		})

	default:
		return nil, fmt.Errorf("invalid provider")
	}

	return provider, nil
}
