package upload

import (
	"io"
	"mime/multipart"
	"path"
	"strings"
)

type File struct {
	Name    string `json:"name"`
	Hash    string `json:"hash"`
	Mime    string `json:"mime"`
	Content []byte `json:"content"`
	Public  bool   `json:"public"`
}

type UploadedFileInfo struct {
	Name           string `json:"name"`
	Hash           string `json:"hash"`
	Mime           string `json:"mime"`
	Ext            string `json:"ext"`
	URL            string `json:"url"`
	ThumbnailURL   string `json:"thumbnail_url"`
	Width          int64  `json:"width"`
	Height         int64  `json:"height"`
	Size           int64  `json:"size"`
	BunnyVideoID   string `json:"bunny_video_id"`
	BunnyLibraryID string `json:"bunny_library_id"`
	Duration       int64  `json:"duration"`
}

func (file *File) IsImage() bool {
	return strings.HasPrefix(file.Mime, "image/")
}

func (file *File) IsVideo() bool {
	return strings.HasPrefix(file.Mime, "video/")
}

func getExt(fileName string) string {
	return path.Ext(fileName)
}

func generateFileName(filename, hash string) string {
	if hash == "" {
		return strings.ReplaceAll(filename, " ", "-")
	}
	return hash + "_" + strings.ReplaceAll(filename, " ", "-")
}

func generateThumbnailName(filename, hash string) string {
	return "thumb_" + hash + "_" + strings.ReplaceAll(filename, " ", "-")
}

// ParseMultipartFiles parses a slice of multipart.FileHeader into a slice of File.
func ParseMultipartFiles(fileHeaders []*multipart.FileHeader) ([]*File, error) {
	var files []*File

	for _, fileHeader := range fileHeaders {
		// Open the uploaded file
		file, err := fileHeader.Open()
		if err != nil {
			return nil, err
		}
		defer file.Close()

		// Read the file content
		fileContent, err := io.ReadAll(file)
		if err != nil {
			return nil, err
		}

		// Create a File instance and populate it with the data
		fileInfo := &File{
			Name:    fileHeader.Filename,
			Mime:    fileHeader.Header.Get("Content-Type"),
			Content: fileContent,
		}

		files = append(files, fileInfo)
	}

	return files, nil
}
