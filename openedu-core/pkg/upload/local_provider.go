package upload

import (
	"bytes"
	"context"
	"fmt"
	_ "image/color"
	_ "image/draw"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"openedu-core/pkg/log"
	"os"
	"path"
	"path/filepath"
	"sync"

	"github.com/disintegration/imaging"
)

type localUploadProvider struct {
	uploadDirPath string
	concurrency   int
}

func newLocalProvider(uploadDirPath string, concurrency int) *localUploadProvider {
	p := localUploadProvider{
		uploadDirPath: uploadDirPath,
		concurrency:   concurrency,
	}
	return &p
}

func saveFile(src io.Reader, dst string) error {
	dir := filepath.Dir(dst)

	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		return err
	}

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

func (p *localUploadProvider) UploadFiles(files []*File, _ string) ([]*UploadedFileInfo, error) {
	var fileInfos []*UploadedFileInfo
	var wg sync.WaitGroup
	var mu sync.Mutex

	// Create a context with a cancellation mechanism
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Channel to signal errors
	errCh := make(chan error, len(files))

	// Limit the number of concurrent goroutines
	concurrencyLimit := p.concurrency
	semaphore := make(chan struct{}, concurrencyLimit)

	for _, file := range files {
		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore
		go func(file *File) {
			defer func() {
				<-semaphore // Release semaphore
				wg.Done()
			}()

			select {
			case <-ctx.Done():
				// Context cancelled, do not proceed
				return
			default:
				var fileInfo UploadedFileInfo
				fileInfo.Name = generateFileName(file.Name, file.Hash)
				fileInfo.Hash = file.Hash
				fileInfo.Mime = file.Mime
				fileInfo.Ext = getExt(file.Name)
				fileInfo.Size = int64(len(file.Content))

				// Bookmark file to disk
				dst := path.Join(p.uploadDirPath, fileInfo.Name)
				if err := saveFile(bytes.NewReader(file.Content), dst); err != nil {
					errCh <- err
					cancel()
					return
				}
				fileInfo.URL = fmt.Sprintf("/uploads/%s", fileInfo.Name)

				// If file is not an image, skip to extract its thumbnail, width, and height
				if !file.IsImage() {
					mu.Lock()
					fileInfos = append(fileInfos, &fileInfo)
					mu.Unlock()
					return
				}

				// Get image dimension and thumbnail
				width, height, thumbnail, err := GetImageDimensionAndThumbnail(bytes.NewReader(file.Content), DefaultWidthThumbnail, DefaultHeightThumbnail)
				if err != nil {
					// Go cannot process animated WebP and AVIF images yet. Skipping related errors
					// until support is added in future releases.
					// Ref: https://github.com/golang/go/issues/53364
					if file.Mime == GIFMimeType || file.Mime == AVIFMimeType {
						mu.Lock()
						fileInfos = append(fileInfos, &fileInfo)
						mu.Unlock()
						return
					}
					errCh <- err
					cancel()
					return
				}
				fileInfo.Width = int64(width)
				fileInfo.Height = int64(height)

				// Generate and save the thumbnail to disk
				thumbnailName := generateThumbnailName(file.Name, fileInfo.Hash)
				thumbnailPath := path.Join(p.uploadDirPath, thumbnailName)
				err = imaging.Save(thumbnail, thumbnailPath)
				if err != nil {
					errCh <- err
					cancel()
					return
				}

				fileInfo.ThumbnailURL = fmt.Sprintf("/uploads/%s", thumbnailName)
				mu.Lock()
				fileInfos = append(fileInfos, &fileInfo)
				mu.Unlock()
			}
		}(file)
	}

	wg.Wait()

	// Check if any errors were sent to the channel
	select {
	case err := <-errCh:
		// Return the error that caused the cancellation
		log.Debug("error: ", err)
		return nil, err
	default:
		// No errors, return fileInfos
		return fileInfos, nil
	}
}

func (p *localUploadProvider) RemoveFiles(fileInfos []*UploadedFileInfo, _ string) error {
	var wg sync.WaitGroup

	// Create a context with a cancellation mechanism
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Channel to signal errors
	errCh := make(chan error, len(fileInfos))

	// Limit the number of concurrent goroutines
	concurrencyLimit := p.concurrency
	semaphore := make(chan struct{}, concurrencyLimit)

	for _, fileInfo := range fileInfos {
		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore

		go func(fileInfo *UploadedFileInfo) {
			defer func() {
				<-semaphore // Release semaphore
				wg.Done()
			}()

			select {
			case <-ctx.Done():
				// Context cancelled, do not proceed
				return
			default:

				dst := path.Join(p.uploadDirPath, fileInfo.Name)
				if err := os.Remove(dst); err != nil {
					// Send error to channel and cancel other goroutines
					errCh <- err
					cancel()
					return
				}
			}
		}(fileInfo)
	}

	wg.Wait()

	// Check if any errors were sent to the channel
	select {
	case err := <-errCh:
		// Return the error that caused the cancellation
		log.Debug("err", err)
		return err
	default:
		// No errors, return fileInfos
		return nil
	}
}

func (p *localUploadProvider) GetFileData(fileInfo *UploadedFileInfo) ([]byte, error) {
	dst := path.Join(p.uploadDirPath, fileInfo.Name)

	file, err := os.Open(dst)
	if err != nil {
		log.Fatalf("failed to open file: %s", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read object data: %w", err)
	}

	return data, nil
}
