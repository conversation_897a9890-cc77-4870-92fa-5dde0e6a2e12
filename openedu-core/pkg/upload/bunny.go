package upload

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"sync"
)

type BunnyUploadProvider struct {
	ApiKey      string
	ApiURI      string
	LibraryID   string
	IFrameURL   string
	Concurrency int
}

type CreateVideoRequest struct {
	Title        string `json:"title"`
	CollectionID string `json:"collectionId"`
}

type UploadVideoRequest struct {
	VideoID  string
	Contents io.Reader
}

type ContentVideo struct {
	VideoLibraryID int    `json:"videoLibraryId"`
	Guid           string `json:"guid"`
	Title          string `json:"title"`
	Length         int    `json:"length"`
	Status         int    `json:"status"`
}

type UploadVideoResponse struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	StatusCode int    `json:"statusCode"`
}

type DeleteVideoResponse struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	StatusCode int    `json:"statusCode"`
}

type GetVideoRequest struct {
	VideoID string
}

type BunnyUploadRequest struct {
	Title        string    `json:"title"`
	Contents     io.Reader `json:"contents"`
	CollectionID string    `json:"collectionId"`
}

type BunnyUploadResponse struct {
	Guid      string `json:"guid"`
	IFrameURL string `json:"iFrameURL"`
	Duration  int    `json:"duration"`
}

func NewBunnyProvider(opts *BunnyUploadProvider) *BunnyUploadProvider {
	return opts
}

func GetPublicBunnyProvider() *BunnyUploadProvider {
	return NewBunnyProvider(&BunnyUploadProvider{
		ApiKey:      setting.UploadSetting.UploadPublicBunnyAPIKey,
		ApiURI:      setting.UploadSetting.UploadBunnyApiURI,
		LibraryID:   setting.UploadSetting.UploadPublicBunnyLibraryID,
		IFrameURL:   setting.UploadSetting.UploadBunnyIFrameURL,
		Concurrency: ConcurrencyLimit,
	})
}

func GetPrivateBunnyProvider() *BunnyUploadProvider {
	return NewBunnyProvider(&BunnyUploadProvider{
		ApiKey:      setting.UploadSetting.UploadPrivateBunnyAPIKey,
		ApiURI:      setting.UploadSetting.UploadBunnyApiURI,
		LibraryID:   setting.UploadSetting.UploadPrivateBunnyLibraryID,
		IFrameURL:   setting.UploadSetting.UploadBunnyIFrameURL,
		Concurrency: ConcurrencyLimit,
	})
}

func (s *BunnyUploadProvider) CreateVideo(req *CreateVideoRequest) (*ContentVideo, error) {
	createVideoURL := fmt.Sprintf("%s/%s/videos", s.ApiURI, s.LibraryID)

	// Make an HTTP POST
	body, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	httpReq, pErr := http.NewRequest("POST", createVideoURL, bytes.NewBuffer(body))
	if pErr != nil {
		return nil, pErr
	}

	httpReq.Header.Set("AccessKey", s.ApiKey)
	httpReq.Header.Set("accept", "application/json")
	httpReq.Header.Set("content-type", "application/json")

	resp, qErr := http.DefaultClient.Do(httpReq)
	if qErr != nil {
		return nil, qErr
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	var content ContentVideo

	if err := json.Unmarshal(respBody, &content); err != nil {
		return nil, err
	}

	return &content, nil
}

func (s *BunnyUploadProvider) UploadVideoContent(req *UploadVideoRequest) (*UploadVideoResponse, error) {
	createVideoURL := fmt.Sprintf("%s/%s/videos/%s", s.ApiURI, s.LibraryID, req.VideoID)

	// Make an HTTP PUT
	httpReq, pErr := http.NewRequest("PUT", createVideoURL, req.Contents)
	if pErr != nil {
		return nil, pErr
	}

	httpReq.Header.Set("AccessKey", s.ApiKey)
	httpReq.Header.Set("accept", "application/json")

	resp, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	var content UploadVideoResponse

	if err := json.Unmarshal(respBody, &content); err != nil {
		return nil, err
	}

	return &content, nil
}

func (s *BunnyUploadProvider) GetVideoById(req *GetVideoRequest) (*ContentVideo, error) {
	getVideoURL := fmt.Sprintf("%s/%s/videos/%s", s.ApiURI, s.LibraryID, req.VideoID)

	// Make an HTTP GET
	httpReq, pErr := http.NewRequest("GET", getVideoURL, nil)
	if pErr != nil {
		return nil, pErr
	}

	httpReq.Header.Set("AccessKey", s.ApiKey)
	httpReq.Header.Set("accept", "application/json")
	httpReq.Header.Set("content-type", "application/json")

	resp, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	var content ContentVideo

	if err := json.Unmarshal(respBody, &content); err != nil {
		return nil, err
	}

	return &content, nil
}

func (s *BunnyUploadProvider) BunnyUploader(req *BunnyUploadRequest) (*BunnyUploadResponse, error) {
	// Create Video
	video, err := s.CreateVideo(&CreateVideoRequest{
		Title:        req.Title,
		CollectionID: req.CollectionID,
	})

	if err != nil {
		return nil, err
	}

	if video.Status != 0 {
		return nil, fmt.Errorf("bunny fail to call api create video")
	}

	// Upload Video
	resp, uErr := s.UploadVideoContent(&UploadVideoRequest{
		VideoID:  video.Guid,
		Contents: req.Contents,
	})

	if uErr != nil {
		return nil, uErr
	}

	if resp.StatusCode != util.SuccessStatusCode {
		return nil, fmt.Errorf("bunny fail to call api upload video")
	}

	// Get Video
	videoUploaded, gErr := s.GetVideoById(&GetVideoRequest{
		VideoID: video.Guid,
	})

	if gErr != nil {
		return nil, gErr
	}

	if videoUploaded.Status == int(util.UploadFailed) || videoUploaded.Status == int(util.Error) {
		return nil, fmt.Errorf("bunny fail to upload video")
	}

	return &BunnyUploadResponse{
		Guid:      video.Guid,
		IFrameURL: fmt.Sprintf("%s/%s/%s", s.IFrameURL, s.LibraryID, videoUploaded.Guid),
		Duration:  videoUploaded.Length,
	}, nil
}

func (s *BunnyUploadProvider) UploadFiles(files []*File, _ string) ([]*UploadedFileInfo, error) {
	var fileInfos []*UploadedFileInfo
	var wg sync.WaitGroup
	var mu sync.Mutex

	// Create a context with a cancellation mechanism
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Channel to signal errors
	errCh := make(chan error, len(files))

	// Limit the number of concurrent goroutines
	concurrencyLimit := s.Concurrency
	semaphore := make(chan struct{}, concurrencyLimit)

	for _, file := range files {
		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore

		go func(file *File) {
			defer func() {
				<-semaphore // Release semaphore
				wg.Done()
			}()
			select {
			case <-ctx.Done():
				// Context cancelled, do not proceed
				return
			default:
				var fileInfo UploadedFileInfo
				fileInfo.Name = generateFileName(file.Name, file.Hash)
				fileInfo.Hash = file.Hash
				fileInfo.Mime = file.Mime
				fileInfo.Ext = getExt(file.Name)
				fileInfo.Size = int64(len(file.Content))

				// Upload file to Bunny
				uploadedFile, err := s.BunnyUploader(&BunnyUploadRequest{
					Title:        fileInfo.Name,
					CollectionID: "",
					Contents:     bytes.NewReader(file.Content),
				})

				if err != nil {
					errCh <- err
					cancel()
					return
				}

				fileInfo.URL = uploadedFile.IFrameURL
				fileInfo.BunnyVideoID = uploadedFile.Guid
				fileInfo.BunnyLibraryID = s.LibraryID
				fileInfo.Duration = int64(uploadedFile.Duration)
				if file.IsVideo() {
					mu.Lock()
					fileInfos = append(fileInfos, &fileInfo)
					mu.Unlock()
					return
				}

				mu.Lock()
				fileInfos = append(fileInfos, &fileInfo)
				mu.Unlock()
			}

		}(file)
	}

	wg.Wait()
	select {
	case err := <-errCh:
		return nil, err
	default:
		return fileInfos, nil
	}
}

func (s *BunnyUploadProvider) RemoveFiles(fileInfos []*UploadedFileInfo, _ string) error {
	if len(fileInfos) == 0 {
		return nil
	}

	for _, fileInfo := range fileInfos {
		deleteVideoURL := fmt.Sprintf("%s/%s/videos/%s", s.ApiURI, s.LibraryID, fileInfo.BunnyVideoID)
		// Make an HTTP DELETE
		httpReq, pErr := http.NewRequest("DELETE", deleteVideoURL, nil)
		if pErr != nil {
			return pErr
		}

		httpReq.Header.Set("AccessKey", s.ApiKey)
		httpReq.Header.Set("accept", "application/json")

		resp, err := http.DefaultClient.Do(httpReq)
		if err != nil {
			return err
		}

		defer resp.Body.Close()
	}

	return nil
}

func (s *BunnyUploadProvider) GetFileData(_ *UploadedFileInfo) ([]byte, error) {
	return nil, errors.New("bunny not support get file data")
}
