package upload

import (
	"bytes"
	"fmt"
	"image"
	"io"
	"mime/multipart"
	"openedu-core/pkg/file"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"os"
	"path"
	"strings"

	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"

	"github.com/nfnt/resize"
	_ "golang.org/x/image/bmp"
	_ "golang.org/x/image/tiff"
	_ "golang.org/x/image/webp"
)

const (
	GIFMimeType            = "image/gif"
	AVIFMimeType           = "image/avif"
	DefaultHeightThumbnail = 400
	DefaultWidthThumbnail  = 400
)

// GetImageDimensionAndThumbnail returns image's width, height and thumbnail
func GetImageDimensionAndThumbnail(r io.Reader, width int, height int) (int, int, image.Image, error) {
	data, err := io.ReadAll(r)
	if err != nil {
		return 0, 0, nil, err
	}

	reader := bytes.NewReader(data)
	img, _, err := image.Decode(reader)
	if err != nil {
		return 0, 0, nil, err
	}

	// thumbnail := resize.Resize(100, 100, img, resize.Lanczos3)
	thumbnail := resize.Resize(uint(width), uint(height), img, resize.Lanczos3)
	return img.Bounds().Dx(), img.Bounds().Dy(), thumbnail, nil
}

// GetImageName get image name
func GetImageName(name string) string {
	ext := path.Ext(name)
	fileName := strings.TrimSuffix(name, ext)
	fileName = util.EncodeMD5(fileName)

	return fileName + ext
}

// GetImagePath get save path
func GetImagePath() string {
	return setting.AppSetting.ImageSavePath
}

// GetImageFullPath get full save path
func GetImageFullPath() string {
	return setting.AppSetting.RuntimeRootPath + GetImagePath()
}

// CheckImageExt check image file ext
func CheckImageExt(fileName string) bool {
	ext := file.GetExt(fileName)
	for _, allowExt := range setting.AppSetting.ImageAllowExts {
		if strings.ToUpper(allowExt) == strings.ToUpper(ext) {
			return true
		}
	}

	return false
}

// CheckImageSize check image size
func CheckImageSize(f multipart.File) bool {
	size, err := file.GetSize(f)
	if err != nil {
		log.Warn(err)
		return false
	}

	return size <= setting.AppSetting.ImageMaxSize
}

// CheckImage check if the file exists
func CheckImage(src string) error {
	dir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("os.Getwd err: %v", err)
	}

	err = file.IsNotExistMkDir(dir + "/" + src)
	if err != nil {
		return fmt.Errorf("file.IsNotExistMkDir err: %v", err)
	}

	perm := file.CheckPermission(src)
	if perm == true {
		return fmt.Errorf("file.CheckPermission Permission denied src: %s", src)
	}

	return nil
}
