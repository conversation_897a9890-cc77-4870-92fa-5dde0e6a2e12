package communicationdto

type BlogType string

const (
	BlogTypePersonal BlogType = "personal"
	BlogTypeOrg      BlogType = "org"
)

type BlogResp struct {
	Model
	AuthorID      string   `json:"author_id,omitempty"`
	Cuid          string   `json:"cuid,omitempty" `
	Title         string   `json:"title,omitempty"`
	BlogType      BlogType `json:"blog_type,omitempty"`
	Slug          string   `json:"slug,omitempty"`
	PubDate       int      `json:"pub_date" `
	PubRejectDate int      `json:"pub_reject_date" `
	View          int      `json:"view" `
}

type ListBlogsResponse struct {
	Results    []*BlogResp `json:"results"`
	Pagination *Pagination `json:"pagination"`
}
