package communicationdto

type SendEmailRequest struct {
	User        *User                           `json:"user"`
	Org         *Organization                   `json:"org"`
	Event       EventType                       `json:"event"`
	Code        *EmailCodeType                  `json:"code"`
	ExtendDatas map[EmailParamField]interface{} `json:"extend_datas"`
	IsQueue     bool                            `json:"is_queue"`
	From        string                          `json:"from"`
}

type CreateEmailTemplateRequest struct {
	Name    string        `json:"name" validate:"required"`
	Subject string        `json:"subject"`
	Html    string        `json:"html"`
	Json    string        `json:"json"`
	Code    EmailCodeType `json:"code"`
	User    *User         `json:"user"`
	Org     *Organization `json:"org"`
}

type UpdateEmailTemplateRequest struct {
	Name    string        `json:"name" `
	Subject string        `json:"subject"`
	Html    string        `json:"html"`
	Json    string        `json:"json"`
	Code    EmailCodeType `json:"code"`
	User    *User         `json:"user"`
	Org     *Organization `json:"org"`
}

type DeleteEmailTemplateRequest struct {
	TemplateID string        `validate:"required" json:"template_id"`
	User       *User         `validate:"required" json:"user"`
	Org        *Organization `validate:"required" json:"org"`
}

type InitDefaultEmailOrgRequest struct {
	Org *Organization `validate:"required" json:"org"`
}

type EmailTemplateDataParams struct {
	FullName            string `json:"full_name"`
	Email               string `json:"email"`
	Password            string `json:"password"`
	Phone               string `json:"phone_number"`
	OrgName             string `json:"org_name"`
	OrgURL              string `json:"org_url"`
	OrgAdminURL         string `json:"org_admin_url"`
	CreatorURL          string `json:"creator_url"`
	VerifyEmailLink     string `json:"verify_email_link"`
	ResetPasswordLink   string `json:"reset_password_link"`
	SetPasswordLink     string `json:"set_password_link"`
	InviteCreatorLink   string `json:"invite_creator_link"`
	InviteUserLink      string `json:"invite_user_link"`
	RejectionReason     string `json:"rejection_reason"`
	PolicyURL           string `json:"policy_url"`
	OpenEduSupportEmail string `json:"openedu_support_email"`
	OpenEduSupportPhone string `json:"openedu_support_phone"`
	OTP                 string `json:"otp"`
}

type EmailTemplateRequest struct {
	Name      string                 `json:"name"`
	Subject   string                 `json:"subject"`
	Html      string                 `json:"html"`
	Json      string                 `json:"json"`
	Variables []EmailTemplateVarName `json:"variables"`
}

type ListEmailTemplateResponse struct {
	Results    []*EmailTemplate `json:"results"`
	Pagination *Pagination      `json:"pagination"`
}

type PreviewEmailTemplateRequest struct {
	Org        *Organization           `json:"org" validate:"required"`
	Emails     []string                `json:"emails" validate:"required"`
	Data       EmailTemplateDataParams `json:"data" validate:"required"`
	TemplateID string                  `json:"template_id" validate:"required"`
}

type EventType string

const (
	EventRegister         EventType = "REGISTER"
	EventResetPassword    EventType = "RESET_PASSWORD"
	EventExternalRegister EventType = "EXTERNAL_REGISTER"
	EventInviteCreator    EventType = "INVITE_CREATOR"
	EventInviteUser       EventType = "INVITE_USER"
	EventInviteReferrer   EventType = "INVITE_REFERRER"
	EventSendOTP          EventType = "SEND_OTP"

	EventApproveRegisterOrgNewUser      EventType = "APPROVE_REGISTER_ORG_NEW_USER"
	EventApproveRegisterOrgExistingUser EventType = "APPROVE_REGISTER_ORG_EXISTING_USER"
	EventDeactivateOrg                  EventType = "DEACTIVATE_ORG"

	//APPROVE_CREATOR EventType = "APPROVE_CREATOR"
	// => Separate to EventApproveRegisterCreatorNewUser and EventApproveRegisterCreatorExistingUser for easier
	// to maintain and debugging

	//Writer
	EventApproveRegisterWriterNewUser      EventType = "APPROVE_REGISTER_WRITER_NEW_USER"
	EventApproveRegisterWriterExistingUser EventType = "APPROVE_REGISTER_WRITER_EXISTING_USER"

	//Creator
	EventApproveRegisterCreatorNewUser      EventType = "APPROVE_REGISTER_CREATOR_NEW_USER"
	EventApproveRegisterCreatorExistingUser EventType = "APPROVE_REGISTER_CREATOR_EXISTING_USER"
	EventInviteCreatorBeforeAccept          EventType = "INVITE_CREATOR_BEFORE_ACCEPT"
	EventAcceptInviteCreatorNewUser         EventType = "ACCEPT_INVITE_CREATOR_NEW_USER"
	EventAcceptInviteCreatorExistingUser    EventType = "ACCEPT_INVITE_CREATOR_EXISTING_USER"
	// Editor writer
	EventInviteOrgEditor EventType = "INVITE_ORG_EDITOR"
	EventInviteOrgWriter EventType = "INVITE_ORG_WRITER"

	// enroll success
	EnrollCourseSuccessEn EventType = "ENROLL_COURSE_SUCCESS_EN"
	EnrollCourseSuccessVi EventType = "ENROLL_COURSE_SUCCESS_VI"

	//USER
	EventInviteUserBeforeAccept       EventType = "INVITE_USER_BEFORE_ACCEPT"
	EventAcceptInviteUserNewUser      EventType = "ACCEPT_INVITE_USER_NEW_USER"
	EventAcceptInviteUserExistingUser EventType = "ACCEPT_INVITE_USER_EXISTING_USER"

	EventAcceptInviteEditorNewUser      EventType = "ACCEPT_INVITE_EDITOR_NEW_USER"
	EventAcceptInviteEditorExistingUser EventType = "ACCEPT_INVITE_EDITOR_EXISTING_USER"

	EventAcceptInviteWriterNewUser      EventType = "ACCEPT_INVITE_WRITER_NEW_USER"
	EventAcceptInviteWriterExistingUser EventType = "ACCEPT_INVITE_WRITER_EXISTING_USER"

	EventEnrolledCourse             EventType = "enrolled_course"
	EventSpecificTime               EventType = "specific_time"
	EventCompletedCourse            EventType = "completed_course"
	EventNDaysAfterCourseEnrollment EventType = "n_days_after_course_enrollment"
	EventStartedSection             EventType = "started_section"
	EventCompletedSection           EventType = "completed_section"
	EventStartedLesson              EventType = "started_lesson"
	EventCompletedLesson            EventType = "completed_lesson"
	EventClickOn                    EventType = "clicked_on"
	EventNDaysAfterSettingTrigger   EventType = "n_days_after_setting_trigger"
	EventReachNumSubmissions        EventType = "reach_num_submissions"
	EventCompletedQuiz              EventType = "completed_quiz"
	EventPaymentInsufficient        EventType = "payment_insufficient"
	EventPaymentOver                EventType = "payment_over"
	EventBecomeAffiliate            EventType = "become_affiliate"
	EventRejectRegisterCreator      EventType = "reject_register_creator"
	EventRejectRegisterWriter       EventType = "reject_register_writer"
	EventRejectRegisterOrg          EventType = "reject_register_org"

	EventReferralNewUserProgramVi EventType = "referral_invite_referee_vi"
	EventReferralNewUserProgramEn EventType = "referral_invite_referee_en"

	EventAiGovernmentInviteModNewAccount   EventType = "ai_government_invite_mod_new_account"
	EventAiGovernmentInviteModExistAccount EventType = "ai_government_invite_mod_exist_account"

	// SUBSCRIPTION
	EventSubscribeHutechPricingPlan EventType = "SUBSCRIBE_HUTECH_PRICING_PLAN"
	EventSubscribeW3hfPricingPlan   EventType = "SUBSCRIBE_W3HF_PRICING_PLAN"
	EventSubscribeAIGovPricingPlan  EventType = "SUBSCRIBE_AI_GOV_PRICING_PLAN"
)

func (e EventType) String() string {
	return string(e)
}

func (e EventType) IsValidFormTriggerEvent() bool {
	switch e {
	case EventEnrolledCourse,
		EventSpecificTime,
		EventCompletedCourse,
		EventNDaysAfterCourseEnrollment,
		EventStartedSection,
		EventCompletedSection,
		EventStartedLesson,
		EventCompletedLesson,
		EventClickOn,
		EventNDaysAfterSettingTrigger,
		EventReachNumSubmissions,
		EventCompletedQuiz:
		return true

	default:
		return false
	}
}

type EmailCodeType string

type EmailTemplateVarName string

type EmailLinkType string

type EmailParamField string

const (
	EmailParamFullName              EmailParamField = "full_name"
	EmailParamEmail                 EmailParamField = "email"
	EmailParamUsername              EmailParamField = "username"
	EmailParamPassword              EmailParamField = "password"
	EmailParamPhone                 EmailParamField = "phone_number"
	EmailParamOrgName               EmailParamField = "org_name"
	EmailParamOrgURL                EmailParamField = "org_url"
	EmailParamOrgAdminURL           EmailParamField = "org_admin_url"
	EmailParamCreatorURL            EmailParamField = "creator_url"
	EmailParamVerifyEmailLink       EmailParamField = "verify_email_link"
	EmailParamResetPasswordLink     EmailParamField = "reset_password_link"
	EmailParamSetPasswordLink       EmailParamField = "set_password_link"
	EmailParamInviteCreatorLink     EmailParamField = "invite_creator_link"
	EmailParamInviteUserLink        EmailParamField = "invite_user_link"
	EmailParamRejectionReason       EmailParamField = "rejection_reason"
	EmailParamPolicyURL             EmailParamField = "policy_url"
	EmailParamOpenEduSupportEmail   EmailParamField = "openedu_support_email"
	EmailParamOpenEduSupportPhone   EmailParamField = "openedu_support_phone"
	EmailParamOTPCode               EmailParamField = "otp"
	EmailParamRole                  EmailParamField = "role"
	EmailParamCourseURL             EmailParamField = "course_url"
	EmailParamCourseName            EmailParamField = "course_name"
	EmailParamCreatorName           EmailParamField = "creator_name"
	EmailParamZaloChannel           EmailParamField = "zalo_channel"
	EmailParamTelegramChannel       EmailParamField = "telegram_channel"
	EmailParamUserUrl               EmailParamField = "user_url"
	EmailParamAffiliateCode         EmailParamField = "affiliate_code"
	EmailParamPercentageRate        EmailParamField = "percentage_rate"
	EmailParamAffiliateLink         EmailParamField = "affiliate_link"
	EmailParamAffiliateDashboardUrl EmailParamField = "affiliate_dashboard_url"
	EmailParamPaymentAmount         EmailParamField = "payment_amount"
	EmailParamEvent                 EmailParamField = "event"
	EmailParamUser                  EmailParamField = "user"
	EmailParamUserToken             EmailParamField = "user_token"
	EmailParamOrg                   EmailParamField = "org"
	EmailParamCourse                EmailParamField = "course"
	EmailParamNextPath              EmailParamField = "next_path"

	EmailParamReferralUserLink      EmailParamField = "referral_user_link"
	EmailParamReferralReferrerName  EmailParamField = "referrer_name"
	EmailParamReferralReferrerEmail EmailParamField = "referrer_email"
	EmailParamReferralReferralCode  EmailParamField = "referral_code"
)

type EmailTemplate struct {
	Model
	OrgID     string                 `json:"org_id" gorm:"not null;type:varchar(20)"`
	UserID    string                 `json:"user_id"`
	User      *User                  `json:"user"`
	Code      EmailCodeType          `json:"code"`
	Name      string                 `json:"name"`
	Subject   string                 `json:"subject" gorm:"type:varchar(1000)"`
	Html      string                 `json:"html" gorm:"type:text"`
	Json      string                 `json:"json" gorm:"type:text"`
	Variables []EmailTemplateVarName `json:"variables" bson:"variables"`
}

type EmailTemplateQuery struct {
	ID             *string       `json:"id" form:"id"`
	Code           EmailCodeType `json:"code"`
	Name           *string       `json:"name" form:"name"`
	OrgID          *string       `json:"org_id" form:"org_id"`
	UserID         *string       `json:"user_id" form:"user_id"`
	IncludeDeleted *bool
}

type MapEmailParams map[EmailParamField]interface{}

const (
	//Email template
	EmailCodeVerifyEmail                    EmailCodeType = "verify_email"
	EmailCodeResetPassword                  EmailCodeType = "reset_password"
	EmailCodeSetPassword                    EmailCodeType = "set_password"
	EmailCodeApproveRegisterOrgNewUser      EmailCodeType = "approve_register_org_new_user"
	EmailCodeApproveRegisterOrgExistingUser EmailCodeType = "approve_register_org_existing_user"
	EmailCodeRejectRegisterOrg              EmailCodeType = "reject_register_org"
	EmailCodeAddOrgModerator                EmailCodeType = "add_org_moderator"
	EmailCodeAddOrgAdmin                    EmailCodeType = "add_org_admin"
	EmailCodeAddOrgModeratorNewUser         EmailCodeType = "add_org_moderator_new_user"
	EmailCodeAddOrgAdminNewUser             EmailCodeType = "add_org_admin_new_user"
	EmailCodeDeactivateOrg                  EmailCodeType = "deactivate_org"
	EmailCodeOthers                         EmailCodeType = "others"
	EmailCodeSendOTP                        EmailCodeType = "send_otp"
	EmailReferralInviteRefereeEn            EmailCodeType = "referral_invite_referee_en"
	EmailReferralInviteRefereeVi            EmailCodeType = "referral_invite_referee_vi"

	////// Creator
	EmailCodeApproveRegisterCreatorNewUser      EmailCodeType = "approve_register_creator_new_user"
	EmailCodeApproveRegisterCreatorExistingUser EmailCodeType = "approve_register_creator_existing_user"
	EmailCodeRejectRegisterCreator              EmailCodeType = "reject_register_creator"
	EmailCodeAcceptInviteCreatorNewUser         EmailCodeType = "accept_invite_creator_new_user"
	EmailCodeAcceptInviteCreatorExistingUser    EmailCodeType = "accept_invite_creator_existing_user"
	EmailCodeInviteCreatorBeforeAccept          EmailCodeType = "invite_creator_before_accept"
	////// User
	EmailCodeAcceptInviteNewUser      EmailCodeType = "accept_invite_new_user"
	EmailCodeAcceptInviteExistingUser EmailCodeType = "accept_invite_existing_user"
	EmailCodeInviteUserBeforeAccept   EmailCodeType = "invite_user_before_accept"
	///// Writer
	EmailCodeApproveRegisterWriterNewUser      EmailCodeType = "approve_register_writer_new_user"
	EmailCodeApproveRegisterWriterExistingUser EmailCodeType = "approve_register_writer_existing_user"
	EmailCodeRejectRegisterWriter              EmailCodeType = "reject_register_writer"

	// Enroll course
	EmailCodeEnrollCourseSuccessEn EmailCodeType = "enroll_course_success_en"
	EmailCodeEnrollCourseSuccessVi EmailCodeType = "enroll_course_success_vi"
	//Payment
	EmailCodePaymentSucess       EmailCodeType = "payment sucess"
	EmailCodePaymentInsufficient EmailCodeType = "payment insufficient"
	EmailCodePaymentOver         EmailCodeType = "payment over"
	// Affiliate
	EmailCodeBecomeAffiliate EmailCodeType = "become_affiliate"
	// Subscription
	EmailCodeSubscribeHutechPricingPlan EmailCodeType = "subscribe_hutech_pricing_plan"
	EmailCodeSubscribeW3hfPricingPlan   EmailCodeType = "subscribe_w3hf_pricing_plan"
	//EnrollmentCourse
	EmailCodeEnrollmentCourseSaga EmailCodeType = "enrollment_course_saga"

	EmailCodeRegisterUser       EmailCodeType = "user_register"
	EmailCodeNewCourseContent   EmailCodeType = "new_course_content"
	EmailCodeCompleteHalfCourse EmailCodeType = "complete_half_course"
	EmailCodeCompleteCourse     EmailCodeType = "complete_course"
	EmailCodeRemind             EmailCodeType = "remind_learner"
	EmailCodeGetCertificate     EmailCodeType = "get_certificate"
)
