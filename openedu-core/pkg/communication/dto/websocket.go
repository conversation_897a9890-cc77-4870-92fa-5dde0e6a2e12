package communicationdto

type WebsocketEventType string

const (
	WebsocketEventHello          WebsocketEventType = "hello"
	WebsocketEventCourse         WebsocketEventType = "course"
	WebsocketEventOrganization   WebsocketEventType = "organization"
	WebsocketEventSection        WebsocketEventType = "section"
	WebsocketEventLesson         WebsocketEventType = "lesson"
	WebsocketEventFormAnswer     WebsocketEventType = "form_answer"
	WebsocketEventVideo          WebsocketEventType = "video"
	WebsocketEventPayment        WebsocketEventType = "payment"
	WebsocketEventBlog           WebsocketEventType = "blog"
	WebsocketEventUser           WebsocketEventType = "user"
	WebsocketEventBadge          WebsocketEventType = "badge"
	WebsocketEventCertificate    WebsocketEventType = "certificate"
	WebsocketEventAIBlogStatus   WebsocketEventType = "ai_blog_status"
	WebsocketEventAICourseStatus WebsocketEventType = "ai_course_status"
)

type WebsocketBroadcastParams struct {
	OmitUserIDs      []string `json:"omit_user_ids"`      // broadcast is omitted for users listed here
	UserIDs          []string `json:"user_ids"`           // broadcast only occurs for these users listed here
	UserID           string   `json:"user_id"`            // broadcast only occurs for this user
	CourseID         string   `json:"course_id"`          // broadcast only occurs for users in this course
	OrgID            string   `json:"org_id"`             // broadcast only occurs for users in this organization
	ConnectionId     string   `json:"connection_id"`      // broadcast only occurs for this connection
	OmitConnectionId string   `json:"omit_connection_id"` // broadcast is omitted for this connection
	All              bool     `json:"all"`
}

type WebsocketMessageRequest struct {
	Event     WebsocketEventType       `json:"event" validate:"required"`
	Data      map[string]any           `json:"data" validate:"required"`
	Broadcast WebsocketBroadcastParams `json:"broadcast" validate:"required"`
}
