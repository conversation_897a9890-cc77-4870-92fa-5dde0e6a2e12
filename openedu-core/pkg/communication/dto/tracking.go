package communicationdto

type VerbType string

const (
	Created  VerbType = "created"
	Updated  VerbType = "updated"
	Deleted  VerbType = "deleted"
	Started  VerbType = "started"
	Stopped  VerbType = "stopped"
	Paid     VerbType = "paid"
	Enrolled VerbType = "enrolled"
	Ordered  VerbType = "ordered"
	Referred VerbType = "referred"
)

type ObjectType string

const (
	OrganizationObject ObjectType = "organization"
	CourseObject       ObjectType = "course"
	SectionObject      ObjectType = "section"
	LessonObject       ObjectType = "lesson"
	Form_answerObject  ObjectType = "form_answer"
	VideoObject        ObjectType = "video"
	PaymentObject      ObjectType = "payment"
	BlogObject         ObjectType = "blog"
)

type Context string

const (
	SourceContext         Context = "source"
	BlogViewContext       Context = "blog_view"
	AuthorBlogViewContext Context = "author_blog_view"
	CourseContext         Context = "course_context"
)

type SourceType string

const (
	Email    SourceType = "email"
	Zalo     SourceType = "zalo"
	FB       SourceType = "fb"
	Ads      SourceType = "ads"
	Direct   SourceType = "direct"
	JobFair  SourceType = "job_fair"
	Codemely SourceType = "code_mely"
	Discord  SourceType = "discord"
)

type ListTrackingResponse struct {
	Results    []*TrackingResponse `json:"results"`
	Pagination *Pagination         `json:"pagination"`
}

type TrackingResponse struct {
	ID           string    `json:"id,omitempty"`
	ActorID      string    `json:"actor_id,omitempty"`
	Verb         VerbType  `json:"verb,omitempty"`
	Object       ModelName `json:"object,omitempty"`
	ObjectID     string    `json:"object_id,omitempty"`
	Context      Context   `json:"context,omitempty"`
	ContextValue any       `json:"context_value,omitempty"`
	OrgID        string    `json:"org_id,omitempty"`
	OrgSchema    string    `json:"org_schema,omitempty"`
	CreateAt     int64     `json:"create_at,omitempty"`
	UpdateAt     int64     `json:"update_at,omitempty"`
	DeleteAt     int64     `json:"delete_at,omitempty"`
	IsValid      bool      `json:"is_valid,omitempty"`
	CreateDate   string    `json:"create_date" excel:"Create Date"`
	ActorEmail   string    `json:"-" excel:"-"`
}

type TrackingRequest struct {
	ActorID      string    `json:"actor_id" validate:"required"`
	Verb         VerbType  `json:"verb" validate:"required"`
	Object       ModelName `json:"object" validate:"required"`
	ObjectID     string    `json:"object_id" validate:"required"`
	Context      Context   `json:"context" validate:"required"`
	ContextValue any       `json:"context_value"`
	OrgID        string    `json:"org_id" validate:"required"`
	OrgSchema    string    `json:"org_schema" validate:"required"`
	IsValid      bool      `json:"is_valid,omitempty"`
}

type TrackingQuery struct {
	ID                *string    `form:"id" json:"id,omitempty"`
	ActorID           *string    `form:"actor_id" json:"actor_id,omitempty"`
	ActorIDIn         []string   `form:"actor_id_in,omitempty" json:"actor_id_in,omitempty"`
	Verb              *VerbType  `form:"verb" json:"verb,omitempty"`
	Object            *ModelName `form:"object" json:"object,omitempty"`
	ObjectID          *string    `form:"object_id" json:"object_id,omitempty"`
	ObjectIDIn        []string   `form:"object_id_in,omitempty" json:"object_id_in,omitempty"`
	Context           *Context   `form:"context" json:"context,omitempty"`
	ContextValue      *string    `form:"context_value" json:"context_value,omitempty"`
	IncludeDeleted    *bool      `form:"include_deleted" json:"include_deleted,omitempty"`
	TrackingDateStart *int64     `form:"tracking_date_start" json:"tracking_date_start,omitempty"`
	TrackingDateEnd   *int64     `form:"tracking_date_end" json:"tracking_date_end,omitempty"`
	IDIn              []string   `form:"id_in" json:"id_in,omitempty"`
	IsValid           *bool      `form:"is_valid" json:"is_valid,omitempty"`
}
type CountRefUserResp struct {
	UserID     string                   `json:"user_id"`
	RefCount   int64                    `json:"count"`
	Email      string                   `json:"email"`
	DislayName string                   `json:"dislay_name"`
	Phone      string                   `json:"phone"`
	Active     bool                     `json:"active"`
	RefBy      []map[string]interface{} `json:"ref_by"`
}

type ListRefCountResponse struct {
	Results    []*CountRefUserResp `json:"results"`
	Pagination *Pagination         `json:"pagination"`
}

type UpdateRefTrackingRequest struct {
	ActorID      string `json:"actor_id" validate:"required"`
	ContextValue string `json:"context_value"`
	OrgID        string `json:"org_id" validate:"required"`
}

func GetValidSourceType(source string) string {
	if source == "" {
		return string(Direct)
	}
	// switch SourceType(source) {
	// case Email, Zalo, FB, Ads, Direct, JobFair, Codemely, Discord:
	// 	return source
	// default:
	// 	return string(Direct)
	// }
	return source
}

type ChatStatus string

const (
	ChatStatusManual     ChatStatus = "manual"
	ChatStatusPending    ChatStatus = "pending"
	ChatStatusGenerating ChatStatus = "generating"
	ChatStatusWaiting    ChatStatus = "waiting"
	ChatStatusCompleted  ChatStatus = "completed"
	ChatStatusFailed     ChatStatus = "failed"
	ChatStatusStopped    ChatStatus = "stopped"
	ChatStatusToolEnded  ChatStatus = "tool_ended"
	ChatStatusReasoning  ChatStatus = "reasoning"
)

type Message struct {
	SenderID  string     `json:"sender_id,omitempty" bson:"sender_id"`
	UserID    string     `json:"user_id,omitempty" bson:"user_id"`
	OrgID     string     `json:"org_id,omitempty" bson:"org_id"`
	OrgSchema string     `json:"org_schema,omitempty" bson:"org_schema"`
	Status    ChatStatus `json:"status" bson:"status"`
}

type MessageQuery struct {
	ID             *string  `form:"id,omitempty" json:"id,omitempty"`
	UserID         *string  `form:"user_id,omitempty" json:"user_id,omitempty"`
	UserIDIn       []string `form:"user_id_in,omitempty" json:"user_id_in,omitempty"`
	ConversationID *string  `form:"conversation_id,omitempty" json:"conversation_id,omitempty"`
	ContextID      *string  `form:"context_id,omitempty" json:"context_id,omitempty"`
	SenderID       *string  `form:"sender_id,omitempty" json:"sender_id,omitempty"`
	IsEdited       *bool    `form:"is_edited,omitempty" json:"is_edited,omitempty"`
	IsAI           *bool    `form:"is_ai,omitempty" json:"is_ai,omitempty"`
	IDIn           []string `form:"id_in,omitempty" json:"id_in,omitempty"`
	SenderIDIn     []string `form:"sender_id_in,omitempty" json:"sender_id_in,omitempty"`
	OrgID          *string  `form:"org_id,omitempty" json:"org_id,omitempty"`
	CreateAtLte    *int64   `form:"create_at_lte,omitempty" json:"create_at_lte,omitempty"`
	CreateAtGte    *int64   `form:"create_at_gte,omitempty" json:"create_at_gte,omitempty"`
	IDNotIn        []string `form:"id_not_in,omitempty" json:"id_not_in,omitempty"`
	CreateAtLt     *int64   `form:"create_at_lt,omitempty" json:"create_at_lt,omitempty"`
	CreateAtGt     *int64   `form:"create_at_gt,omitempty" json:"create_at_gt,omitempty"`
	IncludeDeleted *bool    `form:"include_deleted,omitempty" json:"include_deleted,omitempty"`
}

type ListMessageResponse struct {
	Results    []*Message  `json:"results"`
	Pagination *Pagination `json:"pagination"`
}
