package communicationdto

type ScheduledJobType string

type ScheduledJ<PERSON><PERSON>ey string

type JobExecutionStatus string

const (
	Success JobExecutionStatus = "success"
	Failed  JobExecutionStatus = "failed"

	OnceJobType ScheduledJobType = "once"
	CronJobType ScheduledJobType = "cron"

	PushNotificationOnceJobKey ScheduledJobKey = "push_noti_once"
	PushNotificationCronJobKey ScheduledJobKey = "push_noti_cron"
)
