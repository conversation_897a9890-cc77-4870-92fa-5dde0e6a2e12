package communicationdto

type Course struct {
	OrgID       string      `json:"org_id" gorm:"not null;type:varchar(20)"`
	Cuid        string      `json:"cuid" gorm:"not null;type:varchar(20)"` // course unique ID
	Name        string      `json:"name" gorm:"not null"`
	Slug        string      `json:"slug"`
	Description string      `json:"description" gorm:"type:text"`
	ShortDesc   string      `json:"short_desc" gorm:"type:text"`
	ThumbnailID string      `json:"thumbnail_id"`
	LearnMethod string      `json:"learn_method"`
	UserID      string      `json:"user_id" gorm:"not null"`
	Props       CourseProps `json:"props" gorm:"type:jsonb"`
	PubDate     int         `json:"pub_date" gorm:"type:int8;default:0"`
	StartDate   int         `json:"start_date" gorm:"type:int8;default:0"`
	EndDate     int         `json:"end_date" gorm:"type:int8;default:0"`
	Model
}

type CourseProps struct {
	SupportChannel  JSONB       `json:"support_channel"`
	DefaultLanguage string      `json:"default_language"`
	TelegramChannel string      `json:"telegram_channel"`
	PrivateChannels StringArray `json:"private_channels"`
}
