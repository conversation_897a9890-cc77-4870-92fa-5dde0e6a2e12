package communicationdto

type Organization struct {
	Model
	User        *User   `json:"user,omitempty"`
	UserID      string  `json:"user_id"`
	ThumbnailID *string `json:"thumbnail_id" gorm:"default:null"`
	Schema      string  `json:"schema" validate:"required" gorm:"unique,not null"`
	Name        string  `json:"name" validate:"required" gorm:"unique,not null"`
	Domain      string  `json:"domain" validate:"required" gorm:"unique,not null"`
	AltDomain   string  `json:"alt_domain" validate:"required" gorm:"unique,not null"`
	Active      bool    `json:"active" gorm:"default:true"`
	BannerID    *string `json:"banner_id" gorm:"default:null"`
}
