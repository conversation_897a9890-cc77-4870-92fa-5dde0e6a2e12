package communicationdto

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"math"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type Model struct {
	ID       string `json:"id"`
	CreateAt int    `json:"create_at"`
	UpdateAt int    `json:"update_at"`
	DeleteAt int    `json:"delete_at"`
}

type FindOneOptions struct {
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort" form:"sort"`
}

func (o *FindOneOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	return qb
}

type FindManyOptions struct {
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort" form:"sort"`
	Limit          *int                               `json:"limit" form:"limit"`
	Offset         *int                               `json:"offset" form:"offset"`
}

func (o *FindManyOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	if o.Limit != nil {
		qb = qb.Limit(*o.Limit)
	}

	if o.Offset != nil {
		qb = qb.Offset(*o.Offset)
	}

	return qb
}

type FindPageOptions struct {
	Preloads       []string                           `json:"preloads" form:"preloads"`
	CustomPreloads map[string]func(*gorm.DB) *gorm.DB `json:"custom_preloads" form:"-"`
	Sort           []string                           `json:"sort"  form:"sort"`
	Page           int                                `json:"page" form:"page"`
	PerPage        int                                `json:"per_page" form:"per_page"`
}

func (o *FindPageOptions) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	lo.ForEach(o.Preloads, func(clause string, _ int) {
		qb = qb.Preload(clause)
	})

	for field, preloadFn := range o.CustomPreloads {
		qb = qb.Preload(field, preloadFn)
	}

	lo.ForEach(o.Sort, func(clause string, _ int) {
		qb = qb.Order(clause)
	})

	limit := o.PerPage
	offset := (o.Page - 1) * o.PerPage
	qb = qb.Limit(limit).Offset(offset)
	return qb
}

type Pagination struct {
	Page       int `json:"page"`
	PerPage    int `json:"per_page"`
	TotalPages int `json:"total_pages"`
	TotalItems int `json:"total_items"`
}

func NewPagination(page, perPage, totalItems int) *Pagination {
	return &Pagination{
		Page:    page,
		PerPage: perPage,
		TotalPages: lo.If(totalItems == 0, 0).
			Else(int(math.Ceil(float64(totalItems) / float64(perPage)))),
		TotalItems: totalItems,
	}
}

type ModelName string

const (
	UserModelName             ModelName = "user"
	OrganizationModelName     ModelName = "organization"
	CourseModelName           ModelName = "course"
	SectionModelName          ModelName = "section"
	LessonModelName           ModelName = "lesson"
	FormAnswerModelName       ModelName = "form_answer"
	BlogModelName             ModelName = "blog"
	QuizModelName             ModelName = "quiz"
	QuizQuestionModelName     ModelName = "quiz_question"
	QuizQuestionItemModelName ModelName = "quiz_question_item"
	CertificateModelName      ModelName = "certificate"
	HTMLTemplateModelName     ModelName = "html_template"
	ReferrerModelName         ModelName = "referrer"
	ReferralModelName         ModelName = "referral"
	WalletModelName           ModelName = "wallet"
	ApprovalModelName         ModelName = "approval"
	OrderModelName            ModelName = "order"
	TransactionModelName      ModelName = "transaction"
	FileModelName             ModelName = "file"
)

type JSONB map[string]interface{}

func (j JSONB) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *JSONB) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type StringArray []string

func (p StringArray) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	return string(val), err
}

func (p *StringArray) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &p)
	if err != nil {
		return err
	}

	return nil
}
