package communication

import (
	"encoding/json"
	"openedu-core/pkg/e"
)

func decodeDataResponseToRawJson(dataBytes []byte) (json.RawMessage, *e.AppError) {
	var mapDataResp map[string]json.RawMessage
	if err := json.Unmarshal(dataBytes, &mapDataResp); err != nil {
		return nil, e.NewError500(e.Json_decode_error, err.Error())
	}
	dataResp, ok := mapDataResp["data"]
	if !ok {
		return nil, e.NewError500(e.Json_decode_error, "data not found")
	}
	var resultData json.RawMessage
	if err := json.Unmarshal(dataResp, &resultData); err != nil {
		return nil, e.NewError500(e.Json_decode_error, "data not found")
	}
	return resultData, nil
}
