package communication

import (
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	dto "openedu-core/pkg/communication/dto"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strings"
)

func (s *emailService) SendEmail(req *dto.SendEmailRequest) ([]byte, error) {
	_, body, err := s.httpClient.Post(emailPath+"/send", req, nil)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func (s *emailService) InitDefaultEmailTemplateForOrg(org *dto.Organization) error {
	req := dto.InitDefaultEmailOrgRequest{
		Org: org,
	}
	_, _, err := s.httpClient.Post(emailPath+"/init-default-org", req, nil)
	if err != nil {
		return err
	}

	return nil
}

func (s *emailService) CreateEmailTemplate(req *dto.CreateEmailTemplateRequest) ([]byte, error) {
	_, body, err := s.httpClient.Post(emailPath, req, nil)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func (s *emailService) UpdateEmailTemplate(req *dto.UpdateEmailTemplateRequest, templateID string) ([]byte, error) {
	_, body, err := s.httpClient.Put(fmt.Sprintf("%s/%s", emailPath, templateID), req, nil)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func (s *emailService) FindOneEmailTemplate(templateID string) (*dto.EmailTemplate, error) {
	findOneTemplatePath := fmt.Sprintf("%s/%s", emailPath, templateID)
	_, dataBytes, err := s.httpClient.Get(findOneTemplatePath, nil, nil)
	if err != nil {
		return nil, err
	}
	resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
	if decodeErr != nil {
		return nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
	}

	var dataResp dto.EmailTemplate
	if err := json.Unmarshal(resultData, &dataResp); err != nil {
		return nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
	}

	return &dataResp, nil

}

func (s *emailService) FindPageEmailTemplate(query *dto.EmailTemplateQuery, options *dto.FindPageOptions) ([]*dto.EmailTemplate, *dto.Pagination, error) {
	headers := map[string]string{}
	queryParams, stmErr := util.StructToMap(query)
	if stmErr != nil {
		return nil, nil, stmErr
	}
	optionsParams, stmErr := util.StructToMap(options)
	if stmErr != nil {
		return nil, nil, stmErr
	}

	_, dataBytes, err := s.httpClient.Get(emailPath, headers, util.MergeMap(queryParams, optionsParams))
	if err != nil {
		return nil, nil, err
	}

	resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
	if decodeErr != nil {
		return nil, nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
	}

	var dataResp dto.ListEmailTemplateResponse
	if err := json.Unmarshal(resultData, &dataResp); err != nil {
		return nil, nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
	}

	return dataResp.Results, dataResp.Pagination, nil
}

func (s *emailService) DeleteEmailTemplate(req *dto.DeleteEmailTemplateRequest) ([]byte, error) {
	deleteEmailTemplatePath := fmt.Sprintf("%s/%s", emailPath, req.TemplateID)
	_, body, err := s.httpClient.Delete(deleteEmailTemplatePath, req, nil)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func (s *emailService) PreviewEmailTemplate(req *dto.PreviewEmailTemplateRequest) ([]byte, error) {
	previewEmailTemplatePath := fmt.Sprintf("%s/%s/preview", emailPath, req.TemplateID)
	_, body, err := s.httpClient.Post(previewEmailTemplatePath, req, nil)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func (s *emailService) FindEmailTemplateVariables() (map[dto.EmailCodeType][]dto.EmailTemplateVarName, error) {
	findEmailParamsPath := fmt.Sprintf("%s/variables", emailPath)
	_, dataBytes, err := s.httpClient.Get(findEmailParamsPath, nil, nil)
	if err != nil {
		return nil, err
	}
	resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
	if decodeErr != nil {
		return nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
	}

	var dataResp map[dto.EmailCodeType][]dto.EmailTemplateVarName
	if err := json.Unmarshal(resultData, &dataResp); err != nil {
		return nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
	}

	return dataResp, nil

}

func (s *emailService) ParseTemplate(
	tmplName string,
	content string,
	vars map[string]interface{},
	funcs template.FuncMap,
) (string, error) {
	tmpl, err := template.New(tmplName).Funcs(funcs).Parse(content)
	if err != nil {
		log.Errorf("Parse email template %s error: %v", tmplName, err)
		return replaceVariables(content, vars), nil
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, vars); err != nil {
		return "", err
	}
	return buf.String(), nil
}

func replaceVariables(content string, vars map[string]interface{}) string {
	result := content
	for name, value := range vars {
		placeholder := fmt.Sprintf("{{.%s}}", name)
		replacement := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, replacement)
	}
	return result
}
