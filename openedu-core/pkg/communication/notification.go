package communication

import (
	"encoding/json"
	"fmt"
	communicationdto "openedu-core/pkg/communication/dto"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/queue/producer"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

func (s *notificationService) PushNotification(req *communicationdto.PushNotificationRequest) error {
	headers := map[string]string{}
	if req.Org != nil {
		headers["X-referrer"] = req.Org.AltDomain
	}

	_, _, err := s.httpClient.Post(notificationPath, req, headers)
	if err != nil {
		return err
	}

	return nil
}

func (s *notificationService) PushMultipleNotification(req []*communicationdto.PushNotificationRequest) error {
	for i := 0; i < len(req); i += BatchNotificationSize {
		end := i + BatchNotificationSize
		if end > len(req) {
			end = len(req)
		}

		batch := req[i:end]
		bytes, err := json.Marshal(batch)
		if err != nil {
			return fmt.Errorf("%w: failed to marshal request: %s", httpclient.ErrMakeRequest, err)
		}

		// Publish batch
		if err = s.producer.Publish(
			setting.RabbitMQSetting.Prefix+NotificationQueueName,
			&producer.Message{
				ID:          util.GenerateId(),
				Body:        bytes,
				ContentType: "application/json",
			},
		); err != nil {
			return fmt.Errorf("%w: failed to send amqp publishing request: %s", httpclient.ErrMakeRequest, err)
		}
	}

	return nil
}
