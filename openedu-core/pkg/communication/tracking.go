package communication

import (
	"encoding/json"
	"fmt"
	"net/http"
	dto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
)

func (s *trackingService) FindTopUserBlogView(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.UserResp, *dto.Pagination, error) {
	headers := map[string]string{}
	queryParams, stmErr := util.StructToMap(query)
	if stmErr != nil {
		return nil, nil, stmErr
	}
	optionsParams, stmErr := util.StructToMap(options)
	if stmErr != nil {
		return nil, nil, stmErr
	}

	_, dataBytes, err := s.httpClient.Get(reportPath+"/users/top-blog-viewed", headers, util.MergeMap(queryParams, optionsParams))
	if err != nil {
		return nil, nil, err
	}

	resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
	if decodeErr != nil {
		return nil, nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
	}

	var dataResp dto.ListUsersResponse
	if err := json.Unmarshal(resultData, &dataResp); err != nil {
		return nil, nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
	}

	return dataResp.Results, dataResp.Pagination, nil
}

func (s *trackingService) FindPaginationTracking(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.TrackingResponse, *dto.Pagination, error) {
	headers := map[string]string{}
	queryParams, stmErr := util.StructToMap(query)
	if stmErr != nil {
		return nil, nil, stmErr
	}
	optionsParams, stmErr := util.StructToMap(options)
	if stmErr != nil {
		return nil, nil, stmErr
	}

	_, dataBytes, err := s.httpClient.Get(trackingPath, headers, util.MergeMap(queryParams, optionsParams))
	if err != nil {
		return nil, nil, err
	}
	resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
	if decodeErr != nil {
		return nil, nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
	}

	var dataResp dto.ListTrackingResponse
	if err := json.Unmarshal(resultData, &dataResp); err != nil {
		return nil, nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
	}

	return dataResp.Results, dataResp.Pagination, nil
}
func (s *trackingService) FindPaginationAIMessage(query *dto.MessageQuery, options *dto.FindPageOptions) ([]*dto.Message, *dto.Pagination, error) {
	headers := map[string]string{}
	queryParams, stmErr := util.StructToMap(query)
	if stmErr != nil {
		return nil, nil, stmErr
	}
	optionsParams, stmErr := util.StructToMap(options)
	if stmErr != nil {
		return nil, nil, stmErr
	}

	_, dataBytes, err := s.httpClient.Get(messagePath, headers, util.MergeMap(queryParams, optionsParams))
	if err != nil {
		return nil, nil, err
	}
	resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
	if decodeErr != nil {
		return nil, nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
	}

	var dataResp dto.ListMessageResponse
	if err := json.Unmarshal(resultData, &dataResp); err != nil {
		return nil, nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
	}

	return dataResp.Results, dataResp.Pagination, nil

}

func (s *trackingService) CreateTracking(req *dto.TrackingRequest) error {
	_, _, err := s.httpClient.Post(trackingPath, req, nil)
	if err != nil {
		return err
	}

	return nil
}

func (s *trackingService) FindTopViewedBlog(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.BlogResp, *dto.Pagination, error) {
	headers := map[string]string{}

	queryParams, stmErr := util.StructToMap(query)
	if stmErr != nil {
		return nil, nil, stmErr
	}
	optionsParams, stmErr := util.StructToMap(options)
	if stmErr != nil {
		return nil, nil, stmErr
	}

	_, dataBytes, err := s.httpClient.Get(reportPath+"/blogs/top-viewed", headers, util.MergeMap(queryParams, optionsParams))
	if err != nil {
		return nil, nil, err
	}

	resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
	if decodeErr != nil {
		return nil, nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
	}

	var dataResp dto.ListBlogsResponse
	if err := json.Unmarshal(resultData, &dataResp); err != nil {
		return nil, nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
	}

	return dataResp.Results, dataResp.Pagination, nil

}

func (s *trackingService) CountTrackingByUserAndEvent(query *dto.TrackingQuery, options *dto.FindPageOptions) ([]*dto.CountRefUserResp, *dto.Pagination, error) {
	headers := map[string]string{}

	queryParams, stmErr := util.StructToMap(query)
	if stmErr != nil {
		return nil, nil, stmErr
	}
	optionsParams, stmErr := util.StructToMap(options)
	if stmErr != nil {
		return nil, nil, stmErr
	}

	countRefPath := fmt.Sprintf("%s/%s", trackingPath, "count-tracking-by-user-and-event")
	resp, dataBytes, err := s.httpClient.Get(countRefPath, headers, util.MergeMap(queryParams, optionsParams))
	if err != nil {
		log.Debugf("Get top ref count failed: %v", err.Error())
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		resultData, decodeErr := decodeDataResponseToRawJson(dataBytes)
		if decodeErr != nil {
			return nil, nil, fmt.Errorf("%w: %s", httpclient.ErrDecodeResponse, decodeErr)
		}

		var dataResp dto.ListRefCountResponse
		if err := json.Unmarshal(resultData, &dataResp); err != nil {
			return nil, nil, fmt.Errorf("%w: failed to unmarshal response body: %s", httpclient.ErrDecodeResponse, err)
		}

		return dataResp.Results, dataResp.Pagination, nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(dataBytes, &errResp); err != nil {
		return nil, nil, fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case e.INVALID_PARAMS:
		return nil, nil, fmt.Errorf("%w: %s", httpclient.ErrInvalidParams, errResp.Data.Message)

	default:
		return nil, nil, fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrUnknown, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}
func (s *trackingService) UpdateRefTracking(req *dto.UpdateRefTrackingRequest) error {
	_, _, err := s.httpClient.Put(trackingPath, req, nil)
	if err != nil {
		return err
	}
	return nil
}
