package exchange

import (
	"encoding/json"
	"fmt"
	"net/http"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"strings"
	"time"
)

const (
	CoinGeckoIDNear    = "near"
	CoinGeckoIDTether  = "tether"
	CoinGeckoIDUSDCoin = "usd-coin"
	CoinGeckoIDAvail   = "avail"
	CoinGeckoIDEth     = "ethereum"

	CoinMarketCapSymbolNear  = "NEAR"
	CoinMarketCapSymbolUSDT  = "USDT"
	CoinMarketCapSymbolUSDC  = "USDC"
	CoinMarketCapSymbolAVAIL = "AVAIL"
	CoinMarketCapSymbolETH   = "ETH"

	LatestFiatExchangeRatesPath                 = "/latest.json"
	CoinGeckoMarketPath                         = "/coins/markets"
	CoinMarketCapCryptocurrencyQuotesLatestPath = "/v2/cryptocurrency/quotes/latest"
)

type Exchange interface {
	GetFiatExchangeRates() (*FiatExchangeRates, error)
	GetCryptoExchangeRates() (*CryptoExchangeRates, error)
}

type FiatExchangeRates struct {
	USD2VND   float64 `json:"USD_VND"`
	VND2USD   float64 `json:"VND_USD"`
	Timestamp int64   `json:"timestamp"` // Unix milli
}

type CryptoExchangeRates struct {
	NEAR2USD  float64 `json:"NEAR_USD"`
	USDT2USD  float64 `json:"USDT_USD"`
	USDC2USD  float64 `json:"USDC_USD"`
	AVAIL2USD float64 `json:"AVAIL_USD"`
	ETH2USD   float64 `json:"ETH_USD"`
}

type OpenExchangeLatestRatesResponse struct {
	Disclaimer string `json:"disclaimer"`
	License    string `json:"license"`
	Timestamp  int64  `json:"timestamp"`
	Base       string `json:"base"`
	Rates      struct {
		AED float64 `json:"AED"`
		AFN float64 `json:"AFN"`
		ALL float64 `json:"ALL"`
		AMD float64 `json:"AMD"`
		ANG float64 `json:"ANG"`
		AOA float64 `json:"AOA"`
		ARS float64 `json:"ARS"`
		AUD float64 `json:"AUD"`
		AWG float64 `json:"AWG"`
		AZN float64 `json:"AZN"`
		BAM float64 `json:"BAM"`
		BBD float64 `json:"BBD"`
		BDT float64 `json:"BDT"`
		BGN float64 `json:"BGN"`
		BHD float64 `json:"BHD"`
		BIF float64 `json:"BIF"`
		BMD float64 `json:"BMD"`
		BND float64 `json:"BND"`
		BOB float64 `json:"BOB"`
		BRL float64 `json:"BRL"`
		BSD float64 `json:"BSD"`
		BTC float64 `json:"BTC"`
		BTN float64 `json:"BTN"`
		BWP float64 `json:"BWP"`
		BYN float64 `json:"BYN"`
		BZD float64 `json:"BZD"`
		CAD float64 `json:"CAD"`
		CDF float64 `json:"CDF"`
		CHF float64 `json:"CHF"`
		CLF float64 `json:"CLF"`
		CLP float64 `json:"CLP"`
		CNH float64 `json:"CNH"`
		CNY float64 `json:"CNY"`
		COP float64 `json:"COP"`
		CRC float64 `json:"CRC"`
		CUC float64 `json:"CUC"`
		CUP float64 `json:"CUP"`
		CVE float64 `json:"CVE"`
		CZK float64 `json:"CZK"`
		DJF float64 `json:"DJF"`
		DKK float64 `json:"DKK"`
		DOP float64 `json:"DOP"`
		DZD float64 `json:"DZD"`
		EGP float64 `json:"EGP"`
		ERN float64 `json:"ERN"`
		ETB float64 `json:"ETB"`
		EUR float64 `json:"EUR"`
		FJD float64 `json:"FJD"`
		FKP float64 `json:"FKP"`
		GBP float64 `json:"GBP"`
		GEL float64 `json:"GEL"`
		GGP float64 `json:"GGP"`
		GHS float64 `json:"GHS"`
		GIP float64 `json:"GIP"`
		GMD float64 `json:"GMD"`
		GNF float64 `json:"GNF"`
		GTQ float64 `json:"GTQ"`
		GYD float64 `json:"GYD"`
		HKD float64 `json:"HKD"`
		HNL float64 `json:"HNL"`
		HRK float64 `json:"HRK"`
		HTG float64 `json:"HTG"`
		HUF float64 `json:"HUF"`
		IDR float64 `json:"IDR"`
		ILS float64 `json:"ILS"`
		IMP float64 `json:"IMP"`
		INR float64 `json:"INR"`
		IQD float64 `json:"IQD"`
		IRR float64 `json:"IRR"`
		ISK float64 `json:"ISK"`
		JEP float64 `json:"JEP"`
		JMD float64 `json:"JMD"`
		JOD float64 `json:"JOD"`
		JPY float64 `json:"JPY"`
		KES float64 `json:"KES"`
		KGS float64 `json:"KGS"`
		KHR float64 `json:"KHR"`
		KMF float64 `json:"KMF"`
		KPW float64 `json:"KPW"`
		KRW float64 `json:"KRW"`
		KWD float64 `json:"KWD"`
		KYD float64 `json:"KYD"`
		KZT float64 `json:"KZT"`
		LAK float64 `json:"LAK"`
		LBP float64 `json:"LBP"`
		LKR float64 `json:"LKR"`
		LRD float64 `json:"LRD"`
		LSL float64 `json:"LSL"`
		LYD float64 `json:"LYD"`
		MAD float64 `json:"MAD"`
		MDL float64 `json:"MDL"`
		MGA float64 `json:"MGA"`
		MKD float64 `json:"MKD"`
		MMK float64 `json:"MMK"`
		MNT float64 `json:"MNT"`
		MOP float64 `json:"MOP"`
		MRU float64 `json:"MRU"`
		MUR float64 `json:"MUR"`
		MVR float64 `json:"MVR"`
		MWK float64 `json:"MWK"`
		MXN float64 `json:"MXN"`
		MYR float64 `json:"MYR"`
		MZN float64 `json:"MZN"`
		NAD float64 `json:"NAD"`
		NGN float64 `json:"NGN"`
		NIO float64 `json:"NIO"`
		NOK float64 `json:"NOK"`
		NPR float64 `json:"NPR"`
		NZD float64 `json:"NZD"`
		OMR float64 `json:"OMR"`
		PAB float64 `json:"PAB"`
		PEN float64 `json:"PEN"`
		PGK float64 `json:"PGK"`
		PHP float64 `json:"PHP"`
		PKR float64 `json:"PKR"`
		PLN float64 `json:"PLN"`
		PYG float64 `json:"PYG"`
		QAR float64 `json:"QAR"`
		RON float64 `json:"RON"`
		RSD float64 `json:"RSD"`
		RUB float64 `json:"RUB"`
		RWF float64 `json:"RWF"`
		SAR float64 `json:"SAR"`
		SBD float64 `json:"SBD"`
		SCR float64 `json:"SCR"`
		SDG float64 `json:"SDG"`
		SEK float64 `json:"SEK"`
		SGD float64 `json:"SGD"`
		SHP float64 `json:"SHP"`
		SLL float64 `json:"SLL"`
		SOS float64 `json:"SOS"`
		SRD float64 `json:"SRD"`
		SSP float64 `json:"SSP"`
		STD float64 `json:"STD"`
		STN float64 `json:"STN"`
		SVC float64 `json:"SVC"`
		SYP float64 `json:"SYP"`
		SZL float64 `json:"SZL"`
		THB float64 `json:"THB"`
		TJS float64 `json:"TJS"`
		TMT float64 `json:"TMT"`
		TND float64 `json:"TND"`
		TOP float64 `json:"TOP"`
		TRY float64 `json:"TRY"`
		TTD float64 `json:"TTD"`
		TWD float64 `json:"TWD"`
		TZS float64 `json:"TZS"`
		UAH float64 `json:"UAH"`
		UGX float64 `json:"UGX"`
		USD float64 `json:"USD"`
		UYU float64 `json:"UYU"`
		UZS float64 `json:"UZS"`
		VES float64 `json:"VES"`
		VND float64 `json:"VND"`
		VUV float64 `json:"VUV"`
		WST float64 `json:"WST"`
		XAF float64 `json:"XAF"`
		XAG float64 `json:"XAG"`
		XAU float64 `json:"XAU"`
		XCD float64 `json:"XCD"`
		XDR float64 `json:"XDR"`
		XOF float64 `json:"XOF"`
		XPD float64 `json:"XPD"`
		XPF float64 `json:"XPF"`
		XPT float64 `json:"XPT"`
		YER float64 `json:"YER"`
		ZAR float64 `json:"ZAR"`
		ZMW float64 `json:"ZMW"`
		ZWL int     `json:"ZWL"`
	} `json:"rates"`
}

type OpenExchangeErrorResponse struct {
	Error       bool   `json:"error"`
	Status      int    `json:"status"`
	Message     string `json:"message"`
	Description string `json:"description"`
}

type CoinGeckoMarketResponse struct {
	ID           string  `json:"id"`
	Symbol       string  `json:"symbol"`
	Name         string  `json:"name"`
	CurrentPrice float64 `json:"current_price"`
}

type CoinMarketCapQuotesLatestStatus struct {
	Timestamp    time.Time   `json:"timestamp"`
	ErrorCode    int         `json:"error_code"`
	ErrorMessage interface{} `json:"error_message"`
	Elapsed      int         `json:"elapsed"`
	CreditCount  int         `json:"credit_count"`
	Notice       interface{} `json:"notice"`
}

type CoinMarketCapQuotesLatest struct {
	Id     int    `json:"id"`
	Name   string `json:"name"`
	Symbol string `json:"symbol"`
	//Slug           string    `json:"slug"`
	//NumMarketPairs int       `json:"num_market_pairs"`
	//DateAdded      time.Time `json:"date_added"`
	//Tags           []struct {
	//	Slug     string `json:"slug"`
	//	Name     string `json:"name"`
	//	Category string `json:"category"`
	//} `json:"tags"`
	//MaxSupply                     interface{} `json:"max_supply"`
	//CirculatingSupply             int         `json:"circulating_supply"`
	//TotalSupply                   int         `json:"total_supply"`
	//IsActive                      int         `json:"is_active"`
	//InfiniteSupply                bool        `json:"infinite_supply"`
	//Platform                      interface{} `json:"platform"`
	//CmcRank                       int         `json:"cmc_rank"`
	//IsFiat                        int         `json:"is_fiat"`
	//SelfReportedCirculatingSupply interface{} `json:"self_reported_circulating_supply"`
	//SelfReportedMarketCap         interface{} `json:"self_reported_market_cap"`
	//TvlRatio                      interface{} `json:"tvl_ratio"`
	//LastUpdated                   time.Time   `json:"last_updated"`
	Quote struct {
		USD struct {
			Price float64 `json:"price"`
			//Volume24H             float64     `json:"volume_24h"`
			//VolumeChange24H       float64     `json:"volume_change_24h"`
			//PercentChange1H       float64     `json:"percent_change_1h"`
			//PercentChange24H      float64     `json:"percent_change_24h"`
			//PercentChange7D       float64     `json:"percent_change_7d"`
			//PercentChange30D      float64     `json:"percent_change_30d"`
			//PercentChange60D      float64     `json:"percent_change_60d"`
			//PercentChange90D      float64     `json:"percent_change_90d"`
			//MarketCap             float64     `json:"market_cap"`
			//MarketCapDominance    float64     `json:"market_cap_dominance"`
			//FullyDilutedMarketCap float64     `json:"fully_diluted_market_cap"`
			//Tvl                   interface{} `json:"tvl"`
			LastUpdated time.Time `json:"last_updated"`
		} `json:"USD"`
	} `json:"quote"`
}

type CoinMarketCapQuotesLatestResponse struct {
	Status *CoinMarketCapQuotesLatestStatus        `json:"status"`
	Data   map[string][]*CoinMarketCapQuotesLatest `json:"data"`
}

type exchangeImpl struct {
	fHttpClient             *httpclient.Client
	coinGeckoHttpClient     *httpclient.Client
	coinMarketCapHttpClient *httpclient.Client
}

func (e *exchangeImpl) GetFiatExchangeRates() (*FiatExchangeRates, error) {
	headers := map[string]string{}
	queryParams := map[string]interface{}{
		"app_id": setting.OpenExchangeRatesSetting.AppID,
	}
	resp, data, err := e.fHttpClient.Get(LatestFiatExchangeRatesPath, headers, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch latest fiat exchange rates: %w", err)
	}

	if resp.StatusCode == http.StatusOK {
		var exchangeRates OpenExchangeLatestRatesResponse
		if err = json.Unmarshal(data, &exchangeRates); err != nil {
			return nil, fmt.Errorf("failed to unmarshal latest fiat exchange rates: %w", err)
		}

		rates := &FiatExchangeRates{
			USD2VND:   exchangeRates.Rates.VND,
			VND2USD:   1 / exchangeRates.Rates.VND,
			Timestamp: exchangeRates.Timestamp * 1000,
		}
		return rates, nil
	}

	var errResp OpenExchangeErrorResponse
	if err = json.Unmarshal(data, &errResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal fiat exchange error response: %w", err)
	}

	switch resp.StatusCode {
	case http.StatusUnauthorized:
		return nil, fmt.Errorf("unauthorized: %d - %s -%s", errResp.Status, errResp.Message, errResp.Description)

	default:
		return nil, fmt.Errorf("unknown error: %d - %s -%s", errResp.Status, errResp.Message, errResp.Description)
	}
}

func (e *exchangeImpl) GetCryptoExchangeRates() (*CryptoExchangeRates, error) {
	if exchangeRates, err := e.getCryptoExchangeRatesCoinGecko(); err == nil {
		return exchangeRates, nil
	} else {
		log.Errorf("Get crypto exchange rates from CoinGecko failed: %v", err)
	}

	return e.getCryptoExchangeRatesCoinMarketCap()
}

func (e *exchangeImpl) getCryptoExchangeRatesCoinGecko() (*CryptoExchangeRates, error) {
	headers := map[string]string{}
	queryParams := map[string]interface{}{
		"vs_currency": "usd",
		"ids": strings.Join([]string{
			CoinGeckoIDNear,
			CoinGeckoIDTether,
			CoinGeckoIDUSDCoin,
			CoinGeckoIDAvail,
			CoinGeckoIDEth,
		}, ","),
	}
	resp, data, err := e.coinGeckoHttpClient.Get(CoinGeckoMarketPath, headers, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch latest crypto exchange rates: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		errResp := map[string]interface{}{}
		if err = json.Unmarshal(data, &errResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal latest crypto exchange rates error response with status %d (%s): %w", resp.StatusCode, string(data), err)
		}
		return nil, fmt.Errorf("failed to fetch latest crypto exchange rates with status %d: %v", resp.StatusCode, errResp)
	}

	var coinMarkets []CoinGeckoMarketResponse
	if err = json.Unmarshal(data, &coinMarkets); err != nil {
		return nil, fmt.Errorf("failed to unmarshal latest crypto exchange rates: %w", err)
	}

	var rates CryptoExchangeRates
	for _, coinMarket := range coinMarkets {
		switch coinMarket.ID {
		case CoinGeckoIDNear:
			rates.NEAR2USD = coinMarket.CurrentPrice
		case CoinGeckoIDTether:
			rates.USDT2USD = coinMarket.CurrentPrice
		case CoinGeckoIDUSDCoin:
			rates.USDC2USD = coinMarket.CurrentPrice
		case CoinGeckoIDAvail:
			rates.AVAIL2USD = coinMarket.CurrentPrice
		case CoinGeckoIDEth:
			rates.ETH2USD = coinMarket.CurrentPrice
		}
	}

	return &rates, nil
}

func (e *exchangeImpl) getCryptoExchangeRatesCoinMarketCap() (*CryptoExchangeRates, error) {
	headers := map[string]string{
		"X-CMC_PRO_API_KEY": setting.CoinMarketCapSetting.ApiKey,
	}
	queryParams := map[string]interface{}{
		"symbol": strings.Join([]string{
			CoinMarketCapSymbolNear,
			CoinMarketCapSymbolUSDT,
			CoinMarketCapSymbolUSDC,
			CoinMarketCapSymbolAVAIL,
			CoinMarketCapSymbolETH,
		}, ","),
	}
	resp, data, err := e.coinMarketCapHttpClient.Get(CoinMarketCapCryptocurrencyQuotesLatestPath, headers, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch latest crypto exchange rates from CoinMarketCap: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		errResp := map[string]interface{}{}
		if err = json.Unmarshal(data, &errResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal latest crypto exchange rates error response with status %d (%s): %w", resp.StatusCode, string(data), err)
		}
		return nil, fmt.Errorf("failed to fetch latest crypto exchange rates from CoinMarketCap with status %d: %v", resp.StatusCode, errResp)
	}

	var coinMarketCapResp CoinMarketCapQuotesLatestResponse
	if err = json.Unmarshal(data, &coinMarketCapResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal latest crypto exchange rates from CoinMarketCap: %w", err)
	}

	var rates CryptoExchangeRates
	if nearQuotes, found := coinMarketCapResp.Data[CoinMarketCapSymbolNear]; found {
		if len(nearQuotes) > 0 {
			rates.NEAR2USD = nearQuotes[0].Quote.USD.Price
		} else {
			return nil, fmt.Errorf("no NEAR latest quote found from CoinMarketCap")
		}
	}

	if usdtQuotes, found := coinMarketCapResp.Data[CoinMarketCapSymbolUSDT]; found {
		if len(usdtQuotes) > 0 {
			rates.USDT2USD = usdtQuotes[0].Quote.USD.Price
		} else {
			return nil, fmt.Errorf("no USDT latest quote found from CoinMarketCap")
		}
	}

	if usdcQuotes, found := coinMarketCapResp.Data[CoinMarketCapSymbolUSDC]; found {
		if len(usdcQuotes) > 0 {
			rates.USDC2USD = usdcQuotes[0].Quote.USD.Price
		} else {
			return nil, fmt.Errorf("no USDC latest quote found from CoinMarketCap")
		}
	}

	if availQuotes, found := coinMarketCapResp.Data[CoinMarketCapSymbolAVAIL]; found {
		if len(availQuotes) > 0 {
			rates.AVAIL2USD = availQuotes[0].Quote.USD.Price
		} else {
			return nil, fmt.Errorf("no AVAIL latest quote found from CoinMarketCap")
		}
	}

	if ethQuotes, found := coinMarketCapResp.Data[CoinMarketCapSymbolETH]; found {
		if len(ethQuotes) > 0 {
			rates.ETH2USD = ethQuotes[0].Quote.USD.Price
		} else {
			return nil, fmt.Errorf("no ETH latest quote found from CoinMarketCap")
		}
	}

	return &rates, nil
}

var defaultInstance Exchange

func Setup() {
	lazyInit()
}

func lazyInit() {
	if defaultInstance != nil {
		return
	}
	defaultInstance = &exchangeImpl{
		fHttpClient:             httpclient.NewClient(setting.OpenExchangeRatesSetting.BaseURL, nil),
		coinGeckoHttpClient:     httpclient.NewClient(setting.CoinGeckoSetting.BaseURL, nil),
		coinMarketCapHttpClient: httpclient.NewClient(setting.CoinMarketCapSetting.BaseURL, nil),
	}
}

func GetFiatExchangeRates() (*FiatExchangeRates, error) {
	return defaultInstance.GetFiatExchangeRates()
}

func GetCryptoExchangeRates() (*CryptoExchangeRates, error) {
	return defaultInstance.GetCryptoExchangeRates()
}
