package excel

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
)

type SheetName string

const (
	TickChar  = "\u2713"
	CrossChar = "\u2717"
)

var colLetters = [...]string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
	"AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ",
}

type WriteOptions struct {
	Headers        []string
	HeadersMapping map[string]string
	Style          *ExcelStyle
}

type WriteOption func(*WriteOptions)

func WithHeaders(headers []string) WriteOption {
	return func(o *WriteOptions) {
		o.Headers = headers
	}
}

func WithHeadersMapping(mapping map[string]string) WriteOption {
	return func(o *WriteOptions) {
		o.HeadersMapping = mapping
	}
}

func WithStyle(style *ExcelStyle) WriteOption {
	return func(o *WriteOptions) {
		o.Style = style
	}
}

type Excel struct {
	client *excelize.File
}

func New() *Excel {
	f := excelize.NewFile()
	return &Excel{
		client: f,
	}
}

func (c *Excel) NewExcelWithSheet(sheetNames []string) error {
	f := c.client
	defaultSheet := f.GetSheetName(0)

	for _, name := range sheetNames {
		if _, err := f.NewSheet(name); err != nil {
			return err
		}
	}

	if len(sheetNames) > 0 {
		if err := f.DeleteSheet(defaultSheet); err != nil {
			return err
		}
	}

	return nil
}

func (c *Excel) AddNewSheet(sheetName string) error {
	f := c.client
	if _, err := f.NewSheet(sheetName); err != nil {
		return err
	}
	return nil
}

func toTitleCase(s string) string {
	s = strings.ReplaceAll(s, "_", " ")

	var camelRegex = regexp.MustCompile(`([a-z0-9])([A-Z])`)
	s = camelRegex.ReplaceAllString(s, "$1 $2")

	return strings.Title(strings.TrimSpace(s))
}

func (c *Excel) WriteData(sheetName string, data interface{}, options ...WriteOption) error {
	f := c.client

	v := reflect.ValueOf(data)
	if v.Kind() != reflect.Slice {
		return errors.New("data must be a slice of structs")
	}
	if v.Len() == 0 {
		return errors.New("have no date to report")
	}

	firstElem := v.Index(0)
	elemType := firstElem.Type()
	isPtr := elemType.Kind() == reflect.Ptr

	// Get the actual struct type
	structType := elemType
	if isPtr {
		structType = elemType.Elem()
		if firstElem.IsNil() {
			return fmt.Errorf("nil pointer in data slice at index 0")
		}
		firstElem = firstElem.Elem()
	}

	if structType.Kind() != reflect.Struct {
		return errors.New("data must be a slice of structs or struct pointers")
	}

	opts := &WriteOptions{
		Headers:        nil,
		HeadersMapping: make(map[string]string),
		Style:          DefaultExcelStyle(),
	}

	for _, opt := range options {
		opt(opts)
	}

	validFields := make(map[string]string)
	fieldTypes := make(map[string]reflect.Type) // Store field types
	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		if field.PkgPath != "" {
			continue
		}
		tag := field.Tag.Get("excel")
		if tag == "-" {
			continue
		}
		if tag != "" {
			validFields[field.Name] = tag
		} else {
			validFields[field.Name] = field.Name
		}
		fieldTypes[field.Name] = field.Type // Store the field type
	}

	if opts.Headers == nil {
		opts.Headers = make([]string, 0, len(validFields))
		for i := 0; i < structType.NumField(); i++ {
			field := structType.Field(i)
			if field.PkgPath != "" || field.Tag.Get("excel") == "-" {
				continue
			}
			opts.Headers = append(opts.Headers, field.Name)
		}
	} else {
		for _, header := range opts.Headers {
			if _, ok := validFields[header]; !ok {
				return errors.New(fmt.Sprintf("header %q is not a valid field name in struct", header))
			}
		}
	}

	// Check column limit
	if len(opts.Headers) >= len(colLetters) {
		return errors.New(fmt.Sprintf("too many columns, maximum supported is %d", len(colLetters)))
	}

	styles, err := c.createStyles(map[string]*StyleOptions{
		"header":  opts.Style.HeaderStyle,
		"tick":    opts.Style.TickStyle,
		"cross":   opts.Style.CrossStyle,
		"content": opts.Style.ContentStyle,
	})
	if err != nil {
		return err
	}
	for colIndex, field := range opts.Headers {
		colName := colLetters[colIndex]

		if err := f.SetColWidth(sheetName, colName, colName, 15); err != nil {
			return err
		}

		cell := fmt.Sprintf("%s1", colName)
		var headerValue string
		if mappedValue, exists := opts.HeadersMapping[field]; exists {
			headerValue = mappedValue
		} else if tagValue, exists := validFields[field]; exists && tagValue != field {
			headerValue = toTitleCase(tagValue)
		} else {
			headerValue = toTitleCase(field)
		}

		// Write header
		if err := f.SetCellValue(sheetName, cell, headerValue); err != nil {
			return err
		}

		// Apply header style
		if err := c.ApplyStyle(sheetName, cell, styles["header"]); err != nil {
			return err
		}
	}

	// Write data
	for i := 0; i < v.Len(); i++ {
		rowNum := i + 2
		item := v.Index(i)

		if isPtr {
			if item.IsNil() {
				continue
			}
			item = item.Elem()
		}

		for colIndex, field := range opts.Headers {
			value := item.FieldByName(field)
			if !value.IsValid() {
				continue
			}

			if value.Kind() == reflect.Ptr {
				if value.IsNil() {
					continue
				}
				value = value.Elem()
			}

			cell := fmt.Sprintf("%s%d", colLetters[colIndex], rowNum)

			if fieldTypes[field].Kind() == reflect.Bool {
				boolVal := value.Bool()
				if boolVal {
					if err := f.SetCellValue(sheetName, cell, TickChar); err != nil {
						return err
					}
					if err := c.ApplyStyle(sheetName, cell, styles["tick"]); err != nil {
						return err
					}
				} else {
					if err := f.SetCellValue(sheetName, cell, CrossChar); err != nil {
						return err
					}

					if err := c.ApplyStyle(sheetName, cell, styles["cross"]); err != nil {
						return err
					}
				}
			} else {
				if err := f.SetCellValue(sheetName, cell, value.Interface()); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func (c *Excel) SaveFile(directory string, fileName string) error {
	if directory == "" {
		directory = "report"
	}

	if fileName == "" {
		fileName = fmt.Sprintf("report_%s", time.Now().Format("20060102_150405"))
	}

	if err := os.MkdirAll(directory, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	filePath := filepath.Join(directory, fileName)

	if !strings.HasSuffix(filePath, ".xlsx") {
		filePath += ".xlsx"
	}

	if err := c.client.SaveAs(filePath); err != nil {
		return fmt.Errorf("failed to save excel file: %w", err)
	}

	return nil
}

func (c *Excel) ToByte() ([]byte, error) {
	f := c.client
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("error writing excel to buffer: %w", err)
	}

	return buffer.Bytes(), nil

}

func (c *Excel) SetCellValue(sheet string, cell string, value interface{}) error {
	f := c.client
	return f.SetCellValue(sheet, cell, value)
}
