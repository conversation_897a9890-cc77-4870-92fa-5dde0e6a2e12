package helpers

import (
	"strings"
)

func IsNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := strings.ToLower(err.<PERSON>rror())
	return strings.Contains(errMsg, "not found")
}

func IsNotExistsError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "does not exist")
}

// IsNotFoundOrNotExistsError checks if error indicates either "not found" or "does not exist"
func IsNotFoundOrNotExistsError(err error) bool {
	return IsNotFoundError(err) || IsNotExistsError(err)
}

// IsConnectionError checks if error message indicates a connection issue
func IsConnectionError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := strings.ToLower(err.<PERSON>rror())
	return strings.Contains(errMsg, "connection") || 
		   strings.Contains(errMsg, "timeout") ||
		   strings.Contains(errMsg, "network")
}

// IsValidationError checks if error message indicates a validation issue
func IsValidationError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "validation") ||
		   strings.Contains(errMsg, "invalid") ||
		   strings.Contains(errMsg, "required")
}
