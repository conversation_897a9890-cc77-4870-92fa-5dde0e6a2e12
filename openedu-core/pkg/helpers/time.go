package helpers

import (
	"time"
)

// FormatUnixMilliToISO formats unix milliseconds timestamp to ISO 8601 format
func FormatUnixMilliToISO(unixMilli int64) string {
	return time.UnixMilli(unixMilli).Format("2006-01-02T15:04:05Z")
}

// FormatUnixMilliToISOFromUint formats unix milliseconds timestamp from uint to ISO 8601 format
func FormatUnixMilliToISOFromUint(unixMilli uint) string {
	return FormatUnixMilliToISO(int64(unixMilli))
}

// FormatTimestampToISO formats timestamp to ISO 8601 format with custom layout
func FormatTimestampToISO(timestamp int64, layout string) string {
	if layout == "" {
		layout = "2006-01-02T15:04:05Z"
	}
	return time.UnixMilli(timestamp).Format(layout)
}
