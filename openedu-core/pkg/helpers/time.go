package helpers

import (
	"time"
)


func FormatUnixMilliToISO(unixMilli int64) string {
	return time.UnixMilli(unixMilli).Format("2006-01-02T15:04:05Z")
}

func FormatUnixMilliToISOFromUint(unixMilli uint) string {
	return FormatUnixMilliToISO(int64(unixMilli))
}

func FormatTimestampToISO(timestamp int64, layout string) string {
	if layout == "" {
		layout = "2006-01-02T15:04:05Z"
	}
	return time.UnixMilli(timestamp).Format(layout)
}
