package helpers

import (
	"openedu-core/models"
)

// GetCurrencyByNetwork returns the appropriate currency for a given blockchain network
func GetCurrencyByNetwork(network models.BlockchainNetwork) models.CryptoCurrency {
	switch network {
	case models.BlockchainNetworkBASE:
		return models.CryptoCurrencyETH
	case models.BlockchainNetworkNEAR:
		return models.CryptoCurrencyNEAR
	default:
		// Default to NEAR for unknown networks
		return models.CryptoCurrencyNEAR
	}
}

// GetEstimatedFeeByNetwork returns the estimated fee for minting NFT on a given network
func GetEstimatedFeeByNetwork(network models.BlockchainNetwork) models.EstimatedFee {
	switch network {
	case models.BlockchainNetworkBASE:
		return models.EstimatedETHToMintNFT
	case models.BlockchainNetworkNEAR:
		return models.EstimatedNEARsToMintNFT
	default:
		// Default to NEAR fee for unknown networks
		return models.EstimatedNEARsToMintNFT
	}
}
