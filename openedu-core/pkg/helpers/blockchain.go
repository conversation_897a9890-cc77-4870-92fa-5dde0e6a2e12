package helpers

import (
	"openedu-core/models"

	"github.com/shopspring/decimal"
)

func GetCurrencyByNetwork(network models.BlockchainNetwork) models.Currency {
	switch network {
	case models.BlockchainNetworkBASE:
		return models.CryptoCurrencyETH
	case models.BlockchainNetworkNEAR:
		return models.CryptoCurrencyNEAR
	default:
		return models.CryptoCurrencyNEAR
	}
}

func GetEstimatedFeeByNetwork(network models.BlockchainNetwork) decimal.Decimal {
	switch network {
	case models.BlockchainNetworkBASE:
		return models.EstimatedETHToMintNFT
	case models.BlockchainNetworkNEAR:
		return models.EstimatedNEARsToMintNFT
	default:
		// Default to NEAR fee for unknown networks
		return models.EstimatedNEARsToMintNFT
	}
}
