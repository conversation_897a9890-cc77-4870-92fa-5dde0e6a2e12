package util

import (
	"github.com/nfnt/resize"
	"image"
)

// ResizeImageMaxWidth resizes the image if its width exceeds maxWidth maintains aspect ratio
func ResizeImageMaxWidth(img image.Image, maxWidth uint) image.Image {
	bounds := img.Bounds()
	width := bounds.Max.X - bounds.Min.X
	height := bounds.Max.Y - bounds.Min.Y

	// If image is already smaller than maxWidth, return original
	if width <= int(maxWidth) {
		return img
	}

	// Calculate new height to maintain aspect ratio
	ratio := float64(maxWidth) / float64(width)
	newHeight := uint(float64(height) * ratio)

	// Resize using Lanczos resampling
	resized := resize.Resize(maxWidth, newHeight, img, resize.Lanczos3)
	return resized
}
