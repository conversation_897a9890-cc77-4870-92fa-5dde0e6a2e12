package util

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"unicode"

	"github.com/samber/lo"
)

func SetDynamicTagStruct(mStruct interface{}, overrideTags map[string]string) (interface{}, error) {
	t := reflect.TypeOf(mStruct)
	if t == nil {
		return nil, errors.New("struct is nil")
	}
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return nil, errors.New("this is not a struct")
	}

	fields := make([]reflect.StructField, t.NumField())

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		newField := reflect.StructField{
			Name:      field.Name,
			Type:      field.Type,
			Tag:       field.Tag,
			PkgPath:   field.PkgPath,
			Anonymous: field.Anonymous,
		}

		if newTag, exists := overrideTags[field.Name]; exists {
			if newTag == "" {
				return nil, fmt.Errorf("new tag for field %s cannot be empty", field.Name)
			}
			newField.Tag = reflect.StructTag(newTag)
		}

		fields[i] = newField
	}
	newType := reflect.StructOf(fields)

	newStruct := reflect.New(newType).Elem()

	oldValue := reflect.ValueOf(mStruct)
	if oldValue.Kind() == reflect.Ptr {
		oldValue = oldValue.Elem()
	}

	for i := 0; i < t.NumField(); i++ {
		newStruct.Field(i).Set(oldValue.Field(i))
	}

	return newStruct.Interface(), nil
}

type fieldMetadata struct {
	originalName string
	newName      string
	field        reflect.StructField
}

func combineStructs(inputs []SliceJoinInput, tagOverrides map[string]map[string]string, structs ...interface{}) (interface{}, error) {
	if len(inputs) == 0 || len(structs) == 0 {
		return nil, errors.New("inputs and structs cannot be empty")
	}
	// Pre-allocate with estimated capacity
	fields := make([]fieldMetadata, 0, len(structs)*5)
	usedNames := make(map[string]bool, len(structs)*5)

	// Process all structs and collect field metadata
	for i, s := range structs {
		t := reflect.TypeOf(s)
		if t == nil {
			return nil, fmt.Errorf("struct %d is nil", i)
		}
		if t.Kind() == reflect.Ptr {
			t = t.Elem()
		}
		if t.Kind() != reflect.Struct {
			return nil, fmt.Errorf("argument %d is not a struct", i)
		}

		isFirstStruct := i == 0
		structName := t.Name()

		// Process fields
		for j := 0; j < t.NumField(); j++ {
			field := t.Field(j)
			fieldName := field.Name

			// Skip key fields for non-first structs
			if !isFirstStruct && field.Name == inputs[i].Key {
				continue
			}

			// Handle field renaming
			if !isFirstStruct {
				if inputs[i].RenameFields != nil {
					if newName, exists := inputs[i].RenameFields[field.Name]; exists {
						if newName == "" {
							return nil, fmt.Errorf("new name for field %s cannot be empty", field.Name)
						}
						fieldName = newName
					}
				}

				// Skip if field name already used
				if usedNames[fieldName] {
					continue
				}

				// Append struct name for key fields
				if field.Name == inputs[i].Key {
					fieldName = structName + "_" + fieldName
				}
			}

			if fieldName == "" {
				return nil, errors.New("field name cannot be empty")
			}

			// Store field metadata
			fields = append(fields, fieldMetadata{
				originalName: field.Name,
				newName:      fieldName,
				field:        field,
			})
			usedNames[fieldName] = true
		}
	}

	if len(fields) == 0 {
		return nil, errors.New("no fields found in structs")
	}

	// Create final struct fields with tag overrides
	finalFields := make([]reflect.StructField, len(fields))
	for i, metadata := range fields {
		newField := reflect.StructField{
			Name:      sanitizeFieldName(metadata.newName),
			Type:      metadata.field.Type,
			Tag:       metadata.field.Tag,
			PkgPath:   metadata.field.PkgPath,
			Anonymous: metadata.field.Anonymous,
		}

		// Apply tag overrides if they exist
		if tagOverrides != nil {
			if tagMap, exists := tagOverrides[metadata.newName]; exists {
				newField.Tag = reflect.StructTag(buildTagString(tagMap))
			}
		}

		finalFields[i] = newField
	}

	// Sort embeded field go to the top
	sort.SliceStable(finalFields, func(i, j int) bool {
		return finalFields[i].Anonymous && !finalFields[j].Anonymous
	})

	// Create the combined type safely
	var result interface{}
	func() {
		defer func() {
			if r := recover(); r != nil {
				panic(fmt.Errorf("failed to create struct: %v", r))
			}
		}()

		lo.ForEach(finalFields, func(item reflect.StructField, _ int) {

		})
		combinedType := reflect.StructOf(finalFields)
		fmt.Println("Combine type field have")
		PrintStructFields(combinedType)
		sliceType := reflect.SliceOf(combinedType)
		result = reflect.MakeSlice(sliceType, 0, 1).Interface()
	}()

	return result, nil
}

func PrintStructFields(t reflect.Type) {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		fmt.Printf("Not a struct type: %v\n", t.Kind())
		return
	}

	fmt.Printf("Struct: %s\n", t.Name())
	fmt.Println("Fields:")
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fmt.Printf("  %d: %-20s %-20s", i, field.Name, field.Type)
		if field.Tag != "" {
			fmt.Printf(" tag:%q", field.Tag)
		}
		if field.Anonymous {
			fmt.Printf(" embedded")
		}
		if field.PkgPath != "" {
			fmt.Printf(" unexported")
		}
		fmt.Println()
	}
}

func PrintStructFieldsRecursive(t reflect.Type, indent string) {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		fmt.Printf("%sNot a struct type: %v\n", indent, t.Kind())
		return
	}

	fmt.Printf("%sStruct: %s\n", indent, t.Name())
	fmt.Printf("%sFields:\n", indent)
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fmt.Printf("%s  %d: %-20s %-20s", indent, i, field.Name, field.Type)
		if field.Tag != "" {
			fmt.Printf(" tag:%q", field.Tag)
		}
		if field.Anonymous {
			fmt.Printf(" embedded")
		}
		if field.PkgPath != "" {
			fmt.Printf(" unexported")
		}
		fmt.Println()

		// If this field is itself a struct, recursively print its fields
		fieldType := field.Type
		if fieldType.Kind() == reflect.Ptr {
			fieldType = fieldType.Elem()
		}
		if fieldType.Kind() == reflect.Struct {
			PrintStructFieldsRecursive(fieldType, indent+"    ")
		}
	}
}

func isValidTagValue(value string) bool {
	return value != ""
}

type SliceJoinInput struct {
	Slice        interface{}                  // The slice to join
	Key          string                       // Key field to join on
	RenameFields map[string]string            // Original field name -> New field name
	TagOverrides map[string]map[string]string // field name -> tag key -> tag value
}

func parseStructTags(tag string) map[string]string {
	tags := make(map[string]string)
	if tag == "" {
		return tags
	}

	for _, tagPair := range strings.Split(string(tag), " ") {
		parts := strings.SplitN(tagPair, ":", 2)
		if len(parts) == 2 {
			key := parts[0]
			value := strings.Trim(parts[1], `"`)
			tags[key] = value
		}
	}
	return tags
}

func buildTagString(tags map[string]string) string {
	var tagPairs []string
	for key, value := range tags {
		tagPairs = append(tagPairs, fmt.Sprintf(`%s:"%s"`, key, value))
	}
	return strings.Join(tagPairs, " ")
}

func JoinSlices(inputs []SliceJoinInput, tagOverrides map[string]map[string]string) (reflect.Value, error) {
	//if len(inputs) < 2 {
	//	return reflect.Value{}, errors.New("at least two slices are required")
	//}

	if len(inputs) == 0 {
		sliceVal := reflect.ValueOf(inputs[0].Slice)
		elemType := sliceVal.Type().Elem()
		if elemType.Kind() == reflect.Ptr {
			elemType = elemType.Elem()
		}
		resultSlice := reflect.MakeSlice(reflect.SliceOf(elemType), 0, 0)
		return resultSlice, nil
	}

	sliceValues := make([]reflect.Value, len(inputs)) // store slice value
	elemTypes := make([]reflect.Type, len(inputs))    // store slice type
	emptyStructs := make([]interface{}, len(inputs))  // emptyStructs

	for i, input := range inputs {
		sliceVal := reflect.ValueOf(input.Slice)
		if sliceVal.Kind() != reflect.Slice {
			return reflect.Value{}, fmt.Errorf("input %d must be a slice", i)
		}
		if sliceVal.Len() == 0 {
			return reflect.Value{}, fmt.Errorf("input slice %d cannot be empty", i)
		}

		sliceValues[i] = sliceVal

		elemType := sliceVal.Type().Elem()
		if elemType.Kind() == reflect.Ptr {
			elemType = elemType.Elem()
		}
		elemTypes[i] = elemType

		emptyStructs[i] = reflect.New(elemType).Elem().Interface()
	}

	var combinedType reflect.Type
	if len(inputs) > 1 {
		combined, err := combineStructs(inputs, tagOverrides, emptyStructs...)
		if err != nil {
			return reflect.Value{}, fmt.Errorf("failed to combine structs: %w", err)
		}

		combinedType = reflect.TypeOf(combined).Elem()
	} else {
		sliceVal := reflect.ValueOf(inputs[0].Slice)
		combinedType = sliceVal.Type().Elem()
		if combinedType.Kind() == reflect.Ptr {
			combinedType = combinedType.Elem()
		}
	}

	maps := make([]map[interface{}][]reflect.Value, len(inputs)-1)
	for i := 1; i < len(inputs); i++ {
		maps[i-1] = make(map[interface{}][]reflect.Value)

		// Fill map with slice elements using the specified join key
		for j := 0; j < sliceValues[i].Len(); j++ {
			elem := sliceValues[i].Index(j)
			if elem.Kind() == reflect.Ptr {
				elem = elem.Elem()
			}

			keyValue := elem.FieldByName(inputs[i].Key)
			if !keyValue.IsValid() {
				return reflect.Value{}, fmt.Errorf("key %s not found in slice %d elements", inputs[i].Key, i)
			}

			// Store all values with the same key in a slice
			keyInterface := keyValue.Interface()
			maps[i-1][keyInterface] = append(maps[i-1][keyInterface], elem)
		}
	}

	// Create result slice
	resultSlice := reflect.MakeSlice(reflect.SliceOf(combinedType), 0, sliceValues[0].Len())

	// Fill result slice
	for i := 0; i < sliceValues[0].Len(); i++ {
		elem1 := sliceValues[0].Index(i)
		if elem1.Kind() == reflect.Ptr {
			elem1 = elem1.Elem()
		}

		key1Value := elem1.FieldByName(inputs[0].Key)
		if !key1Value.IsValid() {
			return reflect.Value{}, fmt.Errorf("key %s not found in first slice elements", inputs[0].Key)
		}

		// Create new element
		newElem := reflect.New(combinedType).Elem()

		// Copy fields from first struct
		for j := 0; j < elem1.NumField(); j++ {
			field := elem1.Type().Field(j)
			newField := newElem.FieldByName(field.Name)
			if newField.IsValid() {
				newField.Set(elem1.Field(j))
			}
		}

		// Copy fields from joined structs using the key value for lookup
		keyInterface := key1Value.Interface()

		// Track which fields have been set
		setFields := make(map[string]bool)

		for sliceIndex, m := range maps {
			// Get all matching elements for this key
			if matchingElems, exists := m[keyInterface]; exists {
				for _, elem2 := range matchingElems {
					for j := 0; j < elem2.NumField(); j++ {
						field := elem2.Type().Field(j)

						// Skip the key field
						if field.Name == inputs[sliceIndex+1].Key {
							continue
						}

						// Calculate target field name
						fieldName := field.Name
						//					structName := elemTypes[sliceIndex+1].Name()

						// Handle renames first
						if inputs[sliceIndex+1].RenameFields != nil {
							if newName, exists := inputs[sliceIndex+1].RenameFields[field.Name]; exists {
								fieldName = newName
							}
						}

						// // Handle name conflicts
						// firstStructType := elemTypes[0]
						// if _, exists := firstStructType.FieldByName(fieldName); exists || field.Name == inputs[sliceIndex+1].Key {
						// 	fieldName = structName + "_" + field.Name
						// }

						if !setFields[fieldName] {
							newField := newElem.FieldByName(fieldName)
							if !newField.IsValid() {
								return reflect.Value{}, fmt.Errorf("field %s not found in combined struct", fieldName)
							}

							if newField.IsValid() && elem2.Field(j).IsValid() {
								if newField.Interface() == reflect.Zero(newField.Type()).Interface() {
									newField.Set(elem2.Field(j))
									setFields[fieldName] = true
								}
							}
						}
					}
				}
			}
		}

		resultSlice = reflect.Append(resultSlice, newElem)
	}

	return resultSlice, nil
}

func IsStructSliceEqual[T1, T2 any](a []T1, b []T2, equals func(T1, T2) bool) bool {
	if len(a) != len(b) {
		return false
	}

	used := make([]bool, len(b))

	for _, itemA := range a {
		found := false
		for j, itemB := range b {
			if !used[j] && equals(itemA, itemB) {
				used[j] = true
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}
func sanitizeFieldName(s string) string {
	// Trim spaces and replace internal spaces with underscores
	s = strings.TrimSpace(s)
	s = strings.ReplaceAll(s, " ", "_")

	// Replace any other invalid characters with underscores
	var result strings.Builder
	for i, r := range s {
		if i == 0 {
			if unicode.IsLetter(r) || r == '_' {
				result.WriteRune(r)
			} else {
				result.WriteRune('_')
			}
		} else {
			if unicode.IsLetter(r) || unicode.IsDigit(r) || r == '_' {
				result.WriteRune(r)
			} else {
				result.WriteRune('_')
			}
		}
	}

	return result.String()
}

func ExtendStruct(baseStruct interface{}, optsExtraFields ...map[string]interface{}) reflect.Value {
	baseVal := reflect.ValueOf(baseStruct)
	baseType := baseVal.Type()

	var fields []reflect.StructField

	for i := 0; i < baseType.NumField(); i++ {
		field := baseType.Field(i)
		if field.PkgPath != "" {
			continue
		}
		fields = append(fields, field)
	}

	for _, extraFields := range optsExtraFields {
		for name, value := range extraFields {
			valueType := reflect.TypeOf(value)
			fields = append(fields, reflect.StructField{
				Name: name,
				Type: valueType,
				Tag:  reflect.StructTag(fmt.Sprintf(`json:"%s"`, strings.ToLower(name))),
			})
		}

	}

	newStructType := reflect.StructOf(fields)
	newStruct := reflect.New(newStructType).Elem()

	for i := 0; i < baseVal.NumField(); i++ {
		field := baseType.Field(i)
		if field.PkgPath != "" {
			continue
		}
		newStruct.FieldByName(field.Name).Set(baseVal.Field(i))
	}

	for _, extraFields := range optsExtraFields {
		for name, value := range extraFields {
			newStruct.FieldByName(name).Set(reflect.ValueOf(value))
		}
	}

	return newStruct
}

// Must have json tag
func ConvertToReflectValueSlice(values []reflect.Value) reflect.Value {
	if len(values) == 0 {
		return reflect.Value{}
	}

	elementType := values[0].Type()
	sliceType := reflect.SliceOf(elementType)
	sliceValue := reflect.MakeSlice(sliceType, 0, len(values))

	for _, val := range values {
		if val.Type() != elementType {
			newVal := reflect.New(elementType).Elem()

			sourceJSON, _ := json.Marshal(val.Interface())

			target := newVal.Addr().Interface()
			json.Unmarshal(sourceJSON, target)

			sliceValue = reflect.Append(sliceValue, newVal)
		} else {
			sliceValue = reflect.Append(sliceValue, val)
		}
	}

	return sliceValue
}
