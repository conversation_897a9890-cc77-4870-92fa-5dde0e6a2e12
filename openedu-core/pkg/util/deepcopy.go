package util

import "reflect"

func DeepCopyAny(value any) any {
	if value == nil {
		return nil
	}
	return deepCopyRecursive(reflect.ValueOf(value)).Interface()
}

func DeepCopy[T any](value T) T {
	original := reflect.ValueOf(value)
	copied := deepCopyRecursive(original)
	return copied.Interface().(T)
}

func deepCopyRecursive(val reflect.Value) reflect.Value {
	switch val.Kind() {
	case reflect.Ptr:
		if val.IsNil() {
			return reflect.Zero(val.Type())
		}
		copyPtr := reflect.New(val.Elem().Type())
		copyPtr.Elem().Set(deepCopyRecursive(val.Elem()))
		return copyPtr

	case reflect.Slice:
		if val.IsNil() {
			return reflect.Zero(val.Type())
		}
		copySlice := reflect.MakeSlice(val.Type(), val.Len(), val.Cap())
		for i := 0; i < val.Len(); i++ {
			copySlice.Index(i).Set(deepCopyRecursive(val.Index(i)))
		}
		return copySlice

	case reflect.Map:
		if val.IsNil() {
			return reflect.Zero(val.Type())
		}
		copyMap := reflect.MakeMap(val.Type())
		for _, key := range val.MapKeys() {
			newKey := deepCopyRecursive(key)
			newValue := deepCopyRecursive(val.MapIndex(key))
			// Fixed: Split the Set call into key and value arguments
			copyMap.SetMapIndex(newKey, newValue)
		}
		return copyMap
	case reflect.Struct:
		copyStruct := reflect.New(val.Type()).Elem()
		for i := 0; i < val.NumField(); i++ {
			field := val.Field(i)
			if field.CanSet() {
				copyStruct.Field(i).Set(deepCopyRecursive(field))
			}
		}
		return copyStruct
	default:
		// For basic types and non-complex kinds, return the value itself
		return reflect.ValueOf(val.Interface())
	}
}
