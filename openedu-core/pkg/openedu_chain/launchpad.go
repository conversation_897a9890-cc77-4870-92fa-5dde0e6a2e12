package openedu_chain

import (
	"encoding/json"
	"fmt"
	"openedu-core/pkg/openedu_chain/dto"
	"openedu-core/pkg/queue/producer"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

const (
	LaunchpadRpcQueryQueueName = "launchpad_rpc_query"
)

type LaunchpadService struct {
	producer producer.Producer
}

func newLaunchpadService() (*LaunchpadService, error) {
	p, err := producer.NewProducer(producer.RabbitMQ)
	if err != nil {
		return nil, fmt.Errorf("failed to create producer: %v", err)
	}

	return &LaunchpadService{
		producer: p,
	}, nil
}

func (s *LaunchpadService) GetVotingPowersByPool(req *dto.GetVotingPowersRequest) (*dto.GetVotingPowersResponse, error) {
	bytes, err := json.Marshal(dto.RpcQueryRequest{
		Type: dto.QueryTypeLaunchpadVotingPowers,
		Data: req,
	})
	if err != nil {
		return nil, fmt.Errorf("%w: marshal request failed: %w", ErrMakeRequest, err)
	}

	msg := &producer.Message{
		ID:          util.GenerateId(),
		Body:        bytes,
		ContentType: "application/json",
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+LaunchpadRpcQueryQueueName,
		msg,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: publish rpc message failed: %w", ErrMakeRequest, err)
	}
	var respData dto.ResponseT[any]
	if err := json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return nil, fmt.Errorf("%w: failed to decode base response: %w", ErrDecodeResponse, err)
	}

	if respData.Code == Success {
		var resp dto.ResponseT[dto.GetVotingPowersResponse]
		if err := json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("%w: failed to decode success response: %w", ErrDecodeResponse, err)
		}
		return &resp.Data, nil
	}

	var errorResp dto.ResponseT[dto.ErrorData]
	if err := json.Unmarshal(repliedMsg.Body, &errorResp); err != nil {
		return nil, fmt.Errorf("%w: failed to decode error response: %w", ErrDecodeResponse, err)
	}

	switch errorResp.Code {
	default:
		return nil, fmt.Errorf("%w: %s", ErrGetVotingPowersFailed, errorResp.Data.Message)
	}
}
