package openedu_chain

import (
	"encoding/json"
	"fmt"
	"openedu-core/pkg/openedu_chain/dto"
	"openedu-core/pkg/queue/producer"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

const (
	TransactionCreateQueueName = "transaction_create_queue"
)

type TransactionService struct {
	producer producer.Producer
}

func NewTransactionService() (*TransactionService, error) {
	p, err := producer.NewProducer(producer.RabbitMQ)
	if err != nil {
		return nil, fmt.Errorf("failed to create producer: %v", err)
	}

	return &TransactionService{
		producer: p,
	}, nil
}

func (s *TransactionService) Create(req *dto.CreateTransactionRequest) error {
	bytes, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	if err = s.producer.Publish(
		setting.RabbitMQSetting.Prefix+TransactionCreateQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	); err != nil {
		return fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}
	return nil
}

func (s *TransactionService) Transfer(req *dto.SingleTransferRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnTransfer, req)
}

func (s *TransactionService) BatchTransfer(req *dto.BatchTransferRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnBatchTransfer, req)
}

func (s *TransactionService) MintNFT(req *dto.MintNFTRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnMintNFT, req)
}

func (s *TransactionService) DepositSponsorGas(req *dto.DepositSponsorGasRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnDepositSponsorGas, req)
}

func (s *TransactionService) WithdrawSponsorGas(req *dto.WithdrawSponsorGasRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnWithdrawSponsorGas, req)
}

func (s *TransactionService) InitLaunchpadPool(req *dto.InitLaunchpadPoolRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnInitLaunchpadPool, req)
}

func (s *TransactionService) ApproveLaunchpadPool(req *dto.ApproveLaunchpadPoolRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnApproveLaunchpadPool, req)
}

//func (s *TransactionService) UpdateLpPoolFundingTime(req *UpdateLpPoolFundingTimeRequest) (*TransactionResponse, error) {
//	return s.createTransaction(TxnUpdateLpPoolFundingTime, req)
//}

func (s *TransactionService) PledgeLaunchpad(req *dto.PledgeLaunchpadRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnPledgeLaunchpad, req)
}

func (s *TransactionService) ClaimLaunchpadRefund(req *dto.ClaimLaunchpadRefundRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnClaimLaunchpadRefund, req)
}

func (s *TransactionService) CancelLaunchpadPool(req *dto.CancelLpPoolRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnCancelLaunchpad, req)
}

func (s *TransactionService) ContinueLpPartialFund(req *dto.ContinueLpPartialFundRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnContinueLpPartialFund, req)
}

func (s *TransactionService) SetLpFundingTime(req *dto.SetLpFundingTimeRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnSetLpPoolFundingTime, req)
}

func (s *TransactionService) UpdateLpPoolStatus(req *dto.UpdateLaunchpadPoolStatusRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnUpdateLaunchpadPoolStatus, req)
}

func (s *TransactionService) WithdrawLpFundToCreator(req *dto.WithdrawLaunchpadFundToCreatorRequest) (*dto.TransactionResponse, error) {
	return s.createTransaction(dto.TxnWithdrawLaunchpadFundToCreator, req)
}

func (s *TransactionService) CheckLpFundingResult(req *dto.CheckLpFundingResultRequest) (*dto.CheckLpFundingResultResponse, error) {
	bytes, err := json.Marshal(dto.CreateTransactionRequest{
		Type: dto.TxnCheckLpFundingResult,
		Data: req,
	})
	if err != nil {
		return nil, fmt.Errorf("%w: marshal request failed: %w", ErrMakeRequest, err)
	}

	msg := &producer.Message{
		ID:          util.GenerateId(),
		Body:        bytes,
		ContentType: "application/json",
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+TransactionCreateQueueName,
		msg,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: publish rpc message failed: %w", ErrMakeRequest, err)
	}

	var respData dto.ResponseT[any]
	if err := json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return nil, fmt.Errorf("%w: failed to decode base response: %w", ErrDecodeResponse, err)
	}

	if respData.Code == Success {
		var resp dto.ResponseT[dto.CheckLpFundingResultResponse]
		if err := json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("%w: failed to decode success response: %w", ErrDecodeResponse, err)
		}
		return &resp.Data, nil
	}

	var errorResp dto.ResponseT[dto.ErrorData]
	if err := json.Unmarshal(repliedMsg.Body, &errorResp); err != nil {
		return nil, fmt.Errorf("%w: failed to decode error response: %w", ErrDecodeResponse, err)
	}

	return nil, s.mapErrorResponse(respData.Code, errorResp.Data.Message, dto.TxnCheckLpFundingResult)
}

func (s *TransactionService) createTransaction(txType dto.TransactionType, data interface{}) (*dto.TransactionResponse, error) {
	bytes, err := json.Marshal(dto.CreateTransactionRequest{
		Type: txType,
		Data: data,
	})
	if err != nil {
		return nil, fmt.Errorf("%w: marshal request failed: %w", ErrMakeRequest, err)
	}

	msg := &producer.Message{
		ID:          util.GenerateId(),
		Body:        bytes,
		ContentType: "application/json",
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+TransactionCreateQueueName,
		msg,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: publish rpc message failed: %w", ErrMakeRequest, err)
	}

	return s.handleTransactionResponse(repliedMsg, txType)
}

func (s *TransactionService) handleTransactionResponse(repliedMsg *producer.Message, txType dto.TransactionType) (*dto.TransactionResponse, error) {
	var respData dto.ResponseT[any]
	if err := json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return nil, fmt.Errorf("%w: failed to decode base response: %w", ErrDecodeResponse, err)
	}

	if respData.Code == Success {
		var resp dto.ResponseT[dto.TransactionResponse]
		if err := json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("%w: failed to decode success response: %w", ErrDecodeResponse, err)
		}
		return &resp.Data, nil
	}

	var errorResp dto.ResponseT[dto.ErrorData]
	if err := json.Unmarshal(repliedMsg.Body, &errorResp); err != nil {
		return nil, fmt.Errorf("%w: failed to decode error response: %w", ErrDecodeResponse, err)
	}

	return nil, s.mapErrorResponse(respData.Code, errorResp.Data.Message, txType)
}

func (s *TransactionService) mapErrorResponse(code int, message string, txType dto.TransactionType) error {
	switch code {
	case WalletInvalidAddress:
		return fmt.Errorf("%w: %s", ErrInvalidAddress, message)
	case TransactionInsufficientBalance:
		return fmt.Errorf("%w: %s", ErrInsufficientBalance, message)
	case TransactionInsufficientGasFee:
		return fmt.Errorf("%w: %s", ErrInsufficientGasFee, message)
	}

	var baseErr error
	switch txType {
	case dto.TxnTransfer:
		baseErr = ErrTransferFailed
	case dto.TxnBatchTransfer:
		baseErr = ErrBatchTransferFailed
	case dto.TxnMintNFT:
		baseErr = ErrMintNFTFailed
	case dto.TxnDepositSponsorGas:
		baseErr = ErrDepositSponsorFailed
	case dto.TxnWithdrawSponsorGas:
		baseErr = ErrWithdrawSponsorFailed
	case dto.TxnInitLaunchpadPool:
		baseErr = ErrInitPoolFailed
	case dto.TxnApproveLaunchpadPool:
		baseErr = ErrApprovePoolFailed
	case dto.TxnPledgeLaunchpad:
		baseErr = ErrPledgeLaunchpadFailed
	case dto.TxnUpdateLpPoolFundingTime:
		baseErr = ErrUpdateLpPoolFundingTimeFailed
	case dto.TxnCancelLaunchpad:
		baseErr = ErrCancelLaunchpadFailed
	case dto.TxnCheckLpFundingResult:
		baseErr = ErrCheckFundingResultFailed
	default:
		baseErr = fmt.Errorf("unknown transaction type: %s", txType)
	}
	return fmt.Errorf("%w: %d - %s", baseErr, code, message)
}
