package openedu_chain

import (
	"github.com/shopspring/decimal"
	"log"
	"openedu-core/pkg/openedu_chain/dto"
)

type WalletServiceIface interface {
	Create(req *dto.CreateWalletRequest) (*dto.WalletResponse, error)
	GetAccountInfo(req *dto.GetAccountInfoRequest) (*dto.GetAccountInfoResponse, error)
	GetGasSponsorBalance(req *dto.GetWalletGasSponsorBalanceRequest) (decimal.Decimal, error)
	CreateSponsorWallet(req *dto.CreateSponsorWalletRequest) error
	CheckSponsorWalletExists(req *dto.CheckSponsorWalletExistsRequest) (bool, error)
	GetSponsorWallets(req *dto.GetSponsorWalletsRequest) (*dto.GetSponsorWalletsResponse, error)
}

type TransactionServiceIface interface {
	Create(req *dto.CreateTransactionRequest) error
	DepositSponsorGas(req *dto.DepositSponsorGasRequest) (*dto.TransactionResponse, error)
	WithdrawSponsorGas(req *dto.WithdrawSponsorGasRequest) (*dto.TransactionResponse, error)
	MintNFT(req *dto.MintNFTRequest) (*dto.TransactionResponse, error)
	BatchTransfer(req *dto.BatchTransferRequest) (*dto.TransactionResponse, error)
	Transfer(req *dto.SingleTransferRequest) (*dto.TransactionResponse, error)
	InitLaunchpadPool(req *dto.InitLaunchpadPoolRequest) (*dto.TransactionResponse, error)
	ApproveLaunchpadPool(req *dto.ApproveLaunchpadPoolRequest) (*dto.TransactionResponse, error)
	PledgeLaunchpad(req *dto.PledgeLaunchpadRequest) (*dto.TransactionResponse, error)
	CancelLaunchpadPool(req *dto.CancelLpPoolRequest) (*dto.TransactionResponse, error)
	CheckLpFundingResult(req *dto.CheckLpFundingResultRequest) (*dto.CheckLpFundingResultResponse, error)
	ContinueLpPartialFund(req *dto.ContinueLpPartialFundRequest) (*dto.TransactionResponse, error)
	WithdrawLpFundToCreator(req *dto.WithdrawLaunchpadFundToCreatorRequest) (*dto.TransactionResponse, error)
	SetLpFundingTime(req *dto.SetLpFundingTimeRequest) (*dto.TransactionResponse, error)
	ClaimLaunchpadRefund(req *dto.ClaimLaunchpadRefundRequest) (*dto.TransactionResponse, error)
	UpdateLpPoolStatus(req *dto.UpdateLaunchpadPoolStatusRequest) (*dto.TransactionResponse, error)
}

type UserServiceIface interface {
	GetEarnings(userID string) ([]*dto.WalletEarningResponse, error)
}

type LaunchpadServiceIface interface {
	GetVotingPowersByPool(req *dto.GetVotingPowersRequest) (*dto.GetVotingPowersResponse, error)
}

var Wallet WalletServiceIface
var Transaction TransactionServiceIface
var User UserServiceIface
var Launchpad LaunchpadServiceIface

func Setup() {
	walletService, err := newWalletService()
	if err != nil {
		log.Fatalf("failed to initialize wallet service: %v", err)
	}

	txService, err := NewTransactionService()
	if err != nil {
		log.Fatalf("failed to initialize transaction service: %v", err)
	}

	usrService, err := newUserService()
	if err != nil {
		log.Fatalf("failed to initialize user service: %v", err)
	}

	launchpadService, err := newLaunchpadService()
	if err != nil {
		log.Fatalf("failed to initialize launchpad service: %v", err)
	}

	Wallet = walletService
	Transaction = txService
	User = usrService
	Launchpad = launchpadService
}
