package openedu_chain

import (
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"openedu-core/pkg/openedu_chain/dto"
	"openedu-core/pkg/queue/producer"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

const (
	WalletCreateQueueName            = "wallet_create_queue"
	WalletRequestGetDetailsQueueName = "wallet_request_get_details_queue"
	WalletRPCQueryQueueName          = "wallet_rpc_query"
	SponsorWalletCreateQueueName     = "sponsor_wallet_create_queue"
)

type WalletService struct {
	producer producer.Producer
}

func newWalletService() (*WalletService, error) {
	p, err := producer.NewProducer(producer.RabbitMQ)
	if err != nil {
		return nil, fmt.Errorf("failed to create producer: %v", err)
	}

	return &WalletService{
		producer: p,
	}, nil
}

func (s *WalletService) Create(req *dto.CreateWalletRequest) (*dto.WalletResponse, error) {
	bytes, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+WalletCreateQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	)
	if err != nil {
		return nil, fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}

	var respData dto.ResponseT[any]
	if err = json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal create crypto wallet response: %w", err)
	}

	switch respData.Code {
	case Success:
		var resp dto.ResponseT[dto.WalletResponse]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal create crypto wallet response: %w", err)
		}

		return &resp.Data, nil

	default:
		var resp dto.ResponseT[dto.ErrorData]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal create crypto wallet response: %w", err)
		}

		return nil, fmt.Errorf("failed to create crypto wallet: %s (code=%d): %s",
			resp.Msg, respData.Code, resp.Data.Message)
	}
}

func (s *WalletService) GetGasSponsorBalance(
	req *dto.GetWalletGasSponsorBalanceRequest,
) (decimal.Decimal, error) {

	bytes, err := json.Marshal(dto.WalletQueryRequest{
		Type: dto.QueryTypeWalletGasSponsorBalance,
		Data: req,
	})
	if err != nil {
		return decimal.Zero, fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+WalletRPCQueryQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	)
	if err != nil {
		return decimal.Zero, fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}

	var respData dto.ResponseT[any]
	if err = json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return decimal.Zero, fmt.Errorf("failed to unmarshal get earnings by user response: %w", err)
	}

	switch respData.Code {
	case Success:
		var resp dto.ResponseT[decimal.Decimal]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return decimal.Zero, fmt.Errorf("failed to unmarshal get earnings by user response: %w", err)
		}

		return resp.Data, nil

	default:
		var resp dto.ResponseT[dto.ErrorData]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return decimal.Zero, fmt.Errorf("failed to unmarshal get earnings by user response: %w", err)
		}

		return decimal.Zero, fmt.Errorf("failed to get gas sponsor balance: %s (code=%d): %s",
			resp.Msg, respData.Code, resp.Data.Message)
	}
}

func (s *WalletService) GetAccountInfo(req *dto.GetAccountInfoRequest) (*dto.GetAccountInfoResponse, error) {
	bytes, err := json.Marshal(dto.RpcQueryRequest{
		Type: dto.QueryTypeWalletAccountInfo,
		Data: req,
	})
	if err != nil {
		return nil, fmt.Errorf("%w: marshal request failed: %w", ErrMakeRequest, err)
	}

	msg := &producer.Message{
		ID:          util.GenerateId(),
		Body:        bytes,
		ContentType: "application/json",
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+WalletRPCQueryQueueName,
		msg,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: publish rpc message failed: %w", ErrMakeRequest, err)
	}
	var respData dto.ResponseT[any]
	if err := json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return nil, fmt.Errorf("%w: failed to decode base response: %w", ErrDecodeResponse, err)
	}

	if respData.Code == Success {
		var resp dto.ResponseT[dto.GetAccountInfoResponse]
		if err := json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("%w: failed to decode success response: %w", ErrDecodeResponse, err)
		}
		return &resp.Data, nil
	}

	var errorResp dto.ResponseT[dto.ErrorData]
	if err := json.Unmarshal(repliedMsg.Body, &errorResp); err != nil {
		return nil, fmt.Errorf("%w: failed to decode error response: %w", ErrDecodeResponse, err)
	}

	switch errorResp.Code {
	case WalletInvalidAddress:
		return nil, fmt.Errorf("%w: %s", ErrInvalidAddress, errorResp.Data.Message)
	default:
		return nil, fmt.Errorf("%w: %s", ErrGetAccountInfoFailed, errorResp.Data.Message)
	}
}

func (s *WalletService) CreateSponsorWallet(req *dto.CreateSponsorWalletRequest) error {
	bytes, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	if err = s.producer.Publish(
		setting.RabbitMQSetting.Prefix+SponsorWalletCreateQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	); err != nil {
		return fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}
	return nil
}

func (s *WalletService) CheckSponsorWalletExists(req *dto.CheckSponsorWalletExistsRequest) (bool, error) {
	bytes, err := json.Marshal(dto.WalletQueryRequest{
		Type: dto.QueryTypeSponsorWalletExists,
		Data: req,
	})
	if err != nil {
		return false, fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+WalletRPCQueryQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	)
	if err != nil {
		return false, fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}

	var respData dto.ResponseT[any]
	if err = json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return false, fmt.Errorf("failed to unmarshal check sponsor wallet exists response: %w", err)
	}

	switch respData.Code {
	case Success:
		var resp dto.ResponseT[bool]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return false, fmt.Errorf("failed to unmarshal check sponsor wallet exists response: %w", err)
		}
		return resp.Data, nil

	default:
		var resp dto.ResponseT[dto.ErrorData]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return false, fmt.Errorf("failed to unmarshal check sponsor wallet exists response: %w", err)
		}
		return false, fmt.Errorf("failed to check sponsor wallet exists: %s (code=%d): %s",
			resp.Msg, respData.Code, resp.Data.Message)
	}
}

func (s *WalletService) GetSponsorWallets(req *dto.GetSponsorWalletsRequest) (*dto.GetSponsorWalletsResponse, error) {
	bytes, err := json.Marshal(dto.WalletQueryRequest{
		Type: dto.QueryTypeGetSponsorWallets,
		Data: req,
	})
	if err != nil {
		return nil, fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	repliedMsg, err := s.producer.PublishRPC(
		setting.RabbitMQSetting.Prefix+WalletRPCQueryQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	)
	if err != nil {
		return nil, fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}

	var respData dto.ResponseT[any]
	if err = json.Unmarshal(repliedMsg.Body, &respData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal get sponsor wallets response: %w", err)
	}

	switch respData.Code {
	case Success:
		var resp dto.ResponseT[dto.GetSponsorWalletsResponse]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal get sponsor wallets response: %w", err)
		}
		return &resp.Data, nil

	default:
		var resp dto.ResponseT[dto.ErrorData]
		if err = json.Unmarshal(repliedMsg.Body, &resp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal get sponsor wallets response: %w", err)
		}
		return nil, fmt.Errorf("failed to get sponsor wallets: %s (code=%d): %s",
			resp.Msg, respData.Code, resp.Data.Message)
	}
}
