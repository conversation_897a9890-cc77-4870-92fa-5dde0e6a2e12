package dto

type BlockchainNetwork string

type TransactionStatus string

type BlockchainToken string

type GetWalletDetailType string

type QueryType string

const (
	BlockchainNetworkNEAR  BlockchainNetwork = "near"
	BlockchainNetworkAVAIL BlockchainNetwork = "avail"
	BlockchainNetworkBASE  BlockchainNetwork = "base"

	QueryTypeWalletEarnings          QueryType = "earnings"
	QueryTypeWalletGasSponsorBalance QueryType = "gas_sponsor_balance"
	QueryTypeLaunchpadVotingPowers   QueryType = "launchpad_voting_powers"
	QueryTypeWalletAccountInfo       QueryType = "wallet_account_info"
	QueryTypeSponsorWalletExists     QueryType = "sponsor_wallet_exists"
	QueryTypeGetSponsorWallets       QueryType = "get_sponsor_wallets"

	BlockchainTokenNEAR    BlockchainToken = "NEAR"
	BlockchainTokenUSDT    BlockchainToken = "USDT"
	BlockchainTokenUSDC    BlockchainToken = "USDC"
	BlockchainTokenOpenEdu BlockchainToken = "OPENEDU"
)

type Model struct {
	ID       string `json:"id"`
	CreateAt int64  `json:"create_at"`
	UpdateAt int64  `json:"update_at"`
	DeleteAt int64  `json:"delete_at"`
}

type RpcQueryRequest struct {
	Type QueryType   `json:"type"`
	Data interface{} `json:"data"`
}

type ResponseT[T any] struct {
	Data T      `json:"data"`
	Msg  string `json:"msg"`
	Code int    `json:"code"`
}

type ErrorData struct {
	Message string `json:"message"`
}
