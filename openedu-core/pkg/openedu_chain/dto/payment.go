package dto

import "github.com/shopspring/decimal"

type PaymentRequest struct {
	WalletID            string                `json:"wallet_id"`
	CoreTxID            string                `json:"core_tx_id"`
	Token               BlockchainToken       `json:"token"`
	Amount              decimal.Decimal       `json:"amount"`
	ProfitDistributions []*ProfitDistribution `json:"profit_distributions"`
	IsMainnet           bool                  `json:"is_mainnet"`
}

type ProfitDistribution struct {
	Address string            `json:"address"`
	Network BlockchainNetwork `json:"network"`
	Amount  decimal.Decimal   `json:"amount"`
}

type ClaimEarningRequest struct {
	WalletID  string          `json:"wallet_id"`
	CoreTxID  string          `json:"core_tx_id"`
	Token     BlockchainToken `json:"token"`
	IsMainnet bool            `json:"is_mainnet"`
}
