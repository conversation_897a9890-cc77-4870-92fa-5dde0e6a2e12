package dto

import "github.com/shopspring/decimal"

type MintNFTRequest struct {
	GasFeePayer        string               `json:"gas_fee_payer"`
	CourseCuid         string               `json:"course_cuid"`
	CourseOwnerAddress string               `json:"course_owner_address"`
	TokenMetadata      TokenMetadataRequest `json:"token_metadata"`
	ReceiverWalletID   string               `json:"receiver_wallet_id"`
	CoreTxID           string               `json:"core_tx_id"`
	IsMainnet          bool                 `json:"is_mainnet"`
	Network            BlockchainNetwork    `json:"network"`
}

type TokenMetadataRequest struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	MediaURL    string `json:"media_url"`
}

type DepositSponsorGasRequest struct {
	WalletID   string            `json:"wallet_id"`
	CoreTxID   string            `json:"core_tx_id"`
	Amount     decimal.Decimal   `json:"amount"`
	Token      BlockchainToken   `json:"token"`
	Network    BlockchainNetwork `json:"network"`
	CourseCuid string            `json:"course_cuid"`
	IsMainnet  bool              `json:"is_mainnet"`
}

type WithdrawSponsorGasRequest struct {
	WalletID   string            `json:"wallet_id"`
	CoreTxID   string            `json:"core_tx_id"`
	Amount     decimal.Decimal   `json:"amount"`
	Token      BlockchainToken   `json:"token"`
	Network    BlockchainNetwork `json:"network"`
	CourseCuid string            `json:"course_cuid"`
	IsMainnet  bool              `json:"is_mainnet"`
}
