package dto

import "github.com/shopspring/decimal"

type WalletQueryRequest struct {
	Type QueryType   `json:"type"`
	Data interface{} `json:"data"`
}

type CreateWalletRequest struct {
	UserID       string            `json:"user_id"`
	Network      BlockchainNetwork `json:"network"`
	CoreWalletID string            `json:"core_wallet_id"`
}

type WalletResponse struct {
	Model
	UserID       string `json:"user_id"`
	Address      string `json:"address"`
	PublicKey    string `json:"public_key"`
	Type         string `json:"type"`
	Status       string `json:"status"`
	CoreWalletID string `json:"core_wallet_id"`
}

type GetAccountInfoRequest struct {
	Network   BlockchainNetwork `json:"network"`
	Address   string            `json:"address"`
	IsMainnet bool              `json:"is_mainnet"`
}

type GetAccountInfoResponse struct {
	Network     BlockchainNetwork `json:"network"`
	Address     string            `json:"address"`
	AccountInfo interface{}       `json:"account_info"`
}

type WalletEarningResponse struct {
	BlockchainWalletID string            `json:"blockchain_wallet_id"`
	CoreWalletID       string            `json:"core_wallet_id"`
	Address            string            `json:"address"`
	Network            BlockchainNetwork `json:"network"`
	Token              BlockchainToken   `json:"token"`
	TokenID            string            `json:"token_id"`
	Amount             decimal.Decimal   `json:"amount"`
}

type GetWalletGasSponsorBalanceRequest struct {
	WalletID   string `json:"wallet_id"`
	CourseCuid string `json:"course_cuid"`
	IsMainnet  bool   `json:"is_mainnet"`
}

type CheckSponsorWalletExistsRequest struct {
	WalletID  string `json:"wallet_id"`
	IsMainnet bool   `json:"is_mainnet"`
}

type GetSponsorWalletsRequest struct {
	WalletID  string `json:"wallet_id,omitempty"`
	SponsorID string `json:"sponsor_id,omitempty"`
	Network   string `json:"network,omitempty"`
	Status    string `json:"status,omitempty"`
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
	IsMainnet bool   `json:"is_mainnet"`
}

type SponsorWalletData struct {
	ID          string `json:"id"`
	WalletID    string `json:"wallet_id"`
	SponsorID   string `json:"sponsor_id"`
	SponsorName string `json:"sponsor_name"`
	Description string `json:"description"`
	Balance     string `json:"balance"`
	Network     string `json:"network"`
	Status      string `json:"status"`
	Address     string `json:"address"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

type GetSponsorWalletsResponse struct {
	Results    []SponsorWalletData `json:"results"`
	Pagination struct {
		Page       int `json:"page"`
		PerPage    int `json:"per_page"`
		Total      int `json:"total"`
		TotalPages int `json:"total_pages"`
	} `json:"pagination"`
}

type CreateSponsorWalletRequest struct {
	WalletID    string `json:"wallet_id"`
	CoreTxID    string `json:"core_tx_id"`
	SponsorID   string `json:"sponsor_id"`
	SponsorName string `json:"sponsor_name"`
	Description string `json:"description"`
	Amount      string `json:"amount"`
	Token       string `json:"token"`
	Network     string `json:"network"`
	IsMainnet   bool   `json:"is_mainnet"`
}
