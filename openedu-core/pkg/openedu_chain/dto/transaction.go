package dto

import "github.com/shopspring/decimal"

type TransactionType string

const (
	TxnTransfer                       TransactionType = "transfer"
	TxnMintNFT                        TransactionType = "mint_nft"
	TxnPayment                        TransactionType = "payment"
	TxnClaimEarning                   TransactionType = "claim_earning"
	TxnBatchTransfer                  TransactionType = "batch_transfer"
	TxnDepositSponsorGas              TransactionType = "deposit_sponsor_gas"
	TxnWithdrawSponsorGas             TransactionType = "withdraw_sponsor_gas"
	TxnInitLaunchpadPool              TransactionType = "init_launchpad_pool"
	TxnApproveLaunchpadPool           TransactionType = "approve_launchpad_pool"
	TxnUpdateLaunchpadPoolStatus      TransactionType = "update_launchpad_pool_status"
	TxnPledgeLaunchpad                TransactionType = "pledge_launchpad"
	TxnUpdateLpPoolFundingTime        TransactionType = "update_launchpad_funding_time"
	TxnCancelLaunchpad                TransactionType = "cancel_launchpad"
	TxnCheckLpFundingResult           TransactionType = "check_launchpad_funding_result"
	TxnContinueLpPartialFund          TransactionType = "continue_launchpad_partial_fund"
	TxnWithdrawLaunchpadFundToCreator TransactionType = "withdraw_launchpad_fund_to_creator"
	TxnSetLpPoolFundingTime           TransactionType = "set_launchpad_funding_time"
	TxnClaimLaunchpadRefund           TransactionType = "claim_launchpad_refund"
)

type CreateTransactionRequest struct {
	Type TransactionType `json:"type"`
	Data interface{}     `json:"data"`
}

type SingleTransferRequest struct {
	SenderWalletID string            `json:"sender_wallet_id"`
	CoreTxID       string            `json:"core_tx_id"`
	Amount         decimal.Decimal   `json:"amount"`
	ToAddress      string            `json:"to_address"`
	Network        BlockchainNetwork `json:"network"`
	Token          BlockchainToken   `json:"token"`
	ContractID     string            `json:"contract_id"`
	IsMainnet      bool              `json:"is_mainnet"`
}

type BatchTransferRequest struct {
	SenderWalletID string                    `json:"sender_wallet_id"`
	CoreTxID       string                    `json:"core_tx_id"`
	Recipients     []*BatchTransferRecipient `json:"recipients"`
	Token          BlockchainToken           `json:"token"`
	Network        BlockchainNetwork         `json:"network"`
	IsMainnet      bool                      `json:"is_mainnet"`
}

type BatchTransferRecipient struct {
	Address string          `json:"address"`
	Amount  decimal.Decimal `json:"amount"`
}

type TransactionResponse struct {
	Model
	WalletID    string                 `json:"wallet_id"`
	FromAddress string                 `json:"from_address"`
	FromNetwork BlockchainNetwork      `json:"from_network"`
	ToAddress   string                 `json:"to_address"`
	ToNetwork   BlockchainNetwork      `json:"to_network"`
	Status      TransactionStatus      `json:"status"`
	ErrorCode   int                    `json:"error_code"`
	Type        TransactionType        `json:"type"`
	TxHash      string                 `json:"tx_hash"`
	MethodName  string                 `json:"method_name"`
	InputData   map[string]interface{} `json:"input_data" gorm:"type:jsonb"`
	Deposit     decimal.Decimal        `json:"value"`
	Token       BlockchainToken        `json:"token"`
	ContractID  string                 `json:"contract_id"`
	GasLimit    uint64                 `json:"gas_limit"`
	GasBurnt    uint64                 `json:"gas_burnt"`
	Nonce       uint64                 `json:"nonce"`
	BlockHash   string                 `json:"block_hash"`
	IsMainnet   bool                   `json:"is_mainnet"`
	Props       TransactionProps       `json:"props"`
}

type TransactionProps struct {
	CoreTxIDs []string `json:"core_tx_ids"`

	Recipients any `json:"recipients,omitempty"`

	NftTokenID   string          `json:"nft_token_id,omitempty"`
	StorageCost  decimal.Decimal `json:"storage_cost,omitempty"`
	GasCost      decimal.Decimal `json:"gas_cost,omitempty"`
	TotalGasCost decimal.Decimal `json:"total_gas_cost,omitempty"`

	PoolID string `json:"pool_id,omitempty"`
}
