package metrics

import (
	"os"
	"runtime"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/process"
	"gorm.io/gorm"
)

type MetricCollector interface {
	Start()
	GetMetrics() []prometheus.Collector
}

var (
	startTime = time.Now()
)

// SystemMetrics contains system metrics
type SystemMetrics struct {
	ServiceUptime       prometheus.GaugeVec
	ServiceHealth       prometheus.GaugeVec
	ServiceRestartCount prometheus.GaugeVec
	CPUUsagePercent     prometheus.GaugeVec
	RAMUsageBytes       prometheus.GaugeVec
	GoroutineCount      prometheus.GaugeVec
}

// HttpMetrics contains HTTP metrics
type HttpMetrics struct {
	RequestDuration  prometheus.HistogramVec
	RequestsTotal    prometheus.CounterVec
	RequestsInFlight prometheus.GaugeVec
	ResponseSize     prometheus.HistogramVec
}

// SqlDbMetrics contains SQL Database metrics
type SqlDbMetrics struct {
	Connections   prometheus.GaugeVec
	QueryDuration prometheus.HistogramVec
	Errors        prometheus.CounterVec
}

// MongoDBMetrics contains Mongo DB metrics
type MongoDBMetrics struct {
	Operations        prometheus.CounterVec
	OperationDuration prometheus.HistogramVec
	Connections       prometheus.GaugeVec
	Errors            prometheus.CounterVec
}

// Registry holds all prometheus metrics
type Registry struct {
	ServiceName string
	Stage       string
	System      SystemMetrics
	Http        HttpMetrics
	Postgres    SqlDbMetrics
	//MongoDB     MongoDBMetrics
}

// NewMetricsRegistry creates and registers all prometheus metrics
func NewMetricsRegistry(serviceName, stage string) *Registry {
	return &Registry{
		ServiceName: serviceName,
		Stage:       stage,
		System: SystemMetrics{
			ServiceUptime: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "service_uptime_seconds",
					Help: "How long the service has been running in seconds",
				},
				[]string{"stage", "service"},
			),
			ServiceHealth: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "service_health_status",
					Help: "Service health status (1 = alive, 0 = dead)",
				},
				[]string{"stage", "service"},
			),
			ServiceRestartCount: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "service_restart_count",
					Help: "Number of times the service has been restarted",
				},
				[]string{"stage", "service"},
			),
			CPUUsagePercent: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "service_cpu_usage_percent",
					Help: "CPU usage percentage of the service",
				},
				[]string{"stage", "service"},
			),
			RAMUsageBytes: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "service_memory_usage_bytes",
					Help: "Memory usage of the service in bytes",
				},
				[]string{"stage", "service", "type"},
			),
			GoroutineCount: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "service_goroutine_count",
					Help: "Number of goroutines currently running",
				},
				[]string{"stage", "service"},
			),
		},
		Http: HttpMetrics{
			RequestDuration: *promauto.NewHistogramVec(
				prometheus.HistogramOpts{
					Name:    "http_request_duration_seconds",
					Help:    "Duration of HTTP requests in seconds",
					Buckets: []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
				},
				[]string{"handler", "method", "status", "stage", "service"},
			),
			RequestsTotal: *promauto.NewCounterVec(
				prometheus.CounterOpts{
					Name: "http_requests_total",
					Help: "Total number of HTTP requests",
				},
				[]string{"handler", "method", "status", "stage", "service"},
			),
			RequestsInFlight: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "http_requests_in_flight",
					Help: "Current number of HTTP requests in flight",
				},
				[]string{"stage", "service"},
			),
			ResponseSize: *promauto.NewHistogramVec(
				prometheus.HistogramOpts{
					Name:    "http_response_size_bytes",
					Help:    "Size of HTTP responses in bytes",
					Buckets: prometheus.ExponentialBuckets(100, 10, 8),
				},
				[]string{"handler", "method", "stage", "service"},
			),
		},
		Postgres: SqlDbMetrics{
			Connections: *promauto.NewGaugeVec(
				prometheus.GaugeOpts{
					Name: "sqldb_connections",
					Help: "Number of connections to SQL database",
				},
				[]string{"stage", "service", "state", "db_type"},
			),
			QueryDuration: *promauto.NewHistogramVec(
				prometheus.HistogramOpts{
					Name:    "sqldb_query_duration_seconds",
					Help:    "Duration of SQL database queries in seconds",
					Buckets: []float64{.001, .005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5},
				},
				[]string{"stage", "service", "operation", "db_type"},
			),
			Errors: *promauto.NewCounterVec(
				prometheus.CounterOpts{
					Name: "sqldb_query_errors_total",
					Help: "Total number of SQL database query errors",
				},
				[]string{"stage", "service", "operation", "db_type"},
			),
		},
		//MongoDB: MongoDBMetrics{
		//	Operations: *promauto.NewCounterVec(
		//		prometheus.CounterOpts{
		//			Name: "mongodb_operations_total",
		//			Help: "Total number of MongoDB operations",
		//		},
		//		[]string{"stage", "service", "operation", "database", "collection"},
		//	),
		//	OperationDuration: *promauto.NewHistogramVec(
		//		prometheus.HistogramOpts{
		//			Name:    "mongodb_operation_duration_seconds",
		//			Help:    "Duration of MongoDB operations in seconds",
		//			Buckets: []float64{.001, .005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5},
		//		},
		//		[]string{"stage", "service", "operation", "database", "collection"},
		//	),
		//	Connections: *promauto.NewGaugeVec(
		//		prometheus.GaugeOpts{
		//			Name: "mongodb_connections",
		//			Help: "Number of connections to MongoDB",
		//		},
		//		[]string{"stage", "service", "state"},
		//	),
		//	Errors: *promauto.NewCounterVec(
		//		prometheus.CounterOpts{
		//			Name: "mongodb_operation_errors_total",
		//			Help: "Total number of MongoDB operation errors",
		//		},
		//		[]string{"stage", "service", "operation", "database", "collection"},
		//	),
		//},
	}
}

// SystemMetricsCollector collects system metrics
type SystemMetricsCollector struct {
	registry *Registry
}

func NewSystemMetricsCollector(registry *Registry) *SystemMetricsCollector {
	return &SystemMetricsCollector{
		registry: registry,
	}
}

func (c *SystemMetricsCollector) Start() {
	go func() {
		proc, err := process.NewProcess(int32(os.Getpid()))
		if err != nil {
			return
		}

		for {
			// Update service uptime
			uptime := time.Since(startTime).Seconds()
			c.registry.System.ServiceUptime.WithLabelValues(c.registry.Stage, c.registry.ServiceName).Set(uptime)

			// Update service health (1 = alive)
			c.registry.System.ServiceHealth.WithLabelValues(c.registry.Stage, c.registry.ServiceName).Set(1)

			// Update CPU usage
			cpuPercent, cpuErr := proc.CPUPercent()
			if cpuErr == nil {
				c.registry.System.CPUUsagePercent.WithLabelValues(c.registry.Stage, c.registry.ServiceName).Set(cpuPercent)
			}

			// Update memory usage
			memInfo, memErr := proc.MemoryInfo()
			if memErr == nil {
				// RSS (Resident Set Size) - actual memory used
				c.registry.System.RAMUsageBytes.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "rss").Set(float64(memInfo.RSS))
				// VMS (Virtual Memory Size)
				c.registry.System.RAMUsageBytes.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "vms").Set(float64(memInfo.VMS))
			}

			// Update total system memory stats
			virtualMemory, vErr := mem.VirtualMemory()
			if vErr == nil {
				c.registry.System.RAMUsageBytes.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "system_total").Set(float64(virtualMemory.Total))
				c.registry.System.RAMUsageBytes.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "system_used").Set(float64(virtualMemory.Used))
			}

			// Update goroutine count
			c.registry.System.GoroutineCount.WithLabelValues(c.registry.Stage, c.registry.ServiceName).Set(float64(runtime.NumGoroutine()))

			time.Sleep(5 * time.Second)
		}
	}()
}

func (c *SystemMetricsCollector) GetMetrics() []prometheus.Collector {
	return []prometheus.Collector{
		&c.registry.System.ServiceUptime,
		&c.registry.System.ServiceHealth,
		&c.registry.System.ServiceRestartCount,
		&c.registry.System.CPUUsagePercent,
		&c.registry.System.RAMUsageBytes,
		&c.registry.System.GoroutineCount,
	}
}

// SqlDbMetricsCollector collects SQL database metrics
type SqlDbMetricsCollector struct {
	registry *Registry
	db       *gorm.DB
	dbType   string
}

func NewSqlDbMetricsCollector(registry *Registry, db *gorm.DB, dbType string) *SqlDbMetricsCollector {
	if dbType == "" {
		dbType = "unknown"
	}

	collector := &SqlDbMetricsCollector{
		registry: registry,
		db:       db,
		dbType:   dbType,
	}

	collector.setupCallbacks()
	return collector
}

func (c *SqlDbMetricsCollector) setupCallbacks() {
	// Add callback for before query execution
	_ = c.db.Callback().Create().Before("gorm:create").Register("metrics:before_create", func(d *gorm.DB) {
		d.Set("start_time", time.Now())
	})

	// Add callback for after query execution
	_ = c.db.Callback().Create().After("gorm:create").Register("metrics:after_create", func(d *gorm.DB) {
		if startAt, ok := d.Get("start_time"); ok {
			duration := time.Since(startAt.(time.Time)).Seconds()
			c.registry.Postgres.QueryDuration.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "create", c.dbType).Observe(duration)

			if d.Error != nil {
				c.registry.Postgres.Errors.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "create", c.dbType).Inc()
			}
		}
	})

	// Similar callbacks for query, update, delete
	_ = c.db.Callback().Query().Before("gorm:query").Register("metrics:before_query", func(d *gorm.DB) {
		d.Set("start_time", time.Now())
	})

	_ = c.db.Callback().Query().After("gorm:query").Register("metrics:after_query", func(d *gorm.DB) {
		if startAt, ok := d.Get("start_time"); ok {
			duration := time.Since(startAt.(time.Time)).Seconds()
			c.registry.Postgres.QueryDuration.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "query", c.dbType).Observe(duration)

			if d.Error != nil {
				c.registry.Postgres.Errors.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "query", c.dbType).Inc()
			}
		}
	})

	_ = c.db.Callback().Update().Before("gorm:update").Register("metrics:before_update", func(d *gorm.DB) {
		d.Set("start_time", time.Now())
	})

	_ = c.db.Callback().Update().After("gorm:update").Register("metrics:after_update", func(d *gorm.DB) {
		if startAt, ok := d.Get("start_time"); ok {
			duration := time.Since(startAt.(time.Time)).Seconds()
			c.registry.Postgres.QueryDuration.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "update", c.dbType).Observe(duration)

			if d.Error != nil {
				c.registry.Postgres.Errors.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "update", c.dbType).Inc()
			}
		}
	})

	_ = c.db.Callback().Delete().Before("gorm:delete").Register("metrics:before_delete", func(d *gorm.DB) {
		d.Set("start_time", time.Now())
	})

	_ = c.db.Callback().Delete().After("gorm:delete").Register("metrics:after_delete", func(d *gorm.DB) {
		if startAt, ok := d.Get("start_time"); ok {
			duration := time.Since(startAt.(time.Time)).Seconds()
			c.registry.Postgres.QueryDuration.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "delete", c.dbType).Observe(duration)

			if d.Error != nil {
				c.registry.Postgres.Errors.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "delete", c.dbType).Inc()
			}
		}
	})
}

func (c *SqlDbMetricsCollector) Start() {
	go func() {
		for {
			// Get database stats from the connection pool
			sqlDB, err := c.db.DB()
			if err == nil {
				stats := sqlDB.Stats()
				c.registry.Postgres.Connections.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "in_use", c.dbType).Set(float64(stats.InUse))
				c.registry.Postgres.Connections.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "idle", c.dbType).Set(float64(stats.Idle))
				c.registry.Postgres.Connections.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "total", c.dbType).Set(float64(stats.OpenConnections))
				c.registry.Postgres.Connections.WithLabelValues(c.registry.Stage, c.registry.ServiceName, "max_open", c.dbType).Set(float64(stats.MaxOpenConnections))
			}

			time.Sleep(5 * time.Second)
		}
	}()
}

func (c *SqlDbMetricsCollector) GetMetrics() []prometheus.Collector {
	return []prometheus.Collector{
		&c.registry.Postgres.Connections,
		&c.registry.Postgres.QueryDuration,
		&c.registry.Postgres.Errors,
	}
}

// Builder builds a Prometheus metrics middleware for Gin
type Builder struct {
	registry   *Registry
	collectors []MetricCollector
}

// NewMetricsBuilder creates a new metrics builder
func NewMetricsBuilder(serviceName, stage string) *Builder {
	return &Builder{
		registry:   NewMetricsRegistry(serviceName, stage),
		collectors: make([]MetricCollector, 0),
	}
}

// WithSystemMetrics adds system metrics to the builder
func (b *Builder) WithSystemMetrics() *Builder {
	collector := NewSystemMetricsCollector(b.registry)
	b.collectors = append(b.collectors, collector)
	return b
}

// WithSqlDbMetrics adds SQL database metrics to the builder
func (b *Builder) WithSqlDbMetrics(db *gorm.DB, dbType string) *Builder {
	if db != nil {
		collector := NewSqlDbMetricsCollector(b.registry, db, dbType)
		b.collectors = append(b.collectors, collector)
	}
	return b
}

// Build creates a Gin middleware that records HTTP metrics
func (b *Builder) Build() gin.HandlerFunc {
	// Start all collectors
	for _, collector := range b.collectors {
		collector.Start()
	}

	// Create and return the middleware function
	return func(c *gin.Context) {
		start := time.Now()
		path := c.FullPath()
		if path == "" {
			path = "undefined"
		}

		b.registry.Http.RequestsInFlight.WithLabelValues(b.registry.Stage, b.registry.ServiceName).Inc()
		defer b.registry.Http.RequestsInFlight.WithLabelValues(b.registry.Stage, b.registry.ServiceName).Dec()

		c.Next()

		duration := time.Since(start).Seconds()
		status := strconv.Itoa(c.Writer.Status())

		handlerName, exists := c.Get("controller_name")
		if !exists {
			handlerName = path
		}

		b.registry.Http.RequestDuration.WithLabelValues(
			handlerName.(string),
			c.Request.Method,
			status,
			b.registry.Stage,
			b.registry.ServiceName,
		).Observe(duration)

		b.registry.Http.RequestsTotal.WithLabelValues(
			handlerName.(string),
			c.Request.Method,
			status,
			b.registry.Stage,
			b.registry.ServiceName,
		).Inc()

		b.registry.Http.ResponseSize.WithLabelValues(
			handlerName.(string),
			c.Request.Method,
			b.registry.Stage,
			b.registry.ServiceName,
		).Observe(float64(c.Writer.Size()))
	}
}
