package payment

import (
	"fmt"
	"github.com/shopspring/decimal"
)

type PaymentServiceName string

const (
	PaymentServiceSepay PaymentServiceName = "Sepay"
)

type PaymentServiceProvider interface {
	GetUrl(account string, service string, amount decimal.Decimal, des string) *string
}

func Setup() {
}

func New(providerName PaymentServiceName) (PaymentServiceProvider, error) {
	var provider PaymentServiceProvider

	switch providerName {
	case PaymentServiceSepay:
		provider = newSepayProvider()

	default:
		return nil, fmt.Errorf("invalid payment provider")
	}

	return provider, nil
}
