package httpclient

import (
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/http"
	"openedu-core/pkg/e"
	"strings"
)

var (
	ErrMakeRequest          = errors.New("making http request failed")
	ErrDecodeResponse       = errors.New("decoding http response failed")
	ErrInvalidParams        = errors.New("invalid params")
	ErrUnknown              = errors.New("unknown error")
	ErrCreateTrackingFailed = errors.New("create tracking failed")
	ErrNoContext            = errors.New("no context error")
	ErrExceedTokenLength    = errors.New("exceed token length error")
	ErrInternalAIServer     = errors.New("internal AI server error")
	ErrConnectionFailed     = errors.New("cannot connect to server endpoint")
	ErrNotFoundEndpoint     = errors.New("endpoint not found")
)

func IsConnectionError(err error) bool {
	if err == nil {
		return false
	}

	if netErr, ok := err.(net.Error); ok {
		return netErr.Timeout()
	}

	errStr := strings.ToLower(err.Error())
	connectionErrors := []string{
		"connection refused",
		"no such host",
		"network is unreachable",
		"connection reset",
		"dial tcp",
		"no route to host",
		"timeout",
	}

	for _, phrase := range connectionErrors {
		if strings.Contains(errStr, phrase) {
			return true
		}
	}

	return false
}

func handleErrorResp(resp *http.Response, body []byte) error {
	if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusCreated {
		return nil
	}

	var errResp Response[ErrorData]
	if err := json.Unmarshal(body, &errResp); err != nil {
		return fmt.Errorf("%w: unmarshal response body error: %w with bodyResp: %s", ErrDecodeResponse, err, string(body))
	}

	switch errResp.Code {
	case e.INVALID_PARAMS:
		return fmt.Errorf("%w: %s", ErrInvalidParams, errResp.Data.Message)
	default:
		return fmt.Errorf("%w: status %d - error code %d - error message %s", ErrUnknown, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}

}
