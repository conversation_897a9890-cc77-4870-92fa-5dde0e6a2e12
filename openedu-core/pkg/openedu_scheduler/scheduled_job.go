package openedu_scheduler

import (
	"encoding/json"
	"fmt"
	"net/http"
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
)

func (s *ScheduledService) CreateScheduledJob(job *ScheduledJobRequest) error {
	reqBody := map[string]any{
		"name":            job.Name,
		"key":             job.Key,
		"type":            job.Type,
		"args":            job.Args,
		"schedule_at":     job.ScheduleAt,
		"cron_expression": job.CronExpression,
		"active":          job.Active,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"X-api-key":    setting.OpeneduSchedulerSetting.ApiKey,
	}

	resp, body, err := s.httpClient.Post(scheduledPath, reqBody, headers)
	if err != nil {
		log.Debugf("Call create scheduled job to scheduler:  %v", err.Error())
		return err
	}

	if resp != nil && resp.StatusCode == http.StatusCreated {
		return nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *ScheduledService) UpdateScheduledJob(jobID string, job *ScheduledJobRequest) error {
	reqBody := map[string]any{
		"name":            job.Name,
		"key":             job.Key,
		"type":            job.Type,
		"args":            job.Args,
		"schedule_at":     job.ScheduleAt,
		"cron_expression": job.CronExpression,
		"active":          job.Active,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"X-api-key":    setting.AppSetting.ApiKey,
	}

	// Construct URL with ID parameter
	url := scheduledPath
	url = fmt.Sprintf("%s/%s", scheduledPath, jobID)
	resp, body, err := s.httpClient.Put(url, reqBody, headers)
	if err != nil {
		log.Debugf("Call update scheduled job to scheduler:  %v", err.Error())
		return err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		return nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}

func (s *ScheduledService) DeleteScheduledJob(jobID string) error {
	headers := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
		"X-api-key":    setting.AppSetting.ApiKey,
	}

	// Construct URL with ID parameter
	url := scheduledPath
	url = fmt.Sprintf("%s/%s", scheduledPath, jobID)
	resp, body, err := s.httpClient.Delete(url, nil, headers)
	if err != nil {
		log.Debugf("Call delete scheduled job to scheduler:  %v", err.Error())
		return err
	}

	if resp != nil && resp.StatusCode == http.StatusOK {
		return nil
	}

	var errResp httpclient.Response[httpclient.ErrorData]
	if err = json.Unmarshal(body, &errResp); err != nil {
		return fmt.Errorf("%w: unmarshal response body error: %w", httpclient.ErrDecodeResponse, err)
	}

	switch errResp.Code {
	case http.StatusNotFound:
		return fmt.Errorf("%w: %s", httpclient.ErrNoContext, errResp.Data.Message)
	case http.StatusBadRequest:
		return fmt.Errorf("%w: %s", httpclient.ErrExceedTokenLength, errResp.Data.Message)
	default:
		return fmt.Errorf("%w: status %d - error code %d - error message %s", httpclient.ErrInternalAIServer, resp.StatusCode, errResp.Code, errResp.Data.Message)
	}
}
