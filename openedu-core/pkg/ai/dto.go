package ai

type OfferCourseFromYoutubeRequest struct {
	PlaylistID      string `json:"playlist_id"`
	Tone            <PERSON>ne   `json:"tone"`
	Language        string `json:"language"`
	SummaryIncluded bool   `json:"summary_included"`
	XAPIKey         string `json:"-"`
}

type OfferCourseFromYoutubeResponse struct {
	StatusCode int32                       `json:"code,omitempty"`
	Message    string                      `json:"msg,omitempty"`
	Data       *OfferCourseFromYoutubeData `json:"data"`
}

type OfferCourseFromYoutubeData struct {
	OfferID string `json:"offer_id,omitempty"`
}

type GenerateCourseFromYoutubeResponse struct {
	StatusCode int32                          `json:"code,omitempty"`
	Message    string                         `json:"msg,omitempty"`
	Data       *GenerateCourseFromYoutubeData `json:"data"`
}

type GenerateCourseFromYoutubeData struct {
	Status   GenerateStatus  `json:"status"`
	Metadata *CourseMetadata `json:"metadata"`
}

type CourseMetadata struct {
	CourseTitle       string         `json:"course_title"`
	CourseDescription string         `json:"course_description"`
	CourseThumbnail   string         `json:"course_thumbnail"`
	Sections          []*SectionInfo `json:"sections"`
	Cost              float64        `json:"cost,omitempty"`
}

type SectionInfo struct {
	SectionName string        `json:"section_name"`
	Lessons     []*LessonInfo `json:"lessons"`
}

type LessonInfo struct {
	LessonName     string `json:"lesson_name"`
	LessonEmbedded string `json:"lesson_embeded"`
	LessonText     string `json:"lesson_text"`
}

type OfferGenerateQuizRequest struct {
	LessonName     string   `json:"lesson_name"`
	LessonEmbedded string   `json:"lesson_embedded"`
	QuestionType   QuizType `json:"question_type"`
	Language       string   `json:"language"`
	TotalQuestion  int16    `json:"total_question"`
	XAPIKey        string   `json:"-"`
}

type OfferQuizResponse struct {
	StatusCode int32          `json:"code,omitempty"`
	Message    string         `json:"msg,omitempty"`
	Data       *OfferQuizData `json:"data"`
}

type OfferQuizData struct {
	OfferID string `json:"offer_id,omitempty"`
}

type GenerateQuizResponse struct {
	StatusCode int32             `json:"code"`
	Message    string            `json:"msg"`
	Data       *GenerateQuizData `json:"data"`
}

type GenerateQuizData struct {
	Status   GenerateStatus `json:"status"`
	Metadata *QuizMetadata  `json:"metadata"`
}

type QuizMetadata struct {
	Quiz []*QuizItem `json:"quiz"`
	Cost float64     `json:"cost"`
}

type QuizItem struct {
	QuestionContent string   `json:"question_content"`
	Options         []string `json:"options"`
	CorrectAnswer   []string `json:"correct_answer"`
	Explanation     string   `json:"explanation"`
	QuestionNumber  int16    `json:"question_number"`
	QuestionType    QuizType `json:"question_type"`
	Duration        int16    `json:"duration"`
	Point           int16    `json:"point"`
}

type OfferCourseFromLearnerDescriptionRequest struct {
	LearnerInformation string       `json:"learner_information"`
	CourseContent      string       `json:"course_content"`
	MaterialURLs       []string     `json:"material_urls"`
	CourseLevel        string       `json:"course_level"`
	Language           string       `json:"language"`
	CourseDuration     int16        `json:"course_duration"`
	CourseDurationIn   DurationType `json:"course_duration_in"`
	StudyLoad          int16        `json:"study_load"`
	XAPIKey            string       `json:"-"`
}

type OfferCourseFromLearnerDescriptionResponse struct {
	StatusCode int32                                  `json:"code,omitempty"`
	Message    string                                 `json:"msg,omitempty"`
	Data       *OfferCourseFromLearnerDescriptionData `json:"data"`
}

type OfferCourseFromLearnerDescriptionData struct {
	OfferID string `json:"offer_id,omitempty"`
}

type GenerateCourseFromLearnerDescriptionResponse struct {
	StatusCode int32                                     `json:"code,omitempty"`
	Message    string                                    `json:"msg,omitempty"`
	Data       *GenerateCourseFromLearnerDescriptionData `json:"data"`
}

type GenerateCourseFromLearnerDescriptionData struct {
	Metadata *CourseFromLearnerDescriptionMetadata `json:"metadata"`
	Status   GenerateStatus                        `json:"status"`
}

type CourseFromLearnerDescriptionMetadata struct {
	CourseTitle       string  `json:"course_title,omitempty"`
	CourseDescription string  `json:"course_description,omitempty"`
	ThumbnailPrompt   string  `json:"course_thumbnail_prompt,omitempty"`
	Cost              float64 `json:"cost"`
}

type OfferGenerateThumbnailRequest struct {
	ThumbnailPrompt    string         `json:"course_thumbnail_prompt"`
	NumberOfThumbnails int16          `json:"number_of_thumbnails"`
	ThumbnailStyle     ThumbnailStyle `json:"style"`
	OfferID            string         `json:"offer_id"`
	XAPIKey            string         `json:"-"`
}

// learner
type OfferGenerateThumbnailResponse struct {
	StatusCode int32                                  `json:"code,omitempty"`
	Message    string                                 `json:"msg,omitempty"`
	Data       *OfferCourseFromLearnerDescriptionData `json:"data"`
}

type OfferGenerateThumbnailData struct {
	OfferID string `json:"offer_id,omitempty"`
}

type GenerateThumbnailResponse struct {
	StatusCode int32                  `json:"code,omitempty"`
	Message    string                 `json:"msg,omitempty"`
	Data       *GenerateThumbnailData `json:"data"`
}

type GenerateThumbnailData struct {
	Metadata *ThumbnailMetadata `json:"metadata"`
	Status   GenerateStatus     `json:"status"`
}

type ThumbnailMetadata struct {
	CourseTitle       string   `json:"course_title,omitempty"`
	CourseDescription string   `json:"course_description,omitempty"`
	ThumbnailPrompt   string   `json:"course_thumbnail_prompt,omitempty"`
	CourseThumbnails  []string `json:"course_thumbnails"`
	Cost              float64  `json:"cost"`
}

type OfferGenerateOutlineRequest struct {
	CourseTitle       string `json:"course_title"`
	CourseDescription string `json:"course_description"`
	OfferID           string `json:"offer_id"`
	XAPIKey           string `json:"-"`
}

type OfferGenerateOutlineResponse struct {
	StatusCode int32                     `json:"code,omitempty"`
	Message    string                    `json:"msg,omitempty"`
	Data       *OfferGenerateOutlineData `json:"data"`
}

type OfferGenerateOutlineData struct {
	OfferID string `json:"offer_id,omitempty"`
}

type GenerateOutlineResponse struct {
	StatusCode int32                `json:"code,omitempty"`
	Message    string               `json:"msg,omitempty"`
	Data       *GenerateOutlineData `json:"data"`
}

type GenerateOutlineData struct {
	Metadata *OutlineMetadata `json:"metadata"`
	Status   GenerateStatus   `json:"status"`
}

type OutlineMetadata struct {
	CourseTitle       string         `json:"course_title,omitempty"`
	CourseDescription string         `json:"course_description,omitempty"`
	ThumbnailPrompt   string         `json:"course_thumbnail_prompt,omitempty"`
	CourseThumbnail   []string       `json:"course_thumbnails"`
	Cost              float64        `json:"cost"`
	Sections          []*SectionInfo `json:"sections"`
}

type OfferBlogFromLinkRequest struct {
	Link     string `json:"link"`
	Language string `json:"language"`
	XAPIKey  string `json:"-"`
	Tone     Tone   `json:"tone"`
}

type OfferBlogFromLinkResponse struct {
	StatusCode int32                  `json:"code,omitempty"`
	Message    string                 `json:"msg,omitempty"`
	Data       *OfferBlogFromLinkData `json:"data"`
}

type OfferBlogFromLinkData struct {
	OfferID string `json:"offer_id,omitempty"`
}

type GenerateBlogFromLinkResponse struct {
	StatusCode int32                     `json:"code,omitempty"`
	Message    string                    `json:"msg,omitempty"`
	Data       *GenerateBlogFromLinkData `json:"data"`
}

type AIResponse interface {
	GetData() AIData
}

type AIData interface {
	GetMetadata() AIMetadata
	GetStatus() GenerateStatus
}

type AIMetadata interface {
	GetCost() float64
}

func (r *GenerateBlogFromLinkResponse) GetData() AIData {
	return r.Data
}

func (r *GenerateBlogFromLinkData) GetMetadata() AIMetadata {
	return r.Metadata
}

func (r *GenerateBlogFromLinkData) GetStatus() GenerateStatus {
	return r.Status
}

func (r *BlogFromLinkMetadata) GetCost() float64 {
	return r.Cost
}

type GenerateBlogFromLinkData struct {
	Metadata *BlogFromLinkMetadata `json:"metadata"`
	Status   GenerateStatus        `json:"status"`
}

type BlogFromLinkMetadata struct {
	Title     string  `json:"title"`
	Content   string  `json:"content"`
	Cost      float64 `json:"cost"`
	MetaData  string  `json:"metadata"`
	Thumbnail string  `json:"thumbnail"`
}

type OfferBlogFromTextRequest struct {
	Text    string `json:"text"`
	XAPIKey string `json:"-"`
}

type OfferBlogFromTextResponse struct {
	StatusCode int32                  `json:"code,omitempty"`
	Message    string                 `json:"msg,omitempty"`
	Data       *OfferBlogFromTextData `json:"data"`
}

type OfferBlogFromTextData struct {
	OfferID string `json:"offer_id,omitempty"`
}

type RewriteParagraphResponse struct {
	StatusCode int32                 `json:"code,omitempty"`
	Message    string                `json:"msg,omitempty"`
	Data       *RewriteParagraphData `json:"data"`
}

func (r *RewriteParagraphResponse) GetData() AIData {
	return r.Data
}

func (r *RewriteParagraphData) GetMetadata() AIMetadata {
	return r.Metadata
}

func (r *RewriteParagraphData) GetStatus() GenerateStatus {
	return r.Status
}

func (r *RewriteParagraphMetadata) GetCost() float64 {
	return r.Cost
}

type RewriteParagraphData struct {
	Metadata *RewriteParagraphMetadata `json:"metadata"`
	Status   GenerateStatus            `json:"status"`
}

type RewriteParagraphMetadata struct {
	Content string  `json:"content"`
	Cost    float64 `json:"cost"`
}
