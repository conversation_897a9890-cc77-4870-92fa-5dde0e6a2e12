package ai

import (
	httpclient "openedu-core/pkg/http_client"
	"openedu-core/pkg/setting"
)

const (
	rewriteFromLink                      = "/blog/generation"
	rewriteFromText                      = "/paragraph/generation"
	courseGenerateFromYoutube            = "/course/playlist"
	generateStatusTask                   = "/task"
	quizGenerate                         = "/quiz/generation"
	courseGenerateFromLearnerDescription = "/course/basic"
)

type BlogAIService struct {
	httpClient *httpclient.Client
}
type CourseAIService struct {
	httpClient *httpclient.Client
}

type BlogAIServiceIface interface {
	OfferGenerateBlogFromLink(params *OfferBlogFromLinkRequest) (*OfferBlogFromLinkResponse, error)
	GetBlogGenerateFromLink(offerID string, xAPIKey string) (*GenerateBlogFromLinkResponse, error)
	OfferRewriteParagraph(params *OfferBlogFromTextRequest) (*OfferBlogFromTextResponse, error)
	GetRewriteParagraphGenerate(offerID string, xAPIKey string) (*RewriteParagraphResponse, error)
}

type CourseAIServiceIface interface {
	OfferGenerateCourseFromYoutube(params *OfferCourseFromYoutubeRequest) (*OfferCourseFromYoutubeResponse, error)
	GetCourseGenerateFromYoutube(offerID string, xAPIKey string) (*GenerateCourseFromYoutubeResponse, error)
	OfferGenerateQuiz(params *OfferGenerateQuizRequest) (*OfferQuizResponse, error)
	GetQuizGenerate(offerID string, xAPIKey string) (*GenerateQuizResponse, error)
	OfferGenerateCourseFromLearnerDescription(params *OfferCourseFromLearnerDescriptionRequest) (*OfferCourseFromLearnerDescriptionResponse, error)
	GetCourseGenerateFromLearnerDescription(offerID string, xAPIKey string) (*GenerateCourseFromLearnerDescriptionResponse, error)
	OfferGenerateThumbnail(params *OfferGenerateThumbnailRequest) (*OfferGenerateThumbnailResponse, error)
	GetCourseThumbnail(offerID string, xAPIKey string) (*GenerateThumbnailResponse, error)
	OfferGenerateOutline(params *OfferGenerateOutlineRequest) (*OfferGenerateOutlineResponse, error)
	GetCourseOutline(offerID string, xAPIKey string) (*GenerateOutlineResponse, error)
}

func NewBlogAIService(httpClient *httpclient.Client) BlogAIServiceIface {
	return &BlogAIService{
		httpClient: httpClient,
	}
}

func NewCourseAIService(httpClient *httpclient.Client) CourseAIServiceIface {
	return &CourseAIService{
		httpClient: httpClient,
	}
}

var Blog BlogAIServiceIface
var Course CourseAIServiceIface

func Setup() {
	httpClient := httpclient.NewClient(setting.ExternalServiceSetting.AIDomain, nil)

	Blog = NewBlogAIService(httpClient)
	Course = NewCourseAIService(httpClient)
}
