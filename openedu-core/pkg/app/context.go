package app

import (
	"context"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

type AppDomain struct {
	Path      string // {subdomain}.openedu101.com/{uri}
	Domain    string // openedu101.com
	Subdomain string // subdomain
	Uri       string // uri
}

func (g *Gin) GetDomain() *AppDomain {
	var domain *AppDomain
	if v, ok := g.C.Get(util.ContextDomainInfo); ok {
		if d, ok := v.(*AppDomain); ok {
			domain = d
		}
	}
	return domain
}

func (g *Gin) GetLoggedUser() *models.User {
	var loggedUser *models.User
	if v, ok := g.C.Get(util.ContextUserKey); ok {
		if user, ok := v.(*models.User); ok {
			loggedUser = user
		}
	}

	return loggedUser
}

func (g *Gin) GetUA() *string {
	return util.NewString(g.C.GetHeader("User-Agent"))
}

func (g *Gin) GetOrg() *models.Organization {
	var org *models.Organization
	if v, ok := g.C.Get(util.ContextOrgKey); ok {
		if c, ok := v.(*models.Organization); ok {
			org = c
		}
	}

	if org == nil {
		if defaultOrg, _ := models.Repository.Organization.FindOne(&models.OrganizationQuery{
			Domain: util.NewString(setting.AppSetting.BaseDomain),
		}, nil); defaultOrg != nil {
			org = defaultOrg
		}
	}

	return org
}

func (g *Gin) RequiredLogin(user *models.User) bool {
	v, exists := g.C.Get(util.ContextUserKey)
	if !exists {
		g.Response401(e.UNAUTHORIZED, "Unauthorized: cannot get user from context")
		return false
	}

	if usr, ok := v.(*models.User); ok {
		*user = *usr
		return true
	}

	return false
}

func (g *Gin) RequiredOrg(org *models.Organization) bool {
	v, exists := g.C.Get(util.ContextOrgKey)
	if !exists {
		g.Response400(e.INVALID_PARAMS, "Invalid params: cannot get org from context")
		return false
	}

	if organization, ok := v.(*models.Organization); ok {
		*org = *organization
		return true
	}

	return false
}

func (g *Gin) GetCtx() context.Context {
	return context.WithoutCancel(g.C.Request.Context())
}
