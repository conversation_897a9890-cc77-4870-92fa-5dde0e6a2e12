package app

import (
	"context"
	"openedu-core/models"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

func GetOrganization(ctx context.Context) *models.Organization {
	var org *models.Organization
	org = ctx.Value(util.ContextOrgKey).(*models.Organization)

	if org == nil {
		if defaultOrg, _ := models.Repository.Organization.FindOne(&models.OrganizationQuery{
			Domain: util.NewString(setting.AppSetting.BaseDomain),
		}, nil); defaultOrg != nil {
			org = defaultOrg
		}
	}
	return org
}

func GetLoggedUser(ctx context.Context) *models.User {
	if user := ctx.Value(util.ContextUserKey); user != nil {
		return user.(*models.User)
	}
	return nil
}
