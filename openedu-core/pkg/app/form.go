package app

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
)

// BindAndValid binds and validates data
func BindAndValid(c *gin.Context, form interface{}) (int, int) {
	err := c.Bind(form)
	if err != nil {
		return http.StatusBadRequest, e.INVALID_PARAMS
	}

	validErr := util.Validator.Struct(form)
	if validErr != nil {
		MarkErrors(&validErr)
		return http.StatusInternalServerError, e.ERROR
	}

	return http.StatusOK, e.SUCCESS
}
