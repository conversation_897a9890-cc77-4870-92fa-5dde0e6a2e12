package app

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin/binding"
	"openedu-core/models"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"

	"github.com/go-playground/validator/v10"
)

// MarkErrors logs error logs
func MarkErrors(err *error) {
	log.Info(err)
	return
}

func (g *Gin) BindFindPageOptions(options *models.FindPageOptions) error {
	copyContext := g.C.Copy()
	if err := copyContext.ShouldBindQuery(&options); err != nil {
		return err
	}

	if options.Page <= 0 {
		options.Page = 1
	}

	if options.PerPage <= 0 {
		options.PerPage = setting.AppSetting.DefaultPerPage
	}

	return nil
}

func (g *Gin) BindAndValidateJSON(obj any) error {
	if err := g.C.ShouldBindBodyWith(obj, binding.JSON); err != nil {
		return err
	}

	bodyBytes, _ := json.MarshalIndent(obj, "", "")
	g.C.Set(util.ContextBodyDataCopy, bodyBytes)

	if vErr := util.Validator.Struct(obj); vErr != nil {
		var errs validator.ValidationErrors
		if ok := errors.As(vErr, &errs); !ok {
			return vErr
		}

		errMsgs := make([]string, len(errs))
		for idx, err := range errs {
			field := err.Field()
			tag := err.Tag()
			errMsg := fmt.Sprintf("%s invalid on tag: %s", field, tag)
			switch tag {
			case util.RequiredTag:
				errMsg = fmt.Sprintf("%s required", field)

			case util.EmailTag:
				errMsg = fmt.Sprintf("%s must be valid email", field)

			case util.URLTag:
				errMsg = fmt.Sprintf("%s must be valid url", field)

			case util.MinTag:
				errMsg = fmt.Sprintf("minimum length of %s must be %v", field, err.Param())

			case util.MaxTag:
				errMsg = fmt.Sprintf("maximum length of %s must be %v", field, err.Param())
			}
			errMsgs[idx] = errMsg
		}

		return fmt.Errorf("Validation error: " + strings.Join(errMsgs, ", "))
	}

	return nil
}

func (g *Gin) GetAllowFieldsData() map[string]interface{} {
	if value, exist := g.C.Get(util.ContextAllowFields); exist {
		if allowFields, ok := value.(map[string]interface{}); ok {
			return allowFields
		}
	}
	return nil
}
