package cache

import (
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"
)

type GoCache struct {
	client *cache.Cache
}

func newGoCache() *GoCache {
	goCacheClient := cache.New(5*time.Minute, 10*time.Minute)
	return &GoCache{
		client: goCacheClient,
	}
}

func (c *GoCache) Set(key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	c.client.Set(key, data, ttl)
	return nil
}

func (c *GoCache) Get(key string, value interface{}) error {
	data, found := c.client.Get(key)
	if !found {
		return errors.New("key not found")
	}
	return json.Unmarshal(data.([]byte), value)
}

func (c *GoCache) Delete(key string) error {
	// Get the value in the byte format it is stored in
	c.client.Delete(key)
	return nil
}

func (c *GoCache) DeleteByPrefix(prefix string) error {
	for key := range c.client.Items() {
		if strings.HasPrefix(key, prefix) {
			if err := c.Delete(key); err != nil {
				return err
			}
		}
	}
	return nil
}

func (c *GoCache) Flush() error {
	c.client.Flush()
	return nil
}
