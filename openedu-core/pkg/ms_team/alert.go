package ms_team

import (
	"fmt"
	goteamsnotify "github.com/atc0005/go-teams-notify/v2"
	"github.com/atc0005/go-teams-notify/v2/adaptivecard"
	"openedu-core/pkg/setting"
	"strings"
)

const (
	PlainText = ""
	JSON      = "json"
)

type AlertConfig struct {
	WebhookURL        string
	Mentions          []string
	CodeBlockSnippet  string
	CodeBlockLanguage string
}

type AlertOption func(cfg *AlertConfig)

func WithWebhookURL(webhookURL string) AlertOption {
	return func(cfg *AlertConfig) {
		cfg.WebhookURL = webhookURL
	}
}

func WithMentions(mentions []string) AlertOption {
	return func(cfg *AlertConfig) {
		cfg.Mentions = mentions
	}
}

func WithCodeBlock(snippet, language string) AlertOption {
	return func(cfg *AlertConfig) {
		cfg.CodeBlockSnippet = snippet
		cfg.CodeBlockLanguage = language
	}
}

func buildAlertConfig(opts ...AlertOption) *AlertConfig {
	cfg := AlertConfig{
		WebhookURL: setting.AppSetting.MsTeamWebHookURL,
		Mentions:   setting.AppSetting.MsTeamMentionedEmails,
	}

	for _, opt := range opts {
		opt(&cfg)
	}
	return &cfg
}

func Alert(title, body string, opts ...AlertOption) error {
	if !setting.AppSetting.EnableAPIFailureAlerts {
		return nil
	}

	cfg := buildAlertConfig(opts...)

	card, err := newAdaptiveCard(title, body, cfg)
	if err != nil {
		return fmt.Errorf("new adaptive card error: %w", err)
	}

	msg, err := adaptivecard.NewMessageFromCard(card)
	if err != nil {
		return fmt.Errorf("new adaptive message error: %w", err)
	}

	mstClient := goteamsnotify.NewTeamsClient()
	if err = mstClient.Send(cfg.WebhookURL, msg); err != nil {
		return fmt.Errorf("send message error: %w", err)
	}

	return nil
}

func newAdaptiveCard(title, body string, cfg *AlertConfig) (adaptivecard.Card, error) {
	card, err := adaptivecard.NewTextBlockCard(body, title, true)
	if err != nil {
		return adaptivecard.Card{}, err
	}

	card.SetFullWidth()
	card.MSTeams.Width = adaptivecard.MSTeamsWidthFull
	card.MSTeams.AllowExpand = true

	if cfg.CodeBlockSnippet != "" {
		codeSnippet := strings.TrimSpace(cfg.CodeBlockSnippet)
		codeLang := strings.TrimSpace(cfg.CodeBlockLanguage)
		formattedCodeSnippet := "```" + codeLang + "\n" + codeSnippet + "\n```"
		codeBlock := adaptivecard.NewTextBlock(formattedCodeSnippet, true)
		if err := card.AddElement(false, codeBlock); err != nil {
			return adaptivecard.Card{}, err
		}
	}

	if len(cfg.Mentions) == 0 {
		return card, nil
	}

	var userMentions []adaptivecard.Mention
	for _, email := range cfg.Mentions {
		userMention, err := adaptivecard.NewMention(email, email)
		if err != nil {
			return adaptivecard.Card{}, err
		}
		userMentions = append(userMentions, userMention)
	}

	if err = card.AddMention(true, userMentions...); err != nil {
		return adaptivecard.Card{}, err
	}

	return card, nil
}
