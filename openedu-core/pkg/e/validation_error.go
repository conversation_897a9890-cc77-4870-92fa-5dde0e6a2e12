package e

import (
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/go-playground/validator/v10"
)

type CustomValidateError struct {
	Field string `json:"field"`
	Msg   string `json:"msg"`
}

var validationErrorMap = map[string]string{
	"required":              "This field is required",
	"email":                 "Invalid email",
	"discountCoupon":        "Mismatch discount amount type",
	"flatCoupon":            "Invalid flat coupon",
	"orgExists":             "Organization does not exist",
	"courseExists":          "Course does not exist",
	"courseListExists":      "Course does not exist",
	"orgListExists":         "Organization does not exist",
	"checkEntityType":       "Invalid entity type",
	"checkApproveType":      "Invalid approve type",
	"checkApproveStatus":    "Invalid approve status",
	"createBlogStatus":      "Invalid blog status",
	"categoriesExists":      "categories does not exists",
	"hashtagExists":         "hashtag does not exists",
	"userExists":            "user does not exists",
	"validateBlogAIRequest": "Invalid AI blog request",
	"validateReportType":    "Invalid report type",
	"validateLPEventLesson": "Invalid event with lesson_content_uid",
}

func MsgForTag(tag string) string {
	msg, ok := validationErrorMap[tag]
	if !ok {
		return "Error not handle yet"
	}
	return msg
}

func HandleValidationError(data interface{}) []CustomValidateError {
	err := util.Validator.Struct(data)
	if err != nil {
		ve := err.(validator.ValidationErrors)
		log.Infof("Validation error: %v\n", ve)
		log.Info("ve len: ", len(ve))
		out := make([]CustomValidateError, len(ve))
		for i, fe := range ve {
			out[i] = CustomValidateError{fe.Field(), MsgForTag(fe.Tag())}
		}
		return out

	}
	return nil
}
