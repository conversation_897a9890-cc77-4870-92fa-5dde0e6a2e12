package e

var MsgFlags = map[int]string{
	// HTTP Status
	SUCCESS:        "Ok",
	CREATED:        "Ok",
	ERROR:          "Internal server errors",
	INVALID_PARAMS: "Invalid parameters",
	UNAUTHORIZED:   "Unauthorized",
	FORBIDDEN:      "FORBIDDEN",
	NOTFOUND:       "Not found",
	// System error
	Parse_time_error:                             "Parsed layout time error",
	Json_encode_error:                            "Json encoding error",
	Generate_order_code_error:                    "Generate order code error",
	Generate_random_failed:                       "Generate random error",
	User_already_purchased:                       "User already purchased",
	User_already_purchased_combo:                 "User already purchased combo",
	User_already_writer:                          "User already writer",
	Payment_service_not_handle:                   "Payment service not handle",
	Upgrade_connection_ws_error:                  "Upgrade connection ws error",
	Read_msg_ws_error:                            "Read msg from websocket error",
	Cache_delete_all_failed:                      "Internal server error while deleting all caches",
	Cache_delete_by_key_failed:                   "Internal server error while deleting cache by key",
	C<PERSON>_set_failed:                             "Internal server error while setting cache",
	<PERSON><PERSON>_unmarshal_failed:                       "Internal server error while unmarshalling cache",
	Write_msg_ws_error:                           "Write websocket error",
	System_admin_required:                        "System admin required",
	Error_extract_string_slice_from_jsonb_failed: "Extract string slice from jsonb error",
	Commit_transaction_failed:                    "Commit transaction error",

	// Wallet
	WalletInitFailed:                      "Internal server error occurred while initializing the user wallets",
	WalletGetByUserFailed:                 "Internal server error occurred while getting the user wallet list",
	WalletUpdateBalanceFailed:             "Internal server error occurred while updating the wallet balance",
	WalletFindFailed:                      "Internal server error occurred while finding the wallet",
	WalletOwnerRequired:                   "Wallet owner required",
	WalletNotFound:                        "Wallet not found",
	WalletNotAllowWithdraw:                "Wallet is not allowed to withdraw",
	WalletBalanceNeedARemaining:           "Wallet need a minimum remaining",
	WalletParseAmountFailed:               "Internal server error occurred while parsing the amount",
	WalletWithdrawFailed:                  "Internal server error occurred while withdrawing money from the wallet",
	WalletBalanceNotEnough:                "Wallet balance not enough for withdrawal",
	WalletInsufficientGasFee:              "Wallet gas fee not enough to do this action",
	WalletAvailableNotEnough:              "Wallet available balance not enough for withdrawal",
	WalletNotAllowClaimEarnings:           "Wallet is not allowed to claim earning",
	WalletUnknownCurrency:                 "Unknown currency",
	WalletClaimEarningsFailed:             "Internal server error occurred while claiming earnings into the wallet",
	WalletGetEarningsFailed:               "Internal server error occurred while getting the wallet earnings",
	WalletInsufficientEarningToClaim:      "Wallet earning not enough for claiming",
	WalletRetroactiveFailed:               "Internal server error while processing the retroactive",
	WalletInsufficientGasFeeToRetroactive: "Wallet gas fee not enough for retroactive",
	WalletGetAccountInfoFailed:            "Internal server error while get the crypto account info",
	WalletGetSponsorGasFailed:             "Internal server error while getting sponsor gas",
	WalletDepositSponsorGasFailed:         "Internal server error while depositing sponsor gas",
	WalletWithdrawInvalidAddress:          "Invalid wallet address to withdraw",

	// Transaction
	TransactionCreateFailed: "Create transaction failed",
	TransactionFindFailed:   "Find transaction failed",

	Error_file_upload_fail:                "Upload file failed",
	Error_file_delete_fail:                "DeleteMany file failed",
	Error_upload_invalid_content_type:     "Invalid content-type, only accept form-data",
	Error_upload_files_empty:              "Not allow empty files",
	Error_file_not_found:                  "File not exist",
	Error_create_file_failed:              "Create file record(s) failed",
	Error_remove_file_failed:              "Remove files failed",
	Error_file_parse_struct_to_map_failed: "File error: parse struct to map failed",
	Error_file_create_many_failed:         "File error: create many failed",
	Error_file_find_many_failed:           "File error: find many failed",
	Error_get_file_info_failed:            "File error: bunny get info failed",
	Error_file_find_one_failed:            "File error: find one file failed",
	Error_file_relation_find_many_failed:  "File relation find many failed",
	Error_file_update_failed:              "File error update failed",
	Error_bunny_webhook_status_invalid:    "Invalid Bunny webhook status",
	Error_file_is_not_bunny_video:         "File is not bunny video",

	// Email template, email services (1100-1200)
	Email_template_create_failed:         "Create email template failed",
	Email_template_update_failed:         "Update email template failed",
	Email_template_find_one_failed:       "Find email template failed",
	Email_template_not_found:             "Email template not found",
	Email_template_find_page_failed:      "Find page failed",
	Email_template_delete_failed:         "Delete email failed",
	Email_template_get_variables_failed:  "Email template get variables failed",
	Email_template_preview_failed:        "Preview email template failed",
	Email_check_disposable_domain_failed: "Check disposable email failed",
	Email_disposable_domain_not_allowed:  "Disposable email domain is not allowed",

	// User 1500 - 2000
	User_setting_create_failed: "Create user setting failed",
	User_setting_find_failed:   "Find user setting failed",
	User_setting_update_failed: "Update user setting failed",
	User_setting_not_found:     "User setting not found",

	Auth_user_blocked:                            "User blocked",
	Auth_user_inactive:                           "User inactive",
	Error_auth_missing_headers_domain:            "Request missing header Origin",
	Error_auth_invalid_org:                       "Invalid organization",
	Error_auth_invalid_user:                      "Invalid user, user not found",
	Error_auth_invalid_user_role:                 "Invalid user role, userRoleOrg not found",
	Error_auth_domain_miss_match:                 "Request referer and token miss match",
	Error_auth_invalid_permission:                "Invalid permission: find permission failed",
	Error_auth_permission_not_found:              "Invalid permission: method not found",
	Error_auth_invalid_api_key:                   "Invalid API key",
	Error_auth_provider_already_exist_in_user:    "Provider already used",
	Error_auth_create_user_failed:                "Create user failed",
	Error_auth_add_user_role_failed:              "Update UserRoleOrg failed",
	Error_auth_verify_token_fail:                 "An internal error occurred when verifying token",
	Error_auth_expired_token:                     "Token is expired",
	Error_auth_token:                             "An internal error occurred when generating token",
	Error_auth_invalid_username:                  "User not exits",
	Error_auth_invalid_password:                  "Password is wrong",
	Error_auth_email_already_used:                "Email is already used",
	Error_auth_verify_username_email_failed:      "An internal error occurred when verifying username and email available",
	Error_auth_get_default_role_failed:           "An internal error occurred when getting default role for register",
	Error_auth_check_and_create_user_failed:      "An internal error occurred when check user belong or create new user",
	Error_auth_refresh_token_required:            "refresh_token required",
	Error_auth_session_not_found:                 "refresh_token not found, login again",
	Error_auth_invalid_session_domain_miss_match: "Invalid token, Origin URL miss match",
	Error_auth_session_expire:                    "refresh_token expired",
	Error_auth_session_user_not_found:            "Invalid token, user not found",
	Error_auth_session_make_token_failed:         "Make token failed",
	Error_auth_email_existed_in_another_user:     "This email address is already been used by another user",
	Error_auth_invalid_credential:                "Username or password is wrong",
	Error_auth_verify_credential_fail:            "An internal error occurred when verifying credential",
	Error_auth_verify_sns_token_failed:           "Verify Sns token failed",
	Error_auth_update_user_role_failed:           "Permission error",
	Error_auth_otp_not_verified:                  "OTP not verified",
	Error_auth_otp_incorrect:                     "Invalid OTP",
	Error_auth_otp_expired:                       "OTP has expired",
	Error_auth_verify_normalized_email_failed:    "An internal error occurred when verifying normalized email",
	Error_auth_existing_normalized_email:         "Normalized email already exists",

	Error_user_not_found:                   "User not found",
	Error_user_find_failed:                 "Finding user failed",
	Error_user_update_failed:               "Update user failed",
	User_already_creator:                   "User already creator",
	Error_find_role_failed:                 "Find role failed",
	Error_remove_multiple_user_role_failed: "Remove multiple user role failed",
	Error_auth_remove_user_role_failed:     "Remove user role failed",
	Error_username_is_existed:              "Username is existed",
	Error_user_count_failed:                "Count user failed",
	Check_old_username_failed:              "Check username from user and user setting failed",

	Error_update_sns_account_by_id_failed:  "Update sns account failed",
	Error_upsert_sns_account_failed:        "Upset sns account failed",
	Error_parse_sns_account_profile_failed: "Parse sns account failed",
	Error_get_linked_sns_accounts_failed:   "Get linked sns failed",
	Error_sns_accounts_find_failed:         "Find sns account failed",
	Error_sns_account_delete_failed:        "DeleteMany sns account failed",
	Find_user_role_failed:                  "Find user by role failed",
	Create_session_failed:                  "Create session failed",
	Error_upsert_session_failed:            "Upsert session failed",

	Error_ses_send_mail_failed:        "Ses send mail failed",
	Error_create_user_token_failed:    "Create user token failed",
	Error_create_email_failed:         "Create new email failed",
	Error_user_token_find_failed:      "Find user token failed",
	Error_token_not_ready:             "Email can only send after 1 minute",
	Error_user_token_not_found:        "Token not found",
	Error_user_token_expired:          "Token is expired",
	Error_update_user_token_failed:    "Update user token failed",
	Error_find_user_with_token_failed: "Find user with user token failed",
	Error_user_token_find_many_failed: "Find many userTokens failed",
	Error_user_token_verified:         "Token already verified",
	Error_find_page_user_token_failed: "Find page user token failed",
	Error_delete_user_token_failed:    "DeleteMany user token failed",
	Error_count_user_token_failed:     "Count user token failed",
	Error_token_already_used:          "Token already used",
	Error_upsert_user_token_failed:    "Upsert user token failed",

	Error_find_session_failed:       "Find session failed",
	Error_find_user_by_email_failed: "Find user by email failed",

	// User agency// referral
	Affiliate_campaign_create_failed:         "Create affiliate campaign failed",
	Affiliate_campaign_find_failed:           "Find affiliate campaign failed",
	Affiliate_campaign_find_not_found:        "Affiliate campaign not found",
	Affiliate_campaign_update_failed:         "Update affiliate campaign failed",
	Affiliate_campaign_delete_failed:         "Delete affiliate campaign failed",
	Affiliate_campaign_add_course_failed:     "Affiliate campaign add course failed",
	Affiliate_campaign_find_course_failed:    "Affiliate campaign find course failed",
	Affiliate_campaign_remove_courses_failed: "Affiliate campaign remove course failed",
	Referrer_create_failed:                   "Create referrer failed",
	Referrer_remove_failed:                   "Remove referrer failed",
	Referrer_find_failed:                     "Find referrer failed",
	Referrer_not_found:                       "Referrer not found",

	Commission_create_failed:       "Create commission failed",
	Commission_update_failed:       "Update commission failed",
	Commission_delete_failed:       "Delete commission failed",
	Commission_find_failed:         "Find commission failed",
	Commission_not_found:           "Commission not found",
	Commission_bonus_create_failed: "Creat commission bonus failed",

	Referral_link_create_failed:   "Create referral link failed",
	Referral_link_owner_required:  "Referral link owner required",
	Referral_link_update_failed:   "Update referral link failed",
	Referral_link_find_failed:     "Find referral link failed",
	Referral_link_disabled:        "Referral link disabled",
	Campaign_not_commission_match: "Campaign don't have commission match for user",
	Campaign_not_include_course:   "The campaign does not include this course",

	Referral_create_failed:                   "Create referral failed",
	Referral_find_page_failed:                "FindPage referral failed",
	Referral_link_require_purchase_to_extend: "Require purchase item before extend",
	Referral_link_not_found:                  "Referral link not found",
	Referral_count_failed:                    "Count referral failed",
	Referral_report_failed:                   "Referral report failed",
	Affiliate_campaign_disabled:              "Affiliate campaign disabled",
	Affiliate_campaign_not_active_time:       "Affiliate campaign don't available this time",
	Commission_disabled:                      "Commission disabled",
	GetApplicableCommissionFailed:            "Get applicable commission failed",

	// Organization
	Organization_domain_required:                   "domain already existed",
	Organization_find_one_failed:                   "find one failed",
	Organization_create_failed:                     "create failed",
	Organization_find_page_failed:                  "find page failed",
	Organization_update_failed:                     "update failed",
	Organization_delete_failed:                     "delete failed",
	Organization_not_found:                         "org not found",
	Organization_owner_required:                    "org owner required",
	Organization_migrate_schema_failed:             "Migrate org schema failed",
	Organization_set_owner_failed:                  "Set org owner failed",
	Organization_update_allow_origin_failed:        "Update org domain to allow origins failed",
	Organization_subdomain_must_include_root:       "Subdomain must be include root",
	Organization_init_register_creator_form_failed: "Init register creator form failed",
	Organization_send_approval_email_failed:        "Internal error while sending approval email for organization",
	Organization_send_rejection_email_failed:       "Internal error while sending rejection email for organization",
	Organization_check_validation_failed:           "Internal error while check validation for organization",
	Organization_domain_invalid:                    "Domain is invalid",
	Organization_domain_already_exists:             "Domain already exists",
	Organization_init_default_course_levels_failed: "Internal error while initializing default course levels",
	Organization_find_many_failed:                  "Find many organization failed",
	Organization_init_default_models:               "Init default models failed",
	Organization_init_default_email_templates:      "Init default email templates failed",
	Organization_need_permission_to_edit:           "Need permission to edit",

	FeaturedContentFindAllFailed:       "Find all failed",
	FeaturedContentDeleteManyFailed:    "Delete all failed",
	FeaturedContentCreateManyFailed:    "Create all failed",
	FeaturedContentCreateFailed:        "Create failed",
	FeaturedContentFindPageFailed:      "Find page failed",
	FeaturedContentFindByTypeFailed:    "Find type failed",
	FeaturedContentUnSupportEntityType: "Unsupport entity type",
	FeaturedContentLoadEntityFailed:    "Load entity failed",

	// Course
	Create_course_failed:                        "Create course failed",
	Update_course_failed:                        "Update course failed",
	Update_course_partner_failed:                "Update course partner failed",
	Course_not_found:                            "Course not found",
	Course_find_one_failed:                      "Find one course failed",
	Course_find_page_failed:                     "Find page course failed",
	Course_delete_failed:                        "DeleteMany course failed",
	Course_update_need_permission_or_owner:      "Update course need permission",
	Course_delete_need_permission_or_owner:      "Update course need permission",
	Course_register_form_required:               "Course requires register form",
	Section_create_failed:                       "Create section failed",
	Section_update_failed:                       "Update section failed",
	Section_not_found:                           "Section not found",
	Section_find_one_failed:                     "Find one section failed",
	Section_find_many_failed:                    "Find many section failed",
	Section_find_page_failed:                    "Find page section failed",
	Section_delete_failed:                       "DeleteMany section failed",
	Section_count_failed:                        "Count section failed",
	Lesson_create_failed:                        "Create lesson failed",
	Lesson_update_failed:                        "Update lesson failed",
	Lesson_upsert_failed:                        "Upsert lesson failed",
	Lesson_not_found:                            "Lesson not found",
	Lesson_find_one_failed:                      "Find one lesson failed",
	Lesson_find_page_failed:                     "Fine page lesson failed",
	Lesson_delete_failed:                        "DeleteMany lesson failed",
	Course_update_stats_failed:                  "Course create stats failed",
	Lesson_update_stats_failed:                  "Course update stats failed",
	Create_course_enrollment_failed:             "Create course enrollment failed",
	Update_course_enrollment_failed:             "Update course enrollment failed",
	Find_course_enrollment_failed:               "Find one course enrollment failed",
	Find_many_course_enrollment_failed:          "Find many course enrollment failed",
	Find_page_course_enrollment_failed:          "Find page course enrollment failed",
	Course_enrollment_not_found:                 "Course enrollment not found",
	Course_enrollment_already_exist:             "Course enrollment already exist",
	You_have_been_blocked_from_this_course:      "You have been blocked from this course",
	Course_slug_already_exists:                  "Course slug is already exists. Please try another slug.",
	Course_check_slug_failed:                    "Internal server error while checking slug",
	Course_status_reviewing:                     "Course is reviewing",
	Course_is_not_publishing:                    "Course is not publishing",
	Publish_course_delete_failed:                "Publish course delete failed",
	Publish_course_update_failed:                "Publish course update failed",
	Publish_course_find_failed:                  "Publish course find failed",
	Course_is_not_reviewing:                     "Course is not reviewing",
	Remove_course_partner_failed:                "remove partner failed",
	Course_assign_user_status_failed:            "Internal server error while assigning user status to course",
	Error_course_find_many_failed:               "Find many course failed",
	Error_course_count_failed:                   "Count number of courses failed",
	Course_revenue_summary_failed:               "Course revenue summary failed",
	Course_revenue_detail_failed:                "Course revenue detail failed",
	Course_revenue_create_failed:                "Course revenue create failed",
	Course_view_report_need_permission_or_owner: "Course view report need permission or owner",
	Course_revenue_graph_failed:                 "Course revenue graph failed",
	Course_props_find_failed:                    "Course props find failed",

	// Lesson Content
	LessonContentCountFailed: "Count Lesson Content failed",
	Not_course_owner:         "You are not course owner",

	// Payment
	Create_payment_failed:    "Create payment failed",
	Update_payment_failed:    "Update payment failed",
	Find_payment_failed:      "Find one payment failed",
	Find_many_payment_failed: "Find many payment failed",
	Find_page_payment_failed: "Find page payment failed",
	Payment_not_found:        "Payment not found",
	Payment_find_one_failed:  "Payment find one failed",
	Payment_owner_required:   "payment owner required",
	// payment method
	Create_payment_method_failed:    "Create payment method failed",
	Update_payment_method_failed:    "Update payment method failed",
	Find_payment_method_failed:      "Find one payment method failed",
	Find_many_payment_method_failed: "Find many payment method failed",
	Find_page_payment_method_failed: "Find page payment method failed",
	Payment_method_not_found:        "Payment method not found",
	Payment_method_find_one_failed:  "Payment method find one failed",
	Payment_method_owner_required:   "payment method owner required",

	// Order
	OrderCreateFailed:                  "An internal error occurred when creating a new order",
	OrderUpdateFailed:                  "An internal error occurred when updating the order",
	OrderFindManyFailed:                "An internal error occurred when finding many orders",
	OrderFindPageFailed:                "An internal error occurred when finding page orders",
	OrderNotFound:                      "Order not found",
	OrderFindOneFailed:                 "An internal error occurred when finding one order",
	OrderOwnerRequired:                 "Order owner required",
	OrderFindOrderItemsFailed:          "Order items not found on the order",
	OrderFindOrderItemWithCourseFailed: "An internal error occurred when finding order items of the order with course",
	OrderFindCourseByOrderIDFailed:     "An internal error occurred when finding the course by order ID",
	OrderDeleteFailed:                  "An internal error occurred when deleting the order",
	OrderPaymentWithWalletFailed:       "An internal error occurred when paying for the order with the selected wallet",
	OrderMismatchCurrencies:            "Currencies are mismatch",

	// Order item
	OrderItemCreateFailed:   "An internal error occurred when creating a new order item",
	OrderItemUpdateFailed:   "An internal error occurred when updating the order item",
	OrderItemFindPageFailed: "An internal error occurred when finding the page order items",
	OrderAlreadySuccess:     "Order is already success",
	OrderItemNotFound:       "Order item not found",
	OrderItemFindOneFailed:  "An internal error occurred when finding the order item",
	OrderStatusNotAllow:     "Order status not allow",
	OrderNeedPayment:        "Order need payment",
	OrderAlreadyFailed:      "Order is already failed",

	// Category, Hashtag
	Category_create_or_update_failed:     "Create or update category failed",
	Category_not_found:                   "Category not found",
	Category_find_one_failed:             "Find one category failed",
	Category_find_page_failed:            "Find page category failed",
	Category_find_many_failed:            "Find many category failed",
	Category_relation_create_many_failed: "Create many category relation failed",
	Category_relation_not_found:          "Category relation not found",
	Category_relation_find_one_failed:    "Find one category relation failed",
	Category_relation_find_many_failed:   "Find many category relation failed",
	Hashtag_create_many_failed:           "Create many hashtag failed",
	Hashtag_delete_many_failed:           "DeleteMany many hashtag failed",
	Hashtag_find_page_failed:             "Find page many hashtag failed",
	Hashtag_find_many_failed:             "Find many hashtag failed",
	Hashtag_not_found:                    "Hashtag not found",
	Hashtag_find_one_failed:              "Find one hashtag failed",
	Hashtag_increase_failed:              "Increase hashtag failed",
	Hashtag_relation_create_failed:       "Create hashtag relation failed",
	Hashtag_relation_delete_many_failed:  "Delete many hashtag relation failed",
	Hashtag_relation_delete_failed:       "Delete hashtag relation failed",
	Hashtag_relation_not_found:           "Hashtag relation not found",
	Hashtag_relation_find_by_id_failed:   "Find by id hashtag relation failed",
	Hashtag_relation_find_many_failed:    "Find many hashtag relation failed",
	Category_delete_failed:               "Delete category failed",
	Category_increase_use_count_failed:   "Increase use count category failed",
	Category_relation_delete_failed:      "Delete category relation failed",
	Category_decrease_use_count_failed:   "Decrease use count category failed",
	Category_relation_delete_many_failed: "Delete many category relation failed",
	Category_relation_upsert_many_failed: "Upsert many category relation failed",

	// Coupon
	CouponCreateFailed:                     "An internal error occurred when creating the coupon",
	CouponUpdateFailed:                     "An internal error occurred when updating the coupon",
	CouponFindFailed:                       "An internal error occurred when finding the coupon",
	CouponFindManyFailed:                   "An internal error occurred when finding many coupons",
	CouponFindPageFailed:                   "An internal error occurred when finding the page coupons",
	CouponDeleteFailed:                     "An internal error occurred when deleting coupons",
	CouponNotFound:                         "Coupon not found",
	Coupon_find_one_failed:                 "Coupon find one failed",
	Coupon_owner_required:                  "Coupon owner required",
	Coupon_delete_need_permission_or_owner: "Coupon delete need permission or owner",
	Coupon_update_need_permission_or_owner: "Coupon update need permission or owner",
	Coupon_not_available_in_this_org:       "Coupon not in this org",
	Coupon_already_used:                    "coupon already used",
	Coupon_not_available:                   "Coupon not available",
	Coupon_maximum_usage:                   "Coupon maximum usage",
	Coupon_expired:                         "Coupon expired",
	Coupon_not_allow_course:                "Coupon not allow course",
	Coupon_not_allow_user:                  "Coupon not allow user",
	CouponAlreadyInUseForThisOrder:         "Coupon already in use for this order",
	CouponNotEnoughMinAmountToUse:          "Coupon min amount to use",
	CouponCodeAlreadyExists:                "Coupon code already exists",
	CouponNotAvailableYet:                  "Coupon not available yet",
	CouponFiatDiscountDisabled:             "The coupon for fiat currency discount is currently disabled",
	CouponCryptoDiscountDisabled:           "The coupon for crypto currency discount is currently disabled",

	// Coupon History
	CouponHistoryNotFound:       "Coupon history not found",
	CouponHistoryCreateFailed:   "An internal error occurred when creating the coupon history",
	CouponHistoryUpdateFailed:   "An internal error occurred when updating the coupon history",
	CouponHistoryFindFailed:     "An internal error occurred when finding the coupon history",
	CouponHistoryDeleteFailed:   "An internal error occurred when deleting the coupon history",
	CouponHistoryFindManyFailed: "An internal error occurred when finding many coupon histories",
	CouponHistoryFindPageFailed: "An internal error occurred when finding the page coupon histories",

	// Form
	Form_create_failed:                    "Create form failed",
	Form_not_found:                        "Form not found",
	Form_find_one_failed:                  "Find one course failed",
	Form_find_failed:                      "Internal error while finding forms",
	Form_invalid_event:                    "Invalid event type",
	Form_invalid_permission:               "You do not have permission to this form",
	Form_already_expired:                  "Form is already expired",
	Form_invalid_submission:               "Invalid submission! Please check your submission again",
	Form_submit_failed:                    "Internal error while submitting form",
	Form_missing_answer:                   "Missing answer for required question",
	Form_invalid_answer:                   "Invalid answer for question",
	Form_invalid_status:                   "Invalid form status",
	Form_publish_failed:                   "Internal error while publishing form",
	Form_update_failed:                    "Internal error while updating form",
	Form_invalid_question:                 "Invalid question, maybe the question not exists",
	Form_create_questions_failed:          "Internal error while creating questions",
	Form_view_need_permission:             "View form need permission",
	Form_update_need_permission:           "Update form need permission",
	Form_missing_default_questions:        "Missing or wrong order for default questions! Please check your form again",
	Form_invalid_data:                     "Invalid form data",
	Form_session_not_found:                "From session not found",
	Form_session_find_one_failed:          "Internal error while find form session",
	Form_session_find_many_failed:         "Internal error while finding form sessions",
	Form_session_update_failed:            "Internal error while updating form session",
	Form_session_approved:                 "Form session already approved",
	Form_session_rejected:                 "Form session already rejected",
	Error_remove_multiple_user_role:       "Remove multiple user role failed",
	Permission_reject_form_failed:         "Reject form need permission",
	Question_not_found:                    "Question not found",
	Question_find_one_failed:              "Internal error while finding question",
	Question_view_answers_need_permission: "You do not have permission to view answers of this question",
	Question_find_answers_failed:          "Internal error while finding answers",
	Form_create_relation_failed:           "Internal error while creating form relation",
	Form_find_relations_failed:            "Internal error while finding form relations",
	Form_find_one_relation_failed:         "Internal error while finding form relation",
	Form_update_relation_failed:           "Internal error while updating form relation",
	Form_relation_not_found:               "Form relation not found",
	Form_relation_delete_failed:           "Internal error while deleting form relation",
	Form_answer_find_failed:               "Internal error while finding answers",
	Form_sub_question_not_found:           "Sub question not found",
	Form_delete_failed:                    "Internal error while deleting form",
	Form_delete_need_permission:           "Delete form need permission",
	Form_duplicate_need_permission:        "Duplicate form need permission",

	Create_system_config_failed:        "Create system config failed",
	Update_system_config_failed:        "Update system config failed",
	Find_system_config_failed:          "Find system config failed",
	Find_many_system_config_failed:     "Find many system config failed",
	Find_page_system_config_failed:     "Find page system config failed",
	Delete_system_config_failed:        "DeleteMany system config failed",
	System_config_not_found:            "System config not found",
	System_config_find_one_failed:      "System config find one failed",
	Find_page_approval_failed:          "Find page approval failed",
	Delete_approval_failed:             "DeleteMany approval failed",
	Approval_not_found:                 "Approval not found",
	Approval_find_one_failed:           "Approval find one failed",
	Approval_owner_required:            "Approval owner required",
	Find_approval_item_by_order_failed: "Find approval item by order failed",
	Entity_not_found:                   "Entity not found",
	Entity_mismatch_type:               "Entity mismatch type",
	Find_entity_failed:                 "Find entity failed",
	Approval_cancelled:                 "approval request already cancelled",
	Approval_approved:                  "approval request already approved",

	// User Action
	Upsert_user_action_failed:      "Upsert user action failed",
	Upsert_many_user_action_failed: "Upsert many user action failed",
	Find_page_user_action_failed:   "Find page user action failed",
	Find_many_user_action_failed:   "Find many user action failed",
	User_action_not_found:          "User action not found",
	Find_one_user_action_failed:    "Find one user action failed",
	Delete_user_action_failed:      "DeleteMany user action failed",
	User_action_already_report:     "User Action already report",
	User_action_not_supported:      "User Action not support",

	// Page config
	Create_page_config_fail: "Create page config fail",
	Update_page_config_fail: "Update page config fail",
	Delete_page_config_fail: "DeleteMany page config fail",
	Page_config_not_found:   "Page congfig not found",

	// Page access
	Create_page_access_fail:      "Create page access fail",
	Update_page_access_fail:      "Update page access fail",
	Delete_page_access_fail:      "DeleteMany page access fail",
	Page_access_not_found:        "Page access not found",
	Page_access_find_one_failed:  "Page access find one fail",
	Page_access_find_all_failed:  "Page access find all fail",
	Find_page_page_access_failed: "Find page page access fail",

	// Custom Role
	Create_custom_role_failed:    "Create custom role failed",
	Update_custom_role_failed:    "Update custom role failed",
	Delete_custom_role_failed:    "DeleteMany custom role failed",
	Custom_role_find_page_failed: "Custom role find page failed",

	Quiz_create_failed:                       "Internal server error while creating the quiz",
	Quiz_not_found:                           "Quiz not found",
	Quiz_find_failed:                         "Internal server error while finding the quiz",
	Quiz_update_failed:                       "Internal server error while updating the quiz",
	Quiz_delete_failed:                       "Internal server error while deleting the quiz",
	Quiz_invalid_data:                        "Quiz has invalid data",
	Quiz_relation_create_failed:              "Internal server error while creating the quiz relation",
	Quiz_relation_invalid_data:               "Quiz relation has invalid data",
	Quiz_relation_delete_failed:              "Internal server error while delete the quiz relation",
	Quiz_update_need_permission:              "Need permission to update quiz",
	Quiz_delete_need_permission:              "Need permission to delete quiz",
	Quiz_duplicate_need_permission:           "Need permission to duplicate quiz",
	Quiz_find_by_lesson_failed:               "Internal server error while find the quiz by lesson",
	Quiz_view_need_permission:                "Need permission to view quiz",
	Quiz_submission_limit_exceeded:           "Your submission limit is exceeded",
	Quiz_create_submission_failed:            "Internal server error while create submission",
	Quiz_submission_not_found:                "Quiz submission not found",
	Quiz_submission_find_failed:              "Internal server error while find the quiz",
	Quiz_submission_find_question_failed:     "Internal server error while find the question",
	Quiz_submission_owner_required:           "Owner required",
	Quiz_submission_already_done:             "Submission is already done",
	Quiz_submission_quesion_not_found:        "Question not found",
	Quiz_submission_find_answer_failed:       "Internal server error while find the answer",
	Quiz_submission_over_deadline:            "Your submission is over deadline",
	Quiz_submission_over_question_time_limit: "Your submission is over time limit for question",
	Quiz_submission_invalid_answer:           "Invalid answer",
	Quiz_submission_submit_answer_failed:     "Internal server error while submit the answer",
	Quiz_submission_answer_already_submitted: "The answer of this question is already submitted",
	Quiz_submission_update_failed:            "Internal server error while update the submission",
	Quiz_check_submission_limit_failed:       "Internal server error while check submission limit",
	Quiz_get_ranks_failed:                    "Internal server error while get ranks",
	Quiz_submission_invalid_status:           "Invalid status for quiz submission",
	Quiz_submission_all_answer_submitted:     "No question. You have already submitted all answers",
	Quiz_question_not_found:                  "Quiz question not found",
	Quiz_relation_find_failed:                "Quiz relation find failed",
	Quiz_submission_count_total_point_failed: "Quiz submission count total point of quiz failed",

	Upsert_learning_progress_failed:    "Learning progress upsert failed",
	Find_page_learning_progress_failed: "Learning progress find page failed",
	Find_many_learning_progress_failed: "Learning progress find many failed",
	Find_learning_progress_failed:      "Learning progress find failed",
	Learning_progress_not_found:        "Learning progress not found",
	Update_learning_progress_failed:    "Learning progress update failed",

	Find_many_user_failed:          "Find many user failed",
	Count_learning_progress_failed: "Learning progress count failed",

	Bookmark_create_failed:   "Internal server error while create the bookmark",
	Bookmark_find_one_failed: "Find save by id fail",
	Bookmark_find_failed:     "Find page save fail",
	Bookmark_update_failed:   "Internal server error while updating the bookmark",
	Bookmark_delete_failed:   "Internal server error while deleting the bookmark",
	Bookmark_need_permission: "You do not have permission to do this action",

	PricingPlanCreateFailed:    "Pricing plan create failed",
	PricingPlanUpdateFailed:    "Pricing plan update failed",
	PricingPlanFindOneFailed:   "Pricing plan find one failed",
	PricingPlanFindByIDFailed:  "Pricing plan find by id failed",
	PricingPlanFindPageFailed:  "Pricing plan find page failed",
	PricingPlanFindManyFailed:  "Pricing plan find many failed",
	PricingPlanDeleteFailed:    "Pricing plan delete failed",
	PricingPlanSameTierEnabled: "Pricing plan samitte enabled failed",

	SubscriptionCreateFailed:   "Subscription create failed",
	SubscriptionUpdateFailed:   "Subscription update failed",
	SubscriptionFindOneFailed:  "Subscription find one failed",
	SubscriptionFindByIDFailed: "Subscription find by id failed",
	SubscriptionFindPageFailed: "Subscription find page failed",
	SubscriptionFindManyFailed: "Subscription find many failed",
	SubscriptionDeleteFailed:   "Subscription delete failed",

	ResourceUsageCreateFailed:   "Resource usage create failed",
	ResourceUsageUpdateFailed:   "Resource usage update failed",
	ResourceUsageFindOneFailed:  "Resource usage find one failed",
	ResourceUsageFindByIDFailed: "Resource usage find by id failed",
	ResourceUsageFindPageFailed: "Resource usage find page failed",
	ResourceUsageFindManyFailed: "Resource usage find many failed",
	ResourceUsageDeleteFailed:   "Resource usage delete failed",
	ResourceUsageAiReachLimit:   "Resource usage ai reach limit",

	OEPointHistoryCreateFailed:                           "OE point history create failed",
	OEPointHistoryUpdateFailed:                           "OE point history update failed",
	OEPointHistoryFindOneFailed:                          "OE point history find one failed",
	OEPointHistoryFindByIDFailed:                         "OE point history find by id failed",
	OEPointHistoryFindPageFailed:                         "OE point history find page failed",
	OEPointHistoryFindManyFailed:                         "OE point history find many failed",
	OEPointHistoryDeleteFailed:                           "OE point history delete failed",
	OEPointHistorySendNotificationFailed:                 "OE point history send notification failed",
	OEPointHistoryUpdateExpiredFailed:                    "OE point history update failed",
	OEPointHistoryGetUserActivePointFailed:               "OE point history get user active point failed",
	OEPointHistoryGetUserActiveReferralEarnedPointFailed: "OE point history get user active referral earned point failed",
	OEPointHistoryCountFailed:                            "OE point history count failed",
	OEPointHistoryByStatusAndSourceFailed:                "OE point history by status and source failed",

	// OE Referral
	OEReferralCreateFailed:    "OE referral create failed",
	OEReferralUpdateFailed:    "OE referral update failed",
	OEReferralNotFound:        "OE referral not found",
	OEReferralFindOneFailed:   "OE referral find one failed",
	OEReferralFindByIDFailed:  "OE referral find by id failed",
	OEReferralFindPageFailed:  "OE referral find page failed",
	OEReferralFindManyFailed:  "OE referral find many failed",
	OEReferralDeleteFailed:    "OE referral delete failed",
	OEReferralAlreadyExisted:  "OE referral already existed",
	OEReferralCalculateFailed: "OE referral calculate failed",

	// oe referral code
	OEReferralCodeFindOneFailed:            "OE referral code find one failed",
	OEReferralCodeCreateFailed:             "OE referral code create failed",
	OEReferralCodeReferrerAdded:            "OE referral code referrer added",
	OEReferralCodeReferrerAddedByOtherUser: "OE referral code referrer addedByOtherUser",
	OEReferralCodeNotFound:                 "OE referral code not found",
	OEReferralCodeUpdateFailed:             "OE referral code update failed",
	OEReferralCodeFindByIDFailed:           "OE referral code find by id failed",
	OEReferralCodeFindPageFailed:           "OE referral code find page failed",
	OEReferralCodeFindManyFailed:           "OE referral code find many failed",
	OEReferralCodeDeleteFailed:             "OE referral code delete failed",

	// Certificate
	CertificateAlreadyExist:                 "Certificate already exist",
	CertificateCreateFailed:                 "Internal server error while creating the certificate",
	CertificateUpdateFailed:                 "Internal server error while updating the certificate",
	CertificateDeleteFailed:                 "Internal server error while deleting the certificate",
	CertificateFindPageFailed:               "Internal server error while finding the certificate page",
	CertificateHaveNotFinish:                "You have not finish this course",
	CertificateNotFound:                     "Certificate not found",
	CertificateFindFailed:                   "Internal server error while finding the certificate",
	CertificateNotCompleteConditions:        "Not complete condition certificate",
	CertificateImageNotFound:                "Certificate image not found",
	CertificateMintNFTFailed:                "Internal server error while minting the NFT for certificate",
	CertificateAlreadyMintedNFT:             "Certificate already minted NFT",
	CertificateInsufficientGasToMintNFT:     "Not enough balance to cover the gas fee",
	CertificateInsufficientBalanceToMintNFT: "Not enough balance to mint NFT",
	CertificateNotAllowMintNFT:              "Certificate not allowed to mint NFT",
	CertificateWrongGasFeePayer:             "Wrong the gas fee payer",
	CertificateInsufficientGasSponsor:       "Gas sponsor not enough to mint NFT",
	CertificateOwnerRequired:                "Certificate owner required",

	Create_html_template_failed:          "Create html template fail",
	Delete_html_template_failed:          "Delete html template fail",
	Find_page_html_template_failed:       "Find page html template fail",
	Html_template_not_found:              "Html template not found",
	Update_html_template_failed:          "Update html template failed",
	Find_by_id_html_template_failed:      "Find html template by id fail",
	Html_template_not_support_failed:     "Html template not supported",
	Blog_publish_org_permission_required: "Blog publish org permission failed",
	Blog_delete_permission_required:      "Blog delete permission required",
	Find_one_html_template_failed:        "Find one html template failed",
	Can_not_delete_template_is_default:   "Can not delete template is default",

	Blog_status_already_existed: "blog status already existed",
	Blog_status_reviewing:       "blog status reviewing",

	Update_many_blog_failed:         "Update many blog failed",
	Blog_publish_failed:             "Blog publish failed",
	Blog_add_publish_failed:         "Blog add publish failed",
	Blog_reject_failed:              "Blog reject failed",
	Blog_status_error:               "Blog status not handle",
	Blog_find_one_failed:            "Blog find one failed",
	Blog_update_permission_required: "Blog update permission required",
	Blog_update_failed:              "Blog update failed",
	Publish_blog_update_failed:      "Update publish blog failed",
	Blog_not_found:                  "Blog not found",
	Blog_find_many_failed:           "Blog find many not found",
	Blog_publish_find_many_failed:   "Blog publish find many failed",
	Blog_delete_many_failed:         "DeleteMany blog failed",
	Error_publish_blog_count_failed: "Count number of publish blog failed",

	// Tracking
	Tracking_find_many_failed:          "Tracking find many failed",
	Tracking_create_Failed:             "Tracking create failed",
	Blog_rewrite_from_link_failed:      "Blog rewrite from link failed",
	Blog_rewrite_from_paragraph_failed: "Blog rewrite from paragraph failed",
	Convert_string_failed:              "Convert string failed",

	// User Summary (24000 - 24999)
	User_summary_find_one_failed:   "User summary find one failed",
	User_summary_find_many_failed:  "User summary find many failed",
	User_summary_not_found:         "User summary not found",
	User_summary_upsert_failed:     "User summary upsert failed",
	User_summary_increase_failed:   "User summary increase failed",
	User_summary_decrease_failed:   "User summary decrease failed",
	External_call_error:            "External call error",
	Find_course_partner_failed:     "Find course partner failed",
	Count_course_enrollment_failed: "Count course enrollment failed",

	//AI Blog Rewrite (10800 - 10899)
	AIBlogRewrite_find_one_failed:                "AI Blog Rewrite find one failed",
	AIBlogRewrite_find_many_failed:               "AI Blog Rewrite find many failed",
	AIBlogRewrite_find_page_failed:               "AI Blog Rewrite find page failed",
	AIBlogRewrite_create_failed:                  "AI Blog Rewrite create failed",
	AIBlogRewrite_update_failed:                  "AI Blog Rewrite update failed",
	AIBlogRewrite_update_many_failed:             "AI Blog Rewrite update many failed",
	AIBlogRewrite_offer_generate_failed:          "AI Blog Rewrite offer generate failed",
	AIBlogRewrite_get_offer_data_failed:          "AI Blog Rewrite get offer data failed",
	AIBlogRewrite_offer_rewrite_from_link_failed: "AI Blog Rewrite offer rewrite from link failed",
	AIBlogRewrite_offer_rewrite_paragraph_failed: "AI Blog Rewrite offer rewrite paragraph failed",
	AIBlogRewrite_internal_ai_generate_failed:    "AI Course internal ai generate failed",

	// AI Course  (10900 - 10999)
	AICourseGenerate_find_one_failed:                         "AI Course find_one failed",
	AICourseGenerate_find_many_failed:                        "AI Course find_many failed",
	AICourseGenerate_find_page_failed:                        "AI Course find_page failed",
	AICourseGenerate_create_failed:                           "AI Course create failed",
	AICourseGenerate_update_failed:                           "AI Course update failed",
	AICourseGenerate_not_found:                               "AI Course not found",
	AICourseGenerate_offer_generate_failed:                   "AI Course offer generate failed",
	AICourseGenerate_get_offer_id_failed:                     "AI Course get offer id failed",
	AICourseGenerate_get_offer_data_failed:                   "AI Course get offer data failed",
	AICourseGenerate_response_with_status_failed:             "AI Course response with status failed",
	AICourseGenerate_invalid_offer_type:                      "AI Course invalid offer type failed",
	AICourseGenerate_response_nothing:                        "AI Course response nothing",
	AICourseGenerate_update_many_failed:                      "AI Course update many failed",
	AICourseGenerate_invalid_playlist_link:                   "AI Course invalid playlist link failed",
	AICourseGenerate_internal_ai_generate_failed:             "AI Course internal ai generate failed",
	AICourseGenerate_offer_generate_thumbnail_failed:         "AI Course offer generate thumbnail failed",
	AICourseGenerate_offer_generate_outline_failed:           "AI Course offer generate outline failed",
	AICourseGenerate_get_thumbnail_data_failed:               "AI Course get thumbnail data failed",
	AICourseGenerate_max_try_thumbnail_times_failed:          "AI Course max try thumbnail times",
	AICourseGenerate_offer_generate_from_youtube_failed:      "AI Course offer generate from youtube failed",
	AICourseGenerate_offer_generate_from_learner_desc_failed: "AI Course offer generate from learner desc failed",
	AICourseGenerate_offer_generate_quiz_failed:              "AI Course offer generate from quiz failed",

	// AI History (13000 - 13999)
	AIHistory_find_one_failed:    "AI History find_one failed",
	AIHistory_find_many_failed:   "AI History find_many failed",
	AIHistory_find_page_failed:   "AI History find_page failed",
	AIHistory_create_failed:      "AI History create failed",
	AIHistory_update_failed:      "AI History update failed",
	AIHistory_not_found:          "AI History not found",
	AIHistory_create_many_failed: "AI History create many failed",

	// AIModel error codes (28000 - 28999)
	AIModel_create_failed:      "AIModel create failed",
	AIModel_find_page_failed:   "AIModel findPage failed",
	AIModel_find_one_failed:    "AIModel findOne failed",
	AIModel_not_found:          "AIModel not found",
	AIModel_update_failed:      "AIModel update failed",
	AIModel_create_many_failed: "AIModel create many failed",
	AIModel_find_many_failed:   "AIModel find many failed",
	AIModel_init_failed:        "AIModel init failed",
	AIModel_delete_many_failed: "AIModel delete many failed",
	AIModel_not_available:      "No available AI model with sufficient balance",

	// EarnedPoint Campaign
	OEPointCampaignCreateFailed:           "OE EarnedPoint Campaign create failed",
	OEPointCampaignUpdateFailed:           "OE EarnedPoint Campaign update failed",
	OEPointCampaignFindOneFailed:          "OE EarnedPoint Campaign find one failed",
	OEPointCampaignFindByIDFailed:         "OE EarnedPoint Campaign find by id failed",
	OEPointCampaignFindPageFailed:         "OE EarnedPoint Campaign find page failed",
	OEPointCampaignFindManyFailed:         "OE EarnedPoint Campaign find many failed",
	OEPointCampaignDeleteFailed:           "OE EarnedPoint Campaign delete failed",
	OEPointCampaignSendNotificationFailed: "OE EarnedPoint Campaign send notification failed",
	OEPointCampaignUpdateExpiredFailed:    "OE EarnedPoint Campaign update expired failed",
	OEPointCampaignNotFound:               "OE EarnedPoint Campaign not found",

	// Launchpad
	Create_launchpad_failed:                     "Create launchpad for course failed",
	Update_launchpad_failed:                     "Update launchpad failed",
	Launchpad_not_found:                         "Launchpad not found",
	Launchpad_find_one_failed:                   "Find one launchpad failed",
	Launchpad_find_page_failed:                  "Find page launchpad failed",
	Delete_launchpad_failed:                     "Delete launchpad failed",
	Not_enough_section_to_make_course_launchpad: "Course not enough section to make launchpad",
	Launchpad_is_existed:                        "Launchpad is existed",
	Launchpad_invalid_status:                    "Status of launchpad invalid",
	Min_pledge_is_invalid:                       "Min pledge is invalid",
	Launchpad_reject_failed:                     "Launchpad reject failed",
	Not_launchpad_owner:                         "You are not owner of launchpad",
	Launchpad_is_not_reviewing:                  "Status launchpad is not reviewing",
	Launchpad_pool_status_invalid:               "Launchpad pool status is invalid",
	Launchpad_estimate_funding_days_invalid:     "Must set estimate funding days",

	// Course Launchpad
	Create_course_launchpad_failed:    "Create course launchpad for course failed",
	Update_course_launchpad_failed:    "Update course launchpad failed",
	Course_launchpad_not_found:        "Course launchpad not found",
	Course_launchpad_find_one_failed:  "Find one  course launchpad failed",
	Course_launchpad_find_page_failed: "Find page course launchpad failed",
	Delete_course_launchpad_failed:    "Delete course launchpad failed",
	Course_launchpad_is_existed:       "Course launchpad is existed",

	// Voting milestones
	Create_voting_milestone_failed:    "Create voting milestone failed",
	Update_voting_milestone_failed:    "Update voting milestone failed",
	Voting_milestone_not_found:        "Voting milestone not found",
	Voting_milestone_find_one_failed:  "Voting milestone fine one failed",
	Voting_milestone_find_page_failed: "Voting milestone find page failed",
	Delete_voting_milestone_failed:    "Delete voting milestone failed",
	Voting_milestone_find_many_failed: "Voting milestone find many failed",
	Voting_milestone_invalid:          "Voting milestone invalid",
	Not_enough_voting_milestone:       "Not enough voting milestones",

	// Investment
	Create_investment_failed: "Create investment failed",
	Find_investment_failed:   "Find investment failed",
	Update_investment_failed: "Update investment failed",
	Investment_not_found:     "Investment not found",
	Count_investments_failed: "Count investments failed",
	Pledge_amount_invalid:    "Pledge amount invalid",

	// Voting phase
	Create_voting_phase_failed:    "Create voting phase failed",
	Update_voting_phase_failed:    "Update voting phase failed",
	Voting_phase_not_found:        "Voting phase not found",
	Voting_phase_find_one_failed:  "Voting phase fine one failed",
	Voting_phase_find_page_failed: "Voting phase find page failed",
	Delete_voting_phase_failed:    "Delete voting phase failed",
	Voting_phase_find_many_failed: "Voting phase find many failed",

	// Voting launchpad
	Create_voting_launchpad_failed:    "Create voting launchpad failed",
	Update_voting_launchpad_failed:    "Update voting launchpad failed",
	Voting_launchpad_not_found:        "Voting launchpad not found",
	Voting_launchpad_find_one_failed:  "Voting launchpad fine one failed",
	Voting_launchpad_find_page_failed: "Voting launchpad find page failed",
	Delete_voting_launchpad_failed:    "Delete voting launchpad failed",
	Voting_launchpad_find_many_failed: "Voting launchpad find many failed",
	Find_many_with_pagination_failed:  "Find many with pagination failed",
}

// GetMsg get error information based on Code
func GetMsg(code int) string {
	msg, ok := MsgFlags[code]
	if ok {
		return msg
	}

	return MsgFlags[ERROR]
}
