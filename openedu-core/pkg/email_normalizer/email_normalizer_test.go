package email_normalizer

import (
	"openedu-core/pkg/email_normalizer/rules"
	"testing"
)

func TestNormalizeEmail(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		// Gmail test cases
		{"<EMAIL>", "<EMAIL>"},
		{"<EMAIL>", "<EMAIL>"},
		{"<EMAIL>", "<EMAIL>"},
		{"<EMAIL>", "<EMAIL>"},

		// Non-Gmail test cases
		{"<EMAIL>", "<EMAIL>"},
		{"<EMAIL>", "<EMAIL>"},

		// Invalid cases
		{"invalid-email", "invalid-email"},
	}

	GetRegistry().Register(&rules.GmailRule)
	for _, test := range tests {
		result := NormalizeEmail(test.input)
		if result != test.expected {
			t.Errorf("NormalizeEmail(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestCustomRuleRegistration(t *testing.T) {
	customRule := &rules.NormalizeEmailRule{
		PrimaryDomain:      "custom.com",
		AliasDomains:       []string{"custom alias.com"},
		RemoveDots:         true,
		RemovePlusSuffix:   false,
		ConvertToLowercase: true,
	}
	GetRegistry().Register(customRule)

	// Test with custom domain
	result := NormalizeEmail("<EMAIL>")
	expected := "<EMAIL>"
	if result != expected {
		t.Errorf("Custom rule normalization failed: got %q, expected %q", result, expected)
	}
}
