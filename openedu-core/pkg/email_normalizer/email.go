package email_normalizer

import "strings"

// EmailParts represents the parsed components of an email address
type EmailParts struct {
	Local  string
	Domain string
}

// ParseEmail splits an email into local and domain parts
func ParseEmail(email string) *EmailParts {
	parts := strings.Split(email, "@")
	if len(parts) != 2 || parts[0] == "" || parts[1] == "" {
		return nil
	}
	return &EmailParts{
		Local:  parts[0],
		Domain: parts[1],
	}
}
