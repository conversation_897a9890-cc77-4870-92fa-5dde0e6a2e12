package producer

import (
	"fmt"
	"time"
)

type Type string

const (
	RabbitMQ Type = "rabbitmq"

	PublishRPCTimeout = 1 * time.Minute
)

type Producer interface {
	Publish(queueName string, msg *Message) error
	PublishRPC(queueName string, msg *Message) (*Message, error)
	Close() error
}

type Message struct {
	ID            string
	Body          []byte
	ContentType   string
	CorrelationID *string
	ReplyTo       string
}

func NewProducer(producerType Type) (Producer, error) {
	switch producerType {
	case RabbitMQ:
		return newRabbitMQProducer()
	default:
		return nil, fmt.Errorf("unsupported producer type: %s", producerType)
	}
}
