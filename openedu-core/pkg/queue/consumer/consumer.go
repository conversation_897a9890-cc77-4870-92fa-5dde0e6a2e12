package consumer

import (
	"context"
	"fmt"
)

type Type string

const (
	RabbitMQ Type = "rabbitmq"
)

type Consumer interface {
	FetchMessage(ctx context.Context, queueName string) (<-chan Message, error)
	Close() error
}

type Message interface {
	Reject() error
	Ack() error
	GetID() string
	GetBody() []byte
	GetCorrelationID() string
}

func NewConsumer(consumerType Type) (Consumer, error) {
	switch consumerType {
	case RabbitMQ:
		return newRabbitMQConsumer()
	default:
		return nil, fmt.Errorf("invalid consumer type")
	}
}
