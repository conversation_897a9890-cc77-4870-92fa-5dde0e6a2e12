package log

import (
	"fmt"
	"openedu-core/pkg/ms_team"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"os"
	"path"
	"strings"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

type ConfigLogger struct {
	Mode              string `yaml:"mode" mapstructure:"mode"`
	DisableCaller     bool   `yaml:"disable_caller" mapstructure:"disable_caller"`
	DisableStacktrace bool   `yaml:"disable_stacktrace" mapstructure:"disable_stacktrace"`
	Encoding          string `yaml:"encoding" mapstructure:"encoding"`
	Level             string `yaml:"level" mapstructure:"level"`
	ZapType           string `yaml:"zap_type" mapstructure:"zap_type"`
}

var loggerLevelMap = map[string]zapcore.Level{
	"debug":  zapcore.DebugLevel,
	"info":   zapcore.InfoLevel,
	"warn":   zapcore.WarnLevel,
	"error":  zapcore.ErrorLevel,
	"dpanic": zapcore.DPanicLevel,
	"panic":  zapcore.PanicLevel,
	"fatal":  zapcore.FatalLevel,
}

type logger struct {
	SugarLogger *zap.SugaredLogger
	Logger      *zap.Logger
	key         string
	zapSugar    bool
}

func (l *logger) Error(args ...interface{}) {
	if l.zapSugar {
		l.SugarLogger.Error(args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", l.key),
	}
	l.Logger.Error(str, fields...)
}

func (l *logger) Errorf(template string, args ...interface{}) {
	if l.zapSugar {
		str := fmt.Sprintf("UUID:%s, %s", l.key, template)
		l.SugarLogger.Errorf(str, args...)
		return
	}
	fields := []zapcore.Field{
		zap.String("UUID", l.key),
	}
	l.Logger.Error(fmt.Sprintf(template, args...), fields...)
}

var Logger *logger = &logger{}
var ginResponseLogger *logger = &logger{}

func configure() zapcore.WriteSyncer {
	filePath := getLogFilePath()
	fileName := getLogFileName()
	w := zapcore.AddSync(&lumberjack.Logger{
		Filename:   path.Join(filePath, fileName),
		MaxSize:    100, // megabytes
		MaxAge:     200, // days
		MaxBackups: 5,
		Compress:   true,
	})
	return zapcore.NewMultiWriteSyncer(
		zapcore.AddSync(os.Stderr),
		zapcore.AddSync(w),
	)
}

func GetLogger() *logger {
	return Logger
}

func GinRespLogger() *logger {
	return ginResponseLogger
}

// App Logger constructor
// Ref: https://github.com/ducnpdev/golang-demo/tree/logger
func Newlogger(cfg ConfigLogger) {
	logLevel, exist := loggerLevelMap[cfg.Level]
	if !exist {
		logLevel = zapcore.DebugLevel
	}

	var encoderCfg zapcore.EncoderConfig
	if cfg.Mode == "pro" {
		encoderCfg = zap.NewProductionEncoderConfig()
	} else {
		encoderCfg = zap.NewDevelopmentEncoderConfig()

	}
	encoderCfg.LevelKey = "LEVEL"
	encoderCfg.CallerKey = "CALLER"
	encoderCfg.TimeKey = "TIME"
	encoderCfg.NameKey = "NAME"
	encoderCfg.MessageKey = "MESSAGE"
	encoderCfg.EncodeDuration = zapcore.SecondsDurationEncoder
	encoderCfg.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderCfg.EncodeCaller = zapcore.ShortCallerEncoder
	encoderCfg.EncodeLevel = zapcore.CapitalLevelEncoder
	//encoderCfg.FunctionKey = "FUNC"
	var encoder zapcore.Encoder
	if cfg.Encoding == "console" {
		encoder = zapcore.NewConsoleEncoder(encoderCfg)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderCfg)
	}

	core := zapcore.NewCore(encoder, configure(), zap.NewAtomicLevelAt(logLevel))
	loggerzap := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(2))
	sugarLogger := loggerzap.Sugar()

	logging := &logger{
		SugarLogger: sugarLogger,
		Logger:      loggerzap,
		key:         util.GenerateId(),
		zapSugar:    strings.Contains(cfg.ZapType, "sugar"),
	}

	Logger = logging

	gLoggerzap := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(3))
	gSugarLogger := gLoggerzap.Sugar()
	gLogging := &logger{
		SugarLogger: gSugarLogger,
		Logger:      gLoggerzap,
		key:         util.GenerateId(),
		zapSugar:    strings.Contains(cfg.ZapType, "sugar"),
	}

	ginResponseLogger = gLogging
}

func SetLogID(key string) {
	Logger.key = key
}

func Debug(args ...interface{}) {
	if Logger.zapSugar {
		Logger.SugarLogger.Debug(args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", Logger.key),
	}
	Logger.Logger.Debug(str, fields...)
}

func Debugf(template string, args ...interface{}) {
	if Logger.zapSugar {
		str := fmt.Sprintf("UUID:%s, %s", Logger.key, template)
		Logger.SugarLogger.Debugf(str, args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", Logger.key),
	}
	Logger.Logger.Debug(str, fields...)
}

func Info(args ...interface{}) {
	if Logger.zapSugar {
		Logger.SugarLogger.Info(args...)
		return
	}
	str := fmt.Sprintf("%s", args...)
	fields := []zapcore.Field{
		zap.String("UUID", Logger.key),
	}
	Logger.Logger.Info(str, fields...)
}

func Infof(template string, args ...interface{}) {
	if Logger.zapSugar {
		str := fmt.Sprintf("UUID:%s, %s", Logger.key, template)
		Logger.SugarLogger.Infof(str, args...)
		return
	}
	fields := []zapcore.Field{
		zap.String("UUID", Logger.key),
	}
	Logger.Logger.Info(fmt.Sprintf(template, args...), fields...)
}

func Warn(args ...interface{}) {
	Logger.SugarLogger.Warn(args...)
}

func Warnf(template string, args ...interface{}) {
	Logger.SugarLogger.Warnf(template, args...)
}

func Error(args ...interface{}) {
	Logger.Error(args...)
}

func Errorf(template string, args ...interface{}) {
	Logger.Errorf(template, args...)
}

func ErrorWithAlertf(template string, args ...interface{}) {
	Logger.Errorf(template, args...)

	stage := os.Getenv("STAGE")
	if stage == "" {
		stage = "local"
	}

	title := fmt.Sprintf("[%s] Error Alerts", strings.ToUpper(stage))
	body := "Here is detail error: \n " +
		fmt.Sprintf("* Service Name: **%s** \n ", setting.AppSetting.Name) +
		fmt.Sprintf("* Stage: **%s** \n ", stage)

	if err := ms_team.Alert(title, body, ms_team.WithCodeBlock(fmt.Sprintf(template, args...), ms_team.PlainText)); err != nil {
		Logger.Errorf("Error with alert failed: %v", err)
	}
}

func DPanic(args ...interface{}) {
	Logger.SugarLogger.DPanic(args...)
}

func LoggerDPanicf(template string, args ...interface{}) {
	Logger.SugarLogger.DPanicf(template, args...)
}

func Panic(args ...interface{}) {
	Logger.SugarLogger.Panic(args...)
}

func Panicf(template string, args ...interface{}) {
	Logger.SugarLogger.Panicf(template, args...)
}

func Fatal(args ...interface{}) {
	Logger.SugarLogger.Fatal(args...)
}

func Fatalf(template string, args ...interface{}) {
	Logger.SugarLogger.Fatalf(template, args...)
}
