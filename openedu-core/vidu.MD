
- Admin nhan duoc request
- Admin keu sua title di khong duoc dau
- User nhan duoc thong bao
- User di sua noi dung 
- User bao lai la sua xong roi 
- Vay thi cu cho no sua, nhung approval request van giu





- Get ref link by campaign: nhieu nhat co 2 link ton tai 
- Order voi referralCode 
- Tinh tien tu referral code
- Create referral


- 1 referral can biet duoc
  - Ai la nguoi mua
  - Mua san pham nao 
  - Gia bao nhieu 
  - Tai thoi diem nao
  - Ai la ref
  - Ref link nao 
  - Ref rate bao nhieu
  - Ref duoc bao nhieu
  - Share rate bao nhieu
  - Share bao nhieu
  - Ai la ref2
  - Re2 link
  - Ref2 rate 
  - ref2 amount
  - Ai la ref3
  - Link ref3
  - Ref3 rate 
  - ref3 amount
  - RefLevel 
- Neu la ref2 thi can biet
  - Ai la nguoi mua
  - Mua san pham nao
  - Thoi diem nao
  - Nguoi nhan 
  - Link ref
  - Ref rate
  - Ref amount 


SysAdmin, <PERSON><PERSON>, <PERSON><PERSON>, Org<PERSON><PERSON><PERSON>, 


// Test Admin Site: 




Input: 

AddCategories(entityType, entityID, categories []*models.Category)



u1 := https://admin.openedu101.com
u2 := https://www.admin.openedu101.com
u3 := http://admin.openedu101.com
u4 := http://www.admin.openedu101.com
u5 := https://near.openedu101.com
u6 := https://near.openedu101.com/admin
u7 := https://near.openedu101.com/instructor
u8 := https://near.openedu101.com/learner
u9 := https://near.openedu101.com





Create coupon: 

- Admin/sys-admin/mod: có thể create coupon cho All or Org cụ thể
- Org Admin/ Org mod: có thể create coupon cho Org 

Update/Delete coupon: 
- Admin/Sysadmin/mod: có thể update tất cả coupon 
- Org Admin/Org mod: có thể update coupon thuộc Org của nó

List: 
- All user có thể thấy được active coupons
- Admin/sys-admin/mod: có thể thấy active/inactive All coupons 
- Orgadmin/orgmod: có thể thấy được active/inactive coupọn thuộc org của nó









Affiliate 
- Course owner đc setting referral
- commission theo level, tối đa là 3 
- Tổng 3 ref không quá 100%


- Ref1 tạo link --> 
- Ref2 tạo link từ link của Ref1 
- Ref3 tạo link từ link của Ref2



Referral 
- Course
- User
- Commissions: [{level: 1, amount: 100}, {level: 2, amount: 10}, {level: 3, amount: 10}]






- [x] Org admin duoc quyen add Agency 
- Campaign gom co:
  - Name
  - Schedule 
  - Courses 
  - Affiliate persons: normal user, KOL, Agency
  - Course owner Add/remove KOL/Agency
  - Commissions: có nhiều commission 
    - Quantity: số lần nhận ref
    - Rate: tỉ lệ nhận được
    - Commission bonus
      - quantity
      - percent
  - Mỗi course sẽ có trạng thái active riêng 
  - Start/Stop/Schedule
  - Mỗi User có thể có rate riêng 
  - Nếu user có rate riêng thì bonus lấy từ commission nó apply cho group đó 
  - Commission sẽ dùng chung hoặc apply riêng cho từng nhóm user

- Campaign performance:
  - List order apply ref của campaign 
  - Người mua/người nhận ref



- Course detail trả về Campaign: 
  - Tuỳ vào user đang login và Campagin đang active cho course sẽ trả về commission 
  - User có thể set chia commission của mình cho user người mà mua course 
  - User được copy link ref
    - Link là link kết hợp bởi: course, user, campagin cùng với commission details 
  - User đã mua khoá học sẽ được tiếp ref như là 1 ref1, thằng ref trước là ref 2 ????????




- User mua khoa hoc them theo ref --> ref theo order
- Neu co ref thi sau khi order success --> tinh tien cho tung nguoi ref 
- Chuyen tien va balance cua user theo Fiat Wallet 
- Tao transaction 
- Ghi vao lich su ref: nguoi, mua, ref1, ref2, ref3










4500.0000	2700.0000	0.0000
5000.0000	3000.0000	2000.0000
5000.0000	3000.0000	2000.0000
6000.0000	3000.0000	2000.0000
650.0000	300.0000	200.0000
5850.0000	2700.0000	1800.0000
6500.0000	3000.0000	2000.0000
7150.0000	3300.0000	2200.0000


Đi từ lesson content đi lên:
- filter progress --> lấy ra list content 
- Add content
- Group content vào 1 list lessonUID
- Xác định completed của lesson = content cuối cùng đã hoàn thành hay chưa




