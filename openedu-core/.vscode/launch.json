{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Package",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/main.go",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Remote Debug API-DEV",
            "type": "go",
            "debugAdapter": "dlv-dap",
            "request": "attach",
            "mode": "remote",
            "remotePath": "/home/<USER>/openedu/dev/openedu_core",
            "port": 28000,
            "host": "**************",
            "showLog": true,
            "trace": "verbose",
            "substitutePath": [
                {
                    "from": "${workspaceFolder}",
                    "to": "/home/<USER>/openedu/dev/openedu_core"
                }
            ]
        },
        {
            "name": "Remote Debug API-STAGING",
            "type": "go",
            "debugAdapter": "dlv-dap",
            "request": "attach",
            "mode": "remote",
            "remotePath": "/home/<USER>/openedu/staging/openedu_core",
            "port": 28001,
            "host": "**************",
            "showLog": true,
            "trace": "verbose",
            "substitutePath": [
                {
                    "from": "${workspaceFolder}",
                    "to": "/home/<USER>/openedu/staging/openedu_core"
                }
            ]
        },
        {
            "name": "Remote Debug API-DEMO",
            "type": "go",
            "debugAdapter": "dlv-dap",
            "request": "attach",
            "mode": "remote",
            "remotePath": "/home/<USER>/openedu/demo/openedu_core",
            "port": 28002,
            "host": "**************",
            "showLog": true,
            "trace": "verbose",
            "substitutePath": [
                {
                    "from": "${workspaceFolder}",
                    "to": "/home/<USER>/openedu/demo/openedu_core"
                }
            ]
        },
        {
            "name": "Remote Debug API-TEST",
            "type": "go",
            "debugAdapter": "dlv-dap",
            "request": "attach",
            "mode": "remote",
            "remotePath": "/home/<USER>/openedu/test/openedu_core",
            "port": 28003,
            "host": "**************",
            "showLog": true,
            "trace": "verbose",
            "substitutePath": [
                {
                    "from": "${workspaceFolder}",
                    "to": "/home/<USER>/openedu/test/openedu_core"
                }
            ]
        },
        {
            "name": "Remote Debug API-PHUONG",
            "type": "go",
            "debugAdapter": "dlv-dap",
            "request": "attach",
            "mode": "remote",
            "remotePath": "/home/<USER>/openedu/phuong/openedu_core",
            "port": 28011,
            "host": "**************",
            "showLog": true,
            "trace": "verbose",
            "substitutePath": [
                {
                    "from": "${workspaceFolder}",
                    "to": "/home/<USER>/openedu/phuong/openedu_core"
                }
            ]
        },
        {
            "name": "Remote Debug API-THANH",
            "type": "go",
            "debugAdapter": "dlv-dap",
            "request": "attach",
            "mode": "remote",
            "remotePath": "/home/<USER>/openedu/thanh/openedu_core",
            "port": 28013,
            "host": "**************",
            "showLog": true,
            "trace": "verbose",
            "substitutePath": [
                {
                    "from": "${workspaceFolder}",
                    "to": "/home/<USER>/openedu/thanh/openedu_core"
                }
            ]
        }
    ]
}