package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"strings"

	"gorm.io/gorm"
)

func makeRoleID(input string, orgId *string) string {
	trimmed := strings.TrimSpace(input)

	lowercase := strings.ToLower(trimmed)

	result := strings.ReplaceAll(lowercase, " ", "_")

	if orgId != nil {
		return result + "_" + *orgId
	}
	return result
}

func (s *RoleService) Create(data *dto.RoleRequest) (*models.Role, *e.AppError) {
	role := models.Role{
		Name:        data.Name,
		Description: data.Description,
		OrgId:       data.OrgId,
		ID:          makeRoleID(data.Name, &data.OrgId),
	}

	newRole, err := models.Repository.Role.CreateRole(&role)
	if err != nil {
		return nil, e.NewError500(e.Create_custom_role_failed, err.Error())
	}

	return newRole, nil
}

func (s *RoleService) Update(data *models.Role) *e.AppError {
	if err := models.Repository.Role.Update(data, nil); err != nil {
		return e.NewError500(e.Update_custom_role_failed, err.Error())
	}
	return nil
}

func (s *RoleService) Delete(role *models.Role, orgId *string) *e.AppError {
	if err := models.Repository.Role.Delete(role.ID, orgId, nil); err != nil {
		return e.NewError500(e.Delete_custom_role_failed, err.Error())
	}
	return nil
}

func (s *RoleService) FindPage(query *models.RoleQuery, options *models.FindPageOptions) ([]*models.Role, *models.Pagination, *e.AppError) {
	if roles, pagination, err := models.Repository.Role.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.CouponFindPageFailed, err.Error())
	} else {
		return roles, pagination, nil
	}
}

func (s *RoleService) FindByID(id string) (*models.Role, *e.AppError) {
	if role, err := models.Repository.Role.FindById(id); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Find_role_by_id_failed, err.Error())
		}
		return nil, e.NewError500(e.Find_role_by_id_failed, err.Error())
	} else {
		return role, nil
	}
}

func (s *RoleService) InitNewOrganizationAccess(org *models.Organization) *e.AppError {
	initPageAccess := []*dto.PageAccessRequest{}
	for _, pageAccess := range initPageAccess {
		if _, err := models.Repository.PageAccess.FindOne(&models.PageAccessQuery{Entity: &pageAccess.Entity, Role: &pageAccess.Role, Action: &pageAccess.Action, OrgID: &org.ID}, nil); err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				if _, e := PageAccess.Create(pageAccess, org); e != nil {
					log.Fatalf("Init page access failed: %v", e)
				}
			} else {
				log.Fatalf("Init page access failed: %v", err)
			}
		}
	}
	return nil
}

func (s *RoleService) FindAll() ([]*models.Role, *e.AppError) {
	roles, err := models.Repository.Role.FindAll()
	if err != nil {
		return nil, e.NewError500(e.Error_find_role_failed, err.Error())
	}

	return roles, nil
}
