package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *UserActionService) FollowUser(params *dto.ActionParams) *e.AppError {
	targetUser, fErr := models.Repository.User.FindOne(&models.UserQuery{
		ID: &params.TargetUserID,
	}, nil)
	if fErr != nil {
		if errors.Is(fErr, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Error_user_not_found, fErr.Error())
		}
		return e.NewError500(e.Error_user_find_failed, fErr.Error())
	}

	isUpdated, err := models.Repository.UserAction.Upsert(&models.UserAction{
		UserID:       params.User.ID,
		TargetUserID: targetUser.ID,
		Action:       models.Followed,
	}, nil)

	if err != nil {
		return e.NewError500(e.Upsert_user_action_failed, err.Error())
	}

	if *isUpdated {
		return nil
	}

	if err := models.Repository.UserSummary.Increase(&models.UserSummaryQuery{
		UserID: &params.User.ID,
	}, models.FollowingField, util.FollowUnit, nil); err != nil {
		return e.NewError500(e.User_summary_increase_failed, "Increase follow count for user error: "+err.Error())
	}

	if err := models.Repository.UserSummary.Increase(&models.UserSummaryQuery{
		UserID: &targetUser.ID,
	}, models.FollowersField, util.FollowUnit, nil); err != nil {
		return e.NewError500(e.User_summary_increase_failed, "Increase follow count for user error: "+err.Error())
	}

	return nil
}

func (s *UserActionService) UnFollowUser(params *dto.ActionParams) *e.AppError {
	targetUser, fErr := models.Repository.User.FindOne(&models.UserQuery{
		ID: &params.TargetUserID,
	}, nil)
	if fErr != nil {
		if errors.Is(fErr, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Error_user_not_found, fErr.Error())
		}
		return e.NewError500(e.Error_user_find_failed, fErr.Error())
	}

	userAction, err := models.Repository.UserAction.FindOne(&models.UserActionQuery{
		UserID:       &params.User.ID,
		TargetUserID: &targetUser.ID,
		Action:       util.NewT(models.Followed),
	}, nil)

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.User_action_not_found, err.Error())
		}
		return e.NewError500(e.Find_one_user_action_failed, err.Error())
	}

	if dErr := models.Repository.UserAction.DeleteHard(userAction.ID, nil); dErr != nil {
		return e.NewError500(e.Delete_user_action_failed, dErr.Error())
	}

	if err := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
		UserID: &params.User.ID,
	}, models.FollowingField, util.FollowUnit, nil); err != nil {
		return e.NewError500(e.User_summary_decrease_failed, "Decrease follow count for user error: "+err.Error())
	}

	if err := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
		UserID: &targetUser.ID,
	}, models.FollowersField, util.FollowUnit, nil); err != nil {
		return e.NewError500(e.User_summary_decrease_failed, "Decrease follow count for user error: "+err.Error())
	}

	return nil
}

func (s *UserActionService) BlockUser(params *dto.ActionParams) *e.AppError {
	targetUser, fErr := models.Repository.User.FindOne(&models.UserQuery{
		ID: &params.TargetUserID,
	}, nil)
	if fErr != nil {
		if errors.Is(fErr, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Error_user_not_found, fErr.Error())
		}
		return e.NewError500(e.Error_user_find_failed, fErr.Error())
	}

	if _, err := models.Repository.UserAction.Upsert(&models.UserAction{
		UserID:       params.User.ID,
		TargetUserID: params.TargetUserID,
		Action:       models.Blocked,
	}, nil); err != nil {
		return e.NewError500(e.Upsert_user_action_failed, err.Error())
	}

	userAction, err := models.Repository.UserAction.FindOne(&models.UserActionQuery{
		UserID:       &params.User.ID,
		TargetUserID: &targetUser.ID,
		Action:       util.NewT(models.Followed),
	}, nil)

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return e.NewError500(e.Find_one_user_action_failed, err.Error())
	}

	if dErr := models.Repository.UserAction.DeleteHard(userAction.ID, nil); dErr != nil {
		return e.NewError500(e.Delete_user_action_failed, dErr.Error())
	}

	if err := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
		UserID: &params.User.ID,
	}, models.FollowingField, util.FollowUnit, nil); err != nil {
		return e.NewError500(e.User_summary_decrease_failed, "Decrease follow count for user error: "+err.Error())
	}

	if err := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
		UserID: &targetUser.ID,
	}, models.FollowersField, util.FollowUnit, nil); err != nil {
		return e.NewError500(e.User_summary_decrease_failed, "Decrease follow count for user error: "+err.Error())
	}

	return nil
}

func (s *UserActionService) UnblockUser(params *dto.ActionParams) *e.AppError {
	targetUser, fErr := models.Repository.User.FindByID(params.TargetUserID)
	if fErr != nil {
		if errors.Is(fErr, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Error_user_not_found, fErr.Error())
		}
		return e.NewError500(e.Error_user_find_failed, fErr.Error())
	}

	userAction, err := models.Repository.UserAction.FindOne(&models.UserActionQuery{
		UserID:       &params.User.ID,
		TargetUserID: &targetUser.ID,
		Action:       util.NewT(models.Blocked),
	}, nil)

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.User_action_not_found, err.Error())
		}
		return e.NewError500(e.Find_one_user_action_failed, err.Error())
	}

	if dErr := models.Repository.UserAction.DeleteHard(userAction.ID, nil); dErr != nil {
		return e.NewError500(e.Delete_user_action_failed, dErr.Error())
	}

	return nil
}

func (s *UserActionService) ReportUser(params *dto.ReportUserParams) *e.AppError {
	userAction, err := models.Repository.UserAction.FindOne(&models.UserActionQuery{
		UserID:       &params.User.ID,
		TargetUserID: &params.TargetUserID,
		Action:       util.NewT(models.Reported),
	}, nil)

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Find_one_user_action_failed, err.Error())
	}

	if userAction != nil {
		return e.NewError400(e.User_action_already_report, e.MsgFlags[e.User_action_already_report])
	}

	if err := models.Repository.UserAction.Create(&models.UserAction{
		UserID:       params.User.ID,
		TargetUserID: params.TargetUserID,
		Action:       models.Reported,
	}, nil); err != nil {
		return e.NewError500(e.Create_user_action_failed, err.Error())
	}

	return nil
}

func (s *UserActionService) FindPage(query *models.UserActionQuery, options *models.FindPageOptions) ([]*models.UserAction, *models.Pagination, *e.AppError) {
	if userActions, pagination, err := models.Repository.UserAction.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_user_action_failed, err.Error())
	} else {
		return userActions, pagination, nil
	}
}

func (s *UserActionService) BlockListUsers(user *models.User, userBlockIDs []string) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			log.Error("panic: %v", r)
		}
	}()

	targetUsers, err := models.Repository.User.FindMany(&models.UserQuery{IDIn: &userBlockIDs}, nil)
	if err != nil {
		tx.Rollback()
		return e.NewError500(e.Error_user_find_failed, err.Error())
	}

	userActions := lo.Map(targetUsers, func(tUser *models.User, _ int) *models.UserAction {
		return &models.UserAction{
			UserID:       user.ID,
			TargetUserID: tUser.ID,
			Action:       models.Blocked,
		}
	})
	targetUserIDs := lo.Map(targetUsers, func(tUser *models.User, _ int) string {
		return tUser.ID
	})

	err = models.Repository.UserAction.UpsertMany(userActions, tx)
	if err != nil {
		tx.Rollback()
		return e.NewError500(e.Upsert_many_user_action_failed, err.Error())
	}

	uActionsFollowed, err := models.Repository.UserAction.FindMany(&models.UserActionQuery{
		UserID:         &user.ID,
		TargetUserIDIn: targetUserIDs,
		Action:         util.NewT(models.Followed),
	}, nil)
	if err != nil {
		tx.Rollback()
		return e.NewError500(e.Find_many_user_action_failed, err.Error())
	}

	targetUserIDsFollowed := lo.Map(uActionsFollowed, func(ua *models.UserAction, _ int) string {
		return ua.TargetUserID
	})

	uActionsFollowedIDs := lo.Map(uActionsFollowed, func(ua *models.UserAction, _ int) string {
		return ua.ID
	})

	if len(uActionsFollowed) > 0 {
		if dErr := models.Repository.UserAction.DeleteHardMultiple(uActionsFollowedIDs, tx); dErr != nil {
			tx.Rollback()
			return e.NewError500(e.Delete_user_action_failed, dErr.Error())
		}

		if err := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
			UserID: &user.ID,
		}, models.FollowingField, len(uActionsFollowed), tx); err != nil {
			tx.Rollback()
			return e.NewError500(e.User_summary_decrease_failed, "Decrease follow count for user error: "+err.Error())
		}

		if err := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
			UserIDIn: targetUserIDsFollowed,
		}, models.FollowersField, util.FollowUnit, tx); err != nil {
			tx.Rollback()
			return e.NewError500(e.User_summary_decrease_failed, "Decrease follow count for user error: "+err.Error())
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Upsert_many_user_action_failed, err.Error())
	}

	return nil
}
