package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *HtmlTemplateService) Create(data *dto.HtmlTemplateRequest, userId string, orgId string) (*models.HtmlTemplate, *e.AppError) {
	htmlTemplate := &models.HtmlTemplate{
		Name:          data.Name,
		Type:          data.Type,
		UserID:        userId,
		OrgID:         orgId,
		EnableProject: false,
	}

	if data.FileID != "" {
		file := &models.File{
			Model: models.Model{ID: data.FileID},
		}

		htmlTemplate.Files = append(htmlTemplate.Files, file)
	}

	if data.Props != nil {
		htmlTemplate.Props = *data.Props
	}

	if data.Template != nil {
		htmlTemplate.Template = *data.Template
	}

	if data.CourseCuid != nil {
		htmlTemplate.CourseCuid = *data.CourseCuid
	}

	if data.CreatorName != nil {
		htmlTemplate.CreatorName = *data.CreatorName
	}

	if data.CourseName != nil {
		htmlTemplate.CourseName = *data.CourseName
	}

	if data.Enable != nil {
		htmlTemplate.Enable = *data.Enable
	}

	if err := models.Repository.HtmlTemplate.Create(htmlTemplate, nil); err != nil {
		return nil, e.NewError500(e.Create_html_template_failed, err.Error())
	}

	return htmlTemplate, nil
}

func (s *HtmlTemplateService) FindPage(org *models.Organization, options *models.FindPageOptions) ([]*models.HtmlTemplate, *models.Pagination, *e.AppError) {
	var templateRootID string
	var orgRootID string
	if org.IsRoot() {
		orgRootID = org.ID
	} else {
		rootDomain := setting.AppSetting.BaseDomain
		organization, err := models.Repository.Organization.FindByDomain(rootDomain)
		if err != nil {
			return nil, nil, e.NewError500(e.Organization_find_one_failed, "Get org by domain '"+rootDomain+"' error: "+err.Error())
		}
		orgRootID = organization.ID
	}

	sysConfigRoot, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
		Key:   util.NewT(models.CertificateTemplateDefault),
		OrgID: &orgRootID}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return nil, nil, e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
	}

	if sysConfigRoot != nil {
		templateRootID = sysConfigRoot.Value
	} else {
		templateRootID = ""
	}

	htmlTemplates, pagination, err := models.Repository.HtmlTemplate.FindPageCertificateTemplate(templateRootID, org, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Find_page_html_template_failed, err.Error())
	}

	templateIDEnable := ""
	if !org.IsRoot() {
		sysConfigOrg, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
			Key:   util.NewT(models.CertificateTemplateDefault),
			OrgID: &org.ID}, nil)
		if err != nil && !models.IsRecordNotFound(err) {
			return nil, nil, e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
		}

		if sysConfigOrg != nil {
			templateIDEnable = sysConfigOrg.Value
		}
	}

	if templateIDEnable == "" {
		if templateRootID == "" {
			return htmlTemplates, pagination, nil
		} else {
			templateIDEnable = templateRootID
		}
	}

	lo.ForEach(htmlTemplates, func(item *models.HtmlTemplate, _ int) {
		if item.ID == templateIDEnable {
			item.Enable = true
		}
	})

	return htmlTemplates, pagination, nil
}

func (s *HtmlTemplateService) FindByID(id string) (*models.HtmlTemplate, *e.AppError) {
	if htmlTemplates, err := models.Repository.HtmlTemplate.FindByID(id); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Html_template_not_found, err.Error())
		}
		return nil, e.NewError500(e.Find_by_id_html_template_failed, err.Error())
	} else {
		return htmlTemplates, nil
	}
}

func (r *HtmlTemplateService) Update(data dto.UpdateHtmlTemplateRequest, template *models.HtmlTemplate) *e.AppError {
	if data.FileID != nil {
		newFile := &models.File{
			Model: models.Model{ID: *data.FileID},
		}

		template.Files = []*models.File{newFile}
	}

	if data.Name != nil {
		template.Name = *data.Name
	}

	if data.Props != nil {
		template.Props = *data.Props
	}

	if data.Template != nil {
		template.Template = *data.Template
	}

	if data.EnableProject != nil {
		template.EnableProject = *data.EnableProject
	}

	err := models.Repository.HtmlTemplate.Update(template, nil)
	if err != nil {
		return e.NewError500(e.Update_html_template_failed, err.Error())
	}

	return nil
}

func (s *HtmlTemplateService) DeleteByID(id string) *e.AppError {
	if err := models.Repository.HtmlTemplate.Delete(id, nil); err != nil {
		return e.NewError500(e.Delete_html_template_failed, err.Error())
	}

	return nil
}

func (s *HtmlTemplateService) FindOne(query *models.HtmlTemplateQuery, options *models.FindOneOptions) (*models.HtmlTemplate, *e.AppError) {
	template, err := models.Repository.HtmlTemplate.FindOne(query, options)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Html_template_not_found, err.Error())
		}
		return nil, e.NewError500(e.Find_one_html_template_failed, err.Error())
	}

	return template, nil
}

func (s *HtmlTemplateService) FindOneOptions(query *models.HtmlTemplateQuery, options *models.FindOneOptions) (*models.HtmlTemplate, *e.AppError) {
	template, err := models.Repository.HtmlTemplate.FindOne(query, options)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.CertificateFindFailed, err.Error())
	}

	return template, nil
}

func (s *HtmlTemplateService) FindPageCertificateTemplateForCourse(course *models.Course, options *models.FindPageOptions, org *models.Organization) ([]*models.HtmlTemplate, *models.Pagination, *e.AppError) {
	var templateRootID string
	var orgRootID string
	if org.IsRoot() {
		orgRootID = org.ID
	} else {
		rootDomain := setting.AppSetting.BaseDomain
		organization, err := models.Repository.Organization.FindByDomain(rootDomain)
		if err != nil {
			return nil, nil, e.NewError500(e.Organization_find_one_failed, "Get org by domain '"+rootDomain+"' error: "+err.Error())
		}
		orgRootID = organization.ID
	}

	sysConfigRoot, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
		Key:   util.NewT(models.CertificateTemplateDefault),
		OrgID: &orgRootID}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return nil, nil, e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
	}

	if sysConfigRoot != nil {
		templateRootID = sysConfigRoot.Value
	} else {
		templateRootID = ""
	}

	htmlTemplates, pagination, err := models.Repository.HtmlTemplate.FindPageCertificateTemplateForCourse(course.Cuid, templateRootID, org, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Find_page_html_template_failed, err.Error())
	}

	return htmlTemplates, pagination, nil
}

func (r *HtmlTemplateService) EnableCertificateLayer(template *models.HtmlTemplate, course *models.Course, userID string, orgID string, data *dto.EnableCertificateRequest) *e.AppError {
	// find layer of course is enabled
	templateEnableExist, appErr := HtmlTemplate.FindOneOptions(
		&models.HtmlTemplateQuery{
			CourseCuid:     &course.Cuid,
			Enable:         util.NewBool(true),
			IncludeDeleted: util.NewBool(false),
			Type:           util.NewString(string(models.CertificateLayer))}, nil)
	if appErr != nil {
		return appErr
	}

	if templateEnableExist != nil {
		if templateEnableExist.ID == template.ID {
			if data.CourseName != "" {
				templateEnableExist.CourseName = data.CourseName
			}
			templateEnableExist.Organizations = data.Organizations
			templateEnableExist.Signatures = data.Signatures

			err := models.Repository.HtmlTemplate.Update(templateEnableExist, nil)
			if err != nil {
				return e.NewError500(e.Update_html_template_failed, "update template layer for course failed: "+err.Error())
			}

			return nil

		} else {
			appErr := HtmlTemplate.DeleteByID(templateEnableExist.ID)
			if appErr != nil {
				return appErr
			}
		}
	}

	newTemplate := &models.HtmlTemplate{
		Name:          template.Name,
		Type:          models.CertificateLayer,
		UserID:        userID,
		CourseCuid:    course.Cuid,
		CourseName:    course.Name,
		CreatorName:   template.CreatorName,
		OrgID:         orgID,
		Enable:        true,
		Files:         template.Files,
		Props:         template.Props,
		Template:      template.Template,
		Organizations: data.Organizations,
		Signatures:    data.Signatures,
		EnableProject: template.EnableProject,
	}

	if data.CourseName != "" {
		newTemplate.CourseName = data.CourseName
	}

	err := models.Repository.HtmlTemplate.Create(newTemplate, nil)
	if err != nil {
		return e.NewError500(e.Create_html_template_failed, err.Error())
	}

	return nil
}

func (r *HtmlTemplateService) CheckPermAndCreateHTMLTemplates(data *dto.HtmlTemplateRequest, user *models.User, orgID string) (*models.HtmlTemplate, *e.AppError) {
	switch data.Type {
	case models.CertificateTemplate:
		if !user.IsSysAdmin() && !user.IsOrgAdmin(orgID) {
			return nil, e.NewError403(e.FORBIDDEN, "Permission denied")
		}

		template, aErr := HtmlTemplate.Create(data, user.ID, orgID)
		if aErr != nil {
			return nil, aErr
		}

		return template, nil

	default:
		return nil, e.NewError400(e.INVALID_PARAMS, "Type of html-template not supported")
	}
}

func (r *HtmlTemplateService) CheckPermDeleteHTMLTemplates(template *models.HtmlTemplate, user *models.User, org *models.Organization) *e.AppError {
	switch template.Type {
	case models.CertificateTemplate:
		if !user.IsSysAdmin() && !user.IsOrgAdmin(org.ID) {
			return e.NewError403(e.FORBIDDEN, "Permission denied")
		}

		if !user.IsOrgAdmin(template.OrgID) {
			return e.NewError403(e.FORBIDDEN, "Permission denied")
		}

		sysConfig, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
			Key:   util.NewT(models.CertificateTemplateDefault),
			OrgID: &template.OrgID}, nil)
		if err != nil && !models.IsRecordNotFound(err) {
			return e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
		}

		if template.ID == sysConfig.Value {
			return e.NewError400(e.Can_not_delete_template_is_default, "Template is default")
		}

		oErr := r.DeleteByID(template.ID)
		if oErr != nil {
			log.Error("delete certificate template by ID failed", oErr)
			return oErr
		}

	default:
		return e.NewError400(e.INVALID_PARAMS, "Type of html-template not supported")
	}

	return nil
}

func (r *HtmlTemplateService) CheckPermUpdateHTMLTemplates(template *models.HtmlTemplate, user *models.User, orgID string) *e.AppError {
	switch template.Type {
	case models.CertificateTemplate:
		if !user.IsSysAdmin() && !user.IsOrgAdmin(orgID) {
			return e.NewError403(e.FORBIDDEN, "Permission denied")
		}

	default:
		return e.NewError400(e.INVALID_PARAMS, "Type of html-template not supported")
	}

	return nil
}

func (r *HtmlTemplateService) UpdateTemplateConfig(templateID string, orgID string, isDefault bool) *e.AppError {
	sysConfig, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
		Key:   util.NewT(models.CertificateTemplateDefault),
		OrgID: &orgID}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
	}

	// config existed
	if sysConfig != nil {
		// not default
		if !isDefault {
			return nil
		} else {
			// set default new for config
			sysConfig.Value = templateID
			if err = models.Repository.System.Update(sysConfig, nil); err != nil {
				return e.NewError500(e.Update_system_config_failed, "Update system config failed: "+err.Error())
			}

			return nil
		}
	}

	// create new config
	newSysConfig := &models.SystemConfig{
		Key:      models.CertificateTemplateDefault,
		Value:    templateID,
		OrgID:    orgID,
		DataType: models.String,
	}
	if err = models.Repository.System.Create(newSysConfig, nil); err != nil {
		return e.NewError500(e.Create_system_config_failed, "Create system config failed: "+err.Error())
	}

	return nil
}

func (r *HtmlTemplateService) EnableCertificateTemplateForOrg(templateID string, org *models.Organization, user *models.User) *e.AppError {
	sysConfig, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
		Key:   util.NewT(models.CertificateTemplateDefault),
		OrgID: &org.ID}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
	}

	if templateID == sysConfig.Value {
		return nil
	}

	if org.IsRoot() {
		sysConfig.Value = templateID
		if err = models.Repository.System.Update(sysConfig, nil); err != nil {
			return e.NewError500(e.Update_system_config_failed, "Update system config failed: "+err.Error())
		}

		return nil
	}

	// if org not root
	rootOrg := Organization.GetRoot()
	rootConfig, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
		Key:   util.NewT(models.CertificateTemplateDefault),
		OrgID: &rootOrg.ID}, nil)
	if err != nil {
		return e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
	}

	if templateID == rootConfig.Value {
		sysConfig.Value = ""
		if err = models.Repository.System.Update(sysConfig, nil); err != nil {
			return e.NewError500(e.Update_system_config_failed, "Update system config failed: "+err.Error())
		}

		return nil
	}

	sysConfig.Value = templateID
	if err = models.Repository.System.Update(sysConfig, nil); err != nil {
		return e.NewError500(e.Update_system_config_failed, "Update system config failed: "+err.Error())
	}

	return nil
}

func (r *HtmlTemplateService) GetCertificateLayerForCourse(course *models.Course) (*models.HtmlTemplate, *e.AppError) {
	template, appErr := HtmlTemplate.FindOneOptions(
		&models.HtmlTemplateQuery{
			CourseCuid:     &course.Cuid,
			Enable:         util.NewBool(true),
			IncludeDeleted: util.NewBool(false),
			Type:           util.NewString(string(models.CertificateLayer))},
		&models.FindOneOptions{
			Preloads: []string{models.FilesField},
		})
	if appErr != nil {
		return nil, appErr
	}

	var result *models.HtmlTemplate
	if template == nil {
		org, appErr := Organization.FindByID(course.OrgID, true, nil)
		if appErr != nil {
			return nil, appErr
		}

		sysConfig, appErr := SystemConfig.FindOneOptions(&models.SystemConfigQuery{
			Key:   util.NewT(models.CertificateTemplateDefault),
			OrgID: &course.OrgID}, nil)
		if appErr != nil {
			return nil, appErr
		}

		if org.IsRoot() && sysConfig == nil {
			return nil, e.NewError400(e.Html_template_not_found, "Not exist html template")
		}

		var templateDefault *models.HtmlTemplate
		if org.IsRoot() {
			if sysConfig.Value == "" {
				return nil, e.NewError400(e.Html_template_not_found, "Not exist html template")
			}

			templateRoot, appErr := HtmlTemplate.FindByID(sysConfig.Value)
			if appErr != nil {
				return nil, appErr
			}

			templateDefault = templateRoot
		} else {
			if sysConfig != nil && sysConfig.Value != "" {
				templateOrg, appErr := HtmlTemplate.FindByID(sysConfig.Value)
				if appErr != nil {
					return nil, appErr
				}

				templateDefault = templateOrg

			} else {
				rootOrg := Organization.GetRoot()
				rootConfig, err := models.Repository.System.FindOne(&models.SystemConfigQuery{
					Key:   util.NewT(models.CertificateTemplateDefault),
					OrgID: &rootOrg.ID}, nil)
				if err != nil {
					return nil, e.NewError500(e.System_config_find_one_failed, "Check existing system config error: "+err.Error())
				}

				templateRoot, appErr := HtmlTemplate.FindByID(rootConfig.Value)
				if appErr != nil {
					return nil, appErr
				}

				templateDefault = templateRoot
			}
		}

		templateDefault.CourseName = course.Name
		templateDefault.CourseCuid = course.Cuid
		if course.Owner.DisplayName == "" {
			templateDefault.CreatorName = course.Owner.Username
		} else {
			templateDefault.CreatorName = course.Owner.DisplayName
		}

		templateDefault.Enable = true
		result = templateDefault
	} else {
		result = template
	}

	return result, nil
}
