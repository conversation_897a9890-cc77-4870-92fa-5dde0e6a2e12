package services

import (
	"openedu-core/models"
	"openedu-core/pkg/e"

	"gorm.io/gorm"
)

func (s *UserRoleOrgService) FindByUserId(userID string) ([]*models.UserRoleOrg, *e.AppError) {
	if urs, err := models.Repository.UserRoleOrg.FindByUserId(userID); err != nil {
		return nil, e.NewError500(e.Find_user_role_org_faild, err.Error())
	} else {
		return urs, nil
	}
}

func (s *UserRoleOrgService) UpsertManyRole(urs []*models.UserRoleOrg, trans *gorm.DB) *e.AppError {
	if err := models.Repository.UserRoleOrg.UpsertManyRole(urs, nil); err != nil {
		return e.NewError500(e.User_role_org_upsert_many_failed, err.Error())
	}
	return nil
}
