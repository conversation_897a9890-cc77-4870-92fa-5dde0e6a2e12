package services

import (
	"errors"
	"fmt"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"time"
)

func (s *OEReferralService) Create(history *models.OEReferral) (*models.OEReferral, *e.AppError) {
	if err := models.Repository.OEReferral(s.ctx).Create(history, nil); err != nil {
		return nil, e.NewError500(e.OEReferralCreateFailed, "create: "+err.Error())
	} else {
		return history, nil
	}
}

func (s *OEReferralService) Update(history *models.OEReferral) (*models.OEReferral, *e.AppError) {
	if err := models.Repository.OEReferral(s.ctx).Update(history, nil); err != nil {
		return nil, e.NewError500(e.OEReferralUpdateFailed, "create: "+err.Error())
	} else {
		return history, nil
	}
}

func (s *OEReferralService) FindOne(query *models.OEReferralQuery, options *models.FindOneOptions) (*models.OEReferral, *e.AppError) {
	if plan, err := models.Repository.OEReferral(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OEReferralNotFound, "referral not found")
		}
		return nil, e.NewError500(e.OEReferralFindOneFailed, "FindOne: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEReferralService) FindById(id string, options *models.FindOneOptions) (*models.OEReferral, *e.AppError) {
	if plan, err := models.Repository.OEReferral(s.ctx).FindOne(&models.OEReferralQuery{ID: util.NewString(id)}, options); err != nil {
		return nil, e.NewError500(e.OEReferralFindByIDFailed, "FindById: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEReferralService) FindPage(query *models.OEReferralQuery, options *models.FindPageOptions) ([]*models.OEReferral, *models.Pagination, *e.AppError) {
	if plans, pagination, err := models.Repository.OEReferral(s.ctx).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.OEReferralFindPageFailed, "FindPage: "+err.Error())
	} else {
		return plans, pagination, nil
	}
}

func (s *OEReferralService) FindMany(query *models.OEReferralQuery, options *models.FindManyOptions) ([]*models.OEReferral, *e.AppError) {
	if plans, err := models.Repository.OEReferral(s.ctx).FindMany(query, options); err != nil {
		return nil, e.NewError500(e.OEReferralFindManyFailed, "FindMany: "+err.Error())
	} else {
		return plans, nil
	}
}

func (s *OEReferralService) Delete(id string) *e.AppError {
	if err := models.Repository.OEReferral(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.OEReferralDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}

func (s *OEReferralService) RefNewUserProgram(code string, referee *models.User) *e.AppError {
	// lay code de lay ra referrer
	refCode, cErr := OEReferralCode(s.ctx).FindOne(&models.OEReferralCodeQuery{
		Code: util.NewString(code),
	}, &models.FindOneOptions{Preloads: []string{models.UserField}})
	if cErr != nil {
		return cErr
	}

	// get ref user campaign
	campaigns, campaignErr := OEPointCampaign(s.ctx).GetActiveReferralUserCampaign()
	if campaignErr != nil {
		return campaignErr
	}

	return s.RefUserReward(&dto.OEReferralRequest{
		Referrer:  refCode.User,
		Referee:   referee,
		Trigger:   models.CompleteRegisterTrigger,
		Campaigns: campaigns,
		Code:      refCode,
	})
}

func (s *OEReferralService) RefUserReward(request *dto.OEReferralRequest) *e.AppError {
	trigger := request.Trigger
	for _, campaign := range request.Campaigns {
		existedRef, eErr := s.FindOne(&models.OEReferralQuery{
			UserID:     util.NewString(request.Referrer.ID),
			RefereeID:  util.NewString(request.Referee.ID),
			Trigger:    util.NewT(trigger),
			CampaignID: util.NewString(campaign.ID),
		}, nil)

		if eErr != nil && eErr.ErrCode != e.OEReferralNotFound {
			return eErr
		}

		if existedRef != nil {
			return e.NewError400(e.OEReferralAlreadyExisted, "ReferralAlreadyExisted: "+existedRef.ID)
		}

		setting := models.ReferralInviteUserProgram{}
		util.MapToStruct(campaign.Setting, &setting)
		referrerReward := models.PointReward{}
		refereeReward := models.PointReward{}
		milestoneBonus := models.PointReward{}

		isTimeBasedBonus := false
		timeBaseBonus := models.TimeBasedBonus{}

		now := time.Now().UnixMilli()
		if setting.TimeBased {
			bonus := setting.TimeBasedBonus
			if (bonus.StartDate == 0 || bonus.StartDate <= now) && (bonus.EndDate == 0 || bonus.EndDate >= now) {
				isTimeBasedBonus = true
				timeBaseBonus = bonus
			}
		}

		// New user referral
		if setting.Trigger == trigger {
			referrerReward = setting.ReferrerReward
			refereeReward = setting.RefereeReward
			if referrerReward.Amount.GreaterThan(decimal.Zero) {
				if refErr := s.referralRegisterAccountReward(request, referrerReward, refereeReward, isTimeBasedBonus, timeBaseBonus, campaign, trigger); refErr != nil {
					return refErr
				}
			}

		}

		if setting.MilestoneBonus {
			refCount, cErr := models.Repository.OEReferral(s.ctx).Count(&models.OEReferralQuery{
				UserID:     util.NewString(request.Referrer.ID),
				CampaignID: util.NewString(campaign.ID),
			})
			if cErr != nil {
				return e.NewError500(e.OEReferralCalculateFailed, "refCount: "+cErr.Error())
			}

			bonus, ok := lo.Find(setting.RefCountBonus, func(item models.RefCountBonus) bool {
				return item.Enable && item.ReachCount == int(refCount)
			})
			if ok {
				milestoneBonus = bonus.Reward
				mileStoneClaim := dto.UserClaimPointRequest{
					User:   request.Referrer,
					Point:  milestoneBonus.Amount,
					Source: models.ReferralMilestoneSource,
					Props: &models.OEPointHistoryProps{
						MilestoneReached: refCount,
					},
					CampaignID: campaign.ID,
				}
				if his, hisErr := OEPointHistory(s.ctx).ClaimPoint(&mileStoneClaim); hisErr != nil {
					return hisErr
				} else {
					if bErr := s.calBaseTimeBonus(request.Referrer, trigger, milestoneBonus.Amount, isTimeBasedBonus, timeBaseBonus, his.ID, campaign.ID); bErr != nil {
						return bErr
					}
				}
			}
		}

		// Feature discovery
		if setting.FeaturedDiscover {
			if trigger == models.FirstCompleteCourseTrigger {
				if fdErr := s.featureDiscoveryReward(setting.CompleteCourseBonus, request.Referrer, models.ReferralFeatureDiscoveryCourseSource, trigger, isTimeBasedBonus, timeBaseBonus, campaign.ID); fdErr != nil {
					return fdErr
				}
			}

			if trigger == models.FirstDepositFiatTrigger {
				if fdErr := s.featureDiscoveryReward(setting.DepositFiatBonus, request.Referrer, models.ReferralFeatureDiscoveryFiatSource, trigger, isTimeBasedBonus, timeBaseBonus, campaign.ID); fdErr != nil {
					return fdErr
				}
			}

			if trigger == models.FirstDepositCryptoTrigger {
				if fdErr := s.featureDiscoveryReward(setting.DepositTokenBonus, request.Referrer, models.ReferralFeatureDiscoveryCryptoSource, trigger, isTimeBasedBonus, timeBaseBonus, campaign.ID); fdErr != nil {
					return fdErr
				}
			}
		}

		// Streak
		if setting.StreakReward {
			if setting.WeeklyStreakBonus.Enable {
				weeklyStreakReward := models.PointReward{}
				startOfWeek, endOfWeek := util.GetCurrentWeekRange()
				refCountByWeek, cwErr := models.Repository.OEReferral(s.ctx).Count(&models.OEReferralQuery{
					StartDateLt: util.NewInt(int(startOfWeek.UnixMilli())),
					EndDateGt:   util.NewInt(int(endOfWeek.UnixMilli())),
					CampaignID:  util.NewString(campaign.ID),
					UserID:      util.NewString(request.Referrer.ID),
				})
				if cwErr != nil {
					return e.NewError500(e.OEReferralCalculateFailed, "refCountByWeek: "+cwErr.Error())
				}

				if setting.WeeklyStreakBonus.Threshold == int(refCountByWeek) {
					weeklyStreakReward = setting.WeeklyStreakBonus.Reward
				}
				if weeklyStreakReward.Amount.GreaterThan(decimal.Zero) {
					featureRewardClaim := dto.UserClaimPointRequest{
						User:   request.Referrer,
						Point:  weeklyStreakReward.Amount,
						Source: models.ReferralStreakWeeklySource,
						Props: &models.OEPointHistoryProps{
							Trigger: string(trigger),
						},
						CampaignID: campaign.ID,
					}

					if his, hisErr := OEPointHistory(s.ctx).ClaimPoint(&featureRewardClaim); hisErr != nil {
						return hisErr
					} else {
						if bErr := s.calBaseTimeBonus(request.Referrer, trigger, weeklyStreakReward.Amount, isTimeBasedBonus, timeBaseBonus, his.ID, campaign.ID); bErr != nil {
							return bErr
						}
					}
				}
			}

			if setting.MonthlyStreakBonus.Enable {
				monthlyStreakReward := models.PointReward{}
				startOfMonth, endOfMonth := util.GetCurrentWeekRange()
				refCountByMonth, cwErr := models.Repository.OEReferral(s.ctx).Count(&models.OEReferralQuery{
					StartDateLt: util.NewInt(int(startOfMonth.UnixMilli())),
					EndDateGt:   util.NewInt(int(endOfMonth.UnixMilli())),
					CampaignID:  util.NewString(campaign.ID),
					UserID:      util.NewString(request.Referrer.ID),
				})
				if cwErr != nil {
					return e.NewError500(e.OEReferralCalculateFailed, "refCountByMonth: "+cwErr.Error())
				}

				if setting.MonthlyStreakBonus.Threshold == int(refCountByMonth) {
					monthlyStreakReward = setting.MonthlyStreakBonus.Reward
				}

				if monthlyStreakReward.Amount.GreaterThan(decimal.Zero) {
					featureRewardClaim := dto.UserClaimPointRequest{
						User:   request.Referrer,
						Point:  monthlyStreakReward.Amount,
						Source: models.ReferralStreakMonthlySource,
						Props: &models.OEPointHistoryProps{
							Trigger: string(trigger),
						},
						CampaignID: campaign.ID,
					}

					// tất cả những chỗ tính amount này sẽ cộng + trước, nếu có bonus thì thêm bonus bằng 1 record khác
					if his, hisErr := OEPointHistory(s.ctx).ClaimPoint(&featureRewardClaim); hisErr != nil {
						return hisErr
					} else {
						if bErr := s.calBaseTimeBonus(request.Referrer, trigger, monthlyStreakReward.Amount, isTimeBasedBonus, timeBaseBonus, his.ID, campaign.ID); bErr != nil {
							return bErr
						}
					}

				}
			}
		}

	}

	return nil
}

func (s *OEReferralService) featureDiscoveryReward(
	featureRewardReward models.PointReward,
	referrer *models.User,
	source models.PointSource,
	trigger models.OEReferralTrigger,
	isBonus bool,
	bonus models.TimeBasedBonus,
	campaignID string,
) *e.AppError {
	if featureRewardReward.Amount.GreaterThan(decimal.Zero) {
		featureRewardClaim := dto.UserClaimPointRequest{
			User:   referrer,
			Point:  featureRewardReward.Amount,
			Source: source,
			Props: &models.OEPointHistoryProps{
				Trigger: string(trigger),
			},
			CampaignID: campaignID,
		}

		if his, hisErr := OEPointHistory(s.ctx).ClaimPoint(&featureRewardClaim); hisErr != nil {
			return hisErr
		} else {
			if bErr := s.calBaseTimeBonus(referrer, trigger, featureRewardReward.Amount, isBonus, bonus, his.ID, campaignID); bErr != nil {
				return bErr
			}
		}
	}

	return nil
}

func (s *OEReferralService) calBaseTimeBonus(
	user *models.User,
	trigger models.OEReferralTrigger,
	amount decimal.Decimal,
	isBonus bool,
	bonus models.TimeBasedBonus,
	bonusFor string,
	campaignID string,
) *e.AppError {
	if isBonus {
		bonusAmount := decimal.Zero
		if bonus.Reward.Type == models.PercentageValue {
			bonusAmount = amount.Mul(bonus.Reward.Amount).Div(decimal.NewFromInt(100))
		} else {
			bonusAmount = bonus.Reward.Amount
		}

		if bonusAmount.GreaterThan(decimal.Zero) {
			baseTimeBonus := dto.UserClaimPointRequest{
				User:   user,
				Point:  bonusAmount,
				Source: models.ReferralTimeBaseBonusSource,
				Props: &models.OEPointHistoryProps{
					Trigger:  string(trigger),
					BonusFor: bonusFor,
				},
				CampaignID: campaignID,
			}
			if _, hisErr := OEPointHistory(s.ctx).ClaimPoint(&baseTimeBonus); hisErr != nil {
				return hisErr
			}
		}
	}

	return nil
}

func (s *OEReferralService) referralRegisterAccountReward(
	request *dto.OEReferralRequest,
	referrerReward models.PointReward,
	refereeReward models.PointReward,
	isTimeBasedBonus bool,
	timeBaseBonus models.TimeBasedBonus,
	campaign *models.OEPointCampaign,
	trigger models.OEReferralTrigger,
) *e.AppError {
	referral := &models.OEReferral{
		UserID:        request.Referrer.ID,
		Code:          request.Code.Code,
		RefCodeID:     request.Code.ID,
		CampaignID:    campaign.ID,
		CampaignType:  campaign.Program,
		PointReward:   referrerReward,
		Amount:        referrerReward.Amount,
		Trigger:       trigger,
		RefereeID:     request.Referee.ID,
		RefereeReward: refereeReward,
		RefereeAmount: refereeReward.Amount,
	}

	if refErr := models.Repository.OEReferral(s.ctx).Create(referral, nil); refErr != nil {
		return e.NewError500(e.OEReferralCreateFailed, "create ref failed"+refErr.Error())
	}

	// referral claim
	referrerClaim := &dto.UserClaimPointRequest{
		User:       request.Referrer,
		Point:      referrerReward.Amount,
		Source:     models.ReferralUserSource,
		Props:      &models.OEPointHistoryProps{RefFromUserID: request.Referee.ID, Trigger: string(models.RefereeUserSource)},
		CampaignID: campaign.ID,
	}
	if referrerHis, hisErr := OEPointHistory(s.ctx).ClaimPoint(referrerClaim); hisErr != nil {
		return hisErr
	} else {
		if bErr := s.calBaseTimeBonus(request.Referrer, trigger, referrerReward.Amount, isTimeBasedBonus, timeBaseBonus, referrerHis.ID, campaign.ID); bErr != nil {
			return bErr
		}
	}

	// referee claim
	refereeClaim := &dto.UserClaimPointRequest{
		User:       request.Referee,
		Point:      refereeReward.Amount,
		Source:     models.RefereeUserSource,
		Props:      &models.OEPointHistoryProps{RefFromUserID: request.Referrer.ID, Trigger: string(models.RefereeUserSource)},
		CampaignID: campaign.ID,
	}
	if refereeHis, hisErr := OEPointHistory(s.ctx).ClaimPoint(refereeClaim); hisErr != nil {
		return hisErr
	} else {
		if bErr := s.calBaseTimeBonus(request.Referee, trigger, referrerReward.Amount, isTimeBasedBonus, timeBaseBonus, refereeHis.ID, campaign.ID); bErr != nil {
			return bErr
		}
	}

	// update to referrer
	if _, oeErr := OEReferralCode(s.ctx).AddReferrerForReferee(request.Referee, request.Referrer, campaign); oeErr != nil {
		return oeErr
	}
	return nil
}

func (s *OEReferralService) InviteReferee(request dto.OEReferralInviteRefereeRequest) *e.AppError {
	referrer := app.GetLoggedUser(s.ctx)
	refCode, codeErr := OEReferralCode(s.ctx).GetUserReferralCode(referrer.ID)
	if codeErr != nil {
		return codeErr
	}
	openedu, err := models.Repository.Organization.FindByDomain(setting.AppSetting.BaseDomain)
	if err != nil {
		return e.NewError500(e.Organization_find_one_failed, "FindOne: "+err.Error())
	}

	referrerLocale := referrer.MyLocale()
	// base on Referral type and event to choose the email template
	emailEvent := commdto.EventReferralNewUserProgramVi
	switch request.ReferralType {
	case models.RefUserProgram:
		if referrerLocale == models.ViLocale {
			emailEvent = commdto.EventReferralNewUserProgramVi
		} else {
			emailEvent = commdto.EventReferralNewUserProgramEn
		}
	}

	refLink := fmt.Sprintf("https://%s/%s/signup?ref_code=%s",
		setting.AppSetting.BaseDomain,
		referrerLocale,
		refCode.Code)

	for _, email := range request.Emails {
		referee := models.User{
			Email: email,
		}
		// send mail
		mailParams := commdto.MapEmailParams{
			commdto.EmailParamReferralUserLink:      refLink,
			commdto.EmailParamReferralReferrerName:  referrer.MyDisplayName(),
			commdto.EmailParamReferralReferrerEmail: referrer.Email,
			commdto.EmailParamReferralReferralCode:  refCode.Code,
		}

		go func() {
			if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
				User:        referee.IntoComm(),
				Org:         openedu.IntoComm(),
				Event:       emailEvent,
				ExtendDatas: mailParams,
				IsQueue:     true,
			}); eErr != nil {
				log.ErrorWithAlertf("OEReferralService.InviteReferee::Send email failed: %v", eErr)
			}
		}()
	}

	return nil
}

func (s *OEReferralService) RefereeTriggerReferralEvent(referee *models.User, trigger models.OEReferralTrigger) *e.AppError {
	props := referee.Props
	if props.RefCode != "" {
		alreadyRef := false
		firstCourse := false
		firstFiat := false
		firstCrypto := false
		switch trigger {
		case models.FirstCompleteCourseTrigger:
			if props.FirstCompleteCourse == true {
				alreadyRef = true
				firstCourse = true
			}
			break
		case models.FirstDepositFiatTrigger:
			if props.FirstDepositFiat == true {
				alreadyRef = true
				firstFiat = true
			}
			break
		case models.FirstDepositCryptoTrigger:
			if props.FirstDepositCrypto == true {
				alreadyRef = true
				firstCrypto = true
			}
			break
		default:
			break
		}

		if !alreadyRef {
			refCode, codeErr := OEReferralCode(s.ctx).FindByCode(props.RefCode, &models.FindOneOptions{
				Preloads: []string{models.UserField},
			})
			if codeErr != nil {
				return codeErr
			}
			if refCode != nil {
				campaigns, campaignErr := OEPointCampaign(s.ctx).GetActiveReferralUserCampaign()
				if campaignErr != nil {
					return campaignErr
				}

				refUserErr := s.RefUserReward(&dto.OEReferralRequest{
					Referrer:  refCode.User,
					Referee:   referee,
					Trigger:   trigger,
					Campaigns: campaigns,
					Code:      refCode,
				})
				if refUserErr != nil {
					return refUserErr
				}

				// Ref user
				referee.Props.FirstCompleteCourse = firstCourse
				referee.Props.FirstDepositFiat = firstFiat
				referee.Props.FirstDepositCrypto = firstCrypto
				if _, updateUserErr := User.Update(referee); updateUserErr != nil {
					return updateUserErr
				}
			}
		}
	}
	return nil
}
