package services

import (
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"regexp"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *SubscriptionService) Create(subscription *models.Subscription) (*models.Subscription, *e.AppError) {
	if err := models.Repository.Subscription.Create(subscription, nil); err != nil {
		return nil, e.NewError500(e.SubscriptionCreateFailed, "create: "+err.Error())
	} else {
		return subscription, nil
	}
}

func (s *SubscriptionService) Update(subscription *models.Subscription, request *dto.SubscriptionRequest) (*models.Subscription, *e.AppError) {
	if request.ID != nil {
		subscription.ID = *request.ID
	}
	if request.PlanOwnerID != "" {
		subscription.PlanOwnerID = request.PlanOwnerID
	}
	subscription.Enable = request.Enable
	subscription.IsGroup = request.IsGroup
	subscription.AutoRenew = request.AutoRenew
	if err := models.Repository.Subscription.Update(subscription, nil); err != nil {
		return nil, e.NewError500(e.SubscriptionUpdateFailed, "create: "+err.Error())
	} else {
		return subscription, nil
	}
}

func (s *SubscriptionService) FindOne(query *models.SubscriptionQuery, options *models.FindOneOptions) (*models.Subscription, *e.AppError) {
	if subscription, err := models.Repository.Subscription.FindOne(query, options); err != nil {
		return nil, e.NewError500(e.SubscriptionFindOneFailed, "FindOne: "+err.Error())
	} else {
		return subscription, nil
	}
}

func (s *SubscriptionService) FindOneOptions(query *models.SubscriptionQuery, options *models.FindOneOptions) (*models.Subscription, *e.AppError) {
	subs, err := models.Repository.Subscription.FindOne(query, options)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.SubscriptionFindOneFailed, "FindOneOptions: "+err.Error())
	}

	return subs, nil
}

func (s *SubscriptionService) FindById(id string, options *models.FindOneOptions) (*models.Subscription, *e.AppError) {
	if subscription, err := models.Repository.Subscription.FindOne(&models.SubscriptionQuery{ID: util.NewString(id)}, options); err != nil {
		return nil, e.NewError500(e.SubscriptionFindOneFailed, "FindById: "+err.Error())
	} else {
		return subscription, nil
	}
}

func (s *SubscriptionService) FindPage(query *models.SubscriptionQuery, options *models.FindPageOptions) ([]*models.Subscription, *models.Pagination, *e.AppError) {
	if subscriptions, pagination, err := models.Repository.Subscription.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.SubscriptionFindPageFailed, "FindPage: "+err.Error())
	} else {
		return subscriptions, pagination, nil
	}
}

func (s *SubscriptionService) FindMany(query *models.SubscriptionQuery, options *models.FindManyOptions) ([]*models.Subscription, *e.AppError) {
	if subscriptions, err := models.Repository.Subscription.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.SubscriptionFindManyFailed, "FindMany: "+err.Error())
	} else {
		return subscriptions, nil
	}
}

func (s *SubscriptionService) Delete(id string) *e.AppError {
	if err := models.Repository.Subscription.Delete(id, nil); err != nil {
		return e.NewError500(e.SubscriptionDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}

func getEndDateByPlan(startDate time.Time, plan *models.PricingPlan) time.Time {
	endDate := startDate.AddDate(0, 0, 7)
	switch plan.PeriodUnit {
	case models.TimePeriodDay:
		endDate = startDate.AddDate(0, 0, plan.Period)
	case models.TimePeriodWeek:
		endDate = startDate.AddDate(0, 0, plan.Period)
	case models.TimePeriodMonth:
		endDate = startDate.AddDate(0, plan.Period, 0)
	case models.TimePeriodYear:
		endDate = startDate.AddDate(plan.Period, 0, 0)
	case models.TimePeriodUnLimit:
		endDate = startDate.AddDate(100, 0, 0)
	default:
		break
	}
	return endDate
}

func (s *SubscriptionService) Subscribe(data *dto.SubscribePlanRequest) (*models.Subscription, *e.AppError) {
	startDate := time.Now()
	endDate := getEndDateByPlan(startDate, data.Plan)
	subscription := &models.Subscription{
		UserID:       data.User.ID,
		PlanID:       data.Plan.ID,
		StartDate:    int(startDate.UnixMilli()),
		EndDate:      int(endDate.UnixMilli()),
		BillingCycle: data.Plan.Cycle,
		Status:       models.SubscriptionStatusActive,
		AutoRenew:    true,
		Enable:       true,
		PlanOwnerID:  data.User.ID,
		IsGroup:      false,
		SubType:      data.SubType,
	}

	if data.Org == nil {
		data.Org = Organization.GetRoot()
	}

	sub, err := s.Create(subscription)
	if err != nil {
		return nil, err
	}

	if data.EnableEmail && data.Event != nil {
		go func() {
			var eErr error
			defer func() {
				if r := recover(); r != nil {
					eErr = fmt.Errorf("panic: %v", r)
				}

				if eErr != nil {
					log.ErrorWithAlertf("SubscriptionService.Subscribe::Send email failed: %v", eErr)
				}
			}()

			// send mail
			mailParams := communicationdto.MapEmailParams{
				communicationdto.EmailParamFullName: lo.If(data.User.DisplayName != "", data.User.DisplayName).Else(data.User.Username),
				communicationdto.EmailParamEmail:    data.User.Email,
			}
			if _, eErr := communication.Email.SendEmail(&communicationdto.SendEmailRequest{
				User:        data.User.IntoComm(),
				Org:         data.Org.IntoComm(),
				Event:       *data.Event,
				ExtendDatas: mailParams,
				IsQueue:     true,
			}); eErr != nil {
				log.ErrorWithAlertf("SubscriptionService.Subscribe::Send email failed: %v", eErr)
			}

		}()
	}

	go func() {
		// push notification
		req := &communicationdto.PushNotificationRequest{
			Code: communicationdto.CodePhoCapAIRegistrationSuccess,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{data.User.ID},
				},
			},
			Org: data.Org.IntoComm(),
		}
		if err := communication.Notification.PushNotification(req); err != nil {
			log.Errorf("Push notification after register error: %v", err)
		}
	}()

	return sub, nil
}

func (s *SubscriptionService) GetActivePlanByUser(user *models.User) (*models.PricingPlan, *e.AppError) {
	var plan models.PricingPlan
	models.Cache.Subscription.Get(user.ID, &plan)
	if plan.ID != "" {
		return &plan, nil
	}

	resourceUsageEmailConfig := models.GetConfig[models.ResourceUsageEmailConfig](models.AIResourceUsageEmailConfig)
	defaultWhitelistEmails := resourceUsageEmailConfig.WhitelistEmail
	isWhitelistUser := false
	for _, e := range defaultWhitelistEmails {
		if e == user.Email {
			isWhitelistUser = true
			break
		}
	}

	pattern := resourceUsageEmailConfig.WhitelistEmailPattern
	if pattern != "" {
		matched, _ := regexp.MatchString(pattern, user.Email)
		if matched {
			isWhitelistUser = true
		}
	}

	if isWhitelistUser {
		internalPlan, appErr := PricingPlan.GetInternalPlan()
		// update cache
		models.Cache.Subscription.Set(user.ID, internalPlan)
		return internalPlan, appErr
	}

	subscriptions, err := s.FindMany(&models.SubscriptionQuery{
		UserID: util.NewString(user.ID),
		Enable: util.NewBool(true),
		Active: util.NewBool(true),
	}, &models.FindManyOptions{Preloads: []string{models.PricingPlanField}})
	if err != nil {
		return nil, err
	}
	if len(subscriptions) > 0 {
		models.Cache.Subscription.Set(user.ID, subscriptions[0].Plan)
		return subscriptions[0].Plan, nil
	}

	freePlan, appErr := PricingPlan.GetFreePlan()
	// update cache
	models.Cache.Subscription.Set(user.ID, freePlan)
	return freePlan, appErr
}

func (s *SubscriptionService) MigrateWhitelistAISubscription(whitelistEmails []string, orgID string) *e.AppError {
	resourceUsageEmailConfig := models.GetConfig[models.ResourceUsageEmailConfig](models.AIResourceUsageEmailConfig)
	defaultWhitelistEmails := resourceUsageEmailConfig.WhitelistEmail
	defaultWhitelistUsers, aErr := User.FindMany(&models.UserQuery{
		EmailIn:        defaultWhitelistEmails,
		IncludeDeleted: util.NewBool(false),
		Active:         util.NewBool(true),
		OrgID:          &orgID,
	}, &models.FindManyOptions{})
	if aErr != nil {
		return aErr
	}
	toCreate := []*models.User{}
	toDelete := []*models.User{}
	if len(whitelistEmails) > 0 {
		whitelistUsers, aErr := User.FindMany(&models.UserQuery{
			EmailIn:        whitelistEmails,
			IncludeDeleted: util.NewBool(false),
			Active:         util.NewBool(true),
			OrgID:          &orgID,
		}, &models.FindManyOptions{})
		if aErr != nil {
			return aErr
		}

		toCreate, toDelete = util.DifferenceBy(whitelistUsers, defaultWhitelistUsers, func(u *models.User) string {
			return u.ID
		})
	} else {
		toCreate = defaultWhitelistUsers
	}

	if len(toCreate) > 0 {
		if aErr := s.handleWhitelistSubscription(toCreate, true); aErr != nil {
			return aErr
		}
	}

	if len(toDelete) > 0 {
		if aErr := s.handleWhitelistSubscription(toDelete, false); aErr != nil {
			return aErr
		}
	}
	return nil
}

func (s *SubscriptionService) handleWhitelistSubscription(users []*models.User, isWhitelist bool) *e.AppError {
	internalPlan, aErr := PricingPlan.GetInternalPlan()
	if aErr != nil {
		return aErr
	}
	freePlan, aErr := PricingPlan.GetFreePlan()
	if aErr != nil {
		return aErr
	}
	for _, user := range users {
		subscription, err := models.Repository.Subscription.FindOne(&models.SubscriptionQuery{
			UserID: util.NewString(user.ID),
			PlanID: &internalPlan.ID,
			Active: util.NewBool(true),
		}, &models.FindOneOptions{})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError500(e.SubscriptionFindOneFailed, "FindOne: "+err.Error())
		}

		if subscription == nil {
			if isWhitelist {
				if _, aErr := Subscription.Subscribe(&dto.SubscribePlanRequest{
					User:        user,
					Plan:        internalPlan,
					SubType:     models.SubscriptionAuto,
					Event:       nil,
					EnableEmail: false,
				}); aErr != nil {
					return aErr
				}
			} else {
				if _, aErr := Subscription.Subscribe(&dto.SubscribePlanRequest{
					User:        user,
					Plan:        freePlan,
					SubType:     models.SubscriptionAuto,
					Event:       nil,
					EnableEmail: false,
				}); aErr != nil {
					return aErr
				}
			}
		} else {
			if _, aErr := Subscription.Update(subscription, &dto.SubscriptionRequest{
				AutoRenew: subscription.AutoRenew,
				Enable:    isWhitelist,
				IsGroup:   subscription.IsGroup,
			}); aErr != nil {
				return aErr
			}
		}
	}
	return nil
}
func (s *SubscriptionService) GetCurrentSubscriptionInfo(user *models.User, orgID string) (*dto.GetSubscriptionInfo, *e.AppError) {
	subscriptions, err := s.FindMany(&models.SubscriptionQuery{
		UserID: util.NewString(user.ID),
		Enable: util.NewBool(true),
		Active: util.NewBool(true),
	}, &models.FindManyOptions{Preloads: []string{models.PricingPlanField}})
	if err != nil {
		return nil, err
	}

	var currentSubscription *models.Subscription
	if len(subscriptions) > 0 {
		currentSubscription = subscriptions[0]
	} else {
		return nil, e.NewError404(e.SubscriptionFindOneFailed, "User haven't subscribe")
	}

	activePlan, err := s.GetActivePlanByUser(user)
	if err != nil {
		return nil, err
	}

	currentSubscription.Plan = activePlan

	usages, err := ResourceUsage.GetAiUsage(user, activePlan, orgID)
	if err != nil {
		return nil, err
	}

	return &dto.GetSubscriptionInfo{
		Subscription:   currentSubscription.Santinize(),
		ResourceUsages: usages.AiUsages,
	}, nil
}

func (s *SubscriptionService) RemindExpirationByPeriod(period int) *e.AppError {
	batchSize := 100

	for {
		subs, err := models.Repository.Subscription.GetExpirationByPeriod(batchSize, batchSize)
		if err != nil {
			return e.NewError500(e.SubscriptionFindManyFailed, "GetExpirationByPeriod: "+err.Error())
		}

		if len(subs) == 0 {
			return nil
		}

		notiReqs := lo.Map(subs, func(sub *models.Subscription, _ int) *communicationdto.PushNotificationRequest {
			return &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeSubscriptionExpiration,
				EntityID:   sub.PlanID,
				EntityType: communicationdto.SubscriptionEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{sub.UserID},
					},
				},
				Props: communicationdto.JSONB{
					"subscription_id": sub.ID,
					"expiration_date": sub.EndDate,
				},
			}
		})

		if err := communication.Notification.PushMultipleNotification(notiReqs); err != nil {
			if err != nil {
				fmt.Errorf(e.GetMsg(communicationdto.CodeSubscriptionExpiration), err)
			}
		}
	}
}

func (s *SubscriptionService) RemindExpiration() *e.AppError {
	batchSize := 100

	for {
		subs, err := models.Repository.Subscription.GetExpiration(batchSize)
		if err != nil {
			return e.NewError500(e.SubscriptionFindManyFailed, "GetExpirationByPeriod: "+err.Error())
		}

		if len(subs) == 0 {
			return nil
		}

		notiReqs := lo.Map(subs, func(sub *models.Subscription, _ int) *communicationdto.PushNotificationRequest {
			return &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeSubscriptionExpiration,
				EntityID:   sub.PlanID,
				EntityType: communicationdto.SubscriptionEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{sub.UserID},
					},
				},
				Props: communicationdto.JSONB{
					"subscription_id": sub.ID,
					"expiration_date": sub.EndDate,
				},
			}
		})

		if err := communication.Notification.PushMultipleNotification(notiReqs); err != nil {
			if err != nil {
				fmt.Errorf(e.GetMsg(communicationdto.CodeSubscriptionExpiration), err)
			}
		}
	}
}

func (s *SubscriptionService) ExpiredSucriptions() *e.AppError {
	needExpiredSubscriptions, appErr := s.FindMany(&models.SubscriptionQuery{
		Active: util.NewBool(false),
		Enable: util.NewBool(true),
		Status: util.NewT(models.SubscriptionStatusActive),
	}, nil)
	if appErr != nil {
		return appErr
	}

	if len(needExpiredSubscriptions) > 0 {
		err := models.Repository.Subscription.ExpiredManySubscriptions(needExpiredSubscriptions, nil)
		if err != nil {
			return e.NewError500(e.SubscriptionUpdateFailed, "Update many subscripton expired failed: "+err.Error())
		}
	}

	return nil
}

func (s *SubscriptionService) HandleAutoSubscribeAIPlan(org *models.Organization, user *models.User) *e.AppError {
	enableSubscribeAI := models.GetConfig[bool](models.AISubscribePlanForOrgEnable)
	if enableSubscribeAI {
		config := models.GetConfig[models.SubscribeAIPlanInfo](models.AISubscribePlanForOrgConfig)
		if org.Schema == config.OrgSchema {
			planActived, appErr := s.GetActivePlanByUser(user)
			if appErr != nil {
				return appErr
			}

			if planActived.Tier == models.FreePlanTier {
				planUpgrade, appErr := PricingPlan.FindById(config.PlanID, nil)
				if appErr != nil {
					return appErr
				}

				eventType := communicationdto.EventType(config.Event)
				_, appErr = s.Subscribe(&dto.SubscribePlanRequest{
					User:        user,
					Plan:        planUpgrade,
					SubType:     models.SubscriptionAuto,
					Event:       &eventType,
					EnableEmail: config.EmailEnable,
					Org:         org,
				})
				if appErr != nil {
					return appErr
				}
			}

		}
	}

	return nil
}
