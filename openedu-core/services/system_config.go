package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/upload"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

const (
	BackupConfigTimeZone   = "Asia/Ho_Chi_Minh"
	BackupConfigTimeLayout = "20060102_150405"
)

func (s *SystemConfigService) Create(user *models.User, req *dto.SystemConfigRequest) (*models.SystemConfig, *e.AppError) {
	if req.OrgID == "" && req.Domain != "" {
		org, err := models.Repository.Organization.FindByDomain(req.Domain)
		if err != nil {
			return nil, e.NewError500(e.Create_system_config_failed, "Get org by domain '"+req.Domain+"' error: "+err.Error())
		}

		req.OrgID = org.ID
		req.AltDomain = org.AltDomain
	}
	// Check system config already existing
	existing, err := models.Repository.System.FindOne(
		&models.SystemConfigQuery{
			Key:    &req.Key,
			Locale: &req.Locale,
			OrgID:  &req.OrgID,
		},
		&models.FindOneOptions{
			Preloads: []string{models.FilesField},
		},
	)
	if err != nil && !models.IsRecordNotFound(err) {
		return nil, e.NewError500(e.Create_system_config_failed, "Check existing system config error: "+err.Error())
	}

	if existing != nil {
		return s.Update(user, existing, req, req.OrgID)
	}

	sysConfig := models.SystemConfig{
		Key:       req.Key,
		Locale:    req.Locale,
		DataType:  req.DataType,
		OrgID:     req.OrgID,
		Domain:    req.Domain,
		AltDomain: req.AltDomain,
	}

	// Check if value should be stored in a file
	if req.IsStorageInFile {
		f := &upload.File{
			Name:    s.buildUploadFile(req),
			Mime:    s.getFileMime(req),
			Content: []byte(req.Value),
			Public:  true,
		}
		files, appErr := Upload.UploadFiles([]*upload.File{f}, user.ID, models.ConfigPathPrefix)
		if appErr != nil {
			return nil, e.NewError500(e.Create_system_config_failed, "Upload system config file error: "+appErr.Error())
		}

		sysConfig.Files = files
	} else {
		sysConfig.Value = req.Value
	}

	if err = models.Repository.System.Create(&sysConfig, nil); err != nil {
		return nil, e.NewError500(e.Create_system_config_failed, "Create system config failed: "+err.Error())
	}
	return &sysConfig, nil
}

func (s SystemConfigService) buildUploadFile(req *dto.SystemConfigRequest) string {
	fileExt := ".txt"
	switch req.DataType {
	case models.JsonB, models.JsonArr:
		fileExt = ".json"
	}
	now := time.Now()
	formatted := now.Format("2006_01_02_15_04_05")
	return req.OrgID + "/" + req.Key + "/" + req.Locale + "_" + formatted + fileExt
}

func (s *SystemConfigService) getCurrentFileName(req *dto.SystemConfigRequest) string {
	fileExt := ".txt"
	switch req.DataType {
	case models.JsonB, models.JsonArr:
		fileExt = ".json"
	}
	return models.CurrentPrefix + req.OrgID + "_" + req.Key + "_" + req.Locale + fileExt
}

func (s *SystemConfigService) getBackupFileName(req *dto.SystemConfigRequest) string {
	fileExt := ".txt"
	switch req.DataType {
	case models.JsonB, models.JsonArr:
		fileExt = ".json"
	}
	return models.BackupPrefix + req.OrgID + "_" + req.Key + "_" + req.Locale + fileExt
}

func (s *SystemConfigService) getFileMime(req *dto.SystemConfigRequest) string {
	mime := models.MIMETypeTextPlain
	switch req.DataType {
	case models.JsonB, models.JsonArr:
		mime = models.MIMETypeApplicationJson
	}
	return mime
}

func (s *SystemConfigService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.SystemConfig, *e.AppError) {
	query := &models.SystemConfigQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}

	if sysConfig, err := models.Repository.System.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.System_config_not_found, err.Error())
		}
		return nil, e.NewError500(e.System_config_find_one_failed, err.Error())
	} else {
		return sysConfig, nil
	}
}

func (s *SystemConfigService) FindOne(query *models.SystemConfigQuery, options *models.FindOneOptions) (*models.SystemConfig, *e.AppError) {
	if sysConfig, err := models.Repository.System.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.System_config_not_found, err.Error())
		}
		return nil, e.NewError500(e.System_config_find_one_failed, err.Error())
	} else {
		return sysConfig, nil
	}
}

func (s *SystemConfigService) FindPage(query *models.SystemConfigQuery, options *models.FindPageOptions) ([]*models.SystemConfig, *models.Pagination, *e.AppError) {
	if systemConfigs, pagination, err := models.Repository.System.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_system_config_failed, err.Error())
	} else {
		return systemConfigs, pagination, nil
	}
}

func (s *SystemConfigService) FindAll(keys []*string, orgIDs []*string, domains []*string, locales []*string) ([]*models.SimpleConfig, *e.AppError) {
	if systemConfigs, err := models.Repository.System.FindAll(); err != nil {
		return nil, e.NewError500(e.Find_page_system_config_failed, err.Error())
	} else {
		configs := systemConfigs
		if keys != nil && len(keys) > 0 {
			configs = lo.Filter(configs, func(item *models.SystemConfig, _ int) bool {
				return lo.ContainsBy(keys, func(k *string) bool {
					return *k == item.Key
				})
			})
		}

		if orgIDs != nil && len(orgIDs) > 0 {
			configs = lo.Filter(configs, func(item *models.SystemConfig, _ int) bool {
				return lo.ContainsBy(orgIDs, func(k *string) bool {
					return *k == item.OrgID
				})
			})
		}

		if domains != nil && len(domains) > 0 {
			configs = lo.Filter(configs, func(item *models.SystemConfig, _ int) bool {
				return lo.ContainsBy(domains, func(k *string) bool {
					return *k != "" && (*k == item.Domain || *k == item.AltDomain)
				})
			})
		}

		if locales != nil && len(locales) > 0 {
			configs = lo.Filter(configs, func(item *models.SystemConfig, _ int) bool {
				return lo.ContainsBy(locales, func(k *string) bool {
					return *k != "" && *k == item.Locale
				})
			})
		}

		var results []*models.SimpleConfig
		lo.ForEach(configs, func(item *models.SystemConfig, _ int) {
			switch item.DataType {
			case models.JsonB:
				var j *models.JSONB
				json.Unmarshal([]byte(item.Value), &j)
				results = append(results, &models.SimpleConfig{
					Key:      item.Key,
					Locale:   item.Locale,
					Value:    j,
					OrgID:    item.OrgID,
					Domain:   item.Domain,
					Model:    item.Model,
					DataType: item.DataType,
					Files: lo.Map(item.Files, func(file *models.File, _ int) *models.File {
						return &models.File{
							Model:          file.Model,
							UserID:         file.UserID,
							Name:           fmt.Sprintf("%s?update_at=%d", file.Name, item.UpdateAt),
							Mime:           file.Mime,
							Ext:            file.Ext,
							URL:            "",
							ThumbnailURL:   "",
							Width:          file.Width,
							Height:         file.Height,
							Size:           file.Size,
							BunnyVideoID:   file.BunnyVideoID,
							BunnyLibraryID: file.BunnyLibraryID,
							Duration:       file.Duration,
							Props:          file.Props,
						}
					}),
				})
			case models.JsonArr:
				var j *models.JSONArray
				json.Unmarshal([]byte(item.Value), &j)
				results = append(results, &models.SimpleConfig{
					Key:      item.Key,
					Locale:   item.Locale,
					Value:    j,
					OrgID:    item.OrgID,
					Domain:   item.Domain,
					Model:    item.Model,
					DataType: item.DataType,
					Files: lo.Map(item.Files, func(file *models.File, _ int) *models.File {
						return &models.File{
							Model:          file.Model,
							UserID:         file.UserID,
							Name:           fmt.Sprintf("%s?update_at=%d", file.Name, item.UpdateAt),
							Mime:           file.Mime,
							Ext:            file.Ext,
							URL:            "",
							ThumbnailURL:   "",
							Width:          file.Width,
							Height:         file.Height,
							Size:           file.Size,
							BunnyVideoID:   file.BunnyVideoID,
							BunnyLibraryID: file.BunnyLibraryID,
							Duration:       file.Duration,
							Props:          file.Props,
						}
					}),
				})
			case models.Int:
				var j int64
				json.Unmarshal([]byte(item.Value), &j)
				results = append(results, &models.SimpleConfig{
					Key:      item.Key,
					Value:    j,
					OrgID:    item.OrgID,
					Model:    item.Model,
					DataType: item.DataType,
				})
			case models.TimeDuration:
				var j time.Duration
				json.Unmarshal([]byte(item.Value), &j)
				results = append(results, &models.SimpleConfig{
					Key:      item.Key,
					Value:    j,
					OrgID:    item.OrgID,
					Model:    item.Model,
					DataType: item.DataType,
				})
			default:
				results = append(results, &models.SimpleConfig{
					Key:      item.Key,
					Locale:   item.Locale,
					Value:    item.Value,
					OrgID:    item.OrgID,
					Domain:   item.Domain,
					Model:    item.Model,
					DataType: item.DataType,
					Files: lo.Map(item.Files, func(file *models.File, _ int) *models.File {
						return &models.File{
							Model:          file.Model,
							UserID:         file.UserID,
							Name:           fmt.Sprintf("%s?update_at=%d", file.Name, item.UpdateAt),
							Mime:           file.Mime,
							Ext:            file.Ext,
							URL:            "",
							ThumbnailURL:   "",
							Width:          file.Width,
							Height:         file.Height,
							Size:           file.Size,
							BunnyVideoID:   file.BunnyVideoID,
							BunnyLibraryID: file.BunnyLibraryID,
							Duration:       file.Duration,
							Props:          file.Props,
						}
					}),
				})
			}
		})

		return results, nil
	}
}

func (s *SystemConfigService) Update(user *models.User, sysConfig *models.SystemConfig, req *dto.SystemConfigRequest, curOrgID string) (*models.SystemConfig, *e.AppError) {
	// TODO: remove after find better solution for avoid conflict
	if req.Key == string(models.AIResourceUsageEmailConfig) {
		var config models.ResourceUsageEmailConfig
		err := json.Unmarshal([]byte(req.Value), &config)
		if err != nil {
			return nil, e.NewError500(e.Json_decode_error, err.Error())
		}

		if aErr := Subscription.MigrateWhitelistAISubscription(config.WhitelistEmail, curOrgID); aErr != nil {
			return nil, aErr
		}
	}

	if req.Key == string(models.AIModelsConfig) {
		if aErr := AIModel.MigrateAIModel(); aErr != nil {
			return nil, aErr
		}
	}

	if req.OrgID == "" && req.Domain != "" {
		org, err := models.Repository.Organization.FindByDomain(req.Domain)
		if err != nil {
			return nil, e.NewError500(e.Update_system_config_failed, "Get org by domain '"+req.Domain+"' error: "+err.Error())
		}

		req.OrgID = org.ID
	}

	// Check if value should be stored in a file
	if req.IsStorageInFile {
		var sysConfigFiles []*models.File

		//if backupFile, appErr := s.backupBeforeUpdate(user, sysConfig); appErr != nil {
		//	return nil, appErr
		//} else if backupFile != nil {
		//	sysConfigFiles = append(sysConfigFiles, backupFile)
		//}

		files, appErr := Upload.UploadFiles(
			[]*upload.File{{
				Name:    s.buildUploadFile(req),
				Mime:    s.getFileMime(req),
				Content: []byte(req.Value),
				Public:  true,
			}},
			user.ID,
			models.ConfigPathPrefix,
		)
		if appErr != nil {
			return nil, e.NewError500(e.Create_system_config_failed, "Upload system config file error: "+appErr.Error())
		}
		currentFile, found := sysConfig.GetCurrentFile()
		if found {
			currentFile.Name = files[0].Name
			currentFile.Mime = files[0].Mime
			currentFile.Ext = files[0].Ext
			currentFile.Width = files[0].Width
			currentFile.Height = files[0].Height
			currentFile.Size = files[0].Size
			currentFile.URL = files[0].URL
			currentFile.ThumbnailURL = files[0].ThumbnailURL
			currentFile.BunnyVideoID = files[0].BunnyVideoID
			currentFile.BunnyLibraryID = files[0].BunnyLibraryID
			currentFile.Duration = files[0].Duration
			currentFile.UserID = user.ID
			if uErr := models.Repository.File.Update(currentFile, nil); uErr != nil {
				return nil, e.NewError500(e.Create_system_config_failed, "Update system config current file error: "+uErr.Error())
			}
		} else {
			currentFile = &models.File{
				Name:           files[0].Name,
				Mime:           files[0].Mime,
				Ext:            files[0].Ext,
				Width:          files[0].Width,
				Height:         files[0].Height,
				Size:           files[0].Size,
				URL:            files[0].URL,
				ThumbnailURL:   files[0].ThumbnailURL,
				BunnyVideoID:   files[0].BunnyVideoID,
				BunnyLibraryID: files[0].BunnyLibraryID,
				Duration:       files[0].Duration,
				UserID:         user.ID,
			}
			if cErr := models.Repository.File.Create(currentFile, nil); cErr != nil {
				return nil, e.NewError500(e.Create_system_config_failed, "Create system config current file error: "+cErr.Error())
			}
		}

		sysConfig.Files = append(sysConfigFiles, currentFile)
		sysConfig.Value = ""
	} else {
		sysConfig.Files = nil
		sysConfig.Value = req.Value
	}

	sysConfig.Key = req.Key
	sysConfig.Locale = req.Locale
	sysConfig.DataType = req.DataType
	sysConfig.Domain = req.Domain
	if err := models.Repository.System.Update(sysConfig, nil); err != nil {
		return nil, e.NewError500(e.Update_system_config_failed, "Update system config error: "+err.Error())
	}
	return sysConfig, nil
}

func (s *SystemConfigService) backupBeforeUpdate(user *models.User, sysConfig *models.SystemConfig) (*models.File, *e.AppError) {
	currentFile, found := sysConfig.GetCurrentFile()
	if !found {
		return nil, nil
	}

	data, err := Upload.GetFileData(currentFile)
	if err != nil {
		return nil, e.NewError500(e.Create_system_config_failed, "Get current file data error: "+err.Error())
	}

	fileInfos, appErr := upload.DefaultProvider.UploadFiles(
		[]*upload.File{
			{
				Name:    strings.Replace(currentFile.Name, models.CurrentPrefix, models.BackupPrefix, 1),
				Mime:    currentFile.Mime,
				Content: data,
				Public:  true,
			},
		},
		models.ConfigPathPrefix,
	)
	if appErr != nil {
		return nil, e.NewError500(e.Create_system_config_failed, "Upload system config backup file error: "+appErr.Error())
	}

	backupFile, _ := sysConfig.GetBackupFile()
	if backupFile != nil {
		backupFile.Name = fileInfos[0].Name
		backupFile.Mime = fileInfos[0].Mime
		backupFile.Ext = fileInfos[0].Ext
		backupFile.Width = fileInfos[0].Width
		backupFile.Height = fileInfos[0].Height
		backupFile.Size = fileInfos[0].Size
		backupFile.URL = fileInfos[0].URL
		backupFile.ThumbnailURL = fileInfos[0].ThumbnailURL
		backupFile.BunnyVideoID = fileInfos[0].BunnyVideoID
		backupFile.BunnyLibraryID = fileInfos[0].BunnyLibraryID
		backupFile.Duration = fileInfos[0].Duration
		backupFile.UserID = user.ID
		if uErr := models.Repository.File.Update(backupFile, nil); uErr != nil {
			return nil, e.NewError500(e.Create_system_config_failed, "Update system config backup file error: "+uErr.Error())
		}
	} else {
		backupFile = &models.File{
			Name:           fileInfos[0].Name,
			Mime:           fileInfos[0].Mime,
			Ext:            fileInfos[0].Ext,
			Width:          fileInfos[0].Width,
			Height:         fileInfos[0].Height,
			Size:           fileInfos[0].Size,
			URL:            fileInfos[0].URL,
			ThumbnailURL:   fileInfos[0].ThumbnailURL,
			BunnyVideoID:   fileInfos[0].BunnyVideoID,
			BunnyLibraryID: fileInfos[0].BunnyLibraryID,
			Duration:       fileInfos[0].Duration,
			UserID:         user.ID,
		}
		if cErr := models.Repository.File.Create(backupFile, nil); cErr != nil {
			return nil, e.NewError500(e.Create_system_config_failed, "Create system config backup file error: "+cErr.Error())
		}
	}
	return backupFile, nil
}

//func (s *SystemConfigService) backupBeforeUpdate(cfg *models.SystemConfig) error {
//	folder := filepath.Join(setting.AppSetting.RuntimeRootPath, "backups")
//	if err := os.MkdirAll(folder, os.ModePerm); err != nil {
//		return err
//	}
//
//	fileName, err := s.buildBackupFileName(cfg)
//	if err != nil {
//		return err
//	}
//
//	filePath := filepath.Join(folder, fileName)
//	if err := os.WriteFile(filePath, []byte(cfg.Value), 0644); err != nil {
//		return err
//	}
//
//	return nil
//}

func (s *SystemConfigService) buildBackupFileName(cfg *models.SystemConfig) (string, error) {
	location, err := time.LoadLocation(BackupConfigTimeZone)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%s__%s__%s.txt", cfg.OrgID, cfg.Key, time.Now().In(location).Format(BackupConfigTimeLayout)), nil
}

func (s *SystemConfigService) Delete(sys *models.SystemConfig) *e.AppError {
	if err := models.Repository.System.Delete(sys.ID, nil); err != nil {
		return e.NewError500(e.Delete_system_config_failed, err.Error())
	}
	return nil
}

func (s *SystemConfigService) CanUpdateSystemConfig(c *models.SystemConfig, user *models.User) bool {
	return user.IsOrgAdmin(c.OrgID) || user.IsSysAdmin()
}

func (s *SystemConfigService) FindOneOptions(query *models.SystemConfigQuery, options *models.FindOneOptions) (*models.SystemConfig, *e.AppError) {
	sysConfig, err := models.Repository.System.FindOne(query, options)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.System_config_find_one_failed, err.Error())
	}

	return sysConfig, nil
}
