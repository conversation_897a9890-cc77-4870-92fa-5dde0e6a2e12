package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/openedu_chain"
	chaindto "openedu-core/pkg/openedu_chain/dto"
)

func (s *ChainService) GetAccountInfo(req *dto.GetAccountInfoRequest) (*dto.GetAccountInfoResponse, *e.AppError) {
	switch req.Network {
	case models.BlockchainNetworkAVAIL:
	default:
		return nil, e.NewError400(e.WalletUnsupportedGetAccountInfo,
			"Unsupported get account info for network "+string(req.Network))
	}

	resp, err := openedu_chain.Wallet.GetAccountInfo(&chaindto.GetAccountInfoRequest{
		Network:   chaindto.BlockchainNetwork(req.Network), // TODO
		Address:   req.Address,
		IsMainnet: req.IsMainnet,
	})
	if err != nil {
		if errors.Is(err, openedu_chain.ErrInvalidAddress) {
			return nil, e.NewError400(e.INVALID_PARAMS, "Get account info failed: "+err.Error())
		}
		return nil, e.NewError500(e.WalletGetAccountInfoFailed, "Get account info failed: "+err.Error())
	}

	return &dto.GetAccountInfoResponse{
		Network:     models.BlockchainNetwork(resp.Network), // TODO
		Address:     resp.Address,
		AccountInfo: resp.AccountInfo,
	}, nil
}
