package services

import (
	"context"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/helpers"
	chaindto "openedu-core/pkg/openedu_chain/dto"

	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func initWallet(req *dto.InitWalletRequest) *models.Wallet {
	initialBalance := decimal.NewFromFloat(00.00)
	return &models.Wallet{
		UserID:           req.UserID,
		Balance:          initialBalance,
		AvailableBalance: initialBalance,
		Type:             req.Type,
		Currency:         req.Currency,
		Default:          req.IsDefault,
		Network:          req.Network,
	}
}

func initWalletAvail(userID string, wallets []*models.Wallet) ([]*models.Wallet, *e.AppError) {
	var ws []*models.Wallet
	_, ok := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == models.AssetTypeCrypto && item.Currency == models.CryptoCurrencyAVAIL
	})

	if !ok {
		w := initWallet(&dto.InitWalletRequest{
			UserID:            userID,
			Type:              models.AssetTypeCrypto,
			Currency:          models.CryptoCurrencyAVAIL,
			Network:           models.BlockchainNetworkAVAIL,
			IsDefault:         false,
			IsEarningOffChain: false,
		})
		if w.ID == "" {
			w.ID = util.GenerateId()
		}

		resp, err := openedu_chain.Wallet.Create(&chaindto.CreateWalletRequest{
			UserID:       userID,
			Network:      chaindto.BlockchainNetworkAVAIL,
			CoreWalletID: w.ID,
		})
		if err != nil {
			return nil, e.NewError500(e.WalletInitFailed, "Init AVAIL wallet error: "+err.Error())
		}

		w.Address = resp.Address
		w.PublicKey = resp.PublicKey
		w.BlockchainWalletID = resp.ID
		if err = models.Repository.Wallet.Create(w, nil); err != nil {
			return nil, e.NewError500(e.WalletInitFailed, "Init AVAIL wallet error: "+err.Error())
		}

		ws = append(ws, w)
	}

	return ws, nil
}

func initDefaultWallets(userID string, wallets []*models.Wallet) ([]*models.Wallet, *e.AppError) {
	var ws []*models.Wallet
	// Init VND fiat wallet
	_, fOk := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == models.AssetTypeFiat && item.Currency == models.FiatCurrencyVND
	})

	if !fOk {
		w := initWallet(&dto.InitWalletRequest{
			UserID:            userID,
			Type:              models.AssetTypeFiat,
			Currency:          models.FiatCurrencyVND,
			IsDefault:         true,
			IsEarningOffChain: false,
		})
		fErr := models.Repository.Wallet.Create(w, nil)
		if fErr != nil {
			return nil, e.NewError500(e.WalletInitFailed, "init fiat wallet failed: "+fErr.Error())
		}
		ws = append(ws, w)
	}

	// Init USD fiat wallet
	_, fOk = lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == models.AssetTypeFiat && item.Currency == models.FiatCurrencyUSD
	})

	if !fOk {
		w := initWallet(&dto.InitWalletRequest{
			UserID:            userID,
			Type:              models.AssetTypeFiat,
			Currency:          models.FiatCurrencyUSD,
			IsDefault:         true,
			IsEarningOffChain: false,
		})
		fErr := models.Repository.Wallet.Create(w, nil)
		if fErr != nil {
			return nil, e.NewError500(e.WalletInitFailed, "init fiat wallet failed: "+fErr.Error())
		}
		ws = append(ws, w)
	}

	// init NEAR wallet
	nearWallet, cOk := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == models.AssetTypeCrypto && item.Currency == models.CryptoCurrencyNEAR
	})

	if !cOk {
		nearWallet = initWallet(&dto.InitWalletRequest{
			UserID:            userID,
			Type:              models.AssetTypeCrypto,
			Currency:          models.CryptoCurrencyNEAR,
			Network:           models.BlockchainNetworkNEAR,
			IsDefault:         true,
			IsEarningOffChain: false,
		})
		if nearWallet.ID == "" {
			nearWallet.ID = util.GenerateId()
		}

		resp, err := openedu_chain.Wallet.Create(&chaindto.CreateWalletRequest{
			UserID:       userID,
			Network:      chaindto.BlockchainNetworkNEAR,
			CoreWalletID: nearWallet.ID,
		})
		if err != nil {
			return nil, e.NewError500(e.WalletInitFailed, "Init NEAR wallet error: "+err.Error())
		}

		var subWallets []*models.Wallet
		for _, currency := range models.NEARCryptoCurrencies {
			subWallet := initWallet(&dto.InitWalletRequest{
				UserID:            userID,
				Type:              models.AssetTypeCrypto,
				Currency:          currency,
				Network:           models.BlockchainNetworkNEAR,
				IsDefault:         false,
				IsEarningOffChain: false,
			})

			subWallet.Address = resp.Address
			subWallet.PublicKey = resp.PublicKey
			subWallet.BlockchainWalletID = resp.ID
			subWallet.ParentID = nearWallet.ID

			subWallets = append(subWallets, subWallet)
		}

		nearWallet.Address = resp.Address
		nearWallet.PublicKey = resp.PublicKey
		nearWallet.BlockchainWalletID = resp.ID
		if err = models.Repository.Wallet.CreateMany(append(subWallets, nearWallet), nil); err != nil {
			return nil, e.NewError500(e.WalletInitFailed, "Init NEAR wallet error: "+err.Error())
		}

		ws = append(ws, nearWallet)
		ws = append(ws, subWallets...)

	} else {
		if nearWallet.BlockchainWalletID == "" {
			resp, err := openedu_chain.Wallet.Create(&chaindto.CreateWalletRequest{
				UserID:       userID,
				Network:      chaindto.BlockchainNetworkNEAR,
				CoreWalletID: nearWallet.ID,
			})
			if err != nil {
				return nil, e.NewError500(e.WalletInitFailed, "Init NEAR wallet error: "+err.Error())
			}

			nearWallet.Address = resp.Address
			nearWallet.PublicKey = resp.PublicKey
			nearWallet.BlockchainWalletID = resp.ID
			if err = models.Repository.Wallet.Update(nearWallet, nil); err != nil {
				return nil, e.NewError500(e.WalletInitFailed, "Update NEAR wallet error: "+err.Error())
			}
		}

		var subWallets []*models.Wallet
		for _, currency := range models.NEARCryptoCurrencies {
			_, found := lo.Find(wallets, func(item *models.Wallet) bool {
				return item.Type == models.AssetTypeCrypto &&
					item.Network == models.BlockchainNetworkNEAR &&
					item.Currency == currency
			})
			if !found {
				subWallet := initWallet(&dto.InitWalletRequest{
					UserID:            userID,
					Type:              models.AssetTypeCrypto,
					Currency:          currency,
					Network:           models.BlockchainNetworkNEAR,
					IsDefault:         false,
					IsEarningOffChain: false,
				})

				subWallet.Address = nearWallet.Address
				subWallet.PublicKey = nearWallet.PublicKey
				subWallet.BlockchainWalletID = nearWallet.BlockchainWalletID
				subWallet.ParentID = nearWallet.ID

				subWallets = append(subWallets, subWallet)
			}
		}

		if len(subWallets) > 0 {
			if err := models.Repository.Wallet.CreateMany(subWallets, nil); err != nil {
				return nil, e.NewError500(e.WalletInitFailed, "Init NEAR wallet error: "+err.Error())
			}
		}

	}

	// init ETH wallet
	ethWallet, eOk := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == models.AssetTypeCrypto && item.Currency == models.CryptoCurrencyETH
	})

	if !eOk {
		ethWallet = initWallet(&dto.InitWalletRequest{
			UserID:            userID,
			Type:              models.AssetTypeCrypto,
			Currency:          models.CryptoCurrencyETH,
			Network:           models.BlockchainNetworkBASE,
			IsDefault:         true,
			IsEarningOffChain: false,
		})
		if ethWallet.ID == "" {
			ethWallet.ID = util.GenerateId()
		}

		resp, err := openedu_chain.Wallet.Create(&chaindto.CreateWalletRequest{
			UserID:       userID,
			Network:      chaindto.BlockchainNetworkBASE,
			CoreWalletID: ethWallet.ID,
		})
		if err != nil {
			return nil, e.NewError500(e.WalletInitFailed, "Init ETH wallet error: "+err.Error())
		}

		var subWallets []*models.Wallet
		for _, currency := range models.EVMCryptoCurrencies {
			subWallet := initWallet(&dto.InitWalletRequest{
				UserID:            userID,
				Type:              models.AssetTypeCrypto,
				Currency:          currency,
				Network:           models.BlockchainNetworkBASE,
				IsDefault:         false,
				IsEarningOffChain: true,
			})

			subWallet.Address = resp.Address
			subWallet.PublicKey = resp.PublicKey
			subWallet.BlockchainWalletID = resp.ID
			subWallet.ParentID = ethWallet.ID

			subWallets = append(subWallets, subWallet)
		}

		ethWallet.Address = resp.Address
		ethWallet.PublicKey = resp.PublicKey
		ethWallet.BlockchainWalletID = resp.ID
		if err = models.Repository.Wallet.CreateMany(append(subWallets, ethWallet), nil); err != nil {
			return nil, e.NewError500(e.WalletInitFailed, "Init EVM wallet error: "+err.Error())
		}

		ws = append(ws, ethWallet)
		ws = append(ws, subWallets...)

	} else {
		if ethWallet.BlockchainWalletID == "" {
			resp, err := openedu_chain.Wallet.Create(&chaindto.CreateWalletRequest{
				UserID:       userID,
				Network:      chaindto.BlockchainNetworkBASE,
				CoreWalletID: ethWallet.ID,
			})
			if err != nil {
				return nil, e.NewError500(e.WalletInitFailed, "Init NEAR wallet error: "+err.Error())
			}

			ethWallet.Address = resp.Address
			ethWallet.PublicKey = resp.PublicKey
			ethWallet.BlockchainWalletID = resp.ID
			if err = models.Repository.Wallet.Update(ethWallet, nil); err != nil {
				return nil, e.NewError500(e.WalletInitFailed, "Update NEAR wallet error: "+err.Error())
			}
		}

		var subWallets []*models.Wallet
		for _, currency := range models.EVMCryptoCurrencies {
			_, found := lo.Find(wallets, func(item *models.Wallet) bool {
				return item.Type == models.AssetTypeCrypto &&
					item.Network == models.BlockchainNetworkBASE &&
					item.Currency == currency
			})
			if !found {
				subWallet := initWallet(&dto.InitWalletRequest{
					UserID:            userID,
					Type:              models.AssetTypeCrypto,
					Currency:          currency,
					Network:           models.BlockchainNetworkBASE,
					IsDefault:         false,
					IsEarningOffChain: false,
				})

				subWallet.Address = ethWallet.Address
				subWallet.PublicKey = ethWallet.PublicKey
				subWallet.BlockchainWalletID = ethWallet.BlockchainWalletID
				subWallet.ParentID = ethWallet.ID

				subWallets = append(subWallets, subWallet)
			}
		}

		if len(subWallets) > 0 {
			if err := models.Repository.Wallet.CreateMany(subWallets, nil); err != nil {
				return nil, e.NewError500(e.WalletInitFailed, "Init NEAR wallet error: "+err.Error())
			}
		}

	}

	// Init point wallet
	_, pOk := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == models.AssetTypePoint
	})

	if !pOk {
		w := initWallet(&dto.InitWalletRequest{
			UserID:    userID,
			Type:      models.AssetTypePoint,
			Currency:  models.DefaultSystemCurrency,
			IsDefault: true,
		})
		fErr := models.Repository.Wallet.Create(w, nil)
		if fErr != nil {
			return nil, e.NewError500(e.WalletInitFailed, "init point wallet failed: "+fErr.Error())
		}
		ws = append(ws, w)
	}

	return ws, nil
}

func isUserEnoughWallets(wallets []*models.Wallet) bool {
	if len(wallets) < 3 {
		return false
	}

	defaultWalletTypes := []models.AssetType{models.AssetTypeCrypto, models.AssetTypeFiat, models.AssetTypePoint}
	for _, t := range defaultWalletTypes {
		_, ok := lo.Find(wallets, func(item *models.Wallet) bool {
			return item.Type == t
		})
		if !ok {
			return false
		}
	}
	return true
}

func (s *WalletService) InitUserWallets(org *models.Organization, user *models.User) *e.AppError {
	wallets, err := models.Repository.Wallet.FindMany(
		&models.WalletQuery{
			UserID: util.NewString(user.ID),
		}, nil)
	if err != nil {
		return e.NewError500(e.WalletInitFailed, "get user wallets failed: "+err.Error())
	}

	if _, iErr := initDefaultWallets(user.ID, wallets); iErr != nil {
		return iErr
	}

	if org != nil && org.IsAvail() {
		if _, iErr := initWalletAvail(user.ID, wallets); iErr != nil {
			return iErr
		}
	}
	return nil
}

func (s *WalletService) GetUserWalletByType(user *models.User, walletType models.AssetType) (*models.Wallet, *e.AppError) {
	wallets, err := s.GetWalletsByUserID(user.ID)
	if err != nil {
		return nil, err
	}
	wallet, ok := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == walletType
	})
	if !ok {
		return nil, e.NewError400(e.WalletFindFailed, fmt.Sprintf("find wallet by user %s and type %s failed", user.ID, walletType))
	}
	return wallet, nil
}

func (s *WalletService) GetWalletsByUserID(userID string) ([]*models.Wallet, *e.AppError) {
	var wallets []*models.Wallet
	var walletCache []interface{}
	if val := models.Cache.Wallet.GetByUser(userID, &walletCache); val != nil && len(walletCache) > 0 {
		models.Cache.Convert(walletCache, &wallets)
	}

	if len(wallets) > 0 {
		return wallets, nil
	}

	ws, err := models.Repository.Wallet.FindMany(
		&models.WalletQuery{
			UserID: util.NewString(userID),
		}, nil)
	if err != nil {
		return nil, e.NewError500(e.WalletGetByUserFailed, "get user wallets failed: "+err.Error())
	}
	// check if any default wallet don't exist
	if !isUserEnoughWallets(ws) {
		if wss, iErr := initDefaultWallets(userID, ws); iErr != nil {
			return nil, iErr
		} else {
			ws = wss
		}
	}

	walletEarnings, _ := s.getEarningsByUser(userID)
	//if appErr != nil {
	//	return nil, e.NewError500(e.WalletGetByUserFailed, "get wallet earnings failed: "+appErr.Error())
	//}

	for _, earning := range walletEarnings {
		for _, w := range ws {
			if w.Address == earning.Address && w.Currency == earning.Currency {
				w.EarningBalance = earning.Amount
			}
		}
	}

	// set EarnedPoint
	for _, pWallet := range ws {
		if pWallet.Type == models.AssetTypePoint {
			point, aErr := OEPointHistory(context.Background()).GetUserPoints(userID)
			if aErr != nil {
				return nil, aErr
			}
			balance := point
			pWallet.Balance = balance
			pWallet.AvailableBalance = balance
		}
	}

	wallets = ws
	cacheData := lo.Map(wallets, func(w *models.Wallet, _ int) interface{} {
		return w
	})
	models.Cache.Wallet.SetByUser(userID, cacheData)
	return wallets, nil
}

func parseToken2Currency(token chaindto.BlockchainToken) models.Currency {
	switch token {
	case chaindto.BlockchainTokenUSDT:
		return models.CryptoCurrencyUSDT

	case chaindto.BlockchainTokenUSDC:
		return models.CryptoCurrencyUSDC

	case chaindto.BlockchainTokenOpenEdu:
		return models.CryptoCurrencyOpenEdu

	default:
		return models.Currency(token)
	}
}

func (s *WalletService) getEarningsByUser(userID string) ([]*models.WalletEarning, *e.AppError) {
	var cacheValue []interface{}
	if err := models.Cache.WalletEarning.GetByUser(userID, &cacheValue); err == nil && cacheValue != nil {
		var walletEarnings []*models.WalletEarning
		if err = models.Cache.Convert(cacheValue, &walletEarnings); err == nil {
			return walletEarnings, nil
		}
		log.Errorf("walletService::getEarningsByUser Convert cache value to wallet earnings error: %v", err)
	}

	userEarnings, err := openedu_chain.User.GetEarnings(userID)
	if err != nil {
		return nil, e.NewError500(e.WalletGetEarningsFailed, "Request to get wallet earnings error: "+err.Error())
	}

	walletEarnings := lo.Map(userEarnings, func(item *chaindto.WalletEarningResponse, _ int) *models.WalletEarning {
		return &models.WalletEarning{
			Address:  item.Address,
			Currency: parseToken2Currency(item.Token),
			Amount:   item.Amount,
		}
	})

	if cErr := models.Cache.WalletEarning.SetByUser(userID, lo.Map(walletEarnings, func(item *models.WalletEarning, _ int) interface{} {
		return item
	})); cErr != nil {
		log.Errorf("WalletService::getEarningsByUser Set wallet earnings cache error: %v", cErr)
	}

	return walletEarnings, nil
}

func (s *WalletService) GetWalletByUserIDAndType(userID string, walletType models.AssetType) (*models.Wallet, *e.AppError) {
	wallets, err := s.GetWalletsByUserID(userID)
	if err != nil {
		return nil, err
	}
	wallet, ok := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Type == walletType
	})
	if !ok {
		return nil, e.NewError400(e.WalletFindFailed, fmt.Sprintf("find wallet by user %s and type %s failed", userID, walletType))
	}
	return wallet, nil
}

func (s *WalletService) GetWalletByUserIDAndCurrency(userID string, currency models.Currency) (*models.Wallet, *e.AppError) {
	wallets, err := s.GetWalletsByUserID(userID)
	if err != nil {
		return nil, err
	}
	wallet, ok := lo.Find(wallets, func(item *models.Wallet) bool {
		return item.Currency == currency
	})
	if !ok {
		return nil, e.NewError400(e.WalletFindFailed, fmt.Sprintf("find wallet by user %s and currency %s failed", userID, currency))
	}
	return wallet, nil
}

func (s *WalletService) FindOne(query *models.WalletQuery, options *models.FindOneOptions) (*models.Wallet, *e.AppError) {
	if wallet, err := models.Repository.Wallet.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.WalletNotFound, err.Error())
		}
		return nil, e.NewError500(e.WalletFindFailed, "findOne: "+err.Error())
	} else {
		return wallet, nil
	}
}

func (s *WalletService) FindMany(query *models.WalletQuery, options *models.FindManyOptions) ([]*models.Wallet, *e.AppError) {
	if wallets, err := models.Repository.Wallet.FindMany(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.WalletNotFound, err.Error())
		}
		return nil, e.NewError500(e.WalletFindFailed, "findMany: "+err.Error())
	} else {
		return wallets, nil
	}
}

func (s *WalletService) RequestToWithdraw(request *dto.CreateWithdrawRequest) *e.AppError {
	wallet := request.Wallet
	// require wallet owner
	if request.User.ID != request.Wallet.UserID {
		return e.NewError400(e.WalletOwnerRequired, "Wallet owner required")
	}

	if wallet.Type != models.AssetTypeFiat && wallet.Type != models.AssetTypeCrypto {
		return e.NewError400(e.WalletNotAllowWithdraw, "Wallet type do not support withdraw: "+string(wallet.Type))
	}

	if wallet.AvailableBalance.LessThan(request.Amount) {
		return e.NewError400(e.WalletAvailableNotEnough, "Wallet available balance not enough: "+wallet.AvailableBalance.String())
	}

	settingType := lo.If(wallet.Type == models.AssetTypeCrypto, models.WalletCryptoMinRemainAmount).Else(models.WalletFiatMinRemainAmount)
	remainAmount := models.GetConfig[decimal.Decimal](settingType)
	// setting to limit withdraw
	// available_balance - withdraw >= remaining
	// CAN WITHDRAW: available_balance - remaining >= withdraw
	// CANNOT WITHDRAW: available_balance - remaining < withdraw
	if wallet.AvailableBalance.Sub(remainAmount).LessThan(request.Amount) {
		return e.NewError400(e.WalletBalanceNeedARemaining, "Wallet available balance need a minimum remaining: "+remainAmount.String())
	}

	// Decrease available balance of wallet
	if err := models.Repository.Wallet.IncreaseAvailableBalance(wallet, request.Amount.Neg(), nil); err != nil {
		return e.NewError500(e.WalletUpdateBalanceFailed, "Update wallet available balance error: "+err.Error())
	}

	bankAccount, bankErr := UserSetting.FindById(request.BankAccountID, false, nil)
	if bankErr != nil {
		return bankErr
	}

	approvalType := models.ApproveTypeWalletFiatWithdraw
	if wallet.Type == models.AssetTypeCrypto {
		approvalType = models.ApproveTypeWalletCryptoWithdraw
	}

	props := models.ApprovalProp{
		SettingID:    bankAccount.ID,
		SettingValue: bankAccount.Value,
	}

	// create approve request
	approval, appErr := Approval.Create(request.Org, request.User, &dto.CreateApprovalRequest{
		EntityType: models.WalletModelName,
		EntityID:   wallet.ID,
		Type:       approvalType,
		Value:      request.Amount.String(),
		Props:      props,
		Note:       request.Note,
	})
	if appErr != nil {
		// Rollback if create approval failed
		if err := models.Repository.Wallet.IncreaseAvailableBalance(wallet, request.Amount, nil); err != nil {
			return e.NewError500(e.WalletUpdateBalanceFailed, "Update wallet available balance error: "+err.Error())
		}

		return appErr
	}

	go func(a *models.Approval, w *models.Wallet, u *models.User) {
		openeduOrg, err := models.Repository.Organization.FindOne(&models.OrganizationQuery{
			Schema: util.NewString(models.SchemaOpenEdu),
		}, nil)
		if err != nil {
			log.Errorf("Push notification to OpenEdu admins for new withdrawal request ID %s error: %v", a.ID, err)
			return
		}

		userRoles, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
			RoleID: util.NewString(models.OrgAdminRoleType),
			OrgID:  &openeduOrg.ID,
		}, nil)
		if err != nil {
			log.Errorf("Push notification to OpenEdu admins for new withdrawal request ID %s error: %v", a.ID, err)
			return
		}

		if len(userRoles) == 0 {
			return
		}

		openeduAdminIDs := lo.Map(userRoles, func(userRole *models.UserRoleOrg, _ int) string {
			return userRole.UserID
		})
		openeduAdminIDs = lo.Uniq(openeduAdminIDs)

		notificationReq := &commdto.PushNotificationRequest{
			Code:       commdto.CodeNewWithdrawalRequest,
			EntityID:   a.ID,
			EntityType: commdto.ApprovalEntity,
			RuleTree: &commdto.TreeNodeRequest{
				Rule: &commdto.Rule{
					Subject:   commdto.NotiUser,
					Verb:      commdto.IsIn,
					ObjectIDs: openeduAdminIDs,
				},
			},
			Props: s.makeNotificationPropsForWithdraw(a, w, u).IntoComm(),
		}
		if err = communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to OpenEdu admins for new withdrawal request ID %s error: %v", a.ID, err)
		}
	}(approval, wallet, request.User)

	return nil
}

func (s *WalletService) ApproveWithdrawRequest(user *models.User, approval *models.Approval) *e.AppError {
	walletQuery := models.WalletQuery{
		ID: util.NewString(approval.EntityID),
	}
	wallet, err := s.FindOne(&walletQuery, nil)
	if err != nil {
		return err
	}

	// Get approve amount
	approveAmount, aErr := decimal.NewFromString(approval.ApproveValue)
	if aErr != nil {
		return e.NewError400(e.WalletParseAmountFailed, "parse approve value failed: "+aErr.Error())
	}

	// Get request amount
	requestAmount, aErr := decimal.NewFromString(approval.RequestValue)
	if aErr != nil {
		return e.NewError400(e.WalletParseAmountFailed, "parse request value failed: "+aErr.Error())
	}

	settingType := lo.If(wallet.Type == models.AssetTypeCrypto, models.WalletCryptoMinRemainAmount).Else(models.WalletFiatMinRemainAmount)
	remainAmount := models.GetConfig[decimal.Decimal](settingType)
	// setting to limit withdraw
	// CAN WITHDRAW: balance - withdraw >= remaining
	// <=> balance - remaining >= withdraw
	// CANNOT WITHDRAW: balance - remaining < withdraw
	if wallet.Balance.Sub(remainAmount).LessThan(approveAmount) {
		return e.NewError400(e.WalletBalanceNeedARemaining, "wallet balance need a minimum remaining: "+remainAmount.String())
	}

	// Subtract user balance, create transaction
	_, withErr := Transaction.Withdraw(&dto.CreateTransactionRequest{
		Wallet:     wallet,
		Amount:     approveAmount,
		OrgID:      approval.OrgID,
		EntityType: models.ApprovalModelName,
		EntityID:   approval.ID,
		Note:       approval.Note,
		Files:      approval.Files,
	})
	if withErr != nil {
		log.Error("ApproveWithdrawRequest failed:", withErr)
		return withErr
	}

	// If approve value is different request value, update available amount
	// newAvailableAmount = currentAvailableAmount - (approveAmount - requestAmount)
	// <=> newAvailableAmount = currentAvailableAmount - refundAmount
	if !approveAmount.Equal(requestAmount) {
		refundAmount := approveAmount.Sub(requestAmount)
		if err := models.Repository.Wallet.IncreaseAvailableBalance(wallet, refundAmount.Neg(), nil); err != nil {
			return e.NewError500(e.WalletUpdateBalanceFailed, "Refund wallet available balance error: "+err.Error())
		}
	}

	go func(a *models.Approval, w *models.Wallet, u *models.User) {
		notificationReq := &commdto.PushNotificationRequest{
			Code:       commdto.CodeWithdrawalRequestApproved,
			EntityID:   a.ID,
			EntityType: commdto.ApprovalEntity,
			RuleTree: &commdto.TreeNodeRequest{
				Rule: &commdto.Rule{
					Subject:   commdto.NotiUser,
					Verb:      commdto.IsIn,
					ObjectIDs: []string{a.RequesterID},
				},
			},
			Props: s.makeNotificationPropsForWithdraw(a, w, u).IntoComm(),
		}
		if err := communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to requester for withdrawal request ID is approved %s error: %v", a.ID, err)
		}
	}(approval, wallet, user)

	// Send WS
	return nil
}

func (s *WalletService) RejectWithdrawRequest(user *models.User, approval *models.Approval) *e.AppError {
	walletQuery := models.WalletQuery{
		ID: util.NewString(approval.EntityID),
	}
	wallet, appErr := s.FindOne(&walletQuery, nil)
	if appErr != nil {
		return appErr
	}

	// Get withdrawal amount
	amount, aErr := decimal.NewFromString(approval.RequestValue)
	if aErr != nil {
		return e.NewError400(e.WalletParseAmountFailed, "Parse approve value failed: "+approval.RequestValue)
	}

	// Refund withdrawal amount to available balance
	if err := models.Repository.Wallet.IncreaseAvailableBalance(wallet, amount, nil); err != nil {
		return e.NewError500(e.WalletUpdateBalanceFailed, "Refund wallet available balance error: "+err.Error())
	}

	go func(a *models.Approval, u *models.User, w *models.Wallet) {

		notificationReq := &commdto.PushNotificationRequest{
			Code:       commdto.CodeWithdrawalRequestRejected,
			EntityID:   a.ID,
			EntityType: commdto.ApprovalEntity,
			RuleTree: &commdto.TreeNodeRequest{
				Rule: &commdto.Rule{
					Subject:   commdto.NotiUser,
					Verb:      commdto.IsIn,
					ObjectIDs: []string{a.RequesterID},
				},
			},
			Props: s.makeNotificationPropsForWithdraw(a, wallet, u).IntoComm(),
		}
		if err := communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to requester for withdrawal request ID is rejected %s error: %v", a.ID, err)
		}

	}(approval, user, wallet)

	// Send WS
	return nil
}

func (s *WalletService) makeNotificationPropsForWithdraw(approval *models.Approval, wallet *models.Wallet, requester *models.User) models.JSONB {
	return models.JSONB{
		"approval_id": approval.ID,
		"amount":      approval.RequestValue,
		"currency":    wallet.Currency,
		"user_id":     requester.ID,
		"username":    requester.Username,
	}
}

func (s *WalletService) Sync(req *dto.WalletSyncRequest) (*models.Wallet, *e.AppError) {
	wallet, err := models.Repository.Wallet.FindByID(req.CoreWalletID, nil)
	if err != nil {
		return nil, e.NewError500(e.WalletFindFailed, "Find wallet failed: "+err.Error())
	}

	wallet.Address = req.Address
	wallet.PublicKey = req.PublicKey
	wallet.BlockchainWalletID = req.ID
	if err = models.Repository.Wallet.Update(wallet, nil); err != nil {
		return nil, e.NewError500(e.WalletUpdateFailed, "Update wallet failed: "+err.Error())
	}

	subWallets, err := models.Repository.Wallet.FindMany(&models.WalletQuery{
		ParentID: &wallet.ID,
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.WalletFindFailed, "Find sub wallets failed: "+err.Error())
	}

	for _, subWallet := range subWallets {
		subWallet.Address = req.Address
		subWallet.PublicKey = req.PublicKey
		subWallet.BlockchainWalletID = req.ID
		if err = models.Repository.Wallet.Update(subWallet, nil); err != nil {
			return nil, e.NewError500(e.WalletUpdateFailed, "Update wallet failed: "+err.Error())
		}
	}

	return wallet, nil
}

// CreateSponsorWallet creates a sponsor wallet for the given user (manual request)
func (s *WalletService) CreateSponsorWallet(req *dto.CreateSponsorWalletRequest) (*dto.CreateSponsorWalletResponse, *e.AppError) {
	// Check if sponsor wallet already exists
	exists, err := openedu_chain.Wallet.CheckSponsorWalletExists(&chaindto.CheckSponsorWalletExistsRequest{
		WalletID:  req.Wallet.BlockchainWalletID,
		IsMainnet: setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		return nil, e.NewError500(e.WalletCreateSponsorFailed, "Failed to check sponsor wallet existence: "+err.Error())
	}

	if exists {
		return nil, e.NewError400(e.WalletSponsorAlreadyExists, "Sponsor wallet already exists for this wallet")
	}

	// Generate unique transaction ID
	coreTxID := util.GenerateId()

	// Create sponsor wallet request
	sponsorName := "Personal Sponsor"
	if req.User.DisplayName != "" {
		sponsorName = req.User.DisplayName + " Sponsor"
	}

	description := "Personal sponsor wallet for gas fee management"
	if req.Description != "" {
		description = req.Description
	}

	chainReq := &chaindto.CreateSponsorWalletRequest{
		WalletID:    req.Wallet.BlockchainWalletID,
		CoreTxID:    coreTxID,
		SponsorID:   req.User.ID,
		SponsorName: sponsorName,
		Description: description,
		Amount:      req.Amount.String(),
		Token:       string(models.CryptoCurrencyETH),
		Network:     string(req.Network),
		IsMainnet:   setting.OpenEduChainSetting.IsMainnet,
	}

	// Send request to blockchain service
	if err := openedu_chain.Wallet.CreateSponsorWallet(chainReq); err != nil {
		return nil, e.NewError500(e.WalletCreateSponsorFailed, "Failed to create sponsor wallet: "+err.Error())
	}

	log.Infof("Successfully created sponsor wallet for user %s, wallet %s", req.User.ID, req.Wallet.ID)

	return &dto.CreateSponsorWalletResponse{
		SponsorWalletID: chainReq.WalletID,
		TransactionID:   coreTxID,
		Status:          models.WalletStatusPending,
		Message:         "Sponsor wallet creation request submitted successfully",
	}, nil
}

// GetSponsorWallet gets sponsor wallet information for the given user
func (s *WalletService) GetSponsorWallet(userID string) (*dto.GetSponsorWalletResponse, *e.AppError) {
	return s.GetSponsorWalletWithNetwork(userID, models.CryptoCurrencyETH, models.BlockchainNetworkBASE)
}

// GetSponsorWalletWithNetwork gets sponsor wallet information for the given user with specific currency and network
func (s *WalletService) GetSponsorWalletWithNetwork(userID string, currency models.Currency, network models.BlockchainNetwork) (*dto.GetSponsorWalletResponse, *e.AppError) {
	// Find user's wallet for the specified network
	wallet, appErr := s.FindOne(&models.WalletQuery{
		UserID:   &userID,
		Currency: util.NewT(currency),
		Network:  util.NewT(network),
	}, &models.FindOneOptions{})

	if appErr != nil {
		return nil, appErr
	}

	// Check if wallet has blockchain_wallet_id (is synced)
	if wallet.BlockchainWalletID == "" {
		return nil, e.NewError400(e.WalletNotSynced, "Wallet not yet synced with blockchain")
	}

	// Get sponsor wallet balance to check existence and get current balance
	balance, err := openedu_chain.Wallet.GetGasSponsorBalance(&chaindto.GetWalletGasSponsorBalanceRequest{
		WalletID:   wallet.BlockchainWalletID,
		CourseCuid: userID,
		IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
	})
	if err != nil {
		if helpers.IsNotFoundOrNotExistsError(err) {
			return nil, e.NewError404(e.WalletNotFound, "Sponsor wallet not found for this user")
		}
		return nil, e.NewError500(e.WalletGetSponsorGasFailed, "Failed to get sponsor wallet balance: "+err.Error())
	}

	return &dto.GetSponsorWalletResponse{
		SponsorWalletID: wallet.BlockchainWalletID,
		WalletAddress:   wallet.Address,
		Network:         string(network),
		Balance:         balance,
		Token:           string(currency),
		Status:          models.WalletStatusActive,
		CreatedAt:       helpers.FormatUnixMilliToISO(int64(wallet.CreateAt)),
		LastUpdated:     helpers.FormatUnixMilliToISO(int64(wallet.UpdateAt)),
	}, nil
}

func (s *WalletService) SyncSponsorWallet(req *dto.SponsorWalletSyncRequest) (*models.Transaction, *e.AppError) {
	transaction, err := models.Repository.Transaction.FindOne(&models.TransactionQuery{
		ID: &req.CoreTxID,
	}, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.WalletNotFound, "Transaction not found: "+req.CoreTxID)
		}
		return nil, e.NewError500(e.TransactionFindFailed, "Find transaction failed: "+err.Error())
	}

	transaction.BlockchainTxID = req.ID
	transaction.Status = models.TransactionStatus(req.Status)

	if err = models.Repository.Transaction.Update(transaction, nil); err != nil {
		return nil, e.NewError500(e.TransactionUpdateFailed, "Update transaction failed: "+err.Error())
	}

	log.Infof("Successfully synced sponsor wallet transaction %s (blockchain: %s)", req.CoreTxID, req.ID)
	return transaction, nil
}
