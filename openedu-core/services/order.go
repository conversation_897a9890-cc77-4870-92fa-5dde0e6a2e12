package services

import (
	"context"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	chaindto "openedu-core/pkg/openedu_chain/dto"

	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"github.com/avast/retry-go/v4"
	"github.com/shopspring/decimal"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

var editOrderRoles = []string{
	models.AdminRoleType,
	models.SystemAdminRoleType,
	models.OrgAdminRoleType,
	models.ModeratorRoleType,
	models.OrgModeratorRoleType,
}

func generateOrderCode() (*string, *e.AppError) {
	// Handle generate code
	// Find Order MaxNumber
	var lastOrderNumber int64

	latestOrder, oErr := models.Repository.Order.FindOne(&models.OrderQuery{IncludeDeleted: util.NewBool(false)}, &models.FindOneOptions{Sort: []string{"order_number DESC"}})

	if oErr != nil {
		lastOrderNumber = 0
	} else {
		lastOrderNumber = latestOrder.OrderNumber
	}

	code, gErr := util.GenerateOrderCode(lastOrderNumber)
	if gErr != nil {
		return nil, e.NewError500(e.Generate_order_code_error, gErr.Error())
	}
	return code, nil
}

// Calculate actual amount
// Handle coupon id
// discountAmount = coupon.DiscountAmount
func calcDiscountAmount(coupon *models.Coupon, order *models.Order) (decimal.Decimal, decimal.Decimal, *e.AppError) {
	amount := order.Amount
	if coupon == nil {
		return amount, decimal.Zero, nil
	}
	// Get coupon
	// switch case coupon type: flat or percentage
	var actualAmount decimal.Decimal
	var discountValue decimal.Decimal

	currencyInfo, found := models.GetCurrencyInfo(order.Currency)
	if !found {
		return amount, decimal.Zero, e.NewError400(e.OrderMismatchCurrencies, "Unsupported currency: "+string(order.Currency))
	}

	switch coupon.Type {
	case models.CouponFlat:
		switch currencyInfo.Type {
		case models.AssetTypeFiat:
			discountValue = coupon.FiatDiscountAmount
			actualAmount = amount.Sub(discountValue)

		case models.AssetTypeCrypto:
			discountValue = coupon.CryptoDiscountAmount
			actualAmount = amount.Sub(discountValue)
		}

	case models.CouponPercentage:
		switch currencyInfo.Type {
		case models.AssetTypeFiat:
			discountValue = amount.Mul(coupon.FiatDiscountPercentage).Div(decimal.NewFromInt(100))
			actualAmount = amount.Sub(discountValue)
			if coupon.FiatAllowMaximumDiscount.GreaterThan(decimal.Zero) &&
				(discountValue.GreaterThan(coupon.FiatAllowMaximumDiscount)) {
				actualAmount = amount.Sub(coupon.FiatAllowMaximumDiscount)
				discountValue = coupon.FiatAllowMaximumDiscount
			}

		case models.AssetTypeCrypto:
			discountValue = amount.Mul(coupon.CryptoDiscountPercentage).Div(decimal.NewFromInt(100))
			actualAmount = amount.Sub(discountValue)
			if coupon.CryptoAllowMaximumDiscount.GreaterThan(decimal.Zero) &&
				(discountValue.GreaterThan(coupon.CryptoAllowMaximumDiscount)) {
				actualAmount = amount.Sub(coupon.CryptoAllowMaximumDiscount)
				discountValue = coupon.CryptoAllowMaximumDiscount
			}
		}

	default:
		actualAmount = amount
		discountValue = decimal.Zero
	}

	return actualAmount, discountValue, nil
}

func (s *OrderService) calcDiscountAmountByRefCode(refCode *string, amount decimal.Decimal) decimal.Decimal {
	discountAmount := decimal.Zero
	link, err := ReferralLink.FindOne(
		&models.ReferralLinkQuery{RefCode: refCode},
		&models.FindOneOptions{Preloads: []string{models.CommissionField}},
	)
	if err != nil {
		log.Error("Find referral link by ref code failed: ", err.ErrCode)
		return discountAmount
	}
	if link.ShareRate > 0 {
		rate := decimal.NewFromFloat32(link.ShareRate)
		discountAmount = amount.Mul(rate).Div(decimal.NewFromInt(100))
	}

	return discountAmount
}

func (s *OrderService) Create(user *models.User, data *dto.CreateOrderRequest) (*models.Order, *e.AppError) {
	course, pubCourse, err := Course.GetCourseFromPublishCourse(data.CourseCuid, data.CourseID)
	if err != nil {
		return nil, err
	}

	paymentMethodID := data.PaymentMethodID
	if paymentMethodID == "" {
		paymentMethod, nErr := models.Repository.PaymentMethod.FindOne(&models.PaymentMethodQuery{
			PaymentType: util.NewT(course.PriceSettings.FiatCurrency),
			Enable:      util.NewBool(true),
		}, &models.FindOneOptions{Sort: []string{models.CreateAtDESC}})
		if nErr != nil {
			return nil, e.NewError500(e.Payment_method_find_one_failed, "Find the payment method error: "+nErr.Error())
		}

		paymentMethodID = paymentMethod.ID
	}

	var orderItems []*models.OrderItem
	itemAmount := course.PriceSettings.FiatPrice
	orderItems = append(orderItems, &models.OrderItem{
		EntityID:       course.ID,
		EntityCuid:     course.Cuid,
		EntityType:     models.CourseModelName,
		OrgID:          pubCourse.OrgID,
		OrgSchema:      pubCourse.OrgSchema,
		PayFromOrgID:   data.Org.ID,
		UserID:         user.ID,
		Amount:         itemAmount,
		DiscountAmount: decimal.Zero,
		ActualAmount:   itemAmount,
		Status:         models.OrderStatusNew,
		Currency:       data.Currency,
	})

	// Calc total amount form slice order item
	orderAmount := lo.Reduce(orderItems, func(agg decimal.Decimal, item *models.OrderItem, _ int) decimal.Decimal {
		return agg.Add(item.ActualAmount)
	}, decimal.Zero)

	code, codeErr := generateOrderCode()
	if codeErr != nil {
		return nil, codeErr
	}

	actualAmount := orderAmount
	missingAmount := orderAmount
	referralDiscountAmount := decimal.Zero
	if data.ReferralCode != nil && *data.ReferralCode != "" {
		referralDiscountAmount = s.calcDiscountAmountByRefCode(data.ReferralCode, orderAmount)
		actualAmount = actualAmount.Sub(referralDiscountAmount)
		missingAmount = missingAmount.Sub(referralDiscountAmount)
	}

	orderTx := models.GetDb(models.OrderTbl).Begin()
	defer func() {
		if rcv := recover(); rcv != nil {
			orderTx.Rollback()
		}
	}()

	// create order
	order := models.Order{
		OrgID:                  pubCourse.OrgID,
		PayFromOrgID:           data.Org.ID,
		UserID:                 user.ID,
		Amount:                 orderAmount,
		ActualAmount:           actualAmount,
		MissingAmount:          missingAmount,
		DiscountAmount:         decimal.Zero,
		Code:                   strings.ToUpper(*code),
		Status:                 models.OrderStatusNew,
		PaymentMethodID:        paymentMethodID,
		Currency:               data.Currency,
		ReferralDiscountAmount: referralDiscountAmount,
	}
	if data.ReferralCode != nil {
		order.ReferralCode = *data.ReferralCode
	} else {
		order.ReferralCode = ""
	}

	if oErr := models.Repository.Order.Create(&order, orderTx); oErr != nil {
		orderTx.Rollback()
		return nil, e.NewError500(e.OrderCreateFailed, "Create order error: "+oErr.Error())
	}

	orderItems = lo.Map(orderItems, func(o *models.OrderItem, _ int) *models.OrderItem {
		o.OrderID = order.ID
		return o
	})

	// Create order items
	if oiErr := models.Repository.OrderItem.CreateMany(orderItems, orderTx); oiErr != nil {
		orderTx.Rollback()
		return nil, e.NewError500(e.OrderItemCreateFailed, "Create order items error: "+oiErr.Error())
	}

	if cErr := orderTx.Commit().Error; cErr != nil {
		orderTx.Rollback()
		return nil, e.NewError500(e.OrderCreateFailed, "Commit transaction error: "+cErr.Error())
	}

	trackingReq := &communicationdto.TrackingRequest{
		ActorID:      user.ID,
		Verb:         communicationdto.Ordered,
		Object:       communicationdto.CourseModelName,
		ObjectID:     data.CourseCuid,
		Context:      communicationdto.SourceContext,
		ContextValue: communicationdto.GetValidSourceType(data.Source),
		OrgID:        pubCourse.OrgID,
		OrgSchema:    pubCourse.OrgSchema,
	}

	if data.ReferralCode != nil {
		trackingReq.ContextValue = *data.ReferralCode
	} else {
		trackingReq.ContextValue = communicationdto.GetValidSourceType(data.Source)
	}

	if tErr := communication.Tracking.CreateTracking(trackingReq); tErr != nil {
		log.Errorf("Create tracking for order ID %s is created error: %v", order.ID, tErr)
	}

	return &order, nil
}

func (s *OrderService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Order, *e.AppError) {
	query := &models.OrderQuery{ID: util.NewString(id), IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil)}

	if order, err := models.Repository.Order.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OrderNotFound, err.Error())
		}
		return nil, e.NewError500(e.OrderFindOneFailed, err.Error())
	} else {
		return order, nil
	}

}

func (s *OrderService) FindOne(query *models.OrderQuery, options *models.FindOneOptions) (*models.Order, *e.AppError) {
	if order, err := models.Repository.Order.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.OrderNotFound, err.Error())
		}
		return nil, e.NewError500(e.OrderFindOneFailed, err.Error())
	} else {
		return order, nil
	}
}

func (s *OrderService) FindPage(query *models.OrderQuery, options *models.FindPageOptions) ([]*models.Order, *models.Pagination, *e.AppError) {
	if order, pagination, err := models.Repository.Order.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.OrderFindOneFailed, err.Error())
	} else {
		return order, pagination, nil
	}
}

func (s *OrderService) FindMany(query *models.OrderQuery, options *models.FindManyOptions) ([]*models.Order, *e.AppError) {
	if order, err := models.Repository.Order.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.OrderFindManyFailed, err.Error())
	} else {
		return order, nil
	}
}

func (s *OrderService) ChangeReferralCode(order *models.Order, newRefCode string) *e.AppError {
	// Check if new referral code is still existing referral code
	if newRefCode == order.ReferralCode {
		return nil
	}

	newRefDiscountAmount := decimal.Zero
	if newRefCode != "" {
		newRefDiscountAmount = s.calcDiscountAmountByRefCode(&newRefCode, order.Amount)
	}

	// new actual amount = existing actual amount + existing referral discount amount - new referral discount amount
	// new missing amount = existing missing amount + existing referral discount amount - new referral discount amount
	newActualAmount := order.ActualAmount.Add(order.ReferralDiscountAmount).Sub(newRefDiscountAmount)
	newMissingAmount := order.MissingAmount.Add(order.ReferralDiscountAmount).Sub(newRefDiscountAmount)

	order.ReferralCode = newRefCode
	order.ReferralDiscountAmount = newRefDiscountAmount
	order.ActualAmount = newActualAmount
	order.MissingAmount = newMissingAmount

	if err := models.Repository.Order.Update(order, nil); err != nil {
		return e.NewError500(e.OrderUpdateFailed, "Update order by ID error: "+err.Error())
	}

	return nil
}

func (s *OrderService) ChangePaymentMethod(order *models.Order, paymentMethod *models.PaymentMethod) *e.AppError {
	newCurrencyInfo, found := models.GetCurrencyInfo(paymentMethod.PaymentType)
	if !found {
		return e.NewError400(e.OrderMismatchCurrencies, "Unsupported currency: "+string(paymentMethod.PaymentType))
	}

	orderItems, appErr := OrderItem.Find(&models.OrderItemQuery{OrderID: &order.ID}, nil)
	if appErr != nil {
		return appErr
	}

	for _, orderItem := range orderItems {
		course, aErr := Course.FindOne(&models.CourseQuery{ID: &orderItem.EntityID}, nil)
		if aErr != nil {
			return aErr
		}

		switch newCurrencyInfo.Type {
		case models.AssetTypeFiat:
			orderItem.Amount = course.PriceSettings.FiatPrice
			orderItem.DiscountAmount = decimal.Zero
			orderItem.ActualAmount = course.PriceSettings.FiatPrice
			orderItem.Currency = paymentMethod.PaymentType

		case models.AssetTypeCrypto:
			orderItem.Amount = course.PriceSettings.CryptoPrice
			orderItem.DiscountAmount = decimal.Zero
			orderItem.ActualAmount = course.PriceSettings.CryptoPrice
			orderItem.Currency = paymentMethod.PaymentType

		}
		if uErr := models.Repository.OrderItem.Update(orderItem, nil); uErr != nil {
			return e.NewError500(e.OrderItemUpdateFailed, "Update order item error: "+uErr.Error())
		}
	}

	orderAmount := lo.Reduce(orderItems, func(agg decimal.Decimal, item *models.OrderItem, _ int) decimal.Decimal {
		return agg.Add(item.ActualAmount)
	}, decimal.Zero)

	actualAmount := orderAmount
	missingAmount := orderAmount
	referralDiscountAmount := decimal.Zero
	if order.ReferralCode != "" {
		referralDiscountAmount = s.calcDiscountAmountByRefCode(&order.ReferralCode, orderAmount)
		actualAmount = actualAmount.Sub(referralDiscountAmount)
		missingAmount = missingAmount.Sub(referralDiscountAmount)
	}

	order.Amount = orderAmount
	order.ActualAmount = actualAmount
	order.MissingAmount = missingAmount
	order.DiscountAmount = decimal.Zero
	order.ReferralDiscountAmount = referralDiscountAmount
	order.PaymentMethodID = paymentMethod.ID
	order.Currency = paymentMethod.PaymentType
	if err := models.Repository.Order.Update(order, nil); err != nil {
		return e.NewError500(e.OrderUpdateFailed, "Update order by ID error: "+err.Error())
	}

	usedCoupon, _ := CouponHistory.FindOne(
		&models.CouponHistoryQuery{
			OrderID:   &order.ID,
			NotStatus: util.NewT(models.CouponStatusNew),
		},
		&models.FindOneOptions{Preloads: []string{models.CouponField}})
	if usedCoupon != nil {
		order.Coupon = usedCoupon.Coupon
		if aErr := s.CalculateOrderAndSave(order, usedCoupon.Coupon, true); aErr != nil {
			return aErr
		}
	} else {
		if aErr := s.CalculateOrderAndSave(order, nil, true); aErr != nil {
			return aErr
		}
	}

	return nil
}

func (s *OrderService) Update(o *models.Order) *e.AppError {
	if err := models.Repository.Order.Update(o, nil); err != nil {
		return e.NewError500(e.OrderUpdateFailed, "Update order by ID error: "+err.Error())
	}

	// Update status for order items with order status
	orderItems, err := models.Repository.OrderItem.FindMany(&models.OrderItemQuery{OrderID: &o.ID}, &models.FindManyOptions{})
	if err != nil {
		return e.NewError500(e.OrderFindOrderItemsFailed, "Find order items of order to update error: "+err.Error())
	}

	for _, orderItem := range orderItems {
		orderItem.Status = o.Status
		orderItem.Currency = o.Currency
		if oErr := models.Repository.OrderItem.Update(orderItem, nil); oErr != nil {
			return e.NewError500(e.OrderItemUpdateFailed, "Update order item error: "+oErr.Error())
		}
	}

	if couponErr := UpdateUsedCouponOnPayment(o); couponErr != nil {
		return couponErr
	}

	if o.Status == models.OrderStatusSuccess {
		if sErr := s.HandleOrderSuccess(o, orderItems); sErr != nil {
			return e.NewError500(e.OrderItemUpdateFailed, "handle order success failed: "+sErr.Msg)
		}
	}

	return nil
}

func (s *OrderService) updateOrderItem(updatedOrderItem *models.OrderItem) (*models.Order, *e.AppError) {
	if err := models.Repository.OrderItem.Update(updatedOrderItem, nil); err != nil {
		return nil, e.NewError500(e.OrderItemUpdateFailed, "Update order item error: "+err.Error())
	}

	order, oErr := Order.FindByID(updatedOrderItem.OrderID, false, nil)
	if oErr != nil {
		return nil, e.NewError500(e.OrderFindOneFailed, "Find order by ID error: "+oErr.Msg)
	}

	orderItems, err := models.Repository.OrderItem.FindMany(&models.OrderItemQuery{OrderID: &updatedOrderItem.OrderID}, &models.FindManyOptions{})
	if err != nil {
		return nil, e.NewError500(e.OrderFindOrderItemsFailed, "Find order items of order to update error: "+err.Error())
	}

	paidAmount := order.ActualAmount.Sub(order.MissingAmount)

	// Calc total amount form slice order item
	newOrderAmount := lo.Reduce(orderItems, func(agg decimal.Decimal, item *models.OrderItem, _ int) decimal.Decimal {
		return agg.Add(item.ActualAmount)
	}, decimal.Zero)

	newActualAmount := newOrderAmount
	newMissingAmount := newOrderAmount.Sub(paidAmount)
	referralDiscountAmount := decimal.Zero
	if order.ReferralCode != "" {
		referralDiscountAmount = s.calcDiscountAmountByRefCode(&order.ReferralCode, newOrderAmount)
		newActualAmount = newActualAmount.Sub(referralDiscountAmount)
		newMissingAmount = newMissingAmount.Sub(referralDiscountAmount)
	}

	order.Amount = newOrderAmount
	order.ActualAmount = newActualAmount
	order.MissingAmount = newMissingAmount
	order.DiscountAmount = decimal.Zero
	order.ReferralDiscountAmount = referralDiscountAmount

	usedCoupon, _ := CouponHistory.FindOne(
		&models.CouponHistoryQuery{
			OrderID:   &order.ID,
			NotStatus: util.NewT(models.CouponStatusNew),
		},
		&models.FindOneOptions{Preloads: []string{models.CouponField}})
	if usedCoupon != nil {
		order.Coupon = usedCoupon.Coupon
		if aErr := s.CalculateOrderAndSave(order, usedCoupon.Coupon, true); aErr != nil {
			return nil, aErr
		}
	} else {
		if aErr := s.CalculateOrderAndSave(order, nil, true); aErr != nil {
			return nil, aErr
		}
	}

	return order, nil
}

func (s *OrderService) CanUpdateOrder(o *models.Order, user *models.User) bool {
	if o.UserID == user.ID {
		return true
	}
	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Find UserRoleOrg failed: ", err.Error())
		return false
	}
	if len(userRoles) > 0 {
		allow := false
		for _, ur := range userRoles {
			if lo.Contains[string](editOrderRoles, ur.RoleID) {
				allow = true
				break
			}
		}
		return allow
	}

	return false
}

func (s *OrderService) IsPurchased(userId string, data *dto.CreateOrderRequest) (*models.Order, *e.AppError) {
	course, cErr := Course.FindById(data.CourseID, false, nil)
	if cErr != nil {
		return nil, cErr
	}
	orderItems, oErr := OrderItem.Find(
		&models.OrderItemQuery{
			UserID:     &userId,
			EntityCuid: util.NewString(course.Cuid),
			EntityType: util.NewT(models.CourseModelName),
			NotStatus:  util.NewT(models.OrderStatusFailed),
		}, nil)
	if oErr != nil {
		return nil, oErr
	}

	if len(orderItems) <= 0 {
		return nil, nil
	}

	var orderID string
	var courseID string
	var orderItem *models.OrderItem
	for _, item := range orderItems {
		orderID = item.OrderID
		courseID = item.EntityID
		orderItem = item
	}

	order, oErr := Order.FindByID(orderID, false, nil)
	if oErr != nil {
		return nil, e.NewError500(e.OrderFindOneFailed, "Find order by ID error: "+oErr.Msg)
	}

	if order.IsSuccess() {
		return nil, e.NewError400(e.OrderAlreadySuccess, "Course already purchased with order ID: "+order.ID)
	}

	// Check whether the course version in the order item is latest
	publishCourse, err := models.Repository.PublishCourse(context.TODO()).FindOne(&models.PublishCourseQuery{CourseCuid: &course.Cuid}, nil)
	if err != nil {
		return nil, e.NewError500(e.Publish_course_find_failed, "Find the publish course by CUID error: "+err.Error())
	}

	// Update the course version to latest in the order item
	if publishCourse.CourseID != courseID && order.PaymentMethodID != "" {
		coursePrice, pErr := models.Repository.CoursePrice.FindOne(&models.CoursePriceQuery{CourseID: &publishCourse.CourseID}, nil)
		if pErr != nil {
			return nil, e.NewError500(e.Course_find_one_failed, "Find the course price by course ID "+publishCourse.CourseID+" error: "+pErr.Error())
		}

		paymentMethod, pErr := models.Repository.PaymentMethod.FindByID(order.PaymentMethodID, nil)
		if pErr != nil {
			return nil, e.NewError500(e.Payment_method_find_one_failed, "Find the payment method by ID "+order.PaymentMethodID+" error: "+pErr.Error())
		}

		// If the course is changed from paid to free
		if !order.Amount.IsZero() && !coursePrice.IsPay {
			orderItem.Amount = decimal.Zero
			orderItem.ActualAmount = decimal.Zero
			order, oErr = s.updateOrderItem(orderItem)
			if oErr != nil {
				return nil, e.NewError500(e.OrderItemUpdateFailed, "Update the order item error: "+oErr.Msg)
			}
		}

		// If the course crypto payment from enabled to disabled
		if paymentMethod.Service == models.PaymentServiceCrypto && !coursePrice.CryptoPaymentEnabled {
			// Update order payment method to default
			if order.Paid.IsZero() {
				newPaymentMethod, nErr := models.Repository.PaymentMethod.FindOne(&models.PaymentMethodQuery{
					PaymentType: util.NewT(coursePrice.FiatCurrency),
					Enable:      util.NewBool(true),
				}, &models.FindOneOptions{Sort: []string{models.CreateAtDESC}})
				if nErr != nil {
					return nil, e.NewError500(e.Payment_method_find_one_failed, "Find the payment method error: "+nErr.Error())
				}

				if aErr := s.ChangePaymentMethod(order, newPaymentMethod); aErr != nil {
					return nil, aErr
				}
			}
		} else {
			// If the course change price: increase or decrease
			currencyInfo, found := models.GetCurrencyInfo(paymentMethod.PaymentType)
			if found {
				var newCurrency models.Currency
				shouldUpdatePayMethod := false
				switch currencyInfo.Type {
				case models.AssetTypeFiat:
					if paymentMethod.PaymentType != coursePrice.FiatCurrency {
						shouldUpdatePayMethod = true
						newCurrency = coursePrice.FiatCurrency
					} else {
						orderItem.EntityID = publishCourse.CourseID
						orderItem.EntityCuid = publishCourse.CourseCuid
						orderItem.Amount = coursePrice.FiatPrice
						orderItem.DiscountAmount = decimal.Zero
						orderItem.ActualAmount = coursePrice.FiatPrice
						orderItem.Currency = paymentMethod.PaymentType
					}

				case models.AssetTypeCrypto:
					if paymentMethod.PaymentType != coursePrice.CryptoCurrency {
						shouldUpdatePayMethod = true
						newCurrency = coursePrice.CryptoCurrency
					} else {
						orderItem.EntityID = publishCourse.CourseID
						orderItem.EntityCuid = publishCourse.CourseCuid
						orderItem.Amount = coursePrice.CryptoPrice
						orderItem.DiscountAmount = decimal.Zero
						orderItem.ActualAmount = coursePrice.CryptoPrice
						orderItem.Currency = paymentMethod.PaymentType
					}
				}

				if shouldUpdatePayMethod {
					newPaymentMethod, nErr := models.Repository.PaymentMethod.FindOne(&models.PaymentMethodQuery{
						PaymentType: util.NewT(newCurrency),
						Enable:      util.NewBool(true),
					}, &models.FindOneOptions{Sort: []string{models.CreateAtDESC}})
					if nErr != nil {
						return nil, e.NewError500(e.Payment_method_find_one_failed, "Find the payment method error: "+nErr.Error())
					}

					if oErr = s.ChangePaymentMethod(order, newPaymentMethod); oErr != nil {
						return nil, e.NewError500(e.OrderItemUpdateFailed, "Update the order item error: "+oErr.Msg)
					}

				} else {
					order, oErr = s.updateOrderItem(orderItem)
					if oErr != nil {
						return nil, e.NewError500(e.OrderItemUpdateFailed, "Update the order item error: "+oErr.Msg)
					}
				}
			}
		}
	}

	// Order status is new or insufficient then continue payment
	// NOTE: The added coupon will be removed by cronjob after TTL
	usedCoupon, _ := CouponHistory.FindOne(
		&models.CouponHistoryQuery{
			OrderID:   &order.ID,
			NotStatus: util.NewT(models.CouponStatusNew),
		},
		&models.FindOneOptions{Preloads: []string{models.CouponField}})
	if usedCoupon != nil {
		order.Coupon = usedCoupon.Coupon
	}
	return order, nil
}

func (s *OrderService) ApplyCoupon(org *models.Organization, user *models.User, coupon *models.Coupon, order *models.Order) *e.AppError {
	_, verifyErr := Coupon.Verify(org, user, order, coupon)
	if verifyErr != nil {
		if verifyErr.ErrCode == e.CouponAlreadyInUseForThisOrder {
			return nil
		}
		return verifyErr
	}

	// check change coupon ?
	existed, _ := CouponHistory.FindOne(
		&models.CouponHistoryQuery{
			OrderID:   &order.ID,
			NotStatus: util.NewT(models.CouponStatusNew),
		},
		&models.FindOneOptions{Preloads: []string{models.CouponField}})

	if existed != nil {
		if existed.Coupon.CouponCode != coupon.CouponCode {
			// Re-calc amount
			order.ActualAmount = order.Amount
			order.MissingAmount = order.Amount
			order.DiscountAmount = decimal.Zero
		} else {
			return e.NewError400(e.CouponAlreadyInUseForThisOrder, "Coupon already used")
		}
	}

	if err := s.CalculateOrderAndSave(order, coupon, false); err != nil {
		return err
	}

	if err := Order.Update(order); err != nil {
		return err
	}

	if afterError := handleHistoryCoupon(user, coupon, order, existed); afterError != nil {
		return afterError
	}

	return nil
}

func (s *OrderService) RemoveCoupon(order *models.Order, keepOrder bool) (*models.Order, *e.AppError) {
	usedCoupon, _ := CouponHistory.FindOne(
		&models.CouponHistoryQuery{
			OrderID:   &order.ID,
			NotStatus: util.NewT(models.CouponStatusNew),
		},
		&models.FindOneOptions{Preloads: []string{models.CouponField}})
	if usedCoupon != nil {
		if err := models.Repository.CouponHistory.Delete(usedCoupon.ID, nil); err != nil {
			return order, e.NewError500(e.CouponHistoryDeleteFailed, err.Error())
		}
	}
	if keepOrder {
		if err := s.CalculateOrderAndSave(order, nil, true); err != nil {
			return order, err
		}
	} else {
		if err := models.Repository.Order.Delete(order.ID, nil); err != nil {
			return nil, e.NewError500(e.OrderDeleteFailed, "Delete order after removing coupon error: "+err.Error())
		} else {
			if _, itemErr := models.Repository.OrderItem.DeleteMany(&models.OrderItemQuery{OrderID: util.NewString(order.ID)}, nil); itemErr != nil {
				return nil, e.NewError500(e.OrderDeleteFailed, "Delete order items after removing coupon error: "+itemErr.Error())
			}
			return nil, nil
		}
	}

	return order, nil
}

func (s *OrderService) CalculateOrderAndSave(order *models.Order, coupon *models.Coupon, needSave bool) *e.AppError {
	orderActualAmount, orderDiscountAmount, calcErr := calcDiscountAmount(coupon, order)
	if calcErr != nil {
		return calcErr
	}

	// Check if 100% coupon
	if orderActualAmount.LessThanOrEqual(decimal.Zero) {
		order.ActualAmount = decimal.Zero
		order.DiscountAmount = orderDiscountAmount
		order.MissingAmount = decimal.Zero
	} else {
		order.ActualAmount = orderActualAmount
		order.DiscountAmount = orderDiscountAmount
		order.MissingAmount = orderActualAmount
		if order.ReferralCode != "" {
			referralDiscountAmount := s.calcDiscountAmountByRefCode(&order.ReferralCode, order.ActualAmount)
			order.ActualAmount = orderActualAmount.Sub(referralDiscountAmount)
			order.MissingAmount = orderActualAmount.Sub(referralDiscountAmount)
			order.ReferralDiscountAmount = referralDiscountAmount
		}
	}

	if needSave {
		if err := Order.Update(order); err != nil {
			return err
		}
	}
	return nil
}

func handleHistoryCoupon(user *models.User, coupon *models.Coupon, order *models.Order, oldUsed *models.CouponHistory) *e.AppError {
	// check to rollback old coupon
	if oldUsed != nil {
		if oldUsed.Coupon.CouponCode != coupon.CouponCode {
			if err := models.Repository.Coupon.Decrement(oldUsed.Coupon, "total_used", 1, nil); err != nil {
				return e.NewError500(e.CouponUpdateFailed, err.Error())
			}

			if err := models.Repository.CouponHistory.Delete(oldUsed.ID, nil); err != nil {
				return e.NewError500(e.CouponHistoryDeleteFailed, err.Error())
			}
		} else {
			return nil
		}
	}
	// Increment Used
	if err := models.Repository.Coupon.Increment(coupon, "total_used", 1, nil); err != nil {
		return e.NewError500(e.CouponUpdateFailed, err.Error())
	}

	// Create coupon history
	if err := CouponHistory.Create(user, order, coupon); err != nil {
		return err
	}

	return nil
}

func (s *OrderService) MarkOrderSuccessByCoupon(order *models.Order, request *dto.VerifyPayment) *e.AppError {
	if order.IsSuccess() {
		return e.NewError400(e.OrderAlreadySuccess, "Order is already success")
	}

	if order.ActualAmount.GreaterThan(decimal.Zero) {
		return e.NewError400(e.OrderNeedPayment, "order need payment")
	}
	order.Status = models.OrderStatusSuccess
	order.PaymentMethodID = request.PaymentMethodID
	if err := Order.Update(order); err != nil {
		return err
	}
	if wsErr := SendWsOrderStage(order); wsErr != nil {
		return wsErr
	}
	return nil
}

// HandleOrderSuccess
// orderAmount: gia tri cua order
// actualAmount: gia tri user can phai thanh toan
// missingAmount: gia tri user con thieu
func (s *OrderService) HandleOrderSuccess(order *models.Order, orderItems []*models.OrderItem) *e.AppError {
	couponHistories, chErr := CouponHistory.FindMany(&models.CouponHistoryQuery{
		OrderID: util.NewString(order.ID),
		Status:  util.NewT(models.CouponStatusUsed),
	}, &models.FindManyOptions{Preloads: []string{models.CouponField}})
	if chErr != nil {
		log.Error("HandleOrderSuccess: CouponHistory.FindMany ", chErr)
		return chErr
	}

	paymentMethod, pErr := PaymentMethod.FindByID(order.PaymentMethodID, false, nil)
	if pErr != nil {
		log.Error("HandleOrderSuccess: PaymentMethod.FindByID", pErr)
		return pErr
	}

	user, uErr := User.FindByID(order.UserID, nil)
	if uErr != nil {
		log.Error("HandleOrderSuccess: User.FindByID", pErr)
		return pErr
	}

	courseOwnerID := ""
	courseCuid := ""
	courseID := ""
	for _, orderItem := range orderItems {
		course, cErr := models.Repository.Course(context.TODO()).FindOne(
			&models.CourseQuery{ID: util.NewString(orderItem.EntityID)},
			nil)
		if cErr != nil {
			log.Error("HandleOrderSuccess: Course.FindOneBySchema", cErr)
			return e.NewError400(e.Course_find_one_failed, "find course failed: "+cErr.Error())
		}
		// enroll course

		_, enErr := CourseEnrollment.VerifyCreateCourseEnrollment(orderItem.OrgID, orderItem.OrgSchema, user, course, "", "")
		if enErr != nil {
			log.Error("HandleOrderSuccess: CourseEnrollment.VerifyCreateCourseEnrollment", enErr)
			return e.NewError400(e.Create_course_enrollment_failed, "enroll course failed: "+enErr.Msg)
		}

		courseOwnerID = course.UserID
		courseCuid = course.Cuid
		courseID = course.ID

		org, _ := models.Repository.Organization.FindByID(orderItem.OrgID, nil)

		// add referrers if campaign exist
		if addErr := Referrer.AddPurchasedUserOrderSuccess(user, org, course); addErr != nil {
			return addErr
		}

		emailEvent := models.EnrollCourseSuccessEn
		if course.Props.DefaultLanguage != "" {
			emailEvent = lo.If(course.Props.DefaultLanguage == "vi", models.EnrollCourseSuccessVi).Else(models.EnrollCourseSuccessEn)
		}

		mailParams := communicationdto.MapEmailParams{
			communicationdto.EmailParamCourse:    course,
			communicationdto.EmailParamUserToken: UserToken,
		}

		go func() {
			if _, err := communication.Email.SendEmail(&communicationdto.SendEmailRequest{
				User:        user.IntoComm(),
				Org:         org.IntoComm(),
				Event:       emailEvent.IntoComm(),
				ExtendDatas: mailParams,
				IsQueue:     true,
			}); err != nil {
				log.ErrorWithAlertf("Order.HandleOrderSuccess::Send email failed: %v", err)
			}
		}()
	}

	launchpad, cErr := models.Repository.ClpLaunchpad(context.TODO()).FindOne(&models.LaunchpadQuery{
		Status:         util.NewT(models.LaunchpadSTTSuccess),
		CourseCuid:     &courseCuid,
		IncludeDeleted: util.NewBool(false),
	}, &models.FindOneOptions{})
	if cErr != nil && !models.IsRecordNotFound(cErr) {
		return e.NewError500(e.Course_launchpad_find_one_failed, "Find the launchpad error: "+cErr.Error())
	}

	amount := order.Amount
	ownerAmount := amount
	discountedAmount := amount
	log.Debug("couponHistories", len(couponHistories))
	if len(couponHistories) > 0 {
		ownerAmount = amount.Sub(order.DiscountAmount)
		discountedAmount = amount.Sub(order.DiscountAmount)
		if ownerAmount.IsNegative() {
			ownerAmount = decimal.Zero
		}
	}

	if purchasedTx, tErr := models.Repository.Transaction.FindOne(&models.TransactionQuery{
		EntityID:   &order.ID,
		EntityType: util.NewT(models.OrderModelName),
		Type:       util.NewT(models.TransactionTypeBuy),
	}, nil); tErr != nil && !models.IsRecordNotFound(tErr) {
		return e.NewError500(e.TransactionFindFailed, "Find the purchase transaction error: "+tErr.Error())

	} else if purchasedTx != nil {
		log.Debug("Course: ", courseOwnerID, " -- ", courseCuid, " -- ", courseID, " -- ", amount, " -- ", ownerAmount)
		if order.ReferralCode != "" && ownerAmount.GreaterThan(decimal.Zero) {
			referral, refErr := Referral.CreateReferral(order, courseID, courseCuid, true)
			if refErr != nil {
				return refErr
			}
			ownerAmount = ownerAmount.
				Sub(referral.Ref1Amount).
				Sub(referral.Ref1ShareAmount).
				Sub(referral.Ref2Amount).
				Sub(referral.Ref3Amount)

			if referral.RefLinkLevel1 != nil {
				log.Debug(fmt.Sprintf("Ref1 %s received %s", referral.RefLinkLevel1.UserID, referral.Ref1Amount))
				addReferralAmount(referral.Referral, referral.RefLinkLevel1, referral.Ref1Amount, paymentMethod, purchasedTx)
			}

			if referral.RefLinkLevel2 != nil {
				log.Debug(fmt.Sprintf("Ref2 %s received %s", referral.RefLinkLevel2.UserID, referral.Ref2Amount))
				addReferralAmount(referral.Referral, referral.RefLinkLevel2, referral.Ref2Amount, paymentMethod, purchasedTx)
			}

			if referral.RefLinkLevel3 != nil {
				log.Debug(fmt.Sprintf("Ref3 %s received %s", referral.RefLinkLevel3.UserID, referral.Ref3Amount))
				addReferralAmount(referral.Referral, referral.RefLinkLevel3, referral.Ref3Amount, paymentMethod, purchasedTx)
			}
		}

		if launchpad != nil {
			sharingPercentage := decimal.NewFromFloat(launchpad.FundingGoal.ProfitPercentage)
			sharingTotalAmount := ownerAmount.Mul(sharingPercentage).Div(decimal.NewFromInt(100))
			profitDistribution, appErr := s.calcLaunchpadProfitDistributions(launchpad, sharingTotalAmount)
			if appErr != nil {
				return appErr
			}

			for _, distribution := range profitDistribution {
				log.Debug(fmt.Sprintf("Backer with wallet address %s on received %s", distribution.Address, distribution.Amount))
				addLaunchProfitAmount(launchpad, distribution, paymentMethod, purchasedTx)
			}
		}

		// add owner balance
		wallet, err := Wallet.GetWalletByUserIDAndCurrency(courseOwnerID, paymentMethod.PaymentType)
		if err != nil {
			log.Error("Find Wallet for course owner failed: ", courseOwnerID, err.Msg)
		}
		if wallet != nil {
			log.Debug(fmt.Sprintf("Owner %s received %s", courseOwnerID, ownerAmount))
			if tErr := Transaction.OrderSuccess(&dto.CreateTransactionRequest{
				Wallet:         wallet,
				OrgID:          order.OrgID,
				Amount:         ownerAmount,
				EntityID:       order.ID,
				EntityType:     models.OrderModelName,
				Network:        purchasedTx.Network,
				TxHash:         purchasedTx.TxHash,
				BlockchainTxID: purchasedTx.BlockchainTxID,
			}, paymentMethod); tErr != nil {
				log.Error("Update Amount & add transaction for user failed: ", courseOwnerID, wallet.ID, ownerAmount.String(), tErr.Msg)
			}
		}
	} else {
		log.Debug("Course: ", courseOwnerID, " -- ", courseCuid, " -- ", courseID, " -- ", amount, " -- ", ownerAmount)
		if order.ReferralCode != "" && ownerAmount.GreaterThan(decimal.Zero) {
			referral, refErr := Referral.CreateReferral(order, courseID, courseCuid, true)
			if refErr != nil {
				return refErr
			}
			ownerAmount = ownerAmount.
				Sub(referral.Ref1Amount).
				Sub(referral.Ref1ShareAmount).
				Sub(referral.Ref2Amount).
				Sub(referral.Ref3Amount)

			if referral.RefLinkLevel1 != nil {
				log.Debug(fmt.Sprintf("Ref1 %s received %s", referral.RefLinkLevel1.UserID, referral.Ref1Amount))
				addReferralAmount(referral.Referral, referral.RefLinkLevel1, referral.Ref1Amount, paymentMethod, nil)
			}

			if referral.RefLinkLevel2 != nil {
				log.Debug(fmt.Sprintf("Ref2 %s received %s", referral.RefLinkLevel2.UserID, referral.Ref2Amount))
				addReferralAmount(referral.Referral, referral.RefLinkLevel2, referral.Ref2Amount, paymentMethod, nil)
			}

			if referral.RefLinkLevel3 != nil {
				log.Debug(fmt.Sprintf("Ref3 %s received %s", referral.RefLinkLevel3.UserID, referral.Ref3Amount))
				addReferralAmount(referral.Referral, referral.RefLinkLevel3, referral.Ref3Amount, paymentMethod, nil)
			}
		}

		if launchpad != nil {
			sharingPercentage := decimal.NewFromFloat(launchpad.FundingGoal.ProfitPercentage)
			sharingTotalAmount := ownerAmount.Mul(sharingPercentage).Div(decimal.NewFromInt(100))
			profitDistribution, appErr := s.calcLaunchpadProfitDistributions(launchpad, sharingTotalAmount)
			if appErr != nil {
				return appErr
			}

			for _, distribution := range profitDistribution {
				log.Debug(fmt.Sprintf("Backer with wallet address %s on received %s", distribution.Address, distribution.Amount))
				addLaunchProfitAmount(launchpad, distribution, paymentMethod, nil)
			}
		}

		// add owner balance
		wallet, err := Wallet.GetWalletByUserIDAndCurrency(courseOwnerID, paymentMethod.PaymentType)
		if err != nil {
			log.Error("Find Wallet for course owner failed: ", courseOwnerID, err.Msg)
		}
		if wallet != nil {
			log.Debug(fmt.Sprintf("Owner %s received %s", courseOwnerID, ownerAmount))
			if tErr := Transaction.OrderSuccess(&dto.CreateTransactionRequest{
				Wallet:     wallet,
				OrgID:      order.OrgID,
				Amount:     ownerAmount,
				EntityID:   order.ID,
				EntityType: models.OrderModelName,
			}, paymentMethod); tErr != nil {
				log.Error("Update Amount & add transaction for user failed: ", courseOwnerID, wallet.ID, ownerAmount.String(), tErr.Msg)
			}
		}
	}

	order.Props.DiscountedAmount = discountedAmount
	if err := models.Repository.Order.Update(order, nil); err != nil {
		return e.NewError500(e.OrderUpdateFailed, "Update the order error: "+err.Error())
	}
	return nil
}

func addReferralAmount(
	referral *models.Referral,
	link *models.ReferralLink,
	amount decimal.Decimal,
	paymentMethod *models.PaymentMethod,
	purchasedTx *models.Transaction,
) {
	wallet, err := Wallet.GetWalletByUserIDAndCurrency(link.UserID, paymentMethod.PaymentType)
	if err != nil {
		log.Error("Find Wallet for user failed: ", link.UserID)
	}
	if wallet != nil {
		req := &dto.CreateTransactionRequest{
			Wallet:     wallet,
			OrgID:      link.OrgID,
			Amount:     amount,
			EntityID:   referral.ID,
			EntityType: models.ReferralModelName,
		}
		if purchasedTx != nil {
			req.Network = purchasedTx.Network
			req.TxHash = purchasedTx.TxHash
			req.BlockchainTxID = purchasedTx.BlockchainTxID
		}

		if tErr := Transaction.ReferralEarn(req, paymentMethod); tErr != nil {
			log.Error("Update Amount & add transaction for user failed: ", link.UserID, wallet.ID, amount.String())
		}
	}
}

func addLaunchProfitAmount(
	launchpad *models.ClpLaunchpad,
	distribution *chaindto.ProfitDistribution,
	paymentMethod *models.PaymentMethod,
	purchasedTx *models.Transaction,
) {

	query := &models.WalletQuery{
		Network: util.NewT(models.BlockchainNetwork(distribution.Network)),
	}
	if paymentMethod.Type == models.PaymentMethodTypeOpenEduWallet {
		query.Address = &distribution.Address
		query.Currency = &paymentMethod.PaymentType
	}

	wallet, err := models.Repository.Wallet.FindOne(query, &models.FindOneOptions{})
	if err != nil {
		log.Error("Find Wallet for user failed: ", distribution.Address, distribution.Network)
	}
	if wallet != nil {
		req := &dto.CreateTransactionRequest{
			Wallet:     wallet,
			OrgID:      launchpad.OrgID,
			Amount:     distribution.Amount,
			EntityID:   launchpad.ID,
			EntityType: models.ClpLaunchpadModelName,
		}
		if purchasedTx != nil {
			req.Network = purchasedTx.Network
			req.TxHash = purchasedTx.TxHash
			req.BlockchainTxID = purchasedTx.BlockchainTxID
		}

		if tErr := Transaction.LaunchpadProfitEarn(req, paymentMethod); tErr != nil {
			log.Error("Update Amount & add transaction for user failed: ", wallet.UserID, wallet.ID, distribution.Amount.String())
		}
	}
}

func (s *OrderService) ValidateBeforeProcessPayment(user *models.User, order *models.Order, wallet *models.Wallet) *e.AppError {
	if !order.IsOwnedBy(user) {
		return e.NewError400(e.OrderOwnerRequired, "Require order owner")
	}

	if order.IsSuccess() {
		return e.NewError400(e.OrderAlreadySuccess, "Order already processed")
	}

	if order.IsFailed() {
		return e.NewError400(e.OrderAlreadyFailed, "Order already processed")
	}

	if !wallet.IsOwnedBy(user) {
		return e.NewError400(e.WalletOwnerRequired, "Require wallet owner")
	}

	return nil
}

func (s *OrderService) PayThroughWallet(order *models.Order, wallet *models.Wallet) *e.AppError {
	if order.Currency != wallet.Currency {
		return e.NewError400(e.OrderMismatchCurrencies, "Order currency is "+string(order.Currency)+" but wallet currency is "+string(wallet.Currency))
	}

	if wallet.Type != models.AssetTypeCrypto {
		return e.NewError400(e.OrderMismatchCurrencies, "Only support payment with crypto wallet")
	}

	orderItems, err := models.Repository.OrderItem.FindMany(&models.OrderItemQuery{
		OrderID: &order.ID,
	}, nil)
	if err != nil {
		return e.NewError500(e.OrderPaymentWithWalletFailed, "Find order items error: "+err.Error())
	}

	var courseOwnerID string
	var courseID string
	var courseCuid string
	for _, orderItem := range orderItems {
		course, cErr := models.Repository.Course(context.TODO()).FindOne(
			&models.CourseQuery{ID: util.NewString(orderItem.EntityID)},
			&models.FindOneOptions{Preloads: []string{models.OwnerField}})
		if cErr != nil {
			log.Error("HandleOrderSuccess: Course.FindOneBySchema", cErr)
			return e.NewError400(e.Course_find_one_failed, "find course failed: "+cErr.Error())
		}

		courseOwnerID = course.Owner.ID
		courseID = orderItem.EntityID
		courseCuid = orderItem.EntityCuid
	}

	purchasedTx := &models.Transaction{
		UserID:       order.UserID,
		OrgID:        order.OrgID,
		WalletID:     wallet.ID,
		CurrencyType: wallet.Type,
		Currency:     wallet.Currency,
		Network:      wallet.Network,
		Amount:       order.ActualAmount.Neg(),
		Type:         models.TransactionTypeBuy,
		Status:       models.TransactionStatusPending,
		Data:         models.JSONB{},
		EntityID:     order.ID,
		EntityType:   models.OrderModelName,
	}
	if err = models.Repository.Transaction.Create(purchasedTx, nil); err != nil {
		return e.NewError500(e.TransactionCreateFailed, "Create transaction error: "+err.Error())
	}

	launchpad, cErr := models.Repository.ClpLaunchpad(context.TODO()).FindOne(&models.LaunchpadQuery{
		Status:         util.NewT(models.LaunchpadSTTSuccess),
		CourseCuid:     &courseCuid,
		IncludeDeleted: util.NewBool(false),
	}, &models.FindOneOptions{})
	if cErr != nil && !models.IsRecordNotFound(cErr) {
		return e.NewError500(e.Course_launchpad_find_one_failed, "Find the launchpad error: "+cErr.Error())
	}

	var token chaindto.BlockchainToken
	switch order.Currency {
	case models.CryptoCurrencyUSDT:
		token = chaindto.BlockchainTokenUSDT

	case models.CryptoCurrencyUSDC:
		token = chaindto.BlockchainTokenUSDC

	default:
		return e.NewError400(e.OrderMismatchCurrencies, "Unsupported payment with currency: "+string(order.Currency))
	}

	referralDistributions, ownerDistribution, appErr := s.calcReferralProfitDistributions(order, courseOwnerID, courseCuid, courseID)
	if appErr != nil {
		return appErr
	}

	profitDistributions := referralDistributions
	if launchpad != nil {
		sharingPercentage := decimal.NewFromFloat(launchpad.FundingGoal.ProfitPercentage)
		sharingTotalAmount := ownerDistribution.Amount.Mul(sharingPercentage).Div(decimal.NewFromInt(100))
		launchpadProfitDistributions, aErr := s.calcLaunchpadProfitDistributions(launchpad, sharingTotalAmount)
		if aErr != nil {
			return aErr
		}

		ownerDistribution.Amount = ownerDistribution.Amount.Sub(sharingTotalAmount)
		profitDistributions = append(profitDistributions, ownerDistribution)
		profitDistributions = append(profitDistributions, launchpadProfitDistributions...)
	} else {
		profitDistributions = append(profitDistributions, ownerDistribution)
	}

	if err = openedu_chain.Transaction.Create(&chaindto.CreateTransactionRequest{
		Type: chaindto.TxnPayment,
		Data: chaindto.PaymentRequest{
			WalletID:            wallet.BlockchainWalletID,
			CoreTxID:            purchasedTx.ID,
			Token:               token,
			Amount:              order.ActualAmount,
			ProfitDistributions: profitDistributions,
			IsMainnet:           setting.OpenEduChainSetting.IsMainnet,
		},
	}); err != nil {
		return e.NewError500(e.OrderPaymentWithWalletFailed, "Payment order with wallet ID "+wallet.ID+" error: "+err.Error())
	}

	syncedTx, _ := retry.DoWithData(
		func() (*models.Transaction, error) {
			tx, tErr := models.Repository.Transaction.FindByID(purchasedTx.ID, nil)
			if tErr != nil {
				return nil, tErr
			}

			if !tx.IsPending() {
				return tx, nil
			}

			return nil, fmt.Errorf("transaction is still pending, continue check status")
		},
		retry.Delay(200*time.Millisecond),
	)

	if syncedTx.IsSuccess() {
		order.Status = models.OrderStatusSuccess
		if cErr := models.Cache.WalletEarning.Flush(); cErr != nil {
			log.Errorf("OrderService::PayThroughWallet Delete wallet earning all cache error: %v", cErr)
		}
		return s.Update(order)
	}

	if dErr := models.Repository.Transaction.Delete(syncedTx.ID, nil); dErr != nil {
		log.Errorf("OrderService::PayThroughWallet Delete transaction to rollback failed: %v", dErr)
	}

	switch syncedTx.ErrorCode {
	case openedu_chain.TransactionInsufficientGasFee:
		return e.NewError400(e.OrderPaymentWithWalletFailed, "Insufficient wallet balance to cover the gas fee")

	case openedu_chain.TransactionInsufficientBalance:
		return e.NewError400(e.OrderPaymentWithWalletFailed, "Insufficient wallet balance for payment")

	default:
		return e.NewError500(e.OrderPaymentWithWalletFailed, fmt.Sprintf("Payment failed with error code: %d", syncedTx.ErrorCode))
	}
}

func (s *OrderService) calcLaunchpadProfitDistributions(
	launchpad *models.ClpLaunchpad,
	sharingTotalAmount decimal.Decimal,
) ([]*chaindto.ProfitDistribution, *e.AppError) {
	if !launchpad.IsStatusSuccess() {
		return nil, e.NewError400(e.INVALID_PARAMS, "Launchpad must be success to share profit: "+string(launchpad.Status))
	}

	backers, err := ClpLaunchpad.GetVotingPowersOffChain(launchpad)
	if err != nil {
		return nil, e.NewError500(e.ERROR, "Get list backer and voting powers error: "+err.Error())
	}

	var profitDistributions []*chaindto.ProfitDistribution
	sum := decimal.Zero
	for idx, backer := range backers {
		if idx == len(backers)-1 { // If the last
			profitDistributions = append(profitDistributions, &chaindto.ProfitDistribution{
				Address: backer.Address,
				Network: chaindto.BlockchainNetwork(backer.Network),
				Amount:  sharingTotalAmount.Sub(sum),
			})
			continue
		}

		// profit
		profit := sharingTotalAmount.Mul(decimal.NewFromFloat(backer.VotingPower)).Div(decimal.NewFromInt(100))
		sum = sum.Add(profit)
		profitDistributions = append(profitDistributions, &chaindto.ProfitDistribution{
			Address: backer.Address,
			Network: chaindto.BlockchainNetwork(backer.Network),
			Amount:  profit,
		})
	}

	return profitDistributions, nil
}

func (s *OrderService) calcReferralProfitDistributions(
	order *models.Order,
	courseOwnerID, courseCuid, courseID string,
) ([]*chaindto.ProfitDistribution, *chaindto.ProfitDistribution, *e.AppError) {
	couponHistories, appErr := CouponHistory.FindMany(&models.CouponHistoryQuery{
		OrderID: util.NewString(order.ID),
		Status:  util.NewT(models.CouponStatusUsed),
	}, &models.FindManyOptions{Preloads: []string{models.CouponField}})
	if appErr != nil {
		return nil, nil, appErr
	}

	var profitDistributions []*chaindto.ProfitDistribution

	amount := order.Amount
	ownerAmount := amount
	if len(couponHistories) > 0 {
		ownerAmount = amount.Sub(order.DiscountAmount)
		if ownerAmount.IsNegative() {
			ownerAmount = decimal.Zero
		}
	}

	log.Debug("Course: ", courseOwnerID, " -- ", courseCuid, " -- ", courseID, " -- ", amount, " -- ", ownerAmount)
	var referral *dto.CreateReferralResponse
	if order.ReferralCode != "" && ownerAmount.GreaterThan(decimal.Zero) {
		ref, aErr := Referral.CreateReferral(order, courseID, courseCuid, false)
		if aErr != nil {
			return nil, nil, aErr
		}
		referral = ref
		ownerAmount = ownerAmount.
			Sub(referral.Ref1Amount).
			Sub(referral.Ref1ShareAmount).
			Sub(referral.Ref2Amount).
			Sub(referral.Ref3Amount)

		if referral.RefLinkLevel1 != nil {
			log.Debug(fmt.Sprintf("Ref1 %s received %s", referral.RefLinkLevel1.UserID, referral.Ref1Amount))

			ref1Wallet, err := Wallet.GetWalletByUserIDAndCurrency(referral.RefLinkLevel1.UserID, order.Currency)
			if err != nil {
				return nil, nil, e.NewError500(e.WalletFindFailed, "Find the wallet for ref1 error: "+err.Error())
			}

			profitDistributions = append(profitDistributions, &chaindto.ProfitDistribution{
				Address: ref1Wallet.Address,
				Amount:  referral.Ref1Amount,
			})
		}

		if referral.RefLinkLevel2 != nil {
			log.Debug(fmt.Sprintf("Ref2 %s received %s", referral.RefLinkLevel2.UserID, referral.Ref2Amount))

			ref2Wallet, err := Wallet.GetWalletByUserIDAndCurrency(referral.RefLinkLevel2.UserID, order.Currency)
			if err != nil {
				return nil, nil, e.NewError500(e.WalletFindFailed, "Find the wallet for ref2 error: "+err.Error())
			}

			profitDistributions = append(profitDistributions, &chaindto.ProfitDistribution{
				Address: ref2Wallet.Address,
				Amount:  referral.Ref2Amount,
			})
		}

		if referral.RefLinkLevel3 != nil {
			log.Debug(fmt.Sprintf("Ref3 %s received %s", referral.RefLinkLevel3.UserID, referral.Ref3Amount))

			ref3Wallet, err := Wallet.GetWalletByUserIDAndCurrency(referral.RefLinkLevel3.UserID, order.Currency)
			if err != nil {
				return nil, nil, e.NewError500(e.WalletFindFailed, "Find the wallet for ref3 error: "+err.Error())
			}

			profitDistributions = append(profitDistributions, &chaindto.ProfitDistribution{
				Address: ref3Wallet.Address,
				Amount:  referral.Ref3Amount,
			})
		}
	}

	ownerWallet, err := Wallet.GetWalletByUserIDAndCurrency(courseOwnerID, order.Currency)
	if err != nil {
		return nil, nil, e.NewError500(e.WalletFindFailed, "Find the wallet for course owner error: "+err.Error())
	}

	return profitDistributions, &chaindto.ProfitDistribution{
		Address: ownerWallet.Address,
		Amount:  ownerAmount,
	}, nil
}
