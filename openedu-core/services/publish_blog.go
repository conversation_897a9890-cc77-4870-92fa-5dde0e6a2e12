package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"gorm.io/gorm"
)

func (s *PublishBlogService) FindOne(query *models.PublishBlogQuery, options *models.FindOneOptions) (*models.PublishBlog, *e.AppError) {
	if blog, err := models.Repository.PublishBlog.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Blog_not_found, "blog not found")
		}
		return nil, e.NewError500(e.Blog_find_one_failed, err.Error())
	} else {
		return blog, nil
	}
}

func (s *PublishBlogService) Delete(id string) *e.AppError {
	if err := models.Repository.PublishBlog.Delete(id, nil); err != nil {
		return e.NewError500(e.Course_find_one_failed, err.Error())
	} else {
		return nil
	}
}

func (s *PublishBlogService) AddPublishBlog(blog *models.Blog, org *models.Organization) *e.AppError {

	var publish *models.PublishBlog
	var err error
	publish, err = models.Repository.PublishBlog.FindOne(&models.PublishBlogQuery{
		BlogCuid: util.NewString(blog.Cuid),
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Blog_add_publish_failed, "find publish course failed: "+blog.ID+" "+err.Error())
	}

	if publish == nil {
		publish = &models.PublishBlog{
			BlogCuid:  blog.Cuid,
			OrgID:     org.ID,
			OrgSchema: org.Schema,
			PubDate:   blog.PubDate,
			AuthorId:  blog.AuthorID,
			BlogID:    blog.ID,
			Title:     blog.Title,
		}
		copyBlogData(blog, publish, blog.PubDate)
	} else {
		copyBlogData(blog, publish, blog.PubDate)
	}

	if err := models.Repository.PublishBlog.Upsert(publish, nil); err != nil {
		return e.NewError500(e.Blog_add_publish_failed, err.Error())
	}

	//clear cache
	if err := models.Cache.Blog.DeleteAllBlogByCategory(); err != nil {
		return e.NewError500(e.Cache_delete_by_key_failed, err.Error())
	}

	return nil

}

func copyBlogData(blog *models.Blog, pub *models.PublishBlog, pubDate int) *models.PublishBlog {
	props := pub.Props
	if props == nil {
		props = models.JSONB{}
	}

	histories := []interface{}{}
	if props["histories"] != nil {
		histories = props["histories"].([]interface{})
	}
	histories = append(histories, models.JSONB{
		"version": blog.Version,
		"id":      blog.ID,
		"date":    pubDate,
	})

	props["histories"] = histories
	pub.Props = props
	pub.AuthorId = blog.AuthorID
	pub.BlogID = blog.ID
	pub.Title = blog.Title
	pub.IsPin = blog.IsPin
	pub.Slug = blog.Slug
	pub.Description = blog.Description
	pub.IsAIGenerated = blog.IsAIGenerated
	pub.Locale = blog.Locale
	pub.AIBlogID = blog.AIBlogID
	return pub
}

func (s *PublishBlogService) FindPage(query *models.PublishBlogQuery, options *models.FindPageOptions) ([]*models.PublishBlog, *models.Pagination, *e.AppError) {
	if blogs, pagination, err := models.Repository.PublishBlog.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Publish_course_find_failed, err.Error())
	} else {
		return blogs, pagination, nil
	}
}

func (s *PublishBlogService) UnpublishBlog(blogCuid string) (*models.PublishBlog, *e.AppError) {
	publish, err := models.Repository.PublishBlog.FindOne(&models.PublishBlogQuery{
		BlogCuid: util.NewString(blogCuid),
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Find_blog_failed, "find publish blog failed: "+err.Error())
	}

	if publish != nil {
		publish.PubDate = 0
		if pErr := models.Repository.PublishBlog.Update(publish, nil); pErr != nil {
			return nil, e.NewError500(e.Publish_blog_update_failed, "update publish course org failed: "+err.Error())
		}
	}
	return publish, nil

}

func (s *PublishBlogService) Update(blog *models.PublishBlog) *e.AppError {
	if err := models.Repository.PublishBlog.Update(blog, nil); err != nil {
		return e.NewError500(e.Blog_update_failed, err.Error())
	}
	return nil
}

func (s *PublishBlogService) FindMany(query *models.PublishBlogQuery, options *models.FindManyOptions) ([]*models.PublishBlog, *e.AppError) {
	if blogs, err := models.Repository.PublishBlog.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Blog_publish_find_many_failed, err.Error())
	} else {
		return blogs, nil
	}
}
