package services

import (
	"context"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *QuizService) Create(creator *models.User, quizRequest *dto.QuizRequest) (*models.Quiz, *models.QuizRelation, *e.AppError) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create quiz
	quiz, appErr := s.createQuiz(creator, quizRequest, tx)
	if appErr != nil {
		tx.Rollback()
		return nil, nil, appErr
	}

	// Create quiz relation
	quizRelation, appErr := s.makeQuizRelationFromRequest(quiz, quizRequest)
	if appErr != nil {
		tx.Rollback()
		return nil, nil, appErr
	}

	if err := models.Repository.QuizRelation.Create(quizRelation, tx); err != nil {
		tx.Rollback()
		return nil, nil, e.NewError500(e.Quiz_relation_create_failed, "Create quiz error: "+err.Error())
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, nil, e.NewError500(e.Quiz_create_failed, "Commit transaction error: "+err.Error())
	}

	return quiz, quizRelation, nil
}

func (s *QuizService) CreateMany(creator *models.User, quizRequests []*dto.QuizRequest) ([]*models.QuizWithRelation, *e.AppError) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	quizzes, appErr := s.createQuizzes(creator, quizRequests, tx)
	if appErr != nil {
		tx.Rollback()
		return nil, appErr
	}

	var quizRelations []*models.QuizRelation
	for idx, quizRequest := range quizRequests {
		quizRelation, appErr := s.makeQuizRelationFromRequest(quizzes[idx], quizRequest)
		if appErr != nil {
			tx.Rollback()
			return nil, appErr
		}
		quizRelations = append(quizRelations, quizRelation)
	}

	if err := models.Repository.QuizRelation.CreateMany(quizRelations, tx); err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_relation_create_failed, "Create quiz error: "+err.Error())
	}

	var questions []*models.QuizQuestion
	for idx, quizRequest := range quizRequests {
		quizQuestions, appErr := s.makeQuestionsFromRequest(quizzes[idx], quizRequest)
		if appErr != nil {
			tx.Rollback()
			return nil, appErr
		}

		questions = append(questions, quizQuestions...)
	}
	if err := models.Repository.QuizQuestion.CreateMany(questions, tx); err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_create_failed, "Create quiz questions error: "+err.Error())
	}

	var quizWithRelations []*models.QuizWithRelation
	quizWithRelationsByIDs := make(map[string]*models.QuizWithRelation)
	for idx, quiz := range quizzes {
		quizWithRelation := models.NewQuizWithRelation(quiz, quizRelations[idx])
		quizWithRelationsByIDs[quiz.ID] = quizWithRelation
		quizWithRelations = append(quizWithRelations, quizWithRelation)
	}

	for _, question := range questions {
		if quizWithRelation, found := quizWithRelationsByIDs[question.QuizID]; found {
			quizWithRelation.Questions = append(quizWithRelation.Questions, question)
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_create_failed, "Commit transaction error: "+err.Error())
	}

	return quizWithRelations, nil
}

func (s *QuizService) FindByID(quizID string) (*models.Quiz, *e.AppError) {
	quiz, err := models.Repository.Quiz.FindByID(quizID)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.Quiz_not_found, "Quiz not found: "+err.Error())
		}
		return nil, e.NewError500(e.Quiz_find_failed, "Find quiz by ID error: "+err.Error())
	}
	return quiz, nil
}

func (s *QuizService) FindOne(query *models.QuizQuery, options *models.FindOneOptions) (*models.Quiz, *e.AppError) {
	quiz, err := models.Repository.Quiz.FindOne(query, options)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.Quiz_not_found, "Quiz not found: "+err.Error())
		}
		return nil, e.NewError500(e.Quiz_find_failed, "Find quiz by ID error: "+err.Error())
	}
	return quiz, nil
}

func (s *QuizService) PreloadQuizzesForLessonContents(lessonContents []*models.LessonContent) *e.AppError {
	contentIDs := lo.Map(lessonContents, func(content *models.LessonContent, _ int) string {
		return content.ID
	})
	if len(contentIDs) == 0 {
		return nil
	}

	quizRelationQuery := &models.QuizRelationQuery{
		RelatedEntityType: util.NewT(models.QuizRelationEntityLessonContent),
		RelatedEntityIDIn: contentIDs,
	}
	quizRelations, err := models.Repository.QuizRelation.FindMany(quizRelationQuery, nil)
	if err != nil {
		return e.NewError500(e.LessonContentFindFailed, "Find quiz relations failed: "+err.Error())
	}

	if len(quizRelations) == 0 {
		return nil
	}

	quizIDs := lo.Map(quizRelations, func(quizRelation *models.QuizRelation, _ int) string {
		return quizRelation.QuizID
	})
	quizQuery := &models.QuizQuery{
		IDIn: quizIDs,
	}
	quizzes, err := models.Repository.Quiz.FindMany(quizQuery, &models.FindManyOptions{
		Sort: []string{"create_at ASC"},
		Preloads: []string{
			util.FilesField,
			util.QuestionsField,
		},
	})
	if err != nil {
		return e.NewError500(e.LessonContentFindFailed, "Find quizzes failed: "+err.Error())
	}

	if len(quizzes) == 0 {
		return nil
	}

	quizzesByIDs := make(map[string]*models.Quiz)
	for _, quiz := range quizzes {
		quizzesByIDs[quiz.ID] = quiz
	}

	quizRelationsByContentIDs := make(map[string][]*models.QuizRelation)
	for _, quizRelation := range quizRelations {
		quizRelationsByContentIDs[quizRelation.RelatedEntityID] = append(quizRelationsByContentIDs[quizRelation.RelatedEntityID], quizRelation)
	}

	for _, content := range lessonContents {
		if relations, found := quizRelationsByContentIDs[content.ID]; found {
			for _, relation := range relations {
				if quiz, ok := quizzesByIDs[relation.QuizID]; ok {
					content.Quizzes = append(content.Quizzes, models.NewQuizWithRelation(quiz, relation))
				}
			}
		}
	}
	return nil
}

func (s *QuizService) Update(quizRequest *dto.QuizRequest) (*models.Quiz, *models.QuizRelation, *e.AppError) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	quiz, err := models.Repository.Quiz.FindByID(quizRequest.ID)
	if err != nil {
		if models.IsRecordNotFound(err) {
			tx.Rollback()
			return nil, nil, e.NewError404(e.Quiz_not_found, "Quiz not found: "+err.Error())
		}
		tx.Rollback()
		return nil, nil, e.NewError500(e.Quiz_find_failed, "Find quiz by ID error: "+err.Error())
	}

	// Override new information from request
	quiz.Title = quizRequest.Title
	quiz.Description = quizRequest.Description
	quiz.Files = lo.Map(quizRequest.Files, func(simpleFile *models.SimpleFile, _ int) *models.File {
		return &models.File{Model: models.Model{ID: simpleFile.ID}}
	})
	quiz.Settings = models.QuizSettings{
		ShowCorrectAnswersEnabled:      quizRequest.Settings.ShowCorrectAnswersEnabled,
		ShuffleQuestionsEnabled:        quizRequest.Settings.ShuffleQuestionsEnabled,
		ShuffleChoicesEnabled:          quizRequest.Settings.ShuffleChoicesEnabled,
		TimeLimitEnabled:               quizRequest.Settings.TimeLimitEnabled,
		TimeLimitType:                  quizRequest.Settings.TimeLimitType,
		TimeLimit:                      quizRequest.Settings.TimeLimit,
		SubmissionLimitEnabled:         quizRequest.Settings.SubmissionLimitEnabled,
		SubmissionLimit:                quizRequest.Settings.SubmissionLimit,
		PassCriteria:                   quizRequest.Settings.PassCriteria,
		MinPercentageToPass:            quizRequest.Settings.MinPercentageToPass,
		MinCorrectAnswersToPass:        quizRequest.Settings.MinCorrectAnswersToPass,
		TimeBonusPointsEnabled:         quizRequest.Settings.TimeBonusPointsEnabled,
		TimeBonusPointsPerSecond:       quizRequest.Settings.TimeBonusPointsPerSecond,
		StreakBonusEnabled:             quizRequest.Settings.StreakBonusEnabled,
		StreakBonusType:                quizRequest.Settings.StreakBonusType,
		StreakBonusPercentageIncrement: quizRequest.Settings.StreakBonusPercentageIncrement,
		StreakBonusMaxPercentage:       quizRequest.Settings.StreakBonusMaxPercentage,
		PenaltyPointsEnabled:           quizRequest.Settings.PenaltyPointsEnabled,
		PenaltyPointsPerWrongAnswer:    quizRequest.Settings.PenaltyPointsPerWrongAnswer,
	}

	if err = models.Repository.Quiz.Update(quiz, tx); err != nil {
		tx.Rollback()
		return nil, nil, e.NewError500(e.Quiz_update_failed, "Update quiz error: "+err.Error())
	}

	quizRelation, appErr := s.makeQuizRelationFromRequest(quiz, quizRequest)
	if appErr != nil {
		tx.Rollback()
		return nil, nil, appErr
	}

	// Create quiz relation if not exists
	if _, err = models.Repository.QuizRelation.FindOne(&models.QuizRelationQuery{
		QuizID:            &quizRelation.QuizID,
		RelatedEntityType: &quizRelation.RelatedEntityType,
		RelatedEntityID:   &quizRelation.RelatedEntityID,
		RelationType:      &quizRelation.RelationType,
	}, nil); err != nil {
		if !models.IsRecordNotFound(err) {
			tx.Rollback()
			return nil, nil, e.NewError500(e.Quiz_relation_create_failed, "Check existing quiz relation error: "+err.Error())
		}
		if err = models.Repository.QuizRelation.Create(quizRelation, tx); err != nil {
			tx.Rollback()
			return nil, nil, e.NewError500(e.Quiz_relation_create_failed, "Create quiz error: "+err.Error())
		}
	}

	var toCreateQuestions []*models.QuizQuestion
	var toUpdateQuestions []*models.QuizQuestion
	seenQuestionIDs := make(map[string]struct{})
	questionsFromRequest, appErr := s.makeQuestionsFromRequest(quiz, quizRequest)
	if appErr != nil {
		tx.Rollback()
		return nil, nil, appErr
	}
	for _, question := range questionsFromRequest {
		if question.ID != "" {
			// Update
			seenQuestionIDs[question.ID] = struct{}{}
			toUpdateQuestions = append(toUpdateQuestions, question)
		} else {
			// Create
			toCreateQuestions = append(toCreateQuestions, question)
		}
	}

	// Delete quiz questions
	var toDeleteQuestionIDs []string
	for _, question := range quiz.Questions {
		if _, found := seenQuestionIDs[question.ID]; !found {
			toDeleteQuestionIDs = append(toDeleteQuestionIDs, question.ID)
		}
	}

	if len(toDeleteQuestionIDs) > 0 {
		if _, err = models.Repository.QuizQuestion.DeleteMany(&models.QuizQuestionQuery{IDIn: toDeleteQuestionIDs}, tx); err != nil {
			tx.Rollback()
			return nil, nil, e.NewError500(e.Quiz_update_failed, "Delete quiz questions error: "+err.Error())
		}
	}

	// Create quiz questions
	if len(toCreateQuestions) > 0 {
		if err = models.Repository.QuizQuestion.CreateMany(toCreateQuestions, tx); err != nil {
			tx.Rollback()
			return nil, nil, e.NewError500(e.Quiz_update_failed, "Delete quiz questions error: "+err.Error())
		}
	}

	// Update quiz questions
	for _, question := range toUpdateQuestions {
		if err = models.Repository.QuizQuestion.Update(question, tx); err != nil {
			tx.Rollback()
			return nil, nil, e.NewError500(e.Quiz_update_failed, "Update quiz questions error: "+err.Error())
		}
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, nil, e.NewError500(e.Quiz_create_failed, "Commit transaction error: "+err.Error())
	}

	return quiz, quizRelation, nil
}

func (s *QuizService) UpdateMany(quizRequests []*dto.QuizRequest) ([]*models.QuizWithRelation, *e.AppError) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Check quizzes need to update is existing
	quizIDs := lo.Map(quizRequests, func(quizRequest *dto.QuizRequest, _ int) string {
		return quizRequest.ID
	})
	quizQuery := &models.QuizQuery{
		IDIn: quizIDs,
	}
	quizzes, err := models.Repository.Quiz.FindMany(quizQuery, &models.FindManyOptions{
		Preloads: []string{util.FilesField, util.QuestionsField},
	})
	if err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_update_failed, "Find quizzes for updating error: "+err.Error())
	}

	quizzesByIDs := make(map[string]*models.Quiz)
	for _, quiz := range quizzes {
		quizzesByIDs[quiz.ID] = quiz
	}

	for _, quizRequest := range quizRequests {
		if quiz, found := quizzesByIDs[quizRequest.ID]; !found {
			tx.Rollback()
			return nil, e.NewError400(e.Quiz_invalid_data, "Quiz not found, ID: "+quizRequest.ID)
		} else {
			// Override new information from request
			quiz.Title = quizRequest.Title
			quiz.Description = quizRequest.Description
			quiz.Files = lo.Map(quizRequest.Files, func(simpleFile *models.SimpleFile, _ int) *models.File {
				return &models.File{Model: models.Model{ID: simpleFile.ID}}
			})
			quiz.Settings = models.QuizSettings{
				ShowCorrectAnswersEnabled:      quizRequest.Settings.ShowCorrectAnswersEnabled,
				ShuffleQuestionsEnabled:        quizRequest.Settings.ShuffleQuestionsEnabled,
				ShuffleChoicesEnabled:          quizRequest.Settings.ShuffleChoicesEnabled,
				TimeLimitEnabled:               quizRequest.Settings.TimeLimitEnabled,
				TimeLimitType:                  quizRequest.Settings.TimeLimitType,
				TimeLimit:                      quizRequest.Settings.TimeLimit,
				SubmissionLimitEnabled:         quizRequest.Settings.SubmissionLimitEnabled,
				SubmissionLimit:                quizRequest.Settings.SubmissionLimit,
				PassCriteria:                   quizRequest.Settings.PassCriteria,
				MinPercentageToPass:            quizRequest.Settings.MinPercentageToPass,
				MinCorrectAnswersToPass:        quizRequest.Settings.MinCorrectAnswersToPass,
				TimeBonusPointsEnabled:         quizRequest.Settings.TimeBonusPointsEnabled,
				TimeBonusPointsPerSecond:       quizRequest.Settings.TimeBonusPointsPerSecond,
				StreakBonusEnabled:             quizRequest.Settings.StreakBonusEnabled,
				StreakBonusType:                quizRequest.Settings.StreakBonusType,
				StreakBonusPercentageIncrement: quizRequest.Settings.StreakBonusPercentageIncrement,
				StreakBonusMaxPercentage:       quizRequest.Settings.StreakBonusMaxPercentage,
				PenaltyPointsEnabled:           quizRequest.Settings.PenaltyPointsEnabled,
				PenaltyPointsPerWrongAnswer:    quizRequest.Settings.PenaltyPointsPerWrongAnswer,
			}
		}
	}

	// Update quizzes
	for _, quiz := range quizzes {
		if err = models.Repository.Quiz.Update(quiz, tx); err != nil {
			tx.Rollback()
			return nil, e.NewError500(e.Quiz_update_failed, "Update quiz error: "+err.Error())
		}
	}

	// Create quiz relations
	var quizRelationsToCreate []*models.QuizRelation
	for _, quizRequest := range quizRequests {
		quizRelationsToCreate = append(quizRelationsToCreate, &models.QuizRelation{
			QuizID:            quizRequest.ID,
			RelatedEntityType: quizRequest.RelatedEntityType,
			RelatedEntityID:   quizRequest.RelatedEntityID,
			RelationType:      quizRequest.RelationType,
			TriggerConditions: models.QuizTriggerConditions{
				IsTriggeredByTimestamp:     quizRequest.TriggerConditions.IsTriggeredByTimestamp,
				Timestamp:                  quizRequest.TriggerConditions.Timestamp,
				IsTriggerByReachPageNumber: quizRequest.TriggerConditions.IsTriggerByReachPageNumber,
				PageNumber:                 quizRequest.TriggerConditions.PageNumber,
				ShowAtPercentage:           quizRequest.TriggerConditions.ShowAtPercentage,
			},
		})
	}
	if len(quizRelationsToCreate) > 0 {
		if err = models.Repository.QuizRelation.CreateMany(quizRelationsToCreate, tx); err != nil {
			tx.Rollback()
			return nil, e.NewError500(e.Quiz_update_failed, "Update quiz error: "+err.Error())
		}
	}

	var toCreateQuestions []*models.QuizQuestion
	var toUpdateQuestions []*models.QuizQuestion
	seenQuestionIDs := make(map[string]struct{})
	for _, quizRequest := range quizRequests {
		if quiz, found := quizzesByIDs[quizRequest.ID]; !found {
			tx.Rollback()
			return nil, e.NewError400(e.Quiz_invalid_data, "Quiz not found, ID: "+quizRequest.ID)
		} else {
			questions, appErr := s.makeQuestionsFromRequest(quiz, quizRequest)
			if appErr != nil {
				tx.Rollback()
				return nil, appErr
			}
			for _, question := range questions {
				if question.ID != "" {
					// Update
					seenQuestionIDs[question.ID] = struct{}{}
					toUpdateQuestions = append(toUpdateQuestions, question)
				} else {
					// Create
					toCreateQuestions = append(toCreateQuestions, question)
				}
			}
		}
	}

	// Delete quiz questions
	var toDeleteQuestionIDs []string
	for _, quiz := range quizzes {
		for _, question := range quiz.Questions {
			if _, found := seenQuestionIDs[question.ID]; !found {
				toDeleteQuestionIDs = append(toDeleteQuestionIDs, question.ID)
			}
		}
	}

	if len(toDeleteQuestionIDs) > 0 {
		if _, err = models.Repository.QuizQuestion.DeleteMany(&models.QuizQuestionQuery{IDIn: toDeleteQuestionIDs}, tx); err != nil {
			tx.Rollback()
			return nil, e.NewError500(e.Quiz_update_failed, "Delete quiz questions error: "+err.Error())
		}
	}

	// Create quiz questions
	if len(toCreateQuestions) > 0 {
		if err = models.Repository.QuizQuestion.CreateMany(toCreateQuestions, tx); err != nil {
			tx.Rollback()
			return nil, e.NewError500(e.Quiz_update_failed, "Delete quiz questions error: "+err.Error())
		}
	}

	// Update quiz questions
	for _, question := range toUpdateQuestions {
		if err = models.Repository.QuizQuestion.Update(question, tx); err != nil {
			tx.Rollback()
			return nil, e.NewError500(e.Quiz_update_failed, "Update quiz questions error: "+err.Error())
		}
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_update_failed, "Commit transaction error: "+err.Error())
	}

	var quizzesWithRelations []*models.QuizWithRelation
	for _, quizRequest := range quizRequests {
		if quiz, found := quizzesByIDs[quizRequest.ID]; found {
			quizzesWithRelations = append(quizzesWithRelations, models.NewQuizWithRelation(quiz, &models.QuizRelation{
				QuizID:            quizRequest.ID,
				RelatedEntityType: quizRequest.RelatedEntityType,
				RelatedEntityID:   quizRequest.RelatedEntityID,
				RelationType:      quizRequest.RelationType,
				TriggerConditions: models.QuizTriggerConditions{
					IsTriggeredByTimestamp:     quizRequest.TriggerConditions.IsTriggeredByTimestamp,
					Timestamp:                  quizRequest.TriggerConditions.Timestamp,
					IsTriggerByReachPageNumber: quizRequest.TriggerConditions.IsTriggerByReachPageNumber,
					PageNumber:                 quizRequest.TriggerConditions.PageNumber,
					ShowAtPercentage:           quizRequest.TriggerConditions.ShowAtPercentage,
				},
			}))
		}
	}
	return quizzesWithRelations, nil
}

func (s *QuizService) ConvertQuizToQuizRequest(quizWithRelation *models.QuizWithRelation) *dto.QuizRequest {
	return &dto.QuizRequest{
		OrgID:             quizWithRelation.Quiz.OrgID,
		UID:               &quizWithRelation.UID,
		Version:           util.NewInt(quizWithRelation.Version + 1),
		RelatedEntityID:   quizWithRelation.RelatedEntityID,
		RelatedEntityType: quizWithRelation.RelatedEntityType,
		RelationType:      quizWithRelation.RelationType,
		TriggerConditions: dto.QuizTriggerConditionsRequest{
			IsTriggeredByTimestamp:     quizWithRelation.TriggerConditions.IsTriggeredByTimestamp,
			Timestamp:                  quizWithRelation.TriggerConditions.Timestamp,
			IsTriggerByReachPageNumber: quizWithRelation.TriggerConditions.IsTriggerByReachPageNumber,
			PageNumber:                 quizWithRelation.TriggerConditions.PageNumber,
			ShowAtPercentage:           quizWithRelation.TriggerConditions.ShowAtPercentage,
		},
		Title:       quizWithRelation.Title,
		Description: quizWithRelation.Description,
		Files: lo.Map(quizWithRelation.Files, func(file *models.File, index int) *models.SimpleFile {
			return file.Sanitize()
		}),
		Settings: dto.QuizSettingsRequest{
			ShowCorrectAnswersEnabled:      quizWithRelation.Settings.ShowCorrectAnswersEnabled,
			ShuffleQuestionsEnabled:        quizWithRelation.Settings.ShuffleQuestionsEnabled,
			ShuffleChoicesEnabled:          quizWithRelation.Settings.ShuffleChoicesEnabled,
			TimeLimitEnabled:               quizWithRelation.Settings.TimeLimitEnabled,
			TimeLimitType:                  quizWithRelation.Settings.TimeLimitType,
			TimeLimit:                      quizWithRelation.Settings.TimeLimit,
			SubmissionLimitEnabled:         quizWithRelation.Settings.SubmissionLimitEnabled,
			SubmissionLimit:                quizWithRelation.Settings.SubmissionLimit,
			PassCriteria:                   quizWithRelation.Settings.PassCriteria,
			MinPercentageToPass:            quizWithRelation.Settings.MinPercentageToPass,
			MinCorrectAnswersToPass:        quizWithRelation.Settings.MinCorrectAnswersToPass,
			TimeBonusPointsEnabled:         quizWithRelation.Settings.TimeBonusPointsEnabled,
			TimeBonusPointsPerSecond:       quizWithRelation.Settings.TimeBonusPointsPerSecond,
			StreakBonusEnabled:             quizWithRelation.Settings.StreakBonusEnabled,
			StreakBonusType:                quizWithRelation.Settings.StreakBonusType,
			StreakBonusPercentageIncrement: quizWithRelation.Settings.StreakBonusPercentageIncrement,
			StreakBonusMaxPercentage:       quizWithRelation.Settings.StreakBonusMaxPercentage,
			PenaltyPointsEnabled:           quizWithRelation.Settings.PenaltyPointsEnabled,
			PenaltyPointsPerWrongAnswer:    quizWithRelation.Settings.PenaltyPointsPerWrongAnswer,
		},
		Questions: lo.Map(quizWithRelation.Questions, func(question *models.QuizQuestion, _ int) *dto.QuizQuestionRequest {
			return &dto.QuizQuestionRequest{
				Title:       question.Title,
				Description: question.Description,
				Text:        question.Text,
				Files: lo.Map(quizWithRelation.Files, func(file *models.File, _ int) *models.SimpleFile {
					return file.Sanitize()
				}),
				Type:        question.Type,
				Explanation: question.Explanation,
				Points:      question.Points,
				Items: lo.Map(question.Items, func(item *models.QuizQuestionItem, itemIdx int) *dto.QuizQuestionItemRequest {
					return &dto.QuizQuestionItemRequest{
						Text:   item.Text,
						FileID: item.FileID,
						Side:   item.Side,
					}
				}),
				CorrectItemSets: lo.Map(question.CorrectItemSets, func(items models.QuizQuestionItems, _ int) []*dto.QuizQuestionItemRequest {
					return lo.Map(items, func(item *models.QuizQuestionItem, _ int) *dto.QuizQuestionItemRequest {
						return &dto.QuizQuestionItemRequest{
							Text:   item.Text,
							FileID: item.FileID,
							Side:   item.Side,
						}
					})
				}),
				Settings: dto.QuizQuestionSettingsRequest{
					TimeLimit: question.Settings.TimeLimit,
				},
			}
		}),
	}
}

func (s *QuizService) Duplicate(user *models.User, quiz *models.Quiz, newRelationRequest *dto.QuizRelationRequest) (*models.Quiz, *models.QuizRelation, *e.AppError) {
	quizRelation := &models.QuizRelation{
		QuizID:            quiz.ID,
		RelatedEntityType: newRelationRequest.RelatedEntityType,
		RelatedEntityID:   newRelationRequest.RelatedEntityID,
		RelationType:      newRelationRequest.RelationType,
		TriggerConditions: models.QuizTriggerConditions{
			IsTriggeredByTimestamp:     newRelationRequest.TriggerConditions.IsTriggeredByTimestamp,
			Timestamp:                  newRelationRequest.TriggerConditions.Timestamp,
			IsTriggerByReachPageNumber: newRelationRequest.TriggerConditions.IsTriggerByReachPageNumber,
			PageNumber:                 newRelationRequest.TriggerConditions.PageNumber,
			ShowAtPercentage:           newRelationRequest.TriggerConditions.ShowAtPercentage,
		},
	}
	return s.Create(user, s.ConvertQuizToQuizRequest(models.NewQuizWithRelation(quiz, quizRelation)))
}

func (s *QuizService) FindQuizRelationByLessonContent(lessonContent *models.LessonContent) ([]*models.QuizRelation, *e.AppError) {
	quizRelations, err := models.Repository.QuizRelation.FindMany(
		&models.QuizRelationQuery{
			RelatedEntityType: util.NewT(models.QuizRelationEntityLessonContent),
			RelatedEntityID:   &lessonContent.ID,
		},
		&models.FindManyOptions{Sort: []string{"quiz_id desc"}},
	)
	if err != nil {
		return nil, e.NewError500(e.Quiz_find_by_lesson_failed, "Find quiz relations failed: "+err.Error())
	}
	return quizRelations, nil
}

func (s *QuizService) Delete(quiz *models.Quiz) *e.AppError {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if _, err := models.Repository.QuizRelation.DeleteMany(&models.QuizRelationQuery{QuizID: &quiz.ID}, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Quiz_relation_delete_failed, "Delete quiz relations error: "+err.Error())
	}

	if err := models.Repository.Quiz.Delete(quiz.ID, tx); err != nil {
		tx.Rollback()
		return e.NewError500(e.Quiz_delete_failed, "Delete quiz error: "+err.Error())
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return e.NewError500(e.Quiz_delete_failed, "Commit transaction error: "+err.Error())
	}

	return nil
}

func (s *QuizService) CheckUserCanDoQuiz(user *models.User, quiz *models.Quiz) *e.AppError {
	if quiz.Settings.SubmissionLimitEnabled {
		submissionCount, err := models.Repository.QuizSubmission.Count(&models.QuizSubmissionQuery{
			UserID: &user.ID,
			QuizID: &quiz.ID,
		})
		if err != nil {
			return e.NewError500(e.Quiz_check_submission_limit_failed, "Find list user's submissions on quiz error: "+err.Error())
		}

		if submissionCount >= int64(quiz.Settings.SubmissionLimit) {
			return e.NewError400(e.Quiz_submission_limit_exceeded, fmt.Sprintf("You can only submit %d times for this quiz", quiz.Settings.SubmissionLimit))
		}
	}
	return nil
}

func (s *QuizService) createQuiz(creator *models.User, quizRequest *dto.QuizRequest, tx *gorm.DB) (*models.Quiz, *e.AppError) {
	quiz, appErr := s.makeQuizFromRequest(creator, quizRequest)
	if appErr != nil {
		return nil, appErr
	}

	if err := models.Repository.Quiz.Create(quiz, tx); err != nil {
		return nil, e.NewError500(e.Quiz_create_failed, "Create quiz error: "+err.Error())
	}

	questions, appErr := s.makeQuestionsFromRequest(quiz, quizRequest)
	if appErr != nil {
		return nil, appErr
	}

	if err := models.Repository.QuizQuestion.CreateMany(questions, tx); err != nil {
		return nil, e.NewError500(e.Quiz_create_failed, "Create quiz questions error: "+err.Error())
	}

	quiz.Questions = questions
	return quiz, nil
}

func (s *QuizService) createQuizzes(creator *models.User, quizRequests []*dto.QuizRequest, tx *gorm.DB) ([]*models.Quiz, *e.AppError) {
	var quizzes []*models.Quiz
	for _, quizRequest := range quizRequests {
		quiz, appErr := s.makeQuizFromRequest(creator, quizRequest)
		if appErr != nil {
			return nil, appErr
		}
		quizzes = append(quizzes, quiz)
	}

	if err := models.Repository.Quiz.CreateMany(quizzes, tx); err != nil {
		return nil, e.NewError500(e.Quiz_create_failed, "Create quizzes error: "+err.Error())
	}
	return quizzes, nil
}

func (s *QuizService) checkQuizIsValid(quiz *models.Quiz) *e.AppError {
	// Check quiz is valid
	if err := quiz.CheckValid(); err != nil {
		return e.NewError400(e.Quiz_invalid_data, "Invalid data: "+err.Error())
	}
	return nil
}

func (s *QuizService) checkQuizRelationIsValid(quizRelation *models.QuizRelation) *e.AppError {
	if err := quizRelation.CheckValid(); err != nil {
		return e.NewError400(e.Quiz_relation_invalid_data, "Invalid data: "+err.Error())
	}

	// Check lesson content exists and doesn't have another quiz
	if quizRelation.IsRelationToLessonContent() {
		lessonContent, err := models.Repository.LessonContent(context.TODO()).FindByID(quizRelation.RelatedEntityID, nil)
		if err != nil {
			if models.IsRecordNotFound(err) {
				return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content not found: "+err.Error())
			}
			return e.NewError500(e.Quiz_create_failed, "Check lesson content exists error: "+err.Error())
		}

		if quizRelation.IsRelationIs() {
			if !lessonContent.IsQuizLesson() {
				return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content ID "+lessonContent.ID+" is not quiz type")
			}
			existingQuizRelation, err := models.Repository.QuizRelation.FindOne(&models.QuizRelationQuery{
				RelatedEntityType: &quizRelation.RelatedEntityType,
				RelatedEntityID:   &quizRelation.RelatedEntityID,
				RelationType:      &quizRelation.RelationType,
			}, nil)
			if err != nil && !models.IsRecordNotFound(err) {
				return e.NewError500(e.Quiz_create_failed, "Check lesson content already be another quiz error: "+err.Error())
			}
			if existingQuizRelation != nil {
				return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content already be another quiz ID "+existingQuizRelation.QuizID)
			}

		} else if quizRelation.IsRelationTriggerBy() {
			switch lessonContent.Type {
			case models.LessonTypeVideo:
				if !quizRelation.IsTriggeredByTimestamp() {
					return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content is video type but trigger by timestamp is not enabled")
				}
			case models.LessonTypePDF:
				if !quizRelation.IsTriggeredByReachPageNumber() {
					return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content is video type but trigger by reach page number is not enabled")
				}
			}
		}
	}
	return nil
}

func (s *QuizService) checkQuizRelationIsValidBySchema(schema string, quizRelation *models.QuizRelation) *e.AppError {
	if err := quizRelation.CheckValid(); err != nil {
		return e.NewError400(e.Quiz_relation_invalid_data, "Invalid data: "+err.Error())
	}

	// Check lesson content exists and doesn't have another quiz
	if quizRelation.IsRelationToLessonContent() {
		lessonContent, err := models.Repository.LessonContent(context.TODO()).FindOne(&models.LessonContentQuery{
			ID: &quizRelation.RelatedEntityID,
		}, nil)
		if err != nil {
			if models.IsRecordNotFound(err) {
				return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content not found: "+err.Error())
			}
			return e.NewError500(e.Quiz_create_failed, "Check lesson content exists error: "+err.Error())
		}

		if quizRelation.IsRelationIs() {
			if !lessonContent.IsQuizLesson() {
				return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content ID "+lessonContent.ID+" is not quiz type")
			}
			existingQuizRelation, err := models.Repository.QuizRelation.FindOne(&models.QuizRelationQuery{
				RelatedEntityType: &quizRelation.RelatedEntityType,
				RelatedEntityID:   &quizRelation.RelatedEntityID,
				RelationType:      &quizRelation.RelationType,
			}, nil)
			if err != nil && !models.IsRecordNotFound(err) {
				return e.NewError500(e.Quiz_create_failed, "Check lesson content already be another quiz error: "+err.Error())
			}
			if existingQuizRelation != nil {
				return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content already be another quiz ID "+existingQuizRelation.QuizID)
			}

		} else if quizRelation.IsRelationTriggerBy() {
			switch lessonContent.Type {
			case models.LessonTypeVideo:
				if !quizRelation.IsTriggeredByTimestamp() {
					return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content is video type but trigger by timestamp is not enabled")
				}
			case models.LessonTypePDF:
				if !quizRelation.IsTriggeredByReachPageNumber() {
					return e.NewError400(e.Quiz_relation_invalid_data, "Lesson content is video type but trigger by reach page number is not enabled")
				}
			}
		}
	}
	return nil
}

func (s *QuizService) makeQuizFromRequest(creator *models.User, quizRequest *dto.QuizRequest) (*models.Quiz, *e.AppError) {
	quiz := &models.Quiz{
		OrgID:       quizRequest.OrgID,
		Model:       models.Model{ID: quizRequest.ID},
		Title:       quizRequest.Title,
		Description: quizRequest.Description,
		CreatorID:   creator.ID,
		Files: lo.Map(quizRequest.Files, func(simpleFile *models.SimpleFile, _ int) *models.File {
			return &models.File{Model: models.Model{ID: simpleFile.ID}}
		}),
		Settings: models.QuizSettings{
			ShowCorrectAnswersEnabled:      quizRequest.Settings.ShowCorrectAnswersEnabled,
			ShuffleQuestionsEnabled:        quizRequest.Settings.ShuffleQuestionsEnabled,
			ShuffleChoicesEnabled:          quizRequest.Settings.ShuffleChoicesEnabled,
			TimeLimitEnabled:               quizRequest.Settings.TimeLimitEnabled,
			TimeLimitType:                  quizRequest.Settings.TimeLimitType,
			TimeLimit:                      quizRequest.Settings.TimeLimit,
			SubmissionLimitEnabled:         quizRequest.Settings.SubmissionLimitEnabled,
			SubmissionLimit:                quizRequest.Settings.SubmissionLimit,
			PassCriteria:                   quizRequest.Settings.PassCriteria,
			MinPercentageToPass:            quizRequest.Settings.MinPercentageToPass,
			MinCorrectAnswersToPass:        quizRequest.Settings.MinCorrectAnswersToPass,
			TimeBonusPointsEnabled:         quizRequest.Settings.TimeBonusPointsEnabled,
			TimeBonusPointsPerSecond:       quizRequest.Settings.TimeBonusPointsPerSecond,
			StreakBonusEnabled:             quizRequest.Settings.StreakBonusEnabled,
			StreakBonusType:                quizRequest.Settings.StreakBonusType,
			StreakBonusPercentageIncrement: quizRequest.Settings.StreakBonusPercentageIncrement,
			StreakBonusMaxPercentage:       quizRequest.Settings.StreakBonusMaxPercentage,
			PenaltyPointsEnabled:           quizRequest.Settings.PenaltyPointsEnabled,
			PenaltyPointsPerWrongAnswer:    quizRequest.Settings.PenaltyPointsPerWrongAnswer,
		},
	}

	if quizRequest.UID != nil {
		quiz.UID = *quizRequest.UID
	} else {
		quiz.UID = util.GenerateId()
	}

	if quizRequest.Version != nil {
		quiz.Version = *quizRequest.Version
	} else {
		quiz.Version = 1
	}

	if appErr := s.checkQuizIsValid(quiz); appErr != nil {
		return nil, appErr
	}

	return quiz, nil
}

func (s *QuizService) makeQuizRelationFromRequest(quiz *models.Quiz, quizRequest *dto.QuizRequest) (*models.QuizRelation, *e.AppError) {
	quizRelation := &models.QuizRelation{
		QuizID:            quiz.ID,
		RelatedEntityType: quizRequest.RelatedEntityType,
		RelatedEntityID:   quizRequest.RelatedEntityID,
		RelationType:      quizRequest.RelationType,
		TriggerConditions: models.QuizTriggerConditions{
			IsTriggeredByTimestamp:     quizRequest.TriggerConditions.IsTriggeredByTimestamp,
			Timestamp:                  quizRequest.TriggerConditions.Timestamp,
			IsTriggerByReachPageNumber: quizRequest.TriggerConditions.IsTriggerByReachPageNumber,
			PageNumber:                 quizRequest.TriggerConditions.PageNumber,
			ShowAtPercentage:           quizRequest.TriggerConditions.ShowAtPercentage,
		},
	}
	if appErr := s.checkQuizRelationIsValid(quizRelation); appErr != nil {
		return nil, appErr
	}
	return quizRelation, nil
}

func (s *QuizService) makeQuizRelationFromRequestBySchema(schema string, quiz *models.Quiz, quizRequest *dto.QuizRequest) (*models.QuizRelation, *e.AppError) {
	quizRelation := &models.QuizRelation{
		QuizID:            quiz.ID,
		RelatedEntityType: quizRequest.RelatedEntityType,
		RelatedEntityID:   quizRequest.RelatedEntityID,
		RelationType:      quizRequest.RelationType,
		TriggerConditions: models.QuizTriggerConditions{
			IsTriggeredByTimestamp:     quizRequest.TriggerConditions.IsTriggeredByTimestamp,
			Timestamp:                  quizRequest.TriggerConditions.Timestamp,
			IsTriggerByReachPageNumber: quizRequest.TriggerConditions.IsTriggerByReachPageNumber,
			PageNumber:                 quizRequest.TriggerConditions.PageNumber,
			ShowAtPercentage:           quizRequest.TriggerConditions.ShowAtPercentage,
		},
	}
	if appErr := s.checkQuizRelationIsValidBySchema(schema, quizRelation); appErr != nil {
		return nil, appErr
	}
	return quizRelation, nil
}

func (s *QuizService) makeQuestionsFromRequest(quiz *models.Quiz, quizRequest *dto.QuizRequest) ([]*models.QuizQuestion, *e.AppError) {
	var questions []*models.QuizQuestion
	for questionIdx, questionParams := range quizRequest.Questions {
		//if questionParams.Settings.TimeLimitEnabled && quiz.Settings.TimeLimitEnabled {
		//	return nil, e.NewError400(e.Quiz_invalid_data, "Quiz has time limit settings enabled. Time limits can only be enabled for the quiz or on questions")
		//}

		var items models.QuizQuestionItems
		itemsMap := map[string]*models.QuizQuestionItem{}
		for questionItemIdx, questionItemParams := range questionParams.Items {
			questionItem := &models.QuizQuestionItem{
				ID: util.GenerateId(),
			}
			switch questionParams.Type {
			case models.QuizQuestionTypeSingleChoice,
				models.QuizQuestionTypeMultipleChoice:
				questionItem.Type = models.QuizQuestionItemTypeChoice
				questionItem.Text = questionItemParams.Text
				questionItem.FileID = questionItemParams.FileID
				questionItem.Order = questionItemIdx + 1

			case models.QuizQuestionTypeMatching:
				questionItem.Type = models.QuizQuestionItemTypeMatching
				questionItem.Text = questionItemParams.Text
				questionItem.FileID = questionItemParams.FileID
				questionItem.Side = questionItemParams.Side
				questionItem.Order = questionItemIdx + 1

			case models.QuizQuestionTypeOrdering:
				questionItem.Type = models.QuizQuestionItemTypeOrdering
				questionItem.Text = questionItemParams.Text
				questionItem.FileID = questionItemParams.FileID
				questionItem.Order = questionItemIdx + 1

			case models.QuizQuestionTypeText:
			case models.QuizQuestionTypeFillInBlanks:
				// Question type FillInBlanks no need question items

			default:
				return nil, e.NewError400(e.Quiz_invalid_data, "Quiz has invalid question type")
			}
			items = append(items, questionItem)
			itemsMap[fmt.Sprintf("%s_%s_%d", questionItemParams.Text, questionItemParams.FileID, questionItemParams.Side)] = questionItem
		}

		var correctAnswerSets models.QuizQuestionItemSets
		for _, correctAnswerParams := range questionParams.CorrectItemSets {
			var correctAnswers []*models.QuizQuestionItem
			for answerIdx, correctAnswerParam := range correctAnswerParams {
				switch questionParams.Type {
				case models.QuizQuestionTypeSingleChoice,
					models.QuizQuestionTypeMultipleChoice,
					models.QuizQuestionTypeMatching,
					models.QuizQuestionTypeOrdering:
					item, found := itemsMap[fmt.Sprintf("%s_%s_%d", correctAnswerParam.Text, correctAnswerParam.FileID, correctAnswerParam.Side)]
					if !found {
						return nil, e.NewError400(e.Quiz_invalid_data, "Correct answer is invalid for question: "+questionParams.Title)
					}
					correctAnswers = append(correctAnswers, &models.QuizQuestionItem{
						ID:     item.ID,
						Type:   item.Type,
						Text:   item.Text,
						FileID: item.FileID,
						File:   item.File,
						Side:   item.Side,
						Order:  answerIdx + 1,
					})

				case models.QuizQuestionTypeText,
					models.QuizQuestionTypeFillInBlanks:
					if correctAnswerParam.Text == "" {
						return nil, e.NewError400(e.Quiz_invalid_data, "Correct answer is empty for question: "+questionParams.Title)
					}

					correctAnswers = append(correctAnswers, &models.QuizQuestionItem{
						Type: lo.If[models.QuizQuestionItemType](questionParams.Type == models.QuizQuestionTypeText, models.QuizQuestionTypeText).
							Else(models.QuizQuestionTypeFillInBlanks),
						Text:  correctAnswerParam.Text,
						Order: answerIdx + 1,
					})

				default:
					return nil, e.NewError400(e.Quiz_invalid_data, "Quiz has invalid question type")
				}
			}
			correctAnswerSets = append(correctAnswerSets, correctAnswers)
		}
		questions = append(questions, &models.QuizQuestion{
			Model: models.Model{
				ID: questionParams.ID,
			},
			QuizID:      quiz.ID,
			Title:       questionParams.Title,
			Description: questionParams.Description,
			Text:        questionParams.Text,
			Files: lo.Map(questionParams.Files, func(simpleFile *models.SimpleFile, _ int) *models.File {
				return &models.File{Model: models.Model{ID: simpleFile.ID}}
			}),
			Type:            questionParams.Type,
			Items:           items,
			CorrectItemSets: correctAnswerSets,
			Explanation:     questionParams.Explanation,
			Points:          questionParams.Points,
			Order:           questionIdx + 1,
			Settings: models.QuizQuestionSettings{
				TimeLimit: questionParams.Settings.TimeLimit,
			},
		})
	}
	return questions, nil
}
