package services

import (
	"errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
)

func (s *FormQuestionService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.FormQuestion, *e.AppError) {
	query := &models.FormQuestionQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}
	question, err := models.Repository.FormQuestion.FindOne(query, options)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Question_not_found, err.Error())
		}
		return nil, e.NewError500(e.Question_find_one_failed, err.Error())
	}

	return question, nil
}

func (s *FormQuestionService) FindOne(query *models.FormQuestionQuery, options *models.FindOneOptions) (*models.FormQuestion, *e.AppError) {
	question, err := models.Repository.FormQuestion.FindOne(query, options)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Question_not_found, err.Error())
		}
		return nil, e.NewError500(e.Question_find_one_failed, err.Error())
	}

	return question, nil
}
