package services

import (
	"context"
	"errors"
	"fmt"
	"openedu-core/cache_clients"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"sort"
	"strings"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

const (
	generateCourseSlugMaxAttempts = 5
)

var CanUpdateCourseRoles = []string{
	models.SystemAdminRoleType, models.AdminRoleType, models.ModeratorRoleType, models.OrgAdminRoleType,
}

var CanViewReportCourseRoles = []string{
	models.SystemAdminRoleType, models.AdminRoleType, models.ModeratorRoleType, models.OrgAdminRoleType,
}

var PermissionsByPartnerRoles = map[models.CourseRole]models.StringArray{
	models.CourseRoleOwner:      {string(models.CoursePerAll)},
	models.CourseRoleCoCreator:  {string(models.CoursePerAll)},
	models.CourseRoleMentor:     {}, // TODO
	models.CourseRoleSponsor:    {}, // TODO
	models.CourseRoleSupervisor: {}, // TODO
}

func getPermissionsByRoles(roles models.StringArray) models.StringArray {
	permissions := models.StringArray{}
	for _, role := range roles {
		permissions = append(permissions, PermissionsByPartnerRoles[models.CourseRole(role)]...)
	}
	return lo.Uniq(permissions)
}

func medias2Files(medias []*dto.DefaultEntityRequest) ([]*models.File, *e.AppError) {
	var files []*models.File
	for _, item := range medias {
		if item.ID != "" {
			if file, err := models.Repository.File.FindByID(item.ID, nil); err != nil {
				return nil, e.NewError400(e.Error_file_not_found, "file not found: "+item.ID)
			} else {
				files = append(files, file)
			}
		}
	}
	return files, nil
}

func categories2Categories(categoryType models.CategoryType, input []*dto.DefaultEntityRequest) ([]*models.Category, *e.AppError) {
	all, err := models.Repository.Category.FindMany(&models.CategoryQuery{Type: &categoryType}, nil)
	if err != nil {
		return nil, e.NewError500(e.Category_find_many_failed, "Find all categories failed"+err.Error())
	}
	var categories []*models.Category
	for _, item := range input {
		if item.ID != "" {
			cate, ok := lo.Find(all, func(c *models.Category) bool {
				return c.ID == item.ID
			})
			if ok {
				categories = append(categories, cate)
			}
		}
	}
	return categories, nil
}

func (s *CourseService) addPartners(course *models.Course, org *models.Organization, user *models.User, pts []*dto.PartnerRequest) ([]*models.CoursePartner, *e.AppError) {
	var partners []*models.CoursePartner
	partners = append(partners, &models.CoursePartner{
		OrgID:       org.ID,
		CourseID:    course.Cuid,
		PartnerID:   course.UserID,
		Roles:       models.StringArray{string(models.CourseRoleOwner)},
		IsActive:    true,
		Permissions: getPermissionsByRoles(models.StringArray{string(models.CourseRoleOwner)}),
	})
	if pts != nil {
		for _, p := range pts {
			partners = append(partners, &models.CoursePartner{
				OrgID:       org.ID,
				CourseID:    course.Cuid,
				PartnerID:   p.ID,
				Roles:       p.Roles,
				IsActive:    p.Enable,
				Permissions: getPermissionsByRoles(p.Roles),
			})
		}
	}
	var err error
	var ps []*models.CoursePartner
	existingPartners, err := models.Repository.CoursePartner.FindMany(&models.CoursePartnerQuery{
		CourseID: &course.Cuid,
		IsActive: util.NewBool(true),
	}, &models.FindManyOptions{})
	if err != nil {
		return nil, e.NewError500(e.Update_course_partner_failed, err.Error())
	}

	existingPartnersByPartnerIDs := make(map[string]*models.CoursePartner)
	for _, partner := range existingPartners {
		existingPartnersByPartnerIDs[partner.PartnerID] = partner
	}

	for _, partner := range partners {
		p, pErr := models.Repository.CoursePartner.FirstOrCreate(partner)
		if pErr != nil {
			err = pErr
			break
		}

		if enErr := models.Repository.CourseEnrollment(s.ctx).Upsert(&models.CourseEnrollment{CourseCuid: course.Cuid, UserID: user.ID, OrgID: org.ID}, nil); enErr != nil {
			return nil, e.NewError500(e.Create_course_enrollment_failed, enErr.Error())
		}

		newRoles := p.Roles
		if existingPartner, found := existingPartnersByPartnerIDs[p.PartnerID]; found {
			stringRoles := []string(existingPartner.Roles)
			newRoles = lo.Without(p.Roles, stringRoles...)
		}

		if len(newRoles) > 0 && user.ID != p.PartnerID {
			notiReq := &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeNewCoursePartnerAdded,
				EntityID:   course.ID, // Jump to course
				EntityType: communicationdto.CourseEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{p.PartnerID},
					},
				},
				Props: makeNotificationPropsForAddCoursePartner(course, org, user, newRoles).IntoComm(),
			}
			if err := communication.Notification.PushNotification(notiReq); err != nil {
				log.Errorf("Push notification after approval request error: %v", err)
			}
		}

		ps = append(ps, p)
	}
	if err != nil {
		return nil, e.NewError500(e.Update_course_partner_failed, err.Error())
	}
	return ps, nil
}

func makeNotificationPropsForAddCoursePartner(course *models.Course, org *models.Organization, user *models.User, newRoles []string) models.JSONB {
	return models.JSONB{
		"collaborator": newRoles[0],
		"course_roles": newRoles,
		"user_id":      user.ID,
		"username":     user.Username,
		"course_id":    course.ID,
		"course_cuid":  course.Cuid,
		"course_name":  course.Name,
		"course_slug":  course.Slug,
		"org_id":       org.ID,
		"org_domain":   org.Domain,
	}
}

func (s *CourseService) Create(org *models.Organization, user *models.User, data *dto.CreateCourseRequest, defaultSection bool) (*models.Course, *e.AppError) {
	files, fErr := medias2Files(data.MediaIDs)
	if fErr != nil {
		return nil, fErr
	}

	docs, fErr := medias2Files(data.DocsIDs)
	if fErr != nil {
		return nil, fErr
	}

	categories, cErr := categories2Categories(models.TypeCategoryCourse, data.Categories)
	levels, cErr := categories2Categories(models.TypeLevel, data.Levels)
	if cErr != nil {
		return nil, cErr
	}

	status := models.CourseSTTDraft
	if data.Status != nil {
		status = *data.Status
	}
	course := models.Course{
		OrgID:            org.ID,
		UserID:           user.ID,
		Status:           status,
		Name:             data.Name,
		Description:      data.Description,
		ShortDesc:        data.ShortDesc,
		PriceSettings:    data.PriceSettings,
		Medias:           files,
		Docs:             docs,
		LearnMethod:      data.LearnMethod,
		ThumbnailID:      data.ThumbnailID,
		Categories:       categories,
		Levels:           levels,
		Cuid:             util.GenerateId(),
		Version:          1,
		Props:            models.CourseProps{PreviousVersion: 0},
		Enable:           data.Enable,
		StartDate:        data.StartDate,
		EndDate:          data.EndDate,
		MarkAsCompleted:  data.MarkAsCompleted,
		HasCertificate:   *(lo.If(data.HasCertificate != nil, data.HasCertificate).Else(util.NewBool(false))),
		IsAIGenerated:    *(lo.If(data.IsAIGenerated != nil, data.IsAIGenerated).Else(util.NewBool(false))),
		AIGenerateStatus: *(lo.If(data.AIGenerateStatus != nil, data.AIGenerateStatus).Else(util.NewT(models.AIStatusManual))),
	}

	if data.ID != nil {
		course.Model = models.Model{ID: *data.ID}
	}

	if data.Cuid != nil {
		course.Cuid = *data.Cuid
	} else {
		course.Cuid = util.GenerateId()
	}

	if data.AICourseID != nil {
		course.AICourseID = *data.AICourseID
	}

	if data.Props != nil {
		course.Props = *data.Props
	}
	course.Props.CertificateCondition = models.MakeDefaultCertificateCondition()
	course.Props.MintCertNFTSettings = models.MakeDefaultMintCertNFTSettings()

	if data.Slug != nil {
		course.Slug = *data.Slug
		existingCourse, err := models.Repository.Course(s.ctx).FindOne(&models.CourseQuery{
			Slug:   &course.Slug,
			CuidNe: &course.Cuid, // Versions of a course can have the same slug
		}, nil)
		if err != nil && !models.IsRecordNotFound(err) {
			return nil, e.NewError500(e.Course_check_slug_failed, "Check course slug error: "+err.Error())
		}

		if existingCourse != nil {
			return nil, e.NewError400(e.Course_slug_already_exists, "Course slug already exists")
		}
	} else {
		// Attempt generate slugs
		if _, err := lo.Attempt(generateCourseSlugMaxAttempts, func(_ int) error {
			slug, err := util.Slugify(course.Name, 5)
			if err != nil {
				return err
			}

			existingCourse, err := models.Repository.Course(s.ctx).FindOne(&models.CourseQuery{
				Slug:   &slug,
				CuidNe: &course.Cuid, // Versions of a course can have the same slug
			}, nil)
			if err != nil && !models.IsRecordNotFound(err) {
				return err
			}

			if existingCourse != nil {
				return fmt.Errorf("course slug already exists")
			}

			course.Slug = slug
			return nil
		}); err != nil {
			return nil, e.NewError500(e.Create_course_failed, err.Error())
		}
	}

	if cErr := models.Repository.Course(s.ctx).Create(&course, nil); cErr != nil {
		return nil, e.NewError500(e.Create_course_failed, cErr.Error())
	}

	_, pErr := s.addPartners(&course, org, user, data.Partners)
	if pErr != nil {
		return &course, e.NewError500(e.Update_course_partner_failed, pErr.Msg)
	}

	if defaultSection {
		// create default section
		sectionData := &dto.CreateSectionRequest{
			CourseID: course.ID,
			Title:    "Create your first section",
			OrgID:    org.ID,
			Note:     "",
			Order:    0,
			Course:   &course,
		}

		if section, sErr := Section.Create(user, sectionData, true); sErr != nil {
			return &course, sErr
		} else {
			course.Outline = append([]*models.Section{}, section)
		}
	}

	// update user role org to partner for course owner
	if _, err := models.Repository.UserRoleOrg.FirstOrCreate(&models.UserRoleOrg{
		UserID: user.ID,
		OrgID:  org.ID,
		RoleID: models.PartnerRoleType,
	}); err != nil {
		return nil, e.NewError500(e.Error_auth_add_user_role_failed, "Create user role org failed: "+err.Error())
	}
	course.Categories = categories
	course.Levels = levels
	return &course, nil
}

func (s *CourseService) Update(course *models.Course, org *models.Organization, user *models.User, data *dto.UpdateCourseRequest) (*models.Course, *e.AppError) {
	files, fErr := medias2Files(data.MediaIDs)
	if fErr != nil {
		return nil, fErr
	}
	docs, fErr := medias2Files(data.DocsIDs)
	if fErr != nil {
		return nil, fErr
	}
	categories, cErr := categories2Categories(models.TypeCategoryCourse, data.Categories)
	levels, cErr := categories2Categories(models.TypeLevel, data.Levels)
	if cErr != nil {
		return nil, cErr
	}

	course.Name = data.Name
	course.Description = data.Description
	course.ShortDesc = data.ShortDesc
	course.Status = models.CourseSTTDraft
	course.Medias = files
	course.Docs = docs
	course.LearnMethod = data.LearnMethod
	course.ThumbnailID = data.ThumbnailID
	course.PriceSettings = data.PriceSettings
	course.Categories = categories
	course.Levels = levels
	course.Enable = data.Enable
	course.StartDate = data.StartDate
	course.EndDate = data.EndDate
	course.MarkAsCompleted = data.MarkAsCompleted
	if data.Props != nil {
		data.Props.PreviousVersion = course.Props.PreviousVersion
		data.Props.PreviousID = course.Props.PreviousID
		if data.Props.CertificateCondition == nil {
			data.Props.CertificateCondition = course.Props.CertificateCondition
		}
		// } else {
		// 	// if update require lesson uid must validate lesson
		// 	if data.Props.CertificateCondition.RequiredLessonUID != course.Props.CertificateCondition.RequiredLessonUID && data.Props.CertificateCondition.RequiredLessonUID != "" {
		// 		lesson, err := models.Repository.Section.FindOne(&models.SectionQuery{
		// 			UID:             &data.Props.CertificateCondition.RequiredLessonUID,
		// 			ParentIDNotNull: util.NewBool(true),
		// 			CourseID:        &course.ID,
		// 		}, nil)
		// 		if err != nil {
		// 			if models.IsRecordNotFound(err) {
		// 				return nil, e.NewError404(e.Lesson_not_found, "Lesson not found: "+err.Error())
		// 			}
		// 			return nil, e.NewError500(e.Lesson_find_one_failed, "Find lesson failed: "+err.Error())
		// 		}

		// 		if lesson.Status != models.SectionStatusPublish {
		// 			return nil, e.NewError400(e.Lesson_find_one_failed, "Lesson not published")
		// 		}
		// 	}
		// }
		if data.Props.MintCertNFTSettings == nil {
			data.Props.MintCertNFTSettings = course.Props.MintCertNFTSettings
		}
		if data.Props.CourseEnrollmentEmailTemplateCode == "" {
			data.Props.CourseEnrollmentEmailTemplateCode = course.Props.CourseEnrollmentEmailTemplateCode
		}
		// TODO
		completedLessons := course.Props.CertificateCondition.CompletedRequiredLesson
		requiredLessonUIDs := course.Props.CertificateCondition.RequiredLessonUIDs

		course.Props = *data.Props
		course.Props.CertificateCondition.CompletedRequiredLesson = completedLessons
		course.Props.CertificateCondition.RequiredLessonUIDs = requiredLessonUIDs
	} else {
		course.Props = models.CourseProps{
			PreviousVersion:      1,
			SupportChannel:       models.JSONB{},
			CertificateCondition: course.Props.CertificateCondition,
			PreviewLessons:       []models.PreviewLessonContent{},
		}
	}

	if data.Slug != nil && *data.Slug != course.Slug {
		course.Slug = *data.Slug
		existingCourse, err := models.Repository.Course(s.ctx).FindOne(&models.CourseQuery{
			Slug:   &course.Slug,
			CuidNe: &course.Cuid, // Versions of a course can have the same slug
		}, nil)
		if err != nil && !models.IsRecordNotFound(err) {
			return nil, e.NewError500(e.Course_check_slug_failed, "Check course slug error: "+err.Error())
		}

		if existingCourse != nil {
			return nil, e.NewError400(e.Course_slug_already_exists, "Course slug already exists")
		}
	}

	if data.HasCertificate != nil {
		course.HasCertificate = *data.HasCertificate
	}

	if ucErr := models.Repository.Course(s.ctx).Update(course, nil); ucErr != nil {
		return nil, e.NewError500(e.Update_course_failed, ucErr.Error())
	}

	_, pErr := s.addPartners(course, org, user, data.Partners)
	if pErr != nil {
		return course, e.NewError500(e.Update_course_partner_failed, pErr.Msg)
	}

	course.Categories = categories
	course.Levels = levels

	return course, nil
}

func (s *CourseService) FindById(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Course, *e.AppError) {
	query := &models.CourseQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}
	if course, err := models.Repository.Course(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Course_not_found, err.Error())
		}
		return nil, e.NewError500(e.Course_find_one_failed, err.Error())
	} else {
		return course, nil
	}
}

func (s *CourseService) FindByIdForOutline(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Course, *e.AppError) {
	res := &models.Course{}
	cacheKey := cache_clients.MakeCacheKeyFromPreloads(id, options.Preloads)
	if err := models.Cache.Course.GetByKey(cacheKey, res); err != nil {
		query := &models.CourseQuery{
			ID:             util.NewString(id),
			IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
		}
		if course, err := models.Repository.Course(s.ctx).FindOne(query, options); err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, e.NewError404(e.Course_not_found, err.Error())
			}
			return nil, e.NewError500(e.Course_find_one_failed, err.Error())
		} else {
			course, err := s.GetOutline(course)
			if err != nil {
				return nil, err
			}

			if cErr := models.Cache.Course.Set(cacheKey, course); cErr != nil {
				log.Warnf("Service::Course::FindByIdForOutline set course to cache failed: %v", cErr)
			}

			return course, nil
		}

	} else {
		return res, nil
	}

}

func (s *CourseService) FindOne(query *models.CourseQuery, options *models.FindOneOptions) (*models.Course, *e.AppError) {
	if course, err := models.Repository.Course(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Course_not_found, err.Error())
		}
		return nil, e.NewError500(e.Course_find_one_failed, err.Error())
	} else {
		return course, nil
	}
}

func (s *CourseService) FindPageByPartner(query *models.CourseQuery, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError) {

	if courses, pagination, err := models.Repository.Course(s.ctx).FindPageByPartner(query, options); err != nil {
		return nil, nil, e.NewError500(e.Course_find_page_failed, err.Error())
	} else {
		return courses, pagination, nil
	}
}

func (s *CourseService) FindPage(query *models.CourseQuery, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError) {
	if courses, pagination, err := models.Repository.Course(s.ctx).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Course_find_page_failed, err.Error())
	} else {
		return courses, pagination, nil
	}
}

func (s *CourseService) Delete(course *models.Course) *e.AppError {
	if count, err := models.Repository.Course(s.ctx).DeleteMany(&models.CourseQuery{Cuid: util.NewString(course.Cuid)}, nil); err != nil {
		log.Error("[course][delete] failed: ", err)
		return e.NewError500(e.Course_delete_failed, err.Error())
	} else {
		log.Info("[course][delete] success: ", count)
		_, rErr := PublishCourse.UnPublishCourse(course.Cuid, models.UnPublishCourseTargetAll)
		if rErr != nil {
			return rErr
		}
	}
	return nil
}

func (s *CourseService) FindCoursePartners(query *models.CoursePartnerQuery, options *models.FindPageOptions) ([]*models.CoursePartner, *models.Pagination, *e.AppError) {
	if partners, pagination, err := models.Repository.CoursePartner.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Course_find_page_failed, err.Error())
	} else {
		return partners, pagination, nil
	}
}

func (s *CourseService) FindManyCoursePartners(query *models.CoursePartnerQuery, options *models.FindManyOptions) ([]*models.CoursePartner, *e.AppError) {
	if partners, err := models.Repository.CoursePartner.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Course_find_page_failed, err.Error())
	} else {
		return partners, nil
	}
}

func (s *CourseService) UpdateCoursePartners(course *models.Course, org *models.Organization, user *models.User, data []*dto.PartnerRequest) ([]*models.CoursePartner, *e.AppError) {
	if partners, err := s.addPartners(course, org, user, data); err != nil {
		return nil, e.NewError500(e.Update_course_partner_failed, "Update course partner failed"+err.Msg)
	} else {
		return partners, nil
	}
}

func (s *CourseService) DeleteCoursePartners(course *models.Course, org *models.Organization, user *models.User, userIDs []string) *e.AppError {
	partners, err := s.FindManyCoursePartners(&models.CoursePartnerQuery{
		CourseID: util.NewString(course.Cuid),
	}, nil)
	if err != nil {
		return err
	}

	for _, userID := range userIDs {
		f, ok := lo.Find(partners, func(item *models.CoursePartner) bool {
			return item.PartnerID == userID
		})
		if ok {
			if delErr := models.Repository.CoursePartner.Delete(f.ID, nil); delErr != nil {
				return e.NewError500(e.Remove_course_partner_failed, "DeleteCoursePartners: "+delErr.Error())
			}
			if err := communication.Notification.PushNotification(&communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeNewCoursePartnerRemoved,
				EntityID:   course.ID, // Jump to course
				EntityType: communicationdto.CourseEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{userID},
					},
				},
				Props: makeNotificationPropsForRemoveCoursePartner(course, org, user, f.Roles).IntoComm(),
			}); err != nil {
				log.Errorf("Push notification after approval request error: %v", err)
			}
		}
	}

	return nil
}

func makeNotificationPropsForRemoveCoursePartner(course *models.Course, org *models.Organization, user *models.User, removedRoles []string) models.JSONB {
	return models.JSONB{
		"course_roles": removedRoles,
		"user_id":      user.ID,
		"username":     user.Username,
		"course_id":    course.ID,
		"course_cuid":  course.Cuid,
		"course_name":  course.Name,
		"course_slug":  course.Slug,
		"org_id":       org.ID,
		"org_domain":   org.Domain,
	}
}

func (s *CourseService) UpdateStats(course *models.Course, requireSave bool) *e.AppError {
	stats, err := models.Repository.Section.CountCourseStats(course.ID)
	if err != nil {
		return e.NewError500(e.Update_course_failed, "Query stats failed: "+err.Error())
	}

	course.SectionCount = stats.SectionCount
	course.LessonCount = stats.LessonCount
	course.ActiveLesson = stats.ActiveLesson
	course.ActiveSection = stats.ActiveSection
	course.VideoCount = stats.VideoCount
	course.QuizCount = stats.QuizCount
	if requireSave {
		if err = models.Repository.Course(s.ctx).UpdateBasic(course, nil); err != nil {
			return e.NewError500(e.Update_course_failed, "Update course: "+err.Error())
		}
	}

	return nil
}

func (s *CourseService) GetOutline(course *models.Course) (*models.Course, *e.AppError) {
	options := &models.FindPageOptions{
		Page:     util.PageMin,
		PerPage:  util.PerPageMax,
		Preloads: []string{},
		Sort:     []string{models.OrderASC},
	}

	sectionQuery := &models.SectionQuery{
		CourseID:     util.NewString(course.ID),
		Status:       util.NewT(models.SectionStatusPublish),
		ParentIDNull: util.NewBool(true),
	}

	sections, _, sErr := models.Repository.Section.FindPage(sectionQuery, options)
	if sErr != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "GetOutline::FindSections failed: "+sErr.Error())
	}

	lessonQuery := &models.SectionQuery{
		CourseID:        util.NewString(course.ID),
		Status:          util.NewT(models.SectionStatusPublish),
		ParentIDNotNull: util.NewBool(true),
	}

	lessons, _, sErr := models.Repository.Section.FindPage(lessonQuery, options)
	if sErr != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "GetOutline::FindSections failed: "+sErr.Error())
	}

	type groupLesson map[string][]*models.Section
	lessonBySections := lo.Reduce(lessons, func(agg groupLesson, item *models.Section, index int) groupLesson {
		results := agg[item.ParentID]
		if results == nil {
			results = []*models.Section{}
		}
		results = append(results, item)
		agg[item.ParentID] = results
		return agg
	}, groupLesson{})

	lo.ForEach(sections, func(item *models.Section, index int) {
		item.Lessons = lessonBySections[item.ID]
	})

	course.Outline = sections
	return course, nil
}

func (s *CourseService) CanUpdateCourse(course *models.Course, user *models.User, permission models.CoursePermission) *e.AppError {
	log.Info(fmt.Sprintf("Course current version: %s, Latest: %v", string(rune(course.Version)), course.Latest))
	// only allow update on latest version
	if (permission != models.CoursePerUnPublish && permission != models.CoursePerCancel && permission != models.CoursePerEnable) && !course.Latest {
		return e.NewError400(e.Course_update_need_permission_or_owner, "can only action on latest version")
	}

	// allow course owner
	if course.UserID == user.ID {
		return nil
	}
	urs, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:User:CanUpdateCourse: Find UserRoleOrg by user_id failed ", err)
		return e.NewError400(e.Course_update_need_permission_or_owner, "find user role failed")
	}

	for _, r := range urs {
		if lo.Contains(CanUpdateCourseRoles, r.RoleID) {
			return nil
		}
	}

	partners, _, pErr := s.FindCoursePartners(&models.CoursePartnerQuery{
		CourseID:  util.NewString(course.Cuid),
		PartnerID: util.NewString(user.ID),
		IsActive:  util.NewBool(true),
	}, &models.FindPageOptions{PerPage: util.PerPageMax})

	if pErr != nil {
		log.Error("Get course partner failed: ", pErr)
		return e.NewError400(e.Course_update_need_permission_or_owner, "find course partner failed")
	}

	if lo.ContainsBy(partners, func(item *models.CoursePartner) bool {
		return lo.Contains(item.Permissions, string(models.CoursePerAll)) || lo.Contains(item.Permissions, string(permission))
	}) {
		log.Debug(fmt.Sprintf("User %s has permission to %s this course: %s", user.ID, permission, course.ID))
		return nil
	}

	return e.NewError400(e.Course_update_need_permission_or_owner, "Need admin or course owner for update permission")
}

func (s *CourseService) RequestPublishCourse(request *dto.PublishCourseRequest) (*models.Course, *e.AppError) {
	course := request.Course
	var approval *models.Approval

	if course.Status != models.CourseSTTPublic {
		uid := util.GenerateId()
		pub := &dto.CreateApprovalRequest{
			EntityType:    models.CourseModelName,
			EntityID:      course.ID,
			EntityUID:     course.Cuid,
			Type:          models.ApproveTypePublishOrg,
			RequestUid:    uid,
			EntityVersion: course.Version,
		}

		publish, pubErr := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
			CourseCuid: util.NewString(course.Cuid),
		}, nil)
		if pubErr != nil && !errors.Is(pubErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Publish_course_find_failed, "find publish course failed")
		}

		if publish != nil {
			pub.PreVersion = util.NewT(publish.Version)
			pub.PreID = util.NewString(publish.CourseID)
		}

		if a, err := Approval.Create(request.Org, request.Requester, pub); err != nil {
			return nil, err
		} else {
			approval = a
		}
		if caErr := Approval.CancelOldRequest(approval); caErr != nil {
			return nil, caErr
		}
	}

	if course.Version == course.Props.PreviousVersion {
		course.Version = course.Version + 1
	}

	course.Props.PreApprovalUid = course.Props.ApprovalUid
	course.Props.ApprovalUid = approval.RequestUid
	course.Props.RequestID = course.ID
	course.Props.RequestVersion = course.Version
	// freeze current version, clone new version for editing
	newCourse, cloneErr := s.CloneNewVersion(course, request.Requester)
	if cloneErr != nil {
		return nil, cloneErr
	}

	// update current version to reviewing
	course.Status = models.CourseSTTReviewing
	course.Latest = false
	if udErr := models.Repository.Course(s.ctx).UpdateBasic(course, nil); udErr != nil {
		return nil, e.NewError500(e.Course_publish_failed, "Update current version failed: "+udErr.Error())
	}

	log.Debugf("CloneVersionReviewing has props: %#v", course.Props)

	if approval != nil {
		userRoleOrgs, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
			RoleIDIn: []string{models.OrgAdminRoleType, models.OrgModeratorRoleType},
			OrgID:    &approval.OrgID,
		}, nil)
		if err != nil {
			log.Errorf("Get list org admins and moderators of org ID %s error: %v", approval.OrgID, err)
		} else if len(userRoleOrgs) > 0 {
			var userIDs []string
			for _, urg := range userRoleOrgs {
				userIDs = append(userIDs, urg.UserID)
			}
			userIDs = lo.Uniq(userIDs)

			notificationReq := &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeNewPublishCourseRequest,
				EntityID:   approval.ID, // Jump to course publish request
				EntityType: communicationdto.CourseApprovalEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: userIDs,
					},
				},
				Props: s.makeNotificationPropsForCourse(course, request.Org, request.Requester).IntoComm(),
			}
			if err := communication.Notification.PushNotification(notificationReq); err != nil {
				log.Errorf("Push notification to admin of org ID %s new publish request for course ID %s error: %v", approval.OrgID, course.ID, err)
			}
		}
	}

	return newCourse, nil
}

func (s *CourseService) PublishCourse(user *models.User, approval *models.Approval) *e.AppError {
	course, cErr := models.Repository.Course(s.ctx).FindOne(&models.CourseQuery{
		ID: util.NewString(approval.EntityID),
	}, nil)
	if cErr != nil {
		return e.NewError400(e.Course_not_found, "models.Repository.Course.FindOneBySchema: "+cErr.Error())
	}
	org, oErr := Organization.FindByID(approval.OrgID, false, nil)
	if oErr != nil {
		return e.NewError500(e.Course_add_publish_failed, "find org failed: "+approval.OrgID)
	}
	now := time.Now().UnixMilli()
	var scope *models.PublishScope
	switch approval.Type {
	case models.ApproveTypePublishRoot:
		course.PubRootDate = int(now)
		scope = util.NewT(models.ScopeAll)
	case models.ApproveTypePublishOrg:
		course.PubDate = int(now)
		course.PubRootDate = int(now)
		scope = util.NewT(models.ScopeAll)
	case models.ApproveTypePublishInvestment:
		course.PubRootDate = int(now)
		scope = util.NewT(models.ScopeInvestment)
	}

	if course.PubDate > 0 && course.PubRootDate > 0 {
		course.Status = models.CourseSTTPublic
	}

	if cuErr := models.Repository.Course(s.ctx).UpdateBasic(course, nil); cuErr != nil {
		return e.NewError500(e.Update_course_failed, cuErr.Error())
	}

	// add publish course list
	if pErr := PublishCourse.AddPublishCourse(course, org, scope); pErr != nil {
		return pErr
	}

	//Clear cache lessonContents of new publishCourse
	previousCourseID := course.Props.PreviousID
	if err := models.Cache.LessonContent.DeleteByCourseID(previousCourseID); err != nil {
		log.ErrorWithAlertf("Cannot clear cache lesson content by courseID: %#v", err)
	}

	notificationReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeRequestPublishCourseApproved,
		EntityID:   approval.EntityID, // Jump to course
		EntityType: communicationdto.CourseEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{approval.RequesterID},
			},
		},
		Props: s.makeNotificationPropsForCourse(course, org, user).IntoComm(),
	}
	if err := communication.Notification.PushNotification(notificationReq); err != nil {
		log.Errorf("Push notification to user ID %s after approving publish course ID %s error: %v", approval.RequesterID, course.ID, err)
	}

	//orgCourse, err := Organization.FindByIDWithCache(course.OrgID, &models.FindOneOptions{})
	//if err != nil {
	//	log.ErrorWithAlertf("Cannot find org course to noti: %#v", err)
	//	return err
	//}
	//// Push new content for learner
	//notificationReq = &communicationdto.PushNotificationRequest{
	//	Code:       communicationdto.CodeNewContentAlert,
	//	EntityID:   approval.EntityID, // Jump to course
	//	EntityType: communicationdto.CourseEntity,
	//	RuleTree: &communicationdto.TreeNodeRequest{
	//		Rule: &communicationdto.Rule{
	//			Subject:   communicationdto.Learner,
	//			Verb:      communicationdto.Joined,
	//			Object:    communicationdto.CourseNotificationObject,
	//			ObjectIDs: []string{course.Cuid},
	//			OrgID:     course.OrgID,
	//		},
	//	},
	//	Props: s.makeNotificationPropsForCourse(course, orgCourse, user).IntoComm(),
	//	Org:   orgCourse.IntoComm(),
	//}
	//if err := communication.Notification.PushNotification(notificationReq); err != nil {
	//	log.Errorf("Push notification to user ID %s after approving publish course ID %s error: %v", approval.RequesterID, course.ID, err)
	//}
	//SendEmail
	//userEnrollments, err := FindManyWithPagination(models.Repository.CourseEnrollment(s.ctx).FindPage, &models.CourseEnrollmentQuery{CourseCuid: &course.Cuid}, &models.FindManyOptions{}, 50)
	//if err != nil {
	//	log.Errorf("Find learner enrollment course failed")
	//}
	//
	//userIDs := lo.Map(userEnrollments, func(item *models.CourseEnrollment, _ int) string {
	//	return item.UserID
	//})
	//
	//users, err := FindManyWithPagination(models.Repository.User.FindPage, &models.UserQuery{IDIn: &userIDs}, &models.FindManyOptions{}, 50)
	//if err != nil {
	//	log.Errorf("Find learner enrollment course failed")
	//}
	//
	//go func(users []*models.User) {
	//	aigov := models.GetConfig[models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	//	for _, user := range users {
	//		if org.ID == aigov.OrgID {
	//			if _, err := communication.Email.SendEmail(&communicationdto.SendEmailRequest{
	//				User: user.IntoComm(),
	//				Org:  orgCourse.IntoComm(),
	//				Code: util.NewT(communicationdto.EmailCodeNewCourseContent),
	//				ExtendDatas: communicationdto.MapEmailParams{
	//					"user_name":   user.Username,
	//					"user_email":  user.Email,
	//					"course_name": course.Name,
	//					"course_slug": course.Slug,
	//				},
	//				IsQueue: true,
	//				From:    org.Settings.SenderEmail,
	//			}); err != nil {
	//				log.ErrorWithAlertf("Order.HandleOrderSuccess::Send email code %v failed: %v", communicationdto.EmailCodeNewCourseContent, err)
	//			}
	//		}
	//	}
	//}(users)

	return nil
}

func (s *CourseService) makeNotificationPropsForCourse(course *models.Course, org *models.Organization, user *models.User) models.JSONB {
	return models.JSONB{
		"org_id":      org.ID,
		"org_name":    org.Name,
		"org_domain":  org.Domain,
		"course_id":   course.ID,
		"course_cuid": course.Cuid,
		"course_name": course.Name,
		"course_slug": course.Slug,
		"user_id":     user.ID,
		"username":    user.Username,
	}
}

func (s *CourseService) RejectPublishCourse(user *models.User, approval *models.Approval) *e.AppError {
	course, cErr := models.Repository.Course(s.ctx).FindOne(
		&models.CourseQuery{ID: util.NewString(approval.EntityID)},
		nil)
	if cErr != nil {
		return e.NewError400(e.Course_reject_failed, "find course failed: "+cErr.Error())
	}
	org, oErr := Organization.FindByID(approval.OrgID, false, nil)
	if oErr != nil {
		return e.NewError500(e.Course_add_publish_failed, "find org failed: "+approval.OrgID)
	}
	now := time.Now().UnixMilli()
	if approval.Type == models.ApproveTypePublishOrg {
		course.PubRejectDate = int(now)
		course.Props.RejectOrgReason = approval.Note
	} else if approval.Type == models.ApproveTypePublishRoot {
		course.PubRootRejectDate = int(now)
		course.Props.RejectRootReason = approval.Note
		course.PubRootRejectDate = int(now)
		course.Props.RejectRootReason = approval.Note
	}

	if course.PubRejectDate > 0 && course.PubRootRejectDate > 0 {
		course.Status = models.CourseSTTReject
	}

	if udErr := models.Repository.Course(s.ctx).UpdateBasic(course, nil); udErr != nil {
		return e.NewError500(e.Course_reject_failed, "Update course current version failed: "+udErr.Error())
	}

	notificationReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeRequestPublishCourseRejected,
		EntityID:   approval.EntityID,
		EntityType: communicationdto.CourseEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{approval.RequesterID},
			},
		},
		Props: s.makeNotificationPropsForCourse(course, org, user).IntoComm(),
	}

	if err := communication.Notification.PushNotification(notificationReq); err != nil {
		log.Errorf("Push notification to user ID %s after rejecting publish course ID %s error: %v", approval.RequesterID, course.ID, err)
	}

	return nil
}

func (s *CourseService) FindCourseListItems(
	user *models.User,
	org *models.Organization,
	query *models.PublishCourseQuery,
	options *models.FindPageOptions,

) ([]*models.CourseListItem, *models.Pagination, *e.AppError) {
	if query == nil {
		query = &models.PublishCourseQuery{}
	}

	isOpenedu := org.Domain == setting.AppSetting.BaseDomain
	query.IncludeDeleted = util.NewBool(false)
	if !isOpenedu {
		query.OrgID = util.NewString(org.ID)
		query.OrgIDNot = nil
		query.Enable = util.NewBool(true)
		query.EnableRoot = nil
	} else {
		// is Openedu
		if query.OrgID != nil {
			query.Enable = util.NewBool(true)
		}
		if query.OrgIDNot != nil {
			query.EnableRoot = util.NewBool(true)
		}
		if query.Enable == nil && query.EnableRoot == nil {
			if query.OrgID == nil && query.OrgIDNot == nil {
				query.EnableAll = util.NewBool(true)
			}
		}

	}

	pubOptions := &models.FindPageOptions{
		Page:    options.Page,
		PerPage: options.PerPage,
		Sort:    options.Sort,
	}

	courseIDs, pagination, err := models.Repository.PublishCourse(s.ctx).FindPageCourseIDs(query, pubOptions)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
	}

	courses, appErr := s.findCourseItemsByCourseIDs(user, courseIDs, &models.FindManyOptions{
		Preloads: options.Preloads,
	}, false)
	if appErr != nil {
		return nil, nil, appErr
	}

	var output []*models.CourseListItem
	for _, courseID := range courseIDs {
		c, found := lo.Find(courses, func(cm *models.CourseListItem) bool {
			return cm.ID == courseID
		})
		if found {
			output = append(output, c)
		}
	}

	// TODO make course rating
	for _, course := range output {
		course.Rating = 5
	}

	return output, pagination, nil
}

func (s *CourseService) FindLearnerPublishCourses(
	user *models.User,
	org *models.Organization,
	query *models.PublishCourseQuery,
	options *models.FindPageOptions,

) ([]*models.Course, *models.Pagination, *e.AppError) {
	if query == nil {
		query = &models.PublishCourseQuery{}
	}

	isOpenedu := org.Domain == setting.AppSetting.BaseDomain
	query.IncludeDeleted = util.NewBool(false)
	if !isOpenedu {
		query.OrgID = util.NewString(org.ID)
		query.OrgIDNot = nil
		query.Enable = util.NewBool(true)
	} else {
		// is Openedu
		if query.OrgID != nil {
			query.Enable = util.NewBool(true)
		}
		if query.OrgIDNot != nil {
			query.EnableRoot = util.NewBool(true)
		}
		if query.Enable == nil && query.EnableRoot == nil {
			if query.OrgID == nil && query.OrgIDNot == nil {
				query.EnableAll = util.NewBool(true)
			}
		}

	}

	pubOptions := &models.FindPageOptions{
		Page:    options.Page,
		PerPage: options.PerPage,
		Sort:    options.Sort,
	}

	pubCourses, pagination, err := models.Repository.PublishCourse(s.ctx).FindPage(query, pubOptions)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
	}

	courses, appErr := s.findCoursesByPublishCourses(user, pubCourses, &models.FindManyOptions{
		Preloads: options.Preloads,
	}, false)
	if appErr != nil {
		return nil, nil, appErr
	}

	var output []*models.Course
	for _, item := range pubCourses {
		c, found := lo.Find(courses, func(cm *models.Course) bool {
			return cm.ID == item.CourseID
		})
		if found {
			output = append(output, c)
		}
	}

	// TODO make course rating
	for _, course := range output {
		course.Rating = 5
	}

	return output, pagination, nil
}

func (s *CourseService) FindPublishCourses(user *models.User, org *models.Organization, query *models.PublishCourseQuery, options *models.FindPageOptions) ([]*models.Course, *models.Pagination, *e.AppError) {
	if query == nil {
		query = &models.PublishCourseQuery{}
	}

	isRoot := org.Domain == setting.AppSetting.BaseDomain
	query.IncludeDeleted = util.NewBool(false)
	//query.IsRoot = util.NewBool(isRoot)

	// If not root domain then default get only org's course
	if !isRoot {
		query.OrgID = util.NewString(org.ID)
		query.EnableRoot = nil
	}

	pubOptions := &models.FindPageOptions{
		Page:    options.Page,
		PerPage: options.PerPage,
		Sort:    options.Sort,
	}

	pubCourses, pagination, err := models.Repository.PublishCourse(s.ctx).FindPage(query, pubOptions)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
	}

	courses, appErr := s.findCoursesByPublishCourses(user, pubCourses, &models.FindManyOptions{
		Preloads: options.Preloads,
	}, false)
	if appErr != nil {
		return nil, nil, appErr
	}

	var output []*models.Course
	for _, item := range pubCourses {
		c, found := lo.Find(courses, func(cm *models.Course) bool {
			return cm.ID == item.CourseID
		})
		if found {
			output = append(output, c)
		}
	}

	for _, course := range output {
		course.Rating = 5
	}

	return output, pagination, nil
}

func (s *CourseService) findCoursesByPublishCourses(
	user *models.User,
	pubCourses []*models.PublishCourse,
	options *models.FindManyOptions,
	preloadLearningProgressOverview bool,
) ([]*models.Course, *e.AppError) {

	splitStr := "__"
	groupIdBySchemas := lo.Reduce(pubCourses, func(agg map[string][]string, item *models.PublishCourse, _ int) map[string][]string {
		key := item.OrgSchema + splitStr + item.OrgDomain
		ids := agg[key]
		ids = append(ids, item.CourseID)
		agg[key] = ids
		return agg
	}, make(map[string][]string))

	orgs, _, orgErr := Organization.FindPage(
		&models.OrganizationQuery{Active: util.NewBool(true)},
		&models.FindPageOptions{PerPage: util.PerPageMax},
	)

	if orgErr != nil {
		return nil, orgErr
	}

	var courses []*models.Course
	for key, ids := range groupIdBySchemas {
		keyArr := strings.Split(key, splitStr)
		domain := keyArr[1]
		q := &models.CourseQuery{
			IDIn:           ids,
			IncludeDeleted: util.NewBool(false),
		}
		css, cErr := models.Repository.Course(s.ctx).FindMany(q, options)
		if cErr != nil {
			return nil, e.NewError500(e.Course_find_publish_failed, "find courses failed: "+cErr.Error())
		}
		orgByCourse, ok := lo.Find(orgs, func(org *models.Organization) bool {
			return org.Domain == domain
		})
		if ok {
			lo.ForEach(css, func(item *models.Course, _ int) {
				item.Org = orgByCourse.ToSimple()
			})
		}

		courses = append(courses, css...)
	}

	if user != nil {
		if appErr := AssignUserStatusToCourses(s.ctx, user, courses); appErr != nil {
			return nil, appErr
		}

		if preloadLearningProgressOverview {
			courseIDs := lo.Map(courses, func(course *models.Course, _ int) string {
				return course.ID
			})

			completedLessonCounts, err := models.Repository.LearningStatus(context.TODO()).CountCompletedLessonsByUsers(courseIDs, []string{user.ID})

			if err != nil {
				return nil, e.NewError500(e.Course_find_publish_failed, "Preload learning progress overview failed: "+err.Error())
			}

			currentSections, err := models.Repository.LearningStatus(context.TODO()).FindCurrentSectionsAndLessonsByUser(courseIDs, user)
			if err != nil {
				return nil, e.NewError500(e.Course_find_publish_failed, "Preload learning progress overview failed: "+err.Error())
			}

			completedLessonCountsByCourseCUIDs := make(map[string]*models.CompletedLessonCountByUser)
			for _, completedLessonCount := range completedLessonCounts {
				completedLessonCountsByCourseCUIDs[completedLessonCount.CourseCUID] = completedLessonCount
			}

			currentSectionsByCourseIDs := make(map[string]*models.Section)
			currentLessonsByCourseIDs := make(map[string]*models.Section)
			for _, section := range currentSections {
				if section.IsLesson() {
					currentLessonsByCourseIDs[section.CourseID] = section
				} else {
					currentSectionsByCourseIDs[section.CourseID] = section
				}
			}

			for _, course := range courses {
				course.LearningProgressOverview = &models.SimpleLearningProgressOverview{}
				if completedLessonCount, found := completedLessonCountsByCourseCUIDs[course.Cuid]; found {
					course.LearningProgressOverview.TotalLesson = completedLessonCount.TotalLessons
					course.LearningProgressOverview.CompletedLesson = completedLessonCount.CompletedLessons
				}

				if section, found := currentSectionsByCourseIDs[course.ID]; found {
					course.LearningProgressOverview.CurrentSection = section
				}

				if lesson, found := currentLessonsByCourseIDs[course.ID]; found {
					course.LearningProgressOverview.CurrentLesson = lesson
				}
			}
		}
	}

	// TODO make course rating
	for _, course := range courses {
		course.Rating = 5
	}

	return courses, nil
}

func makeCourseItemCacheKey(courseID string, preloads []string) string {
	return courseID + "_" + strings.Join(preloads, "__")
}

func (s *CourseService) findCourseItemsByCourseIDs(
	user *models.User,
	courseIDs []string,
	options *models.FindManyOptions,
	preloadLearningProgressOverview bool,
) ([]*models.CourseListItem, *e.AppError) {

	if len(courseIDs) == 0 {
		return []*models.CourseListItem{}, nil
	}

	if options != nil {
		// Org must be preloaded to be displayed on the UI.
		options.Preloads = append(options.Preloads, models.OrgField)
	} else {
		options = &models.FindManyOptions{
			Preloads: []string{models.OrgField},
		}
	}

	sort.Slice(options.Preloads, func(i, j int) bool {
		return options.Preloads[i] < options.Preloads[j]
	})

	var courses []*models.CourseListItem
	var queryCourseIDs []string
	var preloads []string
	copy(preloads, options.Preloads)
	for _, courseID := range courseIDs {
		var courseCache models.CourseListItem
		key := makeCourseItemCacheKey(courseID, preloads)
		if cErr := models.Cache.Course.GetListItem(key, &courseCache); cErr == nil && courseCache.ID != "" {
			courses = append(courses, &courseCache)
		} else {
			queryCourseIDs = append(queryCourseIDs, courseID)
		}
	}

	if len(queryCourseIDs) > 0 {
		q := &models.CourseQuery{
			IDIn:           queryCourseIDs,
			IncludeDeleted: util.NewBool(false),
		}

		c, cErr := models.Repository.Course(s.ctx).FindManyListItems(q, options)
		if cErr != nil {
			return nil, e.NewError500(e.Course_find_publish_failed, "Find courses failed: "+cErr.Error())
		}

		courses = append(courses, c...)
	}

	coursesByIDs := make(map[string]*models.CourseListItem)
	for _, course := range courses {
		key := makeCourseItemCacheKey(course.ID, preloads)
		coursesByIDs[course.ID] = course
		course.Rating = 5 // TODO make course rating
		models.Cache.Course.SetListItem(key, course)
	}

	if user != nil {
		if preloadLearningProgressOverview {
			completedLessonCounts, err := models.Repository.LearningStatus(context.TODO()).CountCompletedLessonsByUsers(courseIDs, []string{user.ID})
			if err != nil {
				return nil, e.NewError500(e.Course_find_publish_failed, "Preload learning progress overview failed: "+err.Error())
			}

			currentSections, err := models.Repository.LearningStatus(context.TODO()).FindCurrentSectionsAndLessonsByUser(courseIDs, user)
			if err != nil {
				return nil, e.NewError500(e.Course_find_publish_failed, "Preload learning progress overview failed: "+err.Error())
			}

			completedLessonCountsByCourseCUIDs := make(map[string]*models.CompletedLessonCountByUser)
			for _, completedLessonCount := range completedLessonCounts {
				completedLessonCountsByCourseCUIDs[completedLessonCount.CourseCUID] = completedLessonCount
			}

			currentSectionsByCourseIDs := make(map[string]*models.Section)
			currentLessonsByCourseIDs := make(map[string]*models.Section)
			for _, section := range currentSections {
				if section.IsLesson() {
					currentLessonsByCourseIDs[section.CourseID] = section
				} else {
					currentSectionsByCourseIDs[section.CourseID] = section
				}
			}

			for _, course := range courses {
				course.LearningProgressOverview = &models.SimpleLearningProgressOverview{}
				if completedLessonCount, found := completedLessonCountsByCourseCUIDs[course.Cuid]; found {
					course.LearningProgressOverview.TotalLesson = completedLessonCount.TotalLessons
					course.LearningProgressOverview.CompletedLesson = completedLessonCount.CompletedLessons
				}

				if section, found := currentSectionsByCourseIDs[course.ID]; found {
					course.LearningProgressOverview.CurrentSection = section
				}

				if lesson, found := currentLessonsByCourseIDs[course.ID]; found {
					course.LearningProgressOverview.CurrentLesson = lesson
				}
			}
		}
	}

	if user != nil {
		if appErr := AssignUserStatusToCourses(s.ctx, user, courses); appErr != nil {
			return nil, appErr
		}
	}

	var sortedCourses []*models.CourseListItem
	for _, courseID := range courseIDs {
		if course, found := coursesByIDs[courseID]; found {
			sortedCourses = append(sortedCourses, course)
		}
	}

	return sortedCourses, nil
}

func AssignUserStatusToCourses[T models.CourseIface](ctx context.Context, user *models.User, courses []T) *e.AppError {
	userStats, err := models.Repository.Course(ctx).FindUserStats(user, lo.Map(courses, func(course T, _ int) string {
		return course.GetCourseCUID()
	}))
	if err != nil {
		return e.NewError500(e.Course_assign_user_status_failed, "Assign user stats to courses error: "+err.Error())
	}
	userStatsByCourseCuids := make(map[string]*models.UserCourseStats)
	for _, userStat := range userStats {
		userStatsByCourseCuids[userStat.CourseCuid] = userStat
	}

	for _, course := range courses {
		course.SetIsPaid(false)
		course.SetIsEnrolled(false)
		course.SetIsWishlist(false)
		course.SetBookmark(nil)
		if userStat, found := userStatsByCourseCuids[course.GetCourseCUID()]; found {
			course.SetIsPaid(userStat.IsPaid)
			course.SetIsEnrolled(userStat.IsEnrolled)
			if userStat.Bookmark != nil && userStat.Bookmark.ID != "" {
				course.SetBookmark(userStat.Bookmark)
				course.SetIsWishlist(true)
			}
		}
	}
	return nil
}

func (s *CourseService) getUserOrderItemsByCourseCUIDs(user *models.User, courseCUIDs []string) (map[string]*models.OrderItem, error) {
	orderItems, err := models.Repository.OrderItem.FindMany(&models.OrderItemQuery{
		EntityCuidIn: courseCUIDs,
		EntityType:   util.NewT(models.CourseModelName),
		UserID:       &user.ID,
		Status:       util.NewT(models.OrderStatusSuccess),
	}, nil)
	if err != nil {
		return nil, err
	}

	orderItemsByCourseCUIDs := make(map[string]*models.OrderItem)
	for _, orderItem := range orderItems {
		orderItemsByCourseCUIDs[orderItem.EntityCuid] = orderItem
	}
	return orderItemsByCourseCUIDs, nil
}

func (s *CourseService) getUserEnrollmentsByCourses(user *models.User, courseCUIDs []string) (map[string]*models.CourseEnrollment, error) {
	enrollments, err := models.Repository.CourseEnrollment(s.ctx).FindMany(&models.CourseEnrollmentQuery{
		CourseCuidIn: courseCUIDs,
		UserID:       &user.ID,
	}, nil)
	if err != nil {
		return nil, err
	}

	enrollmentsByCourseCUIDs := make(map[string]*models.CourseEnrollment)
	for _, enrollment := range enrollments {
		enrollmentsByCourseCUIDs[enrollment.CourseCuid] = enrollment
	}
	return enrollmentsByCourseCUIDs, nil
}

func (s *CourseService) getUserBookmarksByCourses(user *models.User, courseCUIDs []string) (map[string]*models.Bookmark, error) {
	bookmarks, err := models.Repository.Bookmark.FindMany(&models.BookmarkQuery{
		EntityIDIn: courseCUIDs,
		EntityType: util.NewT(models.CourseModelName),
		UserID:     &user.ID,
	}, nil)
	if err != nil {
		return nil, err
	}

	bookmarksByCourseCUIDs := make(map[string]*models.Bookmark)
	for _, bookmark := range bookmarks {
		bookmarksByCourseCUIDs[bookmark.EntityID] = bookmark
	}
	return bookmarksByCourseCUIDs, nil
}

func (s *CourseService) UnPublishCourse(user *models.User, cuid string, target models.UnPublishCourseTarget) *e.AppError {
	_, err := PublishCourse.UnPublishCourse(cuid, target)
	if err != nil {
		return err
	}

	// todo: send noti
	return nil
}

func (s *CourseService) CancelRequest(course *models.Course) *e.AppError {
	if !lo.Contains([]models.CourseStatus{models.CourseSTTReviewing, models.CourseSTTUnCancelled}, course.Status) {
		return e.NewError400(e.Course_is_not_reviewing, "course is not reviewing")
	}
	approvals, aErr := Approval.FindMany(&models.ApprovalQuery{
		EntityType: util.NewString(string(models.CourseModelName)),
		EntityID:   util.NewString(course.ID),
	}, nil)
	if aErr != nil {
		return aErr
	}
	for _, approval := range approvals {
		if approval.Status == models.ApprovalStatusNew || approval.Status == models.ApprovalStatusPending {
			approval.Status = models.ApprovalStatusCancel
			if uErr := Approval.Update(approval); uErr != nil {
				return uErr
			}
		}
	}
	course.Status = models.CourseSTTUnCancelled
	if uErr := models.Repository.Course(s.ctx).UpdateBasic(course, nil); uErr != nil {
		return e.NewError500(e.Update_course_failed, uErr.Error())
	}
	return nil
}

func (s *CourseService) CloneNewVersion(course *models.Course, user *models.User) (*models.Course, *e.AppError) {
	//get course details, preload: files, partners, categories
	//Get all section and lesson
	segments, _, sErr := Section.FindPage(&models.SectionQuery{
		CourseID:       util.NewString(course.ID),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindPageOptions{PerPage: util.PerPageMax})
	if sErr != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "CourseService::CloneCourse: "+sErr.Msg)
	}

	// Get all lesson content
	lessonContents, _, lErr := LessonContent.FindPage(&models.LessonContentQuery{
		CourseID:       util.NewString(course.ID),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindPageOptions{
		PerPage:  util.PerPageMax,
		Preloads: []string{util.FilesField},
	})
	if lErr != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "CourseService::CloneCourse: "+lErr.Msg)
	}

	// Preload quizzes for all lesson contents
	if err := Quiz.PreloadQuizzesForLessonContents(lessonContents); err != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "CourseService::CloneCourse: "+err.Msg)
	}

	// Clone course
	newCourse, ncErr := s.cloneCourse(course)
	if ncErr != nil {
		return nil, ncErr
	}

	sections := lo.Filter(segments, func(item *models.Section, _ int) bool {
		return item.ParentID == ""
	})

	// group lesson content by lessons
	mapLessonContents := lo.Reduce(lessonContents,
		func(agg map[string][]*dto.LessonContentRequest,
			item *models.LessonContent,
			_ int) map[string][]*dto.LessonContentRequest {
			contents := agg[item.LessonID]
			if contents == nil {
				contents = []*dto.LessonContentRequest{}
			}
			contents = append(contents, &dto.LessonContentRequest{
				OrgID:       course.OrgID,
				UID:         &item.UID,
				CourseID:    newCourse.ID,
				Title:       item.Title,
				Content:     item.Content,
				Duration:    item.Duration,
				Type:        item.Type,
				Order:       item.Order,
				Files:       item.Files,
				JsonContent: item.JsonContent,
				Quizzes: lo.Map(item.Quizzes, func(quizWithRelation *models.QuizWithRelation, _ int) *dto.QuizRequest {
					return Quiz.ConvertQuizToQuizRequest(quizWithRelation)
				}),
			})
			agg[item.LessonID] = contents
			return agg
		}, map[string][]*dto.LessonContentRequest{})

	// build section request to bulk create sections
	var sectionReqs []*dto.UpdateSectionRequest
	for _, section := range sections {
		// copy section data
		sectionReq := sectionToCreateSectionRequest(section, newCourse)
		sectionReq.User = user
		lessonBySections := lo.Filter(segments, func(item *models.Section, _ int) bool {
			return item.ParentID == section.ID
		})
		lessonReqs := lo.Reduce(lessonBySections, func(agg []*dto.CreateSectionRequest, item *models.Section, _ int) []*dto.CreateSectionRequest {
			// copy lesson data
			lessonReq := sectionToCreateSectionRequest(item, newCourse)
			lessonReq.Contents = mapLessonContents[item.ID] // copy lesson content
			agg = append(agg, lessonReq)
			return agg
		}, []*dto.CreateSectionRequest{})

		req := dto.UpdateSectionRequest{
			CreateSectionRequest: *sectionReq,
			Lessons:              lessonReqs,
		}
		sectionReqs = append(sectionReqs, &req)
	}

	bulkUpdateSections := dto.BulkUpdateSectionRequest{
		CourseID: newCourse.ID,
		Sections: sectionReqs,
	}
	if _, bulkErr := Section.BulkUpdate(&bulkUpdateSections, user); bulkErr != nil {
		return newCourse, bulkErr
	}

	// Clone forms and form relations
	if len(course.FormRelations) > 0 {
		formIDs := lo.Map(course.FormRelations, func(formRelation *models.FormRelation, _ int) string {
			return formRelation.FormID
		})
		formIDs = lo.Uniq(formIDs)
		forms, err := models.Repository.Form.FindMany(&models.FormQuery{
			IDIn: formIDs,
		}, models.FindManyFormsOptsFullPreloads)
		if err != nil {
			return nil, e.NewError500(e.Form_find_failed, "Find forms error: "+err.Error())
		}

		newFormsByOldIDs := map[string]*models.Form{}
		for _, form := range forms {
			newForm, appErr := Form.Duplicate(form, true)
			if appErr != nil {
				return nil, appErr
			}

			newFormsByOldIDs[form.ID] = newForm
		}

		var newFormRelations []*models.FormRelation
		for _, formRelation := range course.FormRelations {
			newForm, found := newFormsByOldIDs[formRelation.FormID]
			if !found {
				continue
			}
			newFormRelations = append(newFormRelations, &models.FormRelation{
				FormID:               newForm.ID,
				FormUID:              newForm.UID,
				RelatedEntityID:      newCourse.ID,
				RelatedEntityUID:     newCourse.Cuid,
				RelatedEntityType:    models.CourseModelName,
				Enabled:              formRelation.Enabled,
				OrgID:                formRelation.OrgID,
				OrgSchema:            formRelation.OrgSchema,
				StartWhen:            formRelation.StartWhen,
				EndWhen:              formRelation.EndWhen,
				ConfirmationSettings: formRelation.ConfirmationSettings,
				Type:                 formRelation.Type,
				Name:                 formRelation.Name,
				AddDate:              lo.If(formRelation.AddDate == 0, int(time.Now().UnixMilli())).Else(formRelation.AddDate),
				HasCancelBtn:         formRelation.HasCancelBtn,
			})
		}

		if err = models.Repository.FormRelation.CreateMany(newFormRelations, nil); err != nil {
			return nil, e.NewError500(e.Form_create_relation_failed, "Create form relations error: "+err.Error())
		}
	}

	if ulErr := models.Repository.Course(s.ctx).UpdateLatestVersions(course.Cuid); ulErr != nil {
		return nil, e.NewError500(e.Update_course_failed, "UpdateLatestVersions: "+ulErr.Error())
	}

	return newCourse, nil
}

func (s *CourseService) cloneCourse(course *models.Course) (*models.Course, *e.AppError) {
	props := course.Props
	props.PreviousVersion = course.Version
	props.PreviousID = course.ID
	props.ApprovalUid = course.Props.ApprovalUid
	props.RequestID = course.ID
	props.RequestVersion = course.Version
	props.PreApprovalUid = course.Props.PreApprovalUid
	if props.MintCertNFTSettings == nil {
		props.MintCertNFTSettings = models.MakeDefaultMintCertNFTSettings()
	}
	props.CourseEnrollmentEmailTemplateCode = course.Props.CourseEnrollmentEmailTemplateCode
	newCourse := models.Course{
		OrgID:       course.OrgID,
		Cuid:        course.Cuid,
		Version:     course.Version + 1,
		Latest:      true,
		Name:        course.Name,
		Slug:        course.Slug,
		Description: course.Description,
		ShortDesc:   course.ShortDesc,
		ThumbnailID: course.ThumbnailID,
		LearnMethod: course.LearnMethod,
		UserID:      course.UserID,
		PriceSettings: &models.CoursePrice{
			IsPay:                course.PriceSettings.IsPay,
			FiatCurrency:         course.PriceSettings.FiatCurrency,
			FiatPrice:            course.PriceSettings.FiatPrice,
			FiatDiscountPrice:    course.PriceSettings.FiatDiscountPrice,
			CryptoPaymentEnabled: course.PriceSettings.CryptoPaymentEnabled,
			CryptoCurrency:       course.PriceSettings.CryptoCurrency,
			CryptoPrice:          course.PriceSettings.CryptoPrice,
			CryptoDiscountPrice:  course.PriceSettings.CryptoDiscountPrice,
		},
		Categories:       course.Categories,
		Levels:           course.Levels,
		SectionCount:     course.SectionCount,
		LessonCount:      course.LessonCount,
		ActiveLesson:     course.ActiveLesson,
		Status:           models.CourseSTTDraft,
		Medias:           course.Medias,
		Docs:             course.Docs,
		Props:            props,
		Enable:           course.Enable,
		StartDate:        course.StartDate,
		EndDate:          course.EndDate,
		MarkAsCompleted:  course.MarkAsCompleted,
		HasCertificate:   course.HasCertificate,
		AIGenerateStatus: course.AIGenerateStatus,
		AICourseID:       course.AICourseID,
		AICourse:         course.AICourse,
		IsAIGenerated:    course.IsAIGenerated,
	}

	if cErr := models.Repository.Course(s.ctx).Create(&newCourse, nil); cErr != nil {
		return nil, e.NewError500(e.Create_course_failed, cErr.Error())
	}

	return &newCourse, nil
}

func sectionToCreateSectionRequest(section *models.Section, course *models.Course) *dto.CreateSectionRequest {
	return &dto.CreateSectionRequest{
		OrgID:    course.OrgID,
		UID:      &section.UID,
		Title:    section.Title,
		Note:     section.Note,
		Order:    section.Order,
		Free:     section.Free,
		Status:   &section.Status,
		Course:   course,
		CourseID: course.ID,
	}
}

func (s *CourseService) ExtendResponseForOutline(course *models.Course, user *models.User) (appErr *e.AppError) {
	if appErr = AssignUserStatusToCourses(s.ctx, user, []*models.Course{course}); appErr != nil {
		return appErr
	}

	queryCoursePartner := models.CoursePartnerQuery{
		CourseID: util.NewString(course.Cuid),
	}
	partners, appErr := Course.FindManyCoursePartners(&queryCoursePartner, &models.FindManyOptions{
		Preloads: []string{models.PartnerField},
	})
	if appErr != nil {
		return
	}

	course.Partners = lo.Map(partners, func(p *models.CoursePartner, _ int) *models.SimplePartner { return p.ToSimplePartner() })

	// Check user submitted forms
	formUIDs := lo.Map(course.FormRelations, func(formRelation *models.FormRelation, _ int) string {
		return formRelation.FormUID
	})
	hasSubmittedByFormUIDs, appErr := Form.CheckSubmittedByUIDs(user, formUIDs)
	if appErr != nil {
		return
	}

	for _, formRelation := range course.FormRelations {
		formRelation.Submitted = util.NewBool(hasSubmittedByFormUIDs[formRelation.FormUID])
	}

	// Check user received certificate or not
	if course.MarkAsCompleted && course.HasCertificate {
		cert, cErr := models.Repository.Certificate.FindOne(&models.CertificateQuery{
			CourseCuid: &course.Cuid,
			UserID:     &user.ID,
			OrgID:      &course.OrgID}, &models.FindOneOptions{})
		if cErr != nil && !models.IsRecordNotFound(cErr) {
			return e.NewError500(e.CertificateFindFailed, "Find certificate error: "+cErr.Error())
		}

		course.IsReceivedCert = cert != nil
	}

	return nil
}

func (s *CourseService) GetCourseFromPublishCourse(cuid string, id string) (*models.Course, *models.PublishCourse, *e.AppError) {
	pubCourse, pcErr := PublishCourse.FindOne(&models.PublishCourseQuery{
		CourseCuid: util.NewString(cuid),
		CourseID:   util.NewString(id),
	}, nil)
	if pcErr != nil {
		return nil, nil, pcErr
	}

	course, cErr := models.Repository.Course(s.ctx).FindOne(
		&models.CourseQuery{ID: util.NewString(id)},
		nil)

	if cErr != nil {
		return nil, nil, e.NewError400(e.Course_not_found, fmt.Sprintf("find course by schema failed: %s - %s", pubCourse.OrgSchema, id))
	}

	return course, pubCourse, nil
}

func (s *CourseService) FindPageCoursesByUser(
	req *dto.GetMyCoursesRequest,
	options *models.FindPageOptions,
) ([]*models.Course, *models.Pagination, *e.AppError) {

	var pagination *models.Pagination
	var enrollments []*models.CourseEnrollment
	var learningStatues []*models.LearningStatus
	var pubCourses []*models.PublishCourse
	var err error

	// Find course CUIDs by group
	switch req.Group {
	case models.CourseGroupInProgress,
		models.CourseGroupCompleted:
		query := &models.LearningStatusQuery{
			UserID:                &req.User.ID,
			IsCompleted:           util.NewBool(req.Group == models.CourseGroupCompleted),
			ExcludeDeletedCourses: util.NewBool(true),
			IncludeDeleted:        util.NewBool(false),
		}

		if req.ExcludeUnPublishCourses != nil {
			query.ExcludeUnPublishCourses = req.ExcludeUnPublishCourses
		}

		isRoot := req.Org.Domain == setting.AppSetting.BaseDomain
		// If not root domain then default get only organization's course
		if !isRoot {
			query.OrgID = &req.Org.ID
		}

		lpOptions := &models.FindPageOptions{
			Page:    options.Page,
			PerPage: options.PerPage,
			Sort:    options.Sort,
		}
		learningStatues, pagination, err = models.Repository.LearningStatus(s.ctx).FindPage(query, lpOptions)
		if err != nil {
			return nil, nil, e.NewError500(e.Course_find_page_failed, "Find learning progresses error: "+err.Error())
		}

		courseCUIDs := lo.Map(learningStatues, func(status *models.LearningStatus, _ int) string {
			return status.CourseCuid
		})

		if len(courseCUIDs) == 0 {
			return []*models.Course{}, models.NewPagination(options.Page, options.PerPage, 0), nil
		}

		pubCourses, err = models.Repository.PublishCourse(s.ctx).FindMany(&models.PublishCourseQuery{
			CourseCuidIn: courseCUIDs,
		}, nil)
		if err != nil {
			return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
		}

		if len(pubCourses) == 0 {
			return []*models.Course{}, models.NewPagination(options.Page, options.PerPage, 0), nil
		}

	case models.CourseGroupNotStarted:
		query := &models.CourseEnrollmentQuery{
			UserID:                 &req.User.ID,
			ExcludeStartedLearning: util.NewBool(true),
			ExcludeDeletedCourses:  util.NewBool(true),
			IncludeDeleted:         util.NewBool(false),
		}

		if req.ExcludeUnPublishCourses != nil {
			query.ExcludeUnPublishCourses = req.ExcludeUnPublishCourses
		}

		isRoot := req.Org.Domain == setting.AppSetting.BaseDomain
		// If not root domain then default get only organization's course
		if !isRoot {
			query.OrgID = &req.Org.ID
		}
		ceOptions := &models.FindPageOptions{
			Page:    options.Page,
			PerPage: options.PerPage,
			Sort:    options.Sort,
		}
		enrollments, pagination, err = models.Repository.CourseEnrollment(s.ctx).FindPage(query, ceOptions)
		if err != nil {
			return nil, nil, e.NewError500(e.Course_find_page_failed, "Find course enrollments error: "+err.Error())
		}
		courseCUIDs := lo.Map(enrollments, func(enrollment *models.CourseEnrollment, _ int) string {
			return enrollment.CourseCuid
		})

		if len(courseCUIDs) == 0 {
			return []*models.Course{}, models.NewPagination(options.Page, options.PerPage, 0), nil
		}

		pubCourses, err = models.Repository.PublishCourse(s.ctx).FindMany(&models.PublishCourseQuery{
			CourseCuidIn: courseCUIDs,
		}, nil)
		if err != nil {
			return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
		}

		if len(pubCourses) == 0 {
			return []*models.Course{}, models.NewPagination(options.Page, options.PerPage, 0), nil
		}

	case models.CourseGroupWishlist:
		query := &models.PublishCourseQuery{
			BookmarkedBy:   &req.User.ID,
			IncludeDeleted: util.NewBool(false),
			//IsRoot:         util.NewBool(req.Org.Domain == setting.AppSetting.BaseDomain),
		}

		isRoot := req.Org.Domain == setting.AppSetting.BaseDomain
		// If not root domain then default get only organization's course
		if !isRoot {
			query.OrgID = &req.Org.ID
		}

		pcOptions := &models.FindPageOptions{
			Page:    options.Page,
			PerPage: options.PerPage,
			Sort:    options.Sort,
		}
		pubCourses, pagination, err = models.Repository.PublishCourse(s.ctx).FindPage(query, pcOptions)
		if err != nil {
			return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
		}

		if len(pubCourses) == 0 {
			return []*models.Course{}, models.NewPagination(options.Page, options.PerPage, 0), nil
		}

	default:
		return nil, nil, e.NewError400(e.INVALID_PARAMS, "Invalid course group: "+string(req.Group))
	}

	var preloadLearnProgressOverview bool
	if lo.Contains(options.Preloads, models.LearningProgressOverviewField) {
		preloadLearnProgressOverview = true
		options.Preloads = util.RemoveElement(options.Preloads, models.LearningProgressOverviewField)
	}

	courses, appErr := s.findCoursesByPublishCourses(req.User, pubCourses, &models.FindManyOptions{
		Preloads: options.Preloads,
	}, preloadLearnProgressOverview)
	if appErr != nil {
		return nil, nil, appErr
	}

	// Sort output
	var output []*models.Course
	switch req.Group {
	case models.CourseGroupInProgress,
		models.CourseGroupCompleted:
		for _, item := range learningStatues {
			c, found := lo.Find(courses, func(cm *models.Course) bool {
				return cm.Cuid == item.CourseCuid
			})
			if found {
				output = append(output, c)
			}
		}

	case models.CourseGroupNotStarted:
		for _, item := range enrollments {
			c, found := lo.Find(courses, func(cm *models.Course) bool {
				return cm.Cuid == item.CourseCuid
			})
			if found {
				output = append(output, c)
			}
		}

	case models.CourseGroupWishlist:
		for _, item := range pubCourses {
			c, found := lo.Find(courses, func(cm *models.Course) bool {
				return cm.Cuid == item.CourseCuid
			})
			if found {
				output = append(output, c)
			}
		}
	}

	return output, pagination, nil
}

func (s *CourseService) CountCoursesByUser(req *dto.CountMyCoursesRequest) (int64, *e.AppError) {
	var count int64
	var err error

	// Find course CUIDs by group
	switch req.Group {
	case models.CourseGroupInProgress,
		models.CourseGroupCompleted:
		query := &models.LearningStatusQuery{
			UserID:                &req.User.ID,
			IsCompleted:           util.NewBool(req.Group == models.CourseGroupCompleted),
			ExcludeDeletedCourses: util.NewBool(true),
			IncludeDeleted:        util.NewBool(false),
		}

		if req.ExcludeUnPublishCourses != nil {
			query.ExcludeUnPublishCourses = req.ExcludeUnPublishCourses
		}

		isRoot := req.Org.Domain == setting.AppSetting.BaseDomain
		// If not root domain then default get only organization's course
		if !isRoot {
			query.OrgID = &req.Org.ID
		}

		count, err = models.Repository.LearningStatus(s.ctx).Count(query)
		if err != nil {
			return 0, e.NewError500(e.Course_find_page_failed, "Find learning progresses error: "+err.Error())
		}

	case models.CourseGroupNotStarted:
		query := &models.CourseEnrollmentQuery{
			UserID:                 &req.User.ID,
			ExcludeStartedLearning: util.NewBool(true),
			ExcludeDeletedCourses:  util.NewBool(true),
			IncludeDeleted:         util.NewBool(false),
		}

		if req.ExcludeUnPublishCourses != nil {
			query.ExcludeUnPublishCourses = req.ExcludeUnPublishCourses
		}

		isRoot := req.Org.Domain == setting.AppSetting.BaseDomain
		// If not root domain then default get only organization's course
		if !isRoot {
			query.OrgID = &req.Org.ID
		}

		count, err = models.Repository.CourseEnrollment(s.ctx).Count(query)
		if err != nil {
			return 0, e.NewError500(e.Course_find_page_failed, "Find course enrollments error: "+err.Error())
		}

	case models.CourseGroupWishlist:
		query := &models.PublishCourseQuery{
			BookmarkedBy:   &req.User.ID,
			IncludeDeleted: util.NewBool(false),
		}

		isRoot := req.Org.Domain == setting.AppSetting.BaseDomain
		// If not root domain then default get only organization's course
		if !isRoot {
			query.OrgID = &req.Org.ID
		}

		count, err = models.Repository.PublishCourse(s.ctx).Count(query)
		if err != nil {
			return 0, e.NewError500(e.Course_find_page_failed, "Find course enrollments error: "+err.Error())
		}

	default:
		return 0, e.NewError400(e.INVALID_PARAMS, "Invalid course group: "+string(req.Group))
	}

	return count, nil
}

func (s *CourseService) ReplyFeedbackPublishCourse(approval *models.Approval, course *models.Course, feedback *dto.ApprovalFeedback) (*models.Course, *e.AppError) {
	isReplyRoot := approval.Type == models.ApproveTypePublishRoot
	includeChange := feedback.EntityID != nil
	user := feedback.User
	feedback.EntityVersion = &course.Version

	if !isReplyRoot || (isReplyRoot && !includeChange) {
		aErr := Approval.ApprovalSendFeedback(approval, user, feedback)
		if aErr != nil {
			return nil, aErr
		}
		if includeChange {
			newCourse, newcErr := s.CloneNewVersion(course, user)
			course.Status = models.CourseSTTReviewing
			course.Latest = false
			if udErr := models.Repository.Course(s.ctx).UpdateBasic(course, nil); udErr != nil {
				return nil, e.NewError500(e.Course_publish_failed, "Update current version failed: "+udErr.Error())
			}
			return newCourse, newcErr
		}
	} else {
		// reply to root and include change
		orgApproval, oaErr := Approval.FindOne(&models.ApprovalQuery{
			RequestUid: util.NewString(approval.RequestUid),
			Type:       util.NewT(models.ApproveTypePublishOrg),
		}, nil)
		if oaErr != nil {
			return nil, oaErr
		}
		aErr := Approval.ApprovalSendFeedback(orgApproval, user, feedback)
		if aErr != nil {
			return nil, aErr
		}
		if includeChange {
			newCourse, newcErr := s.CloneNewVersion(course, user)
			course.Status = models.CourseSTTReviewing
			course.Latest = false
			if udErr := models.Repository.Course(s.ctx).UpdateBasic(course, nil); udErr != nil {
				return nil, e.NewError500(e.Course_publish_failed, "Update current version failed: "+udErr.Error())
			}
			return newCourse, newcErr
		}
	}

	return course, nil
}

func (s *CourseService) buildSlug(courseName string) (string, *e.AppError) {
	newSlug := ""
	// Attempt generate slugs
	if _, err := lo.Attempt(generateCourseSlugMaxAttempts, func(_ int) error {
		slug, err := util.Slugify(courseName, 5)
		if err != nil {
			return err
		}

		existingCourse, err := models.Repository.Course(s.ctx).FindOne(&models.CourseQuery{
			Slug: &slug,
		}, nil)
		if err != nil && !models.IsRecordNotFound(err) {
			return err
		}

		if existingCourse != nil {
			return fmt.Errorf("course slug already exists")
		}

		newSlug = slug
		return nil
	}); err != nil {
		return "", e.NewError500(e.Create_course_failed, "CourseService::DuplicateCourse:buildSlug: "+err.Error())
	}
	return newSlug, nil
}

func (s *CourseService) DuplicateCourse(org *models.Organization, course *models.Course, user *models.User) (*models.Course, *e.AppError) {
	//get course details, preload: files, partners, categories
	segments, _, sErr := Section.FindPage(&models.SectionQuery{
		CourseID:       util.NewString(course.ID),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindPageOptions{PerPage: util.PerPageMax})
	if sErr != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "CourseService::DuplicateCourse:Section.FindPage "+sErr.Msg)
	}
	// Get all lesson content
	lessonContents, _, lErr := LessonContent.FindPage(&models.LessonContentQuery{
		CourseID:       util.NewString(course.ID),
		IncludeDeleted: util.NewBool(false),
	}, &models.FindPageOptions{
		PerPage:  util.PerPageMax,
		Preloads: []string{util.FilesField},
	})
	if lErr != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "CourseService::DuplicateCourse:LessonContent.FindPage "+lErr.Msg)
	}

	// Preload quizzes for all lesson contents
	if err := Quiz.PreloadQuizzesForLessonContents(lessonContents); err != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "CourseService::DuplicateCourse:PreloadQuizzesForLessonContents "+err.Msg)
	}
	slug, slugErr := s.buildSlug(course.Name)
	if slugErr != nil {
		return nil, slugErr
	}
	oldProps := course.Props
	props := models.CourseProps{
		PreviousVersion:      0,
		SupportChannel:       oldProps.SupportChannel,
		CertificateCondition: models.MakeDefaultCertificateCondition(),
		MintCertNFTSettings:  models.MakeDefaultMintCertNFTSettings(),
		Achievements:         oldProps.Achievements,
		DefaultLanguage:      oldProps.DefaultLanguage,
		PreviewLessons:       oldProps.PreviewLessons,
	}

	newCourse := models.Course{
		OrgID:       org.ID,
		Cuid:        util.GenerateId(),
		Version:     1,
		Latest:      true,
		Name:        course.Name,
		Slug:        slug,
		Description: course.Description,
		ShortDesc:   course.ShortDesc,
		ThumbnailID: course.ThumbnailID,
		LearnMethod: course.LearnMethod,
		UserID:      course.UserID,
		PriceSettings: &models.CoursePrice{
			IsPay:                course.PriceSettings.IsPay,
			FiatCurrency:         course.PriceSettings.FiatCurrency,
			FiatPrice:            course.PriceSettings.FiatPrice,
			FiatDiscountPrice:    course.PriceSettings.FiatDiscountPrice,
			CryptoPaymentEnabled: course.PriceSettings.CryptoPaymentEnabled,
			CryptoCurrency:       course.PriceSettings.CryptoCurrency,
			CryptoPrice:          course.PriceSettings.CryptoPrice,
			CryptoDiscountPrice:  course.PriceSettings.CryptoDiscountPrice,
		},
		Categories:      course.Categories,
		Levels:          course.Levels,
		SectionCount:    course.SectionCount,
		LessonCount:     course.LessonCount,
		ActiveLesson:    course.ActiveLesson,
		Status:          models.CourseSTTDraft,
		Medias:          course.Medias,
		Docs:            course.Docs,
		Props:           props,
		Enable:          course.Enable,
		StartDate:       course.StartDate,
		EndDate:         course.EndDate,
		MarkAsCompleted: course.MarkAsCompleted,
		HasCertificate:  course.HasCertificate,
	}

	if cErr := models.Repository.Course(s.ctx).Create(&newCourse, nil); cErr != nil {
		return nil, e.NewError500(e.Create_course_failed, cErr.Error())
	}

	_, pErr := s.addPartners(&newCourse, org, user, []*dto.PartnerRequest{})
	if pErr != nil {
		return &newCourse, e.NewError500(e.Update_course_partner_failed, pErr.Msg)
	}

	sections := lo.Filter(segments, func(item *models.Section, _ int) bool {
		return item.ParentID == ""
	})

	// group lesson content by lessons
	mapLessonContents := lo.Reduce(lessonContents,
		func(agg map[string][]*dto.LessonContentRequest,
			item *models.LessonContent,
			_ int) map[string][]*dto.LessonContentRequest {
			contents := agg[item.LessonID]
			if contents == nil {
				contents = []*dto.LessonContentRequest{}
			}

			contents = append(contents, &dto.LessonContentRequest{
				OrgID:       org.ID,
				UID:         util.NewString(util.GenerateId()),
				CourseID:    newCourse.ID,
				Title:       item.Title,
				Content:     item.Content,
				Duration:    item.Duration,
				Type:        item.Type,
				Order:       item.Order,
				Files:       item.Files,
				JsonContent: item.JsonContent, // todo
				Quizzes: lo.Map(item.Quizzes, func(quizWithRelation *models.QuizWithRelation, _ int) *dto.QuizRequest {
					return Quiz.ConvertQuizToQuizRequest(quizWithRelation)
				}),
			})
			agg[item.LessonID] = contents
			return agg
		}, map[string][]*dto.LessonContentRequest{})

	// build section request to bulk create sections
	var sectionReqs []*dto.UpdateSectionRequest
	for _, section := range sections {
		// copy section data
		section.UID = util.GenerateId()
		section.OrgID = org.ID
		sectionReq := sectionToCreateSectionRequest(section, &newCourse)
		sectionReq.User = user
		lessonBySections := lo.Filter(segments, func(item *models.Section, _ int) bool {
			return item.ParentID == section.ID
		})
		lessonReqs := lo.Reduce(lessonBySections, func(agg []*dto.CreateSectionRequest, item *models.Section, _ int) []*dto.CreateSectionRequest {
			// copy lesson data
			item.UID = util.GenerateId()
			item.OrgID = org.ID
			lessonReq := sectionToCreateSectionRequest(item, &newCourse)
			lessonReq.Contents = mapLessonContents[item.ID] // copy lesson content
			agg = append(agg, lessonReq)
			return agg
		}, []*dto.CreateSectionRequest{})

		req := dto.UpdateSectionRequest{
			CreateSectionRequest: *sectionReq,
			Lessons:              lessonReqs,
		}
		sectionReqs = append(sectionReqs, &req)
	}

	bulkUpdateSections := dto.BulkUpdateSectionRequest{
		CourseID: newCourse.ID,
		Sections: sectionReqs,
	}
	if _, bulkErr := Section.BulkUpdate(&bulkUpdateSections, user); bulkErr != nil {
		return &newCourse, bulkErr
	}

	// Clone forms and form relations
	if len(course.FormRelations) > 0 {
		formIDs := lo.Map(course.FormRelations, func(formRelation *models.FormRelation, _ int) string {
			return formRelation.FormID
		})
		formIDs = lo.Uniq(formIDs)
		forms, err := models.Repository.Form.FindMany(&models.FormQuery{
			IDIn: formIDs,
		}, models.FindManyFormsOptsFullPreloads)
		if err != nil {
			return nil, e.NewError500(e.Form_find_failed, "Find forms error: "+err.Error())
		}

		newFormsByOldIDs := map[string]*models.Form{}
		for _, form := range forms {
			newForm, appErr := Form.Duplicate(form, false)
			if appErr != nil {
				return nil, appErr
			}

			newFormsByOldIDs[form.ID] = newForm
		}

		var newFormRelations []*models.FormRelation
		for _, formRelation := range course.FormRelations {
			newForm, found := newFormsByOldIDs[formRelation.FormID]
			if !found {
				continue
			}
			newFormRelations = append(newFormRelations, &models.FormRelation{
				FormID:               newForm.ID,
				FormUID:              newForm.UID,
				RelatedEntityID:      newCourse.ID,
				RelatedEntityUID:     newCourse.Cuid,
				RelatedEntityType:    models.CourseModelName,
				OrgID:                formRelation.OrgID,
				OrgSchema:            formRelation.OrgSchema,
				StartWhen:            formRelation.StartWhen,
				EndWhen:              formRelation.EndWhen,
				ConfirmationSettings: formRelation.ConfirmationSettings,
			})
		}

		if err = models.Repository.FormRelation.CreateMany(newFormRelations, nil); err != nil {
			return nil, e.NewError500(e.Form_create_relation_failed, "Create form relations error: "+err.Error())
		}
	}

	return &newCourse, nil
}

func (s *CourseService) ExtendOutlineLearningProgressOverview(user *models.User, course *models.Course) *e.AppError {
	if user != nil {
		var completedLessonCounts []*models.CompletedLessonCountByUser
		var err error
		completedLessonCounts, err = models.Repository.LearningStatus(context.TODO()).CountCompletedLessonsByUsers([]string{course.ID}, []string{user.ID})

		if err != nil {
			return e.NewError500(e.Course_find_publish_failed, "Preload learning progress overview failed: "+err.Error())
		}

		completedLessonCountsByCourseCUIDs := make(map[string]*models.CompletedLessonCountByUser)
		for _, completedLessonCount := range completedLessonCounts {
			completedLessonCountsByCourseCUIDs[completedLessonCount.CourseCUID] = completedLessonCount
		}

		var currentSections map[string][]*models.Section
		currentSections, err = models.Repository.LearningStatus(s.ctx).FindCurrentSectionsAndLessonsByUsers(course.ID, course.Cuid, []string{user.ID})
		if err != nil {
			return e.NewError500(e.Course_find_publish_failed, "Preload learning progress overview failed: "+err.Error())
		}
		currentSectionsByUserID := make(map[string]*models.Section)
		currentLessonsByUserID := make(map[string]*models.Section)

		for userID, listSection := range currentSections {
			for _, section := range listSection {
				if section.IsLesson() {
					currentLessonsByUserID[userID] = section
				} else {
					currentSectionsByUserID[userID] = section

				}
			}
		}

		course.LearningProgressOverview = &models.SimpleLearningProgressOverview{}
		if completedLessonCount, found := completedLessonCountsByCourseCUIDs[course.Cuid]; found {
			course.LearningProgressOverview.TotalLesson = completedLessonCount.TotalLessons
			course.LearningProgressOverview.CompletedLesson = completedLessonCount.CompletedLessons
		}

		if section, found := currentSectionsByUserID[user.ID]; found {
			course.LearningProgressOverview.CurrentSection = section
		}

		if lesson, found := currentLessonsByUserID[user.ID]; found {
			course.LearningProgressOverview.CurrentLesson = lesson
		}
	}
	return nil
}

func (s *CourseService) RequestPublishCourseLaunchpad(request *dto.PublishCourseRequest) (*models.Course, *e.AppError) {
	course := request.Course
	var approval *models.Approval

	if course.Status != models.CourseSTTPublic {
		uid := util.GenerateId()
		pub := &dto.CreateApprovalRequest{
			EntityType:    models.CourseModelName,
			EntityID:      course.ID,
			EntityUID:     course.Cuid,
			Type:          models.ApproveTypePublishOrg,
			RequestUid:    uid,
			EntityVersion: course.Version,
		}

		publish, pubErr := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
			CourseCuid: util.NewString(course.Cuid),
		}, nil)
		if pubErr != nil && !errors.Is(pubErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Publish_course_find_failed, "find publish course failed")
		}

		if publish != nil {
			pub.PreVersion = util.NewT(publish.Version)
			pub.PreID = util.NewString(publish.CourseID)
		}

		if a, err := Approval.Create(request.Org, request.Requester, pub); err != nil {
			return nil, err
		} else {
			approval = a
		}
		if caErr := Approval.CancelOldRequest(approval); caErr != nil {
			return nil, caErr
		}
	}

	if course.Version == course.Props.PreviousVersion {
		course.Version = course.Version + 1
	}

	course.Props.PreApprovalUid = course.Props.ApprovalUid
	course.Props.ApprovalUid = approval.RequestUid
	course.Props.RequestID = course.ID
	course.Props.RequestVersion = course.Version
	// freeze current version, clone new version for editing
	newCourse, cloneErr := s.CloneNewVersion(course, request.Requester)
	if cloneErr != nil {
		return nil, cloneErr
	}

	// update current version to reviewing
	course.Status = models.CourseSTTReviewing
	course.Latest = false
	if udErr := models.Repository.Course(s.ctx).UpdateBasic(course, nil); udErr != nil {
		return nil, e.NewError500(e.Course_publish_failed, "Update current version failed: "+udErr.Error())
	}

	if approval != nil {
		userRoleOrgs, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
			RoleIDIn: []string{models.OrgAdminRoleType, models.OrgModeratorRoleType},
			OrgID:    &approval.OrgID,
		}, nil)
		if err != nil {
			log.ErrorWithAlertf("Get list org admins and moderators of org ID %s error: %v", approval.OrgID, err)
		} else if len(userRoleOrgs) > 0 {
			var userIDs []string
			for _, urg := range userRoleOrgs {
				userIDs = append(userIDs, urg.UserID)
			}
			userIDs = lo.Uniq(userIDs)

			notificationReq := &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeRequestPublishCourseLaunchpadForAdmin,
				EntityID:   approval.ID, // Jump to course publish request
				EntityType: communicationdto.CourseApprovalEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: userIDs,
					},
				},
				Props: s.makeNotificationPropsForCourse(course, request.Org, request.Requester).IntoComm(),
			}
			if err := communication.Notification.PushNotification(notificationReq); err != nil {
				log.ErrorWithAlertf("Push notification to admin of org ID %s new publish request for course ID %s error: %v", approval.OrgID, course.ID, err)
			}
		}
	}

	return newCourse, nil
}

func (s *CourseService) CanViewReport(course *models.Course, user *models.User) (bool, *e.AppError) {
	if course.UserID == user.ID {
		return true, nil
	}

	for _, r := range user.Roles {
		if lo.Contains(CanViewReportCourseRoles, r.RoleID) {
			return true, nil
		}
	}

	partners, _, pErr := s.FindCoursePartners(&models.CoursePartnerQuery{
		CourseID:  util.NewString(course.Cuid),
		PartnerID: util.NewString(user.ID),
		IsActive:  util.NewBool(true),
	}, &models.FindPageOptions{PerPage: util.PerPageMax})

	if pErr != nil {
		log.Error("Get course partner failed: ", pErr)
		return false, e.NewError400(e.Course_update_need_permission_or_owner, "find course partner failed")
	}

	if lo.ContainsBy(partners, func(item *models.CoursePartner) bool {
		return lo.Contains(item.Permissions, string(models.CoursePerAll)) || lo.Contains(item.Permissions, string(models.CoursePerViewReport))
	}) {
		return false, nil
	}
	return false, nil
}

func (s *CourseService) GetCourseRevenueSummaryReport(query *models.CourseRevenueSummaryQuery, user *models.User) (*models.CourseRevenueSummary, *e.AppError) {
	roles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		return nil, e.NewError400(e.Find_user_role_failed, "find user role failed")
	}
	user.Roles = roles

	courses, err := models.Repository.Course(s.ctx).FindMany(&models.CourseQuery{CuidIn: query.CuidIn}, nil)
	if err != nil {
		return nil, e.NewError400(e.Course_revenue_summary_failed, "Find many courses failed")
	}

	for _, course := range courses {
		canViewReport, viewErr := s.CanViewReport(course, user)
		if viewErr != nil {
			return nil, viewErr
		}
		if !canViewReport {
			return nil, e.NewError403(e.Course_view_report_need_permission_or_owner, "Need permission to view report")
		}
	}

	summary, err := models.Repository.Course(s.ctx).GetCoursesRevenueSummary(query)
	if err != nil {
		return nil, e.NewError500(e.Course_revenue_summary_failed, "Get course revenue summary failed: "+err.Error())
	}
	return summary, nil
}

func (s *CourseService) GetCourseRevenueDetailReport(query *models.CourseRevenueDetailQuery, user *models.User, options *models.FindPageOptions) ([]*models.CourseRevenueOrderDetail, *models.Pagination, *e.AppError) {
	roles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		return nil, nil, e.NewError400(e.Find_user_role_failed, "find user role failed")
	}
	user.Roles = roles

	courses, err := models.Repository.Course(s.ctx).FindMany(&models.CourseQuery{CuidIn: query.CuidIn}, nil)
	if err != nil {
		return nil, nil, e.NewError400(e.Course_revenue_summary_failed, "Find many courses failed")
	}
	for _, course := range courses {
		canViewReport, viewErr := s.CanViewReport(course, user)
		if viewErr != nil {
			return nil, nil, viewErr
		}
		if !canViewReport {
			return nil, nil, e.NewError403(e.Course_view_report_need_permission_or_owner, "Need permission to view report")
		}
	}

	details, pagination, err := models.Repository.Course(s.ctx).GetCourseRevenueDetail(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_revenue_detail_failed, "Get course revenue detail failed: "+err.Error())
	}
	return details, pagination, nil
}

func (s *CourseService) GetCourseRevenueGraph(query *dto.CourseRevenueGraphReq, user *models.User, opt *models.FindPageOptions) ([]*models.CourseRevenuePoint, *models.Pagination, *e.AppError) {
	roles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		return nil, nil, e.NewError400(e.Find_user_role_failed, "find user role failed")
	}
	user.Roles = roles

	courses, err := models.Repository.Course(s.ctx).FindMany(&models.CourseQuery{CuidIn: query.CuidIn}, nil)
	if err != nil {
		return nil, nil, e.NewError400(e.Course_revenue_summary_failed, "Find many courses failed")
	}
	for _, course := range courses {
		canViewReport, viewErr := s.CanViewReport(course, user)
		if viewErr != nil {
			return nil, nil, viewErr
		}
		if !canViewReport {
			return nil, nil, e.NewError403(e.Course_view_report_need_permission_or_owner, "Need permission to view report")
		}
	}
	graph, pagination, err := models.Repository.CourseRevenue(s.ctx).FindPage(&models.CourseRevenuePointQuery{CourseCuids: query.CuidIn, StartDate: query.StartDate, EndDate: query.EndDate}, opt)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_revenue_graph_failed, "Get course revenue graph failed: "+err.Error())
	}
	return graph, pagination, nil
}

func (s *CourseService) CronJobCreateCourseRevenue(timestamp int64, period models.TimePeriod) *e.AppError {
	err := models.Repository.CourseRevenue(s.ctx).AddCourseRevenuePoint(timestamp, period)
	if err != nil {
		return e.NewError500(e.Course_revenue_create_failed, "Create course revenue failed: "+err.Error())
	}
	return nil
}
