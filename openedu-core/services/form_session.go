package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"

	"gorm.io/gorm"
)

func (s *FormSessionService) FindPage(query *models.FormSessionQuery, options *models.FindPageOptions) ([]*models.FormSession, *models.Pagination, *e.AppError) {
	formSessions, pagination, err := models.Repository.FormSession.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_find_page_failed, err.Error())
	}
	return formSessions, pagination, nil
}

func (s *FormSessionService) FindMany(query *models.FormSessionQuery, options *models.FindManyOptions) ([]*models.FormSession, *e.AppError) {
	forms, err := models.Repository.FormSession.FindMany(query, options)
	if err != nil {
		return nil, e.NewError500(e.Form_find_failed, err.Error())
	}
	return forms, nil
}

func (s *FormSessionService) FindByID(id string, options *models.FindOneOptions) (*models.FormSession, *e.AppError) {
	if form, err := models.Repository.FormSession.FindByID(id, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Form_session_not_found, "Form session not found")
		}
		return nil, e.NewError500(e.Form_session_find_one_failed, "Find form session by id error: "+err.Error())
	} else {
		return form, nil
	}
}

func (s *FormSessionService) FindOne(query *models.FormSessionQuery, options *models.FindOneOptions) (*models.FormSession, *e.AppError) {
	if form, err := models.Repository.FormSession.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Form_session_not_found, "form session not found")
		}
		return nil, e.NewError500(e.Form_session_find_one_failed, "find one form session failed")
	} else {
		return form, nil
	}
}

func (s *FormSessionService) Update(session *models.FormSession) *e.AppError {
	if err := models.Repository.FormSession.Update(session, nil); err != nil {
		return e.NewError500(e.Form_session_update_failed, err.Error())
	}
	return nil
}

func (s *FormSessionService) UpdateStatus(user *models.User, registerUser *models.User, org *models.Organization, session *models.FormSession, status models.FormSessionStatus, reason string) *e.AppError {
	session.Status = status
	session.Note = reason
	if registerUser != nil {
		session.UserID = &registerUser.ID
	}

	if uErr := s.Update(session); uErr != nil {
		return uErr
	}

	form, fErr := models.Repository.Form.FindByID(session.FormID)
	if fErr != nil {
		return e.NewError500(e.Form_find_one_failed, "Find form by ID failed: "+fErr.Error())
	}

	var eventSendMail models.EventType
	switch form.Event {
	case models.FormEventRegisterCreator:
		{
			if !user.CanApproveCreatorForm(org.ID, form.OrgID) {
				return e.NewError403(e.Permission_reject_form_failed, "User can't approve register creator form")
			}
		}
		eventSendMail = models.EventRejectRegisterCreator
	case models.FormEventRegisterWriter:
		{
			if !user.CanApproveWriterForm(org.ID, form.OrgID) {
				return e.NewError403(e.Permission_reject_form_failed, "User can't approve register creator form")
			}
		}
		eventSendMail = models.EventRejectRegisterWriter

	case models.FormEventRegisterOrg:
		{
			if !user.CanApproveRegisterOrgForm() {
				return e.NewError403(e.Permission_reject_form_failed, "User can't approve register organization form")
			}
			eventSendMail = models.EventRejectRegisterOrg
		}
	}

	if status == models.FormSessionsStatusRejected {
		return handleSendMailReject(eventSendMail, org, form, session)
	}

	return nil
}

func handleSendMailReject(event models.EventType, org *models.Organization, form *models.Form, session *models.FormSession) *e.AppError {
	questionByID := map[string]*models.FormQuestion{}
	for _, question := range form.Questions {
		questionByID[question.ID] = question
	}

	emailParams := commdto.MapEmailParams{
		commdto.EmailParamRejectionReason: session.Note,
	}

	for _, answer := range session.Answers {
		question, found := questionByID[answer.QuestionID]
		if !found {
			continue
		}
		switch {
		case question.IsKeyFullName():
			emailParams[commdto.EmailParamFullName] = answer.Text
		case question.IsKeyEmail():
			emailParams[commdto.EmailParamEmail] = answer.Text
		case question.IsKeyCompanyName():
			emailParams[commdto.EmailParamOrgName] = answer.Text
		}
	}

	var user *models.User
	if session.UserID == nil {
		user = &models.User{Email: emailParams[commdto.EmailParamEmail].(string)}
	} else {
		usr, uErr := models.Repository.User.FindByID(*session.UserID)
		if uErr != nil {
			return e.NewError500(e.Error_user_find_failed, "Find user by ID failed: "+uErr.Error())
		}
		user = usr
		emailParams[commdto.EmailParamFullName] = usr.DisplayName
		emailParams[commdto.EmailParamEmail] = usr.Email
	}

	go func() {
		if _, err := communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       event.IntoComm(),
			ExtendDatas: emailParams,
			IsQueue:     true,
		}); err != nil {
			log.ErrorWithAlertf("FormSessionService.handleSendMailReject::Send email failed: %v", err)
		}
	}()
	return nil
}

// func (s *FormSessionService) HandleApproveFormSessionCreator(form *models.FormSession, data *dto.ApproveFormSessionCreator) *e.AppError {
// 	switch models.FormSessionStatus(data.Status) {
// 	case models.FormSessionsStatusApproved:
// 		approveCreatorForm(form)
// 	case models.FormSessionsStatusRejected:
// 		rejectCreatorForm(form, &data.Reason)
// 	default:
// 		return e.NewError400(e.Form_invalid_status, "Just handle status approved and rejected")
// 	}
// 	return nil
// }
//
// func approveCreatorForm(form *models.FormSession) *e.AppError {
// 	form.Status = models.FormSessionsStatusApproved
// 	if err := models.Repository.FormSession.Update(form, nil); err != nil {
// 		return e.NewError500(e.Form_update_failed, err.Error())
// 	}
// 	return nil
// }
//
// func rejectCreatorForm(form *models.FormSession, reason *string) *e.AppError {
// 	form.Status = models.FormSessionsStatusRejected
// 	form.Note = *reason
// 	if err := models.Repository.FormSession.Update(form, nil); err != nil {
// 		return e.NewError500(e.Form_update_failed, err.Error())
// 	}
// 	//TODO send rejected email
// 	return nil
// }
