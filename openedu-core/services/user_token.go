package services

import (
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"sync"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *UserTokenService) FindPage(query *models.UserTokenQuery, options *models.FindPageOptions) ([]*models.UserToken, *models.Pagination, *e.AppError) {
	if tokens, pagination, err := models.Repository.UserToken.FindPage(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, e.NewError400(e.NOTFOUND, err.Error())
		}
		return nil, nil, e.NewError500(e.Error_find_page_user_token_failed, err.Error())
	} else {
		return tokens, pagination, nil
	}
}

func (s *UserTokenService) FindOne(query *models.UserTokenQuery, options *models.FindOneOptions) (*models.UserToken, *e.AppError) {
	if token, err := models.Repository.UserToken.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Error_find_page_user_token_failed, err.Error())
		}
		return nil, e.NewError500(e.Error_user_token_find_failed, err.Error())
	} else {
		return token, nil
	}
}
func (s *UserTokenService) FindMany(query *models.UserTokenQuery, options *models.FindManyOptions) ([]*models.UserToken, *e.AppError) {
	if tokens, err := models.Repository.UserToken.FindMany(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.NOTFOUND, err.Error())
		}
		return nil, e.NewError500(e.Error_find_page_user_token_failed, err.Error())
	} else {
		return tokens, nil
	}
}

func (s *UserTokenService) ResendMail(data *dto.ResendMailInvitationRequest, org *models.Organization) *e.AppError {

	uts, err := s.FindMany(&models.UserTokenQuery{IDIn: &data.UserTokenIDs}, &models.FindManyOptions{})
	if err != nil {
		return err
	}

	filterdValidatedUts := lo.Filter(uts, func(ut *models.UserToken, _ int) bool {
		return !ut.IsVerified()
	})

	if err = handleResendInviteUser(filterdValidatedUts, org); err != nil {
		return err
	}

	return nil
}

func handleResendInviteCreator(uts []*models.UserToken, org *models.Organization) *e.AppError {
	lo.ForEach(uts, func(ut *models.UserToken, _ int) {
		ut.Token = util.GenerateToken()
		ut.ExpiryTime = util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn)
		ut.SendEmail = util.GetCurrentTime()
	})

	listEmails := lo.Map(uts, func(ut *models.UserToken, _ int) string { return ut.Email })

	users, err := models.Repository.User.FindMany(&models.UserQuery{EmailIn: listEmails}, nil)
	if err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	usersByEmails := map[string]*models.User{}
	for _, user := range users {
		usersByEmails[user.Email] = user
	}

	for _, ut := range uts {
		if err := models.Repository.UserToken.Update(ut, nil); err != nil {
			return e.NewError500(e.Error_update_user_token_failed, err.Error())
		}
	}

	var wg sync.WaitGroup
	var mailErr *e.AppError
	for _, ut := range uts {
		wg.Add(1)
		go func(userToken *models.UserToken, usersByEmails map[string]*models.User) {
			defer func() {
				if r := recover(); r != nil {
					log.Error("Recovered in handleResendInviteCreator", r)
				}
				wg.Done()
			}()
			user := usersByEmails[ut.Email]
			if user == nil {
				user = &models.User{DisplayName: "Creator", Email: ut.Email}
			}

			go func() {
				var eErr error
				defer func() {
					if r := recover(); r != nil {
						eErr = fmt.Errorf("panic: %v", r)
					}

					if eErr != nil {
						log.ErrorWithAlertf("UserTokenService.handleResendInviteCreator::Send email failed: %v", eErr)
					}
				}()

				mailParams := commdto.MapEmailParams{
					commdto.EmailParamUserToken: userToken,
				}
				_, eErr = communication.Email.SendEmail(&commdto.SendEmailRequest{
					User:        user.IntoComm(),
					Org:         org.IntoComm(),
					Event:       commdto.EventInviteCreatorBeforeAccept,
					ExtendDatas: mailParams,
					IsQueue:     true,
				})
			}()
		}(ut, usersByEmails)
	}

	wg.Wait()

	return mailErr
}

func handleResendInviteUser(uts []*models.UserToken, org *models.Organization) *e.AppError {
	lo.ForEach(uts, func(ut *models.UserToken, _ int) {
		ut.Token = util.GenerateToken()
		ut.ExpiryTime = util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn)
		ut.SendEmail = util.GetCurrentTime()
	})

	listEmails := lo.Map(uts, func(ut *models.UserToken, _ int) string { return ut.Email })

	users, err := models.Repository.User.FindMany(&models.UserQuery{EmailIn: listEmails}, nil)
	if err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	usersByEmails := map[string]*models.User{}
	for _, user := range users {
		usersByEmails[user.Email] = user
	}

	for _, ut := range uts {
		if err := models.Repository.UserToken.Update(ut, nil); err != nil {
			return e.NewError500(e.Error_update_user_token_failed, err.Error())
		}
	}

	var wg sync.WaitGroup
	var mailErr *e.AppError
	for _, ut := range uts {
		wg.Add(1)
		go func(userToken *models.UserToken, usersByEmails map[string]*models.User) {
			defer func() {
				if r := recover(); r != nil {
					log.Error("Recovered in handleResendInviteCreator", r)
				}
				wg.Done()
			}()
			user := usersByEmails[ut.Email]
			if user == nil {
				user = &models.User{DisplayName: "Creator", Email: ut.Email}
			}

			go func() {
				var emailEvent models.EventType
				switch ut.Event {
				case models.EventInviteReferrer:
					emailEvent = models.EventInviteReferrer
				case models.EventInviteOrgWriter, models.EventInviteOrgEditor:
					emailEvent = models.EventInviteUserBeforeAccept
				default:
					emailEvent = models.EventInviteUserBeforeAccept
				}

				var eErr error
				defer func() {
					if r := recover(); r != nil {
						eErr = fmt.Errorf("panic: %v", r)
					}

					if eErr != nil {
						log.ErrorWithAlertf("UserTokenService.handleResendInviteCreator::Send email failed: %v", eErr)
					}
				}()

				mailParams := commdto.MapEmailParams{
					commdto.EmailParamUserToken: userToken,
				}
				_, eErr = communication.Email.SendEmail(&commdto.SendEmailRequest{
					User:        user.IntoComm(),
					Org:         org.IntoComm(),
					Event:       emailEvent.IntoComm(),
					ExtendDatas: mailParams,
					IsQueue:     true,
				})
			}()
		}(ut, usersByEmails)
	}

	wg.Wait()

	return mailErr
}

func (s *UserTokenService) Upsert(userToken *models.UserToken, trans *gorm.DB) error {
	userTokenQuery := models.UserTokenQuery{
		UserID: userToken.UserID,
		Event:  util.NewT(models.EventSendOTP),
	}

	userTokenFind, err := models.Repository.UserToken.FindOne(&userTokenQuery, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			createErr := models.Repository.UserToken.Create(userToken, trans)
			if createErr != nil {
				return createErr
			}

			return nil
		}

		return err
	}

	userTokenFind.OTP = userToken.OTP
	userTokenFind.OTPExpireAt = userToken.OTPExpireAt
	userTokenFind.SendEmail = userToken.SendEmail
	userTokenFind.ExpiryTime = userToken.ExpiryTime
	if err := models.Repository.UserToken.Update(userTokenFind, trans); err != nil {
		return err
	}

	return nil
}
