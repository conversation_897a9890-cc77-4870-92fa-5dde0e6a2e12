package services

import (
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/ai"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"sync"

	"github.com/samber/lo"
)

func (s *AIBlogRewriteService) FindOne(query *models.AIBlogRewriteQuery, options *models.FindOneOptions) (*models.AIBlogRewrite, *e.AppError) {
	if blog, err := models.Repository.AIBlogRewrite.FindOne(query, options); err != nil {
		return nil, e.NewError500(e.AIBlogRewrite_find_one_failed, err.Error())
	} else {
		return blog, nil
	}
}

func (s *AIBlogRewriteService) FindPage(query *models.AIBlogRewriteQuery, options *models.FindPageOptions) ([]*models.AIBlogRewrite, *models.Pagination, *e.AppError) {
	if blogs, pagination, err := models.Repository.AIBlogRewrite.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.AIBlogRewrite_find_page_failed, err.Error())
	} else {
		return blogs, pagination, nil
	}
}

func (s *AIBlogRewriteService) Create(data *models.AIBlogRewrite) (*models.AIBlogRewrite, *e.AppError) {
	if err := models.Repository.AIBlogRewrite.Create(data, nil); err != nil {
		return nil, e.NewError500(e.AIBlogRewrite_create_failed, err.Error())
	}
	return data, nil
}

func (s *AIBlogRewriteService) Update(data *models.AIBlogRewrite) (*models.AIBlogRewrite, *e.AppError) {
	if err := models.Repository.AIBlogRewrite.Update(data, nil); err != nil {
		return nil, e.NewError500(e.AIBlogRewrite_update_failed, err.Error())
	}
	return data, nil
}

func (s *AIBlogRewriteService) FindMany(query *models.AIBlogRewriteQuery, options *models.FindManyOptions) ([]*models.AIBlogRewrite, *e.AppError) {
	if blogs, err := models.Repository.AIBlogRewrite.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.AIBlogRewrite_find_many_failed, err.Error())
	} else {
		return blogs, nil
	}
}

func (s *AIBlogRewriteService) OfferHandleCreateBlogByAI(data []dto.CreateBlogByAI, org *models.Organization, currentUser *models.User, locale string, tone models.AITone) *e.AppError {
	blogs := []*models.Blog{}
	aiBlogs := []*models.AIBlogRewrite{}
	aiHistories := []*models.AIHistory{}
	for _, toCreateBlog := range data {
		blogID := util.GenerateId()
		aiBlogID := util.GenerateId()
		blogCuid := util.GenerateId()
		blog := &models.Blog{
			Model:         models.Model{ID: blogID},
			OrgID:         org.ID,
			Status:        models.BlogStatusPending,
			BlogType:      toCreateBlog.BlogType,
			AuthorID:      currentUser.ID,
			Cuid:          blogCuid,
			IsAIGenerated: true,
			IsOriginDraft: true,
			Version:       1,
			Props:         models.BlogProps{PreviousVersion: 0},
			Locale:        locale,
		}
		aiBlog := &models.AIBlogRewrite{
			Model:    models.Model{ID: aiBlogID},
			Link:     toCreateBlog.Link,
			BlogType: toCreateBlog.BlogType,
			Status:   models.AIStatusPending,
			OrgID:    org.ID,
			Schema:   org.Schema,
			AuthorID: currentUser.ID,
			BlogID:   blogID,
			BlogCuid: blogCuid,
			Tone:     tone,
			Language: models.Repository.AIHistory.GetLanguageFromKey(locale),
		}
		aiHistory := &models.AIHistory{
			RequestIDs:   []string{blogCuid},
			RequestType:  models.BlogModelName,
			UserID:       currentUser.ID,
			EntityID:     blogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlogID,
			GenerateType: models.AIBlogGenerateBlog,
			Status:       aiBlog.Status,
			Cost:         0,
			Request:      map[string]interface{}{},
			Response:     map[string]interface{}{},
			StartDate:    int64(util.GetCurrentTime()),
			EndDate:      0,
			Error:        nil,
			OrgID:        org.ID,
			OrgSchema:    org.Schema,
			Step:         models.AIBlogGenerateBlogStep,
		}
		offerRequest := &ai.OfferBlogFromLinkRequest{
			Link:     toCreateBlog.Link,
			Language: models.Repository.AIHistory.GetLanguageFromKey(locale),
			XAPIKey:  setting.ExternalServiceSetting.XAPIKey,
			Tone:     AIHistory.GetAiToneMapByModelTone(tone),
		}
		request, err := util.StructToMap(offerRequest)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		offerData, err := ai.Blog.OfferGenerateBlogFromLink(offerRequest)
		if err != nil || offerData == nil {
			return e.NewError500(e.AIBlogRewrite_offer_generate_failed, err.Error())
		}
		aiBlog.OfferID = offerData.Data.OfferID
		aiHistory.AIOfferID = offerData.Data.OfferID

		aiHistory.Request = request
		aiBlog.CurrentStep = models.AIBlogGenerateBlogStep

		blogs = append(blogs, blog)
		aiBlogs = append(aiBlogs, aiBlog)
		aiHistories = append(aiHistories, aiHistory)
	}

	if err := models.Repository.AIBlogRewrite.CreateMany(aiBlogs, nil); err != nil {
		return e.NewError500(e.AIBlogRewrite_create_failed, err.Error())
	}

	if err := models.Repository.Blog.CreateMany(blogs, nil); err != nil {
		return e.NewError500(e.Create_blog_failed, err.Error())
	}

	if _, aErr := AIHistory.CreateMany(aiHistories); aErr != nil {
		return aErr
	}

	return nil
}

func (s *AIBlogRewriteService) handleGenerateBlogByAIFailed(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError {
	aiBlog.Status = models.AIStatusFailed
	if _, appErr := s.Update(aiBlog); appErr != nil {
		return appErr
	}

	blog.Status = models.BlogStatusGenerateFailed
	if err := models.Repository.Blog.Update(blog, nil); err != nil {
		return e.NewError500(e.Update_blog_failed, err.Error())
	}

	// Push notification to blog author
	org, err := models.Repository.Organization.FindByID(aiBlog.OrgID, nil)
	if err == nil {
		if err := communication.Notification.PushNotification(&communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateFailed,
			EntityID:   blog.ID, // Jump to blog
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
			Props: s.makeNotificationPropsForBlog(blog, org),
		}); err != nil {
			log.Errorf("Push notification after generating AI blog failed error: %v", err)
		}
	} else {
		log.Errorf("Get organization to push notifications error: %v", err)
	}

	// Push websocket to update blog status
	userRoles, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		RoleIDIn: []string{models.OrgWriterRoleType, models.OrgEditorRoleType},
		OrgID:    util.NewString(aiBlog.OrgID),
	}, nil)
	if err == nil {
		userIDs := lo.Map(userRoles, func(item *models.UserRoleOrg, _ int) string {
			return item.UserID
		})
		userIDs = append(userIDs, aiBlog.AuthorID)
		userIDs = lo.Uniq(userIDs)
		if err := communication.Websocket.SendMsgToUserWebSocket(&communicationdto.WebsocketMessageRequest{
			Event: communicationdto.WebsocketEventAIBlogStatus,
			Data: map[string]any{
				"blog_id":    blog.ID,
				"status":     models.BlogStatusGenerateFailed,
				"error_code": aiBlog.Error.Code,
				"update_at":  blog.UpdateAt,
			},
			Broadcast: communicationdto.WebsocketBroadcastParams{
				UserIDs: userIDs,
			},
		}); err != nil {
			log.Errorf("Push websocket to blog author for sync status error: %v", err)
		}
	} else {
		log.Errorf("Get list users to sync blog status error: %v", err)
	}

	return nil
}

func (s *AIBlogRewriteService) CronJobHandleGenerateBlog() *e.AppError {
	query := &models.AIBlogRewriteQuery{
		Status:      util.NewT(models.AIStatusPending),
		LinkNotNull: util.NewT(true),
	}

	options := &models.FindManyOptions{
		Sort:  []string{"create_at asc"},
		Limit: util.NewT(20),
	}

	aiBlogs, err := s.FindMany(query, options)
	if err != nil {
		return err
	}

	var wg sync.WaitGroup
	lo.ForEach(aiBlogs, func(aiBlog *models.AIBlogRewrite, _ int) {
		wg.Add(1)
		go func(aiBlog *models.AIBlogRewrite) {

			defer wg.Done()
			blog, aErr := Blog.FindByID(aiBlog.BlogID, false, &models.FindOneOptions{})
			if aErr != nil {
				fmt.Errorf(e.GetMsg(e.Blog_find_one_failed))
			}
			if aiBlog != nil && blog != nil {
				aErr = s.processAIBlog(aiBlog, blog)
			}
			if aErr != nil {
				fmt.Errorf(e.GetMsg(e.AIBlogRewrite_get_offer_data_failed), aErr.Msg)
			}
		}(aiBlog)
	})

	wg.Wait()

	aiBlogRewriteHistories, aErr := AIHistory.FindMany(&models.AIHistoryQuery{
		RequestType:     util.NewT(models.BlogModelName),
		RequestIDsEmpty: true,
		Status:          util.NewT(models.AIStatusPending),
		StepIn:          []*models.AIGenerateStep{util.NewT(models.AIBlogRewriteFromLinkStep), util.NewT(models.AIBlogRewriteParagraphStep)},
	}, options)
	if aErr != nil {
		fmt.Errorf(e.GetMsg(e.AIHistory_find_many_failed))
	}

	if len(aiBlogRewriteHistories) > 0 {
		lo.ForEach(aiBlogRewriteHistories, func(rewriteBlogHistory *models.AIHistory, _ int) {
			wg.Add(1)
			go func(rewriteBlogHistory *models.AIHistory) {
				defer wg.Done()

				if aErr := s.processRewriteBlogViaHistory(rewriteBlogHistory); aErr != nil {
					fmt.Errorf(e.GetMsg(e.AIBlogRewrite_get_offer_data_failed), aErr.Msg)
				}

			}(rewriteBlogHistory)
		})

		wg.Wait()
	}

	return nil
}

func (s *AIBlogRewriteService) processAIBlog(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError {
	if aiBlog.CurrentStep == models.AIBlogGenerateBlogStep {
		if aErr := s.handleGeneratingBlog(aiBlog, blog); aErr != nil {
			return s.handleGenerateBlogByAIFailed(aiBlog, blog)
		}

		if aErr := s.handleWriteBlog(aiBlog, blog); aErr != nil {
			return s.handleGenerateBlogByAIFailed(aiBlog, blog)
		}
	} else {
		if aErr := s.HandleRewriteBlog(aiBlog, blog); aErr != nil {
			return s.handleGenerateBlogByAIFailed(aiBlog, blog)
		}
	}

	return nil
}

func (s *AIBlogRewriteService) processRewriteBlogViaHistory(aiHistory *models.AIHistory) *e.AppError {
	models.AppSchema = aiHistory.OrgSchema
	var err error
	var httpResponse ai.AIResponse

	if aiHistory.Step == models.AIBlogRewriteFromLinkStep {
		httpResponse, err = ai.Blog.GetBlogGenerateFromLink(aiHistory.AIOfferID, setting.ExternalServiceSetting.XAPIKey)
	}

	if aiHistory.Step == models.AIBlogRewriteParagraphStep {
		httpResponse, err = ai.Blog.GetRewriteParagraphGenerate(aiHistory.AIOfferID, setting.ExternalServiceSetting.XAPIKey)
	}

	if err != nil {
		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{},
			RequestType:  models.BlogModelName,
			UserID:       aiHistory.UserID,
			EntityType:   models.BlogModelName,
			GenerateType: models.AIBlogRewriteFromLink,
			Step:         models.AIBlogRewriteFromLinkStep,
			Status:       models.AIStatusFailed,
			Cost:         0,
			Error: &models.DetailAIError{
				Code: e.AIBlogRewrite_get_offer_data_failed,
				Msg:  err.Error(),
			},
			EndDate:         int64(util.GetCurrentTime()),
			AIOfferID:       aiHistory.AIOfferID,
			RequestIDsEmpty: true,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogRewriteNotificationToUser(aiHistory, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateFailed,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{aiHistory.UserID},
				},
			},
		})

		s.sendAIBlogRewriteWebsocketMsgToUser(aiHistory, map[string]any{
			"status":     models.AIStatusFailed,
			"rewrite_id": aiHistory.ID,
			"error_code": aiHistory.Error.Code,
		})
		return nil
	}

	response := httpResponse.GetData()
	aiStatus := AIHistory.GetCourseStatusByAIStatus(response.GetStatus())

	if aiStatus == models.AIStatusFailed || response.GetMetadata() == nil {
		historyResp, err := util.StructToMap(httpResponse)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{},
			RequestType:  models.BlogModelName,
			UserID:       aiHistory.UserID,
			EntityType:   models.BlogModelName,
			GenerateType: models.AIBlogRewriteFromLink,
			Step:         models.AIBlogRewriteFromLinkStep,
			Status:       models.AIStatusFailed,
			Response:     historyResp,
			Cost:         0,
			Error: &models.DetailAIError{
				Code: e.AIBlogRewrite_get_offer_data_failed,
				Msg:  e.GetMsg(e.AIBlogRewrite_get_offer_data_failed),
			},
			EndDate:         int64(util.GetCurrentTime()),
			AIOfferID:       aiHistory.AIOfferID,
			RequestIDsEmpty: true,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogRewriteNotificationToUser(aiHistory, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateFailed,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{aiHistory.UserID},
				},
			},
		})

		s.sendAIBlogRewriteWebsocketMsgToUser(aiHistory, map[string]any{
			"status":     models.AIStatusFailed,
			"rewrite_id": aiHistory.ID,
			"error_code": aiHistory.Error.Code,
		})
		return nil
	}

	if aiStatus == models.AIStatusPending {
		aiHistory.Status = models.AIStatusPending
		if _, aErr := AIHistory.Update(aiHistory); aErr != nil {
			return aErr
		}
		return nil
	}

	if aiStatus == models.AIStatusCompleted {
		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:      []string{},
			RequestType:     models.BlogModelName,
			UserID:          aiHistory.UserID,
			EntityType:      models.BlogModelName,
			GenerateType:    models.AIBlogRewriteFromLink,
			Step:            models.AIBlogRewriteFromLinkStep,
			Status:          models.AIStatusCompleted,
			Cost:            response.GetMetadata().GetCost(),
			Response:        historyResp,
			Error:           nil,
			EndDate:         int64(util.GetCurrentTime()),
			AIOfferID:       aiHistory.AIOfferID,
			RequestIDsEmpty: true,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogRewriteNotificationToUser(aiHistory, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateSuccess,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{aiHistory.UserID},
				},
			},
		})

		s.sendAIBlogRewriteWebsocketMsgToUser(aiHistory, map[string]any{
			"status":     models.AIStatusCompleted,
			"rewrite_id": aiHistory.ID,
			"update_at":  aiHistory.UpdateAt,
		})
	}
	return nil
}

func (s *AIBlogRewriteService) HandleRewriteBlog(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError {
	aiBlog.Status = models.AIStatusGenerating
	if _, appErr := s.Update(aiBlog); appErr != nil {
		return appErr
	}

	switch aiBlog.CurrentStep {
	case models.AIBlogRewriteFromLinkStep:
		return s.handleRewriteBlogFromLink(aiBlog, blog)
	case models.AIBlogRewriteParagraphStep:
		return s.handleRewriteBlogFromParagraph(aiBlog, blog)
	}
	return nil
}

func (s *AIBlogRewriteService) handleGeneratingBlog(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError {
	aiBlog.Status = models.AIStatusGenerating
	blog.Status = models.BlogStatusGenerating

	if _, appErr := s.Update(aiBlog); appErr != nil {
		return appErr
	}

	if err := models.Repository.Blog.Update(blog, nil); err != nil {
		return e.NewError500(e.Update_blog_failed, err.Error())
	}

	return nil
}

func (s *AIBlogRewriteService) handleWriteBlog(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError {
	models.AppSchema = aiBlog.Schema
	httpResponse, err := ai.Blog.GetBlogGenerateFromLink(aiBlog.OfferID, setting.ExternalServiceSetting.XAPIKey)
	if err != nil {
		aiBlog.Error = &models.DetailAIError{
			Code: e.AIBlogRewrite_get_offer_data_failed,
			Msg:  err.Error(),
		}
		if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogRewriteParagraph,
			Step:         models.AIBlogRewriteFromLinkStep,
			Status:       models.AIStatusFailed,
			Cost:         0,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.OfferID,
		}); aErr != nil {
			return aErr
		}

		return s.handleGenerateBlogByAIFailed(aiBlog, blog)
	}

	response := httpResponse.Data
	aiStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

	if aiStatus == models.AIStatusFailed || response.Metadata == nil {
		aiBlog.Error = &models.DetailAIError{
			Code: e.AIBlogRewrite_get_offer_data_failed,
			Msg:  e.GetMsg(e.AIBlogRewrite_get_offer_data_failed),
		}
		historyResp, err := util.StructToMap(httpResponse)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		_, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogGenerateBlog,
			Step:         models.AIBlogGenerateBlogStep,
			Status:       models.AIStatusFailed,
			Response:     historyResp,
			Cost:         0,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.OfferID,
		})
		if aErr != nil {
			return aErr
		}
		return s.handleGenerateBlogByAIFailed(aiBlog, blog)
	}

	if aiStatus == models.AIStatusPending {
		aiBlog.Status = models.AIStatusPending
		if _, aErr := s.Update(aiBlog); aErr != nil {
			return aErr
		}
		return nil
	}

	if aiStatus == models.AIStatusCompleted {
		aiBlog.Title = response.Metadata.Title
		aiBlog.NewContent = response.Metadata.Content
		aiBlog.MetaData = response.Metadata.MetaData
		aiBlog.Thumbnail = response.Metadata.Thumbnail
		aiBlog.Cost = response.Metadata.Cost
		aiBlog.Status = models.AIStatusCompleted

		if _, appErr := s.Update(aiBlog); appErr != nil {
			return appErr
		}

		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}

		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogGenerateBlog,
			Step:         models.AIBlogGenerateBlogStep,
			Status:       models.AIStatusCompleted,
			Cost:         response.Metadata.Cost,
			Response:     historyResp,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.OfferID,
		})
		if aErr != nil {
			return aErr
		}

		queryBlog := models.BlogQuery{
			ID:            util.NewT(aiBlog.BlogID),
			IsAIGenerated: util.NewT(true),
		}
		blog, appErr := Blog.FindOne(&queryBlog, &models.FindOneOptions{})
		if appErr != nil {
			return appErr
		}

		slug, slugErr := util.Slugify(aiBlog.Title, 5)
		if slugErr != nil {
			return e.NewError500(e.Generate_random_failed, slugErr.Error())
		}

		// download thumbnail and upload
		thumbnailID := ""
		if response.Metadata.Thumbnail != "" {
			file, aErr := Upload.DownLoadAndUploadFile(response.Metadata.Thumbnail, aiBlog.AuthorID)
			if aErr != nil {
				log.Debugf("Get File From Url Failed:  %v", aErr)
			} else {
				thumbnailID = file.ID
			}
		}

		blog.Status = models.BlogStatusDraft
		blog.Title = aiBlog.Title
		blog.Content = aiBlog.NewContent
		blog.Slug = slug
		blog.BannerID = thumbnailID

		if err := models.Repository.Blog.Update(blog, nil); err != nil {
			return e.NewError500(e.Update_blog_failed, err.Error())
		}

		s.sendAIBlogNotificationToUser(aiBlog, blog, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateSuccess,
			EntityID:   blog.ID,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
		})

		s.sendAIBlogWebsocketMsgToUser(aiBlog, blog, map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusCompleted,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
			"rewrite_id": aiHistory.ID,
		})
	}
	return nil
}

func (s *AIBlogRewriteService) makeNotificationPropsForBlog(blog *models.Blog, org *models.Organization) communicationdto.JSONB {
	return communicationdto.JSONB{
		"org_id":     org.ID,
		"org_name":   org.Name,
		"org_domain": org.Domain,
		"blog_id":    blog.ID,
		"blog_cuid":  blog.Cuid,
		"blog_title": blog.Title,
		"blog_slug":  blog.Slug,
	}
}

func (s *AIBlogRewriteService) OfferRewriteBlogFromLink(link string, locale string, blogCuid string, org *models.Organization, user *models.User, tone models.AITone) (*dto.OfferRewriteBlogDataResponse, *e.AppError) {
	if blogCuid == "" {
		offerRequest := &ai.OfferBlogFromLinkRequest{
			Link:     link,
			Language: models.Repository.AIHistory.GetLanguageFromKey(locale),
			XAPIKey:  setting.ExternalServiceSetting.XAPIKey,
			Tone:     AIHistory.GetAiToneMapByModelTone(tone),
		}
		toCreateHistory := &dto.CreateAIHistoryParams{
			RequestIDs:   []string{},
			RequestType:  models.BlogModelName,
			UserID:       user.ID,
			EntityType:   models.BlogModelName,
			GenerateType: models.AIBlogRewriteFromLink,
			Cost:         0,
			Request:      map[string]interface{}{},
			Response:     map[string]interface{}{},
			Error:        nil,
			StartDate:    int64(util.GetCurrentTime()),
			EndDate:      0,
			OrgID:        org.ID,
			OrgSchema:    org.Schema,
			Step:         models.AIBlogRewriteFromLinkStep,
		}
		offerData, err := ai.Blog.OfferGenerateBlogFromLink(offerRequest)
		if err != nil || offerData == nil {
			return nil, e.NewError500(e.AIBlogRewrite_offer_rewrite_from_link_failed, err.Error())
		}
		toCreateHistory.AIOfferID = offerData.Data.OfferID
		toCreateHistory.Status = models.AIStatusPending
		request, err := util.StructToMap(offerRequest)
		if err != nil {
			return nil, e.NewError500(e.Json_encode_error, err.Error())
		}
		toCreateHistory.Request = request

		toCreateHistory.Step = models.AIBlogRewriteFromLinkStep
		aiHistoryID := util.GenerateId()
		toCreateHistory.ID = aiHistoryID

		if _, aErr := AIHistory.FindAndCreateHistory(toCreateHistory); aErr != nil {
			return nil, aErr
		}

		return &dto.OfferRewriteBlogDataResponse{
			RewriteID:   aiHistoryID,
			Status:      toCreateHistory.Status,
			CurrentStep: toCreateHistory.Step,
			AuthorID:    toCreateHistory.UserID,
		}, nil
	}

	aiBlog, aErr := s.FindOne(&models.AIBlogRewriteQuery{
		BlogCuid: &blogCuid,
	}, &models.FindOneOptions{})
	if aErr != nil {
		return nil, aErr
	}

	offerRequest := &ai.OfferBlogFromLinkRequest{
		Link:     link,
		Language: models.Repository.AIHistory.GetLanguageFromKey(locale),
		XAPIKey:  setting.ExternalServiceSetting.XAPIKey,
		Tone:     AIHistory.GetAiToneMapByModelTone(tone),
	}
	offerData, err := ai.Blog.OfferGenerateBlogFromLink(offerRequest)
	if err != nil || offerData == nil {
		return nil, e.NewError500(e.AIBlogRewrite_offer_rewrite_from_link_failed, err.Error())
	}
	aiBlog.Status = models.AIStatusPending
	aiBlog.RewriteOfferID = offerData.Data.OfferID

	aiBlog.CurrentStep = models.AIBlogRewriteFromLinkStep
	aiBlog.Language = models.Repository.AIHistory.GetLanguageFromKey(locale)
	aiBlog.Tone = tone

	if _, aErr := s.Update(aiBlog); err != nil {
		return nil, aErr
	}

	request, err := util.StructToMap(offerRequest)
	if err != nil {
		return nil, e.NewError500(e.Json_encode_error, err.Error())
	}
	aiHistoryID := util.GenerateId()
	if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
		ID:           aiHistoryID,
		RequestIDs:   []string{aiBlog.BlogCuid},
		RequestType:  models.BlogModelName,
		UserID:       user.ID,
		EntityID:     aiBlog.BlogCuid,
		EntityType:   models.BlogModelName,
		GenerateID:   aiBlog.ID,
		GenerateType: models.AIBlogRewriteFromLink,
		Status:       aiBlog.Status,
		Cost:         0,
		Request:      request,
		Response:     map[string]interface{}{},
		Error:        nil,
		StartDate:    int64(util.GetCurrentTime()),
		EndDate:      0,
		OrgID:        org.ID,
		OrgSchema:    org.Schema,
		Step:         aiBlog.CurrentStep,
		AIOfferID:    aiBlog.RewriteOfferID,
	}); aErr != nil {
		return nil, aErr
	}

	return &dto.OfferRewriteBlogDataResponse{
		BlogID:      aiBlog.BlogID,
		BlogCuid:    blogCuid,
		AIBlogID:    aiBlog.ID,
		RewriteID:   aiHistoryID,
		Status:      aiBlog.Status,
		CurrentStep: aiBlog.CurrentStep,
		AuthorID:    aiBlog.AuthorID,
	}, nil
}

func (s *AIBlogRewriteService) handleRewriteBlogFromLink(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError {
	models.AppSchema = aiBlog.Schema
	httpResponse, err := ai.Blog.GetBlogGenerateFromLink(aiBlog.RewriteOfferID, setting.ExternalServiceSetting.XAPIKey)
	if err != nil {
		aiBlog.Error = &models.DetailAIError{
			Code: e.AIBlogRewrite_get_offer_data_failed,
			Msg:  err.Error(),
		}
		aiBlog.Status = models.AIStatusFailed
		if _, aErr := s.Update(aiBlog); aErr != nil {
			return aErr
		}

		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogRewriteFromLink,
			Step:         models.AIBlogRewriteFromLinkStep,
			Status:       models.AIStatusFailed,
			Cost:         0,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.RewriteOfferID,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogNotificationToUser(aiBlog, blog, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateFailed,
			EntityID:   blog.ID,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
		})

		s.sendAIBlogWebsocketMsgToUser(aiBlog, blog, map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusFailed,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
			"rewrite_id": aiHistory.ID,
			"error_code": aiBlog.Error.Code,
		})
		return nil
	}

	response := httpResponse.Data
	aiStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

	if aiStatus == models.AIStatusFailed || response.Metadata == nil {
		aiBlog.Error = &models.DetailAIError{
			Code: e.AIBlogRewrite_get_offer_data_failed,
			Msg:  e.GetMsg(e.AIBlogRewrite_get_offer_data_failed),
		}
		aiBlog.Status = models.AIStatusFailed
		if _, aErr := s.Update(aiBlog); aErr != nil {
			return aErr
		}
		historyResp, err := util.StructToMap(httpResponse)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogRewriteFromLink,
			Step:         models.AIBlogRewriteFromLinkStep,
			Status:       models.AIStatusFailed,
			Response:     historyResp,
			Cost:         0,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.RewriteOfferID,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogNotificationToUser(aiBlog, blog, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateFailed,
			EntityID:   blog.ID,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
		})

		s.sendAIBlogWebsocketMsgToUser(aiBlog, blog, map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusFailed,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
			"rewrite_id": aiHistory.ID,
			"error_code": aiBlog.Error.Code,
		})
		return nil
	}

	if aiStatus == models.AIStatusPending {
		aiBlog.Status = models.AIStatusPending
		if _, aErr := s.Update(aiBlog); aErr != nil {
			return aErr
		}
		return nil
	}

	if aiStatus == models.AIStatusCompleted {
		aiBlog.Status = models.AIStatusCompleted
		aiBlog.Error = nil

		if _, appErr := s.Update(aiBlog); appErr != nil {
			return appErr
		}

		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogRewriteFromLink,
			Step:         models.AIBlogRewriteFromLinkStep,
			Status:       models.AIStatusCompleted,
			Cost:         response.Metadata.Cost,
			Response:     historyResp,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.RewriteOfferID,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogNotificationToUser(aiBlog, blog, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateSuccess,
			EntityID:   blog.ID,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
		})

		s.sendAIBlogWebsocketMsgToUser(aiBlog, blog, map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusCompleted,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
			"rewrite_id": aiHistory.ID,
		})
	}
	return nil
}

func (s *AIBlogRewriteService) OfferRewriteParagraph(text string, blogCuid string, org *models.Organization, user *models.User) (*dto.OfferRewriteBlogDataResponse, *e.AppError) {
	if blogCuid == "" {
		offerRequest := &ai.OfferBlogFromTextRequest{
			Text:    text,
			XAPIKey: setting.ExternalServiceSetting.XAPIKey,
		}
		toCreateHistory := &dto.CreateAIHistoryParams{
			RequestIDs:   []string{},
			RequestType:  models.BlogModelName,
			UserID:       user.ID,
			EntityType:   models.BlogModelName,
			GenerateType: models.AIBlogRewriteParagraph,
			Cost:         0,
			Request:      map[string]interface{}{},
			Response:     map[string]interface{}{},
			Error:        nil,
			StartDate:    int64(util.GetCurrentTime()),
			EndDate:      0,
			OrgID:        org.ID,
			OrgSchema:    org.Schema,
			Step:         models.AIBlogRewriteParagraphStep,
		}
		offerData, err := ai.Blog.OfferRewriteParagraph(offerRequest)
		if err != nil || offerData == nil {
			return nil, e.NewError500(e.AIBlogRewrite_offer_rewrite_paragraph_failed, err.Error())
		}
		toCreateHistory.Status = models.AIStatusPending
		toCreateHistory.AIOfferID = offerData.Data.OfferID
		request, err := util.StructToMap(offerRequest)
		if err != nil {
			return nil, e.NewError500(e.Json_encode_error, err.Error())
		}
		toCreateHistory.Request = request
		toCreateHistory.Step = models.AIBlogRewriteFromLinkStep
		aiHistoryID := util.GenerateId()
		toCreateHistory.ID = aiHistoryID

		if _, aErr := AIHistory.FindAndCreateHistory(toCreateHistory); aErr != nil {
			return nil, aErr
		}

		return &dto.OfferRewriteBlogDataResponse{
			RewriteID:   aiHistoryID,
			Status:      toCreateHistory.Status,
			CurrentStep: toCreateHistory.Step,
			AuthorID:    toCreateHistory.UserID,
		}, nil
	}
	aiBlog, aErr := s.FindOne(&models.AIBlogRewriteQuery{
		BlogCuid: &blogCuid,
	}, &models.FindOneOptions{})
	if aErr != nil {
		return nil, aErr
	}

	offerRequest := &ai.OfferBlogFromTextRequest{
		Text:    text,
		XAPIKey: setting.ExternalServiceSetting.XAPIKey,
	}
	offerData, err := ai.Blog.OfferRewriteParagraph(offerRequest)
	if err != nil || offerData == nil {
		return nil, e.NewError500(e.AIBlogRewrite_offer_rewrite_from_link_failed, err.Error())
	}
	aiBlog.Status = models.AIStatusPending
	aiBlog.RewriteOfferID = offerData.Data.OfferID

	aiBlog.CurrentStep = models.AIBlogRewriteFromLinkStep

	if _, err := s.Update(aiBlog); err != nil {
		return nil, err
	}

	request, err := util.StructToMap(offerRequest)
	if err != nil {
		return nil, e.NewError500(e.Json_encode_error, err.Error())
	}

	aiHistoryID := util.GenerateId()
	if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
		ID:           aiHistoryID,
		RequestIDs:   []string{aiBlog.BlogCuid},
		RequestType:  models.BlogModelName,
		UserID:       user.ID,
		EntityID:     aiBlog.BlogCuid,
		EntityType:   models.BlogModelName,
		GenerateID:   aiBlog.ID,
		GenerateType: models.AIBlogRewriteParagraph,
		Status:       aiBlog.Status,
		Cost:         0,
		Request:      request,
		Response:     map[string]interface{}{},
		Error:        nil,
		StartDate:    int64(util.GetCurrentTime()),
		EndDate:      0,
		OrgID:        org.ID,
		OrgSchema:    org.Schema,
		Step:         aiBlog.CurrentStep,
		AIOfferID:    aiBlog.RewriteOfferID,
	}); aErr != nil {
		return nil, aErr
	}

	return &dto.OfferRewriteBlogDataResponse{
		BlogID:      aiBlog.BlogID,
		BlogCuid:    blogCuid,
		AIBlogID:    aiBlog.ID,
		RewriteID:   aiHistoryID,
		Status:      aiBlog.Status,
		CurrentStep: aiBlog.CurrentStep,
		AuthorID:    aiBlog.AuthorID,
	}, nil
}

func (s *AIBlogRewriteService) handleRewriteBlogFromParagraph(aiBlog *models.AIBlogRewrite, blog *models.Blog) *e.AppError {
	models.AppSchema = aiBlog.Schema
	httpResponse, err := ai.Blog.GetRewriteParagraphGenerate(aiBlog.RewriteOfferID, setting.ExternalServiceSetting.XAPIKey)
	if err != nil {
		aiBlog.Error = &models.DetailAIError{
			Code: e.AIBlogRewrite_get_offer_data_failed,
			Msg:  err.Error(),
		}
		aiBlog.Status = models.AIStatusFailed
		if _, aErr := s.Update(aiBlog); aErr != nil {
			return aErr
		}
		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogRewriteParagraph,
			Step:         models.AIBlogRewriteParagraphStep,
			Status:       models.AIStatusFailed,
			Cost:         0,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.RewriteOfferID,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogNotificationToUser(aiBlog, blog, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateFailed,
			EntityID:   blog.ID,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
		})

		s.sendAIBlogWebsocketMsgToUser(aiBlog, blog, map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusFailed,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
			"rewrite_id": aiHistory.ID,
			"error_code": aiBlog.Error.Code,
		})
		return nil
	}

	response := httpResponse.Data
	aiStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

	if aiStatus == models.AIStatusFailed || response.Metadata == nil {
		aiBlog.Error = &models.DetailAIError{
			Code: e.AIBlogRewrite_get_offer_data_failed,
			Msg:  e.GetMsg(e.AIBlogRewrite_get_offer_data_failed),
		}
		aiBlog.Status = models.AIStatusFailed
		if _, aErr := s.Update(aiBlog); aErr != nil {
			return aErr
		}
		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogRewriteParagraph,
			Step:         models.AIBlogRewriteParagraphStep,
			Status:       models.AIStatusFailed,
			Cost:         0,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.RewriteOfferID,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogNotificationToUser(aiBlog, blog, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateFailed,
			EntityID:   blog.ID,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
		})

		s.sendAIBlogWebsocketMsgToUser(aiBlog, blog, map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusFailed,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
			"rewrite_id": aiHistory.ID,
			"error_code": aiBlog.Error.Code,
		})
		return nil
	}

	if aiStatus == models.AIStatusPending {
		aiBlog.Status = models.AIStatusPending
		if _, aErr := s.Update(aiBlog); aErr != nil {
			return aErr
		}
		return nil
	}

	if aiStatus == models.AIStatusCompleted {
		aiBlog.Status = models.AIStatusCompleted
		aiBlog.Error = nil

		if _, appErr := s.Update(aiBlog); appErr != nil {
			return appErr
		}

		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}

		aiHistory, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{aiBlog.BlogCuid},
			RequestType:  models.BlogModelName,
			UserID:       aiBlog.AuthorID,
			EntityID:     aiBlog.BlogCuid,
			EntityType:   models.BlogModelName,
			GenerateID:   aiBlog.ID,
			GenerateType: models.AIBlogRewriteParagraph,
			Step:         models.AIBlogRewriteParagraphStep,
			Status:       models.AIStatusCompleted,
			Cost:         response.Metadata.Cost,
			Response:     historyResp,
			Error:        aiBlog.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiBlog.RewriteOfferID,
		})
		if aErr != nil {
			return aErr
		}

		s.sendAIBlogNotificationToUser(aiBlog, blog, &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeBlogAIGenerateSuccess,
			EntityID:   blog.ID,
			EntityType: communicationdto.BlogEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{blog.AuthorID},
				},
			},
		})

		s.sendAIBlogWebsocketMsgToUser(aiBlog, blog, map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusCompleted,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
			"rewrite_id": aiHistory.ID,
		})
	}
	return nil
}

func (s *AIBlogRewriteService) GetRewriteBlogData(rewriteID string, user *models.User) (*dto.RewriteBlogByAIResponse, *e.AppError) {
	aiHistory, aErr := AIHistory.FindOne(&models.AIHistoryQuery{
		ID:             &rewriteID,
		RequestType:    util.NewT(models.BlogModelName),
		UserID:         &user.ID,
		IncludeDeleted: util.NewBool(false),
	}, &models.FindOneOptions{})
	if aErr != nil {
		return nil, aErr
	}

	blogID := ""
	if len(aiHistory.RequestIDs) > 0 {
		blogID = aiHistory.RequestIDs[0]
	}

	if aiHistory.Response != nil && len(aiHistory.Response) > 0 {
		var dataResp dto.RewriteBlogFromAIData
		if err := util.Json2Struct(aiHistory.Response, &dataResp); err != nil {
			return nil, e.NewError500(e.Json_decode_error, err.Error())
		}
		return &dto.RewriteBlogByAIResponse{
			Content:     dataResp.Metadata.Content,
			BlogCuid:    aiHistory.EntityID,
			AIBlogID:    aiHistory.GenerateID,
			RewriteID:   aiHistory.ID,
			Status:      aiHistory.Status,
			CurrentStep: aiHistory.Step,
			AuthorID:    aiHistory.UserID,
			BlogID:      blogID,
		}, nil
	}

	return &dto.RewriteBlogByAIResponse{
		Content:     "",
		BlogID:      blogID,
		BlogCuid:    aiHistory.EntityID,
		AIBlogID:    aiHistory.GenerateID,
		RewriteID:   aiHistory.ID,
		Status:      aiHistory.Status,
		CurrentStep: aiHistory.Step,
		AuthorID:    aiHistory.UserID,
	}, nil
}

func (s *AIBlogRewriteService) sendAIBlogNotificationToUser(aiBlog *models.AIBlogRewrite, blog *models.Blog, notifyRequest *communicationdto.PushNotificationRequest) {
	// Push notification to blog author
	org, err := models.Repository.Organization.FindByID(aiBlog.OrgID, nil)
	if err == nil {
		if notifyRequest.Props == nil {
			notifyRequest.Props = s.makeNotificationPropsForBlog(blog, org)
		}

		if err := communication.Notification.PushNotification(notifyRequest); err != nil {
			log.Errorf("Push notification after generating AI blog success error: %v", err)
		}
	} else {
		log.Errorf("Get organization to push notifications error: %v", err)
	}
}

func (s *AIBlogRewriteService) sendAIBlogWebsocketMsgToUser(aiBlog *models.AIBlogRewrite, blog *models.Blog, websocketMsg map[string]any) {
	// Push websocket to update blog status
	userRoles, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		RoleIDIn: []string{models.OrgWriterRoleType, models.OrgEditorRoleType},
		OrgID:    util.NewString(aiBlog.OrgID),
	}, nil)
	if err == nil {
		userIDs := lo.Map(userRoles, func(item *models.UserRoleOrg, _ int) string {
			return item.UserID
		})
		userIDs = append(userIDs, aiBlog.AuthorID)
		userIDs = lo.Uniq(userIDs)
		msgData := map[string]any{
			"blog_id":    blog.ID,
			"status":     models.AIStatusCompleted,
			"blog_slug":  blog.Slug,
			"blog_title": blog.Title,
			"update_at":  blog.UpdateAt,
		}
		if websocketMsg != nil {
			msgData = websocketMsg
		}
		if err := communication.Websocket.SendMsgToUserWebSocket(&communicationdto.WebsocketMessageRequest{
			Event: communicationdto.WebsocketEventAIBlogStatus,
			Data:  msgData,
			Broadcast: communicationdto.WebsocketBroadcastParams{
				UserIDs: userIDs,
			},
		}); err != nil {
			log.Errorf("Push websocket to blog author for sync status error: %v", err)
		}
	} else {
		log.Errorf("Get list users to sync blog status error: %v", err)
	}
}

func (s *AIBlogRewriteService) sendAIBlogRewriteNotificationToUser(history *models.AIHistory, notifyRequest *communicationdto.PushNotificationRequest) {
	// Push notification to blog author
	if err := communication.Notification.PushNotification(notifyRequest); err != nil {
		log.Errorf("Push notification after generating AI blog success error: %v", err)
	}
}

func (s *AIBlogRewriteService) sendAIBlogRewriteWebsocketMsgToUser(history *models.AIHistory, websocketMsg map[string]any) {
	// Push websocket to update blog status
	userRoles, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		RoleIDIn: []string{models.OrgWriterRoleType, models.OrgEditorRoleType},
		OrgID:    util.NewString(history.OrgID),
	}, nil)
	if err == nil {
		userIDs := lo.Map(userRoles, func(item *models.UserRoleOrg, _ int) string {
			return item.UserID
		})
		userIDs = append(userIDs, history.UserID)
		userIDs = lo.Uniq(userIDs)
		msgData := map[string]any{
			"status":     models.AIStatusCompleted,
			"rewrite_id": history.ID,
		}
		if websocketMsg != nil {
			msgData = websocketMsg
		}
		if err := communication.Websocket.SendMsgToUserWebSocket(&communicationdto.WebsocketMessageRequest{
			Event: communicationdto.WebsocketEventAIBlogStatus,
			Data:  msgData,
			Broadcast: communicationdto.WebsocketBroadcastParams{
				UserIDs: userIDs,
			},
		}); err != nil {
			log.Errorf("Push websocket to blog author for sync status error: %v", err)
		}
	} else {
		log.Errorf("Get list users to sync blog status error: %v", err)
	}
}
