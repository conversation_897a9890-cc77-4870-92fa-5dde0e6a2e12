package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"time"

	"gorm.io/gorm"
)

func (s *OEPointCampaignService) GetPointSystemConfig() *models.OEPointSystemConfig {
	config := models.GetConfig[models.OEPointSystemConfig](models.PointSystemSetting)
	return &config
}

func (s *OEPointCampaignService) ReferralUserCampaign(request *dto.RefUserConfigRequest, config *models.OEPointCampaign) (*models.OEPointCampaign, *e.AppError) {
	if config == nil {
		config = &models.OEPointCampaign{}
	}
	setting, _ := util.StructToMap(request.Setting)

	config.Program = models.RefUserProgram
	config.Name = request.Name
	config.Scope = request.Scope
	config.Enabled = request.Enabled
	config.Entities = request.Entities
	config.StartDate = request.StartDate
	config.EndDate = request.EndDate
	config.Setting = setting

	if config.ID != "" {
		if err := models.Repository.OEPointCampaign(s.ctx).Update(config, nil); err != nil {
			return nil, e.NewError500(e.OEPointCampaignUpdateFailed, "update: "+err.Error())
		} else {
			return config, nil
		}
	} else {
		if err := models.Repository.OEPointCampaign(s.ctx).Create(config, nil); err != nil {
			return nil, e.NewError500(e.OEPointCampaignCreateFailed, "create: "+err.Error())
		} else {
			return config, nil
		}
	}
}

func (s *OEPointCampaignService) GetActiveReferralUserCampaign() ([]*models.OEPointCampaign, *e.AppError) {
	now := time.Now().UnixMilli()
	query := models.OEPointCampaignQuery{
		Program:     util.NewT(models.RefUserProgram),
		Enable:      util.NewBool(true),
		StartDateLt: util.NewT(int(now)),
		EndDateGt:   util.NewT(int(now)),
	}
	campaigns, err := s.FindMany(&query, nil)
	if err != nil {
		return nil, err
	}

	return campaigns, nil
}

func (s *OEPointCampaignService) Update(campaign *models.OEPointCampaign) (*models.OEPointCampaign, *e.AppError) {
	if err := models.Repository.OEPointCampaign(s.ctx).Update(campaign, nil); err != nil {
		return nil, e.NewError500(e.OEPointCampaignUpdateFailed, "create: "+err.Error())
	} else {
		return campaign, nil
	}
}

func (s *OEPointCampaignService) FindOne(query *models.OEPointCampaignQuery, options *models.FindOneOptions) (*models.OEPointCampaign, *e.AppError) {
	if plan, err := models.Repository.OEPointCampaign(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.OEPointCampaignNotFound, err.Error())
		}
		return nil, e.NewError500(e.OEPointCampaignFindOneFailed, "FindOne: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEPointCampaignService) FindById(id string, options *models.FindOneOptions) (*models.OEPointCampaign, *e.AppError) {
	if plan, err := models.Repository.OEPointCampaign(s.ctx).FindOne(&models.OEPointCampaignQuery{ID: util.NewString(id)}, options); err != nil {
		return nil, e.NewError500(e.OEPointCampaignFindByIDFailed, "FindById: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEPointCampaignService) FindPage(query *models.OEPointCampaignQuery, options *models.FindPageOptions) ([]*models.OEPointCampaign, *models.Pagination, *e.AppError) {
	if plans, pagination, err := models.Repository.OEPointCampaign(s.ctx).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.OEPointCampaignFindPageFailed, "FindPage: "+err.Error())
	} else {
		return plans, pagination, nil
	}
}

func (s *OEPointCampaignService) FindMany(query *models.OEPointCampaignQuery, options *models.FindManyOptions) ([]*models.OEPointCampaign, *e.AppError) {
	if plans, err := models.Repository.OEPointCampaign(s.ctx).FindMany(query, options); err != nil {
		return nil, e.NewError500(e.OEPointCampaignFindManyFailed, "FindMany: "+err.Error())
	} else {
		return plans, nil
	}
}

func (s *OEPointCampaignService) Delete(id string) *e.AppError {
	if err := models.Repository.OEPointCampaign(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.OEPointCampaignDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}
