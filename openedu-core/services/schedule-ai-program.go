package services

import (
	"openedu-core/models"
	"openedu-core/pkg/e"
)

func (s *ScheduleService) Create(schedule *models.Schedule) (*models.Schedule, *e.AppError) {
	if err := models.Repository.Schedule(s.ctx).Create(schedule, nil); err != nil {
		return nil, e.NewError500(e.ScheduleCreateFailed, err.Error())
	}
	return schedule, nil
}

func (s *ScheduleService) FindById(id string, options *models.FindOneOptions) (*models.Schedule, *e.AppError) {
	if schedule, err := models.Repository.Schedule(s.ctx).FindByID(id, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.EventScheduleNotFound, err.Error())
		} else {
			return nil, e.NewError500(e.EventScheduleFindFailed, err.Error())
		}
	} else {
		return schedule, nil
	}

}

func (s *ScheduleService) Update(schedule *models.Schedule) (*models.Schedule, *e.AppError) {
	if err := models.Repository.Schedule(s.ctx).Update(schedule, nil); err != nil {
		return nil, e.NewError500(e.ScheduleUpdateFailed, err.Error())
	}
	return schedule, nil
}

func (s *ScheduleService) FindMany(query *models.ScheduleQuery, options *models.FindManyOptions) ([]*models.Schedule, *e.AppError) {
	if schedules, err := models.Repository.Schedule(s.ctx).FindMany(query, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.ScheduleNotFound, err.Error())
		} else {
			return nil, e.NewError500(e.ScheduleFindManyFailed, err.Error())
		}
	} else {
		return schedules, nil
	}
}

func (s *ScheduleService) Delete(id string) *e.AppError {
	if err := models.Repository.Schedule(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.ScheduleDeleteFailed, err.Error())
	}
	return nil
}

func (s *EventScheduleService) Create(es *models.EventSchedule) (*models.EventSchedule, *e.AppError) {
	if err := models.Repository.EventSchedule(s.ctx).Create(es, nil); err != nil {
		return nil, e.NewError500(e.ScheduleCreateFailed, err.Error())
	}
	return es, nil

}

func (s *EventScheduleService) Update(es *models.EventSchedule) (*models.EventSchedule, *e.AppError) {
	if err := models.Repository.EventSchedule(s.ctx).Update(es, nil); err != nil {
		return nil, e.NewError500(e.ScheduleUpdateFailed, err.Error())
	}
	return es, nil
}

func (s *EventScheduleService) FindOne(query *models.EventScheduleQuery, options *models.FindOneOptions) (*models.EventSchedule, *e.AppError) {
	if ess, err := models.Repository.EventSchedule(s.ctx).FindOne(query, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.EventScheduleNotFound, err.Error())
		} else {
			return nil, e.NewError500(e.EventScheduleFindFailed, err.Error())
		}
	} else {
		return ess, nil
	}
}

func (s *EventScheduleService) FindById(id string, options *models.FindOneOptions) (*models.EventSchedule, *e.AppError) {
	if ess, err := models.Repository.EventSchedule(s.ctx).FindByID(id, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.EventScheduleNotFound, err.Error())
		} else {
			return nil, e.NewError500(e.EventScheduleFindFailed, err.Error())
		}
	} else {
		return ess, nil
	}

}

func (s *EventScheduleService) FindPage(query *models.EventScheduleQuery, options *models.FindPageOptions) ([]*models.EventSchedule, *models.Pagination, *e.AppError) {
	if ess, pagination, err := models.Repository.EventSchedule(s.ctx).FindPage(query, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, nil, e.NewError400(e.EventScheduleNotFound, err.Error())
		} else {
			return nil, nil, e.NewError500(e.EventScheduleFindFailed, err.Error())
		}
	} else {
		return ess, pagination, nil
	}

}

func (s *EventScheduleService) FindMany(query *models.EventScheduleQuery, options *models.FindManyOptions) ([]*models.EventSchedule, *e.AppError) {
	if ess, err := models.Repository.EventSchedule(s.ctx).FindMany(query, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.EventScheduleNotFound, err.Error())
		} else {
			return nil, e.NewError500(e.EventScheduleFindManyFailed, err.Error())
		}
	} else {
		return ess, nil
	}

}

func (s *EventScheduleService) Delete(id string) *e.AppError {
	if err := models.Repository.EventSchedule(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.EventScheduleDeleteFailed, err.Error())
	}
	return nil
}
