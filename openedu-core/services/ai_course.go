package services

import (
	"context"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/ai"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"sync"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

func (s *AICourseService) FindOne(query *models.AICourseQuery, options *models.FindOneOptions) (*models.AICourse, *e.AppError) {
	if aiCourse, err := models.Repository.AICourse.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.AICourseGenerate_not_found, err.Error())
		}
		return nil, e.NewError500(e.AICourseGenerate_find_one_failed, err.Error())
	} else {
		return aiCourse, nil
	}
}

func (s *AICourseService) FindPage(query *models.AICourseQuery, options *models.FindPageOptions) ([]*models.AICourse, *models.Pagination, *e.AppError) {
	if aiCourses, pagination, err := models.Repository.AICourse.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.AICourseGenerate_find_page_failed, err.Error())
	} else {
		return aiCourses, pagination, nil
	}
}

func (s *AICourseService) Create(data *models.AICourse) (*models.AICourse, *e.AppError) {
	if err := models.Repository.AICourse.Create(data, nil); err != nil {
		return nil, e.NewError500(e.AICourseGenerate_create_failed, err.Error())
	}
	return data, nil
}

func (s *AICourseService) Update(data *models.AICourse) (*models.AICourse, *e.AppError) {
	if err := models.Repository.AICourse.Update(data, nil); err != nil {
		return nil, e.NewError500(e.AICourseGenerate_update_failed, err.Error())
	}
	return data, nil
}

func (s *AICourseService) FindMany(query *models.AICourseQuery, options *models.FindManyOptions) ([]*models.AICourse, *e.AppError) {
	if aiCourses, err := models.Repository.AICourse.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.AICourseGenerate_find_many_failed, err.Error())
	} else {
		return aiCourses, nil
	}
}

func (s *AICourseService) GenerateCourseByYoutube(params *dto.GenerateCourseFromYoutubeParams) (*models.Course, *e.AppError) {
	playlistID := util.GetPlaylistIDFromYoutubeLink(params.PlaylistLink)
	if playlistID == "" {
		return nil, e.NewError400(e.AICourseGenerate_invalid_playlist_link, e.GetMsg(e.AICourseGenerate_invalid_playlist_link))
	}

	price := decimal.NewFromFloat(0)
	slug := util.GenerateId()

	courseID := util.GenerateId()
	courseCuid := util.GenerateId()
	aiCourseID := util.GenerateId()

	toCreateCourse := &dto.CreateCourseRequest{
		ID:          &courseID,
		Cuid:        &courseCuid,
		Status:      util.NewT(models.CourseSTTDraft),
		Name:        "",
		Slug:        &slug,
		Description: "",
		PriceSettings: &models.CoursePrice{
			IsPay:                false,
			FiatCurrency:         models.DefaultFiatCurrency,
			FiatPrice:            price,
			FiatDiscountPrice:    decimal.NewFromFloat(0),
			CryptoPaymentEnabled: false,
			CryptoCurrency:       models.CryptoCurrencyUSDT,
			CryptoPrice:          decimal.NewFromFloat(0),
			CryptoDiscountPrice:  decimal.NewFromFloat(0),
		},
		ThumbnailID:      "",
		LearnMethod:      "",
		IsAIGenerated:    util.NewBool(true),
		AIGenerateStatus: util.NewT(models.AIStatusPending),
		AICourseID:       &aiCourseID,
		MarkAsCompleted:  false,
	}

	toCreateAICourse := &models.AICourse{
		Model:            models.Model{ID: aiCourseID},
		Status:           models.AIStatusPending,
		TotalCost:        0,
		Language:         models.Repository.AIHistory.GetLanguageFromKey(params.Language),
		Tone:             params.Tone,
		OrgID:            params.Org.ID,
		ThumbnailID:      "",
		UserID:           params.User.ID,
		RetryCount:       0,
		OfferType:        models.AICourseYoutubePlaylistType,
		Title:            "",
		Description:      "",
		SummaryIncluded:  params.SummaryIncluded,
		QuizIncluded:     params.QuizIncluded,
		QuizType:         params.QuizType,
		QuizStatus:       models.AIStatusPending,
		NumberOfQuestion: params.NumberOfQuestion,
		CourseID:         courseID,
		CourseCuid:       courseCuid,
		PlaylistID:       playlistID,
		PlaylistLink:     params.PlaylistLink,
		Slug:             slug,
		OrgSchema:        params.Org.Schema,
		CurrentStep:      models.AICourseYoutubePlaylistGenerateStep,
	}

	offerRequest := &ai.OfferCourseFromYoutubeRequest{
		Tone:            AIHistory.GetAiToneMapByModelTone(params.Tone),
		Language:        models.Repository.AIHistory.GetLanguageFromKey(params.Language),
		PlaylistID:      playlistID,
		SummaryIncluded: params.SummaryIncluded,
		XAPIKey:         setting.ExternalServiceSetting.XAPIKey,
	}
	offerData, err := ai.Course.OfferGenerateCourseFromYoutube(offerRequest)
	if err != nil || offerData == nil {
		return nil, e.NewError500(e.AICourseGenerate_offer_generate_from_youtube_failed, err.Error())
	}
	toCreateAICourse.OfferID = offerData.Data.OfferID

	newCourse, aErr := Course.Create(params.Org, params.User, toCreateCourse, false)
	if aErr != nil {
		return nil, aErr
	}
	newAICourse, aErr := s.Create(toCreateAICourse)
	if aErr != nil {
		return nil, aErr
	}

	request, err := util.StructToMap(offerRequest)
	if err != nil {
		return nil, e.NewError500(e.Json_encode_error, err.Error())
	}

	if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
		RequestIDs:   []string{courseCuid},
		RequestType:  models.CourseModelName,
		UserID:       newAICourse.UserID,
		EntityID:     newCourse.Cuid,
		EntityType:   models.CourseModelName,
		GenerateID:   newAICourse.ID,
		GenerateType: newAICourse.OfferType,
		Step:         newAICourse.CurrentStep,
		Status:       newAICourse.Status,
		Cost:         0,
		Request:      request,
		Response:     map[string]interface{}{},
		Error:        newAICourse.Error,
		StartDate:    int64(util.GetCurrentTime()),
		EndDate:      0,
		OrgID:        newAICourse.OrgID,
		OrgSchema:    newAICourse.OrgSchema,
		AIOfferID:    newAICourse.OfferID,
	}); aErr != nil {
		return nil, aErr
	}

	if newAICourse != nil {
		newCourse.AICourse = newAICourse.ToSimple()
	}

	return newCourse, nil
}

func (s *AICourseService) CronJobGenerateAICourse() *e.AppError {
	query := &models.AICourseQuery{
		Status: util.NewT(models.AIStatusPending),
	}

	options := &models.FindManyOptions{
		Sort:  []string{"create_at asc"},
		Limit: util.NewT(20),
	}

	aiCourses, err := s.FindMany(query, options)
	if err != nil {
		fmt.Errorf(e.GetMsg(e.AICourseGenerate_find_many_failed))
		return err
	}

	if len(aiCourses) > 0 {
		var wg sync.WaitGroup
		lo.ForEach(aiCourses, func(aiCourse *models.AICourse, _ int) {
			wg.Add(1)
			go func(aiCourse *models.AICourse) {
				models.AppSchema = aiCourse.OrgSchema
				defer wg.Done()
				course, aErr := Course.FindOne(&models.CourseQuery{
					ID: &aiCourse.CourseID,
				}, &models.FindOneOptions{})
				if aErr != nil {
					fmt.Errorf(e.GetMsg(e.Course_find_one_failed))
				}

				if aiCourse != nil && course != nil {
					aErr = s.processAICourse(aiCourse, course)
				}
				if aErr != nil {
					fmt.Errorf(e.GetMsg(e.AICourseGenerate_offer_generate_failed), aErr.Msg)
				}

			}(aiCourse)
		})

		wg.Wait()
	}

	if aErr := s.handleTimeOutGenerating(); aErr != nil {
		return aErr
	}

	return nil
}

func (s *AICourseService) handleTimeOutGenerating() *e.AppError {
	now := time.Now()
	tenMinutesAgo := now.Add(-util.TimeOutGeneratingDefault * time.Minute).UnixMilli()

	stillGeneratingAICourse, aErr := s.FindMany(&models.AICourseQuery{
		StatusIn:   []*models.AIStatus{util.NewT(models.AIStatusGenerating), util.NewT(models.AIStatusPending)},
		CreateAtLt: &tenMinutesAgo,
	}, &models.FindManyOptions{})
	if aErr != nil {
		fmt.Errorf(e.GetMsg(e.AICourseGenerate_find_many_failed))
		return aErr
	}

	if len(stillGeneratingAICourse) > 0 {
		for _, a := range stillGeneratingAICourse {
			a.Status = models.AIStatusFailed
			_, aErr := s.Update(a)
			if aErr != nil {
				return aErr
			}

			generatingCourse, err := models.Repository.Course(context.TODO()).FindOne(&models.CourseQuery{
				AICourseID:       &a.ID,
				AIGenerateStatus: util.NewT(models.AIStatusGenerating),
			}, &models.FindOneOptions{})
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				fmt.Errorf(e.GetMsg(e.Course_find_one_failed))
				return e.NewError500(e.Course_find_one_failed, e.GetMsg(e.Course_find_one_failed))
			}
			if generatingCourse != nil {
				generatingCourse.AIGenerateStatus = models.AIStatusFailed
				uErr := models.Repository.Course(context.TODO()).Update(generatingCourse, nil)
				if uErr != nil {
					fmt.Errorf(e.GetMsg(e.Update_course_failed))
					return e.NewError500(e.Update_course_failed, e.GetMsg(e.Update_course_failed))
				}
			}
		}
	}
	return nil
}

func (s *AICourseService) processAICourse(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	// defer func() {
	// 	if aiCourse != nil && aiCourse.Status == models.AICourseStatusGenerating {
	// 		log.Infof("Server stopping, update processing AICourse to Pending with id: %s", aiCourse.ID)
	// 		aiCourse.Status = models.AICourseStatusPending
	// 		if _, appErr := s.Update(aiCourse); appErr != nil {
	// 			log.Errorf("Update AI Course Status To Pending Fail: %s", appErr.Msg)
	// 		}
	// 	}
	// }()
	// log.Debugf("sleeping")
	// time.Sleep(30 * time.Second)

	// update generating flag
	if aErr := s.handleCourseGeneratingGeneration(aiCourse, course); aErr != nil {
		return s.handleCourseFailGeneration(aiCourse, course, nil, &aErr.ErrCode)
	}

	// handle generate course and retry
	if aErr := s.HandleGenerateCourse(aiCourse, course); aErr != nil {
		return s.handleCourseFailGeneration(aiCourse, course, nil, &aErr.ErrCode)
	}

	return nil
}

func (s *AICourseService) HandleGenerateCourse(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	switch aiCourse.OfferType {
	case models.AICourseYoutubePlaylistType:
		if aiCourse.Status == models.AIStatusGenerating && aiCourse.CurrentStep == models.AICourseYoutubePlaylistGenerateStep {
			return s.handleYoutubePlaylistCourse(aiCourse, course)
		}
		if aiCourse.Status == models.AIStatusGenerating && aiCourse.CurrentStep == models.AICourseQuizGenerateStep {
			return s.handleGenerateQuiz(aiCourse, course)
		}
	case models.AICourseLearnerDescriptionType:
		if aiCourse.Status == models.AIStatusGenerating && aiCourse.CurrentStep == models.AICourseLearnerDescriptionGenerateStep {
			return s.handleLearnerDescription(aiCourse, course)
		}
		if aiCourse.Status == models.AIStatusGenerating && aiCourse.CurrentStep == models.AICourseThumbnailGenerateStep {
			return s.handleGenerateThumbnail(aiCourse, course)
		}
		if aiCourse.Status == models.AIStatusGenerating && aiCourse.CurrentStep == models.AICourseOutlineGenerateStep {
			return s.handleGenerateOutline(aiCourse, course)
		}
	default:
	}
	return nil
}

func (s *AICourseService) handleYoutubePlaylistCourse(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	models.AppSchema = aiCourse.OrgSchema
	httpResponse, err := ai.Course.GetCourseGenerateFromYoutube(aiCourse.OfferID, setting.ExternalServiceSetting.XAPIKey)
	if err != nil {
		return s.handleCourseFailGeneration(aiCourse, course, util.NewT(err), util.NewInt(e.AICourseGenerate_get_offer_data_failed))
	}

	response := httpResponse.Data
	aiStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

	if aiStatus == models.AIStatusFailed {
		return s.handleCourseFailGeneration(aiCourse, course, util.NewT(errors.New(e.GetMsg(e.AICourseGenerate_response_with_status_failed))), util.NewInt(e.AICourseGenerate_response_with_status_failed))
	}

	if aiStatus == models.AIStatusPending {
		aiCourse.Status = models.AIStatusPending
		if _, appErr := s.Update(aiCourse); appErr != nil {
			return appErr
		}
		return nil
	}

	if aiStatus == models.AIStatusCompleted {
		// in case ai response nothing
		if response.Metadata == nil {
			return s.handleCourseFailGeneration(aiCourse, course, util.NewT(errors.New(e.GetMsg(e.AICourseGenerate_response_nothing))), util.NewInt(e.AICourseGenerate_response_nothing))
		}

		slug, aErr := s.generateSlug(response.Metadata.CourseTitle, course.Cuid, aiCourse.OrgSchema)
		if aErr != nil {
			return aErr
		}

		// download thumbnail and upload
		thumbnailID := ""
		if response.Metadata.CourseThumbnail != "" {
			file, aErr := Upload.DownLoadAndUploadFile(response.Metadata.CourseThumbnail, aiCourse.UserID)
			if aErr != nil {
				log.Debugf("Get File From Url Failed:  %v", aErr)
			} else {
				thumbnailID = file.ID
			}
		}

		// update ai_course
		aiCourse.Status = models.AIStatusCompleted
		aiCourse.TotalCost = response.Metadata.Cost
		aiCourse.Title = response.Metadata.CourseTitle
		aiCourse.Description = response.Metadata.CourseDescription
		aiCourse.ThumbnailID = thumbnailID
		aiCourse.Slug = slug

		// update course
		course.Name = response.Metadata.CourseTitle
		course.Description = response.Metadata.CourseDescription
		course.ShortDesc = response.Metadata.CourseDescription
		course.ThumbnailID = thumbnailID
		course.AIGenerateStatus = models.AIStatusCompleted
		course.Slug = slug

		if aErr := s.createCourseOutlineFromAIOutline(aiCourse, course, response.Metadata.Sections); aErr != nil {
			return aErr
		}

		// update course and ai course
		if _, appErr := s.Update(aiCourse); appErr != nil {
			return appErr
		}
		if err = models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
			return e.NewError500(e.Update_course_failed, err.Error())
		}

		if aErr := Course.UpdateStats(course, true); err != nil {
			return aErr
		}

		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{course.Cuid},
			RequestType:  models.CourseModelName,
			UserID:       aiCourse.UserID,
			EntityID:     course.Cuid,
			EntityType:   models.CourseModelName,
			GenerateID:   aiCourse.ID,
			GenerateType: aiCourse.OfferType,
			Step:         aiCourse.CurrentStep,
			Status:       models.AIStatusCompleted,
			Cost:         response.Metadata.Cost,
			Response:     historyResp,
			Error:        aiCourse.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiCourse.OfferID,
		}); aErr != nil {
			return aErr
		}

		// wait for quiz complete then send notification
		if !aiCourse.QuizIncluded {
			s.sendAICourseNotificationToUser(aiCourse, course)

			s.sendAICourseWebsocketMsgToUser(aiCourse, course, map[string]any{
				"course_id":   course.ID,
				"status":      models.AIStatusCompleted,
				"course_slug": course.Slug,
				"course_name": course.Name,
				"update_at":   course.UpdateAt,
			})
		}
	}
	return nil
}

func (s *AICourseService) addLessonContents(lesson *ai.LessonInfo, lessonContents *[]*models.LessonContent, course *models.Course, aiCourse *models.AICourse, sectionID string, lessonID string, quizContents *[]*models.LessonContent) {
	order := 0
	if aiCourse.SummaryIncluded || aiCourse.OfferType == models.AICourseLearnerDescriptionType {
		*lessonContents = append(*lessonContents, &models.LessonContent{
			CourseID:  course.ID,
			UserID:    aiCourse.UserID,
			SectionID: sectionID,
			LessonID:  lessonID,
			Title:     lesson.LessonName,
			Content:   lesson.LessonText,
			Order:     order,
			Type:      models.LessonTypeText,
			UID:       util.GenerateId(),
		})
		order++
	}
	if lesson.LessonEmbedded != "" {
		*lessonContents = append(*lessonContents, &models.LessonContent{
			CourseID:  course.ID,
			UserID:    aiCourse.UserID,
			SectionID: sectionID,
			LessonID:  lessonID,
			Title:     lesson.LessonName,
			Content:   lesson.LessonEmbedded,
			Order:     order,
			Type:      models.LessonTypeEmbedded,
			UID:       util.GenerateId(),
		})
		order++
	}
	if aiCourse.QuizIncluded {
		quizContentID := util.GenerateId()
		quizLessonContent := &models.LessonContent{
			Model:     models.Model{ID: quizContentID},
			CourseID:  course.ID,
			UserID:    aiCourse.UserID,
			SectionID: sectionID,
			LessonID:  lessonID,
			Title:     lesson.LessonName,
			Content:   lesson.LessonEmbedded,
			Order:     order,
			Type:      models.LessonTypeQuiz,
			UID:       util.GenerateId(),
		}
		*lessonContents = append(*lessonContents, quizLessonContent)
		*quizContents = append(*quizContents, quizLessonContent)
	}
}

func (s *AICourseService) handleCourseRetryGeneration(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	aiCourse.Status = models.AIStatusPending
	course.AIGenerateStatus = models.AIStatusPending

	aiCourse.RetryCount += 1

	if _, appErr := s.Update(aiCourse); appErr != nil {
		return appErr
	}

	if err := models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
		return e.NewError500(e.Update_course_failed, err.Error())
	}

	return nil
}

func (s *AICourseService) handleCourseGeneratingGeneration(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	aiCourse.Status = models.AIStatusGenerating
	course.AIGenerateStatus = models.AIStatusGenerating

	if _, appErr := s.Update(aiCourse); appErr != nil {
		return appErr
	}

	if err := models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
		return e.NewError500(e.Update_course_failed, err.Error())
	}

	return nil
}

// call when error related to AI
func (s *AICourseService) handleCourseFailGeneration(aiCourse *models.AICourse, course *models.Course, failErr *error, failCode *int) *e.AppError {
	var currentErr error
	var currentCode int
	if failErr != nil {
		currentErr = *failErr
	} else {
		if failCode != nil {
			currentErr = errors.New(e.GetMsg(*failCode))
		} else {
			currentErr = errors.New(e.GetMsg(e.AICourseGenerate_internal_ai_generate_failed))
		}
	}
	if failCode != nil {
		currentCode = *failCode
	} else {
		currentCode = e.AICourseGenerate_internal_ai_generate_failed
	}

	// if internal error, no retry
	if aiCourse.RetryCount < util.MaxTryTimes && failErr != nil && failCode != nil {
		return s.handleCourseRetryGeneration(aiCourse, course)
	}

	aiCourse.Status = models.AIStatusFailed
	course.AIGenerateStatus = models.AIStatusFailed

	aiCourse.Error = &models.DetailAIError{
		Code: currentCode,
		Msg:  currentErr.Error(),
	}

	if _, appErr := s.Update(aiCourse); appErr != nil {
		return appErr
	}

	if err := models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
		return e.NewError500(e.Update_course_failed, err.Error())
	}

	if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
		RequestIDs:   []string{course.Cuid},
		RequestType:  models.CourseModelName,
		UserID:       aiCourse.UserID,
		EntityID:     course.Cuid,
		EntityType:   models.CourseModelName,
		GenerateID:   aiCourse.ID,
		GenerateType: aiCourse.OfferType,
		Step:         aiCourse.CurrentStep,
		Status:       models.AIStatusFailed,
		Cost:         0,
		Error:        aiCourse.Error,
		EndDate:      int64(util.GetCurrentTime()),
	}); aErr != nil {
		return aErr
	}

	s.sendAICourseNotificationToUser(aiCourse, course)

	s.sendAICourseWebsocketMsgToUser(aiCourse, course, map[string]any{
		"course_id":  course.ID,
		"status":     models.AIStatusFailed,
		"error_code": aiCourse.Error.Code,
		"update_at":  course.UpdateAt,
	})

	return nil
}

func (s *AICourseService) makeNotificationPropsForCourse(org *models.Organization, course *models.Course, aiCourse *models.AICourse) models.JSONB {
	return models.JSONB{
		"org_id":      org.ID,
		"org_name":    org.Name,
		"org_domain":  org.Domain,
		"course_id":   course.ID,
		"course_cuid": course.Cuid,
		"course_name": course.Name,
		"course_slug": course.Slug,
		"provider":    aiCourse.OfferType,
	}
}

func (s *AICourseService) offerQuizCourse(aiCourse *models.AICourse, course *models.Course, quizContents []*models.LessonContent) *e.AppError {
	toCreateQuizzes := []*dto.QuizRequest{}
	quizContentMapByID := make(map[string]*models.LessonContent)
	for _, content := range quizContents {
		quizID := util.GenerateId()

		toCreateQuizzes = append(toCreateQuizzes, &dto.QuizRequest{
			OrgID:             course.ID,
			ID:                quizID,
			RelatedEntityType: models.QuizRelationEntityLessonContent,
			RelatedEntityID:   content.ID,
			RelationType:      models.QuizRelationTypeIs,
			TriggerConditions: dto.QuizTriggerConditionsRequest{},
			Title:             "",
			Description:       "",
			Settings: dto.QuizSettingsRequest{
				PassCriteria: models.QuizPassCriteriaCorrectAnswers,
			},
			Questions: []*dto.QuizQuestionRequest{},
		})
		quizContentMapByID[content.ID] = content
	}

	user, aErr := User.FindByID(aiCourse.UserID, &models.FindOneOptions{})
	if aErr != nil {
		return aErr
	}

	if _, aErr := Quiz.CreateMany(user, toCreateQuizzes); aErr != nil {
		return aErr
	}

	aiHistories := []*models.AIHistory{}
	// update ai course
	failOfferQuizCount := 0
	for _, quiz := range toCreateQuizzes {

		offerRequest := &ai.OfferGenerateQuizRequest{
			LessonName:     quizContentMapByID[quiz.RelatedEntityID].Title,
			LessonEmbedded: quizContentMapByID[quiz.RelatedEntityID].Content,
			QuestionType:   AIHistory.GetAIQuizTypeMapByQuestionType(aiCourse.QuizType),
			Language:       aiCourse.Language,
			TotalQuestion:  aiCourse.NumberOfQuestion,
			XAPIKey:        setting.ExternalServiceSetting.XAPIKey,
		}

		request, err := util.StructToMap(offerRequest)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}

		toCreateHistory := &models.AIHistory{
			RequestIDs:   []string{aiCourse.CourseCuid},
			RequestType:  models.QuizModelName,
			UserID:       aiCourse.UserID,
			EntityID:     aiCourse.CourseCuid,
			EntityType:   models.CourseModelName,
			GenerateID:   aiCourse.ID,
			GenerateType: aiCourse.OfferType,
			Request:      request,
			StartDate:    int64(util.GetCurrentTime()),
			Error:        nil,
			OrgID:        aiCourse.OrgID,
			OrgSchema:    aiCourse.OrgSchema,
			Step:         models.AICourseQuizGenerateStep,
		}

		httpResponse, err := ai.Course.OfferGenerateQuiz(offerRequest)

		offerData := httpResponse.Data

		quizDetail := &models.QuizDetail{
			Status:  models.AIStatusPending,
			OfferID: "",
			QuizID:  quiz.ID,
		}
		if err != nil || offerData == nil {
			quizDetail.Status = models.AIStatusFailed
			failOfferQuizCount += 1
			toCreateHistory.Status = models.AIStatusFailed
			toCreateHistory.Error = &models.DetailAIError{
				Code: e.AICourseGenerate_offer_generate_failed,
				Msg:  e.GetMsg(e.AICourseGenerate_offer_generate_failed),
			}
		} else {
			quizDetail.OfferID = offerData.OfferID
			toCreateHistory.AIOfferID = offerData.OfferID
		}

		aiCourse.QuizDetails = append(aiCourse.QuizDetails, quizDetail)

		aiHistories = append(aiHistories, toCreateHistory)
	}

	if len(aiHistories) > 0 {
		if _, aErr := AIHistory.CreateMany(aiHistories); aErr != nil {
			return aErr
		}
	}

	// status course will be waiting, quiz will continue
	if failOfferQuizCount >= len(quizContents) { // if all quiz offer are fail
		aiCourse.QuizStatus = models.AIStatusFailed
	} else {
		aiCourse.QuizStatus = models.AIStatusPending
		aiCourse.Status = models.AIStatusPending
		course.AIGenerateStatus = models.AIStatusPending
		aiCourse.CurrentStep = models.AICourseQuizGenerateStep
	}

	return nil
}

func (s *AICourseService) handleGenerateQuiz(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	// get ai course -> get quiz offer id
	quizDetails := aiCourse.QuizDetails
	stillPending := false
	updateQuizzes := []*dto.QuizRequest{}

	for _, quizDetail := range quizDetails {
		// call api check status for quiz
		httpResp, err := ai.Course.GetQuizGenerate(quizDetail.OfferID, setting.ExternalServiceSetting.XAPIKey)
		response := httpResp.Data
		quizStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

		if quizStatus == models.AIStatusPending {
			stillPending = true
			continue
		}
		if quizStatus == models.AIStatusManual {
			continue
		}

		// if err or status = fail -> soft delete quiz for not showing on FE
		if err != nil || quizStatus == models.AIStatusFailed {
			quizDetail.Status = models.AIStatusFailed
		}

		// handle success
		if quizStatus == models.AIStatusCompleted {
			quizDetail.Status = models.AIStatusCompleted
			quizQuestionRequests := []*dto.QuizQuestionRequest{}
			for _, respQuizQuestion := range response.Metadata.Quiz {
				aiQuizType := AIHistory.GetQuestionTypeByAIQuizType(respQuizQuestion.QuestionType)
				questionItems := []*dto.QuizQuestionItemRequest{}
				for _, item := range respQuizQuestion.Options {
					questionItems = append(questionItems, &dto.QuizQuestionItemRequest{
						Text: item,
						Side: 0,
					})
				}
				// add answer
				correctItemSets := [][]*dto.QuizQuestionItemRequest{}
				switch aiQuizType {
				case models.QuizQuestionTypeSingleChoice:
					for _, answer := range respQuizQuestion.CorrectAnswer {
						correctItems := []*dto.QuizQuestionItemRequest{}
						correctItems = append(correctItems, &dto.QuizQuestionItemRequest{
							Text: answer,
							Side: 0,
						})
						correctItemSets = append(correctItemSets, correctItems)
					}
				case models.QuizQuestionTypeMultipleChoice:
					correctItems := []*dto.QuizQuestionItemRequest{}
					for _, answer := range respQuizQuestion.CorrectAnswer {
						correctItems = append(correctItems, &dto.QuizQuestionItemRequest{
							Text: answer,
							Side: 0,
						})
					}
					correctItemSets = append(correctItemSets, correctItems)
				default:
					continue
				}

				// update quiz request
				quizQuestionRequests = append(quizQuestionRequests, &dto.QuizQuestionRequest{
					Title:           respQuizQuestion.QuestionContent,
					Description:     "",
					Type:            AIHistory.GetQuestionTypeByAIQuizType(respQuizQuestion.QuestionType),
					Explanation:     respQuizQuestion.Explanation,
					Points:          int(respQuizQuestion.Point),
					Items:           questionItems,
					CorrectItemSets: correctItemSets,
					Settings: dto.QuizQuestionSettingsRequest{
						TimeLimit: models.Duration(time.Duration(respQuizQuestion.Duration) * time.Second),
					},
				})
			}
			updateQuizzes = append(updateQuizzes, &dto.QuizRequest{
				ID:        quizDetail.QuizID,
				Questions: quizQuestionRequests,
				Settings: dto.QuizSettingsRequest{
					PassCriteria: models.QuizPassCriteriaCorrectAnswers,
				},
			})

			historyResp, err := util.StructToMap(response)
			if err != nil {
				return e.NewError500(e.Json_encode_error, err.Error())
			}
			if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
				RequestIDs:   []string{quizDetail.QuizID},
				RequestType:  models.QuizModelName,
				UserID:       aiCourse.UserID,
				EntityID:     course.Cuid,
				EntityType:   models.CourseModelName,
				GenerateID:   aiCourse.ID,
				GenerateType: aiCourse.OfferType,
				Step:         aiCourse.CurrentStep,
				Status:       models.AIStatusCompleted,
				Cost:         response.Metadata.Cost,
				Response:     historyResp,
				Error:        aiCourse.Error,
				EndDate:      int64(util.GetCurrentTime()),
				AIOfferID:    aiCourse.OfferID,
			}); aErr != nil {
				return aErr
			}
		}
	}
	if len(updateQuizzes) > 0 {
		if _, appErr := Quiz.UpdateMany(updateQuizzes); appErr != nil {
			return appErr
		}
	}

	if !stillPending {
		numFailQuiz := lo.Filter(aiCourse.QuizDetails, func(q *models.QuizDetail, _ int) bool {
			return q.Status == models.AIStatusFailed
		})
		if len(numFailQuiz) >= len(aiCourse.QuizDetails) {
			aiCourse.QuizStatus = models.AIStatusFailed
		} else {
			aiCourse.QuizStatus = models.AIStatusCompleted
		}
		aiCourse.Status = models.AIStatusCompleted
		course.AIGenerateStatus = models.AIStatusCompleted
	} else {
		aiCourse.Status = models.AIStatusPending
	}

	// update course and ai course
	if _, appErr := s.Update(aiCourse); appErr != nil {
		return appErr
	}
	if err := models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
		return e.NewError500(e.Update_course_failed, err.Error())
	}

	if course.AIGenerateStatus == models.AIStatusCompleted {
		s.sendAICourseNotificationToUser(aiCourse, course)

		s.sendAICourseWebsocketMsgToUser(aiCourse, course, map[string]any{
			"course_id":   course.ID,
			"status":      models.AIStatusCompleted,
			"course_slug": course.Slug,
			"course_name": course.Name,
			"update_at":   course.UpdateAt,
		})
	}

	return nil
}

func (s *AICourseService) GenerateCourseByLearnerDescription(params *dto.GenerateCourseFromLearnerDescriptionParams) (*models.Course, *e.AppError) {

	materialID := ""
	materialURL := ""
	if params.MaterialID != "" {
		materialFile, err := models.Repository.File.FindOne(&models.FileQuery{
			ID: &params.MaterialID,
		}, &models.FindOneOptions{})
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, e.NewError400(e.Error_file_not_found, err.Error())
			}
			return nil, e.NewError500(e.Error_file_find_one_failed, err.Error())
		}
		materialID = materialFile.ID
		materialURL = materialFile.URL
	}

	levels := []*dto.DefaultEntityRequest{}
	level := &models.Category{}
	if params.LevelID != "" {
		existedLevel, aErr := Category.FindOne(&models.CategoryQuery{
			Active: util.NewBool(true),
			Type:   util.NewT(models.TypeLevel),
			ID:     &params.LevelID,
		}, &models.FindOneOptions{})
		if aErr != nil {
			return nil, aErr
		}
		levels = append(levels, &dto.DefaultEntityRequest{
			ID: existedLevel.ID,
		})
		level = existedLevel
	}

	// call generate from ai
	offerRequest := &ai.OfferCourseFromLearnerDescriptionRequest{
		LearnerInformation: params.LearnerInfo,
		CourseContent:      params.ContentInfo,
		MaterialURLs:       []string{materialURL},
		CourseLevel:        level.Name,
		Language:           models.Repository.AIHistory.GetLanguageFromKey(params.Language),
		CourseDuration:     params.Duration,
		CourseDurationIn:   AIHistory.GetAIDurationTypeMapByModelDurationType(params.DurationType),
		StudyLoad:          params.StudyLoad,
		XAPIKey:            setting.ExternalServiceSetting.XAPIKey,
	}
	offerData, err := ai.Course.OfferGenerateCourseFromLearnerDescription(offerRequest)
	if err != nil || offerData == nil {
		return nil, e.NewError500(e.AICourseGenerate_offer_generate_from_learner_desc_failed, err.Error())
	}

	course := &models.Course{}
	aiCourse := &models.AICourse{}

	if params.CourseCuid != "" {
		existAICourse, aErr := s.FindOne(&models.AICourseQuery{
			CourseCuid: &params.CourseCuid,
		}, &models.FindOneOptions{})
		if aErr != nil {
			return nil, aErr
		}

		existCourse, aErr := Course.FindOne(&models.CourseQuery{
			AICourseID: &existAICourse.ID,
		}, &models.FindOneOptions{})
		if aErr != nil {
			return nil, aErr
		}

		course = existCourse
		aiCourse = existAICourse
	} else {
		// create records
		price := decimal.NewFromFloat(0)
		slug := util.GenerateId()

		courseID := util.GenerateId()
		courseCuid := util.GenerateId()
		aiCourseID := util.GenerateId()

		toCreateCourse := &dto.CreateCourseRequest{
			ID:          &courseID,
			Cuid:        &courseCuid,
			Status:      util.NewT(models.CourseSTTDraft),
			Name:        "",
			Slug:        &slug,
			Description: "",
			PriceSettings: &models.CoursePrice{
				IsPay:                false,
				FiatCurrency:         models.DefaultFiatCurrency,
				FiatPrice:            price,
				FiatDiscountPrice:    decimal.NewFromFloat(0),
				CryptoPaymentEnabled: false,
				CryptoCurrency:       models.CryptoCurrencyUSDT,
				CryptoPrice:          decimal.NewFromFloat(0),
				CryptoDiscountPrice:  decimal.NewFromFloat(0),
			},
			ThumbnailID:     "",
			LearnMethod:     "",
			IsAIGenerated:   util.NewBool(true),
			AICourseID:      &aiCourseID,
			MarkAsCompleted: false,
		}

		toCreateAICourse := &models.AICourse{
			Model:       models.Model{ID: aiCourseID},
			TotalCost:   0,
			ThumbnailID: "",
			RetryCount:  0,
			Title:       "",
			Description: "",
			CourseID:    courseID,
			CourseCuid:  courseCuid,
			Slug:        slug,
		}

		newCourse, aErr := Course.Create(params.Org, params.User, toCreateCourse, false)
		if aErr != nil {
			return nil, aErr
		}

		newAICourse, aErr := s.Create(toCreateAICourse)
		if aErr != nil {
			return nil, aErr
		}
		course = newCourse
		aiCourse = newAICourse
	}

	aiCourse.LearnerInfo = params.LearnerInfo
	aiCourse.ContentInfo = params.ContentInfo
	aiCourse.LevelID = level.ID
	aiCourse.MaterialID = materialID
	aiCourse.DurationType = params.DurationType
	aiCourse.Duration = params.Duration
	aiCourse.StudyLoad = params.StudyLoad
	aiCourse.OfferType = models.AICourseLearnerDescriptionType
	aiCourse.Language = models.Repository.AIHistory.GetLanguageFromKey(params.Language)
	aiCourse.Status = models.AIStatusPending
	aiCourse.UserID = params.User.ID
	aiCourse.OrgID = params.Org.ID
	aiCourse.OrgSchema = params.Org.Schema
	aiCourse.CurrentStep = models.AICourseLearnerDescriptionGenerateStep

	course.AIGenerateStatus = models.AIStatusPending

	aiCourse.OfferID = offerData.Data.OfferID
	aiCourse.GeneralInfoStatus = models.AIStatusPending

	if _, aErr := s.Update(aiCourse); aErr != nil {
		return nil, aErr
	}

	if _, aErr := Course.Update(course, params.Org, params.User, &dto.UpdateCourseRequest{
		Name:        course.Name,
		Slug:        &course.Slug,
		Description: course.Description,
		ShortDesc:   course.ShortDesc,
		ThumbnailID: "",
		PriceSettings: &models.CoursePrice{
			IsPay:                course.PriceSettings.IsPay,
			FiatCurrency:         course.PriceSettings.FiatCurrency,
			FiatPrice:            course.PriceSettings.FiatPrice,
			FiatDiscountPrice:    course.PriceSettings.FiatDiscountPrice,
			CryptoPaymentEnabled: course.PriceSettings.CryptoPaymentEnabled,
			CryptoCurrency:       course.PriceSettings.CryptoCurrency,
			CryptoPrice:          course.PriceSettings.CryptoPrice,
			CryptoDiscountPrice:  course.PriceSettings.CryptoDiscountPrice,
		},
		Enable:          course.Enable,
		StartDate:       course.StartDate,
		EndDate:         course.EndDate,
		MarkAsCompleted: course.MarkAsCompleted,
		LearnMethod:     course.LearnMethod,
		Levels:          levels,
		Props:           &course.Props,
	}); aErr != nil {
		return nil, aErr
	}

	request, err := util.StructToMap(offerRequest)
	if err != nil {
		return nil, e.NewError500(e.Json_encode_error, err.Error())
	}

	if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
		RequestIDs:   []string{course.Cuid},
		RequestType:  models.CourseModelName,
		UserID:       aiCourse.UserID,
		EntityID:     course.Cuid,
		EntityType:   models.CourseModelName,
		GenerateID:   aiCourse.ID,
		GenerateType: aiCourse.OfferType,
		Step:         aiCourse.CurrentStep,
		Status:       aiCourse.Status,
		Cost:         0,
		Request:      request,
		Response:     map[string]interface{}{},
		Error:        aiCourse.Error,
		StartDate:    int64(util.GetCurrentTime()),
		EndDate:      0,
		OrgID:        aiCourse.OrgID,
		OrgSchema:    aiCourse.OrgSchema,
		AIOfferID:    aiCourse.OfferID,
	}); aErr != nil {
		return nil, aErr
	}

	if aiCourse != nil {
		course.AICourse = aiCourse.ToSimple()
	}

	return course, nil
}

func (s *AICourseService) GenerateCourseThumbnail(params *dto.GenerateCourseThumbnailParams) (*models.Course, *e.AppError) {
	// find and update record
	aiCourse, aErr := s.FindOne(&models.AICourseQuery{CourseCuid: &params.CourseCuid}, &models.FindOneOptions{})
	if aErr != nil {
		return nil, aErr
	}

	if aiCourse.ThumbnailGenerateCount >= util.ThumbnailMaxGenerateTimes {
		return nil, e.NewError400(e.AICourseGenerate_max_try_thumbnail_times_failed, e.GetMsg(e.AICourseGenerate_max_try_thumbnail_times_failed))
	}

	course, aErr := Course.FindOne(&models.CourseQuery{ID: &aiCourse.CourseID}, &models.FindOneOptions{})
	if aErr != nil {
		return nil, aErr
	}

	// call generate from ai
	offerRequest := &ai.OfferGenerateThumbnailRequest{
		ThumbnailPrompt:    params.ThumbnailDescription,
		NumberOfThumbnails: params.ThumbnailQuantity,
		ThumbnailStyle:     AIHistory.GetAIThumbnailStyleMapByModelThumbnailStyle(params.ThumbnailStyle),
		OfferID:            aiCourse.OfferID,
		XAPIKey:            setting.ExternalServiceSetting.XAPIKey,
	}

	if _, err := ai.Course.OfferGenerateThumbnail(offerRequest); err != nil {
		return nil, e.NewError500(e.AICourseGenerate_offer_generate_thumbnail_failed, err.Error())
	}
	aiCourse.Status = models.AIStatusPending
	course.AIGenerateStatus = models.AIStatusPending
	aiCourse.ThumbnailStatus = models.AIStatusPending

	aiCourse.CurrentStep = models.AICourseThumbnailGenerateStep
	aiCourse.ThumbnailGenerateCount += 1
	aiCourse.ThumbnailDescription = params.ThumbnailDescription
	aiCourse.ThumbnailStyle = params.ThumbnailStyle
	aiCourse.ThumbnailQuantity = params.ThumbnailQuantity

	if _, aErr := s.Update(aiCourse); aErr != nil {
		return nil, aErr
	}

	if err := models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
		return nil, e.NewError500(e.Update_course_failed, err.Error())
	}

	request, err := util.StructToMap(offerRequest)
	if err != nil {
		return nil, e.NewError500(e.Json_encode_error, err.Error())
	}

	if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
		RequestIDs:   []string{course.Cuid},
		RequestType:  models.FileModelName,
		UserID:       aiCourse.UserID,
		EntityID:     course.Cuid,
		EntityType:   models.CourseModelName,
		GenerateID:   aiCourse.ID,
		GenerateType: aiCourse.OfferType,
		Step:         aiCourse.CurrentStep,
		Status:       aiCourse.Status,
		Cost:         0,
		Request:      request,
		Response:     map[string]interface{}{},
		Error:        aiCourse.Error,
		StartDate:    int64(util.GetCurrentTime()),
		EndDate:      0,
		OrgID:        aiCourse.OrgID,
		OrgSchema:    aiCourse.OrgSchema,
		AIOfferID:    aiCourse.OfferID,
	}); aErr != nil {
		return nil, aErr
	}

	if aiCourse != nil {
		course.AICourse = aiCourse.ToSimple()
	}

	return course, nil
}

func (s *AICourseService) GenerateOutlineFromLearnerDescription(params *dto.GenerateOutlineFromLearnerDescriptionParams) (*models.Course, *e.AppError) {
	// find and update record
	aiCourse, aErr := s.FindOne(&models.AICourseQuery{CourseCuid: &params.CourseCuid}, &models.FindOneOptions{})
	if aErr != nil {
		return nil, aErr
	}

	course, aErr := Course.FindOne(&models.CourseQuery{
		ID: &aiCourse.CourseID,
	}, &models.FindOneOptions{})
	if aErr != nil {
		return nil, aErr
	}

	// call generate from ai
	offerRequest := &ai.OfferGenerateOutlineRequest{
		CourseTitle:       params.Title,
		CourseDescription: params.Description,
		OfferID:           aiCourse.OfferID,
		XAPIKey:           setting.ExternalServiceSetting.XAPIKey,
	}
	_, err := ai.Course.OfferGenerateOutline(offerRequest)
	if err != nil {
		return nil, e.NewError500(e.AICourseGenerate_offer_generate_outline_failed, err.Error())
	}
	aiCourse.Status = models.AIStatusPending
	course.AIGenerateStatus = models.AIStatusPending

	aiCourse.CurrentStep = models.AICourseOutlineGenerateStep
	aiCourse.Title = params.Title
	aiCourse.Description = params.Description
	aiCourse.ThumbnailID = params.ThumbnailID

	course.Name = params.Title
	course.Description = params.Description
	course.ShortDesc = params.Description
	course.ThumbnailID = params.ThumbnailID

	if _, aErr := s.Update(aiCourse); aErr != nil {
		return nil, aErr
	}

	if err := models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
		return nil, e.NewError500(e.Update_course_failed, err.Error())
	}

	request, err := util.StructToMap(offerRequest)
	if err != nil {
		return nil, e.NewError500(e.Json_encode_error, err.Error())
	}

	if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
		RequestIDs:   []string{course.Cuid},
		RequestType:  models.CourseModelName,
		UserID:       aiCourse.UserID,
		EntityID:     course.Cuid,
		EntityType:   models.CourseModelName,
		GenerateID:   aiCourse.ID,
		GenerateType: aiCourse.OfferType,
		Step:         aiCourse.CurrentStep,
		Status:       aiCourse.Status,
		Cost:         0,
		Request:      request,
		Response:     map[string]interface{}{},
		Error:        aiCourse.Error,
		StartDate:    int64(util.GetCurrentTime()),
		EndDate:      0,
		OrgID:        aiCourse.OrgID,
		OrgSchema:    aiCourse.OrgSchema,
		AIOfferID:    aiCourse.OfferID,
	}); aErr != nil {
		return nil, aErr
	}

	if aiCourse != nil {
		course.AICourse = aiCourse.ToSimple()
	}

	return course, nil
}

func (s *AICourseService) handleLearnerDescription(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	models.AppSchema = aiCourse.OrgSchema
	httpResponse, err := ai.Course.GetCourseGenerateFromLearnerDescription(aiCourse.OfferID, setting.ExternalServiceSetting.XAPIKey)
	if err != nil {
		aiCourse.GeneralInfoStatus = models.AIStatusFailed
		return s.handleCourseFailGeneration(aiCourse, course, util.NewT(err), util.NewInt(e.AICourseGenerate_get_offer_data_failed))
	}

	response := httpResponse.Data
	aiStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

	if response.Metadata == nil {
		aiCourse.GeneralInfoStatus = models.AIStatusFailed
		return s.handleCourseFailGeneration(aiCourse, course, util.NewT(errors.New(e.GetMsg(e.AICourseGenerate_response_nothing))), util.NewInt(e.AICourseGenerate_response_nothing))
	}

	if aiStatus == models.AIStatusFailed {
		aiCourse.GeneralInfoStatus = models.AIStatusFailed
		return s.handleCourseFailGeneration(aiCourse, course, util.NewT(errors.New(e.GetMsg(e.AICourseGenerate_response_with_status_failed))), util.NewInt(e.AICourseGenerate_response_with_status_failed))
	}

	if aiStatus == models.AIStatusPending {
		aiCourse.GeneralInfoStatus = models.AIStatusPending
		aiCourse.Status = models.AIStatusPending
		course.AIGenerateStatus = models.AIStatusPending

		// update course and ai course
		if _, appErr := s.Update(aiCourse); appErr != nil {
			return appErr
		}
		if err = models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
			return e.NewError500(e.Update_course_failed, err.Error())
		}
		return nil
	}

	if aiStatus == models.AIStatusCompleted {
		slug, aErr := s.generateSlug(response.Metadata.CourseTitle, course.Cuid, aiCourse.OrgSchema)
		if aErr != nil {
			return aErr
		}

		// update ai_course
		aiCourse.Title = response.Metadata.CourseTitle
		aiCourse.Description = response.Metadata.CourseDescription
		aiCourse.TotalCost = response.Metadata.Cost
		aiCourse.Slug = slug
		aiCourse.ThumbnailDescription = response.Metadata.ThumbnailPrompt

		// update course
		course.Name = response.Metadata.CourseTitle
		course.Description = response.Metadata.CourseDescription
		course.ShortDesc = response.Metadata.CourseDescription
		course.Slug = slug

		aiCourse.GeneralInfoStatus = models.AIStatusCompleted
		aiCourse.Status = models.AIStatusWaiting         // step 1 done then -> waiting
		course.AIGenerateStatus = models.AIStatusWaiting // step 1 done then -> waiting
		aiCourse.Error = nil

		// update course and ai course
		if _, appErr := s.Update(aiCourse); appErr != nil {
			return appErr
		}
		if err = models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
			return e.NewError500(e.Update_course_failed, err.Error())
		}

		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{course.Cuid},
			RequestType:  models.CourseModelName,
			UserID:       aiCourse.UserID,
			EntityID:     course.Cuid,
			EntityType:   models.CourseModelName,
			GenerateID:   aiCourse.ID,
			GenerateType: aiCourse.OfferType,
			Step:         aiCourse.CurrentStep,
			Status:       models.AIStatusCompleted,
			Cost:         response.Metadata.Cost,
			Response:     historyResp,
			Error:        aiCourse.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiCourse.OfferID,
		}); aErr != nil {
			return aErr
		}

		s.sendAICourseWebsocketMsgToUser(aiCourse, course, map[string]any{
			"course_id":           course.ID,
			"status":              models.AIStatusPending,
			"course_slug":         course.Slug,
			"course_name":         course.Name,
			"update_at":           course.UpdateAt,
			"general_info_status": models.AIStatusCompleted,
		})
	}
	return nil
}

func (s *AICourseService) handleGenerateThumbnail(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	socketMsgData := map[string]any{}
	models.AppSchema = aiCourse.OrgSchema
	httpResponse, err := ai.Course.GetCourseThumbnail(aiCourse.OfferID, setting.ExternalServiceSetting.XAPIKey)
	if err != nil {
		aiCourse.ThumbnailStatus = models.AIStatusFailed
		aiCourse.ThumbnailError = &models.DetailAIError{
			Code: e.AICourseGenerate_get_thumbnail_data_failed,
			Msg:  errors.New(e.GetMsg(e.AICourseGenerate_get_thumbnail_data_failed) + err.Error()).Error(),
		}

		aiCourse.Status = models.AIStatusPending
		course.AIGenerateStatus = models.AIStatusPending

		// update course and ai course
		if _, appErr := s.Update(aiCourse); appErr != nil {
			return appErr
		}
		if err = models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
			return e.NewError500(e.Update_course_failed, err.Error())
		}

		socketMsgData = map[string]any{
			"course_id":        course.ID,
			"status":           models.AIStatusPending,
			"error_code":       aiCourse.ThumbnailError.Code,
			"update_at":        course.UpdateAt,
			"thumbnail_status": models.AIStatusFailed,
		}
	} else {
		response := httpResponse.Data
		aiStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

		if aiStatus == models.AIStatusPending || aiStatus == models.AIStatusGenerating {
			aiCourse.Status = models.AIStatusPending
			aiCourse.ThumbnailStatus = models.AIStatusPending
			if _, appErr := s.Update(aiCourse); appErr != nil {
				return appErr
			}
			return nil
		}

		if aiStatus == models.AIStatusFailed || response.Metadata == nil {
			aiCourse.ThumbnailStatus = models.AIStatusFailed
			aiCourse.ThumbnailError = &models.DetailAIError{
				Code: e.AICourseGenerate_get_thumbnail_data_failed,
				Msg:  errors.New(e.GetMsg(e.AICourseGenerate_get_thumbnail_data_failed)).Error(),
			}

			aiCourse.Status = models.AIStatusPending
			course.AIGenerateStatus = models.AIStatusPending

			// update course and ai course
			if _, appErr := s.Update(aiCourse); appErr != nil {
				return appErr
			}
			if err = models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
				return e.NewError500(e.Update_course_failed, err.Error())
			}

			socketMsgData = map[string]any{
				"course_id":        course.ID,
				"status":           models.AIStatusPending,
				"error_code":       aiCourse.ThumbnailError.Code,
				"update_at":        course.UpdateAt,
				"thumbnail_status": models.AIStatusFailed,
			}
		}

		if aiStatus == models.AIStatusCompleted {
			// update ai_course
			aiCourse.ThumbnailStatus = models.AIStatusCompleted
			aiCourse.Status = models.AIStatusWaiting
			course.AIGenerateStatus = models.AIStatusWaiting

			thumbnailGeneratedIDs := []string{}
			for _, thumbnail := range response.Metadata.CourseThumbnails {
				// download thumbnail and upload
				if thumbnail != "" {
					file, aErr := Upload.DownLoadAndUploadFile(thumbnail, aiCourse.UserID)
					if aErr != nil {
						log.Debugf("Get File From Url Failed:  %v", aErr)
					} else {
						thumbnailGeneratedIDs = append(thumbnailGeneratedIDs, file.ID)
					}
				}
			}
			aiCourse.TotalCost = response.Metadata.Cost
			aiCourse.GeneratedThumbnailIDs = append(aiCourse.GeneratedThumbnailIDs, thumbnailGeneratedIDs...)
			aiCourse.Error = nil
			aiCourse.ThumbnailDescription = response.Metadata.ThumbnailPrompt

			// update course and ai course
			if _, appErr := s.Update(aiCourse); appErr != nil {
				return appErr
			}
			if err = models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
				return e.NewError500(e.Update_course_failed, err.Error())
			}

			socketMsgData = map[string]any{
				"course_id":        course.ID,
				"status":           models.AIStatusPending,
				"course_slug":      course.Slug,
				"course_name":      course.Name,
				"update_at":        course.UpdateAt,
				"thumbnail_status": models.AIStatusCompleted,
			}

			if len(thumbnailGeneratedIDs) == 0 {
				socketMsgData["thumbnail_status"] = models.AIStatusFailed
			}
		}

		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   aiCourse.GeneratedThumbnailIDs,
			RequestType:  models.FileModelName,
			UserID:       aiCourse.UserID,
			EntityID:     course.Cuid,
			EntityType:   models.CourseModelName,
			GenerateID:   aiCourse.ID,
			GenerateType: aiCourse.OfferType,
			Step:         aiCourse.CurrentStep,
			Status:       models.AIStatusCompleted,
			Cost:         response.Metadata.Cost,
			Response:     historyResp,
			Error:        aiCourse.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiCourse.OfferID,
		}); aErr != nil {
			return aErr
		}

	}
	s.sendAICourseWebsocketMsgToUser(aiCourse, course, socketMsgData)

	return nil
}

func (s *AICourseService) handleGenerateOutline(aiCourse *models.AICourse, course *models.Course) *e.AppError {
	models.AppSchema = aiCourse.OrgSchema
	httpResponse, err := ai.Course.GetCourseOutline(aiCourse.OfferID, setting.ExternalServiceSetting.XAPIKey)
	if err != nil {
		return s.handleCourseFailGeneration(aiCourse, course, util.NewT(err), util.NewInt(e.AICourseGenerate_get_offer_data_failed))
	}

	response := httpResponse.Data
	aiStatus := AIHistory.GetCourseStatusByAIStatus(response.Status)

	if aiStatus == models.AIStatusFailed || response.Metadata == nil {
		return s.handleCourseFailGeneration(aiCourse, course, util.NewT(errors.New(e.GetMsg(e.AICourseGenerate_response_with_status_failed))), util.NewInt(e.AICourseGenerate_response_with_status_failed))
	}

	if aiStatus == models.AIStatusPending {
		aiCourse.Status = models.AIStatusPending
		if _, appErr := s.Update(aiCourse); appErr != nil {
			return appErr
		}
		return nil
	}

	if aiStatus == models.AIStatusCompleted {
		// update ai_course
		aiCourse.TotalCost = response.Metadata.Cost
		aiCourse.Status = models.AIStatusCompleted
		aiCourse.Error = nil

		// update course
		course.Name = response.Metadata.CourseTitle
		course.Description = response.Metadata.CourseDescription
		course.ShortDesc = response.Metadata.CourseDescription
		course.AIGenerateStatus = models.AIStatusCompleted

		if aErr := s.createCourseOutlineFromAIOutline(aiCourse, course, response.Metadata.Sections); aErr != nil {
			return aErr
		}

		// update course and ai course
		if _, appErr := s.Update(aiCourse); appErr != nil {
			return appErr
		}
		if err = models.Repository.Course(context.TODO()).Update(course, nil); err != nil {
			return e.NewError500(e.Update_course_failed, err.Error())
		}

		if aErr := Course.UpdateStats(course, true); err != nil {
			return aErr
		}

		historyResp, err := util.StructToMap(response)
		if err != nil {
			return e.NewError500(e.Json_encode_error, err.Error())
		}
		if _, aErr := AIHistory.FindAndCreateHistory(&dto.CreateAIHistoryParams{
			RequestIDs:   []string{course.Cuid},
			RequestType:  models.CourseModelName,
			UserID:       aiCourse.UserID,
			EntityID:     course.Cuid,
			EntityType:   models.CourseModelName,
			GenerateID:   aiCourse.ID,
			GenerateType: aiCourse.OfferType,
			Step:         aiCourse.CurrentStep,
			Status:       models.AIStatusCompleted,
			Cost:         response.Metadata.Cost,
			Response:     historyResp,
			Error:        aiCourse.Error,
			EndDate:      int64(util.GetCurrentTime()),
			AIOfferID:    aiCourse.OfferID,
		}); aErr != nil {
			return aErr
		}

		s.sendAICourseNotificationToUser(aiCourse, course)

		s.sendAICourseWebsocketMsgToUser(aiCourse, course, map[string]any{
			"course_id":   course.ID,
			"status":      models.AIStatusCompleted,
			"course_slug": course.Slug,
			"course_name": course.Name,
			"update_at":   course.UpdateAt,
		})
	}
	return nil
}

func (s *AICourseService) createCourseOutlineFromAIOutline(aiCourse *models.AICourse, course *models.Course, sectionInfos []*ai.SectionInfo) *e.AppError {
	// insert section, lesson, content, quiz
	sections := []*models.Section{}
	lessons := []*models.Section{}
	lessonContents := []*models.LessonContent{}
	quizContents := []*models.LessonContent{}

	for sectionIdx, section := range sectionInfos {
		sectionID := util.GenerateId()
		sections = append(sections, &models.Section{
			Model:    models.Model{ID: sectionID},
			CourseID: course.ID,
			UserID:   aiCourse.UserID,
			Title:    section.SectionName,
			Note:     "",
			Order:    sectionIdx,
			Status:   models.SectionStatusDraft,
			ParentID: "",
			UID:      util.GenerateId(),
		})

		for lessonIdx, lesson := range section.Lessons {
			lessonID := util.GenerateId()
			lessons = append(lessons, &models.Section{
				Model:    models.Model{ID: lessonID},
				CourseID: course.ID,
				UserID:   aiCourse.UserID,
				Title:    lesson.LessonName,
				Note:     "",
				Order:    lessonIdx,
				Status:   models.SectionStatusDraft,
				ParentID: sectionID,
				UID:      util.GenerateId(),
			})
			s.addLessonContents(lesson, &lessonContents, course, aiCourse, sectionID, lessonID, &quizContents)
		}
	}

	// update stats
	if err := models.Repository.Section.CreateMany(sections, nil); err != nil {
		return e.NewError500(e.Section_create_failed, err.Error())
	}
	if err := models.Repository.Section.CreateMany(lessons, nil); err != nil {
		return e.NewError500(e.Lesson_create_failed, err.Error())
	}
	if err := models.Repository.LessonContent(context.TODO()).CreateMany(lessonContents, nil); err != nil {
		return e.NewError500(e.Lesson_create_failed, err.Error())
	}
	// in case quiz included
	if len(quizContents) > 0 {
		if aErr := s.offerQuizCourse(aiCourse, course, quizContents); aErr != nil {
			return aErr
		}
	}
	return nil
}

func (s *AICourseService) sendAICourseNotificationToUser(aiCourse *models.AICourse, course *models.Course) {
	code := communicationdto.CodeCourseAIGenerateSuccess
	if aiCourse.Status != models.AIStatusCompleted {
		code = communicationdto.CodeCourseAIGenerateFail
	}
	// Push notification to course author
	org, err := models.Repository.Organization.FindByID(aiCourse.OrgID, nil)
	if err == nil {
		if err := communication.Notification.PushNotification(&communicationdto.PushNotificationRequest{
			Code:       communicationdto.NotificationCode(code),
			EntityID:   course.ID,
			EntityType: communicationdto.CourseEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{course.UserID},
				},
			},
			Props: s.makeNotificationPropsForCourse(org, course, aiCourse).IntoComm(),
		}); err != nil {
			log.Errorf("Push notification after generating AI course failed error: %v", err)
		}
	} else {
		log.Errorf("Get organization to push notifications error: %v", err)
	}
}

func (s *AICourseService) sendAICourseWebsocketMsgToUser(aiCourse *models.AICourse, course *models.Course, data map[string]any) {
	// Push websocket to update course status
	userRoles, err := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		RoleIDIn: []string{models.PartnerRoleType},
		OrgID:    util.NewString(aiCourse.OrgID),
	}, nil)
	if err == nil {
		userIDs := lo.Map(userRoles, func(item *models.UserRoleOrg, _ int) string {
			return item.UserID
		})
		userIDs = append(userIDs, aiCourse.UserID)
		userIDs = lo.Uniq(userIDs)
		if err := communication.Websocket.SendMsgToUserWebSocket(&communicationdto.WebsocketMessageRequest{
			Event: communicationdto.WebsocketEventAICourseStatus,
			Data:  data,
			Broadcast: communicationdto.WebsocketBroadcastParams{
				UserIDs: userIDs,
			},
		}); err != nil {
			log.Errorf("Push websocket to course author for sync status error: %v", err)
		}
	} else {
		log.Errorf("Get list users to sync course status error: %v", err)
	}
}

func (s *AICourseService) generateSlug(title string, courseCuid string, orgSchema string) (string, *e.AppError) {
	slug := ""
	// handle slug
	if _, err := lo.Attempt(generateCourseSlugMaxAttempts, func(_ int) error {
		slugGenerated, err := util.Slugify(title, 5)
		if err != nil {
			return err
		}

		existingCourse, err := models.Repository.Course(context.TODO()).FindOne(&models.CourseQuery{
			Slug:   &slugGenerated,
			CuidNe: &courseCuid, // Versions of a course can have the same slug
		}, nil)
		if err != nil && !models.IsRecordNotFound(err) {
			return err
		}

		if existingCourse != nil {
			return fmt.Errorf("course slug already exists")
		}

		slug = slugGenerated
		return nil
	}); err != nil {
		return "", e.NewError500(e.Create_course_failed, err.Error())
	}

	return slug, nil
}
