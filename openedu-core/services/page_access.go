package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *PageAccessService) Create(data *dto.PageAccessRequest, org *models.Organization) (*models.PageAccess, *e.AppError) {
	pageAccess := models.PageAccess{
		Role:   data.Role,
		Entity: data.Entity,
		Action: data.Action,
		Allow:  data.Allow,
		OrgID:  org.ID,
	}

	if err := models.Repository.PageAccess.Create(&pageAccess, nil); err != nil {
		return nil, e.NewError500(e.Create_page_access_fail, err.Error())
	}

	return &pageAccess, nil
}

func (s *PageAccessService) CreateOrUpdate(data *dto.BulkCreatePageAccessRequest, org *models.Organization) ([]*models.PageAccess, []*dto.PageAccessRequest, *e.AppError) {
	if data == nil || len(data.PageAccess) == 0 {
		return nil, nil, e.NewError400(e.INVALID_PARAMS, "page_access required!")
	}
	permissionFromDB, fErr := models.Repository.PageAccess.FindAll()
	if fErr != nil {
		return nil, nil, e.NewError500(e.Page_access_find_all_failed, "CreateOrUpdate: "+fErr.Error())
	}

	allConfig, _, cErr := PageConfig.FindPage(&models.PageConfigQuery{}, &models.FindPageOptions{PerPage: util.PerPageMax})
	if cErr != nil {
		return nil, nil, e.NewError500(e.Page_config_find_page_failed, "CreateOrUpdate: "+cErr.Msg)
	}
	mapEntities := map[string][]string{}
	lo.ForEach(allConfig, func(item *models.PageConfig, _ int) {
		if item.Type == models.PageConfigTypeEntity {
			mapEntities[item.ID] = item.Actions
		}
	})

	var toCreate []*models.PageAccess
	var toUpdate []*models.PageAccess
	var failedList []*dto.PageAccessRequest

	for _, per := range data.PageAccess {
		actionByEntities := mapEntities[per.Entity]
		if actionByEntities != nil && lo.Contains(actionByEntities, per.Action) {
			p, ok := lo.Find(permissionFromDB, func(item *models.PageAccess) bool {
				return item.Role == per.Role &&
					item.Entity == per.Entity &&
					item.Action == per.Action &&
					item.OrgID == per.OrgID
			})
			if ok {
				p.Allow = per.Allow
				toUpdate = append(toUpdate, p)
			} else {
				toCreate = append(toCreate, &models.PageAccess{
					Role:   per.Role,
					Entity: per.Entity,
					Action: per.Action,
					Allow:  per.Allow,
					OrgID:  per.OrgID,
				})
			}
		} else {
			failedList = append(failedList, &per)
		}
	}

	if len(toCreate) > 0 {
		if err := models.Repository.PageAccess.CreateMany(toCreate, nil); err != nil {
			return nil, nil, e.NewError500(e.Create_page_access_fail, "Create page access failed: "+err.Error())
		}
	}

	if len(toUpdate) > 0 {
		if err := models.Repository.PageAccess.UpdateMany(toUpdate, nil); err != nil {
			return nil, nil, e.NewError500(e.Create_page_access_fail, "Create page access failed: "+err.Error())
		}
	}
	toCreate = append(toCreate, toUpdate...)
	return toCreate, failedList, nil
}

func (s *PageAccessService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.PageAccess, *e.AppError) {
	query := &models.PageAccessQuery{ID: util.NewString(id)}

	if pageAccess, err := models.Repository.PageAccess.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Page_access_not_found, err.Error())
		}
		return nil, e.NewError500(e.Page_access_find_one_failed, err.Error())
	} else {
		return pageAccess, nil
	}
}

func (s *PageAccessService) FindPage(query *models.PageAccessQuery, options *models.FindPageOptions) ([]*models.PageAccess, *models.Pagination, *e.AppError) {
	if pageAccess, pagination, err := models.Repository.PageAccess.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_page_access_failed, err.Error())
	} else {
		return pageAccess, pagination, nil
	}
}

func (s *PageAccessService) FindAll() ([]*models.PageAccess, *e.AppError) {
	if pageAccesses, err := models.Repository.PageAccess.FindAll(); err != nil {
		return nil, e.NewError500(e.Page_access_find_all_failed, "failed: "+err.Error())
	} else {
		return pageAccesses, nil
	}
}

/*
* @param user
* @param orgID: orgID = "" then query for sysadmin site
 */
func (s *PageAccessService) GetUserPermission(user *models.User, orgID string) ([]*models.PageAccess, *e.AppError) {
	// get user role
	userRoles, gErr := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if gErr != nil {
		return nil, e.NewError500(e.Find_user_role_failed, "find user role failed: "+gErr.Error())
	}

	roles := lo.Map(userRoles, func(item *models.UserRoleOrg, _ int) string {
		return item.RoleID
	})

	pageAccesses, err := models.Repository.PageAccess.FindAll()
	if err != nil {
		return nil, e.NewError500(e.Page_access_find_all_failed, "failed: "+err.Error())
	}

	var userPermissions []*models.PageAccess
	for _, role := range roles {
		permissions := lo.Filter(pageAccesses, func(item *models.PageAccess, _ int) bool {
			return item.Allow == true && item.Role == role && item.OrgID == orgID
		})
		userPermissions = append(userPermissions, permissions...)
	}

	return userPermissions, nil
}

func (s *PageAccessService) FindOne(query *models.PageAccessQuery, options *models.FindOneOptions) (*models.PageAccess, *e.AppError) {
	if pageAccess, err := models.Repository.PageAccess.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Page_access_find_one_failed, err.Error())
		}
		return nil, e.NewError500(e.Page_access_find_one_failed, err.Error())
	} else {
		return pageAccess, nil
	}
}
