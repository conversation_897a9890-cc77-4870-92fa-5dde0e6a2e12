package services

import (
	"errors"
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"sort"
)

func getHighestRateCommission(comms []*models.Commission) *models.Commission {
	// Get commission has highest rate
	sort.Slice(comms[:], func(i, j int) bool {
		return comms[i].Ref1Rate > comms[j].Ref1Rate
	})
	if len(comms) > 0 {
		return comms[0]
	}

	return nil
}

func upsertBonusCommission(commission *models.Commission, request []*dto.CommissionBonusRequest, user *models.User, org *models.Organization) *e.AppError {
	if request == nil || len(request) <= 0 {
		return nil
	}
	bonuses, err := models.Repository.Commission.FindMany(&models.CommissionQuery{
		ParentID: util.NewString(commission.ID),
	}, nil)
	if err != nil {
		return e.NewError500(e.Commission_bonus_create_failed, "upsertBonusCommission find bonuses: "+err.Error())
	}
	for _, req := range request {
		create := true
		if req.ID != nil {
			bonus, ok := lo.Find(bonuses, func(item *models.Commission) bool {
				return item.ID == *req.ID
			})
			if ok {
				create = false
				bonus.Ref1Rate = req.Ref1Rate
				bonus.Qty1 = req.Qty1
				bonus.Enable = req.Enable
				bonus.Order = req.Order
				if uErr := models.Repository.Commission.Update(bonus, nil); uErr != nil {
					return e.NewError500(e.Commission_bonus_create_failed, "upsertBonusCommission update: "+err.Error())
				}
			}
		}
		if create {
			bonusComm := &models.Commission{
				UserID:        user.ID,
				OrgID:         org.ID,
				CampaignID:    commission.CampaignID,
				Ref1Rate:      req.Ref1Rate,
				Qty1:          req.Qty1,
				Enable:        req.Enable,
				Type:          req.Type,
				ParentID:      commission.ID,
				ReferrerIDs:   commission.ReferrerIDs,
				ReferrerTypes: commission.ReferrerTypes,
				Order:         req.Order,
			}
			if bErr := models.Repository.Commission.Create(bonusComm, nil); bErr != nil {
				return e.NewError500(e.Commission_bonus_create_failed, "failed: "+bErr.Error())
			}
		}
	}

	return nil
}

func (s *CommissionService) Create(campaign *models.AffiliateCampaign, request *dto.CommissionRequest) (*models.Commission, *e.AppError) {
	commission := &models.Commission{
		UserID:        request.User.ID,
		OrgID:         request.Org.ID,
		CampaignID:    campaign.ID,
		Ref1Rate:      request.Ref1Rate,
		Qty1:          request.Qty1,
		Ref2Rate:      request.Ref2Rate,
		Qty2:          request.Qty2,
		Ref3Rate:      request.Ref3Rate,
		Qty3:          request.Qty3,
		ReferrerTypes: request.ReferrerTypes,
		ReferrerIDs:   request.ReferrerIDs,
		Enable:        request.Enable,
		Type:          request.Type,
		IsBaseRate:    request.IsBaseRate,
		Order:         request.Order,
	}
	if err := models.Repository.Commission.Create(commission, nil); err != nil {
		return nil, e.NewError500(e.Commission_create_failed, "failed: "+err.Error())
	}

	if bErr := upsertBonusCommission(commission, request.Bonuses, request.User, request.Org); bErr != nil {
		return nil, bErr
	}
	return commission, nil
}

func (s *CommissionService) Update(commission *models.Commission, request *dto.CommissionRequest) (*models.Commission, *e.AppError) {
	if request.ReferrerIDs == nil {
		request.ReferrerIDs = models.StringArray{}
	}
	if request.ReferrerTypes == nil {
		request.ReferrerTypes = models.StringArray{}
	}
	commission.Ref1Rate = request.Ref1Rate
	commission.Qty1 = request.Qty1
	commission.Ref2Rate = request.Ref2Rate
	commission.Qty2 = request.Qty2
	commission.Ref3Rate = request.Ref3Rate
	commission.Qty3 = request.Qty3
	commission.ReferrerTypes = request.ReferrerTypes
	commission.ReferrerIDs = request.ReferrerIDs
	commission.Enable = request.Enable
	commission.Type = request.Type
	commission.IsBaseRate = request.IsBaseRate
	commission.Order = request.Order
	if err := models.Repository.Commission.Update(commission, nil); err != nil {
		return nil, e.NewError500(e.Commission_update_failed, "failed: "+err.Error())
	}

	if bErr := upsertBonusCommission(commission, request.Bonuses, request.User, request.Org); bErr != nil {
		return nil, bErr
	}
	return commission, nil
}

func (s *CommissionService) Delete(request *dto.CommissionDeleteRequest) *e.AppError {
	if len(request.IDs) > 0 {
		for _, id := range request.IDs {
			if uErr := models.Repository.Commission.Delete(id, nil); uErr != nil {
				return e.NewError500(e.Commission_delete_failed, fmt.Sprintf("remove %s: %s", id, uErr.Error()))
			}
		}
		return nil
	}
	return nil
}

func (s *CommissionService) FindPage(query *models.CommissionQuery, options *models.FindPageOptions) ([]*models.Commission, *models.Pagination, *e.AppError) {
	if commissions, pagination, err := models.Repository.Commission.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Commission_find_failed, "find page failed: "+err.Error())
	} else {
		return commissions, pagination, nil
	}
}

func (s *CommissionService) FindMany(query *models.CommissionQuery, options *models.FindManyOptions) ([]*models.Commission, *e.AppError) {
	if commissions, err := models.Repository.Commission.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Commission_find_failed, "find all failed: "+err.Error())
	} else {
		return commissions, nil
	}
}

func (s *CommissionService) FindOne(query *models.CommissionQuery, options *models.FindOneOptions) (*models.Commission, *e.AppError) {
	if commission, err := models.Repository.Commission.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Commission_not_found, err.Error())
		}
		return nil, e.NewError500(e.Commission_find_failed, err.Error())
	} else {
		// get all bonus
		bonuses, bErr := models.Repository.Commission.FindMany(&models.CommissionQuery{
			ParentID: util.NewString(commission.ID),
		}, nil)
		if bErr != nil {
			return nil, e.NewError500(e.Commission_find_failed, "get bonus failed"+bErr.Error())
		}
		commission.Bonuses = bonuses
		return commission, nil
	}
}

func (s *CommissionService) GetApplicableCommission(user *models.User, campaign *models.AffiliateCampaign) ([]*models.Commission, *e.AppError) {
	// get enabled commission by campaign
	commissions, comErr := Commission.FindMany(&models.CommissionQuery{
		CampaignID:   util.NewString(campaign.ID),
		Enable:       util.NewBool(true),
		ParentIDNull: util.NewBool(true),
	}, nil)
	if comErr != nil {
		return nil, e.NewError500(e.Referral_link_create_failed, fmt.Sprintf("get commission by campaign %s failed %s", campaign.ID, comErr.Msg))
	}

	bonueses, comErr := Commission.FindMany(&models.CommissionQuery{
		CampaignID:   util.NewString(campaign.ID),
		Enable:       util.NewBool(true),
		ParentIDNull: util.NewBool(false),
	}, nil)
	if comErr != nil {
		return nil, e.NewError500(e.Referral_link_create_failed, fmt.Sprintf("get commission by campaign %s failed %s", campaign.ID, comErr.Msg))
	}

	// IF user is a KOL of this campaign
	referrers, refErr := models.Repository.Referrer.FindMany(
		&models.ReferrerQuery{
			CampaignID: util.NewString(campaign.ID),
			Enable:     util.NewBool(true),
			UserID:     util.NewString(user.ID),
		}, nil)

	if refErr != nil && !errors.Is(refErr, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Referral_link_create_failed, fmt.Sprintf("get referrers %s by campaign %s failed %s", user.ID, campaign.ID, refErr.Error()))
	}

	// agency, kol, specific, base rate, purchased

	var canApplyCommissions []*models.Commission
	if referrers != nil && len(referrers) > 0 {
		agencyComms := lo.Filter(commissions, func(c *models.Commission, _ int) bool {
			return lo.Contains(c.ReferrerTypes, string(models.ReferrerTypeAgency))
		})
		kolComms := lo.Filter(commissions, func(c *models.Commission, _ int) bool {
			return lo.Contains(c.ReferrerTypes, string(models.ReferrerTypeKOL))
		})
		purchasedComms := lo.Filter(commissions, func(c *models.Commission, _ int) bool {
			return lo.Contains(c.ReferrerTypes, string(models.ReferrerTypePurchasedUser))
		})
		// can phai check type cua referrer
		for _, referrer := range referrers {
			specComms := lo.Filter(commissions, func(c *models.Commission, _ int) bool {
				return lo.Contains(c.ReferrerIDs, referrer.ID)
			})

			if len(specComms) > 0 {
				canApplyCommissions = append(canApplyCommissions, specComms...)
			}

			if referrer.Type == models.ReferrerTypePurchasedUser && len(purchasedComms) > 0 {
				canApplyCommissions = append(canApplyCommissions, purchasedComms...)
			}

			if referrer.Type == models.ReferrerTypeAgency && len(agencyComms) > 0 {
				canApplyCommissions = append(canApplyCommissions, agencyComms...)
			}

			if referrer.Type == models.ReferrerTypeKOL && len(kolComms) > 0 {
				canApplyCommissions = append(canApplyCommissions, kolComms...)
			}
		}

	}

	normalUserComms := lo.Filter(commissions, func(c *models.Commission, _ int) bool {
		return lo.Contains(c.ReferrerTypes, string(models.ReferrerTypeUser))
	})

	if len(normalUserComms) > 0 {
		canApplyCommissions = append(canApplyCommissions, normalUserComms...)
	}

	if len(canApplyCommissions) > 0 {
		lo.ForEach(canApplyCommissions, func(item *models.Commission, _ int) {
			bns := lo.Filter(bonueses, func(b *models.Commission, _ int) bool {
				return b.ParentID == item.ID
			})
			item.Bonuses = bns
		})
	}

	return canApplyCommissions, nil
}

func (s *CommissionService) FindUserCommissionByCampaign(user *models.User, campaign *models.AffiliateCampaign) *e.AppError {
	applicableCommissions, comErr := s.GetApplicableCommission(user, campaign)
	if comErr != nil {
		return comErr
	}

	links, linkErr := ReferralLink.GetLinkByUserAndCampaign(user, campaign)
	if linkErr != nil {
		return linkErr
	}

	if links != nil && len(links) > 0 {
		lo.ForEach(applicableCommissions, func(comm *models.Commission, _ int) {
			link, ok := lo.Find(links, func(item *models.ReferralLink) bool {
				return item.CommissionID == comm.ID && item.IsExtend == false
			})
			if ok {
				comm.ReferralLinkByUser = link
			}
		})
	}
	campaign.UserCanApplicableComms = applicableCommissions

	purchased, refErr := models.Repository.Referrer.FindOne(
		&models.ReferrerQuery{
			CampaignID: util.NewString(campaign.ID),
			Enable:     util.NewBool(true),
			UserID:     util.NewString(user.ID),
			Type:       util.NewT(models.ReferrerTypePurchasedUser),
		}, &models.FindOneOptions{Preloads: []string{"PurchasedLink"}})
	if refErr != nil && !errors.Is(refErr, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Referral_link_create_failed, fmt.Sprintf("get referrers %s by campaign %s failed %s", user.ID, campaign.ID, refErr.Error()))
	}

	if purchased != nil {
		campaign.UserBoughtFrom = purchased.PurchasedLink
		extendLink, ok := lo.Find(links, func(item *models.ReferralLink) bool {
			return item.IsExtend == true
		})
		if ok {
			campaign.UserExtendLink = extendLink
		}
	}

	return nil
}

func (s *CommissionService) GetCommissionByCampaign(campaign *models.AffiliateCampaign) ([]*models.Commission, *e.AppError) {
	commissions, comErr := s.FindMany(&models.CommissionQuery{
		CampaignID: util.NewString(campaign.ID),
		Enable:     util.NewBool(true),
	}, nil)

	if comErr != nil {
		return nil, comErr
	}

	parents := lo.Filter(commissions, func(item *models.Commission, _ int) bool {
		return item.ParentID == ""
	})

	lo.ForEach(parents, func(com *models.Commission, _ int) {
		child := lo.Filter(commissions, func(item *models.Commission, _ int) bool {
			return item.ParentID == com.ID
		})
		com.Bonuses = child
	})

	return parents, nil
}
