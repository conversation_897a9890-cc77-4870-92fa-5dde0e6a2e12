package services

import (
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *BlogService) CreateManyWithSchema(data map[string][]*models.Blog) *e.AppError {
	for _, blogs := range data {
		if err := models.Repository.Blog.CreateMany(blogs, nil); err != nil {
			return e.NewError500(e.Blog_create_many_with_schema_failed, err.Error())
		}
	}
	return nil
}

func (s *BlogService) Create(org *models.Organization, user *models.User, data *dto.CreateBlogRequest) (*models.Blog, *e.AppError) {
	slug, err := util.Slugify(data.Title, 5)
	if err != nil {
		return nil, e.<PERSON>rror500(e.Generate_random_failed, err.Error())
	}

	if data.ScheduleAt == 0 {
		data.ScheduleAt = time.Now().Unix()
	}

	categoryIDs := lo.Map(data.CategoryIDs, func(item dto.DefaultEntityRequest, _ int) string {
		return item.ID
	})

	hashTagNames := lo.Map(data.HashTagNames, func(item dto.BlogHashTagRequest, _ int) string {
		return item.Name
	})

	needClearCache := false

	blog := models.Blog{
		OrgID:            org.ID,
		AuthorID:         user.ID,
		BannerID:         data.BannerID,
		ImageDescription: data.ImageDescription,
		Title:            data.Title,
		Slug:             slug,
		Content:          data.Content,
		TimeRead:         data.TimeRead,
		Description:      data.Description,
		ScheduleAt:       data.ScheduleAt,
		Cuid:             util.GenerateId(),
		Version:          1,
		Props:            models.BlogProps{PreviousVersion: 0},
		BlogType:         data.BlogType,
		IsOriginDraft:    true,
		IsAIGenerated:    data.IsAIGenerated,
		Locale:           data.Locale,
	}

	if data.AIBlogID != nil {
		blog.AIBlogID = *data.AIBlogID
	}

	if !data.IsPublish {
		if err := models.Repository.Blog.Create(&blog, nil); err != nil {
			return nil, e.NewError500(e.Create_blog_failed, err.Error())
		}

		// Add category relation
		createCategoryData := dto.CreateCategoryRelationParams{
			CategoryIDs: categoryIDs,
			RelatedID:   blog.Cuid,
			RelatedType: models.BlogModelName,
			Field:       models.CategoriesField,
		}
		if err := CategoryRelation.Upsert(&createCategoryData, needClearCache); err != nil {
			return nil, err
		}

		if len(hashTagNames) > 0 {
			createHashTagDatas, pErr := HashtagRelation.BuildHashtagEntities(hashTagNames, org.ID, blog.Cuid, models.BlogModelName)
			if pErr != nil {
				return nil, pErr
			}

			if len(createHashTagDatas) > 0 {
				if htErr := HashtagRelation.CreateMany(createHashTagDatas); htErr != nil {
					return nil, htErr
				}
			}
		}

		if data.SendAsLetter {
			//TODO handle send email for follower
		}

		return &blog, nil
	}

	// Want to publish

	needApproval := false
	isPassApproval := false
	isBlogPerson := false
	// Indicate which status for blog between publish org or publish personal
	switch blog.BlogType {
	case models.BlogTypeOrg:
		if user.IsOrgWriter(org.ID) {
			blog.Status = models.BlogStatusReviewing
		} else {
			blog.Status = models.BlogStatusPublish
			blog.PubDate = int(time.Now().UnixMilli())
			isPassApproval = true
		}
		needApproval = true
	case models.BlogTypePersonal:
		blog.Status = models.BlogStatusPublish
		blog.PubDate = int(time.Now().UnixMilli())
		isBlogPerson = true
	}

	if err := models.Repository.Blog.Create(&blog, nil); err != nil {
		return nil, e.NewError500(e.Create_blog_failed, err.Error())
	}

	createCategoryData := dto.CreateCategoryRelationParams{
		CategoryIDs: categoryIDs,
		RelatedID:   blog.Cuid,
		RelatedType: models.BlogModelName,
		Field:       models.CategoriesField,
	}

	needClearCache = data.BlogType == models.BlogTypeOrg && isPassApproval

	if err := CategoryRelation.Upsert(&createCategoryData, needClearCache); err != nil {
		return nil, err
	}

	if len(hashTagNames) > 0 {
		createHashTagDatas, pErr := HashtagRelation.BuildHashtagEntities(hashTagNames, org.ID, blog.Cuid, models.BlogModelName)
		if pErr != nil {
			return nil, pErr
		}

		if htErr := HashtagRelation.CreateMany(createHashTagDatas); htErr != nil {
			return nil, htErr
		}
	}

	// writer and contributor must be approved
	if needApproval {
		approvalData := &dto.PublishBlogRequest{
			Blog:      &blog,
			Org:       org,
			Requester: user,
		}

		_, aErr := s.RequestPublishBlog(org, approvalData, isPassApproval)
		if aErr != nil {
			return nil, aErr
		}
	}

	if isBlogPerson {
		s.HandlePublishBlogTypePersonal(user, org, &blog)
	}

	//Todo Handle category and hashTag

	if data.SendAsLetter {
		//TODO handle send email for follower
	}

	return &blog, nil
}

func (s *BlogService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.Blog, *e.AppError) {
	query := &models.BlogQuery{
		ID:             util.NewString(id),
		IncludeDeleted: lo.If(includeDeleted, util.NewBool(true)).Else(nil),
	}

	if blog, err := models.Repository.Blog.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Blog_not_found, err.Error())
		}
		return nil, e.NewError500(e.Blog_find_one_failed, err.Error())

	} else {
		return blog, nil
	}
}

func (s *BlogService) FindOne(query *models.BlogQuery, options *models.FindOneOptions) (*models.Blog, *e.AppError) {
	if order, err := models.Repository.Blog.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Blog_not_found, err.Error())
		}
		return nil, e.NewError500(e.Blog_find_one_failed, err.Error())
	} else {
		return order, nil
	}
}

func (s *BlogService) FindPage(query *models.BlogQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError) {
	if blogs, pagination, err := models.Repository.Blog.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_blog_failed, err.Error())
	} else {
		return blogs, pagination, nil
	}
}

func (s *BlogService) Update(b *models.Blog, data *dto.UpdateBlogRequest, org *models.Organization) *e.AppError {
	b.Description = data.Description
	b.ImageDescription = data.ImageDescription
	b.Title = data.Title
	b.BannerID = data.BannerID
	b.TimeRead = data.TimeRead
	b.Content = data.Content
	b.JsonContent = data.JsonContent
	b.ScheduleAt = data.ScheduleAt
	b.IsPin = data.IsPin
	b.Locale = data.Locale
	b.AIBlogID = data.AIBlogID

	// Handle Category
	existCateRelation, err := CategoryRelation.FindMany(&models.CategoryRelationQuery{
		RelatedID:   util.NewString(b.Cuid),
		RelatedType: util.NewT(models.BlogModelName),
	}, &models.FindManyOptions{})
	if err != nil {
		return err
	}

	existCateRelationIDs := lo.Map(existCateRelation, func(item *models.CategoryRelation, _ int) string {
		return item.CategoryID
	})

	categoryIDs := lo.Map(data.CategoryIDs, func(item dto.DefaultEntityRequest, _ int) string {
		return item.ID
	})

	toAddCateIDs, toRemoveCateIDs := lo.Difference(categoryIDs, existCateRelationIDs)

	if len(toAddCateIDs) > 0 {
		cateRelationData := &dto.CreateCategoryRelationParams{
			CategoryIDs: toAddCateIDs,
			RelatedID:   b.Cuid,
			RelatedType: models.BlogModelName,
			Field:       models.CategoriesField,
		}
		if updateCateErr := CategoryRelation.Upsert(cateRelationData, false); updateCateErr != nil {
			return updateCateErr
		}
	}

	if len(toRemoveCateIDs) > 0 {
		if removeErr := CategoryRelation.DeleteMany(
			&models.CategoryRelationQuery{
				CategoryIDIn: toRemoveCateIDs,
				RelatedID:    util.NewString(b.Cuid),
				RelatedType:  util.NewT(models.BlogModelName),
			}); removeErr != nil {
			return removeErr
		}
	}

	// Handle hashtag
	hashTagquery := &models.HashtagRelationQuery{
		RelatedID:   util.NewString(b.Cuid),
		RelatedType: util.NewT(models.BlogModelName),
	}

	existHashTagRelation, err := HashtagRelation.FindMany(hashTagquery, &models.FindManyOptions{})
	if err != nil {
		return err
	}

	existHashTagIDs := lo.Map(existHashTagRelation, func(item *models.HashtagRelation, _ int) string {
		return item.HashtagID
	})

	// build update hashtag data
	hashTagNames := lo.Map(data.HashTagID, func(item dto.BlogHashTagRequest, _ int) string {
		return item.Name
	})

	updateHashTagDatas, pErr := HashtagRelation.BuildHashtagEntities(hashTagNames, org.ID, b.Cuid, models.BlogModelName)
	if pErr != nil {
		return pErr
	}

	updateHashTagIDs := lo.Map(updateHashTagDatas, func(item *dto.CreateHashtagRelationParams, _ int) string {
		return item.HashtagID
	})

	toAddHashTagIDs, toRemoveHashTagIDs := lo.Difference(updateHashTagIDs, existHashTagIDs)

	if len(toAddHashTagIDs) > 0 {
		hashTagData := lo.Map(toAddHashTagIDs, func(item string, _ int) *dto.CreateHashtagRelationParams {
			return &dto.CreateHashtagRelationParams{
				HashtagID:   item,
				RelatedID:   b.Cuid,
				RelatedType: models.BlogModelName,
			}
		})

		if htErr := HashtagRelation.CreateMany(hashTagData); htErr != nil {
			return htErr
		}
	}

	if len(toRemoveHashTagIDs) > 0 {
		if removeErr := HashtagRelation.DeleteMany(&models.HashtagRelationQuery{
			HashtagIDIn: toRemoveHashTagIDs,
			RelatedID:   util.NewString(b.Cuid),
			RelatedType: util.NewT(models.BlogModelName),
		}); removeErr != nil {
			return removeErr
		}

	}

	if err := models.Repository.Blog.Update(b, nil); err != nil {
		return e.NewError500(e.Update_blog_failed, err.Error())
	}

	return nil
}

func (s *BlogService) TogglePin(b *models.Blog, data *dto.PinBlogRequest) *e.AppError {
	b.IsPin = data.Pin

	if err := models.Repository.Blog.Update(b, nil); err != nil {
		return e.NewError500(e.Update_blog_failed, err.Error())
	}

	if b.BlogType == models.BlogTypeOrg {
		publishBlog, err := PublishBlog.FindOne(&models.PublishBlogQuery{BlogCuid: &b.Cuid}, &models.FindOneOptions{})
		if err != nil {
			return err
		}

		publishBlog.IsPin = b.IsPin
		if updateErr := PublishBlog.Update(publishBlog); updateErr != nil {
			return updateErr
		}
	}

	return nil
}

func (s *BlogService) Delete(b *models.Blog) *e.AppError {
	if err := models.Repository.Blog.Delete(b.ID, nil); err != nil {
		return e.NewError500(e.Delete_blog_failed, err.Error())
	}

	return nil
}

func (s *BlogService) CanUpdateBlog(b *models.Blog, user *models.User, org *models.Organization) bool {
	return (b.AuthorID == user.ID && user.IsOrgWriter(org.ID)) || user.IsSysAdmin() || user.IsOrgAdmin(org.ID) || user.IsOrgEditor(org.ID)
}

func (s *BlogService) DuplicateBlog(blog *models.Blog) (*models.Blog, *e.AppError) {
	newBlog := models.Blog{
		AuthorID:     blog.AuthorID,
		BannerID:     blog.BannerID,
		Title:        blog.Title,
		Slug:         blog.Slug,
		Content:      blog.Content,
		VoteCount:    blog.VoteCount,
		CommentCount: blog.CommentCount,
		TimeRead:     blog.TimeRead,
		Status:       blog.Status,
		Locale:       blog.Locale,
	}
	if bErr := models.Repository.Blog.Create(&newBlog, nil); bErr != nil {
		return nil, e.NewError500(e.Create_blog_failed, bErr.Error())
	}
	return &newBlog, nil
}

func (s *BlogService) FindMany(query *models.BlogQuery, options *models.FindManyOptions) ([]*models.Blog, *e.AppError) {
	if blogs, err := models.Repository.Blog.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Find_many_blog_failed, err.Error())
	} else {
		return blogs, nil
	}
}

func (s *BlogService) UpdateMany(query *models.BlogQuery, data map[string]interface{}, trans *gorm.DB) *e.AppError {
	if err := models.Repository.Blog.UpdateMany(query, data, trans); err != nil {
		return e.NewError500(e.Update_many_blog_failed, err.Error())
	}
	return nil
}

func (s *BlogService) HandlePublishBlogTypePersonal(user *models.User, org *models.Organization, blog *models.Blog) (*models.Blog, *e.AppError) {
	if blog.Version == blog.Props.PreviousVersion {
		blog.Version++
	}

	newBlog, cloneErr := s.CloneNewVersion(blog, user)
	if cloneErr != nil {
		return nil, cloneErr
	}

	blog.Latest = false
	blog.Status = models.BlogStatusPublish
	blog.PubDate = int(time.Now().UnixMilli())
	if buErr := models.Repository.Blog.Update(blog, nil); buErr != nil {
		return nil, e.NewError500(e.Blog_publish_failed, buErr.Error())
	}

	// if publishErr := PublishBlog.AddPublishBlog(blog, org); publishErr != nil {
	// 	return nil, publishErr
	// }

	// increase total_blogs}
	if iErr := models.Repository.UserSummary.Increase(&models.UserSummaryQuery{
		UserID: &blog.AuthorID,
	}, models.TotalBlogsField, util.BlogUnit, nil); iErr != nil {
		return nil, e.NewError500(e.User_summary_increase_failed, "Increase total blogs count for user error: "+iErr.Error())
	}

	return newBlog, nil

}

func (s *BlogService) RequestPublishBlog(org *models.Organization, request *dto.PublishBlogRequest, isPassApproval bool) (*models.Blog, *e.AppError) {
	blog := request.Blog

	if blog.Status != models.BlogStatusPublish && !isPassApproval {
		pub := &dto.CreateApprovalRequest{
			EntityType: models.BlogModelName,
			EntityID:   blog.ID,
			Type:       models.ApproveTypePublishOrg,
		}
		if _, err := Approval.Create(request.Org, request.Requester, pub); err != nil {
			return nil, err
		}
	}

	if blog.Version == blog.Props.PreviousVersion {
		blog.Version++
	}

	newBlog, cloneErr := s.CloneNewVersion(blog, request.Requester)
	if cloneErr != nil {
		return nil, cloneErr
	}

	if isPassApproval {
		blog.Status = models.BlogStatusPublish
		blog.PubDate = int(time.Now().UnixMilli())
		if pErr := PublishBlog.AddPublishBlog(blog, org); pErr != nil {
			return nil, pErr
		}
		if iErr := models.Repository.UserSummary.Increase(&models.UserSummaryQuery{
			UserID: &blog.AuthorID,
		}, models.TotalBlogsField, util.BlogUnit, nil); iErr != nil {
			return nil, e.NewError500(e.User_summary_increase_failed, "Increase total blogs count for user error: "+iErr.Error())
		}

	} else {
		blog.Status = models.BlogStatusReviewing
	}

	blog.Latest = false
	blog.PubDate = int(time.Now().UnixMilli())
	if buErr := models.Repository.Blog.Update(blog, nil); buErr != nil {
		return nil, e.NewError500(e.Blog_publish_failed, buErr.Error())
	}
	return newBlog, nil

}

func (s *BlogService) CloneNewVersion(blog *models.Blog, user *models.User) (*models.Blog, *e.AppError) {
	newBlog, nbErr := cloneBlog(blog, user)
	if nbErr != nil {
		return nil, nbErr
	}
	return newBlog, nil
}

func cloneBlog(blog *models.Blog, user *models.User) (*models.Blog, *e.AppError) {
	props := blog.Props
	props.PreviousVersion = blog.Version
	props.PreviousID = blog.ID
	newBlog := models.Blog{
		OrgID:            blog.OrgID,
		Cuid:             blog.Cuid,
		Version:          blog.Version,
		Latest:           true,
		AuthorID:         blog.AuthorID,
		BannerID:         blog.BannerID,
		Title:            blog.Title,
		HashTagID:        blog.HashTagID,
		ImageDescription: blog.ImageDescription,
		Slug:             blog.Slug,
		Content:          blog.Content,
		JsonContent:      blog.JsonContent,
		TimeRead:         blog.TimeRead,
		Status:           models.BlogStatusDraft,
		VoteCount:        blog.VoteCount,
		CommentCount:     blog.CommentCount,
		Description:      blog.Description,
		ViewCount:        blog.ViewCount,
		BlogType:         blog.BlogType,
		IsAIGenerated:    blog.IsAIGenerated,
		Props:            props,
		Locale:           blog.Locale,
		AIBlogID:         blog.AIBlogID,
	}

	if bErr := models.Repository.Blog.Create(&newBlog, nil); bErr != nil {
		return nil, e.NewError500(e.Create_blog_failed, bErr.Error())
	}
	return &newBlog, nil
}

func (s *BlogService) PublishBlog(user *models.User, approval *models.Approval) *e.AppError {
	blog, bErr := s.FindByID(approval.EntityID, false, nil)
	if bErr != nil {
		return bErr
	}

	org, oErr := Organization.FindByID(approval.OrgID, false, nil)
	if oErr != nil {
		return e.NewError500(e.Blog_add_publish_failed, "find org failed: "+approval.OrgID)
	}

	blog.PubDate = int(time.Now().UnixMilli())

	blog.Status = models.BlogStatusPublish

	if buErr := models.Repository.Blog.Update(blog, nil); buErr != nil {
		return e.NewError500(e.Update_blog_failed, buErr.Error())
	}

	if pErr := PublishBlog.AddPublishBlog(blog, org); pErr != nil {
		return pErr
	}

	// increase total_blogs
	if iErr := models.Repository.UserSummary.Increase(&models.UserSummaryQuery{
		UserID: &blog.AuthorID,
	}, models.TotalBlogsField, util.BlogUnit, nil); iErr != nil {
		return e.NewError500(e.User_summary_increase_failed, "Increase total blogs count for user error: "+iErr.Error())
	}

	notificationReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeRequestPublishBlogApproved,
		EntityID:   approval.EntityID,
		EntityType: communicationdto.BlogEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{approval.RequesterID},
			},
		},
		Props: s.makeNotificationPropsForBlog(blog, org, user).IntoComm(),
	}
	if err := communication.Notification.PushNotification(notificationReq); err != nil {
		log.Errorf("Push notification to user ID %s after rejecting publish blog ID %s error: %v", approval.RequesterID, blog.ID, err)
	}

	// TODO: Send email to requester

	return nil
}

func (s *BlogService) makeNotificationPropsForBlog(blog *models.Blog, org *models.Organization, user *models.User) models.JSONB {
	return models.JSONB{
		"org_id":     org.ID,
		"org_name":   org.Name,
		"org_domain": org.Domain,
		"blog_id":    blog.ID,
		"blog_cuid":  blog.Cuid,
		"blog_title": blog.Title,
		"blog_slug":  blog.Slug,
		"user_id":    user.ID,
		"username":   user.Username,
	}
}

func (s *BlogService) RejectPublishBlog(user *models.User, approval *models.Approval) *e.AppError {
	blog, cErr := models.Repository.Blog.FindOne(
		&models.BlogQuery{ID: util.NewString(approval.EntityID)},
		nil)
	if cErr != nil {
		return e.NewError400(e.Course_reject_failed, "find course failed: "+cErr.Error())
	}
	blog.PubRejectDate = int(time.Now().UnixMilli())
	blog.Props.RejectOrgReason = approval.Note
	blog.Status = models.BlogStatusRejected

	org, oErr := Organization.FindByID(approval.OrgID, false, nil)
	if oErr != nil {
		return e.NewError500(e.Blog_add_publish_failed, "find org failed: "+approval.OrgID)
	}

	if udErr := models.Repository.Blog.Update(blog, nil); udErr != nil {
		return e.NewError500(e.Blog_reject_failed, "Update blog current version failed: "+udErr.Error())
	}

	notificationReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.CodeRequestPublishBlogRejected,
		EntityID:   approval.EntityID,
		EntityType: communicationdto.BlogEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{approval.RequesterID},
			},
		},
		Props: s.makeNotificationPropsForBlog(blog, org, user).IntoComm(),
	}
	if err := communication.Notification.PushNotification(notificationReq); err != nil {
		log.Errorf("Push notification to user ID %s after rejecting publish blog ID %s error: %v", approval.RequesterID, blog.ID, err)
	}

	// TODO: Send email to requester

	return nil
}

func (s *BlogService) CanApproveOrgBlog(user *models.User, orgID string) bool {
	urs, err := UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:User:IsOrgAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	if models.ExactlyOrgAdminRoles(urs, orgID) || models.IsSysAdminRoles(urs) || models.IsOrgEditor(urs, orgID) {
		return true
	}
	return false

}

func (s *BlogService) CanPublishOrgBlog(user *models.User, orgID string) bool {
	urs, err := UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:User:IsOrgAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	if models.ExactlyOrgAdminRoles(urs, orgID) || models.IsSysAdminRoles(urs) || models.IsOrgEditor(urs, orgID) || models.IsOrgWriter(urs, orgID) {
		return true
	}
	return false

}

func (s *BlogService) GetPublishBlog(query *models.PublishBlogQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError) {
	shouldPreloadCategories := false
	if options.Preloads != nil && lo.Contains(options.Preloads, models.BlogPreloadsCategories) {
		shouldPreloadCategories = true
		options.Preloads = util.RemoveElement(options.Preloads, models.BlogPreloadsCategories)
	}

	shouldPreloadHashtags := false
	if options.Preloads != nil && lo.Contains(options.Preloads, models.BlogPreloadsHashTag) {
		shouldPreloadHashtags = true
		options.Preloads = util.RemoveElement(options.Preloads, models.BlogPreloadsHashTag)
	}

	shouldPreloadUser := false
	if options.Preloads != nil && lo.Contains(options.Preloads, models.BlogPreloadsUser) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, models.BlogPreloadsUser)
	}

	pubBlogs, pagination, err := PublishBlog.FindPage(query, options)
	if err != nil {
		return nil, nil, err
	}

	if len(pubBlogs) == 0 {
		return []*models.Blog{}, &models.Pagination{}, nil
	}

	findManyOption := &models.FindManyOptions{}
	if shouldPreloadCategories {
		findManyOption.Preloads = append(findManyOption.Preloads, models.BlogPreloadsCategories)
	}
	if shouldPreloadHashtags {
		findManyOption.Preloads = append(findManyOption.Preloads, models.BlogPreloadsHashTag)
	}

	if shouldPreloadUser {
		findManyOption.Preloads = append(findManyOption.Preloads, models.BlogPreloadsUser)
	}

	blogIds := lo.Map(pubBlogs, func(item *models.PublishBlog, _ int) string {
		return item.BlogID
	})

	blogQuery := &models.BlogQuery{IDIn: blogIds, ScheduleAtLower: util.NewT(time.Now().Unix())}
	findManyOption.Preloads = append(findManyOption.Preloads, models.OrgField)
	blogs, err := Blog.FindMany(blogQuery, findManyOption)
	if err != nil {
		return nil, nil, err
	}

	var results []*models.Blog
	for _, pb := range pubBlogs {
		blog, ok := lo.Find(blogs, func(item *models.Blog) bool {
			return item.Cuid == pb.BlogCuid
		})
		if ok {
			results = append(results, blog)
		}
	}

	return results, pagination, err

}
func (s *BlogService) GetPublishBlogMany(query *models.PublishBlogQuery, options *models.FindManyOptions) ([]*models.Blog, *e.AppError) {
	shouldPreloadCategories := false
	if options.Preloads != nil && lo.Contains(options.Preloads, models.BlogPreloadsCategories) {
		shouldPreloadCategories = true
		options.Preloads = util.RemoveElement(options.Preloads, models.BlogPreloadsCategories)
	}

	shouldPreloadHashtags := false
	if options.Preloads != nil && lo.Contains(options.Preloads, models.BlogPreloadsHashTag) {
		shouldPreloadHashtags = true
		options.Preloads = util.RemoveElement(options.Preloads, models.BlogPreloadsHashTag)
	}

	shouldPreloadUser := false
	if options.Preloads != nil && lo.Contains(options.Preloads, models.BlogPreloadsUser) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, models.BlogPreloadsUser)
	}

	pubBlogs, err := PublishBlog.FindMany(query, options)
	if err != nil {
		return nil, err
	}

	if len(pubBlogs) == 0 {
		return []*models.Blog{}, nil
	}

	findManyOption := &models.FindManyOptions{}
	if shouldPreloadCategories {
		findManyOption.Preloads = append(findManyOption.Preloads, models.BlogPreloadsCategories)
	}
	if shouldPreloadHashtags {
		findManyOption.Preloads = append(findManyOption.Preloads, models.BlogPreloadsHashTag)
	}

	if shouldPreloadUser {
		findManyOption.Preloads = append(findManyOption.Preloads, models.BlogPreloadsUser)
	}

	blogIds := lo.Map(pubBlogs, func(item *models.PublishBlog, _ int) string {
		return item.BlogID
	})

	blogQuery := &models.BlogQuery{IDIn: blogIds, ScheduleAtLower: util.NewT(time.Now().Unix())}

	blogs, err := Blog.FindMany(blogQuery, findManyOption)
	if err != nil {
		return nil, err
	}

	return blogs, err

}

func (s *BlogService) UnpublishBlogPerson(user *models.User, org *models.Organization, cuid string) *e.AppError {
	// Handle case publish person
	pubPerson, err := s.FindOne(&models.BlogQuery{Cuid: &cuid, Status: util.NewT(models.BlogStatusPublish)}, &models.FindOneOptions{Sort: []string{"pub_date desc"}})
	if err != nil {
		return err
	}

	if pubPerson != nil {
		pubPerson.Status = models.BlogStatusUnPublish
		updateErr := models.Repository.Blog.Update(pubPerson, nil)
		if updateErr != nil {
			return e.NewError500(e.Update_blog_failed, updateErr.Error())
		}

		// decrease total_blogs
		if iErr := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
			UserID: &pubPerson.AuthorID,
		}, models.TotalBlogsField, util.BlogUnit, nil); iErr != nil {
			return e.NewError500(e.User_summary_decrease_failed, "Decrease total blogs count for user error: "+iErr.Error())
		}

		if pubPerson.AuthorID != user.ID {
			notificationReq := &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeBlogUnpublishedByAdmin,
				EntityID:   pubPerson.ID,
				EntityType: communicationdto.BlogEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{pubPerson.AuthorID},
					},
				},
				Props: s.makeNotificationPropsForBlog(pubPerson, org, user).IntoComm(),
			}
			if err := communication.Notification.PushNotification(notificationReq); err != nil {
				log.ErrorWithAlertf("Push notification to user ID %s after unpublishing blog ID %s error: %v", pubPerson.AuthorID, pubPerson.ID, err)
			}
		}
	}

	return nil
}

func (s *BlogService) UnpublishBlogOrg(user *models.User, org *models.Organization, cuid string) *e.AppError {
	unPublishResult, err := PublishBlog.UnpublishBlog(cuid)
	if err != nil {
		return err
	}

	if unPublishResult != nil && unPublishResult.PubDate == 0 {
		if uErr := s.unPublish(unPublishResult, user, org); uErr != nil {
			return uErr
		}

		// decrease total_blogs
		if iErr := models.Repository.UserSummary.Decrease(&models.UserSummaryQuery{
			UserID: &unPublishResult.AuthorId,
		}, models.TotalBlogsField, util.BlogUnit, nil); iErr != nil {
			return e.NewError500(e.User_summary_decrease_failed, "Decrease total blogs count for user error: "+iErr.Error())
		}

		if unPublishResult.AuthorId != user.ID {
			pubOrg, err := s.FindOne(&models.BlogQuery{Cuid: &cuid, Status: util.NewT(models.BlogStatusPublish)}, &models.FindOneOptions{Sort: []string{"pub_date desc"}})
			if err != nil {
				return err
			}

			notificationReq := &communicationdto.PushNotificationRequest{
				Code:       communicationdto.CodeBlogUnpublishedByAdmin,
				EntityID:   unPublishResult.ID,
				EntityType: communicationdto.BlogEntity,
				RuleTree: &communicationdto.TreeNodeRequest{
					Rule: &communicationdto.Rule{
						Subject:   communicationdto.NotiUser,
						Verb:      communicationdto.IsIn,
						ObjectIDs: []string{unPublishResult.AuthorId},
					},
				},
				Props: s.makeNotificationPropsForBlog(pubOrg, org, user).IntoComm(),
			}
			if err := communication.Notification.PushNotification(notificationReq); err != nil {
				log.ErrorWithAlertf("Push notification to user ID %s after unpublishing blog ID %s error: %v", pubOrg.AuthorID, pubOrg.ID, err)
			}
		}
	}

	return nil
}

func (s *BlogService) unPublish(pub *models.PublishBlog, user *models.User, org *models.Organization) *e.AppError {
	if pub != nil && pub.PubDate == 0 {
		blog, cErr := models.Repository.Blog.FindOne(
			&models.BlogQuery{ID: util.NewString(pub.BlogID)},
			nil,
		)
		if cErr != nil {
			return e.NewError400(e.Blog_not_found, fmt.Sprintf("UnPublishBlog not found blog %s", pub.BlogID))
		}

		if !s.CanUpdateBlog(blog, user, org) {
			return e.NewError400(e.Blog_update_permission_required, "You don't have permission to unpublish this blog")
		}

		blog.Status = models.BlogStatusUnPublish
		if uErr := models.Repository.Blog.Update(blog, nil); uErr != nil {
			return e.NewError500(e.Update_blog_failed, uErr.Error())
		}
	}

	return nil
}

func (s *BlogService) CanPassApprovals(user *models.User, org *models.Organization) bool {
	return user.IsSysAdmin() || user.IsOrgAdmin(org.ID) || user.IsOrgEditor(org.ID)

}

func (s *BlogService) GetPublishedPersonalBlogsWithHighestVersion(query *models.BlogQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError) {
	if blogs, pagination, err := models.Repository.Blog.GetPublishedPersonalBlogsWithHighestVersion(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_blog_failed, err.Error())
	} else {
		return blogs, pagination, nil
	}
}

func (s *BlogService) GetPublishBlogByCategoryParent(org *models.Organization, categoryID string, eachCategoriesOptions *models.FindManyOptions) (map[string]*dto.BlogTreeByCateResponse, *e.AppError) {
	cates, fcErr := models.Repository.Category.GetFullChildIdsByParentId(categoryID)
	if fcErr != nil {
		return nil, e.NewError500(e.Category_find_many_failed, fcErr.Error())
	}

	mapCateByCateID := map[string]*models.Category{}

	cateIds := lo.Map(cates, func(item *models.Category, _ int) string {
		mapCateByCateID[item.ID] = item
		return item.ID
	})

	if len(cateIds) == 0 {
		return nil, nil
	}

	// init var
	isFullFill := true
	isHitCache := false
	mapCountRemain := map[string]int{}
	mapBlogByCateIDFromCache := map[string][]*models.Blog{}

	// get blog from cache
	mapBlogCacheIfaceByCateID := make(map[string][]interface{})
	models.Cache.Blog.GetManyBlogByCategory(cateIds, mapBlogCacheIfaceByCateID)
	if len(mapBlogCacheIfaceByCateID) > 0 {
		isHitCache = true
		// hit cache succesfull
		//Handle convert BlogIface to Blog
		for cateID, blogs := range mapBlogCacheIfaceByCateID {
			var blogsUnmarshal []*models.Blog
			models.Cache.Convert(blogs, &blogsUnmarshal)

			// Check if the number of unmarshalled blogs is less than the limit
			if len(blogsUnmarshal) < *eachCategoriesOptions.Limit {
				isFullFill = false
				mapCountRemain[cateID] = *eachCategoriesOptions.Limit - len(blogsUnmarshal)
			}

			// Store the unmarshalled blogs in the cache map
			mapBlogByCateIDFromCache[cateID] = blogsUnmarshal
		}

		// Handle convert Blog into Blog tree if fullfill
		if isFullFill {
			blogTreeResp := map[string]*dto.BlogTreeByCateResponse{}
			for cateID, blogs := range mapBlogByCateIDFromCache {
				if cate, ok := mapCateByCateID[cateID]; ok {
					listSimpleBlog := lo.Map(blogs, func(item *models.Blog, _ int) *models.SimpleBlog { return item.Sanitize() })
					blogTree := &dto.BlogTreeByCateResponse{Blogs: listSimpleBlog[:*eachCategoriesOptions.Limit], CategoryName: cate.Name}
					blogTreeResp[cateID] = blogTree
				} else {
					return nil, e.NewError400(e.Category_not_found, "Category not found")
				}
			}
			return blogTreeResp, nil
		}
	}

	categoryRelationOption := &models.FindManyOptions{}
	categoryRelationQuery := &models.CategoryRelationQuery{CategoryIDIn: cateIds, RelatedType: util.NewT(models.BlogModelName)}

	cateRelations, fmErr := CategoryRelation.FindMany(categoryRelationQuery, categoryRelationOption)
	if fmErr != nil {
		return nil, fmErr
	}

	mapCateIDCateRelation := lo.GroupBy(cateRelations, func(item *models.CategoryRelation) string { return item.CategoryID })

	mapBlogByCateIDFromDB := map[string][]*models.Blog{}

	for cateID, cateRelations := range mapCateIDCateRelation {
		if mapCountRemain[cateID] == 0 && isHitCache {
			continue
		}

		blogCuids := lo.Map(cateRelations, func(item *models.CategoryRelation, _ int) string {
			return item.RelatedID
		})

		uniqBlogCuids := lo.Uniq(blogCuids)
		if len(uniqBlogCuids) == 0 {
			return nil, nil
		}

		// Build query and options
		fpPublishBlogQuery := &models.PublishBlogQuery{BlogCuidIn: uniqBlogCuids, OrgID: &org.ID}

		findManyBlogOptions := &models.FindManyOptions{}
		findManyBlogOptions.Preloads = append(findManyBlogOptions.Preloads, eachCategoriesOptions.Preloads...)
		findManyBlogOptions.Sort = append(findManyBlogOptions.Sort, "create_at DESC")

		if isHitCache {
			//build limit
			if _, ok := mapCountRemain[cateID]; ok {
				findManyBlogOptions.Limit = util.NewT(mapCountRemain[cateID])
			}
			//build offset
			if _, ok := mapBlogByCateIDFromCache[cateID]; ok {
				findManyBlogOptions.Offset = util.NewT(len(mapBlogByCateIDFromCache[cateID]))
			}
		} else {
			findManyBlogOptions.Limit = util.NewT(*eachCategoriesOptions.Limit)
		}

		//
		blogs, getPublishErr := s.GetPublishBlogMany(fpPublishBlogQuery, findManyBlogOptions)
		if getPublishErr != nil {
			return nil, getPublishErr
		}

		mapBlogByCateIDFromDB[cateID] = blogs
	}

	// Handle merge two map from cache and from db Blog into Blog tree
	blogTreeResp := map[string]*dto.BlogTreeByCateResponse{}
	buildCache := make(map[string][]interface{})

	mergedMapBlogByCateID := util.MergeMaps(mapBlogByCateIDFromCache, mapBlogByCateIDFromDB)
	for cateID, blogs := range mergedMapBlogByCateID {
		simpleBlogs := lo.Map(blogs, func(item *models.Blog, _ int) *models.SimpleBlog { return item.Sanitize() })
		blogTree := &dto.BlogTreeByCateResponse{
			Blogs:        simpleBlogs,
			CategoryName: mapCateByCateID[cateID].Name,
		}
		blogTreeResp[cateID] = blogTree
		buildCache[cateID] = lo.Map(blogs, func(item *models.Blog, _ int) interface{} { return item })
	}

	// build cache
	if cacheErr := models.Cache.Blog.SetManyBlogByCategory(buildCache); cacheErr != nil {
		return nil, e.NewError500(e.Cache_set_failed, cacheErr.Error())
	}

	return blogTreeResp, nil

}

func (s *BlogService) GetPublishBlogByCategory(org *models.Organization, query *models.CategoryRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError) {
	categoryRelationOption := &models.FindManyOptions{}
	query.RelatedType = util.NewT(models.BlogModelName)
	cateRelations, fmErr := CategoryRelation.FindManyJoinBlog(query, categoryRelationOption)
	if fmErr != nil {
		return nil, nil, fmErr
	}

	if len(cateRelations) == 0 {
		return []*models.Blog{}, &models.Pagination{}, nil
	}

	blogCuids := lo.Map(cateRelations, func(item *models.CategoryRelation, _ int) string {
		return item.RelatedID
	})

	uniqBlogCuids := lo.Uniq(blogCuids)

	if len(uniqBlogCuids) == 0 {
		return []*models.Blog{}, &models.Pagination{}, nil
	}

	fpPublishBlogQuery := &models.PublishBlogQuery{BlogCuidIn: uniqBlogCuids, OrgID: &org.ID}
	blogs, pagination, getPublishErr := s.GetPublishBlog(fpPublishBlogQuery, options)
	if getPublishErr != nil {
		return nil, nil, getPublishErr
	}
	return blogs, pagination, nil
}

func (s *BlogService) GetRecommendPublishBlogByCategory(query *models.CategoryRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError) {
	categoryRelationOption := &models.FindManyOptions{}
	query.RelatedType = util.NewT(models.BlogModelName)
	cateRelations, fmErr := CategoryRelation.FindMany(query, categoryRelationOption)
	if fmErr != nil {
		return nil, nil, fmErr
	}

	blogCuids := lo.Map(cateRelations, func(item *models.CategoryRelation, _ int) string {
		return item.RelatedID
	})

	uniqBlogCuids := lo.Uniq(blogCuids)

	if len(uniqBlogCuids) == 0 {
		return []*models.Blog{}, &models.Pagination{}, nil
	}

	baseOrg, fOrgErr := Organization.FindOne(&models.OrganizationQuery{Domain: util.NewString(setting.AppSetting.BaseDomain)}, &models.FindOneOptions{})
	if fOrgErr != nil {
		return nil, nil, fOrgErr
	}

	fpPublishBlogQuery := &models.PublishBlogQuery{BlogCuidIn: uniqBlogCuids, OrgID: &baseOrg.ID}

	blogs, pagination, getPublishErr := s.GetPublishBlog(fpPublishBlogQuery, options)
	if getPublishErr != nil {
		return nil, nil, getPublishErr
	}
	return blogs, pagination, nil
}

func (s *BlogService) GetPublishBlogByHashtag(org *models.Organization, query *models.HashtagRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError) {
	hashtagRelationOption := &models.FindManyOptions{}
	htRelations, fmErr := HashtagRelation.FindManyJoinBlog(query, hashtagRelationOption)
	if fmErr != nil {
		return nil, nil, fmErr
	}

	if len(htRelations) == 0 {
		return []*models.Blog{}, &models.Pagination{}, nil
	}

	blogCuids := lo.Map(htRelations, func(item *models.HashtagRelation, _ int) string {
		return item.RelatedID
	})

	uniqBlogCuids := lo.Uniq(blogCuids)

	if len(uniqBlogCuids) == 0 {
		return []*models.Blog{}, &models.Pagination{}, nil
	}

	fpPublishBlogQuery := &models.PublishBlogQuery{BlogCuidIn: uniqBlogCuids, OrgID: &org.ID}
	blogs, pagination, getPublishErr := s.GetPublishBlog(fpPublishBlogQuery, options)
	if getPublishErr != nil {
		return nil, nil, getPublishErr
	}
	return blogs, pagination, nil
}

// This function get recommend publish blog from base org (vbi)
func (s *BlogService) GetRecommendPublishBlogByHashtag(query *models.HashtagRelationQuery, options *models.FindPageOptions) ([]*models.Blog, *models.Pagination, *e.AppError) {
	hashtagRelationOption := &models.FindManyOptions{}
	htRelations, fmErr := HashtagRelation.FindMany(query, hashtagRelationOption)
	if fmErr != nil {
		return nil, nil, fmErr
	}

	blogCuids := lo.Map(htRelations, func(item *models.HashtagRelation, _ int) string {
		return item.RelatedID
	})

	uniqBlogCuids := lo.Uniq(blogCuids)

	if len(uniqBlogCuids) == 0 {
		return []*models.Blog{}, &models.Pagination{}, nil
	}

	baseOrg, fOrgErr := Organization.FindOne(&models.OrganizationQuery{Domain: util.NewString(setting.AppSetting.BaseDomain)}, &models.FindOneOptions{})
	if fOrgErr != nil {
		return nil, nil, fOrgErr
	}

	fpPublishBlogQuery := &models.PublishBlogQuery{BlogCuidIn: uniqBlogCuids, OrgID: &baseOrg.ID}
	blogs, pagination, getPublishErr := s.GetPublishBlog(fpPublishBlogQuery, options)
	if getPublishErr != nil {
		return nil, nil, getPublishErr
	}
	return blogs, pagination, nil
}

func (s *BlogService) CanAddRoleWriter(user *models.User, org *models.Organization) bool {
	return user.IsSysAdmin() || user.IsOrgAdmin(org.ID) || user.IsOrgEditor(org.ID)
}

func (s *BlogService) AddRemoveRoleWriter(org *models.Organization, data *dto.AddRoleWriterRequest) *e.AppError {
	addRoleList := lo.Map(data.AddIds, func(item string, _ int) *models.UserRoleOrg {
		return &models.UserRoleOrg{
			UserID: item,
			RoleID: models.OrgWriterRoleType,
			OrgID:  org.ID,
		}
	})

	if len(addRoleList) > 0 {
		if err := models.Repository.UserRoleOrg.UpsertManyRole(addRoleList, nil); err != nil {
			return e.NewError500(e.Error_auth_add_user_role_failed, err.Error())
		}
	}

	if len(data.RemoveIds) > 0 {
		if _, err := models.Repository.UserRoleOrg.DeleteMany(&models.UserRoleOrgQuery{OrgID: &org.ID, UserIDIn: data.RemoveIds, RoleID: util.NewT(models.OrgWriterRoleType)}, nil); err != nil {
			return e.NewError500(e.Error_auth_remove_user_role_failed, err.Error())
		}
	}
	return nil
}

func (s *BlogService) CanAddRoleEditor(user *models.User, org *models.Organization) bool {
	return user.IsSysAdmin() || user.IsOrgAdmin(org.ID)
}

func (s *BlogService) AddRemoveRoleEditor(org *models.Organization, data *dto.AddRoleEditorRequest) *e.AppError {
	addRoleList := lo.Map(data.AddIds, func(item string, _ int) *models.UserRoleOrg {
		return &models.UserRoleOrg{
			UserID: item,
			RoleID: models.OrgEditorRoleType,
			OrgID:  org.ID,
		}
	})

	if len(addRoleList) > 0 {
		if err := models.Repository.UserRoleOrg.UpsertManyRole(addRoleList, nil); err != nil {
			return e.NewError500(e.Error_auth_add_user_role_failed, err.Error())
		}
	}

	if len(data.RemoveIds) > 0 {
		if _, err := models.Repository.UserRoleOrg.DeleteMany(&models.UserRoleOrgQuery{OrgID: &org.ID, UserIDIn: data.RemoveIds, RoleID: util.NewT(models.OrgEditorRoleType)}, nil); err != nil {
			return e.NewError500(e.Error_auth_remove_user_role_failed, err.Error())
		}
	}
	return nil
}

func (s *BlogService) DeleteMany(query *models.BlogQuery) *e.AppError {
	_, err := models.Repository.Blog.DeleteMany(query, nil)
	if err != nil {
		return e.NewError500(e.Blog_delete_many_failed, err.Error())
	}
	return nil
}
