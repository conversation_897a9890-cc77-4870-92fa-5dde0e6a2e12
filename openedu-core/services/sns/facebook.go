package sns

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

type Facebook struct {
	ClientID     string
	ClientSecret string
	RedirectURI  string
	TokenURL     string
	UserDataURL  string
}

// Placeholder structs to represent Facebook user data structure
type FacebookUserData struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	FirstName  string `json:"first_name"`
	LastName   string `json:"last_name"`
	MiddleName string `json:"middle_name"`
	Email      string `json:"email"`
	Birthday   string `json:"birthday"`
	Picture    struct {
		Data struct {
			URL string `json:"url"`
		} `json:"data"`
	} `json:"picture"`
}

func (d *FacebookUserData) GetDisplayName() string {
	displayName := d.FirstName
	if d.MiddleName != "" {
		displayName += " " + d.MiddleName
	}

	if d.LastName != "" {
		displayName += " " + d.LastName
	}
	return displayName
}

type facebookTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	Error       struct {
		Message string `json:"message"`
	} `json:"error"`
}

func NewFaceBook() *Facebook {
	return &Facebook{
		ClientID:     setting.FacebookSetting.ClientID,
		ClientSecret: setting.FacebookSetting.ClientSecret,
		RedirectURI:  setting.FacebookSetting.RedirectURI,
		TokenURL:     setting.FacebookSetting.TokenURL,
		UserDataURL:  setting.FacebookSetting.UserDataURL,
	}
}

func (s *Facebook) SetRedirectURIFromDomain(domain string) {
	// if setting.IsOrgSite(domain) {
	// 	s.RedirectURI = fmt.Sprint(domain + "/auth-callback")
	// }
}

func (s *Facebook) VerifyCode(code string, codeVerifier string) (SNSInfo, error) {
	// Exchange the code for an access token
	tokenResp, err := s.exchangeFacebookCodeForAccessToken(code, codeVerifier)
	if err != nil {
		return SNSInfo{}, err
	}

	// Get user data using the access token
	userData, err := s.getFacebookUserData(tokenResp.AccessToken)
	if err != nil {
		return SNSInfo{}, err
	}

	// Step 3: Construct the SNSInfo struct with the obtained data

	snsInfo := SNSInfo{
		Provider:            util.Facebook,
		SnsId:               userData.ID,
		Username:            userData.Name,
		DisplayName:         userData.Name,
		Email:               userData.Email,
		Avatar:              userData.Picture.Data.URL,
		AccessToken:         tokenResp.AccessToken,
		AccessTokenExpireIn: tokenResp.ExpiresIn,
		RefreshToken:        "", // Facebook doesn't provide refresh tokens
		Data:                userData,
	}

	return snsInfo, nil
}

func (s *Facebook) exchangeFacebookCodeForAccessToken(code string, codeVerifier string) (*facebookTokenResponse, error) {
	// Construct the URL for exchanging the code for an access token
	tokenURL := fmt.Sprintf(s.TokenURL+
		"client_id=%s&redirect_uri=%s&code_verifier=%s&code=%s",
		s.ClientID, url.QueryEscape(s.RedirectURI), codeVerifier, code)

	// Make an HTTP GET request to the token URL
	resp, err := http.Get(tokenURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read and parse the response body
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Check the response status code
	if resp.StatusCode != http.StatusOK {
		// Create an interface{} to unmarshal the JSON into
		var data map[string]interface{}

		// Unmarshal the response data into the interface
		if err := json.Unmarshal(responseData, &data); err != nil {
			return nil, fmt.Errorf("facebook token exchange::Unmarshal failed %v", err)
		}

		log.Errorf("Facebook token exchange failed %v and redirect uri %s", data, s.RedirectURI)
		return nil, fmt.Errorf("facebook token exchange failed %v and status code with %d", data, resp.StatusCode)
	}

	// Unmarshal the response JSON to extract the access token
	var tokenResponse *facebookTokenResponse
	err = json.Unmarshal(responseData, &tokenResponse)
	if err != nil {
		return nil, err
	}

	if tokenResponse == nil || tokenResponse.AccessToken == "" {
		return nil, fmt.Errorf("facebook token exchange failed: %s", tokenResponse.Error.Message)
	}

	return tokenResponse, nil
}

func (s *Facebook) getFacebookUserData(accessToken string) (*FacebookUserData, error) {
	// Construct the URL for fetching user data from the Graph API
	graphAPIURL := s.UserDataURL + "?fields=id,name,first_name,middle_name,last_name,picture,email,birthday"

	graphAPIURL += "&access_token=" + accessToken

	// Make an HTTP GET request to the Graph API URL
	resp, err := http.Get(graphAPIURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read and parse the response body
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Check the response status code
	if resp.StatusCode != http.StatusOK {
		// Create an interface{} to unmarshal the JSON into
		var data map[string]interface{}

		// Unmarshal the response data into the interface
		if err := json.Unmarshal(responseData, &data); err != nil {
			return nil, fmt.Errorf("get Facebook's user data::Unmarshal failed %v", err)
		}

		log.Errorf("get Facebook's user data failed %v and status code with %d", data, resp.StatusCode)
		return nil, fmt.Errorf("get Facebook's user data failed %v and status code with %d", data, resp.StatusCode)
	}

	// Unmarshal the response JSON to extract facebookUser
	var facebookUser *FacebookUserData
	err = json.Unmarshal(responseData, &facebookUser)
	if err != nil {
		return nil, err
	}

	return facebookUser, nil
}
