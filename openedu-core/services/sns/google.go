package sns

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
)

type Google struct {
	ClientID     string
	ClientSecret string
	RedirectURI  string
	TokenURL     string
	UserDataURL  string
}

type GoogleUserData struct {
	ID            string `json:"sub"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Picture       string `json:"picture"`
	Locale        string `json:"locale"`
}

func (d *GoogleUserData) GetDisplayName() string {
	displayName := d.GivenName
	if d.FamilyName != "" {
		displayName += " " + d.FamilyName
	}

	return displayName
}

type googleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Error        string `json:"error"`
}

func NewGoogle() *Google {
	return &Google{
		ClientID:     setting.GoogleSetting.ClientID,
		ClientSecret: setting.GoogleSetting.ClientSecret,
		RedirectURI:  setting.GoogleSetting.RedirectURI,
		TokenURL:     setting.GoogleSetting.TokenURL,
		UserDataURL:  setting.GoogleSetting.UserDataURL,
	}
}

func (s *Google) SetRedirectURIFromDomain(domain string) {
	//if setting.IsOrgSite(domain) {
	//	s.RedirectURI = fmt.Sprint(domain + "/auth-callback")
	//}
}

func (s *Google) VerifyCode(code string, codeVerifier string) (SNSInfo, error) {
	tokenResp, err := s.exchangeGoogleCodeForAccessToken(code)
	if err != nil {
		return SNSInfo{}, err
	}

	// verify access token
	userData, err := s.getGoogleUserData(tokenResp.AccessToken)
	if err != nil {
		return SNSInfo{}, err
	}

	snsInfo := SNSInfo{
		Provider:            util.Google,
		SnsId:               userData.ID,
		Username:            userData.Name,
		DisplayName:         userData.Name,
		Email:               userData.Email,
		Avatar:              userData.Picture,
		AccessToken:         tokenResp.AccessToken,
		AccessTokenExpireIn: tokenResp.ExpiresIn,
		RefreshToken:        tokenResp.RefreshToken,
		Data:                userData,
	}

	return snsInfo, nil
}

func (s *Google) exchangeGoogleCodeForAccessToken(code string) (*googleTokenResponse, error) {
	tokenURL := fmt.Sprintf(s.TokenURL+
		//"code=%s&client_id=%s&client_secret=%s&redirect_uri=%s&grant_type=authorization_code&access_type=offline",
		"code=%s&client_id=%s&client_secret=%s&redirect_uri=%s&grant_type=authorization_code",
		code, s.ClientID, s.ClientSecret, url.QueryEscape(s.RedirectURI))

	// Make an HTTP POST request to the token URL
	resp, err := http.Post(tokenURL, "application/x-www-form-urlencoded", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read and parse the response body
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Check the response status code
	if resp.StatusCode != http.StatusOK {
		// Create an interface{} to unmarshal the JSON into
		var data map[string]interface{}

		// Unmarshal the response data into the interface
		if err := json.Unmarshal(responseData, &data); err != nil {
			return nil, fmt.Errorf("google token exchange::Unmarshal failed %v", err)
		}

		return nil, fmt.Errorf("google token exchange failed %v and status code with %d", data, resp.StatusCode)
	}

	// Unmarshal the response JSON to extract the access token and refresh token
	var tokenResponse *googleTokenResponse
	err = json.Unmarshal(responseData, &tokenResponse)
	if err != nil {
		return nil, err
	}

	if tokenResponse == nil || tokenResponse.AccessToken == "" {
		return nil, fmt.Errorf("google token exchange failed: %s", tokenResponse.Error)
	}

	return tokenResponse, nil
}

func (s *Google) getGoogleUserData(accessToken string) (*GoogleUserData, error) {
	req, err := http.NewRequest("GET", s.UserDataURL, nil)
	if err != nil {
		return nil, err
	}

	// Set the authorization header with the access token
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// Make the request to Google's user data endpoint
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read and parse the response body
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Check the response status code
	if resp.StatusCode != http.StatusOK {
		// Create an interface{} to unmarshal the JSON into
		var data map[string]interface{}

		// Unmarshal the response data into the interface
		if err := json.Unmarshal(responseData, &data); err != nil {
			return nil, fmt.Errorf("get Google's user data::Unmarshal failed %v", err)
		}

		log.Errorf("get Google's user data failed %v and status code with %d", data, resp.StatusCode)
		return nil, fmt.Errorf("get Google's user data failed %v and status code with %d", data, resp.StatusCode)
	}

	// Unmarshal the response JSON to extract user data
	var googleUser *GoogleUserData
	err = json.Unmarshal(responseData, &googleUser)
	if err != nil {
		return nil, err
	}

	return googleUser, nil
}
