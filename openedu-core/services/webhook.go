package services

import (
	"context"
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/upload"
	"openedu-core/pkg/util"
	"regexp"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *WebhookService) HandleBunnyVideoWebhook(params *dto.BunnyVideoWebhookRequest) *e.AppError {
	if params.Status != int(util.FinishedWebhookBunnyStatus) {
		return e.NewError400(e.Error_bunny_webhook_status_invalid, e.MsgFlags[e.Error_bunny_webhook_status_invalid])
	}

	file, err := models.Repository.File.FindOne(&models.FileQuery{
		BunnyVideoID: &params.BunnyVideoID,
	}, &models.FindOneOptions{})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Error_file_not_found, err.Error())
		}
		return e.NewError500(e.Error_file_find_one_failed, err.Error())
	}

	var provider *upload.BunnyUploadProvider
	if file.BunnyLibraryID == setting.UploadSetting.UploadPrivateBunnyLibraryID {
		provider = upload.GetPrivateBunnyProvider()
	} else {
		provider = upload.GetPublicBunnyProvider()
	}

	videoUploaded, gErr := provider.GetVideoById(&upload.GetVideoRequest{
		VideoID: params.BunnyVideoID,
	})
	if gErr != nil {
		return e.NewError500(e.Error_get_file_info_failed, gErr.Error())
	}

	file.Duration = int64(videoUploaded.Length)
	if file.URL != "" {
		re := regexp.MustCompile(util.SignBunnyIFrameRegex)
		cleanedURL := re.ReplaceAllString(file.URL, "")
		file.URL = cleanedURL
	}
	if err := models.Repository.File.Update(file, nil); err != nil {
		return e.NewError500(e.Error_file_update_failed, err.Error())
	}

	relations, rErr := models.Repository.FileRelation.FindMany(&models.FileRelationQuery{
		FileID:      &file.ID,
		RelatedType: util.NewString(string(models.LessonModelName)),
		Field:       util.NewString(models.FilesField),
	}, &models.FindManyOptions{})
	if rErr != nil {
		return e.NewError500(e.Error_file_relation_find_many_failed, rErr.Error())
	}

	if len(relations) == 0 {
		return nil
	}

	lessonContentIDs := lo.Map(relations, func(r *models.FileRelation, _ int) string {
		return r.RelatedID
	})
	lessonContentIDs = lo.Uniq(lessonContentIDs)

	lessonContents, lErr := models.Repository.LessonContent(context.TODO()).FindMany(&models.LessonContentQuery{
		IDIn:           lessonContentIDs,
		IncludeDeleted: util.NewBool(false),
	}, &models.FindManyOptions{})

	if lErr != nil {
		return e.NewError500(e.LessonContentFindFailed, lErr.Error())
	}

	if len(lessonContents) == 0 {
		return nil
	}

	for _, lc := range lessonContents {
		lc.Duration = videoUploaded.Length
	}

	uErr := models.Repository.LessonContent(context.TODO()).UpdateManyDurationFromWebhook(lessonContents, nil)
	if uErr != nil {
		return e.NewError500(e.Lesson_update_failed, uErr.Error())
	}

	return nil
}
