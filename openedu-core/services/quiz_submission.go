package services

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"sort"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *QuizSubmissionService) Create(user *models.User, quiz *models.Quiz, course *models.Course) (*models.QuizSubmission, *e.AppError) {
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	submission := &models.QuizSubmission{
		QuizUID:                       quiz.UID,
		QuizID:                        quiz.ID,
		UserID:                        user.ID,
		CourseCuid:                    course.Cuid,
		Status:                        models.QuizSubmissionStatusInProgress,
		Passed:                        false,
		StartAt:                       now.UnixMilli(),
		ArchivedPoints:                0,
		HighestPointsOnSingleQuestion: 0,
		HighestStreak:                 0,
	}
	if quiz.IsTimeLimitEnabled() && quiz.IsTimeLimitOverall() {
		submission.DeadlineAt = now.Add(time.Duration(quiz.Settings.TimeLimit)).UnixMilli()
	}

	if err := models.Repository.QuizSubmission.Create(submission, tx); err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_create_submission_failed, "Create quiz submission error: "+err.Error())
	}

	//if quiz.IsShuffleChoicesEnabled() {
	//	quiz.ShuffleQuestionChoices()
	//}

	if quiz.IsShuffleQuestionsEnabled() {
		quiz.ShuffleQuestions()
	}

	var answers []*models.QuizAnswer
	for idx, question := range quiz.Questions {
		answers = append(answers, &models.QuizAnswer{
			SubmissionID:     submission.ID,
			QuizID:           quiz.ID,
			UserID:           user.ID,
			QuestionID:       question.ID,
			Order:            idx + 1,
			AnsweredItemSets: []models.QuizQuestionItems{},
			StartAt:          0,
			EndAt:            0,
			Correct:          false,
			ArchivedPoints:   0,
		})
	}
	if err := models.Repository.QuizAnswer.CreateMany(answers, tx); err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_create_submission_failed, "Create quiz submission error: "+err.Error())
	}

	submission.Answers = answers
	submission.NumQuestions = len(answers)
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, e.NewError500(e.Quiz_create_submission_failed, "Commit transaction error: "+err.Error())
	}

	return submission, nil
}

func (s *QuizSubmissionService) FindOne(query *models.QuizSubmissionQuery, options *models.FindOneOptions) (*models.QuizSubmission, *e.AppError) {
	submission, err := models.Repository.QuizSubmission.FindOne(query, options)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.Quiz_submission_not_found, "Quiz submission not found: "+err.Error())
		}
		return nil, e.NewError500(e.Quiz_submission_find_failed, "Find quiz submission error: "+err.Error())
	}
	return submission, nil
}

func (s *QuizSubmissionService) FindByID(submissionID string, options *models.FindOneOptions) (*models.QuizSubmission, *e.AppError) {
	submission, err := models.Repository.QuizSubmission.FindByID(submissionID, options)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.Quiz_submission_not_found, "Quiz submission not found: "+err.Error())
		}
		return nil, e.NewError500(e.Quiz_submission_find_failed, "Find quiz submission error: "+err.Error())
	}
	return submission, nil
}

func (s *QuizSubmissionService) FindPage(query *models.QuizSubmissionQuery, options *models.FindPageOptions) ([]*models.QuizSubmission, *models.Pagination, *e.AppError) {
	submissions, pagination, err := models.Repository.QuizSubmission.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Quiz_submission_find_failed, "Find quiz submissions error: "+err.Error())
	}
	return submissions, pagination, nil
}

func (s *QuizSubmissionService) Update(submission *models.QuizSubmission) (*models.QuizSubmission, *e.AppError) {
	if err := models.Repository.QuizSubmission.Update(submission, nil); err != nil {
		return nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission error: "+err.Error())
	}
	return submission, nil
}

func (s *QuizSubmissionService) FindRanksByQuizUID(quizUID string, userID string, top int) ([]*models.QuizSubmissionRank, *e.AppError) {
	ranks, err := models.Repository.QuizSubmission.FindRanksByQuizUID(quizUID, userID, top)
	if err != nil {
		return nil, e.NewError500(e.Quiz_get_ranks_failed, "Find ranks by quiz UID error: "+err.Error())
	}
	return ranks, nil
}

func (s *QuizSubmissionService) FindCurrentQuestion(submission *models.QuizSubmission) (*models.QuizAnswer, *models.QuizQuestion, *e.AppError) {
	quiz, err := models.Repository.Quiz.FindByID(submission.QuizID)
	if err != nil {
		return nil, nil, e.NewError500(e.Quiz_find_failed, "Find quiz failed: "+err.Error())
	}

	// Quiz submission is over time limit, update its status to `done`
	if !submission.IsInDeadline() {
		if submission.Status == models.QuizSubmissionStatusInProgress {
			// Update status submission to done
			submission.Status = models.QuizSubmissionStatusDone
			submission.EndAt = time.Now().UnixMilli()
			submission.TimeToCompleteInMilliSeconds = submission.CalcTimeToCompleteInMilliSeconds()
			submission.Passed = s.checkSubmissionIsPassed(quiz, submission)
			if err = models.Repository.QuizSubmission.Update(submission, nil); err != nil {
				return nil, nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission error: "+err.Error())
			}
		}
		return nil, nil, e.NewError400(e.Quiz_submission_over_deadline, "Quiz submission is over deadline")
	}

	currentAnswer, appErr := s.getCurrentAnswer(quiz, submission)
	if appErr != nil {
		return nil, nil, appErr
	}

	if currentAnswer.StartAt == 0 {
		currentAnswer.StartAt = time.Now().UnixMilli()
		if err = models.Repository.QuizAnswer.Update(currentAnswer, nil); err != nil {
			return nil, nil, e.NewError500(e.Quiz_submission_find_question_failed, "Update quiz answer error: "+err.Error())
		}
	}

	nextQuestion, err := models.Repository.QuizQuestion.FindByID(currentAnswer.QuestionID, &models.FindOneOptions{
		Preloads: []string{util.FilesField, util.ItemsFilesField},
	})
	if err != nil {
		return nil, nil, e.NewError500(e.Quiz_submission_find_question_failed,
			"Find quiz question ID "+currentAnswer.QuestionID+" for answer ID "+currentAnswer.ID+" in schema "+models.AppSchema+" error: "+err.Error())
	}

	if quiz.IsShuffleChoicesEnabled() {
		nextQuestion.ShuffleChoices()
	}

	return currentAnswer, nextQuestion, nil
}

func (s *QuizSubmissionService) getCurrentAnswer(quiz *models.Quiz, submission *models.QuizSubmission) (*models.QuizAnswer, *e.AppError) {
	// Sort submission answers by order ASC
	sort.Slice(submission.Answers, func(i, j int) bool {
		return submission.Answers[i].Order < submission.Answers[j].Order
	})

	for _, answer := range submission.Answers {
		if answer.StartAt == 0 {
			return answer, nil
		}

		// Answer already submitted
		if answer.EndAt != 0 {
			continue
		}

		// StartAt <> 0 and EndAt = 0
		if !quiz.IsTimeLimitEnabled() {
			return answer, nil
		}

		if quiz.IsTimeLimitOverall() {
			if submission.IsInDeadline() {
				return answer, nil
			}

			// Quiz submission is over time limit, update its status to `done`
			if submission.Status == models.QuizSubmissionStatusInProgress {
				// Update status submission to done
				submission.Status = models.QuizSubmissionStatusDone
				submission.EndAt = time.Now().UnixMilli()
				submission.TimeToCompleteInMilliSeconds = submission.CalcTimeToCompleteInMilliSeconds()
				submission.Passed = s.checkSubmissionIsPassed(quiz, submission)
				if err := models.Repository.QuizSubmission.Update(submission, nil); err != nil {
					return nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission error: "+err.Error())
				}
			}
			return nil, e.NewError400(e.Quiz_submission_over_deadline, "Quiz submission is over deadline")

		} else if quiz.IsTimeLimitPerQuestion() {
			question, found := lo.Find(quiz.Questions, func(q *models.QuizQuestion) bool {
				return q.ID == answer.QuestionID
			})
			if !found {
				return nil, e.NewError500(e.Quiz_question_not_found, "Find question by answer error: question not found")
			}

			now := time.Now()
			deadline := time.UnixMilli(answer.StartAt).Add(time.Duration(question.Settings.TimeLimit))
			if !now.After(deadline) { // Still in deadline
				if answer.Order == len(submission.Answers) { // Last question, set its deadline to submission deadline
					submission.DeadlineAt = deadline.UnixMilli()
					if err := models.Repository.QuizSubmission.Update(submission, nil); err != nil {
						return nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission error: "+err.Error())
					}
				}
				return answer, nil
			}
		}
	}

	// All answer already submitted, update quiz submission status to `done`
	if submission.Status == models.QuizSubmissionStatusInProgress {
		if submission.EndAt == 0 {
			submission.EndAt = time.Now().UnixMilli()
		}
		submission.Status = models.QuizSubmissionStatusDone
		if err := models.Repository.QuizSubmission.Update(submission, nil); err != nil {
			return nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission done error: "+err.Error())
		}
	}
	return nil, e.NewError400(e.Quiz_submission_all_answer_submitted, "All answers have been already submitted")
}

func (s *QuizSubmissionService) SubmitAnswer(submission *models.QuizSubmission, req *dto.QuizQuestionSubmissionRequest) (*models.QuizAnswer, *e.AppError) {
	submittedAnswer, err := models.Repository.QuizAnswer.FindOne(
		&models.QuizAnswerQuery{
			QuestionID:   &req.QuestionID,
			SubmissionID: &submission.ID,
		},
		&models.FindOneOptions{
			CustomPreloads: map[string]func(*gorm.DB) *gorm.DB{
				util.QuestionField: func(db *gorm.DB) *gorm.DB {
					return db.Table(models.GetTblName(models.QuizQuestionTbl))
				},
			},
		},
	)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.Quiz_submission_quesion_not_found, "Question not found: "+err.Error())
		}
		return nil, e.NewError500(e.Quiz_submission_find_answer_failed, "Find question for submit error: "+err.Error())
	}

	if submittedAnswer.EndAt != 0 {
		return nil, e.NewError400(e.Quiz_submission_answer_already_submitted, "You have already submit answer for this question")
	}

	quiz, err := models.Repository.Quiz.FindByID(submittedAnswer.QuizID)
	if err != nil {
		return nil, e.NewError500(e.Quiz_find_failed, "Find quiz failed: "+err.Error())
	}

	question, found := lo.Find(quiz.Questions, func(question *models.QuizQuestion) bool {
		return question.ID == req.QuestionID
	})
	if !found {
		return nil, e.NewError400(e.Question_not_found, "Question not found, ID: "+req.QuestionID)
	}

	// Check answer is over time limit or not
	now := time.Now()
	if quiz.IsTimeLimitEnabled() {
		switch quiz.Settings.TimeLimitType {
		case models.QuizTimeLimitTypeOverall:
			deadline := time.UnixMilli(submission.DeadlineAt)
			if now.After(deadline) {
				if submission.Status == models.QuizSubmissionStatusInProgress {
					if submission.EndAt == 0 {
						submission.EndAt = now.UnixMilli()
					}
					submission.Status = models.QuizSubmissionStatusDone
					if err = models.Repository.QuizSubmission.Update(submission, nil); err != nil {
						return nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission done error: "+err.Error())
					}
				}
				return nil, e.NewError400(e.Quiz_submission_over_deadline, "Quiz submission is over deadline")
			}

		case models.QuizTimeLimitTypePerQuestion:
			deadline := time.UnixMilli(submittedAnswer.StartAt).Add(time.Duration(question.Settings.TimeLimit))
			if now.After(deadline) {
				// If answer is the last, update submission status to `done`
				if submittedAnswer.Order >= len(submission.Answers) {
					if submission.Status == models.QuizSubmissionStatusInProgress {
						if submission.EndAt == 0 {
							submission.EndAt = now.UnixMilli()
						}
						submission.Status = models.QuizSubmissionStatusDone
						if err = models.Repository.QuizSubmission.Update(submission, nil); err != nil {
							return nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission done error: "+err.Error())
						}
					}
				}
				return nil, e.NewError400(e.Quiz_submission_over_question_time_limit, "Quiz submission is over question time limit")
			}
		}
	}

	// Check answer is correct
	questionItemsByIDs := make(map[string]*models.QuizQuestionItem)
	for _, questionItem := range question.Items {
		questionItemsByIDs[questionItem.ID] = questionItem
	}

	submittedAnswer.AnsweredItemSets = make([]models.QuizQuestionItems, len(req.AnsweredItemSets))
	for idx, answeredItemRequests := range req.AnsweredItemSets {
		var answeredItems []*models.QuizQuestionItem
		for _, answeredItemRequest := range answeredItemRequests {
			if answeredItemRequest.ID != "" {
				questionItem, ok := questionItemsByIDs[answeredItemRequest.ID]
				if !ok {
					return nil, e.NewError400(e.Quiz_submission_invalid_answer, "Quiz submission answer is invalid, item ID: "+answeredItemRequest.ID)
				}
				answeredItems = append(answeredItems, questionItem)
			} else {
				answeredItems = append(answeredItems, &models.QuizQuestionItem{
					Text: answeredItemRequest.Text,
				})
			}
		}
		submittedAnswer.AnsweredItemSets[idx] = answeredItems
	}

	submittedAnswer.Correct = question.IsCorrectAnswer(submittedAnswer)
	submittedAnswer.EndAt = now.UnixMilli()

	// Calculate bonus and penalty points
	if appErr := s.updateSubmissionAfterSubmitAnswer(quiz, question, submittedAnswer, submission); appErr != nil {
		return nil, appErr
	}

	// Update submission answer
	if err = models.Repository.QuizAnswer.Update(submittedAnswer, nil); err != nil {
		return nil, e.NewError500(e.Quiz_submission_submit_answer_failed, "Submit answer for submission failed: "+err.Error())
	}
	return submittedAnswer, nil
}

func (s *QuizSubmissionService) UpdateStatus(submission *models.QuizSubmission, newStatus models.QuizSubmissionStatus) (*models.QuizSubmission, *e.AppError) {
	quiz, err := models.Repository.Quiz.FindByID(submission.QuizID)
	if err != nil {
		return nil, e.NewError500(e.Quiz_find_failed, "Find quiz failed: "+err.Error())
	}

	if newStatus == models.QuizSubmissionStatusDone {
		submission.Status = models.QuizSubmissionStatusDone
		submission.EndAt = time.Now().UnixMilli()
		submission.TimeToCompleteInMilliSeconds = submission.CalcTimeToCompleteInMilliSeconds()
		submission.Passed = s.checkSubmissionIsPassed(quiz, submission)
	}
	if err = models.Repository.QuizSubmission.Update(submission, nil); err != nil {
		return nil, e.NewError500(e.Quiz_submission_update_failed, "Update quiz submission failed: "+err.Error())
	}
	return submission, nil
}

func (s *QuizSubmissionService) updateSubmissionAfterSubmitAnswer(quiz *models.Quiz, question *models.QuizQuestion, answer *models.QuizAnswer, submission *models.QuizSubmission) *e.AppError {
	bonusPoints := 0
	if answer.Correct {
		_, idx, found := lo.FindIndexOf(submission.Answers, func(ans *models.QuizAnswer) bool {
			return ans.ID == answer.ID
		})
		if !found {
			return e.NewError500(e.Quiz_submission_submit_answer_failed, "Answer not found")
		}

		streaks := 1
		for i := idx - 1; i >= 0; i-- {
			if !submission.Answers[i].Correct {
				break
			}
			streaks++
		}

		if submission.HighestStreak < streaks {
			submission.HighestStreak = streaks
		}

		if quiz.IsTimeBonusPointsEnabled() {
			takeInSeconds := time.UnixMilli(int64(answer.EndAt)).Sub(time.UnixMilli(int64(answer.StartAt))).Seconds()
			leftSeconds := time.Duration(question.Settings.TimeLimit).Seconds() - takeInSeconds

			if leftSeconds > 0 {
				bonusPoints += int(leftSeconds) * quiz.Settings.TimeBonusPointsPerSecond
			}
		}

		if quiz.IsStreakBonusEnabled() {
			bonusPercentage := quiz.Settings.StreakBonusPercentageIncrement * float64(streaks-1) // Not include current answer in streak bonus
			if bonusPercentage > quiz.Settings.StreakBonusMaxPercentage {
				bonusPercentage = quiz.Settings.StreakBonusMaxPercentage
			}

			bonusPoints += int((float64(question.Points) * bonusPercentage) / 100)
		}
	} else {
		// Penalty points
		if quiz.IsPenaltyPointsEnabled() {
			bonusPoints -= quiz.Settings.PenaltyPointsPerWrongAnswer
		}
	}

	if answer.ID == submission.Answers[len(submission.Answers)-1].ID {
		submission.Status = models.QuizSubmissionStatusDone
		submission.EndAt = time.Now().UnixMilli()
		submission.TimeToCompleteInMilliSeconds = submission.CalcTimeToCompleteInMilliSeconds()
		submission.Answers[len(submission.Answers)-1] = answer
		submission.Passed = s.checkSubmissionIsPassed(quiz, submission)
	}

	// Update bonus points to submission
	answer.ArchivedPoints = lo.If(answer.Correct, question.Points+bonusPoints).Else(bonusPoints)
	submission.ArchivedPoints += answer.ArchivedPoints
	if answer.ArchivedPoints > submission.HighestPointsOnSingleQuestion {
		submission.HighestPointsOnSingleQuestion = answer.ArchivedPoints
	}

	if err := models.Repository.QuizSubmission.Update(submission, nil); err != nil {
		return e.NewError500(e.Quiz_submission_submit_answer_failed, "Update quiz submission failed: "+err.Error())
	}

	return nil
}

func (s *QuizSubmissionService) checkSubmissionIsPassed(quiz *models.Quiz, submission *models.QuizSubmission) bool {
	switch quiz.Settings.PassCriteria {
	case models.QuizPassCriteriaPercentage:
		maxPointsCanArchived := lo.Reduce(quiz.Questions, func(points int, question *models.QuizQuestion, _ int) int {
			return points + question.Points
		}, 0)

		return (float64(submission.ArchivedPoints) * 100 / float64(maxPointsCanArchived)) >= quiz.Settings.MinPercentageToPass

	case models.QuizPassCriteriaCorrectAnswers:
		numCorrectAnswers := lo.Reduce(submission.Answers, func(count int, answer *models.QuizAnswer, _ int) int {
			return lo.If(answer.Correct, count+1).Else(count)
		}, 0)

		return numCorrectAnswers >= quiz.Settings.MinCorrectAnswersToPass
	}
	return false
}

func (s *QuizSubmissionService) CheckFinalQuizCompletePercent(quiz *models.Quiz, submission *models.QuizSubmission, percentRequired int) bool {
	switch quiz.Settings.PassCriteria {
	case models.QuizPassCriteriaPercentage:
		maxPointsCanArchived := lo.Reduce(quiz.Questions, func(points int, question *models.QuizQuestion, _ int) int {
			return points + question.Points
		}, 0)

		return (float64(submission.ArchivedPoints) * 100 / float64(maxPointsCanArchived)) >= float64(percentRequired)

	case models.QuizPassCriteriaCorrectAnswers:
		numCorrectAnswers := lo.Reduce(submission.Answers, func(count int, answer *models.QuizAnswer, _ int) int {
			return lo.If(answer.Correct, count+1).Else(count)
		}, 0)

		return (float64(numCorrectAnswers) * 100 / float64(len(quiz.Questions))) >= float64(percentRequired)
	}

	return true
}

func (s *QuizSubmissionService) FindTotalPointForUserByCourse(courseCuid, userID string) (int, *e.AppError) {
	totalPoint, err := models.Repository.QuizSubmission.FindTotalPointForUserByCourse(courseCuid, userID)
	if err != nil {
		return -1, e.NewError500(e.Quiz_submission_count_total_point_failed, err.Error())
	}

	return totalPoint, nil
}

func (s *QuizSubmissionService) GetSubmissionSummary(user *models.User, quizIDs []string) ([]dto.QuizSubmissionSummary, *e.AppError) {
	var result []dto.QuizSubmissionSummary
	// Get submission count
	submissionCountMap, err := models.Repository.QuizSubmission.CountQuizSubmisison(user, quizIDs)
	if err != nil {
		return nil, e.NewError500(e.Quiz_check_submission_limit_failed, "Find list user's submissions on quiz error: "+err.Error())
	}

	for quizID, count := range submissionCountMap {
		result = append(result, dto.QuizSubmissionSummary{
			QuizID:       quizID,
			TotalSubmits: count,
		})
	}

	return result, nil
}

func (s *QuizSubmissionService) FindLatestPassedByQuizUID(quizUID string, userID string) (*models.QuizSubmission, *e.AppError) {
	var cacheSubmission models.QuizSubmission
	cacheKey := "latest_passed_" + quizUID + "_" + userID
	if cErr := models.Cache.QuizSubmission.GetByKey(cacheKey, &cacheSubmission); cErr != nil {
		log.Errorf("QuizSubmissionService::FindLatestPassedByQuizUID failed to get latest passed quiz submission cache: %v", cErr)
	}

	if cacheSubmission.ID != "" {
		return &cacheSubmission, nil
	}

	submission, err := models.Repository.QuizSubmission.FindOne(&models.QuizSubmissionQuery{
		QuizUID: &quizUID,
		UserID:  &userID,
		Passed:  util.NewBool(true),
	}, &models.FindOneOptions{
		Preloads: []string{models.AnswersField, util.AnswersQuestionField},
		Sort:     []string{models.CreateAtDESC},
	})
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.Quiz_submission_not_found, "Latest passed quiz submission not found for quiz UID: "+quizUID+" and user ID: "+userID)
		}
		return nil, e.NewError500(e.Quiz_submission_find_failed, "Find passed quiz submissions by quiz UID error: "+err.Error())
	}

	if cErr := models.Cache.QuizSubmission.SetByKey(cacheKey, submission); cErr != nil {
		log.Errorf("QuizSubmissionService::FindLatestPassedByQuizUID failed to set latest quiz submission cache: %v", cErr)
	}
	return submission, nil
}
