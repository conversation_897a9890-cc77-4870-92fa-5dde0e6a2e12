package services

import (
	"context"
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

func createSection(user *models.User, data *dto.CreateSectionRequest) (*models.Section, *e.AppError) {
	section := &models.Section{}
	copier.Copy(section, data)
	if data.UID != nil {
		section.UID = *data.UID
	} else {
		section.UID = util.GenerateId()
	}

	section.OrgID = data.OrgID
	section.UserID = user.ID
	section.LessonCount = 0
	section.Course = nil
	if data.Status != nil {
		section.Status = *data.Status
	} else {
		section.Status = models.SectionStatusDraft
	}

	if err := models.Repository.Section.Create(section, nil); err != nil {
		return nil, e.NewError500(e.Section_create_failed, "Create section failed: "+err.Error())
	}

	return section, nil
}

func (c *SectionService) Create(user *models.User, data *dto.CreateSectionRequest, defaultLesson bool) (*models.Section, *e.AppError) {
	section, err := createSection(user, data)
	if err != nil {
		return nil, err
	}
	if defaultLesson {
		lessonData := dto.CreateSectionRequest{
			OrgID:    section.OrgID,
			CourseID: section.CourseID,
			ParentID: section.ID,
			Title:    "Create your first lesson",
			Note:     "",
			Order:    0,
		}
		if lesson, lErr := createSection(user, &lessonData); lErr != nil {
			return nil, lErr
		} else {
			contentData := dto.LessonContentRequest{
				OrgID:       section.OrgID,
				CourseID:    section.CourseID,
				SectionID:   section.ID,
				LessonID:    lesson.ID,
				Title:       "Your first content",
				Content:     "",
				Duration:    0,
				Type:        models.LessonTypeText,
				Order:       0,
				JsonContent: models.JSONB{},
			}
			if _, lcErr := LessonContent.Create(user, &contentData); lcErr != nil {
				return nil, lcErr
			}
		}
	}

	if cErr := Course.UpdateStats(data.Course, true); cErr != nil {
		return nil, cErr
	}

	if err := handleUpdateStats(section, true); err != nil {
		return nil, err

	}
	return section, nil
}

func copySectionData(section *models.Section, req *dto.CreateSectionRequest) *models.Section {
	if req.UID != nil {
		section.UID = *req.UID
	}
	section.Title = req.Title
	section.Note = req.Note
	section.Order = req.Order
	section.Free = req.Free
	if req.Status != nil {
		section.Status = *req.Status
	} else {
		section.Status = models.SectionStatusDraft
	}
	section.ParentID = req.ParentID
	return section
}

func appendContents(lessonContents []*dto.LessonContentRequest, contents []*dto.LessonContentRequest, u *models.Section) []*dto.LessonContentRequest {
	if contents != nil && len(contents) > 0 && u.ParentID != "" {
		cts := lo.Map(contents, func(c *dto.LessonContentRequest, _ int) *dto.LessonContentRequest {
			c.SectionID = u.ParentID
			c.LessonID = u.ID
			c.OrgID = u.OrgID
			return c
		})
		lessonContents = append(lessonContents, cts...)
	}

	return lessonContents
}

func (s SectionService) UpsertManyLesson(course *models.Course, user *models.User, data []*dto.CreateSectionRequest) ([]*models.Section, *e.AppError) {
	var results []*models.Section
	var lessonContents []*dto.LessonContentRequest
	for _, d := range data {
		if d.ID != nil {
			l, _ := s.FindOne(&models.SectionQuery{ID: d.ID, IncludeDeleted: util.NewBool(false)}, nil)
			if l != nil {
				u := copySectionData(l, d)
				if err := models.Repository.Section.Update(u, nil); err != nil {
					return nil, e.NewError500(e.Lesson_update_failed, "Update sections failed: "+err.Error())
				}

				lessonContents = appendContents(lessonContents, d.Contents, u)
				results = append(results, u)
				continue
			}
		}
		lesson := copySectionData(&models.Section{}, d)
		lesson.CourseID = course.ID
		lesson.UserID = user.ID
		lesson.OrgID = course.OrgID
		if err := models.Repository.Section.Create(lesson, nil); err != nil {
			return nil, e.NewError500(e.Lesson_create_failed, "Create sections failed: "+err.Error())
		}
		lessonContents = appendContents(lessonContents, d.Contents, lesson)
		results = append(results, lesson)
	}

	if len(lessonContents) > 0 {
		if _, cErr := LessonContent.UpsertMany(course, user, lessonContents); cErr != nil {
			return nil, cErr
		}
	}

	if cErr := Course.UpdateStats(course, true); cErr != nil {
		return nil, cErr
	}

	for _, l := range results {
		if err := handleUpdateStats(l, true); err != nil {
			return nil, err
		}
	}

	return results, nil
}

func (c *SectionService) Update(section *models.Section, data *dto.UpdateSectionRequest) (*models.Section, *e.AppError) {
	section.Status = *data.Status
	section.Title = data.Title
	section.Order = data.Order
	section.Free = data.Free
	var sLessons []*models.Section

	if len(data.Lessons) > 0 {
		lo.ForEach(data.Lessons, func(item *dto.CreateSectionRequest, index int) {
			item.CourseID = section.CourseID
			item.ParentID = section.ID
		})
		if lessons, lErr := Section.UpsertManyLesson(data.Course, data.User, data.Lessons); lErr != nil {
			return nil, lErr
		} else {
			lessonCount := 0
			activeLessonCount := 0
			for _, lesson := range lessons {
				if lesson.DeleteAt != 0 {
					continue
				}

				lessonCount++
				if lesson.Status == models.SectionStatusPublish {
					activeLessonCount++
				}
			}
			sLessons = lessons
			section.LessonCount = lessonCount
			section.ActiveLesson = activeLessonCount
		}
	}

	// section type is lesson then update lesson content
	var lessonContents []*dto.LessonContentRequest
	lessonContents = appendContents(lessonContents, data.Contents, section)
	if len(lessonContents) > 0 {
		if _, cErr := LessonContent.UpsertMany(data.Course, data.User, lessonContents); cErr != nil {
			return nil, cErr
		}
	}

	if err := models.Repository.Section.Update(section, nil); err != nil {
		return nil, e.NewError500(e.Section_update_failed, "Update section failed: "+err.Error())
	}

	if cErr := Course.UpdateStats(data.Course, true); cErr != nil {
		return nil, cErr
	}

	if err := handleUpdateStats(section, true); err != nil {
		return nil, err
	}

	section.Lessons = sLessons
	return section, nil
}

func (s SectionService) BulkUpdate(request *dto.BulkUpdateSectionRequest, user *models.User) ([]*models.Section, *e.AppError) {
	if request.Sections == nil || (request.Sections != nil && len(request.Sections) <= 0) {
		return nil, e.NewError400(e.Sections_missing, "sections are missing")
	}
	course, cErr := Course.FindById(request.CourseID, false, nil)
	if cErr != nil {
		return nil, cErr
	}

	if perErr := Course.CanUpdateCourse(course, user, models.CoursePerUpdate); perErr != nil {
		return nil, perErr
	}

	for _, item := range request.Sections {
		if item.ID == nil {
			sec, sErr := s.Create(user, &dto.CreateSectionRequest{
				OrgID:    item.OrgID,
				UID:      item.UID,
				Title:    item.Title,
				Note:     item.Note,
				Order:    item.Order,
				Free:     item.Free,
				Status:   item.Status,
				Course:   course,
				CourseID: course.ID,
			}, false)
			if sErr != nil {
				return nil, sErr
			}
			item.ID = &sec.ID
		}
	}

	ids := lo.Map(request.Sections, func(item *dto.UpdateSectionRequest, _ int) *string {
		return item.ID
	})

	sections, sErr := models.Repository.Section.FindMany(&models.SectionQuery{IDIn: ids, CourseID: util.NewString(course.ID)}, nil)
	if sErr != nil {
		return nil, e.NewError500(e.Section_find_page_failed, "find many failed"+sErr.Error())
	}

	var results []*models.Section
	for _, val := range sections {
		req, ok := lo.Find(request.Sections, func(item *dto.UpdateSectionRequest) bool {
			return *item.ID == val.ID
		})
		if ok {
			req.Course = course
			req.User = user
			if s, uErr := Section.Update(val, req); uErr != nil {
				return nil, uErr
			} else {
				// if err := Section.CheckLessonIfExistInCondition(course, s.UID); err != nil {
				// 	return nil, err
				// }
				results = append(results, s)
			}
		}
	}

	for _, l := range results {
		if err := handleUpdateStats(l, true); err != nil {
			return nil, err
		}
	}

	return results, nil
}

func (c *SectionService) FindOne(query *models.SectionQuery, options *models.FindOneOptions) (*models.Section, *e.AppError) {
	if section, err := models.Repository.Section.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Section_not_found, err.Error())
		}
		return nil, e.NewError500(e.Section_find_one_failed, err.Error())
	} else {
		// section.ParentID == "" then this is section
		if section.ParentID == "" {
			lQuery := &models.SectionQuery{
				ParentID:       util.NewString(section.ID),
				IncludeDeleted: util.NewBool(false),
			}
			lOptions := &models.FindPageOptions{
				Page:     util.PageMin,
				PerPage:  util.PerPageMax,
				Preloads: []string{},
				Sort:     []string{models.OrderASC},
			}
			if lessons, _, lErr := Section.FindPage(lQuery, lOptions); lErr != nil {
				return nil, e.NewError404(e.Lesson_find_page_failed, lErr.Error())
			} else {
				lss, glErr := LessonContent.GetLessonContentByLessons(lessons)
				if glErr != nil {
					return section, glErr
				}
				section.Lessons = lss
			}
			return section, nil
		} else {
			lss, glErr := LessonContent.GetLessonContentByLessons([]*models.Section{section})
			if glErr != nil {
				return section, glErr
			}
			return lss[0], nil
		}
	}
}

func (s *SectionService) FindPage(query *models.SectionQuery, options *models.FindPageOptions) ([]*models.Section, *models.Pagination, *e.AppError) {
	// var course *models.Course
	// if query.CourseID != nil {
	// 	courseFind, appErr := Course.FindOne(&models.CourseQuery{
	// 		ID: query.CourseID,
	// 	}, nil)
	// 	if appErr != nil {
	// 		return nil, nil, appErr
	// 	}

	// 	course = courseFind
	// }

	shouldPreloadLesson := false
	if options.Preloads == nil {
		options.Preloads = []string{}
	}
	if lo.Contains(options.Preloads, models.LessonField) {
		shouldPreloadLesson = true
		options.Preloads = util.RemoveElement(options.Preloads, models.LessonField)
	}
	lessonQuery := *query
	if shouldPreloadLesson {
		query.ParentIDNull = util.NewBool(true)
		sections, pagination, err := models.Repository.Section.FindPage(query, options)
		if err != nil {
			return nil, nil, e.NewError500(e.Section_find_page_failed, "find sections failed: "+err.Error())
		}
		lessonQuery.ParentIDNotNull = util.NewBool(true)
		lessons, _, err := models.Repository.Section.FindPage(&lessonQuery, options)
		if err != nil {
			return nil, nil, e.NewError500(e.Section_find_page_failed, "find lessons failed: "+err.Error())
		}

		sections = lo.Map(sections, func(c *models.Section, _ int) *models.Section {
			lss := lo.Filter(lessons, func(l *models.Section, _ int) bool {
				// if l.UID == course.Props.CertificateCondition.RequiredLessonUID {
				// 	l.IsCertificateCondition = true
				// }
				return l.ParentID == c.ID
			})
			c.Lessons = lss
			return c
		})
		return sections, pagination, nil
	} else {
		if sections, pagination, err := models.Repository.Section.FindPage(query, options); err != nil {
			return nil, nil, e.NewError500(e.Section_find_page_failed, err.Error())
		} else {
			return sections, pagination, nil
		}
	}
}
func (s *SectionService) FindMany(query *models.SectionQuery, options *models.FindManyOptions) ([]*models.Section, *e.AppError) {
	shouldPreloadLesson := false
	if options.Preloads == nil {
		options.Preloads = []string{}
	}
	if lo.Contains(options.Preloads, models.LessonField) {
		shouldPreloadLesson = true
		options.Preloads = util.RemoveElement(options.Preloads, models.LessonField)
	}
	lessonQuery := *query
	if shouldPreloadLesson {
		query.ParentIDNull = util.NewBool(true)
		sections, err := models.Repository.Section.FindMany(query, options)
		if err != nil {
			return nil, e.NewError500(e.Section_find_many_failed, "find sections failed: "+err.Error())
		}
		lessonQuery.ParentIDNotNull = util.NewBool(true)
		lessons, err := models.Repository.Section.FindMany(&lessonQuery, options)
		if err != nil {
			return nil, e.NewError500(e.Section_find_many_failed, "find lessons failed: "+err.Error())
		}

		sections = lo.Map(sections, func(c *models.Section, _ int) *models.Section {
			lss := lo.Filter(lessons, func(l *models.Section, _ int) bool {
				return l.ParentID == c.ID
			})
			c.Lessons = lss
			return c
		})
		return sections, nil
	} else {
		if sections, err := models.Repository.Section.FindMany(query, options); err != nil {
			return nil, e.NewError500(e.Section_find_many_failed, err.Error())
		} else {
			return sections, nil
		}
	}
}

func (s *SectionService) Delete(section *models.Section) *e.AppError {
	if err := models.Repository.Section.Delete(section.ID, nil); err != nil {
		return e.NewError500(e.Section_delete_failed, err.Error())
	}

	if section.ParentID != "" {
		rootSection, findErr := s.FindOne(&models.SectionQuery{ID: &section.ParentID}, nil)
		if findErr != nil {
			return findErr
		}
		if err := s.UpdateStats(rootSection, true); err != nil {
			return err
		}
	}

	return nil
}

func (s *SectionService) UpdateStats(section *models.Section, requireSave bool) *e.AppError {
	query := &models.SectionQuery{
		ParentID:       util.NewString(section.ID),
		IncludeDeleted: util.NewBool(false),
	}
	// count total lesson
	if lessonCount, err := models.Repository.Section.Count(query); err != nil {
		return e.NewError500(e.Lesson_update_stats_failed, "Update stat failed: "+err.Error())
	} else {
		section.LessonCount = int(lessonCount)
	}

	// count active lesson
	query.Status = util.NewT(models.SectionStatusPublish)
	activeLesson, findErr := models.Repository.Section.FindMany(query, &models.FindManyOptions{})
	if findErr != nil {
		return e.NewError500(e.Lesson_update_stats_failed, "Update stat failed: "+findErr.Error())
	}

	section.LessonCount = len(activeLesson)

	//count lesson content type
	sumActiveTextLesson, sumActiveVideoLesson, sumActiveQuizLesson, sumActivePdfLesson, sumActivePPLesson, sumActiveDocLesson, sumActiveEmbedLesson := 0, 0, 0, 0, 0, 0, 0
	lo.ForEach(activeLesson, func(item *models.Section, _ int) {
		sumActiveTextLesson += item.CountTextLesson
		sumActiveVideoLesson += item.CountVideoLesson
		sumActiveQuizLesson += item.CountQuizLesson
		sumActivePdfLesson += item.CountPdfLesson
		sumActivePPLesson += item.CountPPLesson
		sumActiveDocLesson += item.CountDocLesson
		sumActiveEmbedLesson += item.CountEmbedLesson
	})

	section.CountActiveTextLesson = sumActiveTextLesson
	section.CountActiveVideoLesson = sumActiveVideoLesson
	section.CountActiveQuizLesson = sumActiveQuizLesson
	section.CountActivePdfLesson = sumActivePdfLesson
	section.CountActivePPLesson = sumActivePPLesson
	section.CountActiveDocLesson = sumActiveDocLesson
	section.CountActiveEmbedLesson = sumActiveEmbedLesson

	//count all
	query.Status = nil
	lessonOfSection, findErr := models.Repository.Section.FindMany(query, &models.FindManyOptions{})
	if findErr != nil {
		return e.NewError500(e.Lesson_update_stats_failed, "Update stat failed: "+findErr.Error())
	}

	sumTextLesson, sumVideoLesson, sumQuizLesson, sumPdfLesson, sumPPLesson, sumDocLesson, sumEmbedLesson := 0, 0, 0, 0, 0, 0, 0

	lo.ForEach(lessonOfSection, func(item *models.Section, _ int) {
		sumTextLesson += item.CountTextLesson
		sumVideoLesson += item.CountVideoLesson
		sumQuizLesson += item.CountQuizLesson
		sumPdfLesson += item.CountPdfLesson
		sumPPLesson += item.CountPPLesson
		sumDocLesson += item.CountDocLesson
		sumEmbedLesson += item.CountEmbedLesson
	})

	section.CountTextLesson = sumTextLesson
	section.CountVideoLesson = sumVideoLesson
	section.CountQuizLesson = sumQuizLesson
	section.CountPdfLesson = sumPdfLesson
	section.CountPPLesson = sumPPLesson
	section.CountDocLesson = sumDocLesson
	section.CountEmbedLesson = sumEmbedLesson

	if requireSave {
		if err := models.Repository.Section.Update(section, nil); err != nil {
			return e.NewError500(e.Section_update_failed, "Update section: "+err.Error())
		}
	}

	return nil
}

func (s *SectionService) UpdateStatsLesson(section *models.Section, requireSave bool) *e.AppError {
	mapLessonContent, err := models.Repository.LessonContent(context.TODO()).CountEachTypeForSection(section, nil)
	if err != nil {
		return e.NewError500(e.Lesson_update_stats_failed, "Update stat failed: "+err.Error())
	}

	section.CountTextLesson = mapLessonContent[models.LessonTypeText]
	section.CountVideoLesson = mapLessonContent[models.LessonTypeVideo]
	section.CountQuizLesson = mapLessonContent[models.LessonTypeQuiz]
	section.CountPdfLesson = mapLessonContent[models.LessonTypePDF]
	section.CountPPLesson = mapLessonContent[models.LessonTypePP]
	section.CountDocLesson = mapLessonContent[models.LessonTypeDoc]
	section.CountEmbedLesson = mapLessonContent[models.LessonTypeEmbedded]

	lessonContentQuery := &models.LessonContentQuery{
		LessonID: util.NewString(section.ID),
	}

	lessonContents, err := models.Repository.LessonContent(context.TODO()).FindMany(lessonContentQuery, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError404(e.Lesson_content_not_found, err.Error())
		}
		return e.NewError500(e.Lesson_content_find_failed, err.Error())
	}

	lessonContentIds := lo.Map(lessonContents, func(item *models.LessonContent, _ int) string {
		return item.ID
	})

	query := models.QuizRelationQuery{RelatedEntityType: util.NewT(models.QuizRelationEntityLessonContent), RelatedEntityIDIn: lessonContentIds}

	if quizCount, err := models.Repository.QuizRelation.Count(&query); err != nil {
		return e.NewError500(e.Lesson_update_stats_failed, "Update stat failed: "+err.Error())
	} else {
		section.CountQuizLesson = int(quizCount)
	}

	if requireSave {
		if err := models.Repository.Section.Update(section, nil); err != nil {
			return e.NewError500(e.Section_update_failed, "Update section: "+err.Error())
		}
	}

	return nil

}

func handleUpdateStats(section *models.Section, requireSave bool) *e.AppError {
	// section
	if section.ParentID == "" {
		return Section.UpdateStats(section, requireSave)
	}
	// lesson
	if err := Section.UpdateStatsLesson(section, requireSave); err != nil {
		return err
	}
	rootSection, findErr := Section.FindOne(&models.SectionQuery{ID: &section.ParentID}, nil)
	if findErr != nil {
		return findErr
	}
	if err := Section.UpdateStats(rootSection, requireSave); err != nil {
		return err
	}
	return nil

}

func handleUpdateStatsBySchema(schema string, section *models.Section, requireSave bool) *e.AppError {
	// section
	if section.ParentID == "" {
		return Section.UpdateStats(section, requireSave)
	}
	// lesson
	if err := Section.UpdateStatsLesson(section, requireSave); err != nil {
		return err
	}
	rootSection, findErr := Section.FindOne(&models.SectionQuery{ID: &section.ParentID}, nil)
	if findErr != nil {
		return findErr
	}
	if err := Section.UpdateStats(rootSection, requireSave); err != nil {
		return err
	}
	return nil

}

// func (s *SectionService) CheckLessonIfExistInCondition(course *models.Course, lessonUID string) *e.AppError {
// 	if course.Props.CertificateCondition != nil && lessonUID == course.Props.CertificateCondition.RequiredLessonUID {
// 		course.Props.CertificateCondition.RequiredLessonUID = ""
// 		course.Props.CertificateCondition.CompletedRequiredLesson = false
// 		if ucErr := models.Repository.Course(context.TODO()).Update(course, nil); ucErr != nil {
// 			return e.NewError500(e.Update_course_failed, ucErr.Error())
// 		}
// 	}

// 	return nil
// }
