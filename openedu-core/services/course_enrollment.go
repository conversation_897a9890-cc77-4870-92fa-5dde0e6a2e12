package services

import (
	"context"
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *CourseEnrollmentService) FindByID(id string, includeDeleted bool, options *models.FindOneOptions) (*models.CourseEnrollment, *e.AppError) {
	if courseEnrollment, err := models.Repository.CourseEnrollment(context.TODO()).FindOne(&models.CourseEnrollmentQuery{ID: &id}, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Course_enrollment_not_found, err.Error())
		}
		return nil, e.NewError500(e.Find_course_enrollment_failed, err.Error())
	} else {
		return courseEnrollment, nil
	}
}

func (s *CourseEnrollmentService) FindOne(query *models.CourseEnrollmentQuery, options *models.FindOneOptions) (*models.CourseEnrollment, *e.AppError) {
	if courseEnrollment, err := models.Repository.CourseEnrollment(context.TODO()).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Course_enrollment_not_found, err.Error())
		}
		return nil, e.NewError500(e.Find_course_enrollment_failed, err.Error())
	} else {
		return courseEnrollment, nil

	}
}

func (s *CourseEnrollmentService) FindPage(query *models.CourseEnrollmentQuery, options *models.FindPageOptions) ([]*models.CourseEnrollment, *models.Pagination, *e.AppError) {
	if courseEnrollments, pagination, err := models.Repository.CourseEnrollment(context.TODO()).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Find_page_course_enrollment_failed, err.Error())
	} else {
		return courseEnrollments, pagination, nil
	}
}

func (s *CourseEnrollmentService) FindMany(query *models.CourseEnrollmentQuery, options *models.FindManyOptions) ([]*models.CourseEnrollment, *e.AppError) {
	if courseEnrollments, err := models.Repository.CourseEnrollment(context.TODO()).FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Find_many_course_enrollment_failed, err.Error())
	} else {
		return courseEnrollments, nil
	}
}

func (s *CourseEnrollmentService) Update(courseEnrollment *models.CourseEnrollment, data *dto.UpdateCourseEnrollmentRequest) *e.AppError {
	courseEnrollment.Blocked = data.Blocked
	courseEnrollment.BlockedReason = data.BlockedReason
	if err := models.Repository.CourseEnrollment(context.TODO()).Update(courseEnrollment, nil); err != nil {
		return e.NewError500(e.Update_course_enrollment_failed, err.Error())
	}
	return nil
}

func (s *CourseEnrollmentService) Count(query *models.CourseEnrollmentQuery) (int64, *e.AppError) {
	if num, err := models.Repository.CourseEnrollment(context.TODO()).Count(query); err != nil {
		return 0, e.NewError500(e.Count_course_enrollment_failed, err.Error())
	} else {
		return num, nil
	}
}

func (s *CourseEnrollmentService) VerifyCreateCourseEnrollment(orgID string, orgSchema string, user *models.User, course *models.Course, source string, ref string) (*models.CourseEnrollment, *e.AppError) {
	// Check user already enrolled in this course
	findEnrollmentQuery := &models.CourseEnrollmentQuery{UserID: &user.ID, CourseCuid: &course.Cuid}
	if exist, err := models.Repository.CourseEnrollment(context.TODO()).FindOne(findEnrollmentQuery, &models.FindOneOptions{}); err == nil && exist != nil {
		if exist.Blocked {
			return nil, e.NewError400(e.You_have_been_blocked_from_this_course, "You have been blocked form this course")
		}
		return exist, nil
	}

	// Check if user already fill the form
	if err := Form.HandleEventForCourse(user, course, models.FormEventRegisterCourse); err != nil {
		if err.ErrCode == e.Course_register_form_required {
			return nil, e.NewError400(e.Course_register_form_required, "You must fill the register form")
		}
		return nil, err
	}

	if course.PriceSettings.IsPay {
		orderItem, err := OrderItem.FindOne(&models.OrderItemQuery{UserID: &user.ID, EntityCuid: &course.Cuid, Status: util.NewT(models.OrderStatusSuccess)}, &models.FindOneOptions{})
		if err != nil {
			if err.ErrCode == e.OrderItemNotFound {
				return nil, e.NewError400(e.OrderItemNotFound, "You should order this course first or paid first before enrolling")
			}
			return nil, e.NewError500(e.OrderFindOrderItemWithCourseFailed, "You should order this course first or paid first before enrolling")
		}
		if orderItem == nil {
			return nil, e.NewError400(e.OrderStatusNotAllow, "Course must be paid first before enrolling")
		}

		// get source from order's tracking
		trackings, _, fErr := communication.Tracking.FindPaginationTracking(&communicationdto.TrackingQuery{
			ActorID:  &user.ID,
			Verb:     util.NewT(communicationdto.Ordered),
			Object:   util.NewT(communicationdto.CourseModelName),
			ObjectID: &course.Cuid,
			Context:  util.NewT(communicationdto.SourceContext),
		}, &communicationdto.FindPageOptions{})

		if fErr != nil {
			log.Errorf("Fail to find tracking ID (actorID=%s, verb=%s, object=%s, objectID=%s, context=%s from communication: %v",
				user.ID,
				models.Ordered,
				models.CourseModelName,
				course.Cuid,
				models.SourceContext,
				fErr,
			)
		}

		if len(trackings) > 0 {
			log.Error("Context value for order tracking: %v", trackings[0].ContextValue)
			src, ok := trackings[0].ContextValue.(string) // only one records for order
			if ok {
				source = src
			}
		}
	}

	courseEnrollment := &models.CourseEnrollment{
		CourseCuid:    course.Cuid,
		UserID:        user.ID,
		OrgID:         orgID,
		Blocked:       false,
		BlockedReason: "",
	}

	if err := models.Repository.CourseEnrollment(context.TODO()).Upsert(courseEnrollment, nil); err != nil {
		return nil, e.NewError500(e.Create_course_enrollment_failed, err.Error())
	}

	// add tracking enroll here
	trackingReq := &communicationdto.TrackingRequest{
		ActorID:      user.ID,
		Verb:         communicationdto.Enrolled,
		Object:       communicationdto.CourseModelName,
		ObjectID:     course.Cuid,
		Context:      communicationdto.SourceContext,
		ContextValue: communicationdto.GetValidSourceType(source),
		OrgID:        orgID,
		OrgSchema:    orgSchema,
	}

	if ref != "" {
		trackingReq.ContextValue = ref
	} else {
		trackingReq.ContextValue = communicationdto.GetValidSourceType(source)
	}

	if err := communication.Tracking.CreateTracking(trackingReq); err != nil {
		log.Errorf("Create tracking failed: %v", err)
	}

	return courseEnrollment, nil
}

func (s *CourseEnrollmentService) CanActionCourseEnrollment(courseCuid string, currentUser *models.User, org *models.Organization) *e.AppError {
	if !currentUser.IsSysAdmin() && !currentUser.IsOrgAdmin(org.ID) {
		if !currentUser.IsCreator(org.ID) {
			return e.NewError400(e.FORBIDDEN, "You are not allowed to update course enrollment")
		}

		coursePartner, cpErr := models.Repository.CoursePartner.FindMany(&models.CoursePartnerQuery{CourseID: &courseCuid, IsActive: util.NewBool(true)}, &models.FindManyOptions{})
		if cpErr != nil {
			return e.NewError500(e.Find_course_partner_failed, cpErr.Error())
		}

		if !lo.ContainsBy(coursePartner, func(item *models.CoursePartner) bool { return item.PartnerID == currentUser.ID }) {
			return e.NewError400(e.FORBIDDEN, "You are not allowed to update course enrollment")
		}
	}
	return nil
}

func (s *CourseEnrollmentService) EnrollUserFromFormSession(form *models.Form, formSession *models.FormSession, org *models.Organization, source string, ref string, refUser string) (*models.CourseEnrollment, *e.AppError) {
	if formSession.FormID != form.ID {
		return nil, e.NewError500(e.Create_course_enrollment_failed, "Form session is not belong to this form")
	}

	if !form.IsFormRegisterCourse() {
		return nil, e.NewError500(e.Create_course_enrollment_failed, "This form is not course registration form")
	}

	if formSession.FormRelationID == nil {
		return nil, e.NewError500(e.Create_course_enrollment_failed, "Cannot extract course CUID from form session")
	}

	formRelation, err := models.Repository.FormRelation.FindByID(*formSession.FormRelationID, nil)
	if err != nil {
		return nil, e.NewError500(e.Create_course_enrollment_failed, "Get form relation error: "+err.Error())
	}

	query := &models.CourseQuery{ID: &formRelation.RelatedEntityID}
	course, err := models.Repository.Course(context.TODO()).FindOne(query, nil)
	if err != nil {
		return nil, e.NewError500(e.Create_course_enrollment_failed, "Get course by form error: "+err.Error())
	}

	user, isNewUser, appErr := s.getUserForEnrollment(form, formSession)
	if appErr != nil {
		return nil, appErr
	}

	if refUser != "" {
		trackData := communicationdto.TrackingRequest{
			ActorID:      user.ID,
			Verb:         communicationdto.Referred,
			Object:       communicationdto.UserModelName,
			ObjectID:     refUser,
			Context:      communicationdto.CourseContext,
			ContextValue: course.Cuid,
			OrgID:        org.ID,
			OrgSchema:    org.Schema,
			IsValid:      false,
		}

		if err := communication.Tracking.CreateTracking(&trackData); err != nil {
			return nil, e.NewError500(e.External_call_error, "Failed to ref for user")
		}
	}

	if isNewUser {
		if aErr := s.sendEmailForNewUser(org, user); aErr != nil {
			log.Errorf("Send email for new user enrolled from form session failed: %v", aErr)
		}
	}

	return s.VerifyCreateCourseEnrollment(org.ID, org.Schema, user, course, source, ref)
}

func (s *CourseEnrollmentService) getUserForEnrollment(form *models.Form, formSession *models.FormSession) (*models.User, bool, *e.AppError) {
	if formSession.UserID != nil {
		user, err := models.Repository.User.FindByID(*formSession.UserID)
		if err != nil {
			return nil, false, e.NewError500(e.Create_course_enrollment_failed, "Get user by form error: "+err.Error())
		}
		return user, false, nil
	}

	userInfo := formSession.ExtractUserInfo()
	if userInfo.Email == "" {
		return nil, false, e.NewError500(e.Create_course_enrollment_failed, "User does not have email")
	}

	var user *models.User
	createNewUser := false
	if userByEmail, uErr := models.Repository.User.FindByEmailWithOpts(userInfo.Email, nil); uErr != nil {
		if !errors.Is(uErr, gorm.ErrRecordNotFound) {
			return nil, false, e.NewError500(e.Error_find_user_by_email_failed, uErr.Error())
		}
		if userOrg, cErr := User.CreateUserForOrg(&dto.CreateUser{
			Email:       userInfo.Email,
			Username:    util.GenerateId(),
			Password:    util.GenerateId(),
			DisplayName: userInfo.DisplayName,
			OrgID:       &form.OrgID,
			Phone:       userInfo.Phone,
		}); cErr != nil {
			return nil, false, cErr
		} else {
			user = userOrg
			createNewUser = true
		}
	} else {
		user = userByEmail
	}
	return user, createNewUser, nil
}

func (s *CourseEnrollmentService) sendEmailForNewUser(org *models.Organization, user *models.User) *e.AppError {
	userToken := &models.UserToken{
		UserID:     &user.ID,
		Email:      user.Email,
		User:       user,
		Token:      util.GenerateToken(),
		ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		Event:      models.EventExternalRegister,
		SendEmail:  util.GetCurrentTime(),
	}
	if err := models.Repository.UserToken.Create(userToken, nil); err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	go func() {
		mailParams := communicationdto.MapEmailParams{
			communicationdto.EmailParamUserToken: userToken,
		}
		if _, err := communication.Email.SendEmail(&communicationdto.SendEmailRequest{
			User:        user.IntoComm(),
			Org:         org.IntoComm(),
			Event:       communicationdto.EventExternalRegister,
			ExtendDatas: mailParams,
			IsQueue:     true,
		}); err != nil {
			log.ErrorWithAlertf("CourseEnrollmentService.sendEmailForNewUser::Send email failed: %v", err)
		}
	}()
	return nil
}
