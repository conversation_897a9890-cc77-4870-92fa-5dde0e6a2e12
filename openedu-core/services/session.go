package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/e"

	"gorm.io/gorm"
)

func (s *SessionService) FindOne(query *models.SessionQuery, options *models.FindOneOptions) (*models.Session, *e.AppError) {
	if session, sErr := models.Repository.Session.FindOne(query, options); sErr != nil {
		if errors.Is(sErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Error_auth_session_not_found, "Session not found")
		}
		return nil, e.NewError500(e.Error_find_session_failed, sErr.Error())
	} else {
		return session, nil
	}
}

func (s *SessionService) Upsert(session *models.Session) *e.AppError {
	if err := models.Repository.Session.Upsert(session, nil); err != nil {
		return e.NewError500(e.Error_upsert_session_failed, err.Error())
	}
	return nil
}
