package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/e"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *UserSummaryService) GetOrgsAsWriter(user *models.User) ([]*models.SimpleOrganization, *e.AppError) {
	orgIDs := []string{}
	for _, userRole := range user.Roles {
		if user.IsOrgWriter(userRole.OrgID) || user.IsOrgEditor(userRole.OrgID) {
			orgIDs = append(orgIDs, userRole.OrgID)
		}
	}

	if len(orgIDs) <= 0 {
		return []*models.SimpleOrganization{}, nil
	}

	uniqueOrgIDs := lo.Uniq(orgIDs)

	orgs, oErr := models.Repository.Organization.FindMany(&models.OrganizationQuery{
		IDIn: uniqueOrgIDs,
	}, &models.FindManyOptions{})

	if oErr != nil {
		return nil, e.NewError500(e.Organization_find_many_failed, oErr.Error())
	}
	writerInOrgs := lo.Map(orgs, func(org *models.Organization, _ int) *models.SimpleOrganization {
		return org.ToSimple()
	})

	return writerInOrgs, nil
}

func (s *UserSummaryService) FindMany(query *models.UserSummaryQuery, options *models.FindManyOptions) ([]*models.UserSummary, *e.AppError) {
	userSummaries, err := models.Repository.UserSummary.FindMany(query, options)
	if err != nil {
		return nil, e.NewError500(e.User_summary_find_many_failed, err.Error())
	}
	return userSummaries, nil
}

func (s *UserSummaryService) FindOne(query *models.UserSummaryQuery, options *models.FindOneOptions) (*models.UserSummary, *e.AppError) {
	userSummary, err := models.Repository.UserSummary.FindOne(query, options)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.User_summary_not_found, err.Error())
		}
		return nil, e.NewError500(e.User_summary_find_one_failed, err.Error())
	}
	return userSummary, nil
}

func (s *UserSummaryService) Upsert(userSummary *models.UserSummary, trans *gorm.DB) *e.AppError {
	err := models.Repository.UserSummary.Upsert(userSummary, trans)
	if err != nil {
		return e.NewError500(e.User_summary_upsert_failed, err.Error())
	}
	return nil
}
