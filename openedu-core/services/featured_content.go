package services

import (
	"fmt"
	"github.com/samber/lo"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"strings"
)

func (s *FeaturedContentService) UpsertManyFeaturedContent(request *dto.BulkUpdateFeaturedContent) ([]*models.FeaturedContent, *e.AppError) {
	currentUser := app.GetLoggedUser(s.ctx)
	// Check if system admin can edit all content
	// check if org admin they can edit their org content
	if !currentUser.IsSysAdmin() {
		if !currentUser.IsOrgAdmin(request.OrgID) {
			return nil, e.NewError400(e.Organization_need_permission_to_edit, "org admin/mod required to update")
		}
	}

	//if len(request.Entities) == 0 {
	//	return nil, e.NewError400(e.FeaturedContentContentRequired, "len(request.Entities) == 0")
	//}
	entityIDs := lo.Map(request.Entities, func(item dto.FeaturedContent, _ int) string {
		return item.EntityID
	})

	exits, eErr := models.Repository.FeaturedContent.FindMany(&models.FeaturedContentQuery{
		OrgID:      util.NewString(request.OrgID),
		Type:       util.NewT(request.Type),
		EntityType: util.NewT(request.EntityType),
	}, nil)
	if eErr != nil {
		return nil, e.NewError400(e.FeaturedContentFindAllFailed, "UpsertManyFeaturedContent: "+eErr.Error())
	}

	// to create
	toDelete := lo.Filter(exits, func(item *models.FeaturedContent, _ int) bool {
		return !lo.Contains(entityIDs, item.EntityID)
	})

	toCreate := make([]*models.FeaturedContent, 0)
	for _, entity := range request.Entities {
		found, ok := lo.Find(exits, func(item *models.FeaturedContent) bool {
			return item.EntityID == entity.EntityID
		})
		if ok {
			found.Order = entity.Order
			if uErr := models.Repository.FeaturedContent.Update(found, nil); uErr != nil {
				return nil, e.NewError500(e.FeaturedContentCreateFailed, "UpsertManyFeaturedContent.Update: "+uErr.Error())
			}
		} else {
			content := &models.FeaturedContent{
				OrgID:      request.OrgID,
				EntityID:   entity.EntityID,
				EntityType: request.EntityType,
				Order:      entity.Order,
				Type:       request.Type,
			}
			toCreate = append(toCreate, content)
		}
	}

	if len(toDelete) > 0 {
		deleteIDs := lo.Map(toDelete, func(item *models.FeaturedContent, _ int) string {
			return item.ID
		})
		if count, dErr := models.Repository.FeaturedContent.DeleteMany(&models.FeaturedContentQuery{IDIn: deleteIDs}, nil); dErr != nil {
			return nil, e.NewError500(e.FeaturedContentDeleteManyFailed, fmt.Sprintf("DeleteMany: %d %s", count, dErr.Error()))
		}
	}

	if len(toCreate) > 0 {
		if cErr := models.Repository.FeaturedContent.CreateMany(toCreate, nil); cErr != nil {
			return nil, e.NewError500(e.FeaturedContentCreateManyFailed, fmt.Sprintf("CreateMany: %s", cErr.Error()))
		}
	}

	cacheKey := strings.Join([]string{request.OrgID, string(request.EntityType), string(request.Type)}, "_")
	models.Cache.FeaturedContent.DeleteByKey(cacheKey)
	return nil, nil
}

func (s *FeaturedContentService) FindPage(
	query *models.FeaturedContentQuery,
	options *models.FindPageOptions) ([]*models.FeaturedContent, *models.Pagination, *e.AppError) {
	user := app.GetLoggedUser(s.ctx)
	org := app.GetOrganization(s.ctx)
	if contents, pagination, err := models.Repository.FeaturedContent.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.FeaturedContentFindPageFailed, "FindPage: "+err.Error())
	} else {
		if len(contents) > 0 {
			// group by entity type first
			grouEntityByType := lo.Reduce(contents, func(arr map[models.ModelName][]*models.FeaturedContent, item *models.FeaturedContent, _ int) map[models.ModelName][]*models.FeaturedContent {
				var tmp []*models.FeaturedContent
				if len(arr[item.EntityType]) > 0 {
					tmp = arr[item.EntityType]
				}
				tmp = append(tmp, item)
				return arr
			}, map[models.ModelName][]*models.FeaturedContent{})
			for key, val := range grouEntityByType {
				if loadErr := loadEntities(user, org, key, val); loadErr != nil {
					return nil, nil, e.NewError500(e.FeaturedContentLoadEntityFailed, loadErr.Error())
				}
			}

		}
		return contents, pagination, nil
	}
}

func (s *FeaturedContentService) FindByEntityTypeAndType(
	orgID string,
	entityType models.ModelName,
	contentType models.FeaturedContentType,
) ([]*models.FeaturedContent, *e.AppError) {
	user := app.GetLoggedUser(s.ctx)
	org := app.GetOrganization(s.ctx)

	// get from cache
	//cacheKey := strings.Join([]string{orgID, string(entityType), string(contentType)}, "_")
	//var contents []*models.FeaturedContent
	//var contentCache []interface{}
	//if val := models.Cache.FeaturedContent.GetByKey(cacheKey, &contentCache); val == nil && len(contentCache) > 0 {
	//	models.Cache.Convert(contentCache, &contents)
	//}
	//if len(contents) > 0 {
	//	return contents, nil
	//}

	if dbContents, err := models.Repository.FeaturedContent.FindMany(&models.FeaturedContentQuery{
		EntityType: util.NewT(entityType),
		Type:       util.NewT(contentType),
		OrgID:      util.NewString(orgID),
	}, nil); err != nil {
		return nil, e.NewError500(e.FeaturedContentFindByTypeFailed, "FindByEntityTypeAndType: "+err.Error())
	} else {
		if len(dbContents) > 0 {
			if loadErr := loadEntities(user, org, entityType, dbContents); loadErr != nil {
				return nil, e.NewError500(e.FeaturedContentLoadEntityFailed, loadErr.Error())
			}
			//cacheData := lo.Map(dbContents, func(w *models.FeaturedContent, _ int) interface{} {
			//	return w
			//})
			//models.Cache.FeaturedContent.SetByKey(cacheKey, cacheData)
		}
		return dbContents, nil
	}
}

func loadEntities(user *models.User, org *models.Organization, entityType models.ModelName, contents []*models.FeaturedContent) *e.AppError {
	ids := lo.Map(contents, func(item *models.FeaturedContent, _ int) string {
		return item.EntityID
	})

	switch entityType {
	case models.CourseModelName:
		courses, _, cErr := Course.FindCourseListItems(
			user,
			org,
			&models.PublishCourseQuery{CourseCuidIn: ids},
			&models.FindPageOptions{PerPage: util.MaxPerPage},
		)
		if cErr != nil {
			return cErr
		}
		for _, content := range contents {
			entity, ok := lo.Find(courses, func(item *models.CourseListItem) bool {
				return item.Cuid == content.EntityID
			})
			if ok {
				content.Entity = entity
			}
		}
		break
	case models.BlogModelName:
		blogs, bErr := Blog.GetPublishBlogMany(
			&models.PublishBlogQuery{BlogCuidIn: ids},
			&models.FindManyOptions{Preloads: []string{models.BlogPreloadsUser}})
		if bErr != nil {
			return bErr
		}
		for _, content := range contents {
			entity, ok := lo.Find(blogs, func(item *models.Blog) bool {
				return item.Cuid == content.EntityID
			})
			if ok {
				orgBlog, _ := models.Repository.Organization.FindByID(entity.OrgID, nil)
				if orgBlog != nil {
					entity.Org = org
				}
				content.Entity = entity
			}
		}
		break
	case models.OrganizationModelName:
		for _, content := range contents {
			entity, _ := models.Repository.Organization.FindByID(content.EntityID,
				&models.FindOneOptions{Preloads: []string{models.UserField, models.ThumbnailField, models.BannerField}})
			content.Entity = entity
		}
		break
	default:
		return e.NewError400(e.FeaturedContentUnSupportEntityType, "un-support entity type: "+string(entityType))
	}
	return nil
}
