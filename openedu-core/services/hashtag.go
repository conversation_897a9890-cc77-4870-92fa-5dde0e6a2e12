package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *HashtagService) CreateMany(hashtags []*models.Hashtag, orgID string) *e.AppError {
	hashtags = lo.Map(hashtags, func(hashtag *models.Hashtag, idx int) *models.Hashtag {
		hashtag.Hash = util.Base64Encode(orgID + hashtag.Name)
		formatted, _ := util.RemoveSpecialCharacter(hashtag.Name)
		hashtag.FormattedHashtag = strings.ToLower(strings.TrimSpace(formatted))
		hashtag.OrgID = orgID
		return hashtag
	})
	if err := models.Repository.Hashtag.UpsertMany(hashtags, nil); err != nil {
		log.Error("Api::Hashtag.Create create many hashtag failed", err)
		return e.NewError500(e.Hashtag_create_many_failed, err.Error())
	}
	return nil
}

func (s *HashtagService) FindMany(query *models.HashtagQuery, options *models.FindManyOptions) ([]*models.Hashtag, *e.AppError) {
	hashtags, err := models.Repository.Hashtag.FindMany(query, options)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.Hashtag_not_found, err.Error())
		}
		return nil, e.NewError500(e.Hashtag_find_many_failed, err.Error())
	}
	return hashtags, nil
}

func (s *HashtagService) DeleteMany(query *models.HashtagQuery) *e.AppError {
	_, err := models.Repository.Hashtag.DeleteMany(query, nil)
	if err != nil {
		return e.NewError500(e.Hashtag_delete_many_failed, err.Error())
	}

	return nil
}

func (s *HashtagService) FindPage(query *models.HashtagQuery, options *models.FindPageOptions) ([]*models.Hashtag, *models.Pagination, *e.AppError) {
	if Hashtags, pagination, err := models.Repository.Hashtag.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Hashtag_find_page_failed, err.Error())
	} else {
		return Hashtags, pagination, nil
	}
}
