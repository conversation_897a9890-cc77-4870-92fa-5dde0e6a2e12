package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/ai"
	"openedu-core/pkg/e"

	"gorm.io/gorm"
)

var courseStatusMapByAIStatus = map[ai.GenerateStatus]models.AIStatus{
	ai.StatusManual:     models.AIStatusManual,
	ai.StatusPending:    models.AIStatusPending,
	ai.StatusInProgress: models.AIStatusPending,
	ai.StatusGenerating: models.AIStatusGenerating,
	ai.StatusWaiting:    models.AIStatusWaiting,
	ai.StatusCompleted:  models.AIStatusCompleted,
	ai.StatusFailed:     models.AIStatusFailed,
	ai.StatusStarted:    models.AIStatusCompleted, // ai started -> model completed
	ai.StatusGenerated:  models.AIStatusCompleted, // ai generated -> model completed
}

func (s *AIHistoryService) GetCourseStatusByAIStatus(status ai.GenerateStatus) models.AIStatus {
	if courseStatus, exists := courseStatusMapByAIStatus[status]; exists {
		return courseStatus
	}
	return models.AIStatusManual
}

var questionTypeMapByAIQuizType = map[ai.QuizType]models.QuizQuestionType{
	ai.QuizTypeSingleChoice:   models.QuizQuestionTypeSingleChoice,
	ai.QuizTypeMultipleChoice: models.QuizQuestionTypeMultipleChoice,
	ai.QuizTypeText:           models.QuizQuestionTypeText,
	ai.QuizTypeMatching:       models.QuizQuestionTypeMatching,
	ai.QuizTypeOrdering:       models.QuizQuestionTypeOrdering,
	ai.QuizTypeFillInBlanks:   models.QuizQuestionTypeFillInBlanks,
}

func (s *AIHistoryService) GetQuestionTypeByAIQuizType(aiQuizType ai.QuizType) models.QuizQuestionType {
	if quizQuestionType, exists := questionTypeMapByAIQuizType[aiQuizType]; exists {
		return quizQuestionType
	}
	return ""
}

var aiQuizTypeMapByQuestionType = map[models.QuizQuestionType]ai.QuizType{
	models.QuizQuestionTypeSingleChoice:   ai.QuizTypeSingleChoice,
	models.QuizQuestionTypeMultipleChoice: ai.QuizTypeMultipleChoice,
	models.QuizQuestionTypeText:           ai.QuizTypeText,
	models.QuizQuestionTypeMatching:       ai.QuizTypeMatching,
	models.QuizQuestionTypeOrdering:       ai.QuizTypeOrdering,
	models.QuizQuestionTypeFillInBlanks:   ai.QuizTypeFillInBlanks,
}

func (s *AIHistoryService) GetAIQuizTypeMapByQuestionType(quizQuestionType models.QuizQuestionType) ai.QuizType {
	if aiQuizType, exists := aiQuizTypeMapByQuestionType[quizQuestionType]; exists {
		return aiQuizType
	}
	return ""
}

var toneMapByAITone = map[ai.Tone]models.AITone{
	ai.ToneProfessional: models.ToneProfessional,
	ai.ToneHumorous:     models.ToneHumorous,
	ai.ToneNormal:       models.ToneNormal,
}

func (s *AIHistoryService) GetToneMapByAITone(tone ai.Tone) models.AITone {
	if toneType, exists := toneMapByAITone[tone]; exists {
		return toneType
	}
	return models.ToneNormal
}

var aiToneMapByModelTone = map[models.AITone]ai.Tone{
	models.ToneProfessional: ai.ToneProfessional,
	models.ToneHumorous:     ai.ToneHumorous,
	models.ToneNormal:       ai.ToneNormal,
}

func (s *AIHistoryService) GetAiToneMapByModelTone(tone models.AITone) ai.Tone {
	if toneType, exists := aiToneMapByModelTone[tone]; exists {
		return toneType
	}
	return ai.ToneNormal
}

func (s *AIHistoryService) GetAIDurationTypeMapByModelDurationType(durationType models.AICourseDurationType) ai.DurationType {
	if aiDurationType, exists := aiDurationTypeMapByModelDurationType[durationType]; exists {
		return aiDurationType
	}
	return ai.DayTypeDuration
}

var aiDurationTypeMapByModelDurationType = map[models.AICourseDurationType]ai.DurationType{
	models.AICourseWeekTypeDuration: ai.WeekTypeDuration,
	models.AICourseDayTypeDuration:  ai.DayTypeDuration,
}

func (s *AIHistoryService) GetAIThumbnailStyleMapByModelThumbnailStyle(style models.AIThumbnailStyle) ai.ThumbnailStyle {
	if aiThumbnailStyle, exists := aiThumbnailStyleMapByModelThumbnailStyle[style]; exists {
		return aiThumbnailStyle
	}
	return ai.GeneralThumbnailStyle
}

var aiThumbnailStyleMapByModelThumbnailStyle = map[models.AIThumbnailStyle]ai.ThumbnailStyle{
	models.GeneralThumbnailStyle:      ai.GeneralThumbnailStyle,
	models.AnimeThumbnailStyle:        ai.AnimeThumbnailStyle,
	models.CreativeThumbnailStyle:     ai.CreativeThumbnailStyle,
	models.DynamicThumbnailStyle:      ai.DynamicThumbnailStyle,
	models.EnvironmentThumbnailStyle:  ai.EnvironmentThumbnailStyle,
	models.IllustrationThumbnailStyle: ai.IllustrationThumbnailStyle,
	models.PhotographyThumbnailStyle:  ai.PhotographyThumbnailStyle,
	models.RayTrace3DThumbnailStyle:   ai.RayTrace3DThumbnailStyle,
	models.Render3DThumbnailStyle:     ai.Render3DThumbnailStyle,
	models.SketchBWThumbnailStyle:     ai.SketchBWThumbnailStyle,
	models.SketchColorThumbnailStyle:  ai.SketchColorThumbnailStyle,
}

func (s *AIHistoryService) GetModelThumbnailStyleMapByAIThumbnailStyle(style ai.ThumbnailStyle) models.AIThumbnailStyle {
	if aiThumbnailStyle, exists := thumbnailStyleMapByAIThumbnailStyle[style]; exists {
		return aiThumbnailStyle
	}
	return models.GeneralThumbnailStyle
}

var thumbnailStyleMapByAIThumbnailStyle = map[ai.ThumbnailStyle]models.AIThumbnailStyle{
	ai.GeneralThumbnailStyle:      models.GeneralThumbnailStyle,
	ai.AnimeThumbnailStyle:        models.AnimeThumbnailStyle,
	ai.CreativeThumbnailStyle:     models.CreativeThumbnailStyle,
	ai.DynamicThumbnailStyle:      models.DynamicThumbnailStyle,
	ai.EnvironmentThumbnailStyle:  models.EnvironmentThumbnailStyle,
	ai.IllustrationThumbnailStyle: models.IllustrationThumbnailStyle,
	ai.PhotographyThumbnailStyle:  models.PhotographyThumbnailStyle,
	ai.RayTrace3DThumbnailStyle:   models.RayTrace3DThumbnailStyle,
	ai.Render3DThumbnailStyle:     models.Render3DThumbnailStyle,
	ai.SketchBWThumbnailStyle:     models.SketchBWThumbnailStyle,
	ai.SketchColorThumbnailStyle:  models.SketchColorThumbnailStyle,
}

func (s *AIHistoryService) FindOne(query *models.AIHistoryQuery, options *models.FindOneOptions) (*models.AIHistory, *e.AppError) {
	if aiHistory, err := models.Repository.AIHistory.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.AIHistory_not_found, err.Error())
		}
		return nil, e.NewError500(e.AIHistory_find_one_failed, err.Error())
	} else {
		return aiHistory, nil
	}
}

func (s *AIHistoryService) FindPage(query *models.AIHistoryQuery, options *models.FindPageOptions) ([]*models.AIHistory, *models.Pagination, *e.AppError) {
	if aiHistories, pagination, err := models.Repository.AIHistory.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.AIHistory_find_page_failed, err.Error())
	} else {
		return aiHistories, pagination, nil
	}
}

func (s *AIHistoryService) Create(data *models.AIHistory) (*models.AIHistory, *e.AppError) {
	if err := models.Repository.AIHistory.Create(data, nil); err != nil {
		return nil, e.NewError500(e.AIHistory_create_failed, err.Error())
	}
	return data, nil
}

func (s *AIHistoryService) CreateMany(data []*models.AIHistory) ([]*models.AIHistory, *e.AppError) {
	if err := models.Repository.AIHistory.CreateMany(data, nil); err != nil {
		return nil, e.NewError500(e.AIHistory_create_many_failed, err.Error())
	}
	return data, nil
}

func (s *AIHistoryService) Update(data *models.AIHistory) (*models.AIHistory, *e.AppError) {
	if err := models.Repository.AIHistory.Update(data, nil); err != nil {
		return nil, e.NewError500(e.AIHistory_update_failed, err.Error())
	}
	return data, nil
}

func (s *AIHistoryService) FindMany(query *models.AIHistoryQuery, options *models.FindManyOptions) ([]*models.AIHistory, *e.AppError) {
	if aiHistories, err := models.Repository.AIHistory.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.AIHistory_find_many_failed, err.Error())
	} else {
		return aiHistories, nil
	}
}

func (s *AIHistoryService) FindAndCreateHistory(params *dto.CreateAIHistoryParams) (*models.AIHistory, *e.AppError) {
	aiHistoryRequest := &models.AIHistoryQuery{
		UserID:      &params.UserID,
		EntityID:    &params.EntityID,
		GenerateID:  &params.GenerateID,
		RequestType: &params.RequestType,
		Step:        &params.Step,
		AIOfferID:   &params.AIOfferID,
	}
	if params.RequestIDsEmpty {
		aiHistoryRequest.RequestIDsEmpty = true
	} else {
		aiHistoryRequest.RequestIDsNotEmpty = true
	}
	if params.ID != "" {
		aiHistoryRequest.ID = &params.ID
	}
	history, err := models.Repository.AIHistory.FindOne(aiHistoryRequest, &models.FindOneOptions{})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.AIHistory_find_one_failed, err.Error())
	}

	if history != nil {
		history.Status = params.Status
		history.Cost = params.Cost
		if params.Request != nil {
			history.Request = params.Request
		}
		if params.Response != nil {
			history.Response = params.Response
		}
		if params.Error != nil {
			history.Error = params.Error
		}
		history.RequestIDs = params.RequestIDs
		history.Step = params.Step
		history.EndDate = params.EndDate
		history.RunDuration = int16(history.EndDate - history.StartDate)
		history.WaitDuration = int16(history.EndDate - int64(history.CreateAt))

		if _, aErr := s.Update(history); aErr != nil {
			return nil, aErr
		}

	} else {
		toCreateAIHistory := &models.AIHistory{
			RequestIDs:   params.RequestIDs,
			RequestType:  params.RequestType,
			UserID:       params.UserID,
			EntityID:     params.EntityID,
			EntityType:   params.EntityType,
			GenerateID:   params.GenerateID,
			GenerateType: params.GenerateType,
			Status:       params.Status,
			Cost:         params.Cost,
			Request:      params.Request,
			Response:     params.Response,
			StartDate:    params.StartDate,
			EndDate:      params.EndDate,
			RunDuration:  0,
			WaitDuration: 0,
			Error:        params.Error,
			OrgID:        params.OrgID,
			OrgSchema:    params.OrgSchema,
			Step:         params.Step,
			AIOfferID:    params.AIOfferID,
		}

		if params.ID != "" {
			toCreateAIHistory.Model = models.Model{
				ID: params.ID,
			}
		}

		if _, aErr := s.Create(toCreateAIHistory); aErr != nil {
			return nil, aErr
		}

		history = toCreateAIHistory
	}
	return history, nil
}
