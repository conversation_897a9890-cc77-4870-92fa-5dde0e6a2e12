package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"github.com/samber/lo"

	"gorm.io/gorm"
)

func (s *PublishCourseService) FindOne(query *models.PublishCourseQuery, options *models.FindOneOptions) (*models.PublishCourse, *e.AppError) {
	if course, err := models.Repository.PublishCourse(s.ctx).FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Course_not_found, "publish course not found")
		}
		return nil, e.NewError500(e.Course_find_one_failed, err.Error())
	} else {
		return course, nil
	}
}

func (s *PublishCourseService) FindBySlug(slug string, options *models.FindOneOptions) (*models.PublishCourse, *e.AppError) {
	if course, err := models.Repository.PublishCourse(s.ctx).FindBySlug(slug, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Course_not_found, "publish course not found")
		}
		return nil, e.NewError500(e.Course_find_one_failed, err.Error())
	} else {
		return course, nil
	}
}

func (s *PublishCourseService) Delete(id string) *e.AppError {
	if err := models.Repository.PublishCourse(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.Course_find_one_failed, err.Error())
	} else {
		return nil
	}
}

func (s *PublishCourseService) AddPublishCourseOld(course *models.Course, org *models.Organization) *e.AppError {
	publishs, err := models.Repository.PublishCourse(s.ctx).FindMany(&models.PublishCourseQuery{
		CourseCuid: util.NewString(course.Cuid),
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Course_add_publish_failed, "find publish course failed: "+course.ID+" "+err.Error())
	}

	pubOrg, orgOk := lo.Find(publishs, func(item *models.PublishCourse) bool {
		return item.IsRoot == false
	})

	pubRoot, rootOk := lo.Find(publishs, func(item *models.PublishCourse) bool {
		return item.IsRoot == true
	})

	// can phai xem target la publish o dau

	if course.PubDate > 0 {
		if !setting.IsBaseDomain(org.Domain) {
			if !orgOk {
				pubCourse := models.PublishCourse{
					CourseCuid: course.Cuid,
					OrgID:      org.ID,
					OrgSchema:  org.Schema,
					OrgDomain:  org.Domain,
					IsRoot:     false,
					PubDate:    course.PubDate,
				}
				copyCourseData(course, &pubCourse, course.PubDate)
				if cErr := models.Repository.PublishCourse(s.ctx).Create(&pubCourse, nil); cErr != nil {
					return e.NewError500(e.Course_add_publish_failed, "create publish course failed: "+course.ID+"  cErr "+cErr.Error())
				}
			} else {
				// publish other version THEN update course_id
				copyCourseData(course, pubOrg, course.PubDate)
				if cErr := models.Repository.PublishCourse(s.ctx).Update(pubOrg, nil); cErr != nil {
					return e.NewError500(e.Course_add_publish_failed, "update publish course failed: "+course.ID+"  cErr "+cErr.Error())
				}
			}
		}
	}

	if course.PubRootDate > 0 {
		if !rootOk {
			pubCourse := models.PublishCourse{
				CourseCuid: course.Cuid,
				OrgID:      org.ID,
				OrgSchema:  org.Schema,
				OrgDomain:  org.Domain,
				IsRoot:     true,
				PubDate:    course.PubRootDate,
			}
			copyCourseData(course, &pubCourse, course.PubRootDate)
			if cErr := models.Repository.PublishCourse(s.ctx).Create(&pubCourse, nil); cErr != nil {
				return e.NewError500(e.Course_add_publish_failed, "create publish course failed: "+course.ID+"  cErr "+cErr.Error())
			}
		} else {
			// publish other version THEN update course_id
			copyCourseData(course, pubRoot, course.PubRootDate)
			if cErr := models.Repository.PublishCourse(s.ctx).Update(pubRoot, nil); cErr != nil {
				return e.NewError500(e.Course_add_publish_failed, "update publish course failed: "+course.ID+"  cErr "+cErr.Error())
			}
		}
	}

	return nil
}

func (s *PublishCourseService) AddPublishCourse(course *models.Course, org *models.Organization, scope *models.PublishScope) *e.AppError {
	publish, err := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
		CourseCuid: util.NewString(course.Cuid),
	}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return e.NewError500(e.Course_add_publish_failed, "find publish course failed: "+course.ID+" "+err.Error())
	}

	if publish == nil {

		pubCourse := models.PublishCourse{
			CourseCuid:  course.Cuid,
			OrgID:       org.ID,
			OrgSchema:   org.Schema,
			OrgDomain:   org.Domain,
			PubDate:     course.PubDate,
			Enable:      true,
			PubRootDate: course.PubRootDate,
			EnableRoot:  lo.If(org.IsRoot(), true).Else(false),
		}
		if scope != nil {
			pubCourse.Scope = *scope
		} else {
			pubCourse.Scope = models.ScopeAll
		}
		copyCourseData(course, &pubCourse, course.PubDate)
		if cErr := models.Repository.PublishCourse(s.ctx).Create(&pubCourse, nil); cErr != nil {
			return e.NewError500(e.Course_add_publish_failed, "create publish course failed: "+course.ID+"  cErr "+cErr.Error())
		}

		// increase total_courses
		if iErr := models.Repository.UserSummary.Increase(&models.UserSummaryQuery{
			UserID: &course.UserID,
		}, models.TotalCoursesField, util.CourseUnit, nil); iErr != nil {
			return e.NewError500(e.User_summary_increase_failed, "Increase total courses count for user error: "+iErr.Error())
		}

	} else {
		copyCourseData(course, publish, course.PubDate)
		publish.PubDate = course.PubDate
		publish.PubRootDate = course.PubRootDate
		publish.Enable = true
		publish.EnableRoot = lo.If(org.IsRoot(), true).Else(publish.EnableRoot)
		if scope != nil {
			publish.Scope = *scope
		} else {
			publish.Scope = models.ScopeAll
		}
		if cErr := models.Repository.PublishCourse(s.ctx).Update(publish, nil); cErr != nil {
			return e.NewError500(e.Course_add_publish_failed, "update publish course failed: "+course.ID+"  cErr "+cErr.Error())
		}
	}

	return nil
}

func copyCourseData(course *models.Course, pub *models.PublishCourse, pubDate int) *models.PublishCourse {
	props := pub.Props
	if props == nil {
		props = models.JSONB{}
	}

	var histories []interface{}
	if props["histories"] != nil {
		histories = props["histories"].([]interface{})
	}
	histories = append(histories, models.JSONB{
		"version": course.Version,
		"id":      course.ID,
		"date":    pubDate,
	})

	props["histories"] = histories
	pub.Props = props
	pub.UserID = course.UserID
	pub.CourseID = course.ID
	pub.Name = course.Name
	pub.CourseSlug = course.Slug
	pub.IsPay = course.PriceSettings.IsPay
	pub.Price = course.PriceSettings.FiatPrice
	pub.CryptoPrice = course.PriceSettings.CryptoPrice
	pub.Description = course.Description
	pub.ShortDesc = course.ShortDesc
	pub.Version = course.Version
	pub.ThumbnailID = course.ThumbnailID
	pub.Enable = course.Enable
	pub.StartDate = course.StartDate
	pub.EndDate = course.EndDate
	pub.MarkAsCompleted = course.MarkAsCompleted
	return pub
}

func (s *PublishCourseService) UnPublishCourse(courseCuid string, target models.UnPublishCourseTarget) (*models.PublishCourse, *e.AppError) {
	publish, err := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{CourseCuid: util.NewString(courseCuid)}, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Publish_course_find_failed, "find publish course failed: "+err.Error())
	}

	if publish == nil {
		return nil, nil
	}

	switch target {
	case models.UnPublishCourseTargetOrg:
		publish.PubDate = 0
		publish.PubRootDate = 0
		break
	case models.UnPublishCourseTargetRoot:
		publish.PubRootDate = 0
		break
	default:
		publish.PubDate = 0
		publish.PubRootDate = 0
	}
	publish.Enable = false
	publish.EnableRoot = false
	if pErr := models.Repository.PublishCourse(s.ctx).Update(publish, nil); pErr != nil {
		return nil, e.NewError500(e.Publish_course_update_failed, "update publish course org failed: "+err.Error())
	}
	return publish, nil
}

func (s *PublishCourseService) FindPagePublishCourse(query *models.PublishCourseQuery, options *models.FindPageOptions) ([]*models.PublishCourse, *models.Pagination, *e.AppError) {
	pubCourses, pagination, err := models.Repository.PublishCourse(s.ctx).FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Course_find_publish_failed, "find publish course failed: "+err.Error())
	}
	return pubCourses, pagination, nil
}

func (s *PublishCourseService) ChangeStage(course *models.Course, request *dto.ChangeCourseStageRequest) *e.AppError {
	publish, err := models.Repository.PublishCourse(s.ctx).FindOne(&models.PublishCourseQuery{
		CourseCuid: util.NewString(course.Cuid),
		IsPub:      util.NewBool(true),
	}, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return e.NewError400(e.Publish_course_find_failed, "publish course not found")
		}
		return e.NewError500(e.Publish_course_find_failed, "find publish course failed: "+err.Error())
	}

	notiCode := communicationdto.CodeOrgAdminEnableCourse
	if request.Enable != nil {
		publish.Enable = *request.Enable
		notiCode = lo.If(*request.Enable, communicationdto.CodeOrgAdminEnableCourse).Else(communicationdto.CodeOrgAdminDisableCourse)
	}
	if request.EnableRoot != nil {
		publish.EnableRoot = *request.EnableRoot
		notiCode = lo.If(*request.EnableRoot, communicationdto.CodeOEAdminEnableCourse).Else(communicationdto.CodeOEAdminDisableCourse)
	}
	if uErr := models.Repository.PublishCourse(s.ctx).Update(publish, nil); uErr != nil {
		return e.NewError500(e.Publish_course_update_failed, "update publish course failed: "+uErr.Error())
	}

	org, err := models.Repository.Organization.FindByID(publish.OrgID, nil)
	if err != nil {
		return e.NewError500(e.Organization_find_one_failed, "find organization by ID failed: "+err.Error())
	}

	notificationReq := &communicationdto.PushNotificationRequest{
		Code:       communicationdto.NotificationCode(notiCode),
		EntityID:   publish.CourseID,
		EntityType: communicationdto.CourseEntity,
		RuleTree: &communicationdto.TreeNodeRequest{
			Rule: &communicationdto.Rule{
				Subject:   communicationdto.NotiUser,
				Verb:      communicationdto.IsIn,
				ObjectIDs: []string{publish.UserID},
			},
		},
		Props: s.makeNotifPropsForChangeCourseStage(course, org, request.User).IntoComm(),
	}
	if notiErr := communication.Notification.PushNotification(notificationReq); notiErr != nil {
		log.Errorf("Push notification to user ID %s after enable/disable course ID %s error: %v", publish.UserID, publish.CourseID, notiErr)
	}
	return nil
}

func (s *PublishCourseService) makeNotifPropsForChangeCourseStage(course *models.Course, org *models.Organization, user *models.User) models.JSONB {
	return models.JSONB{
		"org_id":      org.ID,
		"org_name":    org.Name,
		"org_domain":  org.Domain,
		"course_id":   course.ID,
		"course_cuid": course.Cuid,
		"course_name": course.Name,
		"course_slug": course.Slug,
		"user_id":     user.ID,
		"username":    user.Username,
	}
}
