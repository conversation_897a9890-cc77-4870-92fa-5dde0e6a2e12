package services

import (
	"context"
	"errors"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	chaindto "openedu-core/pkg/openedu_chain/dto"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"
)

func (s *CertificateService) Create(req *dto.ClaimCertificateRequest) (*models.Certificate, *e.AppError) {
	if req.CheckCertCondition {
		checkResp, appErr := s.CheckingCertificateCondition(&dto.CheckingCertificateConditionRequest{
			UserID: req.User.ID,
			Course: req.Course,
			OrgID:  req.Cert.OrgID,
		})
		if appErr != nil {
			log.Debug("Checking certificate condition failed")
			return nil, appErr
		}

		if !checkResp.CanReceive {
			return nil, e.NewError400(e.CertificateNotCompleteConditions, "not complete certificate condition")
		}
	}

	if err := models.Repository.Certificate.Create(req.Cert, nil); err != nil {
		return nil, e.NewError500(e.CertificateCreateFailed, "Create certificate error: "+err.Error())
	}

	createdCert, err := models.Repository.Certificate.FindOne(
		&models.CertificateQuery{
			CourseCuid: &req.Cert.CourseCuid,
			UserID:     &req.Cert.UserID,
			OrgID:      &req.Cert.OrgID,
		},
		&models.FindOneOptions{Preloads: []string{
			models.FilesField,
			models.ImageField,
		}})
	if err != nil {
		return nil, e.NewError500(e.CertificateFindFailed, "Find certificate by ID error: "+err.Error())
	}

	// Auto mint NFT if course settings enabled mint NFT and platform sponsor gas for its
	if req.Course.IsMintNFTEnabled() {
		sponsoredCourseCUIDs := models.GetConfig[[]string](models.PlatformSponsorNFTGasCourses)
		if _, sponsored := lo.Find(sponsoredCourseCUIDs, func(cuid string) bool {
			return cuid == req.Course.Cuid
		}); sponsored {
			go func() {
				_, aErr := s.MintNFT(&dto.MintCertificateNFTRequest{
					Certificate: createdCert,
					User:        req.User,
					GasFeePayer: models.Platform,
				})
				if aErr != nil {
					log.Errorf("CertificateService::Create Minting NFT automatically for user ID %s in course CUID %s failed: %v",
						req.User.ID, req.Course.Cuid, aErr)
				}
			}()
		}
	}

	orgCourse, orgErr := Organization.FindByIDWithCache(req.Course.OrgID, &models.FindOneOptions{})
	if orgErr != nil {
		log.ErrorWithAlertf("Cannot find org course to noti: %#v", orgErr)
		return nil, orgErr
	}

	// Push notification
	go func(cert *models.Certificate, u *models.User, c *models.Course) {
		notificationReq := &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeNewCertificateReceived,
			EntityID:   cert.ID,
			EntityType: communicationdto.CertificateEntity,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{req.User.ID},
				},
			},
			Props: s.makeNotificationPropsForCertificate(cert, u, c).IntoComm(),
			Org:   orgCourse.IntoComm(),
		}
		if err = communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification to user ID %s for new certificate received error: %v", u.ID, err)
		}
	}(createdCert, req.User, req.Course)
	// Send email
	aigov := models.GetConfig[models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	if req.Org.ID == aigov.OrgID {
		go func() {
			//SendEmail
			emailReq := &communicationdto.SendEmailRequest{
				User: req.User.IntoComm(),
				Org:  orgCourse.IntoComm(),
				Code: util.NewT(communicationdto.EmailCodeGetCertificate),
				ExtendDatas: communicationdto.MapEmailParams{
					"username":       req.User.Username,
					"user_email":     req.User.Email,
					"course_name":    req.Course.Name,
					"certificate_id": createdCert.ID,
					"full_name":      req.User.DisplayName,
				},
				IsQueue: true,
				From:    req.Org.Settings.SenderEmail,
			}
			if _, err := communication.Email.SendEmail(emailReq); err != nil {
				log.ErrorWithAlertf("GetCertificate::Send email code: %v failed: %v", communicationdto.EmailCodeGetCertificate, err)
			}
		}()

	}

	return createdCert, nil
}

func (s *CertificateService) makeNotificationPropsForCertificate(cert *models.Certificate, user *models.User, course *models.Course) models.JSONB {
	return models.JSONB{
		"user_id":      user.ID,
		"username":     user.Username,
		"display_name": user.DisplayName,
		"course_name":  course.Name,
		"course_slug":  course.Slug,
		"org_id":       cert.OrgID,
	}
}

func (s *CertificateService) FindPage(query *models.CertificateQuery, options *models.FindPageOptions) ([]*models.Certificate, *models.Pagination, *e.AppError) {
	certificates, pagination, err := models.Repository.Certificate.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.CertificateFindPageFailed, "Find page certificate error: "+err.Error())
	}
	return certificates, pagination, nil
}

func (s *CertificateService) Delete(query *models.CertificateQuery) *e.AppError {
	getCert, _ := models.Repository.Certificate.FindOne(query, &models.FindOneOptions{})
	if getCert == nil {
		return e.NewError400(e.CertificateNotFound, "Certificate not found")
	}

	if err := models.Repository.Certificate.Delete(getCert.ID, nil); err != nil {
		return e.NewError500(e.CertificateDeleteFailed, err.Error())
	}

	return nil
}

func (s *CertificateService) FindOneOptions(query *models.CertificateQuery, options *models.FindOneOptions) (*models.Certificate, *e.AppError) {
	cert, err := models.Repository.Certificate.FindOne(query, options)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.CertificateFindFailed, err.Error())
	}

	return cert, nil
}

func (s *CertificateService) FindByID(id string) (*models.Certificate, *e.AppError) {
	cert, err := models.Repository.Certificate.FindOne(
		&models.CertificateQuery{
			ID: util.NewString(id),
		}, &models.FindOneOptions{
			Preloads: []string{models.FilesField, models.ImageField},
		})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.CertificateNotFound, "Certificate not found: "+err.Error())
		}
		return nil, e.NewError500(e.CertificateFindFailed, "Find certificate by ID error: "+err.Error())
	}

	return cert, nil
}

func (s *CertificateService) CheckingCertificateCondition(data *dto.CheckingCertificateConditionRequest) (*dto.CheckCertificateConditionResponse, *e.AppError) {
	course := data.Course
	resp := &dto.CheckCertificateConditionResponse{
		IsReceived:               false,
		CanReceive:               true,
		CompletedCourse:          &dto.CompletedCourseConditionResponse{},
		CompletedAllQuizzes:      &dto.CompletedAllQuizzesConditionResponse{},
		CompletedFinalQuiz:       &dto.CompletedFinalQuizConditionResponse{},
		CompletedRequiredLessons: &dto.CompletedRequiredLessonsConditionResponse{},
	}

	// Check existing certificate
	existingCert, err := models.Repository.Certificate.FindOne(&models.CertificateQuery{
		UserID:     &data.UserID,
		OrgID:      &data.OrgID,
		CourseCuid: &data.Course.Cuid,
	}, &models.FindOneOptions{})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.CertificateFindFailed, "Find certificate by course cuid and user ID error: "+err.Error())
	}

	if existingCert != nil {
		resp.IsReceived = true
	} else {
		resp.IsReceived = false
	}

	// Check completed course percentage
	if course.GetCertCondition().IsCompletedCourseEnabled() {
		completedCourseResp, appErr := s.checkCompletedCourseCondition(course, data.UserID)
		if appErr != nil {
			return nil, appErr
		}
		resp.CompletedCourse = completedCourseResp
		if !resp.CompletedCourse.Passed {
			resp.CanReceive = false
		}
	}

	// Check completed all quizzes
	if course.GetCertCondition().IsCompletedAllQuizzesEnabled() && course.QuizCount > 0 {
		completedAllQuizzesResp, appErr := s.checkCompletedAllQuizzesCondition(course, data.UserID)
		if appErr != nil {
			return nil, appErr
		}
		resp.CompletedAllQuizzes = completedAllQuizzesResp
		if !resp.CompletedAllQuizzes.Passed {
			resp.CanReceive = false
		}
	}

	// Check completed final quiz
	if course.GetCertCondition().IsCompletedFinalQuizEnabled() {
		completedFinalQuizResp, appErr := s.checkCompletedFinalQuizCondition(course, data.UserID)
		if appErr != nil {
			return nil, appErr
		}
		resp.CompletedFinalQuiz = completedFinalQuizResp
		if !resp.CompletedFinalQuiz.Passed {
			resp.CanReceive = false
		}
	}

	if course.GetCertCondition().IsCompletedRequiredLessonEnabled() {
		completedLessonsResp, appErr := s.checkCompletedRequiredLessonCondition(course, data.UserID)
		if appErr != nil {
			return nil, appErr
		}
		resp.CompletedRequiredLessons = completedLessonsResp
		if !resp.CompletedRequiredLessons.Passed {
			resp.CanReceive = false
		}
	}
	return resp, nil
}

func (s *CertificateService) PushNotificationReceiveCertificate(course *models.Course, user *models.User, org *models.Organization) {
	isReceived := false
	canReceive := false

	if course.HasCertificate && course.MarkAsCompleted {
		checkResp, appErr := Certificate.CheckingCertificateCondition(
			&dto.CheckingCertificateConditionRequest{
				UserID: user.ID,
				Course: course,
				OrgID:  org.ID,
			})
		if appErr != nil {
			log.Error("Checking certificate condition failed", appErr)
			return
		}

		isReceived = checkResp.IsReceived
		canReceive = checkResp.CanReceive
	}

	bodyMsg := &communicationdto.WebsocketMessageRequest{
		Event: communicationdto.WebsocketEventCertificate,
		Broadcast: communicationdto.WebsocketBroadcastParams{
			UserID: user.ID,
		},
		Data: map[string]interface{}{
			"can_receive": canReceive,
			"is_received": isReceived,
		},
	}

	if err := communication.Websocket.SendMsgToUserWebSocket(bodyMsg); err != nil {
		log.Error("Send message to user ws error")
		return
	}
}

func (s *CertificateService) validateBeforeMintNFT(req *dto.MintCertificateNFTRequest) (*models.Course, *e.AppError) {
	if req.User.ID != req.Certificate.UserID {
		return nil, e.NewError400(e.CertificateNotAllowMintNFT, "Owner required to mint NFT")
	}

	if req.Certificate.IsMintedNFT() {
		return nil, e.NewError400(e.CertificateAlreadyMintedNFT,
			"Certificate already had NFT token ID "+req.Certificate.NftTokenID+" on network "+
				strings.ToUpper(string(req.Certificate.NftNetwork))+" with transaction hash "+req.Certificate.NftTxHash)
	}

	// Check whether course settings enabled mint NFT
	publishCourse, err := models.Repository.PublishCourse(context.TODO()).FindOne(&models.PublishCourseQuery{
		CourseCuid: &req.Certificate.CourseCuid,
	}, &models.FindOneOptions{})
	if err != nil {
		return nil, e.NewError500(e.Course_find_publish_failed,
			"Find the publish course cuid "+req.Certificate.CourseCuid+"error: "+err.Error())
	}

	course, err := models.Repository.Course(context.TODO()).FindOne(&models.CourseQuery{
		ID: &publishCourse.CourseID,
	}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if err != nil {
		return nil, e.NewError500(e.Course_find_one_failed,
			"Find the course ID "+publishCourse.CourseID+" in schema "+publishCourse.OrgSchema+" error: "+err.Error())
	}

	if !course.IsMintNFTEnabled() {
		return nil, e.NewError400(e.CertificateNotAllowMintNFT,
			"This certificate is not allowed to mint NFT")
	}

	network := course.CertMintNFTNetwork()
	isPaymasterOnBase := req.GasFeePayer == models.Paymaster &&
		network == models.BlockchainNetworkBASE

	if req.GasFeePayer != course.Props.MintCertNFTSettings.GasFeePayer &&
		req.GasFeePayer != models.Learner &&
		!isPaymasterOnBase {
		return nil, e.NewError400(e.CertificateWrongGasFeePayer,
			"Wrong gas fee payer: expected '"+string(course.Props.MintCertNFTSettings.GasFeePayer)+
				"' but received '"+string(req.GasFeePayer)+"'")
	}

	return course, nil
}

func (s *CertificateService) MintNFT(req *dto.MintCertificateNFTRequest) (*models.Certificate, *e.AppError) {
	course, appErr := s.validateBeforeMintNFT(req)
	if appErr != nil {
		return nil, appErr
	}

	network := course.CertMintNFTNetwork()

	currency := models.CryptoCurrencyNEAR
	if network == models.BlockchainNetworkBASE {
		currency = models.CryptoCurrencyETH
	}

	wallet, err := models.Repository.Wallet.FindOne(&models.WalletQuery{
		UserID:   &req.User.ID,
		Type:     util.NewT(models.AssetTypeCrypto),
		Network:  util.NewT(network),
		Currency: util.NewT(currency),
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.WalletFindFailed, "Find default crypto wallet to mint NFT error: "+err.Error())
	}

	ownerWallet, err := models.Repository.Wallet.FindOne(&models.WalletQuery{
		UserID:   &course.Owner.ID,
		Type:     util.NewT(models.AssetTypeCrypto),
		Network:  util.NewT(network),
		Currency: util.NewT(currency),
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.WalletFindFailed, "Find default crypto wallet to mint NFT error: "+err.Error())
	}

	// Mint NFT
	txn := &models.Transaction{
		Model: models.Model{
			ID: util.GenerateId(),
		},
		UserID:     req.Certificate.UserID,
		WalletID:   wallet.ID,
		Network:    wallet.Network,
		Type:       models.TransactionTypeMintNFT,
		Status:     models.TransactionStatusPending,
		ErrorCode:  0,
		TxHash:     "",
		OrgID:      req.Certificate.OrgID,
		Data:       models.JSONB{},
		EntityType: models.CertificateModelName,
		EntityID:   req.Certificate.ID,
	}

	resp, err := openedu_chain.Transaction.MintNFT(&chaindto.MintNFTRequest{
		GasFeePayer:        string(req.GasFeePayer),
		CourseCuid:         course.Cuid,
		CourseOwnerAddress: ownerWallet.Address,
		ReceiverWalletID:   wallet.BlockchainWalletID,
		CoreTxID:           txn.ID,
		IsMainnet:          setting.OpenEduChainSetting.IsMainnet,
		Network:            chaindto.BlockchainNetwork(network),
		TokenMetadata: chaindto.TokenMetadataRequest{
			Title:       models.NftCertificateDefaultTitle,
			Description: models.NftCertificateDefaultDescription,
			MediaURL:    req.Certificate.GetImageURL(),
		},
	})
	if err != nil {
		switch {
		case errors.Is(err, openedu_chain.ErrInsufficientGasFee):
			return nil, e.NewError400(e.CertificateInsufficientGasToMintNFT,
				"Balance is not enough to cover the gas fee: "+err.Error())

		case errors.Is(err, openedu_chain.ErrInsufficientBalance):
			return nil, e.NewError400(e.CertificateInsufficientBalanceToMintNFT,
				"Balance is not enough to mint NFT: "+err.Error())

		default:
			return nil, e.NewError500(e.CertificateMintNFTFailed, "Mint NFT error: "+err.Error())
		}
	}

	txn.BlockchainTxID = resp.ID
	txn.TxHash = resp.TxHash
	txn.Status = models.TransactionStatus(resp.Status)
	txn.ErrorCode = resp.ErrorCode
	if err = models.Repository.Transaction.Create(txn, nil); err != nil {
		return nil, e.NewError500(e.TransactionCreateFailed, "Create transaction error: "+err.Error())
	}

	if txn.IsSuccess() {
		req.Certificate.NftTxHash = txn.TxHash
		req.Certificate.NftNetwork = txn.Network
		req.Certificate.NftTokenID = resp.Props.NftTokenID
		req.Certificate.Props.NftMintAt = resp.CreateAt
		req.Certificate.Props.NftStorageCost = resp.Props.StorageCost
		req.Certificate.Props.NftGasCost = resp.Props.GasCost
		req.Certificate.Props.NftTotalGasCost = resp.Props.TotalGasCost
		req.Certificate.Props.GasFeePayer = req.GasFeePayer
		if uErr := models.Repository.Certificate.Update(req.Certificate, nil); uErr != nil {
			return nil, e.NewError500(e.CertificateUpdateFailed, "Update certificate error: "+uErr.Error())
		}
		return req.Certificate, nil
	}

	return req.Certificate, nil
}

func (s *CertificateService) EstimatedMintNFTFees(certificate *models.Certificate) (*dto.CertificateNFTFeesResponse, *e.AppError) {
	publishCourse, err := models.Repository.PublishCourse(context.TODO()).FindOne(&models.PublishCourseQuery{
		CourseCuid: &certificate.CourseCuid,
	}, &models.FindOneOptions{})
	if err != nil {
		return nil, e.NewError500(e.Course_find_publish_failed, "Find the publish course cuid "+certificate.CourseCuid+"error: "+err.Error())
	}

	course, err := models.Repository.Course(context.TODO()).FindOne(&models.CourseQuery{
		ID: &publishCourse.CourseID,
	}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if err != nil {
		return nil, e.NewError500(e.Course_find_one_failed,
			"Find the course ID "+publishCourse.CourseID+" in schema "+publishCourse.OrgSchema+" error: "+err.Error())
	}

	network := course.CertMintNFTNetwork()

	currency := models.CryptoCurrencyNEAR
	estimatedFee := models.EstimatedNEARsToMintNFT
	if network == models.BlockchainNetworkBASE {
		currency = models.CryptoCurrencyETH
		estimatedFee = models.EstimatedETHToMintNFT
	}

	enabled := false
	gasFeePayer := models.Paymaster
	if course.Props.MintCertNFTSettings != nil {
		enabled = course.Props.MintCertNFTSettings.Enabled
		gasFeePayer = course.Props.MintCertNFTSettings.GasFeePayer
	}

	resp := dto.CertificateNFTFeesResponse{
		MintNFTEnabled:       enabled,
		GasFeePayerInSetting: gasFeePayer,
		ActualGasFeePayer:    gasFeePayer,
		EstimatedFee:         models.EstimatedNEARsToMintNFT,
		SponsorBalance:       sponsorBalance,
		Network:              wallet.Network,
	}

	if network == models.BlockchainNetworkBASE {
		if gasFeePayer == models.Creator {
			resp.ActualGasFeePayer = models.Paymaster
		}
	} else {
		wallet, appErr := Wallet.FindOne(&models.WalletQuery{
			UserID:   &course.Owner.ID,
			Currency: util.NewT(currency),
			Network:  util.NewT(network),
		}, &models.FindOneOptions{})
		if appErr != nil {
			return nil, appErr
		}

		sponsorBalance, bErr := openedu_chain.Wallet.GetGasSponsorBalance(&chaindto.GetWalletGasSponsorBalanceRequest{
			WalletID:   wallet.BlockchainWalletID,
			CourseCuid: course.Cuid,
			IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
		})
		if bErr != nil {
			return nil, e.NewError500(e.Course_find_one_failed, "Get sponsor sponsorBalance error: "+bErr.Error())
		}

		resp.SponsorBalance = sponsorBalance
		if resp.ActualGasFeePayer == models.Creator && sponsorBalance.LessThan(estimatedFee) {
			resp.ActualGasFeePayer = models.Learner
		}
	}

	return &resp, nil
}

func (s *CertificateService) checkCompletedCourseCondition(course *models.Course, userID string) (*dto.CompletedCourseConditionResponse, *e.AppError) {
	resp := &dto.CompletedCourseConditionResponse{
		Passed:              false,
		Enable:              course.Props.CertificateCondition.CompletedCourse,
		RequiredPercentage:  float64(course.Props.CertificateCondition.CourseCompletionPercentage),
		CompletedPercentage: 0,
	}

	completedLessonCount, err := models.Repository.LearningStatus(context.TODO()).
		CountCompletedLessonsByUsers([]string{course.ID}, []string{userID})
	if err != nil {
		return nil, e.NewError500(e.Course_find_publish_failed, "Get complete lesson from learning progress failed: "+err.Error())
	}

	if len(completedLessonCount) > 0 {
		resp.CompletedPercentage = float64(completedLessonCount[0].CompletedLessons) / float64(completedLessonCount[0].TotalLessons) * 100
		if resp.CompletedPercentage >= resp.RequiredPercentage {
			resp.Passed = true
		}
	} else {
		log.Debugf("No completed lesson found for user %s in course %s", userID, course.Cuid)
	}

	return resp, nil
}

func (s *CertificateService) findQuizLessonContentsByCourseID(courseID string) ([]*models.LessonContent, *e.AppError) {
	cacheKey := "quiz_lesson_contents_" + courseID

	var contentsInf []interface{}
	if cErr := models.Cache.LessonContent.GetByKey(cacheKey, &contentsInf); cErr == nil {
		if len(contentsInf) > 0 {
			return util.SliceInf2SliceStruct[*models.LessonContent](contentsInf), nil
		}
	}

	lessonContents, err := models.Repository.LessonContent(context.TODO()).FindMany(&models.LessonContentQuery{
		CourseID:       &courseID,
		IncludeDeleted: util.NewBool(false),
		Type:           util.NewString(string(models.LessonTypeQuiz)),
	}, nil)
	if err != nil {
		return nil, e.NewError500(e.Lesson_find_page_failed, "Find lesson contents of course failed: "+err.Error())
	}

	if cErr := models.Cache.LessonContent.SetByKey(cacheKey, util.SliceStruct2SliceInf(lessonContents)); cErr != nil {
		log.Errorf("CertificateService::getQuizLessonContentsByCourseID set quiz lesson contents cache failed: %v", cErr)
	}
	return lessonContents, nil
}

func (s *CertificateService) findQuizUIDsByLessonContentUIDs(contentUIDs []string) ([]string, *e.AppError) {
	cacheKey := "by_content_uids_" + util.HashSHA256(strings.Join(contentUIDs, "_"))

	var cacheQuizUIDs []string
	cErr := models.Cache.QuizRelation.GetByKey(cacheKey, &cacheQuizUIDs)
	if cErr != nil {
		log.Errorf("CertificateService::findQuizUIDsByLessonContentUIDs failed to get quiz UIDs cache for lesson contents: %v", cErr)
	}

	if len(cacheQuizUIDs) > 0 {
		return cacheQuizUIDs, nil
	}

	quizTbl := models.GetTblName(models.QuizTbl)
	quizRelationTbl := models.GetTblName(models.QuizRelationTbl)
	query := fmt.Sprintf(`
				SELECT q.uid
				FROM %[1]s qr
					LEFT JOIN %[2]s q ON qr.quiz_id = q.id
				WHERE qr.related_entity_type = @entityType AND qr.related_entity_id IN @entityIDs
				`,
		quizRelationTbl, quizTbl,
	)

	var quizUIDs []string
	if err := models.DB.Debug().Raw(query, map[string]interface{}{
		"entityType": models.QuizRelationEntityLessonContent,
		"entityIDs":  contentUIDs,
	}).Scan(&quizUIDs).Error; err != nil {
		return nil, e.NewError500(e.Lesson_find_page_failed, "Find quiz UIDs by lesson content IDs failed: "+err.Error())
	}

	if cErr = models.Cache.QuizRelation.SetByKey(cacheKey, quizUIDs); cErr != nil {
		log.Errorf("CertificateService::findQuizUIDsByLessonContentUIDs failed to set quiz UIDs cache for lesson contents: %v", cErr)
	}

	return quizUIDs, nil
}

func (s *CertificateService) checkCompletedAllQuizzesCondition(course *models.Course, userID string) (*dto.CompletedAllQuizzesConditionResponse, *e.AppError) {
	resp := &dto.CompletedAllQuizzesConditionResponse{
		Enable: course.Props.CertificateCondition.CompletedAllQuizzes,
		Passed: true,
	}

	lessonContents, appErr := s.findQuizLessonContentsByCourseID(course.ID)
	if appErr != nil {
		return nil, appErr
	}

	if len(lessonContents) > 0 {
		lessonContentIDs := lo.Map(lessonContents, func(item *models.LessonContent, _ int) string {
			return item.ID
		})

		quizUIDs, qErr := s.findQuizUIDsByLessonContentUIDs(lessonContentIDs)
		if qErr != nil {
			return nil, qErr
		}

		if len(quizUIDs) > 0 {
			passedQuizCount, cErr := models.Repository.QuizSubmission.Count(&models.QuizSubmissionQuery{
				UserID:    &userID,
				QuizUIDIn: quizUIDs,
				Status:    util.NewT(models.QuizSubmissionStatusDone),
				Passed:    util.NewBool(true),
			})
			if cErr != nil {
				return nil, e.NewError500(e.Quiz_submission_find_failed, "Count quiz submissions failed: "+cErr.Error())
			}

			if passedQuizCount >= int64(course.QuizCount) {
				resp.Passed = true
			} else {
				resp.Passed = false
			}
		}
	}
	return resp, nil
}

func (s *CertificateService) findFinalQuizLessonContentByCourseID(courseID string) (*models.LessonContent, *e.AppError) {
	cacheKey := "final_quiz_" + courseID

	var contentsInf []interface{}
	if cErr := models.Cache.LessonContent.GetByKey(cacheKey, &contentsInf); cErr == nil {
		if len(contentsInf) > 0 {
			cacheLessonContents := util.SliceInf2SliceStruct[*models.LessonContent](contentsInf)
			if len(cacheLessonContents) > 0 && cacheLessonContents[0] != nil {
				return cacheLessonContents[0], nil
			}
		}
	}

	// Get all quiz lessons content of course sort by create at DESC => final quiz of course
	finalQuiz, err := models.Repository.LessonContent(context.TODO()).FindOne(&models.LessonContentQuery{
		Type:           util.NewString(string(models.LessonTypeQuiz)),
		IncludeDeleted: util.NewBool(false),
		CourseID:       util.NewString(courseID),
	}, &models.FindOneOptions{Sort: []string{models.CreateAtDESC}})
	if err != nil && !models.IsRecordNotFound(err) {
		return nil, e.NewError500(e.LessonContentFindFailed, "Find final quiz lesson content failed: "+err.Error())
	}

	if finalQuiz != nil {
		if cErr := models.Cache.LessonContent.SetByKey(cacheKey, util.SliceStruct2SliceInf([]*models.LessonContent{finalQuiz})); cErr != nil {
			log.Errorf("CertificateService::getFinalQuizByCourseID set quiz lesson contents cache failed: %v", cErr)
		}
	}

	return finalQuiz, nil
}

func (s *CertificateService) findQuizRelationByLessonContent(content *models.LessonContent, relationType string) (*models.QuizRelation, *e.AppError) {
	cacheKey := relationType + "_" + content.ID

	var cacheRelation models.QuizRelation
	cErr := models.Cache.QuizRelation.GetByKey(cacheKey, &cacheRelation)
	if cErr != nil {
		log.Errorf("CertificateService::findQuizRelationByLessonContent failed to get quiz relation cache for lesson content %s: %v", content.ID, cErr)
	}

	if cacheRelation.QuizID != "" {
		return &cacheRelation, nil
	}

	quizRelation, err := models.Repository.QuizRelation.FindOne(&models.QuizRelationQuery{
		RelatedEntityType: util.NewT(models.QuizRelationEntityLessonContent),
		RelationType:      util.NewT(models.QuizRelationType(relationType)),
		RelatedEntityID:   &content.ID}, nil)
	if err != nil {
		return nil, e.NewError500(e.Quiz_relation_find_failed, "Find quiz relation by lesson content ID failed: "+err.Error())
	}

	if cErr = models.Cache.QuizRelation.SetByKey(cacheKey, quizRelation); cErr != nil {
		log.Errorf("CertificateService::findQuizRelationByLessonContent failed to set quiz relation cache for lesson content %s: %v", content.ID, err)
	}

	return quizRelation, nil
}

func (s *CertificateService) checkCompletedFinalQuizCondition(
	course *models.Course,
	userID string,
) (*dto.CompletedFinalQuizConditionResponse, *e.AppError) {

	resp := &dto.CompletedFinalQuizConditionResponse{
		Enable:             course.Props.CertificateCondition.CompletedFinalQuiz,
		RequiredPercentage: float64(course.Props.CertificateCondition.FinalQuizCompletionPercentage),
	}

	var passed bool
	cacheKey := "final_quiz_passed" + userID + "_" + course.Cuid
	cErr := models.Cache.QuizSubmission.GetByKey(cacheKey, &passed)
	if cErr != nil {
		log.Errorf("CertificateService::checkCompletedFinalQuizCondition failed to get final quiz passed cache: %v", cErr)
	} else {
		resp.Passed = passed
		if resp.Passed {
			return resp, nil
		}
	}

	lessonContent, appErr := s.findFinalQuizLessonContentByCourseID(course.ID)
	if appErr != nil {
		return nil, appErr
	}

	if lessonContent == nil {
		resp.Passed = true
		return resp, nil
	}

	quizRelation, appErr := s.findQuizRelationByLessonContent(lessonContent, models.QuizRelationTypeIs)
	if appErr != nil {
		return nil, appErr
	}

	quiz, err := models.Repository.Quiz.FindByID(quizRelation.QuizID)
	if err != nil {
		return nil, e.NewError500(e.Quiz_relation_find_failed, err.Error())
	}

	submission, err := models.Repository.QuizSubmission.FindOne(&models.QuizSubmissionQuery{
		QuizUID: &quiz.UID,
		UserID:  &userID,
		Passed:  util.NewBool(true),
	}, &models.FindOneOptions{
		Preloads: []string{models.AnswersField},
		Sort:     []string{models.CreateAtDESC},
	})
	if err != nil {
		if models.IsRecordNotFound(err) {
			resp.Passed = false
			return resp, nil
		}
		return nil, e.NewError500(e.Quiz_submission_find_failed, err.Error())
	}

	resp.Passed = QuizSubmission.CheckFinalQuizCompletePercent(quiz, submission, course.Props.CertificateCondition.FinalQuizCompletionPercentage)

	if cErr = models.Cache.QuizSubmission.SetByKey(cacheKey, resp.Passed); cErr != nil {
		log.Errorf("CertificateService::checkCompletedFinalQuizCondition failed to get final quiz passed cache: %v", cErr)
	}
	return resp, nil
}

func (s *CertificateService) checkCompletedRequiredLessonCondition(course *models.Course, userID string) (*dto.CompletedRequiredLessonsConditionResponse, *e.AppError) {
	resp := &dto.CompletedRequiredLessonsConditionResponse{
		Enable:  course.Props.CertificateCondition.CompletedRequiredLesson,
		Passed:  false,
		Lessons: []*dto.RequiredLesson{},
	}

	if len(course.Props.CertificateCondition.RequiredLessonUIDs) <= 0 {
		resp.Passed = true
		return resp, nil
	}

	courseOutline, appErr := Course.FindByIdForOutline(course.ID, false, &models.FindOneOptions{})
	if appErr != nil {
		return nil, appErr
	}

	isRequiredLessons := make(map[string]bool, len(course.Props.CertificateCondition.RequiredLessonUIDs))
	for _, lessonUID := range course.Props.CertificateCondition.RequiredLessonUIDs {
		isRequiredLessons[lessonUID] = true
	}

	var toCheckLessonUIDs []string
	for _, section := range courseOutline.Outline {
		for _, lesson := range section.Lessons {
			if isRequiredLessons[lesson.UID] {
				toCheckLessonUIDs = append(toCheckLessonUIDs, lesson.UID)
				resp.Lessons = append(resp.Lessons, &dto.RequiredLesson{
					ID:    lesson.ID,
					UID:   lesson.UID,
					Title: lesson.Title,
					Section: &dto.RequiredSection{
						ID:    section.ID,
						UID:   section.UID,
						Title: section.Title,
					},
					CompletedAt: 0,
				})
			}
		}
	}

	if len(toCheckLessonUIDs) <= 0 {
		resp.Passed = true
		return resp, nil
	}

	ls, err := models.Repository.LearningStatus(context.TODO()).FindOne(&models.LearningStatusQuery{
		CourseCuid: &course.Cuid,
		UserID:     &userID,
	}, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			resp.Passed = false // User didn't start learning this course yet
			return resp, nil
		}
		return nil, e.NewError500(e.Find_learning_progress_failed, "Find learning progress of lesson for user failed: "+err.Error())
	}

	completedAtByLessonUIDs := make(map[string]int64)
	for _, section := range ls.Sections {
		for _, lesson := range section.Lessons {
			completedAtByLessonUIDs[lesson.LessonUID] = lesson.CompleteAt
		}
	}

	resp.Passed = true
	for _, lesson := range resp.Lessons {
		if completedAt, ok := completedAtByLessonUIDs[lesson.UID]; ok {
			lesson.CompletedAt = completedAt
		} else {
			resp.Passed = false
		}
	}
	return resp, nil
}
