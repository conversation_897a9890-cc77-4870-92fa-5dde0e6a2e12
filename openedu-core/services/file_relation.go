package services

import (
	"errors"
	"openedu-core/models"
	"openedu-core/pkg/e"

	"gorm.io/gorm"
)

func (s *FileRelationService) CreateMany(entities []*models.FileRelation) *e.AppError {
	if err := models.Repository.FileRelation.CreateMany(entities, nil); err != nil {
		return e.NewError500(e.File_relation_create_failed, "create report files failed: "+err.Error())
	}
	return nil
}

func (s *FileRelationService) FindPage(query *models.FileRelationQuery, options *models.FindPageOptions) ([]*models.FileRelation, *models.Pagination, *e.AppError) {
	if files, pagination, err := models.Repository.FileRelation.FindPage(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, e.NewError400(e.File_relation_find_page_failed, "find report file failed: "+err.Error())
		}
		return nil, nil, e.NewError500(e.File_relation_find_page_failed, "find report file failed: "+err.Error())
	} else {
		return files, pagination, nil
	}
}

func (s *FileRelationService) FindOne(query *models.FileRelationQuery, options *models.FindOneOptions) (*models.FileRelation, *e.AppError) {
	if file, err := models.Repository.FileRelation.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.File_relation_find_one_failed, "find report file failed: "+err.Error())
		}
		return nil, e.NewError500(e.File_relation_find_one_failed, "find report file failed: "+err.Error())
	} else {
		return file, nil
	}
}

func (s *FileRelationService) FindMany(query *models.FileRelationQuery, options *models.FindManyOptions) ([]*models.FileRelation, *e.AppError) {
	if files, err := models.Repository.FileRelation.FindMany(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError400(e.File_relation_find_many_failed, "find report file failed: "+err.Error())
		}
		return nil, e.NewError500(e.File_relation_find_many_failed, "find report file failed: "+err.Error())
	} else {
		return files, nil
	}
}
