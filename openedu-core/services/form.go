package services

import (
	"errors"
	"fmt"
	"net/mail"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"sort"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (s *FormService) Create(org *models.Organization, user *models.User, data *dto.CreateFormRequest) (*models.Form, *e.AppError) {
	//if appErr := s.checkDefaultQuestions(models.FormEvent(data.Event), data.Questions); appErr != nil {
	//	return nil, appErr
	//}

	form := s.convertCreateFormRequestToForm(org, user, data)
	if err := form.CheckValid(); err != nil {
		return nil, e.NewError400(e.Form_invalid_data, fmt.Sprintf("Invalid form data: %v", err))
	}

	if user == nil || org == nil || !user.CanCreateForm(form) {
		return nil, e.NewError400(e.Form_invalid_permission, "You cannot create this form")
	}

	if err := models.Repository.Form.Create(form, nil); err != nil {
		return nil, e.NewError500(e.Form_create_failed, err.Error())
	}

	if _, appErr := s.createQuestions(form, data.Questions); appErr != nil {
		return nil, appErr
	}
	return form, nil
}

func (s *FormService) FindById(id string) (*models.Form, *e.AppError) {
	form, err := models.Repository.Form.FindByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Form_not_found, err.Error())
		}
		return nil, e.NewError500(e.Form_find_one_failed, err.Error())
	}
	return form, nil
}

func (s *FormService) FindBySlug(slug string) (*models.Form, *e.AppError) {
	form, err := models.Repository.Form.FindBySlug(slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Form_not_found, err.Error())
		}
		return nil, e.NewError500(e.Form_find_one_failed, err.Error())
	}
	return form, nil
}

func (s *FormService) FindOne(query *models.FormQuery, options *models.FindOneOptions) (*models.Form, *e.AppError) {
	form, err := models.Repository.Form.FindOne(query, options)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Form_not_found, err.Error())
		}
		return nil, e.NewError500(e.Form_find_one_failed, err.Error())
	}

	return form, nil
}

func (s *FormService) FindMany(query *models.FormQuery, options *models.FindManyOptions) ([]*models.Form, *e.AppError) {
	forms, err := models.Repository.Form.FindMany(query, options)
	if err != nil {
		return nil, e.NewError500(e.Form_find_failed, err.Error())
	}
	return forms, nil
}

func (s *FormService) FindPage(query *models.FormQuery, options *models.FindPageOptions) ([]*models.Form, *models.Pagination, *e.AppError) {
	forms, pagination, err := models.Repository.Form.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Form_find_failed, err.Error())
	}
	return forms, pagination, nil
}

func (s *FormService) Submit(user *models.User, form *models.Form, data *dto.SubmitFormRequest) (formSession *models.FormSession, appErr *e.AppError) {
	answersDataMap, ckErr := s.checkDuplicatedAndMakeAnswerDataMap(form, data)
	if ckErr != nil {
		return nil, ckErr
	}

	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err := fmt.Errorf("panic: %v", r)
			appErr = e.NewError500(e.Form_submit_failed, fmt.Sprintf("Create form session failed: %v", err))
		}
	}()

	formSession = &models.FormSession{
		FormRelationID: data.FormRelationID,
		FormID:         form.ID,
		FormUID:        form.UID,
		Status:         models.FormSessionsStatusReviewing,
	}
	if user != nil {
		formSession.UserID = &user.ID
	}
	if cErr := models.Repository.FormSession.Create(formSession, tx); cErr != nil {
		tx.Rollback()
		formSession = nil
		appErr = e.NewError500(e.Form_submit_failed, fmt.Sprintf("Create form session failed: %v", cErr))
		return
	}

	questionsByIDs := make(map[string]*models.FormQuestion)
	optionsByIDs := make(map[string]*models.FormQuestionOption)
	for _, question := range form.Questions {
		questionsByIDs[question.ID] = question
		for _, option := range question.Options {
			optionsByIDs[option.ID] = option
		}

		for _, subQuestion := range question.SubQuestions {
			questionsByIDs[subQuestion.ID] = subQuestion
			for _, option := range question.Options {
				optionsByIDs[option.ID] = option
			}
		}
	}

	var answers []*models.FormAnswer
	for _, question := range form.Questions {
		if !question.HasSubQuestions() {
			answerData, found := answersDataMap[question.ID]
			if found {
				answersForQuestion, vErr := s.validateDataAndConvertToAnswers(formSession, question, answerData, questionsByIDs, optionsByIDs)
				if vErr != nil {
					tx.Rollback()
					formSession = nil
					appErr = vErr
					return nil, vErr
				}
				answers = append(answers, answersForQuestion...)
			} else if question.IsRequired() {
				tx.Rollback()
				formSession = nil
				appErr = e.NewError400(e.Form_invalid_submission, "Missing answer for question: "+question.Title)
				return
			}

			continue
		}
		for _, subQuestion := range question.SubQuestions {
			answerData, found := answersDataMap[subQuestion.ID]
			if found {
				subQuestion.Options = question.Options
				answersForQuestion, vErr := s.validateDataAndConvertToAnswers(formSession, subQuestion, answerData, questionsByIDs, optionsByIDs)
				if vErr != nil {
					tx.Rollback()
					formSession = nil
					appErr = vErr
					return
				}
				answers = append(answers, answersForQuestion...)
			} else if question.IsRequired() && subQuestion.IsRequired() {
				tx.Rollback()
				formSession = nil
				appErr = e.NewError400(e.Form_invalid_submission, "Missing answer for question: "+question.Title)
				return
			}
		}
	}

	if cErr := models.Repository.FormAnswer.CreateMany(answers, tx); cErr != nil {
		tx.Rollback()
		formSession = nil
		appErr = e.NewError500(e.Form_submit_failed, "Create form answers error: "+cErr.Error())
		return
	}

	// Upsert answer stats
	var listAnswerStats []*models.FormAnswerStats
	for _, answer := range answers {
		fileIDs := lo.Map(answer.Files, func(file *models.File, _ int) string {
			return file.ID
		})

		question := questionsByIDs[answer.QuestionID]
		answerStats := &models.FormAnswerStats{
			FormID:      form.ID,
			FormUID:     form.UID,
			QuestionUID: question.UID,
			FileIDs:     strings.Join(fileIDs, "__"),
			Text:        answer.Text,
			Count:       1,
		}
		if answer.OptionID != nil {
			option := optionsByIDs[*answer.OptionID]
			answerStats.OptionUID = option.UID
		}
		if answer.SubQuestionID != nil {
			subQuestion := questionsByIDs[answer.QuestionID]
			answerStats.SubQuestionUID = subQuestion.UID
		}

		listAnswerStats = append(listAnswerStats, answerStats)
	}
	if err := models.Repository.FormAnswerStats.UpsertMany(listAnswerStats, tx); err != nil {
		tx.Rollback()
		formSession = nil
		appErr = e.NewError500(e.Form_submit_failed, "Create form answer stats error: "+err.Error())
		return
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		formSession = nil
		appErr = e.NewError500(e.ERROR, "Commit transaction error: "+err.Error())
		return
	}
	return formSession, nil
}

func (s *FormService) HandleEventsAfterSubmit(
	form *models.Form,
	formSession *models.FormSession,
	org *models.Organization,
	source string,
	ref string,
	refUser string,
) *e.AppError {
	switch form.Event {
	case models.FormEventRegisterCourse:
		// Enroll user to the course
		if _, appErr := CourseEnrollment.EnrollUserFromFormSession(form, formSession, org, source, ref, refUser); appErr != nil {
			return appErr
		}

	case models.FormEventNewUser:
		// Update NewUserSurveyCompleted to `true` in User.Props
		if formSession.UserID != nil {
			user, err := models.Repository.User.FindByID(*formSession.UserID)
			if err != nil {
				return e.NewError500(e.Error_user_find_failed, "Find user ID "+*formSession.UserID+" after submit new user form failed: "+err.Error())
			}

			user.Props.NewUserSurveyCompleted = true
			if _, err = models.Repository.User.Update(user); err != nil {
				return e.NewError500(e.Error_user_update_failed, "Update user ID "+*formSession.UserID+" after submit new user form failed: "+err.Error())
			}
		}
	}
	return nil
}

func (s *FormService) Update(form *models.Form, data *dto.UpdateFormRequest) (*models.Form, *e.AppError) {
	//if appErr := s.checkDefaultQuestions(models.FormEvent(data.Event), data.Questions); appErr != nil {
	//	return nil, appErr
	//}

	// Override data for updating
	form.Title = data.Title
	form.Description = data.Description
	form.Event = models.FormEvent(data.Event)
	if data.Status != nil {
		form.Status = models.FormStatus(*data.Status)
	}
	form.Type = models.FormType(data.Type)
	form.StartDate = data.StartDate
	form.EndDate = data.EndDate
	form.AuthRequired = data.AuthRequired
	if data.IsTemplate != nil {
		form.IsTemplate = *data.IsTemplate
	}

	if err := form.CheckValid(); err != nil {
		return nil, e.NewError400(e.Form_invalid_data, fmt.Sprintf("Invalid form data: %v", err))
	}

	// Get form's questions
	existingQuestions, fErr := models.Repository.FormQuestion.FindMany(&models.FormQuestionQuery{
		FormID:     &form.ID,
		IsTopLevel: util.NewBool(true),
	}, &models.FindManyOptions{
		Preloads: []string{util.SubQuestionsField, util.OptionsField},
	})
	if fErr != nil {
		return nil, e.NewError500(e.Form_update_failed, fmt.Sprintf("Get existing questions failed: %v", fErr))
	}
	existingQuestionByID := map[string]*models.FormQuestion{}
	for _, question := range existingQuestions {
		existingQuestionByID[question.ID] = question
		for _, subQuestion := range question.SubQuestions {
			existingQuestionByID[subQuestion.ID] = subQuestion
		}
	}

	// Comparing existing questions to new questions
	var listQuestionParamsToCreate []*dto.FormQuestionParams
	var questionsToUpdate []*models.FormQuestion
	var optionsToUpdate []*models.FormQuestionOption
	var subQuestionsToCreate []*models.FormQuestion
	var questionIDsToDelete []string
	var optionIDsToDelete []string
	seenQuestionIDs := map[string]struct{}{}
	for questionParamsIdx, questionParams := range data.Questions {
		if questionParams.ID == "" {
			questionParams.Order = &questionParamsIdx
			listQuestionParamsToCreate = append(listQuestionParamsToCreate, questionParams)
			continue
		}

		seenQuestionIDs[questionParams.ID] = struct{}{}
		if existingQuestion, found := existingQuestionByID[questionParams.ID]; found {
			existingQuestion.Title = questionParams.Title
			existingQuestion.Description = questionParams.Description
			existingQuestion.QuestionType = models.FormQuestionType(questionParams.QuestionType)
			existingQuestion.Settings.IsDefault = questionParams.Settings.IsDefault
			existingQuestion.Settings.Required = questionParams.Settings.Required
			existingQuestion.Settings.OtherOptionEnabled = questionParams.Settings.OtherOptionEnabled
			existingQuestion.Settings.OtherOptionLabel = questionParams.Settings.OtherOptionLabel
			existingQuestion.Settings.BaseDomain = questionParams.Settings.BaseDomain
			existingQuestion.Settings.Props = questionParams.Settings.Props
			existingQuestion.Order = questionParamsIdx

			seenOptionIDs := map[string]struct{}{}
			newOptions := lo.Map(questionParams.Options, func(option *dto.FormQuestionOptionParams, optionIdx int) *models.FormQuestionOption {
				if option.ID != "" {
					seenOptionIDs[option.ID] = struct{}{}
				}
				return &models.FormQuestionOption{
					ID:         option.ID,
					QuestionID: existingQuestion.ID,
					Text:       option.Text,
					Order:      optionIdx,
				}
			})
			existingOptionsByIDs := map[string]*models.FormQuestionOption{}
			for _, option := range existingQuestion.Options {
				if _, seen := seenOptionIDs[option.ID]; !seen {
					optionIDsToDelete = append(optionIDsToDelete, option.ID)
				}
				existingOptionsByIDs[option.ID] = option
			}
			for _, option := range newOptions {
				if existingOption, found := existingOptionsByIDs[option.ID]; !found {
					option.ID = ""
					option.UID = util.GenerateId()
				} else {
					option.UID = existingOption.UID
					optionsToUpdate = append(optionsToUpdate, option)
				}
			}
			existingQuestion.Options = newOptions

			seenSubQuestionIDs := map[string]struct{}{}
			newSubQuestions := lo.Map(questionParams.SubQuestions, func(subQuestionParams *dto.FormSubQuestionParams, subQuestionIdx int) *models.FormQuestion {
				if subQuestionParams.ID != "" {
					seenSubQuestionIDs[subQuestionParams.ID] = struct{}{}
					if existingSubQuestion, found := existingQuestionByID[subQuestionParams.ID]; found {
						existingSubQuestion.Title = subQuestionParams.Title
						existingSubQuestion.Description = subQuestionParams.Description
						existingSubQuestion.Order = subQuestionIdx
						return existingSubQuestion
					}
				}

				subQuestions := &models.FormQuestion{
					ParentID:     &existingQuestion.ID,
					FormID:       existingQuestion.FormID,
					Title:        subQuestionParams.Title,
					Description:  subQuestionParams.Description,
					QuestionType: existingQuestion.QuestionType,
					Order:        0,
					SubQuestions: nil,
					Options:      nil,
					Settings: models.FormQuestionSettings{
						IsDefault:          false,
						Required:           false,
						OtherOptionEnabled: false,
					},
				}
				switch models.FormQuestionType(questionParams.QuestionType) {
				case models.FormQuestionTypeCheckboxChoiceGrid:
					subQuestions.QuestionType = models.FormQuestionTypeMultipleSelection
					subQuestions.Settings.Required = false

				case models.FormQuestionTypeMultipleChoiceGrid:
					subQuestions.QuestionType = models.FormQuestionTypeMultipleChoice
					subQuestions.Settings.Required = true
				}
				return subQuestions
			})
			validExistingSubQuestionIDs := map[string]struct{}{}
			for _, subQuestion := range existingQuestion.SubQuestions {
				if _, seen := seenSubQuestionIDs[subQuestion.ID]; !seen {
					questionIDsToDelete = append(questionIDsToDelete, subQuestion.ID)
				}
				validExistingSubQuestionIDs[subQuestion.ID] = struct{}{}
			}
			for _, subQuestion := range newSubQuestions {
				if _, found := validExistingSubQuestionIDs[subQuestion.ID]; !found {
					subQuestion.ID = ""
					subQuestionsToCreate = append(subQuestionsToCreate, subQuestion)
				} else {
					questionsToUpdate = append(questionsToUpdate, subQuestion)
				}
			}
			existingQuestion.SubQuestions = newSubQuestions

			questionsToUpdate = append(questionsToUpdate, existingQuestion)
		} else {
			questionParams.ID = ""
			questionParams.Order = &questionParamsIdx
			listQuestionParamsToCreate = append(listQuestionParamsToCreate, questionParams)
			continue
		}
	}

	for _, question := range existingQuestions {
		if _, found := seenQuestionIDs[question.ID]; !found {
			questionIDsToDelete = append(questionIDsToDelete, question.ID)
		}
	}

	if uErr := models.Repository.Form.Update(form, nil); uErr != nil {
		return nil, e.NewError500(e.Form_update_failed, fmt.Sprintf("Update form failed: %v", uErr))
	}

	if _, cErr := s.createQuestions(form, listQuestionParamsToCreate); cErr != nil {
		return nil, cErr
	}

	if cErr := models.Repository.FormQuestion.CreateMany(subQuestionsToCreate, nil); cErr != nil {
		return nil, e.NewError500(e.Form_update_failed, fmt.Sprintf("Create new sub questions for existing questions failed: %v", cErr))
	}

	if len(questionIDsToDelete) > 0 {
		if _, dErr := models.Repository.FormQuestion.DeleteMany(&models.FormQuestionQuery{IDIn: questionIDsToDelete}, nil); dErr != nil {
			return nil, e.NewError500(e.Form_update_failed, fmt.Sprintf("Delete questions failed: %v", dErr))
		}
	}

	if len(optionIDsToDelete) > 0 {
		if _, dErr := models.Repository.FormQuestionOption.DeleteMany(&models.FormQuestionOptionQuery{IDIn: optionIDsToDelete}, nil); dErr != nil {
			return nil, e.NewError500(e.Form_update_failed, fmt.Sprintf("Delete question options failed: %v", dErr))
		}
	}

	for _, option := range optionsToUpdate {
		if uErr := models.Repository.FormQuestionOption.Update(option, nil); uErr != nil {
			return nil, e.NewError500(e.Form_update_failed, fmt.Sprintf("Update question options failed: %v", uErr))
		}
	}

	for _, question := range questionsToUpdate {
		if uErr := models.Repository.FormQuestion.Update(question, nil); uErr != nil {
			return nil, e.NewError500(e.Form_update_failed, fmt.Sprintf("Update questions failed: %v", uErr))
		}
	}

	return form, nil
}

func (s *FormService) Summary(form *models.Form) (*models.FormSummary, *e.AppError) {
	formSummary := &models.FormSummary{
		QuestionSummaries: nil,
	}
	count, cErr := models.Repository.FormSession.Count(&models.FormSessionQuery{FormUID: &form.UID})
	if cErr != nil {
		return nil, e.NewError500(e.Form_summary_failed, fmt.Sprintf("Count form session failed: %v", cErr))
	}
	formSummary.TotalResponses = count

	questions, qErr := models.Repository.FormQuestion.FindMany(&models.FormQuestionQuery{
		//IDIn:   questionIDs,
		FormID: &form.ID,
	}, &models.FindManyOptions{
		CustomPreloads: map[string]func(*gorm.DB) *gorm.DB{
			util.OptionsField: func(db *gorm.DB) *gorm.DB {
				return db.Where("delete_at = 0")
			},
			util.SubQuestionsField: func(db *gorm.DB) *gorm.DB {
				return db.Where("delete_at = 0")
			},
		},
		Sort: []string{`"order" ASC`},
	})
	if qErr != nil {
		return nil, e.NewError500(e.Form_summary_failed, fmt.Sprintf("Find questions by form failed: %v", qErr))
	}

	if len(questions) == 0 {
		return formSummary, nil
	}

	questionUIDs := lo.Map(questions, func(question *models.FormQuestion, _ int) string {
		return question.UID
	})

	answerCounts, err := models.Repository.FormAnswerStats.CountTotalByQuestionUIDs(questionUIDs)
	if err != nil {
		return nil, e.NewError500(e.Form_summary_failed, fmt.Sprintf("Find answer stats by form failed: %v", qErr))
	}

	answerCountsByQuestionUIDs := map[string]*models.FormQuestionTotalCount{}
	for _, item := range answerCounts {
		answerCountsByQuestionUIDs[item.QuestionUID] = item
	}

	for _, question := range questions {
		questionSummary := &models.FormQuestionSummary{
			QuestionUID: question.UID,
			Title:       question.Title,
			Description: question.Description,
			Type:        question.QuestionType,
		}
		if answerCount, found := answerCountsByQuestionUIDs[question.UID]; found {
			questionSummary.TotalCount = answerCount.TotalCount
		}

		for _, subQuestion := range question.SubQuestions {
			subQuestionSummary := &models.FormSubQuestionSummary{
				ID:    subQuestion.ID,
				Title: subQuestion.Title,
			}
			if answerCount, found := answerCountsByQuestionUIDs[question.UID]; found {
				subQuestionSummary.TotalCount = answerCount.TotalCount
			}
			questionSummary.SubQuestionSummaries = append(questionSummary.SubQuestionSummaries, subQuestionSummary)
		}
		formSummary.QuestionSummaries = append(formSummary.QuestionSummaries, questionSummary)
	}

	return formSummary, nil
}

func (s *FormService) HandleEventForCourse(user *models.User, course *models.Course, event models.FormEvent) *e.AppError {
	if event != models.FormEventRegisterCourse &&
		event != models.FormEventSurveyCourse {
		return nil
	}

	formRelations, err := models.Repository.FormRelation.FindMany(&models.FormRelationQuery{
		RelatedEntityType: util.NewT(models.CourseModelName),
		RelatedEntityID:   &course.Cuid,
	}, nil)
	if err != nil {
		return e.NewError500(e.Form_find_failed, fmt.Sprintf("Find form relations failed: %v", err))
	}

	if len(formRelations) == 0 {
		return nil
	}

	forms, fErr := models.Repository.Form.FindMany(&models.FormQuery{
		Event:       util.NewT(event),
		Status:      util.NewT(models.FormStatusPublishedAll),
		IsUnExpired: util.NewBool(true),
		IDIn: lo.Map(formRelations, func(formRelation *models.FormRelation, _ int) string {
			return formRelation.ID
		}),
	}, nil)
	if fErr != nil {
		return e.NewError500(e.Form_find_failed, fmt.Sprintf("Find forms failed: %v", fErr))
	}

	if len(forms) <= 0 {
		return nil
	}

	//if user == nil {
	//	return e.NewError400(e.Course_register_form_required, "Require user logged to user")
	//}

	switch event {
	case models.FormEventRegisterCourse:
		formSessions, fsErr := models.Repository.FormSession.FindMany(&models.FormSessionQuery{
			UserID:   &user.ID,
			FormIDIn: lo.Map(forms, func(form *models.Form, _ int) string { return form.ID }),
		}, nil)
		if fsErr != nil {
			return e.NewError500(e.Form_session_find_many_failed, fmt.Sprintf("Find forms failed: %v", fErr))
		}
		submittedFormIDs := map[string]struct{}{}
		for _, formSession := range formSessions {
			submittedFormIDs[formSession.FormID] = struct{}{}
		}
		for _, form := range forms {
			if _, found := submittedFormIDs[form.ID]; !found {
				return e.NewError400(e.Course_register_form_required, "User did not submit for register course form ID: "+form.ID)
			}
		}
	}
	return nil
}

func (s *FormService) Delete(form *models.Form) *e.AppError {
	if err := models.Repository.Form.Delete(form.ID, nil); err != nil {
		return e.NewError500(e.Form_delete_failed, "Delete form error: "+err.Error())
	}
	return nil
}

func (s *FormService) Duplicate(form *models.Form, isCloneNewVersion bool) (*models.Form, *e.AppError) {
	newForm := &models.Form{
		UID:          form.UID,
		Title:        form.Title,
		Description:  form.Description,
		Event:        form.Event,
		Type:         form.Type,
		Status:       form.Status,
		StartDate:    form.StartDate,
		EndDate:      form.EndDate,
		OrgID:        form.OrgID,
		CreatorID:    form.CreatorID,
		AuthRequired: form.AuthRequired,
		//IsTemplate: form.IsTemplate,
	}
	if !isCloneNewVersion {
		newForm.UID = util.GenerateId()
	}

	if err := models.Repository.Form.Create(newForm, nil); err != nil {
		return nil, e.NewError500(e.Form_create_failed, "Duplicate form error: "+err.Error())
	}

	var newQuestions []*models.FormQuestion
	for _, question := range form.Questions {
		questionID := util.GenerateId()
		newQuestion := models.FormQuestion{
			Model:        models.Model{ID: questionID},
			UID:          question.UID,
			ParentID:     nil,
			FormID:       newForm.ID,
			Title:        question.Title,
			Description:  question.Description,
			QuestionType: question.QuestionType,
			Order:        question.Order,
			Settings: models.FormQuestionSettings{
				IsDefault:             question.Settings.IsDefault,
				Required:              question.Settings.Required,
				OtherOptionEnabled:    question.Settings.OtherOptionEnabled,
				OtherOptionLabel:      question.Settings.OtherOptionLabel,
				BaseDomain:            question.Settings.BaseDomain,
				Key:                   question.Settings.Key,
				Props:                 question.Settings.Props,
				ValidateDomainEnabled: false,
			},
			Options: lo.Map(question.Options, func(option *models.FormQuestionOption, _ int) *models.FormQuestionOption {
				newOption := &models.FormQuestionOption{
					UID:        option.UID,
					QuestionID: questionID,
					Text:       option.Text,
					Order:      option.Order,
				}

				if !isCloneNewVersion {
					newOption.UID = util.GenerateId()
				}

				return newOption
			}),
		}

		if !isCloneNewVersion {
			newQuestion.UID = util.GenerateId()
		}

		for _, subQuestion := range question.SubQuestions {
			newSubQuestion := models.FormQuestion{
				UID:          subQuestion.UID,
				FormID:       newForm.ID,
				ParentID:     &questionID,
				Title:        subQuestion.Title,
				Description:  subQuestion.Description,
				Order:        subQuestion.Order,
				QuestionType: subQuestion.QuestionType,
				Settings: models.FormQuestionSettings{
					IsDefault:             subQuestion.Settings.IsDefault,
					Required:              subQuestion.Settings.Required,
					OtherOptionEnabled:    subQuestion.Settings.OtherOptionEnabled,
					OtherOptionLabel:      subQuestion.Settings.OtherOptionLabel,
					BaseDomain:            subQuestion.Settings.BaseDomain,
					Key:                   subQuestion.Settings.Key,
					ValidateDomainEnabled: subQuestion.Settings.ValidateDomainEnabled,
					Props:                 subQuestion.Settings.Props,
				},
			}

			if !isCloneNewVersion {
				newSubQuestion.UID = util.GenerateId()
			}

			newQuestion.SubQuestions = append(newQuestion.SubQuestions, &newSubQuestion)
		}
		newQuestions = append(newQuestions, &newQuestion)
	}

	if err := models.Repository.FormQuestion.CreateMany(newQuestions, nil); err != nil {
		return nil, e.NewError500(e.Form_create_questions_failed, "Duplicate form questions error: "+err.Error())
	}

	if form.Latest {
		form.Latest = false
		if err := models.Repository.Form.Update(form, nil); err != nil {
			return nil, e.NewError500(e.Form_update_failed, "Update form's latest to false error: "+err.Error())
		}
	}

	return newForm, nil
}

func (s *FormService) convertCreateFormRequestToForm(org *models.Organization, user *models.User, data *dto.CreateFormRequest) *models.Form {
	form := models.Form{
		UID:          util.GenerateId(),
		Latest:       true,
		Title:        data.Title,
		Description:  data.Description,
		Event:        models.FormEvent(data.Event),
		Type:         models.FormType(data.Type),
		Status:       models.FormStatusDraft,
		StartDate:    data.StartDate,
		EndDate:      data.EndDate,
		AuthRequired: data.AuthRequired,
		IsTemplate:   data.IsTemplate,
		Questions:    nil,
	}
	if org != nil {
		form.OrgID = org.ID
	}
	if user != nil {
		form.CreatorID = user.ID
	}
	form.GenerateSlug()
	return &form
}

func (s *FormService) isSameQuestion(question *models.FormQuestion, questionParams *dto.FormQuestionParams) bool {
	if questionParams.Title != question.Title ||
		questionParams.Description != question.Description ||
		models.FormQuestionType(questionParams.QuestionType) != question.QuestionType {
		return false
	}

	if questionParams.Settings.IsDefault != question.Settings.IsDefault ||
		questionParams.Settings.Required != question.Settings.Required ||
		questionParams.Settings.OtherOptionEnabled != question.Settings.OtherOptionEnabled ||
		questionParams.Settings.BaseDomain != question.Settings.BaseDomain {
		return false
	}

	if len(questionParams.Options) != len(question.Options) {
		return false
	}

	sort.Slice(question.Options, func(i, j int) bool {
		return question.Options[i].Order < question.Options[j].Order
	})

	for idx, optionParams := range questionParams.Options {
		if optionParams.Text != question.Options[idx].Text {
			return false
		}
	}

	if len(questionParams.SubQuestions) != len(question.SubQuestions) {
		return false
	}

	sort.Slice(question.SubQuestions, func(i, j int) bool {
		return question.SubQuestions[i].Order < question.SubQuestions[j].Order
	})

	for idx, subQuestionParams := range questionParams.SubQuestions {
		if subQuestionParams.Title != question.SubQuestions[idx].Title ||
			subQuestionParams.Description != question.SubQuestions[idx].Description {
			return false
		}
	}

	return true
}

func (s *FormService) checkDefaultQuestions(formEvent models.FormEvent, listQuestionParams []*dto.FormQuestionParams) *e.AppError {
	var defaultQuestions []*models.FormQuestion
	switch formEvent {
	case models.FormEventRegisterOrg:
		defaultQuestions = models.GetDefaultQuestionsForRegisterOrgForm()

	case models.FormEventRegisterCreator:
		defaultQuestions = models.GetDefaultQuestionsForRegisterCreatorForm()

	case models.FormEventRegisterCourse:
		defaultQuestions = models.GetDefaultQuestionsForRegisterCourseForm()
	}

	// Check both order of default questions
	for idx, questionParams := range listQuestionParams {
		if idx >= len(defaultQuestions) {
			break
		}
		if !s.isSameQuestion(defaultQuestions[idx], questionParams) {
			return e.NewError400(e.Form_missing_default_questions, "Missing or wrong order for default questions")
		}
	}
	return nil
}

func (s *FormService) createQuestions(form *models.Form, listQuestionParams []*dto.FormQuestionParams) ([]*models.FormQuestion, *e.AppError) {
	var questions []*models.FormQuestion
	for questionParamIdx, questionParams := range listQuestionParams {
		questionID := util.GenerateId()
		question := models.FormQuestion{
			Model:        models.Model{ID: questionID},
			UID:          util.GenerateId(),
			ParentID:     nil,
			FormID:       form.ID,
			Title:        questionParams.Title,
			Description:  questionParams.Description,
			QuestionType: models.FormQuestionType(questionParams.QuestionType),
			Order:        questionParamIdx,
			Settings: models.FormQuestionSettings{
				IsDefault:             questionParams.Settings.IsDefault,
				Required:              questionParams.Settings.Required,
				OtherOptionEnabled:    questionParams.Settings.OtherOptionEnabled,
				OtherOptionLabel:      questionParams.Settings.OtherOptionLabel,
				BaseDomain:            questionParams.Settings.BaseDomain,
				Key:                   questionParams.Settings.Key,
				ValidateDomainEnabled: false,
				Props:                 questionParams.Settings.Props,
			},
			Options: lo.Map(questionParams.Options, func(option *dto.FormQuestionOptionParams, optionIdx int) *models.FormQuestionOption {
				return &models.FormQuestionOption{
					UID:        util.GenerateId(),
					QuestionID: questionID,
					Text:       option.Text,
					Order:      optionIdx,
				}
			}),
		}
		if questionParams.Order != nil {
			question.Order = *questionParams.Order
		}
		if !question.CanEnableOtherOption() && question.IsOtherOptionEnabled() {
			return nil, e.NewError400(e.Form_invalid_question, fmt.Sprintf("Question type `%s` do not support enabled other option", question.QuestionType))
		}

		for index, subQuestionParams := range questionParams.SubQuestions {
			subQuestion := models.FormQuestion{
				UID:         util.GenerateId(),
				FormID:      form.ID,
				ParentID:    &questionID,
				Title:       subQuestionParams.Title,
				Description: subQuestionParams.Description,
				Order:       index,
			}

			switch models.FormQuestionType(questionParams.QuestionType) {
			case models.FormQuestionTypeCheckboxChoiceGrid:
				subQuestion.QuestionType = models.FormQuestionTypeMultipleSelection
				subQuestion.Settings.Required = false

			case models.FormQuestionTypeMultipleChoiceGrid:
				subQuestion.QuestionType = models.FormQuestionTypeMultipleChoice
				subQuestion.Settings.Required = true
			}
			question.SubQuestions = append(question.SubQuestions, &subQuestion)
		}
		questions = append(questions, &question)
	}

	if err := models.Repository.FormQuestion.CreateMany(questions, nil); err != nil {
		return nil, e.NewError500(e.Form_create_questions_failed, err.Error())
	}

	return questions, nil
}

func (s *FormService) CheckFormCanBeSubmitted(org *models.Organization, user *models.User, form *models.Form) *e.AppError {
	// Check form is draft or unpublished
	if form.IsDraft() || form.IsUnPublished() {
		return e.NewError404(e.Form_not_found, "Form not found or not published")
	}

	// Check form is expired or not
	if form.IsExpired() {
		return e.NewError400(e.Form_already_expired, "This form is already expired")
	}

	// Check form is publish to specific org or all
	if !form.IsPublishedAll() && !form.BelongsToOrg(org) {
		return e.NewError400(e.Form_invalid_permission, "You does not have permission to submit this form")
	}

	// Check form is required auth
	if form.IsRequiredAuth() && user == nil {
		return e.NewError400(e.Form_invalid_permission, "This form is required authentication for submissions")
	}

	return nil
}

func (s *FormService) HandleDuplicatedFormAnswer(
	form *models.Form,
	data *dto.SubmitFormRequest,
) *e.AppError {
	mapFormAnswers, err := models.Repository.FormAnswer.FindFormAnswerGroupByFormID(form.UID)
	if err != nil {
		return e.NewError400(e.Form_answer_find_failed, err.Error())
	}

	formIDToDelete := []string{}
	for formID, answers := range mapFormAnswers {
		if util.IsStructSliceEqual(answers, data.Answers, func(a *models.FormAnswer, b *dto.FormAnswerParams) bool {
			return a.QuestionID == b.QuestionID && a.Text == b.AnswerText
		}) {
			formIDToDelete = append(formIDToDelete, formID)
		}
	}

	if len(formIDToDelete) > 0 {
		if _, err := models.Repository.FormSession.UpdateOutDatedStatus(&models.FormSessionQuery{IDIn: formIDToDelete}, nil); err != nil {
			return e.NewError500(e.Form_session_not_found, err.Error())
		}
	}

	return nil
}

func (s *FormService) checkDuplicatedAndMakeAnswerDataMap(
	form *models.Form,
	data *dto.SubmitFormRequest,
) (map[string][]*dto.FormAnswerParams, *e.AppError) {
	parentQuestionsByID := map[string]*models.FormQuestion{}
	for _, question := range form.Questions {
		parentQuestionsByID[question.ID] = question
	}

	answersDataMap := map[string][]*dto.FormAnswerParams{} // key format `questionID::subQuestionID`
	for _, answerData := range data.Answers {
		question, found := parentQuestionsByID[answerData.QuestionID]
		if !found {
			return nil, e.NewError400(e.Form_invalid_submission, "Question not found: "+answerData.QuestionID)
		}
		allowedMultiAnswers := question.IsTypeMultipleChoice() || question.IsTypeMultipleChoiceGrid()

		qID := answerData.QuestionID
		if answerData.SubQuestionID != nil {
			qID = *answerData.SubQuestionID
		}
		if _, found := answersDataMap[qID]; !found {
			answersDataMap[qID] = []*dto.FormAnswerParams{answerData}
		} else if allowedMultiAnswers {
			answersDataMap[qID] = append(answersDataMap[qID], answerData)
		} else {
			return nil, e.NewError400(e.Form_invalid_submission, "Accept only one answer for question: "+question.Title)
		}
	}
	return answersDataMap, nil
}

func (s *FormService) validateDataAndConvertToAnswers(
	formSession *models.FormSession,
	question *models.FormQuestion,
	answersData []*dto.FormAnswerParams,
	questionsByIDs map[string]*models.FormQuestion,
	optionsByIDs map[string]*models.FormQuestionOption,
) ([]*models.FormAnswer, *e.AppError) {
	var subQuestionID *string
	var subQuestionUID *string
	questionID := question.ID
	questionUID := question.UID
	if question.IsSubQuestion() {
		questionID = ""
		questionUID = ""
		if parent, found := questionsByIDs[*question.ParentID]; found {
			questionID = parent.ID
			questionUID = parent.UID
		}
		subQuestionID = &question.ID
		subQuestionUID = &question.UID
	}

	var answers []*models.FormAnswer
	for _, answerData := range answersData {
		if !question.IsOptionBasedQuestion() {
			answer := &models.FormAnswer{
				SessionID:      formSession.ID,
				FormID:         formSession.FormID,
				FormUID:        formSession.FormUID,
				UserID:         formSession.UserID,
				Key:            question.Settings.Key,
				QuestionID:     questionID,
				QuestionUID:    questionUID,
				SubQuestionID:  subQuestionID,
				SubQuestionUID: subQuestionUID,
				Text:           answerData.AnswerText,
				Files: lo.Map(answerData.AnswerFiles, func(fileID string, _ int) *models.File {
					return &models.File{
						Model: models.Model{ID: fileID},
					}
				}),
			}
			answers = append(answers, answer)
		} else { // multiple_choice, checkbox, multiple_choice_grid, checkbox_grid, dropdown
			for _, optionID := range answerData.Options {
				answer := &models.FormAnswer{
					SessionID:      formSession.ID,
					FormID:         formSession.FormID,
					FormUID:        formSession.FormUID,
					UserID:         formSession.UserID,
					Key:            question.Settings.Key,
					QuestionID:     questionID,
					QuestionUID:    questionUID,
					SubQuestionID:  subQuestionID,
					SubQuestionUID: subQuestionUID,
					Text:           answerData.AnswerText,
					Files: lo.Map(answerData.AnswerFiles, func(fileID string, _ int) *models.File {
						return &models.File{
							Model: models.Model{ID: fileID},
						}
					}),
					OptionID: &optionID,
				}
				if option, found := optionsByIDs[optionID]; found {
					answer.OptionUID = &option.UID
				}
				answers = append(answers, answer)
			}
			// If question enabled other option and user select other option
			if question.IsOtherOptionEnabled() && len(answerData.AnswerText) > 0 {
				answer := &models.FormAnswer{
					SessionID:      formSession.ID,
					FormID:         formSession.FormID,
					FormUID:        formSession.FormUID,
					UserID:         formSession.UserID,
					Key:            question.Settings.Key,
					QuestionID:     questionID,
					QuestionUID:    questionUID,
					SubQuestionID:  subQuestionID,
					SubQuestionUID: subQuestionUID,
					Text:           answerData.AnswerText,
					Files: lo.Map(answerData.AnswerFiles, func(fileID string, _ int) *models.File {
						return &models.File{
							Model: models.Model{ID: fileID},
						}
					}),
				}
				answers = append(answers, answer)
			}
		}
	}

	// Validate answers
	for _, answer := range answers {
		if vErr := s.validateAnswerForQuestion(answer, question); vErr != nil {
			return nil, vErr
		}
	}
	return answers, nil
}

func (s *FormService) validateAnswerForQuestion(answer *models.FormAnswer, question *models.FormQuestion) *e.AppError {
	switch question.QuestionType {
	case models.FormQuestionTypeInput, models.FormQuestionTypeTextarea:
		if question.IsRequired() && answer.IsEmptyAnswerText() {
			return e.NewError400(e.Form_missing_answer, "The answer must be not empty for question: "+question.Title)
		}

	case models.FormQuestionTypeNumber:
		if question.IsRequired() && answer.IsEmptyAnswerText() {
			return e.NewError400(e.Form_missing_answer, "The answer must be not empty for question: "+question.Title)
		}
		_, parseErr := strconv.ParseFloat(answer.Text, 32)
		if parseErr != nil {
			return e.NewError400(e.Form_invalid_answer, "The answer must be a number")
		}

	case models.FormQuestionTypeEmail:
		if question.IsRequired() && answer.IsEmptyAnswerText() {
			return e.NewError400(e.Form_missing_answer, "The answer must be not empty for question: "+question.Title)
		}
		_, parseErr := mail.ParseAddress(answer.Text)
		if parseErr != nil {
			return e.NewError400(e.Form_invalid_answer, "The answer must be an email")
		}

		disposableDomainMap := models.GetConfig[map[string]struct{}](models.DisposableEmailDomains)
		if models.GetConfig[bool](models.BlockDisposableEmailsEnabled) &&
			util.IsDisposableEmail(answer.Text, disposableDomainMap) {
			return e.NewError400(e.Form_invalid_answer, "Disposable email is not allowed")
		}

	case models.FormQuestionTypePhone:
		if question.IsRequired() && answer.IsEmptyAnswerText() {
			return e.NewError400(e.Form_missing_answer, "The answer must be not empty for question: "+question.Title)
		}
		if !util.IsValidPhoneNumber(answer.Text) {
			return e.NewError400(e.Form_invalid_answer, "The answer must be a phone number: "+question.Title)
		}

	case models.FormQuestionTypeCheckbox:
		if question.IsRequired() && answer.IsEmptyAnswerText() {
			return e.NewError400(e.Form_missing_answer, "The answer must be not empty for question: "+question.Title)
		}

		if answer.Text != "true" && answer.Text != "false" {
			return e.NewError400(e.Form_invalid_answer, "The checkbox answer must be boolean `true` or `false`: "+question.Title)
		}

	case models.FormQuestionTypeDateTimePicker:
		if question.IsRequired() && answer.IsEmptyAnswerText() {
			return e.NewError400(e.Form_missing_answer, "The answer must be not empty for question: "+question.Title)
		}

		_, parseErr := strconv.ParseInt(answer.Text, 10, 64)
		if parseErr != nil {
			return e.NewError400(e.Form_invalid_answer, "The answer must be a milli timestamp such as `1742922000000`: "+question.Title)
		}

	case models.FormQuestionTypeFile:
		if question.IsRequired() && answer.IsEmptyAnswerFile() {
			return e.NewError400(e.Form_missing_answer, "The answer must be not empty for question: "+question.Title)
		}

	case models.FormQuestionTypeSelectBox,
		models.FormQuestionTypeAutoComplete,
		models.FormQuestionTypeMultipleChoiceGrid,
		models.FormQuestionTypeMultipleChoice,
		models.FormQuestionTypeRadio,
		models.FormQuestionTypeMultipleSelection,
		models.FormQuestionTypeCheckboxChoiceGrid:
		if answer.OptionID != nil {
			validOptions := map[string]struct{}{}
			for _, option := range question.Options {
				validOptions[option.ID] = struct{}{}
			}
			if _, found := validOptions[*answer.OptionID]; !found {
				if !question.IsOtherOptionEnabled() {
					return e.NewError400(e.Form_invalid_answer, "The question is not enabled other option: "+question.Title)
				}
				answer.Text = *answer.OptionID
				answer.OptionID = nil
				answer.OptionUID = nil
			}
		} else {
			if question.IsRequired() && answer.IsEmptyAnswerText() {
				return e.NewError400(e.Form_invalid_answer, "The answer must be not empty for question: "+question.Title)
			}
		}

	case models.FormQuestionTypeHeading,
		models.FormQuestionTypeSpace,
		models.FormQuestionTypeImage,
		models.FormQuestionTypeSubmitBtn,
		models.FormQuestionTypeParagraph:
		return e.NewError400(e.Form_invalid_answer, "Heading, space, image, paragraph or submit button is not received answers")
	}
	return nil
}

func (s *FormService) GetEmptyForm() (*models.Form, *e.AppError) {
	query := &models.FormQuery{
		Event:          util.NewT(models.FormEventEmpty),
		IncludeDeleted: util.NewBool(false),
	}
	if form, err := s.FindOne(query, nil); err != nil {
		return nil, err
	} else {
		return form, nil
	}
}

func (s *FormService) CheckSubmittedByUIDs(user *models.User, formUIDs []string) (map[string]bool, *e.AppError) {
	cacheSubmittedByFormUIDs := map[string]struct{}{}
	err := models.Cache.FormSession.GetUserSubmitted(user.ID, &cacheSubmittedByFormUIDs)
	if err != nil {
		log.Errorf("FormService::CheckSubmittedByUIDs failed to get user form submitted cache: %v", err)
	}

	var toCheckFormUIDs []string
	hasSubmittedByFormUIDs := map[string]bool{}
	for _, formUID := range formUIDs {
		if _, found := cacheSubmittedByFormUIDs[formUID]; found {
			hasSubmittedByFormUIDs[formUID] = true
		} else {
			toCheckFormUIDs = append(toCheckFormUIDs, formUID)
		}
	}

	if len(toCheckFormUIDs) == 0 {
		return hasSubmittedByFormUIDs, nil
	}

	formSessions, err := models.Repository.FormSession.FindMany(&models.FormSessionQuery{
		FormUIDIn: toCheckFormUIDs,
		UserID:    &user.ID,
	}, &models.FindManyOptions{})
	if err != nil {
		return nil, e.NewError500(e.Form_session_find_many_failed, "Find form sessions error: "+err.Error())
	}

	for _, formSession := range formSessions {
		hasSubmittedByFormUIDs[formSession.FormUID] = true
		cacheSubmittedByFormUIDs[formSession.FormUID] = struct{}{}
	}

	if cErr := models.Cache.FormSession.SetUserSubmitted(user.ID, cacheSubmittedByFormUIDs); cErr != nil {
		log.Errorf("FormService::CheckSubmittedByUIDs failed to set user form submitted cache: %v", err)
	}

	return hasSubmittedByFormUIDs, nil
}
