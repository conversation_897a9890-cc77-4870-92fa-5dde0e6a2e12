package services

import (
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"

	"gorm.io/gorm"
)

func (s *PageConfigService) Create(data *dto.PageConfigRequest) (*models.PageConfig, *e.AppError) {
	pageConfig := models.PageConfig{
		ID:          data.ID,
		Name:        data.Name,
		Type:        data.Type,
		Actions:     data.Actions,
		Description: data.Description,
	}

	if err := models.Repository.PageConfig.Create(&pageConfig, nil); err != nil {
		return nil, e.NewError500(e.Create_page_config_fail, err.Error())
	}

	return &pageConfig, nil
}

func (s *PageConfigService) CreateOrUpdateMany(data *dto.BulkCreatePageConfigRequest) ([]*models.PageConfig, *e.AppError) {
	if data.Configs == nil || len(data.Configs) <= 0 {
		return nil, e.NewError400(e.INVALID_PARAMS, "configs required")
	}
	var toCreate []*models.PageConfig
	var result []*models.PageConfig

	for _, d := range data.Configs {
		c, cErr := models.Repository.PageConfig.FindOne(&models.PageConfigQuery{
			ID: util.NewString(d.ID),
		}, nil)
		if cErr != nil && !errors.Is(cErr, gorm.ErrRecordNotFound) {
			return nil, e.NewError500(e.Page_config_find_one_failed, cErr.Error())
		}
		if c == nil {
			pageConfig := models.PageConfig{
				ID:          d.ID,
				Name:        d.Name,
				Type:        d.Type,
				Actions:     d.Actions,
				Description: d.Description,
			}
			toCreate = append(toCreate, &pageConfig)
		} else {
			c.Type = d.Type
			c.Name = d.Name
			c.Actions = d.Actions
			c.Description = d.Description
			if err := models.Repository.PageConfig.Update(c, nil); err != nil {
				return nil, e.NewError500(e.Create_page_config_fail, "Update existed record failed: "+err.Error())
			} else {
				result = append(result, c)
			}
		}
	}

	if len(toCreate) > 0 {
		if err := models.Repository.PageConfig.CreateMany(toCreate, nil); err != nil {
			return nil, e.NewError500(e.Create_page_config_fail, "Create page config failed: "+err.Error())
		}
		result = append(result, toCreate...)
	}

	return result, nil
}

func (s *PageConfigService) Delete(pageConfig *models.PageConfig) *e.AppError {
	if err := models.Repository.PageConfig.Delete(pageConfig.ID, nil); err != nil {
		return e.NewError500(e.Delete_page_config_fail, err.Error())
	}
	return nil
}

func (s *PageConfigService) FindPage(query *models.PageConfigQuery, options *models.FindPageOptions) ([]*models.PageConfig, *models.Pagination, *e.AppError) {
	if pageConfigs, pagination, err := models.Repository.PageConfig.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Page_config_find_page_failed, err.Error())
	} else {
		// viet sanatize
		return pageConfigs, pagination, nil
	}

}

func (s *PageConfigService) FindOne(query *models.PageConfigQuery, options *models.FindOneOptions) (*models.PageConfig, *e.AppError) {
	if pageConfig, err := models.Repository.PageConfig.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Page_config_find_one_failed, err.Error())
		}
		return nil, e.NewError500(e.Page_config_find_one_failed, err.Error())
	} else {
		return pageConfig, nil
	}
}
