package services

import (
	"errors"
	"fmt"
	"net/url"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	commdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"sync"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

var (
	ErrEmailExistedInAnotherUser = errors.New("this email address is already been used by another user")
)

func (s *UserService) Create(user *models.User) (*models.User, *e.AppError) {
	user.Props.LearningStatusMigrated = true
	if uErr := models.Repository.User.Create(user); uErr != nil {
		return nil, e.NewError500(e.Error_auth_create_user_failed, uErr.Error())
	}

	//wErr := Wallet.InitUserWallets(nil, user)
	//if wErr != nil {
	//	return nil, e.NewError500(e.Error_auth_create_user_failed, "init user's wallets failed: "+wErr.Msg)
	//}

	usErr := UserSummary.Upsert(&models.UserSummary{
		UserID:       user.ID,
		Following:    util.DefaultFollowing,
		Followers:    util.DefaultFollowers,
		TotalBlogs:   util.DefaultTotalBlogs,
		TotalCourses: util.DefaultTotalCourses,
	}, nil)
	if usErr != nil {
		return nil, usErr
	}

	return user, nil
}

func getDefaultRoleByDomain() (*models.Role, error) {
	return models.Repository.Role.FindById(models.GuestRoleType)
}

func (s *UserService) FindUserByUsername(username string) (*models.User, *e.AppError) {
	user, err := models.Repository.User.FindByUsernameWithOpts(username, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, e.NewError500(e.Error_user_find_failed, err.Error())
	}

	return user, nil
}

func (s *UserService) CheckAndCreateUserFromSnsAccountData(snsAccount *models.SnsAccount, org *models.Organization, domain string) (*models.User, error) {
	role, rErr := getDefaultRoleByDomain()
	if rErr != nil {
		return nil, rErr
	}

	// Check sns account is link to user
	if snsAccount.UserID != "" {
		user, err := models.Repository.User.FindByID(snsAccount.UserID)
		if err != nil {
			return nil, err
		}

		userRoleOrg := models.UserRoleOrg{
			UserID: user.ID,
			RoleID: role.ID,
			OrgID:  org.ID,
		}
		if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
			return nil, rErr
		}

		return user, nil
	}

	userFind, err := models.Repository.User.FindByEmailWithOpts(snsAccount.Email, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			newUser, err := createNewUserFromSnsAccount(snsAccount, org, role)
			if err != nil {
				log.Error("Create a new user from sns account failed", err)
				return nil, err
			}

			return newUser, nil
		}

		return nil, err
	}

	if userFind != nil {
		existed, err := s.CheckProviderExisted(snsAccount.Provider, userFind.ID)
		if err != nil {
			log.Error("Check provider existed for user failed", err)
			return nil, err
		}

		// if provider is existed in user
		if existed {
			return nil, ErrEmailExistedInAnotherUser
		}

		userRoleOrg := models.UserRoleOrg{
			UserID: userFind.ID,
			RoleID: role.ID,
			OrgID:  org.ID,
		}
		if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
			return nil, rErr
		}
	}

	return userFind, nil
}

func (s *UserService) GetUserByID(userID string) (*models.User, *e.AppError) {
	user, err := models.Repository.User.FindByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Error_user_not_found, "User not found")
		}
		return nil, e.NewError500(e.Error_user_find_failed, "Find user by ID failed")
	}

	if userRoles, rErr := models.Repository.UserRoleOrg.FindByUserId(user.ID); rErr != nil {
		return nil, e.NewError500(e.Find_user_role_failed, "Find by userID failed")
	} else {
		lo.ForEach(userRoles, func(item *models.UserRoleOrg, _ int) {
			org, _ := models.Repository.Organization.FindByID(item.OrgID, nil)
			if org != nil {
				item.OrgDomain = org.Domain
			}
		})
		user.Roles = userRoles
	}

	return user, nil
}

func (s *UserService) FindByID(id string, options *models.FindOneOptions) (*models.User, *e.AppError) {
	if options == nil {
		user, err := models.Repository.User.FindByID(id)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, e.NewError404(e.User_not_found, err.Error())
			}
			return nil, e.NewError500(e.Error_user_find_failed, err.Error())
		}
		return user, nil
	}
	user, err := models.Repository.User.FindByIDWithOpts(id, options)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.User_not_found, err.Error())
		}
		return nil, e.NewError500(e.Error_user_find_failed, err.Error())
	}
	return user, nil
}

func (s *UserService) GetUserProfile(user *models.User, org *models.Organization) (*dto.UserProfileResponse, *e.AppError) {
	if userRoles, rErr := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		UserID: &user.ID,
	}, &models.FindManyOptions{}); rErr != nil {
		return nil, e.NewError500(e.Find_user_role_failed, "Find by userID failed")
	} else {
		lo.ForEach(userRoles, func(item *models.UserRoleOrg, _ int) {
			org, _ := models.Repository.Organization.FindByID(item.OrgID, nil)
			if org != nil {
				item.OrgDomain = org.Domain
			}
		})
		user.Roles = userRoles
	}

	userSummary, fErr := UserSummary.FindOne(&models.UserSummaryQuery{
		UserID: &user.ID,
	}, &models.FindOneOptions{})

	if fErr != nil {
		return nil, fErr
	}

	userProfile, bErr := buildUserProfileResponse(user, userSummary)
	if bErr != nil {
		return nil, bErr
	}

	response, appErr := buildSettingDisplayForUserProfile(user, userProfile, org)
	if appErr != nil {
		return nil, appErr
	}

	return response, nil
}

func buildUserProfileResponse(user *models.User, userSummary *models.UserSummary) (*dto.UserProfileResponse, *e.AppError) {
	simpleRoles := make([]*models.SimpleUserRole, 0)
	if user.Roles != nil {
		simpleRoles = lo.Map(user.Roles, func(item *models.UserRoleOrg, _ int) *models.SimpleUserRole {
			return item.ToSimple()
		})
	}

	orgsAsWriter, wErr := UserSummary.GetOrgsAsWriter(user)
	if wErr != nil {
		return nil, wErr
	}

	userProfile := &dto.UserProfileResponse{
		ID:           user.ID,
		Username:     user.Username,
		Email:        user.Email,
		Active:       user.Active,
		Blocked:      user.Blocked,
		DisplayName:  user.DisplayName,
		Avatar:       user.Avatar,
		CoverPhoto:   user.CoverPhoto,
		Skills:       user.Skills,
		Headline:     user.Headline,
		About:        user.About,
		Phone:        user.Phone,
		Position:     user.Position,
		Props:        user.Props,
		Roles:        simpleRoles,
		Status:       "",
		WriterInOrgs: orgsAsWriter,
	}

	if userSummary != nil {
		userProfile.Following = userSummary.Following
		userProfile.Followers = userSummary.Followers
		userProfile.TotalBlogs = userSummary.TotalBlogs
		userProfile.TotalCourses = userSummary.TotalCourses
	}

	return userProfile, nil
}

func (s *UserService) GetUserProfileByID(params *dto.GetUserProfileByIDParams, org *models.Organization) (*dto.UserProfileResponse, *e.AppError) {
	user, err := models.Repository.User.FindByUsernameWithOpts(params.TargetUsername, &models.FindOneOptions{
		Preloads: []string{models.SummaryField},
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Error_user_not_found, "User not found")
		}
		return nil, e.NewError500(e.Error_user_find_failed, "Find user by ID failed")
	}

	if userRoles, rErr := models.Repository.UserRoleOrg.FindMany(&models.UserRoleOrgQuery{
		UserID: &user.ID,
	}, &models.FindManyOptions{}); rErr != nil {
		return nil, e.NewError500(e.Find_user_role_failed, "Find by userID failed")
	} else {
		lo.ForEach(userRoles, func(item *models.UserRoleOrg, _ int) {
			org, _ := models.Repository.Organization.FindByID(item.OrgID, nil)
			if org != nil {
				item.OrgDomain = org.Domain
			}
		})
		user.Roles = userRoles
	}

	userProfile, bErr := buildUserProfileResponse(user, user.Summary)
	if bErr != nil {
		return nil, bErr
	}

	if params.User != nil {
		if util.NewString(params.User.ID) != nil {
			userAction, aErr := models.Repository.UserAction.FindOne(&models.UserActionQuery{
				UserID:       &params.User.ID,
				TargetUserID: &user.ID,
				ActionNe:     util.NewT(models.Reported),
			}, &models.FindOneOptions{
				Sort: []string{models.UpdateAtDESC},
			})

			if aErr != nil {
				if !errors.Is(aErr, gorm.ErrRecordNotFound) {
					return nil, e.NewError500(e.Find_one_user_action_failed, "Find user by ID failed")
				}
			} else {
				userProfile.Status = userAction.Action
			}
		}
	}

	response, appErr := buildSettingDisplayForUserProfile(user, userProfile, org)
	if appErr != nil {
		return nil, appErr
	}

	return response, nil
}

func (s *UserService) Update(user *models.User) (*models.User, *e.AppError) {
	user, err := models.Repository.User.Update(user)

	if err != nil {
		return nil, e.NewError500(e.Error_user_update_failed, err.Error())
	}

	return user, nil
}

func (s *UserService) WithdrawUser(user *models.User) error {
	tx := models.GetDb(models.UserTbl).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			log.Error("panic: %v", r)
		}
	}()

	err := models.Repository.Session.DeleteByUserID(user.ID, tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	_, err = models.Repository.SnsAccount.DeleteMany(&models.FindSnsAccountsQuery{
		UserID:    &user.ID,
		IsDeleted: util.NewBool(false),
	}, tx)

	if err != nil {
		tx.Rollback()
		return err
	}

	err = models.Repository.User.WithdrawUser(user.ID, user.Email, tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (s *UserService) CheckProviderExisted(provider util.SNSProvider, userID string) (bool, error) {
	// Find linked sns accounts for user
	querySnsAccounts := &models.FindSnsAccountsQuery{
		UserID:    &userID,
		Provider:  provider,
		IsDeleted: util.NewBool(false),
	}
	_, err := models.Repository.SnsAccount.FindOne(querySnsAccounts)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}

		return false, err
	}

	return true, nil
}

func createNewUserFromSnsAccount(snsAccount *models.SnsAccount, org *models.Organization, role *models.Role) (*models.User, error) {
	userData := &models.User{
		Email:    snsAccount.Email,
		Username: util.GenerateId(),
		Avatar:   snsAccount.Avatar,
		Active:   true,
	}

	if _, uErr := User.Create(userData); uErr != nil {
		return nil, errors.New(uErr.Msg)
	}

	userRoleOrg := models.UserRoleOrg{
		UserID: userData.ID,
		RoleID: role.ID,
		OrgID:  org.ID,
	}
	if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
		return nil, rErr
	}

	// if create new user success set sns account is default
	snsAccount.IsDefault = true
	_, sErr := models.Repository.SnsAccount.Update(snsAccount, nil)
	if sErr != nil {
		return nil, sErr
	}

	return userData, nil
}

func (s *UserService) FindMany(query *models.UserQuery, options *models.FindManyOptions) ([]*models.User, *e.AppError) {
	if users, err := models.Repository.User.FindMany(query, options); err != nil {
		return nil, e.NewError500(e.Find_many_user_failed, err.Error())
	} else {
		return users, nil
	}
}

func (s *UserService) CreateUserForOrg(params *dto.CreateUser, role ...string) (*models.User, *e.AppError) {
	if ok, cErr := models.Repository.User.CanRegister(params.Username, params.Email); cErr != nil {
		return nil, e.NewError500(e.Error_auth_verify_username_email_failed, "")
	} else if !ok {
		return nil, e.NewError500(e.Error_auth_email_already_used, "")
	}

	// Create user with active = false
	user := &models.User{
		Username:    util.GenerateId(),
		Email:       params.Email,
		Phone:       params.Phone,
		Password:    params.Password,
		Active:      true,
		DisplayName: lo.If(params.DisplayName != "", params.DisplayName).Else(params.Username),
		Avatar:      "",
	}

	if _, uErr := User.Create(user); uErr != nil {
		return nil, uErr
	}

	userRoleOrg := models.UserRoleOrg{
		UserID: user.ID,
		RoleID: models.LearnerRoleType,
		OrgID:  *params.OrgID,
	}

	if len(role) > 0 {
		userRoleOrg.RoleID = role[0]
	}

	if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
		return nil, e.NewError500(e.Error_auth_add_user_role_failed, rErr.Error())
	}

	return user, nil
}

func (s *UserService) ChangePassword(params *dto.ChangePasswordParams) (*models.SimpleUser, *e.AppError) {
	user := params.User

	// check if match old password
	if err := user.ComparePassword(params.OldPassword); err != nil {
		return nil, e.NewError401(e.Error_auth_invalid_password, err.Error())
	}

	user.Password = params.NewPassword
	updatedUser, err := models.Repository.User.UpdatePassword(user)
	if err != nil {
		return nil, e.NewError500(e.Error_user_update_failed, err.Error())
	}
	return updatedUser.ToSimpleUserProfile(), nil
}

func (s *UserService) HandleApproveCreator(org *models.Organization, params *dto.CreateCreatorRequest) (*models.User, string, *e.AppError) {
	if existUser, _ := models.Repository.User.FindByEmailWithOpts(params.Email, &models.FindOneOptions{}); existUser != nil {
		return updateRoleAndSendMailUser(org, existUser, models.EventApproveRegisterCreatorExistingUser)
	} else {
		user := &models.User{
			Username:           util.GenerateId(),
			Email:              params.Email,
			Phone:              params.Phone,
			Password:           util.GenerateCode(8),
			Active:             true,
			DisplayName:        params.DisplayName,
			Avatar:             "",
			RequireSetPassword: true,
		}

		if _, uErr := User.Create(user); uErr != nil {
			return nil, "", uErr
		}

		return updateRoleAndSendMailUser(org, user, models.EventApproveRegisterCreatorNewUser)
	}
}

func (s *UserService) HandleApproveWriter(org *models.Organization, params *dto.CreateWriterRequest) (*models.User, string, *e.AppError) {
	if existUser, _ := models.Repository.User.FindByEmailWithOpts(params.Email, &models.FindOneOptions{}); existUser != nil {
		return updateRoleAndSendMailUser(org, existUser, models.EventApproveRegisterWriterExistingUser)
	} else {
		user := &models.User{
			Username:           util.GenerateId(),
			Email:              params.Email,
			Phone:              params.Phone,
			Password:           util.GenerateCode(8),
			Active:             true,
			DisplayName:        params.DisplayName,
			Avatar:             "",
			RequireSetPassword: true,
		}

		if _, uErr := User.Create(user); uErr != nil {
			return nil, "", uErr
		}

		return updateRoleAndSendMailUser(org, user, models.EventApproveRegisterWriterNewUser)
	}
}

func (s *UserService) HandleInviteCreator(org *models.Organization, params *dto.InviteCreatorRequest) *e.AppError {
	// Retrieve existing UserTokens
	existingTokens, err := models.Repository.UserToken.FindMany(&models.UserTokenQuery{
		OrgID:   util.NewString(org.ID),
		Event:   util.NewT(models.EventInviteCreator),
		EmailIn: util.NewT(params.CreatorEmails),
	}, nil)
	if err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	// Filter out existing tokens
	existingEmails := make(map[string]bool)
	for _, token := range existingTokens {
		existingEmails[token.Email] = true
	}

	var listUT []*models.UserToken
	for _, email := range params.CreatorEmails {
		if existingEmails[email] {
			continue
		}
		token := util.GenerateToken()
		userToken := &models.UserToken{
			Email:      email,
			Token:      token,
			ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
			Event:      models.EventInviteCreator,
			SendEmail:  util.GetCurrentTime(),
			OrgID:      org.ID,
		}
		listUT = append(listUT, userToken)
	}

	// Create new UserTokens
	if len(listUT) > 0 {
		if err := models.Repository.UserToken.CreateMany(listUT, nil); err != nil {
			return e.NewError500(e.Error_create_user_token_failed, err.Error())
		}
	}

	// Resend already sent tokens + send new tokens
	//Resend
	existTokensIds := lo.Map(existingTokens, func(token *models.UserToken, _ int) string {
		return token.ID
	})
	if err := UserToken.ResendMail(&dto.ResendMailInvitationRequest{UserTokenIDs: existTokensIds}, org); err != nil {
		return err
	}

	// Send mail to new users
	users, err := models.Repository.User.FindMany(&models.UserQuery{EmailIn: params.CreatorEmails}, nil)
	if err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	usersByEmails := map[string]*models.User{}
	for _, user := range users {
		usersByEmails[user.Email] = user
	}

	var wg sync.WaitGroup
	var mailErr *e.AppError
	for _, ut := range listUT {
		wg.Add(1)
		go func(userToken *models.UserToken, usersByEmails map[string]*models.User) {
			defer func() {
				if r := recover(); r != nil {
					log.Error("Recovered in handleResendInviteCreator", r)
				}
				wg.Done()
			}()

			user := usersByEmails[ut.Email]
			if user == nil {
				user = &models.User{DisplayName: "Creator", Email: ut.Email}
			}

			mailParams := commdto.MapEmailParams{
				commdto.EmailParamUserToken: userToken,
			}

			if v, ok := params.AllowFieldsData[util.NextPathField]; ok {
				mailParams[commdto.EmailParamNextPath] = v
			}
			go func() {
				var eErr error
				defer func() {
					if r := recover(); r != nil {
						eErr = fmt.Errorf("panic: %v", r)
					}

					if eErr != nil {
						log.ErrorWithAlertf("UserService.HandleInviteCreator::Send email invite creators failed: %v", eErr)
					}
				}()

				_, eErr = communication.Email.SendEmail(&commdto.SendEmailRequest{
					User:        user.IntoComm(),
					Org:         org.IntoComm(),
					Event:       commdto.EventInviteCreatorBeforeAccept,
					ExtendDatas: mailParams,
					IsQueue:     true,
				})
			}()
		}(ut, usersByEmails)
	}

	wg.Wait()

	return mailErr
}

func (s *UserService) HandleAcceptInviteCreator(org *models.Organization, params *dto.AcceptInviteRequest) (*models.User, string, *e.AppError) {
	userToken, err := models.Repository.UserToken.FindOne(&models.UserTokenQuery{Token: &params.Token}, &models.FindOneOptions{})
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, "", e.NewError400(e.Error_user_token_not_found, "User token not found")
		}
		return nil, "", e.NewError500(e.Error_user_token_find_failed, "Get user token error: "+err.Error())
	}

	if userToken.IsExpired() {
		if params.User != nil && params.User.Email == userToken.Email {
			log.Infof("Invitation already expired but user ID %s logged in, continue proceeding accept request", params.User.ID)
		} else {
			return nil, "", e.NewError400(e.Error_user_token_expired, "User token expired")
		}
	}

	if userToken.IsVerified() {
		return nil, "", e.NewError400(e.Error_user_token_verified, "User token already verified")
	}

	var existUser *models.User
	if params.User != nil && params.User.Email == userToken.Email {
		existUser = params.User
	} else {
		existUser, err = models.Repository.User.FindByEmailWithOpts(userToken.Email, &models.FindOneOptions{})
		if err != nil && !models.IsRecordNotFound(err) {
			return nil, "", e.NewError500(e.Error_user_find_failed, "Get user by email error: "+err.Error())
		}
	}

	if existUser != nil {
		existUser.RequireSetPassword = false
		updatedExistsUser, updateUserErr := models.Repository.User.Update(existUser)
		if updateUserErr != nil {
			return nil, "", e.NewError500(e.Error_user_update_failed, updateUserErr.Error())
		}

		now := util.GetCurrentTime()
		userToken.VerifyDate = now
		userToken.UserID = &existUser.ID
		if utErr := models.Repository.UserToken.Update(userToken, nil); utErr != nil {
			return nil, "", e.NewError500(e.Error_update_user_token_failed, utErr.Error())
		}

		return updateRoleAndSendMailUser(org, updatedExistsUser, models.EventAcceptInviteCreatorExistingUser)
	}

	user := &models.User{
		Username:           util.GenerateId(),
		Email:              userToken.Email,
		Password:           util.GenerateCode(8),
		Active:             true,
		DisplayName:        userToken.Email,
		Avatar:             "",
		RequireSetPassword: true,
	}

	if _, uErr := User.Create(user); uErr != nil {
		return nil, "", uErr
	}

	now := util.GetCurrentTime()
	userToken.VerifyDate = now
	userToken.UserID = &user.ID

	if utErr := models.Repository.UserToken.Update(userToken, nil); utErr != nil {
		return nil, "", e.NewError500(e.Error_update_user_token_failed, utErr.Error())
	}

	return updateRoleAndSendMailUser(org, user, models.EventAcceptInviteCreatorNewUser)
}

func (s *UserService) FindOne(query *models.UserQuery, options *models.FindOneOptions) (*models.User, *e.AppError) {
	if user, err := models.Repository.User.FindOne(query, options); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Blog_not_found, err.Error())
		}
		return nil, e.NewError500(e.Error_user_find_failed, err.Error())
	} else {
		return user, nil
	}
}

func (s *UserService) FindPage(query *models.UserQuery, options *models.FindPageOptions) ([]*models.User, *models.Pagination, *e.AppError) {
	if users, pagination, err := models.Repository.User.FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.Error_user_find_failed, err.Error())
	} else {
		return users, pagination, nil
	}
}

func (s *UserService) UpdateByAdmin(u *models.User, data *dto.UpdateUserRequest) (*models.User, *e.AppError) {
	u.DisplayName = data.DisplayName
	u.Phone = data.Phone
	u.Active = data.Active
	u.Avatar = data.Avatar
	u.Blocked = data.Blocked
	u.Password = data.Password
	if user, err := models.Repository.User.UpdateByAdmin(u); err != nil {
		return nil, e.NewError500(e.Error_user_update_failed, err.Error())
	} else {
		return user, nil
	}
}

func (s *UserService) FindPageUserOrg(query *models.UserQuery, option *models.FindPageOptions) ([]*models.SimpleProfile, *models.Pagination, *e.AppError) {
	userRoleOrgs, pagination, err := models.Repository.User.FindUserFromRoles(query, option)
	if err != nil {
		return nil, nil, e.NewError500(e.Error_user_role_org_find_failed, err.Error())
	}
	users := []*models.SimpleProfile{}
	if len(userRoleOrgs) > 0 {
		users = lo.Map(userRoleOrgs, func(item *models.User, _ int) *models.SimpleProfile {
			return item.ToSimpleProfile()
		})
	}
	return users, pagination, nil
}

func (s *UserService) groupByRole(data []dto.RoleOrgParamsRequest) map[string][]string {
	roleMap := make(map[string][]string)
	for _, roleOrgParams := range data {
		for _, role := range roleOrgParams.RoleID {
			roleMap[role] = append(roleMap[role], roleOrgParams.UserID)
		}
	}
	return roleMap
}

func (s *UserService) checkPermission(updater *models.User, roleID string) bool {
	switch roleID {
	case models.SystemAdminRoleType:
		return s.canUpdateRoleAdmin(updater)
	case models.ModeratorRoleType:
		return s.canUpdateRoleMod(updater)
	case models.OrgAdminRoleType:
		return s.canUpdateRoleOrgAdmin(updater)
	case models.OrgModeratorRoleType:
		return s.canUpdateRoleOrgMod(updater)
	case models.PartnerRoleType:
		return s.canUpdateRolePartner(updater)
	case models.OrgEditorRoleType:
		return s.canUpdateRoleEditor(updater)
	case models.OrgWriterRoleType:
		return s.canUpdateRoleWriter(updater)
	default:
		return false
	}
}

func (s *UserService) HandleAddRemoveUserRole(updater *models.User, data *dto.AddRemoveRoleRequest) *e.AppError {
	mapAddRoleUsers := s.groupByRole(data.AddIds)
	mapRemoveRoleUsers := s.groupByRole(data.RemoveIds)

	listUserRoleOrgAdd := []*models.UserRoleOrg{}
	for roleID, addUsers := range mapAddRoleUsers {
		if s.checkPermission(updater, roleID) {
			listUserRoleOrgAdd = append(listUserRoleOrgAdd, lo.Map(addUsers, func(userID string, _ int) *models.UserRoleOrg {
				return &models.UserRoleOrg{
					UserID: userID,
					RoleID: roleID,
					OrgID:  *data.OrgID,
				}
			})...)
		} else {
			return e.NewError403(e.Error_auth_add_user_role_failed, fmt.Sprintf("Permission denied for update role %s", roleID))
		}
	}

	listCombineKeyRemove := []string{}
	for roleID, removeUsers := range mapRemoveRoleUsers {
		if s.checkPermission(updater, roleID) {
			listCombineKeyRemove = append(listCombineKeyRemove, lo.Map(removeUsers, func(userID string, _ int) string {
				return userID + roleID + *data.OrgID
			})...)
		} else {
			return e.NewError403(e.Error_auth_remove_user_role_failed, fmt.Sprintf("Permission denied for update role %s", roleID))

		}
	}

	//Add role multiple
	if len(listUserRoleOrgAdd) > 0 {
		if err := models.Repository.UserRoleOrg.UpsertManyRole(listUserRoleOrgAdd, nil); err != nil {
			return e.NewError500(e.Error_auth_add_user_role_failed, err.Error())
		}

	}
	//Remove role multiple
	if len(listCombineKeyRemove) > 0 {
		_, err := models.Repository.UserRoleOrg.DeleteMany(&models.UserRoleOrgQuery{CombineKeyIN: listCombineKeyRemove}, nil)
		if err != nil {
			return e.NewError500(e.Error_auth_remove_user_role_failed, err.Error())
		}

		for _, removeRequest := range data.RemoveIds {
			for _, roleID := range removeRequest.RoleID {
				var notificationReq *commdto.PushNotificationRequest
				switch roleID {
				case models.OrgEditorRoleType:
					notificationReq = &commdto.PushNotificationRequest{
						Code:       commdto.CodeNewOrgEditorRemoved,
						EntityID:   removeRequest.UserID,
						EntityType: commdto.UserEntity,
						RuleTree: &commdto.TreeNodeRequest{
							Rule: &commdto.Rule{
								Subject:   commdto.NotiUser,
								Verb:      commdto.IsIn,
								ObjectIDs: []string{removeRequest.UserID},
							},
						},
						Props: s.makeNotificationPropsRemoveRoles().IntoComm(),
					}

				case models.OrgWriterRoleType:
					notificationReq = &commdto.PushNotificationRequest{
						Code:       commdto.CodeNewOrgWriterRemoved,
						EntityID:   removeRequest.UserID,
						EntityType: commdto.UserEntity,
						RuleTree: &commdto.TreeNodeRequest{
							Rule: &commdto.Rule{
								Subject:   commdto.NotiUser,
								Verb:      commdto.IsIn,
								ObjectIDs: []string{removeRequest.UserID},
							},
						},
						Props: s.makeNotificationPropsRemoveRoles().IntoComm(),
					}
				}
				if err = communication.Notification.PushNotification(notificationReq); err != nil {
					log.Errorf("Push notification to user ID %s after sending invitation error: %v", removeRequest.UserID, err)
				}
			}
		}
	}

	return nil
}

func (s *UserService) makeNotificationPropsRemoveRoles() models.JSONB {
	return models.JSONB{}
}

func (s *UserService) HandleInviteUser(org *models.Organization, data *dto.InviteUserRequest) *e.AppError {
	// Handle create user token for verify later
	event := models.EventInviteUser
	if data.Event != "" {
		event = data.Event
	}
	var listUT []*models.UserToken
	for _, email := range data.UserEmails {
		token := util.GenerateToken()
		props := models.JSONB{}
		if data.ObjectType != nil {
			props["object_type"] = data.ObjectType
			props["object_id"] = data.ObjectID
		}
		userToken := &models.UserToken{
			Email:      email,
			Token:      token,
			ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
			Event:      event,
			SendEmail:  util.GetCurrentTime(),
			OrgID:      org.ID,
			Props:      props,
		}
		listUT = append(listUT, userToken)
	}

	if err := models.Repository.UserToken.CreateMany(listUT, nil); err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}
	// Handle send email to verify
	users, err := models.Repository.User.FindMany(&models.UserQuery{EmailIn: data.UserEmails}, nil)
	if err != nil {
		return e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	usersByEmails := map[string]*models.User{}
	for _, user := range users {
		usersByEmails[user.Email] = user
	}

	var wg sync.WaitGroup
	var mailErr *e.AppError
	for _, ut := range listUT {
		wg.Add(1)
		go func(userToken *models.UserToken, usersByEmails map[string]*models.User) {
			defer func() {
				if r := recover(); r != nil {
					log.Error("Recovered in handleResendInviteUser", r)
				}
				wg.Done()
			}()
			user := usersByEmails[ut.Email]
			if user == nil {
				user = &models.User{DisplayName: "User", Email: ut.Email}
			}

			go func() {
				var emailEvent models.EventType
				switch data.Event {
				case models.EventInviteReferrer:
					emailEvent = models.EventInviteReferrer
				case models.EventInviteOrgWriter, models.EventInviteOrgEditor:
					emailEvent = models.EventInviteUserBeforeAccept
				default:
					emailEvent = models.EventInviteUserBeforeAccept
				}

				mailParams := commdto.MapEmailParams{
					commdto.EmailParamUserToken: userToken,
				}

				if v, ok := data.AllowFieldsData[util.NextPathField]; ok {
					mailParams[commdto.EmailParamNextPath] = v
				}

				if _, eErr := communication.Email.SendEmail(&commdto.SendEmailRequest{
					User:        user.IntoComm(),
					Org:         org.IntoComm(),
					Event:       emailEvent.IntoComm(),
					ExtendDatas: mailParams,
				}); eErr != nil {
					log.ErrorWithAlertf("UserService.HandleInviteUser::Send email invite users failed: %v", eErr)
				}
			}()

			if data.Event == "" {
				return
			}

			var notificationReq *commdto.PushNotificationRequest
			switch data.Event {
			case models.EventInviteOrgWriter:
				notificationReq = &commdto.PushNotificationRequest{
					Code:       commdto.CodeNewOrgWriterAdded,
					EntityID:   user.ID,
					EntityType: commdto.UserEntity,
					RuleTree: &commdto.TreeNodeRequest{
						Rule: &commdto.Rule{
							Subject:   commdto.NotiUser,
							Verb:      commdto.IsIn,
							ObjectIDs: []string{user.ID},
						},
					},
					Props: s.makeNotificationPropsForInvitation(userToken, org).IntoComm(),
				}

			case models.EventInviteOrgEditor:
				notificationReq = &commdto.PushNotificationRequest{
					Code:       commdto.CodeNewOrgEditorAdded,
					EntityID:   user.ID,
					EntityType: commdto.UserEntity,
					RuleTree: &commdto.TreeNodeRequest{
						Rule: &commdto.Rule{
							Subject:   commdto.NotiUser,
							Verb:      commdto.IsIn,
							ObjectIDs: []string{user.ID},
						},
					},
					Props: s.makeNotificationPropsForInvitation(userToken, org).IntoComm(),
				}
			}
			if err = communication.Notification.PushNotification(notificationReq); err != nil {
				log.Errorf("Push notification to user ID %s after sending invitation error: %v", user.ID, err)
			}
		}(ut, usersByEmails)
	}

	wg.Wait()

	return mailErr
}

func (s *UserService) makeNotificationPropsForInvitation(userToken *models.UserToken, org *models.Organization) models.JSONB {
	return models.JSONB{
		"token":      userToken.Token,
		"org_id":     org.ID,
		"org_domain": org.Domain,
	}
}

func updateRoleAndSendMailUser(org *models.Organization, user *models.User, event models.EventType) (*models.User, string, *e.AppError) {
	// create user token
	userToken := &models.UserToken{
		UserID:     &user.ID,
		Email:      user.Email,
		User:       user,
		Token:      util.GenerateToken(),
		ExpiryTime: util.AddCurrentTimeWithHour(setting.EmailSetting.TokenExpireIn),
		SendEmail:  util.GetCurrentTime(),
	}
	switch event {
	//user
	case models.EventAcceptInviteUserExistingUser,
		models.EventAcceptInviteEditorExistingUser,
		//writer
		models.EventAcceptInviteWriterExistingUser,
		models.EventApproveRegisterWriterExistingUser,
		//creator
		models.EventAcceptInviteCreatorExistingUser,
		models.EventApproveRegisterCreatorExistingUser:

		userToken.Event = models.EventResetPassword
		//user
	case models.EventAcceptInviteUserNewUser,
		models.EventAcceptInviteEditorNewUser,
		//creator
		models.EventAcceptInviteCreatorNewUser,
		models.EventApproveRegisterCreatorNewUser,
		//writer
		models.EventAcceptInviteWriterNewUser,
		models.EventApproveRegisterWriterNewUser:

		userToken.Event = models.EventExternalRegister

	}

	if err := models.Repository.UserToken.Create(userToken, nil); err != nil {
		return nil, "", e.NewError500(e.Error_create_user_token_failed, err.Error())
	}

	// Update role
	userRoleOrg := models.UserRoleOrg{
		UserID: user.ID,
		RoleID: models.LearnerRoleType,
		OrgID:  org.ID,
	}

	if roleErr := models.Repository.User.UpdateRoles(&userRoleOrg); roleErr != nil {
		return nil, "", e.NewError500(e.Error_auth_add_user_role_failed, roleErr.Error())
	}

	// Handle add additional role
	switch event {
	//editor
	case models.EventAcceptInviteEditorNewUser,
		models.EventAcceptInviteEditorExistingUser:

		userRoleOrg.RoleID = models.OrgEditorRoleType
		//writer
	case models.EventAcceptInviteWriterNewUser,
		models.EventAcceptInviteWriterExistingUser,
		models.EventApproveRegisterWriterExistingUser,
		models.EventApproveRegisterWriterNewUser:

		userRoleOrg.RoleID = models.OrgWriterRoleType
	case models.EventAcceptInviteCreatorExistingUser,
		models.EventAcceptInviteCreatorNewUser,
		models.EventApproveRegisterCreatorExistingUser,
		models.EventApproveRegisterCreatorNewUser:

		userRoleOrg.RoleID = models.PartnerRoleType
	}

	if userRoleOrg.RoleID != models.LearnerRoleType {
		if roleErr := models.Repository.User.UpdateRoles(&userRoleOrg); roleErr != nil {
			return nil, "", e.NewError500(e.Error_auth_add_user_role_failed, roleErr.Error())
		}

	}

	// Send email depend on event
	go func() {
		var eErr error
		defer func() {
			if r := recover(); r != nil {
				eErr = fmt.Errorf("panic: %v", r)
			}

			if eErr != nil {
				log.ErrorWithAlertf("UserService.updateRoleAndSendMailUser::Send email failed: %v", eErr)
			}
		}()

		_, eErr = communication.Email.SendEmail(&commdto.SendEmailRequest{
			User:  user.IntoComm(),
			Org:   org.IntoComm(),
			Event: event.IntoComm(),
			ExtendDatas: commdto.MapEmailParams{
				commdto.EmailParamUserToken: userToken,
			},
			IsQueue: true,
		})
	}()

	if event == models.EventAcceptInviteUserExistingUser {
		return user, "", nil
	}

	return user, url.QueryEscape(userToken.GenerateEmailToken()), nil
}

func (s *UserService) HandleAcceptInvitationUser(org *models.Organization, data *dto.AcceptInviteUserRequest) (*models.User, string, *e.AppError) {
	userToken, err := models.Repository.UserToken.FindOne(&models.UserTokenQuery{Token: &data.Token}, &models.FindOneOptions{})
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, "", e.NewError400(e.Error_user_token_not_found, "User token not found")
		}
		return nil, "", e.NewError500(e.Error_user_token_find_failed, "Get user token error: "+err.Error())
	}

	if userToken.IsExpired() {
		if data.User != nil && data.User.Email == userToken.Email {
			log.Infof("Invitation already expired but user ID %s logged in, continue proceeding accept request", data.User.ID)
		} else {
			return nil, "", e.NewError400(e.Error_user_token_expired, "User token expired")
		}
	}

	if userToken.IsVerified() {
		return nil, "", e.NewError400(e.Error_user_token_verified, "User token already verified")
	}

	var existUser *models.User
	if data.User != nil && data.User.Email == userToken.Email {
		existUser = data.User
	} else {
		existUser, err = models.Repository.User.FindByEmailWithOpts(userToken.Email, &models.FindOneOptions{})
		if err != nil && !models.IsRecordNotFound(err) {
			return nil, "", e.NewError500(e.Error_user_find_failed, "Get user by email error: "+err.Error())
		}
	}

	if existUser != nil {
		existUser.RequireSetPassword = false
		updatedExistsUser, updateUserErr := models.Repository.User.Update(existUser)
		if updateUserErr != nil {
			return nil, "", e.NewError500(e.Error_user_update_failed, updateUserErr.Error())
		}

		now := util.GetCurrentTime()
		userToken.VerifyDate = now
		userToken.UserID = &existUser.ID
		if utErr := models.Repository.UserToken.Update(userToken, nil); utErr != nil {
			return nil, "", e.NewError500(e.Error_update_user_token_failed, utErr.Error())
		}

		var eventNextStep models.EventType
		switch userToken.Event {
		case models.EventInviteOrgEditor:
			eventNextStep = models.EventAcceptInviteEditorExistingUser

		case models.EventInviteOrgWriter:
			eventNextStep = models.EventAcceptInviteWriterExistingUser

		default:
			eventNextStep = models.EventAcceptInviteUserExistingUser
		}

		user, emailToken, appErr := updateRoleAndSendMailUser(org, updatedExistsUser, eventNextStep)
		if appErr != nil {
			return nil, "", appErr
		}

		return user, emailToken, nil
	}

	user := &models.User{
		Username:           util.GenerateId(),
		Email:              userToken.Email,
		Password:           util.GenerateCode(8),
		Active:             true,
		DisplayName:        userToken.Email,
		Avatar:             "",
		RequireSetPassword: true,
	}

	if _, uErr := User.Create(user); uErr != nil {
		return nil, "", uErr
	}

	now := util.GetCurrentTime()
	userToken.VerifyDate = now
	userToken.UserID = &user.ID

	if utErr := models.Repository.UserToken.Update(userToken, nil); utErr != nil {
		return nil, "", e.NewError500(e.Error_update_user_token_failed, utErr.Error())
	}

	switch userToken.Event {
	case models.EventInviteReferrer:
		referrerID := userToken.Props["object_id"]
		if refErr := Referrer.InviteReferrerSuccess(user, fmt.Sprintf("%v", referrerID)); refErr != nil {
			return nil, "", e.NewError500(e.Error_update_user_token_failed, refErr.Msg)
		}
		break
	default:
		break
	}

	var eventNextStep models.EventType
	var notificationReq *commdto.PushNotificationRequest
	switch userToken.Event {
	case models.EventInviteOrgEditor:
		eventNextStep = models.EventAcceptInviteEditorNewUser
		notificationReq = &commdto.PushNotificationRequest{
			Code:       commdto.CodeNewOrgEditorAdded,
			EntityID:   user.ID,
			EntityType: commdto.UserEntity,
			RuleTree: &commdto.TreeNodeRequest{
				Rule: &commdto.Rule{
					Subject:   commdto.NotiUser,
					Verb:      commdto.IsIn,
					ObjectIDs: []string{user.ID},
				},
			},
		}
	case models.EventInviteOrgWriter:
		eventNextStep = models.EventAcceptInviteWriterNewUser
		notificationReq = &commdto.PushNotificationRequest{
			Code:       commdto.CodeNewOrgWriterAdded,
			EntityID:   user.ID,
			EntityType: commdto.UserEntity,
			RuleTree: &commdto.TreeNodeRequest{
				Rule: &commdto.Rule{
					Subject:   commdto.NotiUser,
					Verb:      commdto.IsIn,
					ObjectIDs: []string{user.ID},
				},
			},
		}
	default:
		eventNextStep = models.EventAcceptInviteUserNewUser
	}

	user, emailToken, appErr := updateRoleAndSendMailUser(org, user, eventNextStep)
	if appErr != nil {
		return nil, "", appErr
	}

	if notificationReq != nil {
		if err := communication.Notification.PushNotification(notificationReq); err != nil {
			log.Errorf("Push notification after approval request error: %v", err)
		}
	}

	return user, emailToken, nil
}

func checkCanUpdateRole(updater *models.User, permission []string) bool {
	urs, err := models.Repository.UserRoleOrg.FindByUserId(updater.ID)
	if err != nil {
		log.Error("Get user role org for check can update role ", err)
		return false
	}
	urs = lo.Filter(urs, func(item *models.UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0 && lo.Contains(permission, item.RoleID)
	})
	return len(urs) > 0
}

func (s *UserService) canUpdateRoleAdmin(updater *models.User) bool {
	return checkCanUpdateRole(updater, models.CanUpdateRoleAdminRoles)
}

func (s *UserService) canUpdateRoleMod(updater *models.User) bool {

	return checkCanUpdateRole(updater, models.CanUpdateRoleModeratorRoles)
}

func (s *UserService) canUpdateRoleOrgAdmin(updater *models.User) bool {
	return checkCanUpdateRole(updater, models.CanUpdateRoleOrgAdminRoles)
}

func (s *UserService) canUpdateRoleOrgMod(updater *models.User) bool {
	return checkCanUpdateRole(updater, models.CanUpdateRoleOrgModeratorRoles)
}

func (s *UserService) canUpdateRoleEditor(updater *models.User) bool {
	return checkCanUpdateRole(updater, models.CanUpdaterRoleEditorRoles)
}

func (s *UserService) canUpdateRoleWriter(updater *models.User) bool {
	return checkCanUpdateRole(updater, models.CanUpdaterRoleWriterRoles)
}

func (s *UserService) canUpdateRolePartner(updater *models.User) bool {
	return checkCanUpdateRole(updater, models.CanUpdateRolePartnerRoles)
}

func buildSettingDisplayForUserProfile(user *models.User, userProfile *dto.UserProfileResponse, org *models.Organization) (*dto.UserProfileResponse, *e.AppError) {
	// handle return role_id and org_id, org_name
	var orgIDs []string
	lo.ForEach(userProfile.Roles, func(item *models.SimpleUserRole, _ int) {
		orgIDs = append(orgIDs, item.OrgID)
	})

	if len(orgIDs) > 0 {
		// Get many org by ids
		uniqOrgIDs := lo.Uniq(orgIDs)
		orgs, err := models.Repository.Organization.FindMany(
			&models.OrganizationQuery{IDIn: uniqOrgIDs},
			&models.FindManyOptions{})
		if err != nil {
			return nil, e.NewError500(e.Organization_find_page_failed, err.Error())
		}

		// set name of org for user role
		mapOrgs := make(map[string]string, len(orgs))
		lo.ForEach(orgs, func(org *models.Organization, _ int) {
			mapOrgs[org.ID] = org.Name

		})

		lo.ForEach(userProfile.Roles, func(us *models.SimpleUserRole, _ int) {
			us.OrgName = mapOrgs[us.OrgID]
		})
	}

	isAdmin, isCreator := false, false
	for _, ur := range user.Roles {
		if models.IsCreatorRole(ur.RoleID) {
			isCreator = true
		}

		if models.IsOrgAdminRole(ur.RoleID) {
			isAdmin = true
		}
	}

	// get setting certificates
	certificateSetting, err := models.Repository.UserSetting.FindOne(
		&models.UserSettingQuery{UserID: &user.ID, OrgID: &org.ID, Type: util.NewString(string(models.UserSettingTypeCertificates))},
		&models.FindOneOptions{})
	if err != nil {
		// if user hasn't record setting make default for certificate setting
		if errors.Is(err, gorm.ErrRecordNotFound) {
			userProfile.Certificate.IsShow = false
			userProfile.Certificate.Results = []*models.Certificate{}
			userProfile.Certificate.Count = 0
		} else {
			return nil, e.NewError500(e.User_setting_find_failed, err.Error())
		}
	}

	// if user has record certificate setting
	if certificateSetting != nil {
		certificates := []*models.Certificate{}
		certificateIDs, err := models.ExtractStringSliceFromJSONB(certificateSetting.Value, models.SettingCertificateKey)
		if err != nil {
			return nil, e.NewError500(e.Error_extract_string_slice_from_jsonb_failed, err.Error())
		}

		uniqCertIDs := lo.Uniq(certificateIDs)
		if len(uniqCertIDs) > 0 {
			certificates, err = models.Repository.Certificate.FindMany(
				&models.CertificateQuery{IDIn: uniqCertIDs, UserID: &user.ID},
				&models.FindManyOptions{Preloads: []string{models.FilesField}, Sort: []string{models.CreateAtDESC}},
			)
			if err != nil {
				return nil, e.NewError500(e.CertificateFindManyFailed, err.Error())
			}
		}

		userProfile.Certificate.Results = certificates
		userProfile.Certificate.Count = len(certificates)
		if certificateSetting.Enable {
			userProfile.Certificate.IsShow = true
		} else {
			userProfile.Certificate.IsShow = false
		}
	}

	// get setting courses
	if isAdmin || isCreator {
		courseSetting, err := models.Repository.UserSetting.FindOne(
			&models.UserSettingQuery{UserID: &user.ID, OrgID: &org.ID, Type: util.NewString(string(models.UserSettingTypeCourses))},
			&models.FindOneOptions{})
		if err != nil {
			// if user hasn't record setting make default for course setting
			if errors.Is(err, gorm.ErrRecordNotFound) {
				userProfile.Course.IsShow = false
				userProfile.Course.Results = []*models.SimpleCourse{}
				userProfile.Course.Count = 0
			} else {
				return nil, e.NewError500(e.User_setting_find_failed, err.Error())
			}
		}

		// if user has record course setting
		if courseSetting != nil {
			var courses []*models.Course
			courseIDs, err := models.ExtractStringSliceFromJSONB(courseSetting.Value, models.SettingCourseKey)
			if err != nil {
				return nil, e.NewError500(e.Error_extract_string_slice_from_jsonb_failed, err.Error())
			}

			uniqCourseIDs := lo.Uniq(courseIDs)
			if len(uniqCourseIDs) > 0 {
				options := &models.FindPageOptions{
					Sort:    []string{models.CreateAtDESC},
					Page:    1,
					PerPage: 10,
				}
				listCourses, _, appErr := Course.FindLearnerPublishCourses(user, org,
					&models.PublishCourseQuery{CourseCuidIn: uniqCourseIDs, UserID: &user.ID}, options)
				if appErr != nil {
					return nil, appErr
				}
				courses = listCourses
			}

			if courseSetting.Enable {
				userProfile.Course.IsShow = true
			} else {
				userProfile.Course.IsShow = false
			}

			result := lo.Map(courses, func(course *models.Course, _ int) *models.SimpleCourse {
				return course.Sanitize()
			})
			userProfile.Course.Results = result
			userProfile.Course.Count = len(courses)
		}
	} else {
		userProfile.Course.IsShow = false
		userProfile.Course.Results = []*models.SimpleCourse{}
		userProfile.Course.Count = 0
	}

	// get setting blogs
	blogsSetting, err := models.Repository.UserSetting.FindOne(
		&models.UserSettingQuery{UserID: &user.ID, OrgID: &org.ID, Type: util.NewString(string(models.UserSettingTypeBlogs))},
		&models.FindOneOptions{})
	if err != nil {
		// if user hasn't record setting make default for blog setting
		if errors.Is(err, gorm.ErrRecordNotFound) {
			userProfile.Blog.IsShow = false
			userProfile.Blog.Results = []*models.SimpleBlog{}
			userProfile.Blog.Count = 0
		} else {
			return nil, e.NewError500(e.User_setting_find_failed, err.Error())
		}
	}

	// if user has record blog setting
	if blogsSetting != nil {
		blogs := []*models.Blog{}
		blogIDs, err := models.ExtractStringSliceFromJSONB(blogsSetting.Value, models.SettingBlogKey)
		if err != nil {
			return nil, e.NewError500(e.Error_extract_string_slice_from_jsonb_failed, err.Error())
		}

		uniqBlogIDs := lo.Uniq(blogIDs)
		if len(uniqBlogIDs) > 0 {
			listBlogs, appErr := Blog.GetPublishBlogMany(
				&models.PublishBlogQuery{BlogCuidIn: uniqBlogIDs, AuthorID: &user.ID},
				&models.FindManyOptions{Preloads: []string{models.BlogPreloadsUser}, Sort: []string{models.CreateAtDESC}},
			)
			if appErr != nil {
				return nil, appErr
			}
			blogs = listBlogs
		}

		result := lo.Map(blogs, func(blog *models.Blog, _ int) *models.SimpleBlog {
			return blog.Sanitize()
		})
		userProfile.Blog.Results = result
		userProfile.Blog.Count = len(blogs)
		if blogsSetting.Enable {
			userProfile.Blog.IsShow = true
		} else {
			userProfile.Blog.IsShow = false
		}
	}

	return userProfile, nil
}

func (s *UserService) GetUniqueUsername(username string) (string, *e.AppError) {
	for i := 0; i < 10; i++ {
		existed, err := models.Repository.User.FindByUsernameWithOpts(username, &models.FindOneOptions{})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return "", e.NewError500(e.Error_user_find_failed, err.Error())
		}
		if existed == nil {
			return username, nil
		}
		username += username + "_" + util.GenerateCode(5)
	}
	return "", e.NewError500(e.UserFindUniqueUsernameFailed, "find unique username failed")
}

func (s *UserService) CreateAccountWithRole(data *dto.AdminCreateUserWithRole) (*dto.AdminCreateUserWithRole, *e.AppError) {
	loggedUser := app.GetLoggedUser(s.ctx) // use service.NewUser(context) for the valid context
	loggedUserRoles := loggedUser.MyRoles()

	if !models.CanCreateUserWithRole(loggedUserRoles, data.OrgID, data.RoleID) {
		return nil, e.NewError400(
			e.UserDoNotAllow2CreateAccount,
			fmt.Sprintf("logged user don't have permission to create account with role: %s", data.RoleID))
	}

	org, oErr := Organization.FindByID(data.OrgID, false, nil)
	if oErr != nil {
		return nil, oErr
	}

	var handledUsers []dto.AdminCreateUserRequest
	for _, userData := range data.Users {
		email := userData.Email
		if email == "" {
			email = util.GenerateEmail(userData.DisplayName, setting.DefaultEmailDomain())
		}

		var user *models.User
		if existUser, _ := models.Repository.User.FindByEmailWithOpts(email, &models.FindOneOptions{}); existUser != nil {
			uros, rErr := UserRoleOrg.FindByUserId(existUser.ID)
			if rErr != nil {
				return nil, rErr
			}
			_, ok := lo.Find(uros, func(item *models.UserRoleOrg) bool {
				return item.OrgID == data.OrgID && item.RoleID == data.RoleID
			})
			if ok {
				user = existUser
			} else {
				userRoleOrg := models.UserRoleOrg{
					UserID: existUser.ID,
					RoleID: data.RoleID,
					OrgID:  data.OrgID,
				}
				if urErr := models.Repository.User.UpdateRoles(&userRoleOrg); urErr != nil {
					return nil, e.NewError500(e.UserAddRoleFailed, "add role failed"+urErr.Error())
				}

				if emailErr := OEAIGovCampaign(s.ctx).SendEmailInviteModExitAccount(existUser, org, data.CampaignKey); emailErr != nil {
					return nil, e.NewError500(e.UserAddRoleFailed, "[CreateAccountWithRole].SendEmailInviteModExitAccount"+emailErr.Error())
				}

				userData.ID = util.NewString(existUser.ID)
				userData.Error = util.NewString("")
				userData.ErrorCode = util.NewInt(e.SUCCESS)
				user = existUser
			}
		} else {
			username, unErr := s.GetUniqueUsername(util.GenerateUsername(userData.DisplayName))
			if unErr != nil {
				return nil, unErr
			}
			password := userData.Password
			if password == "" {
				password = util.GenerateCode(20)
			}
			newUser := models.User{
				Username:           username,
				Email:              email,
				Phone:              userData.Phone,
				Password:           password,
				Active:             true,
				DisplayName:        userData.DisplayName,
				Avatar:             "",
				RequireSetPassword: true,
				Props: models.UserSettingsOption{
					TempPassword: password,
				},
				CreatedByID: &loggedUser.ID,
			}

			if _, uErr := NewUser(s.ctx).Create(&newUser); uErr != nil {
				return nil, uErr
			}

			userRoleOrg := models.UserRoleOrg{
				UserID: newUser.ID,
				RoleID: data.RoleID,
				OrgID:  data.OrgID,
			}
			if rErr := models.Repository.User.UpdateRoles(&userRoleOrg); rErr != nil {
				return nil, e.NewError500(e.UserAddRoleFailed, "[CreateAccountWithRole].UpdateRoles add role failed"+rErr.Error())
			}

			if userData.ReferralCode == "" {
				userData.ReferralCode = newUser.Username
			}
			if uRefCode, codeErr := OEReferralCode(s.ctx).AddReferralCode(newUser.ID, userData.ReferralCode); codeErr != nil {
				return nil, codeErr
			} else {
				userData.RefCode = uRefCode
			}

			if emailErr := OEAIGovCampaign(s.ctx).SendEmailInviteModNewAccount(&newUser, org, data.CampaignKey); emailErr != nil {
				log.ErrorWithAlertf("[CreateAccountWithRole].SendEmailInviteModNewAccount"+emailErr.Error(), emailErr)
			}

			userData.TempPassword = util.NewString(password)
			userData.ID = util.NewString(newUser.ID)
			userData.Error = util.NewString("")
			userData.ErrorCode = util.NewInt(e.SUCCESS)
			user = &newUser
		}

		// Get active code, IF active_code != request_code ==> update new code
		activeCode, acErr := OEReferralCode(s.ctx).GetUserReferralCode(user.ID)
		if acErr != nil {
			return nil, acErr
		}
		if userData.ReferralCode != "" && activeCode.Code != userData.ReferralCode {
			updatedCode, ucErr := OEReferralCode(s.ctx).UpdateCustomCode(activeCode, userData.ReferralCode)
			if ucErr != nil {
				return nil, ucErr
			}
			activeCode = updatedCode
		}

		if ldErr := OEReferralLeaderBoard(s.ctx).CreateForUser(&dto.OELeaderBoardAddMod{
			CampaignKey: data.CampaignKey,
			User:        user,
			Parent:      nil,
			LocalLevel:  data.LocalLevel,
			DisplayName: userData.DisplayName,
			RefCode:     activeCode.Code,
			RefCodeID:   activeCode.ID,
		}); ldErr != nil {
			if ldErr.ErrCode == e.OEReferralLeaderBoardExisted {
				userData.Error = util.NewString("user with role already exist!")
				userData.ErrorCode = util.NewInt(e.UserWithEmailAlreadyExisted)
			} else {
				return nil, e.NewError500(e.UserAddRoleFailed, "OEReferralLeaderBoard(s.ctx).CreateForUser add role failed"+ldErr.Error())
			}
		}

		handledUsers = append(handledUsers, userData)
	}

	data.Users = handledUsers

	return data, nil
}

func (s *UserService) FindManySimpleUser(query *models.UserQuery, options *models.FindManyOptions) ([]*models.BasicUserProfile, *e.AppError) {
	if users, err := models.Repository.User.FindManySimpleUser(query, options); err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.NOTFOUND, err.Error())
		}
		return nil, e.NewError500(e.Find_many_user_failed, err.Error())
	} else {
		return users, nil
	}
}
