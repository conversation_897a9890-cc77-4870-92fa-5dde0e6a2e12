package services

import (
	"openedu-core/models"
	"openedu-core/pkg/e"
)

func (s *FormAnswerService) FindBySessionID(sessionID string, options *models.FindManyOptions) ([]*models.FormAnswer, *e.AppError) {
	query := &models.FormAnswerQuery{
		SessionID: &sessionID,
	}
	answers, err := models.Repository.FormAnswer.FindMany(query, options)
	if err != nil {
		return nil, e.NewError500(e.Form_answer_find_failed, "Find answers by session ID error: "+err.Error())
	}
	return answers, nil
}

func (s *FormAnswerService) FindPageAnswerStats(query *models.FormAnswerStatsQuery, options *models.FindPageOptions) ([]*models.FormAnswerStats, *models.Pagination, *e.AppError) {
	answerStats, pagination, err := models.Repository.FormAnswerStats.FindPage(query, options)
	if err != nil {
		return nil, nil, e.<PERSON><PERSON>rror500(e.Form_answer_find_failed, "Find page answers stats error: "+err.<PERSON>rror())
	}
	return answerStats, pagination, nil
}
