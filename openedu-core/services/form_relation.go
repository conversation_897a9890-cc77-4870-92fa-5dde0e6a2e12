package services

import (
	"context"
	"errors"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/e"
	"time"

	"gorm.io/gorm"
)

func (s *FormRelationService) Create(req *dto.FormRelationRequest) (*models.FormRelation, *e.AppError) {
	relationType := models.FormRelationTypeForm
	if req.Type != nil {
		relationType = *req.Type
	}

	if relationType == models.FormRelationTypeForm && req.FormID == nil {
		return nil, e.NewError400(e.INVALID_PARAMS, "form_id required")
	}

	var form *models.Form
	if req.FormID != nil && *req.FormID != "" {
		validForm, appErr := s.validateFormRelationRequest(req)
		if appErr != nil {
			return nil, appErr
		}
		form = validForm
	} else {
		if emptyForm, emptyErr := Form.GetEmptyForm(); emptyErr != nil {
			return nil, emptyErr
		} else {
			form = emptyForm
		}
	}

	formRelation := &models.FormRelation{
		Enabled:              req.Enabled,
		FormID:               form.ID,
		FormUID:              form.UID,
		RelatedEntityID:      req.RelatedEntityID,
		RelatedEntityUID:     req.RelatedEntityUID,
		RelatedEntityType:    req.RelatedEntityType,
		OrgID:                req.OrgID,
		OrgSchema:            req.OrgSchema,
		StartWhen:            req.StartWhen,
		EndWhen:              req.EndWhen,
		ConfirmationSettings: req.ConfirmationSettings,
		Type:                 relationType,
		Name:                 req.Name,
		HasCancelBtn:         req.HasCancelBtn,
		//SubmissionNotifySettings: req.SubmissionNotifySettings,
		AddDate: int(time.Now().UnixMilli()),
	}
	if err := models.Repository.FormRelation.Create(formRelation, nil); err != nil {
		return nil, e.NewError500(e.Form_create_relation_failed, "Create form relation error: "+err.Error())
	}

	if !form.IsPublishedAll() {
		form.Status = models.FormStatusPublishedAll
		if err := models.Repository.Form.Update(form, nil); err != nil {
			return nil, e.NewError500(e.Form_update_failed, "Update form status error: "+err.Error())
		}
	}

	return formRelation, nil
}

func (s *FormRelationService) FindById(id string) (*models.FormRelation, *e.AppError) {
	formRelation, err := models.Repository.FormRelation.FindByID(id, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, e.NewError404(e.Form_relation_not_found, err.Error())
		}
		return nil, e.NewError500(e.Form_find_one_relation_failed, err.Error())
	}
	return formRelation, nil
}

func (s *FormRelationService) FindPage(query *models.FormRelationQuery, options *models.FindPageOptions) ([]*models.FormRelation, *models.Pagination, *e.AppError) {
	formRelations, pagination, err := models.Repository.FormRelation.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Form_find_relations_failed, err.Error())
	}
	return formRelations, pagination, nil
}

func (s *FormRelationService) Update(formRelation *models.FormRelation, req *dto.FormRelationRequest) (*models.FormRelation, *e.AppError) {
	relationType := models.FormRelationTypeForm
	if req.Type != nil {
		relationType = *req.Type
	}

	if relationType == models.FormRelationTypeForm && req.FormID == nil {
		return nil, e.NewError400(e.INVALID_PARAMS, "form_id required")
	}

	var form *models.Form
	if req.FormID != nil {
		validForm, appErr := s.validateFormRelationRequest(req)
		if appErr != nil {
			return nil, appErr
		}
		form = validForm
	} else {
		if emptyForm, emptyErr := Form.GetEmptyForm(); emptyErr != nil {
			return nil, emptyErr
		} else {
			form = emptyForm
		}
	}

	formRelation.Enabled = req.Enabled
	formRelation.FormID = form.ID
	formRelation.FormUID = form.UID
	formRelation.RelatedEntityID = req.RelatedEntityID
	formRelation.RelatedEntityUID = req.RelatedEntityUID
	formRelation.RelatedEntityType = req.RelatedEntityType
	formRelation.OrgID = req.OrgID
	formRelation.OrgSchema = req.OrgSchema
	formRelation.StartWhen = req.StartWhen
	formRelation.EndWhen = req.EndWhen
	formRelation.ConfirmationSettings = req.ConfirmationSettings
	formRelation.Type = models.FormRelationTypeForm
	formRelation.HasCancelBtn = req.HasCancelBtn
	if req.Type != nil {
		formRelation.Type = *req.Type
	}
	formRelation.Name = req.Name
	//formRelation.SubmissionNotifySettings = req.SubmissionNotifySettings
	if err := models.Repository.FormRelation.Update(formRelation, nil); err != nil {
		return nil, e.NewError500(e.Form_update_relation_failed, "Update form relation error: "+err.Error())
	}

	if !form.IsPublishedAll() {
		form.Status = models.FormStatusPublishedAll
		if err := models.Repository.Form.Update(form, nil); err != nil {
			return nil, e.NewError500(e.Form_update_failed, "Update form status error: "+err.Error())
		}
	}

	return formRelation, nil
}

func (s *FormRelationService) validateFormRelationRequest(req *dto.FormRelationRequest) (*models.Form, *e.AppError) {
	form, err := models.Repository.Form.FindByID(*req.FormID)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError400(e.INVALID_PARAMS, "Form not found, ID: "+*req.FormID)
		}
		return nil, e.NewError500(e.Form_find_one_failed, "Find form by ID error: "+err.Error())
	}

	//if form.IsDraft() {
	//	return nil, e.NewError400(e.INVALID_PARAMS, "Form is still a draft, ID: "+form.ID)
	//}
	//
	//if form.IsUnPublished() {
	//	return nil, e.NewError400(e.INVALID_PARAMS, "Form is unpublished, ID: "+form.ID)
	//}

	switch req.RelatedEntityType {
	case models.CourseModelName:
		course, err := models.Repository.Course(context.TODO()).FindByID(req.RelatedEntityID, nil)
		if err != nil {
			if models.IsRecordNotFound(err) {
				return nil, e.NewError400(e.INVALID_PARAMS, "Course not found, ID: "+req.RelatedEntityID)
			}
			return nil, e.NewError500(e.Course_find_one_failed, "Find course by ID error: "+err.Error())
		}
		req.RelatedEntityUID = course.Cuid
	default:
		return nil, e.NewError400(e.INVALID_PARAMS, "Unsupported form relation entity type")
	}

	if !req.StartWhen.Type.IsValidFormTriggerEvent() {
		return nil, e.NewError400(e.INVALID_PARAMS, "Unsupported event type for StartWhen: "+req.StartWhen.Type.String())
	}

	if req.EndWhen != nil && !req.EndWhen.Type.IsValidFormTriggerEvent() {
		return nil, e.NewError400(e.INVALID_PARAMS, "Unsupported event type for StartWhen: "+req.EndWhen.Type.String())
	}

	return form, nil
}

func (s *FormRelationService) Delete(formRelation *models.FormRelation) *e.AppError {
	if err := models.Repository.FormRelation.Delete(formRelation.ID, nil); err != nil {
		return e.NewError500(e.Form_relation_delete_failed, "Delete form relation error: "+err.Error())
	}
	return nil
}
