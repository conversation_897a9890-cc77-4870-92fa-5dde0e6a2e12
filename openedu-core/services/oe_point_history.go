package services

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

func (s *OEPointHistoryService) Create(history *models.OEPointHistory) (*models.OEPointHistory, *e.AppError) {
	if err := models.Repository.OEPointHistory(s.ctx).Create(history, nil); err != nil {
		return nil, e.NewError500(e.OEPointHistoryCreateFailed, "create: "+err.Error())
	} else {
		return history, nil
	}
}

func (s *OEPointHistoryService) Update(history *models.OEPointHistory) (*models.OEPointHistory, *e.AppError) {
	if err := models.Repository.OEPointHistory(s.ctx).Update(history, nil); err != nil {
		return nil, e.NewError500(e.OEPointHistoryUpdateFailed, "create: "+err.Error())
	} else {
		return history, nil
	}
}

func (s *OEPointHistoryService) FindOne(query *models.OEPointHistoryQuery, options *models.FindOneOptions) (*models.OEPointHistory, *e.AppError) {
	if plan, err := models.Repository.OEPointHistory(s.ctx).FindOne(query, options); err != nil {
		return nil, e.NewError500(e.OEPointHistoryFindOneFailed, "FindOne: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEPointHistoryService) FindById(id string, options *models.FindOneOptions) (*models.OEPointHistory, *e.AppError) {
	if plan, err := models.Repository.OEPointHistory(s.ctx).FindOne(&models.OEPointHistoryQuery{ID: util.NewString(id)}, options); err != nil {
		return nil, e.NewError500(e.OEPointHistoryFindByIDFailed, "FindById: "+err.Error())
	} else {
		return plan, nil
	}
}

func (s *OEPointHistoryService) FindPage(query *models.OEPointHistoryQuery, options *models.FindPageOptions) ([]*models.OEPointHistory, *models.Pagination, *e.AppError) {
	if plans, pagination, err := models.Repository.OEPointHistory(s.ctx).FindPage(query, options); err != nil {
		return nil, nil, e.NewError500(e.OEPointHistoryFindPageFailed, "FindPage: "+err.Error())
	} else {
		return plans, pagination, nil
	}
}

func (s *OEPointHistoryService) FindMany(query *models.OEPointHistoryQuery, options *models.FindManyOptions) ([]*models.OEPointHistory, *e.AppError) {
	if plans, err := models.Repository.OEPointHistory(s.ctx).FindMany(query, options); err != nil {
		return nil, e.NewError500(e.OEPointHistoryFindManyFailed, "FindMany: "+err.Error())
	} else {
		return plans, nil
	}
}

func (s *OEPointHistoryService) Delete(id string) *e.AppError {
	if err := models.Repository.OEPointHistory(s.ctx).Delete(id, nil); err != nil {
		return e.NewError500(e.OEPointHistoryDeleteFailed, "Delete: "+err.Error())
	} else {
		return nil
	}
}

func getExpireDate(startDate time.Time, config models.OEPointSystemConfig) time.Time {
	endDate := startDate.AddDate(0, 0, 7)
	switch config.ExpirationPeriod {
	case models.TimePeriodDay:
		endDate = startDate.AddDate(0, 0, config.ExpirationVal)
	case models.TimePeriodWeek:
		endDate = startDate.AddDate(0, 0, config.ExpirationVal)
	case models.TimePeriodMonth:
		endDate = startDate.AddDate(0, config.ExpirationVal, 0)
	case models.TimePeriodYear:
		endDate = startDate.AddDate(config.ExpirationVal, 0, 0)
	case models.TimePeriodUnLimit:
		endDate = startDate.AddDate(100, 0, 0)
	default:
		break
	}
	return endDate
}

func (s *OEPointHistoryService) GetUserPoints(userID string) (decimal.Decimal, *e.AppError) {
	if number, err := models.Repository.OEPointHistory(s.ctx).GetUserActivePoint(userID); err != nil {
		log.Error("GetUserPoints: ", userID, err)
		return decimal.Zero, e.NewError500(e.OEPointHistoryGetUserActivePointFailed, "OEPointHistoryService.GetUserActivePoint: get active point failed with err: "+err.Error())
	} else {
		return number, nil
	}
}

func (s *OEPointHistoryService) GetUserReferralEarnedPoints(userID string) (int, *e.AppError) {
	if number, err := models.Repository.OEPointHistory(s.ctx).GetUserActiveReferralEarnedPoint(userID); err != nil {
		log.Error("GetUserPoints: ", userID, err)
		return 0, e.NewError500(e.OEPointHistoryGetUserActiveReferralEarnedPointFailed, "OEPointHistoryService.GetUserReferralEarnedPoints: get active referral earned point failed with err: "+err.Error())
	} else {
		return number, nil
	}
}

func (s *OEPointHistoryService) ClaimPoint(request *dto.UserClaimPointRequest) (*models.OEPointHistory, *e.AppError) {
	pointSetting := models.GetConfig[models.OEPointSystemConfig](models.PointSystemSetting)
	startDate := time.Now()
	expireDate := getExpireDate(startDate, pointSetting)
	if request.Props == nil {
		request.Props = &models.OEPointHistoryProps{}
	}
	history := &models.OEPointHistory{
		Source:     request.Source,
		UserID:     request.User.ID,
		Status:     models.OEPointStatusNew,
		Amount:     request.Point,
		Used:       decimal.NewFromInt(0),
		Remaining:  request.Point,
		ExpireDate: int(expireDate.UnixMilli()),
		Props:      *request.Props,
		EntityID:   request.User.ID,
		EntityType: models.UserModelName,
		CampaignID: request.CampaignID,
	}
	if h, err := s.Create(history); err != nil {
		return nil, err
	} else {
		return h, nil
	}
}

func (s *OEPointHistoryService) UsePoint(user *models.User, amount int, entityID string, entityType models.ModelName) *e.AppError {
	activePoints, allErr := s.FindMany(&models.OEPointHistoryQuery{
		UserID: util.NewString(user.ID),
		Status: util.NewT(models.OEPointStatusReceived),
	}, &models.FindManyOptions{Sort: []string{"expired_date desc"}})
	if allErr != nil {
		return allErr
	}

	// points:  10 20 30 40
	// need: 57
	// available = 10, missing = 57 // used = 10, missing = 47, remaining = 0  --> used = available, missing = missing - used, remaining = available - used
	// available = 20, missing = 47 // used = 20, missing = 27, remaining = 0  --> used = available, missing = missing - used, remaining = available - used
	// available = 30, missing = 27 // used = 27, missing = 0, remaining = 3  --> used = missing, missing = 0, remaining = available - used
	// break
	missingAmount := decimal.NewFromInt(int64(amount))
	for _, point := range activePoints {
		availableAmount := point.Remaining
		used := decimal.NewFromInt(0)
		remaining := decimal.NewFromInt(0)
		if missingAmount.GreaterThan(availableAmount) {
			used = availableAmount
			missingAmount = missingAmount.Sub(used)
			remaining = used.Sub(availableAmount)
			point.Status = models.OEPointStatusUsed
		} else {
			used = missingAmount
			missingAmount = decimal.NewFromInt(0)
			remaining = availableAmount.Sub(used)
		}

		point.Used = point.Used.Add(used)
		point.Remaining = remaining
		point.EntityID = entityID
		point.EntityType = entityType
		usedKey := string(entityType) + "-" + entityID
		// point.Props[usedKey] = used.String()
		point.Props.UsedKey = usedKey
		point.Props.Used = used.String()
		if _, err := s.Update(point); err != nil {
			return err
		}
		if missingAmount.LessThanOrEqual(decimal.NewFromInt(0)) {
			break
		}
	}
	return nil
}

func (s *OEPointHistoryService) HandleExpiredPoint() *e.AppError {
	batchSize := 50
	now := time.Now()
	for {
		histories, allErr := models.Repository.OEPointHistory(s.ctx).Update2Expired(int(now.UnixMilli()), batchSize)
		if allErr != nil {
			return e.NewError500(e.OEPointHistoryUpdateExpiredFailed, "HandleExpiredPoint.FindMany: "+allErr.Error())
		}
		if len(histories) > 0 {
			if err := s.sendExpiredNotifications(histories); err != nil {
				log.Error("OEPointHistoryService.HandleExpiredPoint: batch send errors: ", util.Struct2Json(histories), err)
			}
			if len(histories) < batchSize {
				log.Debug("OVER!!!!!")
				break
			}
		} else {
			log.Debug("OVER!!!!!")
			break
		}
	}

	return nil
}

func (s *OEPointHistoryService) sendExpiredNotifications(histories []*models.OEPointHistory) *e.AppError {
	data := lo.Map(histories, func(item *models.OEPointHistory, _ int) *communicationdto.PushNotificationRequest {
		return &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeExpiredOEPointHistory,
			EntityID:   item.ID,
			EntityType: communicationdto.PointHistory,
			Props: communicationdto.JSONB{
				"amount": item.Remaining.String(),
			},
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{item.UserID},
				},
			},
		}
	})
	if err := communication.Notification.PushMultipleNotification(data); err != nil {
		return e.NewError500(e.OEPointHistorySendNotificationFailed, "sendExpiredNotifications: "+err.Error())
	}
	return nil
}

func (s *OEPointHistoryService) PointExpirationReminder(period int) *e.AppError {
	batchSize := 50
	for {
		histories, allErr := models.Repository.OEPointHistory(s.ctx).UpdateNotifiedByPeriod(period, batchSize)
		if allErr != nil {
			return e.NewError500(e.OEPointHistoryFindPageFailed, "HandleExpiredPoint.FindMany: "+allErr.Error())
		}
		if len(histories) > 0 {
			if err := s.sendReminderExpiryNotifications(histories); err != nil {
				log.Error("OEPointHistoryService.HandleExpiredPoint: batch send errors: ", util.Struct2Json(histories), err)
			}
			if len(histories) < batchSize {
				log.Debug("OVER!!!!!")
				break
			}
		} else {
			log.Debug("OVER!!!!!")
			break
		}
	}

	return nil
}

func (s *OEPointHistoryService) sendReminderExpiryNotifications(histories []*models.OEPointHistory) *e.AppError {
	data := lo.Map(histories, func(item *models.OEPointHistory, _ int) *communicationdto.PushNotificationRequest {
		return &communicationdto.PushNotificationRequest{
			Code:       communicationdto.CodeReminderExpiryOEPointHistory,
			EntityID:   item.ID,
			EntityType: communicationdto.PointHistory,
			Props: communicationdto.JSONB{
				"amount":      item.Remaining.String(),
				"expiry_date": item.ExpireDate,
			},
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{item.UserID},
				},
			},
		}
	})
	if err := communication.Notification.PushMultipleNotification(data); err != nil {
		return e.NewError500(e.OEPointHistorySendNotificationFailed, "sendExpiredNotifications: "+err.Error())
	}
	return nil
}

func (s *OEPointHistoryService) Count(query *models.OEPointHistoryQuery) (int64, *e.AppError) {
	count, err := models.Repository.OEPointHistory(s.ctx).Count(query)
	if err != nil {
		return 0, e.NewError400(e.OEPointHistoryCountFailed, err.Error())
	}
	return count, nil
}

func (s *OEPointHistoryService) GetReferralProgramPoints(campaign *models.OEPointCampaign) (*dto.UserPointResponse, *e.AppError) {
	user := app.GetLoggedUser(s.ctx)
	refCode, refErr := OEReferralCode(s.ctx).GetUserReferralCode(user.ID)
	if refErr != nil {
		return nil, refErr
	}

	point, err := models.Repository.OEPointHistory(s.ctx).GetUserPointByStatusAndSources(user.ID, models.OEPointStatusReceived, models.GetReferralUserSources())
	if err != nil {
		return nil, e.NewError500(e.OEPointHistoryByStatusAndSourceFailed, "GetUserPointByStatusAndSources: "+err.Error())
	}

	newHistories, newErr := models.Repository.OEPointHistory(s.ctx).FindMany(&models.OEPointHistoryQuery{
		UserID:     util.NewString(user.ID),
		Status:     util.NewT(models.OEPointStatusNew),
		SourceIn:   models.GetReferralUserSources(),
		CampaignID: util.NewString(campaign.ID),
	}, nil)

	if newErr != nil {
		return nil, e.NewError500(e.OEPointHistoryFindManyFailed, "FindMany: "+newErr.Error())
	}

	newPoint := dto.NewPointHistoryResponse{}
	for _, history := range newHistories {
		source := history.Source
		switch source {
		case models.ReferralUserSource:
			newPoint.Referral.Amount = newPoint.Referral.Amount.Add(history.Amount)
			newPoint.Referral.Count = newPoint.Referral.Count + 1
			break
		case models.RefereeUserSource:
			newPoint.Referee.Amount = newPoint.Referee.Amount.Add(history.Amount)
			newPoint.Referee.Count = newPoint.Referee.Count + 1
			break
		case models.ReferralMilestoneSource:
			newPoint.Milestone.Amount = newPoint.Milestone.Amount.Add(history.Amount)
			newPoint.Milestone.Count = newPoint.Milestone.Count + 1
			break
		case models.ReferralFeatureDiscoveryCourseSource:
			newPoint.Featured.CourseCount = newPoint.Featured.CourseCount + 1
			newPoint.Featured.Amount = newPoint.Featured.Amount.Add(history.Amount)
			newPoint.Featured.Count = newPoint.Featured.Count + 1
			break
		case models.ReferralFeatureDiscoveryFiatSource:
			newPoint.Featured.FiatCount = newPoint.Featured.FiatCount + 1
			newPoint.Featured.Amount = newPoint.Featured.Amount.Add(history.Amount)
			newPoint.Featured.Count = newPoint.Featured.Count + 1
			break
		case models.ReferralFeatureDiscoveryCryptoSource:
			newPoint.Featured.CryptoCount = newPoint.Featured.CryptoCount + 1
			newPoint.Featured.Amount = newPoint.Featured.Amount.Add(history.Amount)
			newPoint.Featured.Count = newPoint.Featured.Count + 1
			break
		case models.ReferralStreakWeeklySource:
			newPoint.WeeklyStreak.Amount = newPoint.WeeklyStreak.Amount.Add(history.Amount)
			newPoint.WeeklyStreak.Count = newPoint.WeeklyStreak.Count + 1
			break
		case models.ReferralStreakMonthlySource:
			newPoint.MonthlyStreak.Amount = newPoint.MonthlyStreak.Amount.Add(history.Amount)
			newPoint.MonthlyStreak.Count = newPoint.MonthlyStreak.Count + 1
			break
		case models.ReferralTimeBaseBonusSource:
			newPoint.Timebase.Amount = newPoint.Timebase.Amount.Add(history.Amount)
			newPoint.Timebase.Count = newPoint.Timebase.Count + 1
			break
		default:
			break
		}
	}

	// lay start_date cua thang + end_date cua thang
	startOfWeek, endOfWeek := util.GetCurrentWeekRange()
	weeklyTotalCount, wtErr := models.Repository.OEPointHistory(s.ctx).Count(&models.OEPointHistoryQuery{
		UserID:      util.NewString(user.ID),
		CampaignID:  util.NewString(campaign.ID),
		Source:      util.NewT(models.ReferralUserSource),
		CreateAtGte: util.NewInt(int(startOfWeek.UnixMilli())),
		CreateAtLte: util.NewInt(int(endOfWeek.UnixMilli())),
		Status:      util.NewT(models.OEPointStatusReceived),
	})

	if wtErr != nil {
		return nil, e.NewError500(e.OEPointHistoryCountFailed, "Count weekly streak: "+wtErr.Error())
	}

	startOfMonth, endOfMonth := util.GetCurrentMonthRange()
	monthlyTotalCount, mtErr := models.Repository.OEPointHistory(s.ctx).Count(&models.OEPointHistoryQuery{
		UserID:      util.NewString(user.ID),
		CampaignID:  util.NewString(campaign.ID),
		Source:      util.NewT(models.ReferralUserSource),
		CreateAtGte: util.NewInt(int(startOfMonth.UnixMilli())),
		CreateAtLte: util.NewInt(int(endOfMonth.UnixMilli())),
		Status:      util.NewT(models.OEPointStatusReceived),
	})
	if mtErr != nil {
		return nil, e.NewError500(e.OEPointHistoryCountFailed, "Count monthly streak: "+mtErr.Error())
	}

	newPoint.WeeklyStreak.TotalCount = int(weeklyTotalCount)
	newPoint.MonthlyStreak.TotalCount = int(monthlyTotalCount)

	// milestone
	milestoneHistories, mileErr := models.Repository.OEPointHistory(s.ctx).FindMany(&models.OEPointHistoryQuery{
		UserID:     util.NewString(user.ID),
		CampaignID: util.NewString(campaign.ID),
		Source:     util.NewT(models.ReferralMilestoneSource),
	}, nil)
	if mileErr != nil {
		return nil, e.NewError500(e.OEPointHistoryCountFailed, "findAll milestone failed: "+mileErr.Error())
	}
	for _, history := range milestoneHistories {
		newPoint.Milestone.Milestones = append(newPoint.Milestone.Milestones, dto.NPointHistoryRes{
			ID:               history.ID,
			CreateAt:         history.CreateAt,
			Amount:           history.Amount,
			UserID:           history.UserID,
			CampaignID:       history.CampaignID,
			Source:           history.Source,
			ClaimDate:        history.ClaimDate,
			MilestoneReached: history.Props.MilestoneReached,
		})
	}

	totalReward, trErr := models.Repository.OEReferral(s.ctx).Count(&models.OEReferralQuery{
		UserID:     util.NewString(user.ID),
		CampaignID: util.NewString(campaign.ID),
		Trigger:    util.NewT(models.CompleteRegisterTrigger),
	})

	if trErr != nil {
		return nil, e.NewError500(e.OEPointHistoryCountFailed, "findAll total reward failed: "+trErr.Error())
	}

	wallets, wErr := Wallet.GetWalletsByUserID(user.ID)
	if wErr != nil {
		return nil, wErr
	}
	myWallet := lo.Filter(wallets, func(wallet *models.Wallet, _ int) bool {
		return wallet.Type == models.AssetTypePoint
	})

	return &dto.UserPointResponse{
		Point:       point,
		RefCode:     refCode,
		NewPoints:   newPoint,
		PointWallet: myWallet,
		TotalReward: totalReward,
	}, nil
}

func (s *OEPointHistoryService) ConfirmClaimNewPointsBySources(sources []models.PointSource) *e.AppError {
	user := app.GetLoggedUser(s.ctx)
	query := models.OEPointHistoryQuery{
		UserID: util.NewString(user.ID),
		Status: util.NewT(models.OEPointStatusNew),
	}

	if len(sources) > 0 {
		query.SourceIn = sources
	}

	histories, err := s.FindMany(&query, nil)

	if err != nil {
		return err
	}

	if len(histories) == 0 {
		return nil
	}

	for _, history := range histories {
		history.Status = models.OEPointStatusReceived
		history.ClaimDate = int(time.Now().UnixMilli())
		_, uErr := s.Update(history)
		if uErr != nil {
			return uErr
		}
	}

	return nil
}
