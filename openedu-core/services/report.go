package services

import (
	"context"
	"fmt"
	"maps"
	"openedu-core/models"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/excel"
	"openedu-core/pkg/log"
	"slices"
	"strings"
	"sync"

	"openedu-core/pkg/util"
	"reflect"
	"time"

	"github.com/samber/lo"
)

const (
	pageSize = 100
)

func (s *ReportService) HandleFindTrackingByChunk(query *models.TrackingQuery, chunksize int) ([]*communicationdto.TrackingResponse, *e.AppError) {
	currentPage := 1
	trackingResp := []*communicationdto.TrackingResponse{}
	for {
		sort := []string{"create_at%20desc"}
		pageOptions := &models.FindPageOptions{
			Page:    currentPage,
			PerPage: chunksize,
			Sort:    sort,
		}

		trackings, pagination, err := communication.Tracking.FindPaginationTracking(query.IntoComm(), pageOptions.IntoComm())
		if err != nil {
			return nil, e.NewError500(e.External_call_error, err.Error())
		}

		trackingResp = append(trackingResp, trackings...)

		if currentPage >= pagination.TotalPages {
			return trackingResp, nil
		}

		currentPage++
	}
}

func (s *ReportService) handleFindMessageByChunk(query *models.MessageQuery, chunksize int) ([]*communicationdto.Message, *e.AppError) {
	currentPage := 1
	messageResp := []*communicationdto.Message{}
	for {
		sort := []string{"create_at%20desc"}
		pageOptions := &models.FindPageOptions{
			Page:    currentPage,
			PerPage: chunksize,
			Sort:    sort,
		}

		messages, pagination, err := communication.Tracking.FindPaginationAIMessage(query.IntoComm(), pageOptions.IntoComm())
		if err != nil {
			return nil, e.NewError500(e.External_call_error, err.Error())
		}

		messageResp = append(messageResp, messages...)

		if currentPage >= pagination.TotalPages {
			return messageResp, nil
		}

		currentPage++
	}
}

func (s *ReportService) ReportAIUserUsage(req *models.ReportAIUserUsageRequest) ([]byte, *e.AppError) {
	if len(req.Data) == 0 {
		return nil, e.NewError400(e.Report_data_invalid, "data empty")
	}

	var users []*models.User
	var err *e.AppError
	if strings.Contains(req.Data[0], "@") {
		queryUser := &models.UserQuery{
			EmailIn: req.Data,
		}

		users, err = User.FindMany(queryUser, &models.FindManyOptions{})
		if err != nil {
			return nil, err
		}
	} else {
		queryUser := &models.UserQuery{
			IDIn: &req.Data,
		}

		users, err = User.FindMany(queryUser, &models.FindManyOptions{})
		if err != nil {
			return nil, err
		}
	}

	simpleUser := lo.Map(users, func(u *models.User, _ int) *models.SimpleProfile { return u.ToSimpleProfile() })

	userIds := lo.Map(users, func(item *models.User, _ int) string {
		return item.ID
	})

	messageQuery := &models.MessageQuery{
		UserIDIn:    userIds,
		CreateAtLte: req.EndAt,
		CreateAtGte: req.StartAt,
	}

	messages, err := s.handleFindMessageByChunk(messageQuery, pageSize)
	if err != nil {
		return nil, err
	}

	log.Debugf("Data of message slice: %#v", messages)

	joinInput := []util.SliceJoinInput{}

	if len(users) > 0 {
		joinInput = append(joinInput, util.SliceJoinInput{
			Slice: simpleUser,
			Key:   models.IDJoinKey,
		})
	}

	if len(messages) > 0 {
		mapUserCountUsage := make(map[string]int)
		for _, m := range messages {
			if _, ok := mapUserCountUsage[m.UserID]; ok {
				mapUserCountUsage[m.UserID]++
			} else {
				mapUserCountUsage[m.UserID] = 0
			}
		}

		type UserCountUsage struct {
			UserID string `json:"user_id,omitempty"`
			Usage  int    `json:"usage,omitempty"`
		}

		sliceUsage := []UserCountUsage{}
		for senderID, usage := range mapUserCountUsage {
			sliceUsage = append(sliceUsage, UserCountUsage{UserID: senderID, Usage: usage})
		}

		joinInput = append(joinInput, util.SliceJoinInput{
			Slice: sliceUsage,
			Key:   models.UserIDJoinKey,
		})
	} else {
		return nil, e.NewError400(e.INVALID_PARAMS, "No one use AI in this date range")
	}

	log.Debugf("value of slice input: %#v", joinInput)

	valueUser, joinErr := util.JoinSlices(joinInput, buildTagOverrides(req.ExceptKey))
	if joinErr != nil {
		return nil, e.NewError400(e.Join_struct_failed, joinErr.Error())
	}

	bytes, excelErr := handleExcelReportAIUserUsage(valueUser)
	if excelErr != nil {
		return nil, e.NewError500(e.Report_user_enrollment_course_failed, excelErr.Error())
	}

	return bytes, nil
}

func handleExcelReportAIUserUsage(data reflect.Value) ([]byte, error) {
	e := excel.New()
	sheetName := "AI Usage"
	if err := e.NewExcelWithSheet([]string{sheetName}); err != nil {
		return nil, err
	}

	if err := e.WriteData(sheetName, data.Interface()); err != nil {
		return nil, err
	}

	return e.ToByte()
}

func (s *ReportService) ReportUserEnrollmentCourse(data *models.ReportCourseEnrollmentRequest) ([]byte, *e.AppError) {
	users, sectionEnrollments, lessonEnrollments, courseID, err := models.Repository.Report.ReportUserEnrollmentCourse(data, nil)
	if err != nil {
		return nil, e.NewError500(e.Report_user_enrollment_course_failed, err.Error())
	}

	fmt.Printf("Total lesson: %d", len(lessonEnrollments))
	for lessonID := range lessonEnrollments {
		fmt.Printf("lesson title id: %s\n", lessonID)
	}

	mapUserIDUser := make(map[string]*models.UserReport)
	lo.ForEach(users, func(u *models.UserReport, _ int) {
		mapUserIDUser[u.UserID] = u
	})

	userIDs := lo.Map(users, func(u *models.UserReport, _ int) string { return u.UserID })

	for _, chunkUserID := range lo.Chunk(userIDs, pageSize) {
		trackingQuery := &models.TrackingQuery{
			ActorIDIn: chunkUserID,
			Verb:      util.NewT(models.Enrolled),
			Object:    util.NewT(models.CourseModelName),
			ObjectID:  data.CourseCuid,
			Context:   util.NewT(models.SourceContext),
		}

		sort := []string{"create_at%20desc"}
		pageOptions := &models.FindPageOptions{
			Page:    1,
			PerPage: pageSize,
			Sort:    sort,
		}

		trackings, _, err := communication.Tracking.FindPaginationTracking(trackingQuery.IntoComm(), pageOptions.IntoComm())
		if err != nil {
			return nil, e.NewError500(e.External_call_error, err.Error())
		}

		lo.ForEach(trackings, func(t *communicationdto.TrackingResponse, _ int) {
			if user, ok := mapUserIDUser[t.ActorID]; ok {
				user.SourceName = t.ContextValue.(string)
			}
		})

		trackingQuery = &models.TrackingQuery{
			ActorIDIn:    chunkUserID,
			Verb:         util.NewT(models.Referred),
			ContextValue: data.CourseCuid,
			Context:      util.NewT(models.CourseContext),
		}

		sort = []string{"create_at%20desc"}
		pageOptions = &models.FindPageOptions{
			Page:    1,
			PerPage: pageSize,
			Sort:    sort,
		}

		trackings, _, err = communication.Tracking.FindPaginationTracking(trackingQuery.IntoComm(), pageOptions.IntoComm())
		if err != nil {
			return nil, e.NewError500(e.External_call_error, err.Error())
		}

		lo.ForEach(trackings, func(t *communicationdto.TrackingResponse, _ int) {
			if user, ok := mapUserIDUser[t.ActorID]; ok {
				ref, err := models.Repository.User.FindByID(t.ObjectID)
				if err != nil {
					return
				} else {
					user.RefBy = ref.Email
				}
			}
		})

	}

	lo.ForEach(users, func(u *models.UserReport, _ int) {
		if u.SourceName == "" {
			u.SourceName = "direct"
		}
	})

	joinInput := []util.SliceJoinInput{
		{
			Slice: users,
			Key:   models.UserIDJoinKey,
		},
	}

	for _, formReq := range data.Form {
		mapKeyQuestionUID := lo.GroupBy(formReq.Questions, func(item models.QuestionColModel) models.ColReport {
			return item.Key
		})

		for key, question := range mapKeyQuestionUID {
			questionUIDs := lo.Map(question, func(item models.QuestionColModel, _ int) string {
				return item.QuestionUID
			})

			formQuery := &models.FormAnswerQuery{
				FormUID:       &formReq.FormUID,
				QuestionUIDIn: questionUIDs,
			}
			forms, err := models.Repository.FormAnswer.FindSimpleQuestionAnwser(formQuery)
			if err != nil {
				return nil, e.NewError500(e.Form_answer_find_failed, err.Error())
			}

			joinInput = append(joinInput, util.SliceJoinInput{
				Slice: forms,
				Key:   models.UserIDJoinKey,
				RenameFields: map[string]string{
					models.TextKey: strings.ReplaceAll(string(key), " ", ""),
				},
			})

		}
	}

	valueUserRegister, err := util.JoinSlices(joinInput, buildTagOverrides(data.ExceptKey))
	if err != nil {
		return nil, e.NewError400(e.Join_struct_failed, err.Error())
	}

	for _, u := range lo.Flatten(slices.Collect(maps.Values(sectionEnrollments))) {
		if user, ok := mapUserIDUser[u.UserID]; ok {
			u.RefBy = user.RefBy
		}
	}

	for _, u := range lo.Flatten(slices.Collect(maps.Values(lessonEnrollments))) {
		if user, ok := mapUserIDUser[u.UserID]; ok {
			u.RefBy = user.RefBy
		}
	}

	// Handle generate xlsx
	excelBytes, err := handleReportUserEnrollmentCourseToExcel(valueUserRegister, sectionEnrollments, lessonEnrollments, courseID)
	if err != nil {
		return nil, e.NewError500(e.Report_user_enrollment_course_failed, err.Error())
	}

	return excelBytes, nil

}

func buildTagOverrides(exceptKeys []models.ColReport) map[string]map[string]string {
	tagOverrides := make(map[string]map[string]string, len(exceptKeys))
	for _, key := range exceptKeys {
		tagOverrides[string(key)] = map[string]string{
			"excel": "-",
		}
	}
	return tagOverrides
}

func handleReportUserEnrollmentCourseToExcel(userRegister reflect.Value, sectionEnrollments map[string][]*models.UserReport, lessonEnrollments map[string][]*models.UserReport, courseID *string) ([]byte, error) {
	e := excel.New()
	if err := e.NewExcelWithSheet([]string{"User Register"}); err != nil {
		return nil, err
	}

	sectionsUIDs := lo.Keys(sectionEnrollments)

	sections, findSectionErr := models.Repository.Section.FindMany(&models.SectionQuery{
		UIDIn:    sectionsUIDs,
		CourseID: courseID,
	}, &models.FindManyOptions{Sort: []string{`"order" asc`}})
	if findSectionErr != nil {
		return nil, findSectionErr
	}

	fmt.Printf("Total final sheetname for section: %d\n", len(sections))
	for _, section := range sections {
		sheetName := models.SanitizeSheetName(section.Title)
		if err := e.AddNewSheet(sheetName); err != nil {
			return nil, err
		}

		if len(sectionEnrollments[section.UID]) > 0 {
			if err := e.WriteData(sheetName, sectionEnrollments[section.UID]); err != nil {
				return nil, err
			}

		}
	}

	// Write lesson
	lessonUIDs := lo.Keys(lessonEnrollments)
	lessons, findLessonErr := models.Repository.Section.FindMany(&models.SectionQuery{
		UIDIn:    lessonUIDs,
		CourseID: courseID,
	}, &models.FindManyOptions{Sort: []string{`"order" asc`}})
	if findLessonErr != nil {
		return nil, findLessonErr
	}
	fmt.Printf("Total final sheetname for lesson: %d\n", len(lessons))

	for n, lesson := range lessons {
		log.Debugf("lesson Title: %s, id: %s", lesson.Title, lesson.ID)
		sheetName := models.SanitizeSheetName(lesson.Title)
		sheetName = fmt.Sprintf("%s-%d...", sheetName, n+1)
		log.Debugf("lesson Title after: %s, id: %s", lesson.Title, lesson.ID)
		if err := e.AddNewSheet(sheetName); err != nil {
			return nil, err
		}

		if len(lessonEnrollments[lesson.UID]) > 0 {
			if err := e.WriteData(sheetName, lessonEnrollments[lesson.UID]); err != nil {
				return nil, err
			}

		}
	}

	if userRegister.Len() > 0 {
		if err := e.WriteData("User Register", userRegister.Interface()); err != nil {
			return nil, err
		}
	}

	return e.ToByte()
}

func (s *ReportService) ReportOrderedAndEnrolled(query *models.ReportCourseEnrollmentRequest) ([]byte, *e.AppError) {

	// Enrolled
	enrolledQuery := &models.TrackingQuery{
		Verb:     util.NewT(models.Enrolled),
		Object:   util.NewT(models.CourseModelName),
		ObjectID: query.CourseCuid,
		Context:  util.NewT(models.SourceContext),
	}

	enrolleds, err := s.HandleFindTrackingByChunk(enrolledQuery, pageSize)
	if err != nil {
		return nil, err
	}

	userEnrolledIds := lo.Map(enrolleds, func(i *communicationdto.TrackingResponse, _ int) string {
		return i.ActorID
	}) // UserIDs

	mapEnrolledIDTracking := make(map[string]*communicationdto.TrackingResponse)
	for _, enrolled := range enrolleds {
		mapEnrolledIDTracking[enrolled.ActorID] = enrolled
	}

	//Find user info
	userEnrolledQuery := &models.UserQuery{
		IDIn: &userEnrolledIds,
	}

	userEnrolled, findueErr := User.FindMany(userEnrolledQuery, &models.FindManyOptions{})
	if findueErr != nil {
		return nil, findueErr
	}

	//Find affilaite
	orderEnrolledQuery := &models.OrderQuery{
		ReferralCodeNotNil: util.NewBool(true),
		Status:             util.NewT(models.OrderStatusSuccess),
	}
	if len(userEnrolled) > 0 {
		orderEnrolledQuery.UserIDIn = userEnrolledIds
	}

	enrolledOrders, findOrderErr := Order.FindMany(orderEnrolledQuery, &models.FindManyOptions{})
	if findOrderErr != nil {
		return nil, findOrderErr
	}
	mapUserIDEnrolledOrder := map[string]*models.Order{}
	for _, order := range enrolledOrders {
		mapUserIDEnrolledOrder[order.UserID] = order
	}

	trackingEnrolledList := []*models.ReportTrackingEnrolledAndOrdered{}
	for _, user := range userEnrolled {
		tracking := mapEnrolledIDTracking[user.ID]
		location := time.FixedZone("ICT", 7*60*60)
		_, ok := mapUserIDEnrolledOrder[user.ID]

		trackingEnrolledList = append(trackingEnrolledList, &models.ReportTrackingEnrolledAndOrdered{
			Email:       user.Email,
			FullName:    user.DisplayName,
			FromName:    tracking.ContextValue.(string),
			UserID:      user.ID,
			CreateAt:    time.UnixMilli(tracking.CreateAt).In(location).Format("15:04:05"),
			Date:        time.UnixMilli(tracking.CreateAt).In(location).Format("02-01-2006"),
			IsAffiliate: ok,
		})

	}

	// Ordered

	orderedQuery := &models.TrackingQuery{
		Verb:     util.NewT(models.Ordered),
		Object:   util.NewT(models.CourseModelName),
		ObjectID: query.CourseCuid,
		Context:  util.NewT(models.SourceContext),
	}

	ordereds, err := s.HandleFindTrackingByChunk(orderedQuery, pageSize)

	mapOrderedIDTracking := make(map[string]*communicationdto.TrackingResponse)
	for _, ordered := range ordereds {
		mapOrderedIDTracking[ordered.ActorID] = ordered
	}

	userOrderedIds := lo.Map(ordereds, func(i *communicationdto.TrackingResponse, _ int) string {
		return i.ActorID
	})

	//Find user info
	userOrderedQuery := &models.UserQuery{
		IDIn: &userOrderedIds,
	}

	userOrdered, finduoErr := User.FindMany(userOrderedQuery, &models.FindManyOptions{})
	if finduoErr != nil {
		return nil, finduoErr
	}

	//Find affilaite
	orderOrderedQuery := &models.OrderQuery{
		ReferralCodeNotNil: util.NewBool(true),
	}
	if len(userEnrolled) > 0 {
		orderEnrolledQuery.UserIDIn = userEnrolledIds
	}

	orderOrders, findOrderErr := Order.FindMany(orderOrderedQuery, &models.FindManyOptions{})
	if findOrderErr != nil {
		return nil, findOrderErr
	}

	mapUserIDOrderOrder := map[string]*models.Order{}
	for _, order := range orderOrders {
		mapUserIDOrderOrder[order.UserID] = order
	}

	trackingOrderedList := []*models.ReportTrackingEnrolledAndOrdered{}
	for _, user := range userOrdered {
		tracking := mapOrderedIDTracking[user.ID]
		location := time.FixedZone("ICT", 7*60*60)
		_, ok := mapUserIDOrderOrder[user.ID]

		trackingOrderedList = append(trackingOrderedList, &models.ReportTrackingEnrolledAndOrdered{
			Email:       user.Email,
			FullName:    user.DisplayName,
			FromName:    tracking.ContextValue.(string),
			UserID:      user.ID,
			CreateAt:    time.UnixMilli(tracking.CreateAt).In(location).Format("15:04:05"),
			Date:        time.UnixMilli(tracking.CreateAt).In(location).Format("02-01-2006"),
			IsAffiliate: ok,
		})
	}

	// Handle generate xlsx
	excelBytes, excelErr := handleReportuserEnrolledAndOrderedToExcel(trackingEnrolledList, trackingOrderedList)
	if err != nil {
		return nil, e.NewError500(e.Report_enrolled_ordered_course_failed, excelErr.Error())
	}

	return excelBytes, nil
}

func handleReportuserEnrolledAndOrderedToExcel(enrolleds, ordered []*models.ReportTrackingEnrolledAndOrdered) ([]byte, error) {
	e := excel.New()

	if err := e.NewExcelWithSheet([]string{"Enrolled Course", "Ordered Course"}); err != nil {
		return nil, err
	}

	if len(enrolleds) > 0 {
		if err := e.WriteData("Enrolled Course", enrolleds); err != nil {
			return nil, err
		}
	}

	if len(ordered) > 0 {
		if err := e.WriteData("Ordered Course", ordered); err != nil {
			return nil, err
		}
	}

	return e.ToByte()

}

func (s *ReportService) CanGetReport(data *models.ReportCourseEnrollmentRequest, currentUser *models.User, org *models.Organization) *e.AppError {
	switch data.ReportType {
	case models.ReportCourseEnrollment:
		if !currentUser.IsSysAdmin() && !currentUser.IsOrgAdmin(org.ID) {
			if !currentUser.IsCreator(org.ID) {
				return e.NewError400(e.FORBIDDEN, "You are not allowed to update course enrollment")
			}

			courseParner, cpErr := models.Repository.CoursePartner.FindMany(&models.CoursePartnerQuery{CourseID: data.CourseCuid, IsActive: util.NewBool(true)}, &models.FindManyOptions{})
			if cpErr != nil {
				return e.NewError500(e.Find_course_partner_failed, cpErr.Error())
			}

			if !lo.ContainsBy(courseParner, func(item *models.CoursePartner) bool { return item.PartnerID == currentUser.ID }) {
				return e.NewError400(e.FORBIDDEN, "You are not allowed to get report course enrollment")
			}
		}
		return nil

	}
	return nil
}

func (s *ReportService) ReportReferralLinkByUser(data *models.ReportReferralLinkByUserRequest) ([]byte, *e.AppError) {
	if len(data.Emails) == 0 {
		return s.ReportReferralLinkAllUser(data)
	}

	queryUser := &models.UserQuery{
		EmailIn: data.Emails,
	}

	users, err := User.FindMany(queryUser, &models.FindManyOptions{})
	if err != nil {
		return nil, err
	}

	mapUserIDUserEmail := map[string]string{}
	for _, user := range users {
		mapUserIDUserEmail[user.ID] = user.Email
	}

	query := &models.TrackingQuery{
		Verb: util.NewT(models.Referred),
		ObjectIDIn: lo.Map(users, func(u *models.User, _ int) string {
			return u.ID
		}),
		ContextValue:      &data.CourseCuid,
		Context:           util.NewT(models.CourseContext),
		TrackingDateStart: data.StartAt,
		TrackingDateEnd:   data.EndAt,
	}

	trackings, err := s.HandleFindTrackingByChunk(query, pageSize)
	if err != nil {
		return nil, err
	}
	log.Debugf("Total trackings in the mongo: %d", len(trackings))

	location := time.FixedZone("ICT", 7*60*60)
	for _, tracking := range trackings {
		tracking.CreateDate = time.UnixMilli(tracking.CreateAt).In(location).Format("02-01-2006 15:04:05")
	}

	mapRefUserByTrackings := lo.GroupBy(trackings, func(t *communicationdto.TrackingResponse) string {
		return t.ObjectID
	})

	for objectID, data := range mapRefUserByTrackings {
		log.Debugf("userID %s have total ref: %d", objectID, len(data))
	}
	mUserEmailRefUser := map[string]reflect.Value{}
	for userID, trackings := range mapRefUserByTrackings {
		refUserIds := lo.Map(trackings, func(item *communicationdto.TrackingResponse, _ int) string {
			return item.ActorID
		})
		referredUserQuery := &models.UserQuery{
			IDIn: &refUserIds,
		}
		referredUser, err := User.FindMany(referredUserQuery, &models.FindManyOptions{})
		if err != nil {
			return nil, err
		}

		log.Debugf("userID in loop %s have num actorID: %d have total ref: %d", userID, len(refUserIds), len(referredUser))

		simpleRefUser := lo.Map(referredUser, func(u *models.User, _ int) *models.SimpleProfile { return u.ToSimpleProfile() })

		//Wallet addition data
		walletQuery := &models.WalletQuery{
			UserIDIn: refUserIds,
			Network:  &data.NetworkWalletAddr,
			Type:     util.NewT(models.AssetTypeCrypto),
			Currency: &data.Currency,
		}

		walletUser, _ := models.Repository.Wallet.FindMany(walletQuery, &models.FindManyOptions{})

		joinInput := []util.SliceJoinInput{}
		if len(simpleRefUser) == 0 {
			continue
		} else {
			joinInput = append(joinInput, util.SliceJoinInput{
				Slice: simpleRefUser,
				Key:   models.IDJoinKey,
			})
		}

		if len(trackings) > 0 {
			joinInput = append(joinInput, util.SliceJoinInput{
				Slice: trackings,
				Key:   models.ActorIDJoinKey,
			})
		}

		if len(walletUser) > 0 {
			joinInput = append(joinInput,
				util.SliceJoinInput{
					Slice: walletUser,
					Key:   models.UserIDJoinKey,
				},
			)
		}

		for t, i := range joinInput {
			log.Debugf("Data of join input %d: %#v\n", t, i)
		}

		valueRefUser, joinErr := util.JoinSlices(joinInput, buildTagOverrides(data.ExceptKey))
		if joinErr != nil {
			return nil, e.NewError400(e.Join_struct_failed, joinErr.Error())
		}

		userEmail, ok := mapUserIDUserEmail[userID]
		if !ok {
			continue
		}

		log.Debugf("UserEmail: %s have total: %d ref: %#v", userEmail, len(simpleRefUser), simpleRefUser)
		mUserEmailRefUser[userEmail] = valueRefUser
	}

	bytes, excelErr := handleExcelReportReferralLink(mUserEmailRefUser)
	if excelErr != nil {
		return nil, e.NewError500(e.Report_user_enrollment_course_failed, excelErr.Error())
	}

	return bytes, nil

}
func (s *ReportService) ReportReferralLinkAllUser(data *models.ReportReferralLinkByUserRequest) ([]byte, *e.AppError) {
	query := &models.TrackingQuery{
		Verb:              util.NewT(models.Referred),
		ContextValue:      &data.CourseCuid,
		Context:           util.NewT(models.CourseContext),
		TrackingDateStart: data.StartAt,
		TrackingDateEnd:   data.EndAt,
	}

	trackings, err := s.HandleFindTrackingByChunk(query, pageSize)
	if err != nil {
		return nil, err
	}
	log.Debugf("Total trackings in the mongo: %d", len(trackings))

	location := time.FixedZone("ICT", 7*60*60)
	for _, tracking := range trackings {
		tracking.CreateDate = time.UnixMilli(tracking.CreateAt).In(location).Format("02-01-2006 15:04:05")
	}

	mapRefUserByTrackings := lo.GroupBy(trackings, func(t *communicationdto.TrackingResponse) string {
		return t.ObjectID
	})

	for objectID, data := range mapRefUserByTrackings {
		log.Debugf("userID %s have total ref: %d", objectID, len(data))
	}
	mUserEmailRefUser := map[string]reflect.Value{}
	//mUserEmailRefUser := map[string][]*models.SimpleProfile{}
	for userID, trackings := range mapRefUserByTrackings {
		refUserIds := lo.Map(trackings, func(item *communicationdto.TrackingResponse, _ int) string {
			return item.ActorID
		})
		referredUserQuery := &models.UserQuery{
			IDIn: &refUserIds,
		}
		referredUser, err := User.FindMany(referredUserQuery, &models.FindManyOptions{})
		if err != nil {
			return nil, err
		}

		log.Debugf("userID in loop %s have num actorID: %d have total ref: %d", userID, len(refUserIds), len(referredUser))

		simpleRefUser := lo.Map(referredUser, func(u *models.User, _ int) *models.SimpleProfile { return u.ToSimpleProfile() })

		//Wallet addition data
		walletQuery := &models.WalletQuery{
			UserIDIn: refUserIds,
			Network:  &data.NetworkWalletAddr,
			Type:     util.NewT(models.AssetTypeCrypto),
			Currency: util.NewT(models.CryptoCurrencyAVAIL),
		}

		walletUser, _ := models.Repository.Wallet.FindMany(walletQuery, &models.FindManyOptions{})

		joinInput := []util.SliceJoinInput{}
		if len(simpleRefUser) == 0 {
			continue
		} else {
			joinInput = append(joinInput, util.SliceJoinInput{
				Slice: simpleRefUser,
				Key:   models.IDJoinKey,
			})
		}

		if len(trackings) > 0 {
			joinInput = append(joinInput, util.SliceJoinInput{
				Slice: trackings,
				Key:   models.ActorIDJoinKey,
			})
		}

		if len(walletUser) > 0 {
			joinInput = append(joinInput,
				util.SliceJoinInput{
					Slice: walletUser,
					Key:   models.UserIDJoinKey,
				},
			)
		}

		for t, i := range joinInput {
			log.Debugf("Data of join input %d: %#v\n", t, i)
		}

		valueRefUser, joinErr := util.JoinSlices(joinInput, buildTagOverrides(data.ExceptKey))
		if joinErr != nil {
			return nil, e.NewError400(e.Join_struct_failed, joinErr.Error())
		}

		//mUserEmailRefUser[userEmail] = simpleRefUser

		emUser, err := User.FindByID(userID, &models.FindOneOptions{})
		if err != nil {
			log.Debugf("Failed to find userID: %s", userID)
			continue
		}

		log.Debugf("UserEmail: %s have total: %d ref: %#v", emUser.Email, len(simpleRefUser), simpleRefUser)
		mUserEmailRefUser[emUser.Email] = valueRefUser
	}

	bytes, excelErr := handleExcelReportReferralLink(mUserEmailRefUser)
	if excelErr != nil {
		return nil, e.NewError500(e.Report_user_enrollment_course_failed, excelErr.Error())
	}

	return bytes, nil

}

func handleExcelReportReferralLink(mUserEmailRefUser map[string]reflect.Value) ([]byte, error) {
	e := excel.New()

	for email, data := range mUserEmailRefUser {
		if data.Len() > 0 {
			sheetName := models.SanitizeSheetName(email)
			if err := e.AddNewSheet(sheetName); err != nil {
				return nil, err
			}

			if err := e.WriteData(sheetName, data.Interface()); err != nil {
				return nil, err
			}

		}
	}

	return e.ToByte()
}

func (s *ReportService) FindStructuresReportType() map[string]map[string]interface{} {
	courseEnrollmentModels := []interface{}{
		models.UserReport{},
		models.CourseEnrollment{},
		models.Form{},
		models.Wallet{},
	}

	referralLinkModels := []interface{}{
		models.User{},
		communicationdto.TrackingResponse{},
		models.Wallet{},
	}
	courseEnrollmentExceptKeys, _ := models.GetAvailableExceptKeys(courseEnrollmentModels...)
	referralLinkExceptKeys, _ := models.GetAvailableExceptKeys(referralLinkModels...)

	ceExceptDisplay := models.FormatExceptKeysForDisplay(courseEnrollmentExceptKeys)
	rlExceptDisplay := models.FormatExceptKeysForDisplay(referralLinkExceptKeys)

	reportOptions := map[string]map[string]interface{}{
		"rp_course_enrollment": {
			"display_name": "Course Enrollment Report",
			"fields": []map[string]interface{}{
				{
					"name":         "course_cuid",
					"input_type":   "string",
					"display_name": "Course",
					"select_type":  "select",
					"required":     true,
					"data": map[string]string{
						"method":  "GET",
						"enpoint": "/api/v1/courses",
					},
				},
				{
					"name":         "org_id",
					"input_type":   "string",
					"display_name": "Organization",
					"select_type":  "select",
					"required":     true,
					"data": map[string]string{
						"method":  "GET",
						"enpoint": "/api/v1/organizes",
					},
				},
				{
					"name": "form",
					"input_type": []map[string]interface{}{
						{
							"form_uid": "string",
							"question_col_model": []map[string]string{
								{
									"question_uid": "string",
									"key":          "string",
								},
							},
						},
					},
					"display_name": "Include Form Data",
					"select_type":  "list_form_selector",
					"required":     false,
					"data": map[string]string{
						"method":   "GET",
						"endpoint": "/api/v1/course/:id/forms",
					},
				},
				{
					"name":         "network",
					"display_name": "Blockchain Network",
					"select_type":  "select",
					"required":     false,
					"options":      models.AllBlockchainNetworks,
				},
				{
					"name":         "report_type",
					"display_name": "Report Type",
					"select_type":  "select",
					"required":     true,
					"data":         []models.ReportType{models.ReportCourseEnrollment, models.ReportCourseOrderedEnrolled},
				},
			},
			"except_keys": ceExceptDisplay,
		},
		"rp_course_referral": {
			"display_name": "Course Referral Report",
			"fields": []map[string]interface{}{
				{
					"name":         "course_cuid",
					"display_name": "Course",
					"select_type":  "select",
					"required":     true,
					"endpoint":     "/api/courses",
				},
				{
					"name":         "emails",
					"display_name": "User Emails",
					"select_type":  "email_list",
					"required":     true,
				},
				{
					"name":         "network",
					"display_name": "Blockchain Network",
					"select_type":  "select",
					"required":     true,
					"options":      models.AllBlockchainNetworks,
				},
				{
					"name":         "report_type",
					"display_name": "Report Type",
					"select_type":  "select",
					"required":     true,
					"values":       []models.ReportType{models.ReportCourseReferral},
				},
			},
			"except_keys": rlExceptDisplay,
		},
	}
	return reportOptions
}

func (s *ReportService) ReportCourseEngagement() ([]byte, *e.AppError) {
	//lps, err := LearningProgress.FindMany(&models.LearningProgressQuery{EventIn: []models.ProgressEvent{
	//	models.CourseProgress,
	//	models.SectionProgress,
	//	models.LessonProgress,
	//	models.LessonContentProgress,
	//}}, nil)
	lss, err := NewLearningStatus(context.TODO()).FindMany(&models.LearningStatusQuery{}, nil)
	if err != nil {
		return nil, err
	}

	location := time.FixedZone("ICT", 7*60*60)
	var dataToExport []*models.LearningProgressReport
	for _, ls := range lss {
		for _, section := range ls.Sections {
			dataToExport = append(dataToExport, &models.LearningProgressReport{
				UserID:     ls.UserID,
				CourseCuid: ls.CourseCuid,
				CreateAt:   lo.If(section.StartAt == 0, "0").Else(time.UnixMilli(section.StartAt).In(location).Format("02-01-2006 15:04:05")),
				SectionUID: section.SectionUID,
				CompleteAt: lo.If(section.CompleteAt == 0, "0").Else(time.UnixMilli(section.CompleteAt).In(location).Format("02-01-2006 15:04:05")),
				StartAt:    lo.If(section.StartAt == 0, "0").Else(time.UnixMilli(section.StartAt).In(location).Format("02-01-2006 15:04:05")),
				Event:      models.LessonContentProgress,
			})

			for _, lesson := range section.Lessons {
				dataToExport = append(dataToExport, &models.LearningProgressReport{
					UserID:     ls.UserID,
					CourseCuid: ls.CourseCuid,
					CreateAt:   lo.If(lesson.StartAt == 0, "0").Else(time.UnixMilli(lesson.StartAt).In(location).Format("02-01-2006 15:04:05")),
					SectionUID: section.SectionUID,
					LessonUID:  lesson.LessonUID,
					CompleteAt: lo.If(lesson.CompleteAt == 0, "0").Else(time.UnixMilli(lesson.CompleteAt).In(location).Format("02-01-2006 15:04:05")),
					StartAt:    lo.If(lesson.StartAt == 0, "0").Else(time.UnixMilli(lesson.StartAt).In(location).Format("02-01-2006 15:04:05")),
					Event:      models.LessonContentProgress,
				})

				for _, content := range lesson.Contents {
					dataToExport = append(dataToExport, &models.LearningProgressReport{
						UserID:           ls.UserID,
						CourseCuid:       ls.CourseCuid,
						CreateAt:         lo.If(content.StartAt == 0, "0").Else(time.UnixMilli(content.StartAt).In(location).Format("02-01-2006 15:04:05")),
						SectionUID:       section.SectionUID,
						LessonContentUID: content.ContentUID,
						LessonUID:        lesson.LessonUID,
						ContentType:      content.ContentType,
						CompleteAt:       lo.If(content.CompleteAt == 0, "0").Else(time.UnixMilli(content.CompleteAt).In(location).Format("02-01-2006 15:04:05")),
						StartAt:          lo.If(content.StartAt == 0, "0").Else(time.UnixMilli(content.StartAt).In(location).Format("02-01-2006 15:04:05")),
						PauseAt:          lo.If(content.PauseAt == 0, "0").Else(time.UnixMilli(content.PauseAt).In(location).Format("02-01-2006 15:04:05")),
						Event:            models.LessonContentProgress,
					})
				}
			}
		}
	}

	mCuidData := lo.GroupBy(dataToExport, func(item *models.LearningProgressReport) string {
		return item.CourseCuid
	})
	ex := excel.New()
	for courseCuid, data := range mCuidData {
		course, err := Course.FindOne(&models.CourseQuery{Cuid: &courseCuid}, &models.FindOneOptions{})
		if err != nil {
			continue
		}
		if len(data) > 0 {
			sheetName := models.SanitizeSheetName(course.Name)
			if err := ex.AddNewSheet(sheetName); err != nil {
				return nil, e.NewError500(e.Report_user_enrollment_course_failed, err.Error())
			}

			if err := ex.WriteData(sheetName, data); err != nil {
				return nil, e.NewError500(e.Report_user_enrollment_course_failed, err.Error())
			}

		}

	}
	excelBytes, exErr := ex.ToByte()
	if exErr != nil {
		return nil, e.NewError500(e.Report_type_not_valid, exErr.Error())

	}

	return excelBytes, nil

}

func (s *ReportService) ReportCourseQuiz() ([]byte, *e.AppError) {
	quizzes, err := models.Repository.QuizSubmission.FindMany(&models.QuizSubmissionQuery{}, &models.FindManyOptions{Preloads: []string{models.AnswersField}})
	if err != nil {
		return nil, e.NewError500(e.Quiz_submission_find_failed, err.Error())
	}

	location := time.FixedZone("ICT", 7*60*60)
	dataToExport := lo.Map(quizzes, func(item *models.QuizSubmission, _ int) *models.QuizSubmissionReport {
		return &models.QuizSubmissionReport{
			UserID:                        item.UserID,
			CourseCuid:                    item.CourseCuid,
			CreateAt:                      lo.If(item.CreateAt == 0, "0").Else(time.UnixMilli(int64(item.CreateAt)).In(location).Format("02-01-2006 15:04:05")),
			QuizUID:                       item.QuizUID,
			Status:                        item.Status,
			Passed:                        item.Passed,
			StartAt:                       lo.If(item.CreateAt == 0, "0").Else(time.UnixMilli(int64(item.CreateAt)).In(location).Format("02-01-2006 15:04:05")),
			EndAt:                         lo.If(item.EndAt == 0, "0").Else(time.UnixMilli(int64(item.EndAt)).In(location).Format("02-01-2006 15:04:05")),
			DeadlineAt:                    lo.If(item.DeadlineAt == 0, "0").Else(time.UnixMilli(int64(item.DeadlineAt)).In(location).Format("02-01-2006 15:04:05")),
			TimeToCompleteInMilliSeconds:  item.TimeToCompleteInMilliSeconds,
			ArchivedPoints:                item.ArchivedPoints,
			HighestStreak:                 item.HighestStreak,
			HighestPointsOnSingleQuestion: item.HighestPointsOnSingleQuestion,
			NumCorrectAnswers:             item.NumCorrectAnswers,
			NumQuestions:                  item.NumQuestions,
		}
	})

	mCuidData := lo.GroupBy(dataToExport, func(item *models.QuizSubmissionReport) string {
		return item.CourseCuid
	})

	ex := excel.New()
	for courseCuid, data := range mCuidData {
		course, err := Course.FindOne(&models.CourseQuery{Cuid: &courseCuid}, &models.FindOneOptions{})
		if err != nil {
			continue
		}
		if len(data) > 0 {
			sheetName := models.SanitizeSheetName(course.Name)
			if err := ex.AddNewSheet(sheetName); err != nil {
				return nil, e.NewError500(e.Report_user_enrollment_course_failed, err.Error())
			}

			if err := ex.WriteData(sheetName, data); err != nil {
				return nil, e.NewError500(e.Report_user_enrollment_course_failed, err.Error())
			}

		}

	}
	excelBytes, exErr := ex.ToByte()
	if exErr != nil {
		return nil, e.NewError500(e.Report_type_not_valid, exErr.Error())

	}

	return excelBytes, nil

}

func (s *ReportService) ReportCourseLearningStatus(data *models.ReportCourseEnrollmentRequest) ([]byte, *e.AppError) {
	userRegisters, courseID, err := models.Repository.Report.ReportUserEnrollmentCourseLearningStatus(data, nil)
	if err != nil {
		return nil, e.NewError500(e.Report_user_enrollment_course_failed, err.Error())
	}

	mapUserRegisterIDUser := make(map[string]*models.UserReport)
	lo.ForEach(userRegisters, func(u *models.UserReport, _ int) {
		mapUserRegisterIDUser[u.UserID] = u
	})

	userRegisterIDs := lo.Map(userRegisters, func(u *models.UserReport, _ int) string { return u.UserID })

	var totalRefTrackings []*communicationdto.TrackingResponse
	var totalSourceTrackings []*communicationdto.TrackingResponse
	if data.IsNeedSource {
		for _, chunkUserID := range lo.Chunk(userRegisterIDs, pageSize) {
			trackingQuery := &models.TrackingQuery{
				ActorIDIn: chunkUserID,
				Verb:      util.NewT(models.Enrolled),
				Object:    util.NewT(models.CourseModelName),
				ObjectID:  data.CourseCuid,
				Context:   util.NewT(models.SourceContext),
			}

			sort := []string{"create_at%20desc"}
			pageOptions := &models.FindPageOptions{
				Page:    1,
				PerPage: pageSize,
				Sort:    sort,
			}

			trackings, _, err := communication.Tracking.FindPaginationTracking(trackingQuery.IntoComm(), pageOptions.IntoComm())
			if err != nil {
				return nil, e.NewError500(e.External_call_error, err.Error())
			}

			lo.ForEach(trackings, func(t *communicationdto.TrackingResponse, _ int) {
				if user, ok := mapUserRegisterIDUser[t.ActorID]; ok {
					user.SourceName = t.ContextValue.(string)
				}
			})
			totalSourceTrackings = append(totalSourceTrackings, trackings...)

			trackingQuery = &models.TrackingQuery{
				ActorIDIn:    chunkUserID,
				Verb:         util.NewT(models.Referred),
				ContextValue: data.CourseCuid,
				Context:      util.NewT(models.CourseContext),
			}

			sort = []string{"create_at%20desc"}
			pageOptions = &models.FindPageOptions{
				Page:    1,
				PerPage: pageSize,
				Sort:    sort,
			}

			trackings, _, err = communication.Tracking.FindPaginationTracking(trackingQuery.IntoComm(), pageOptions.IntoComm())
			if err != nil {
				return nil, e.NewError500(e.External_call_error, err.Error())
			}

			lo.ForEach(trackings, func(t *communicationdto.TrackingResponse, _ int) {
				if user, ok := mapUserRegisterIDUser[t.ActorID]; ok {
					ref, err := models.Repository.User.FindByID(t.ObjectID)
					if err != nil {
						return
					} else {
						t.ActorEmail = ref.Email
						user.RefBy = ref.Email
					}
				}
			})

			totalRefTrackings = append(totalRefTrackings, trackings...)
		}
	}

	lo.ForEach(userRegisters, func(u *models.UserReport, _ int) {
		if u.SourceName == "" {
			u.SourceName = "direct"
		}
	})

	joinInput := []util.SliceJoinInput{
		{
			Slice: userRegisters,
			Key:   models.UserIDJoinKey,
		},
	}

	for _, formReq := range data.Form {
		mapKeyQuestionUID := lo.GroupBy(formReq.Questions, func(item models.QuestionColModel) models.ColReport {
			return item.Key
		})

		for key, question := range mapKeyQuestionUID {
			questionUIDs := lo.Map(question, func(item models.QuestionColModel, _ int) string {
				return item.QuestionUID
			})

			formQuery := &models.FormAnswerQuery{
				FormUID:       &formReq.FormUID,
				QuestionUIDIn: questionUIDs,
			}
			forms, err := models.Repository.FormAnswer.FindSimpleQuestionAnwser(formQuery)
			if err != nil {
				return nil, e.NewError500(e.Form_answer_find_failed, err.Error())
			}

			joinInput = append(joinInput, util.SliceJoinInput{
				Slice: forms,
				Key:   models.UserIDJoinKey,
				RenameFields: map[string]string{
					models.TextKey: strings.ReplaceAll(string(key), " ", ""),
				},
			})

		}
	}

	valueUserRegister, err := util.JoinSlices(joinInput, buildTagOverrides(data.ExceptKey))
	if err != nil {
		return nil, e.NewError400(e.Join_struct_failed, err.Error())
	}

	//Learning Status
	learningStatus, lsErr := FindManyWithPagination(models.Repository.LearningStatus(context.Background()).FindPage, &models.LearningStatusQuery{
		CourseCuid: data.CourseCuid,
	}, &models.FindManyOptions{}, 1000)
	if lsErr != nil {
		return nil, e.NewError400(e.INVALID_PARAMS, lsErr.Error())
	}

	userIDs := lo.Map(learningStatus, func(ls *models.LearningStatus, _ int) string {
		return ls.UserID
	})
	mapUserIDWallet := make(map[string]*models.Wallet)

	if data.Currency != "" && data.NetworkWalletAddr != "" {
		walletQuery := &models.WalletQuery{
			UserIDIn: userIDs,
			Network:  &data.NetworkWalletAddr,
			Type:     util.NewT(models.AssetTypeCrypto),
			Currency: &data.Currency,
		}

		walletUser, _ := FindManyWithPagination(models.Repository.Wallet.FindPage, walletQuery, &models.FindManyOptions{}, 1000)

		for _, w := range walletUser {
			mapUserIDWallet[w.UserID] = w
		}

	}

	users, err := models.Repository.User.FindManySimpleUser(&models.UserQuery{IDIn: &userIDs}, &models.FindManyOptions{})
	mapUserIDUser := make(map[string]models.BasicUserProfile)

	for _, user := range users {
		mapUserIDUser[user.ID] = *user
	}

	mapLessonNameUser := make(map[string][]reflect.Value)
	learnerDatas := []reflect.Value{}

	learningStatusChunk := lo.Chunk(learningStatus, len(learningStatus)/10)
	wg := sync.WaitGroup{}
	var learnerDatasMutex sync.Mutex
	var mapLessonNameUserMutex sync.Mutex

	mapSectionUIDSectionOrder := make(map[string]int)
	mapLessonUIDLessonOrder := make(map[string]int)

	lessonAndSections, err := FindManyWithPagination(models.Repository.Section.FindPage, &models.SectionQuery{
		CourseID: courseID,
	}, &models.FindManyOptions{}, 100)

	for _, ls := range lessonAndSections {
		if ls.ParentID != "" {
			mapLessonUIDLessonOrder[ls.UID] = ls.Order
		} else {
			mapSectionUIDSectionOrder[ls.UID] = ls.Order
		}
	}

	for _, lss := range learningStatusChunk {
		wg.Add(1)

		go func(learningStatus []*models.LearningStatus) {
			defer func() {
				wg.Done()
				if r := recover(); r != nil {
					log.ErrorWithAlertf("Recovered in Service::Register", r)
				}
			}()

			for _, ls := range lss {
				//init
				var date, hour, refBy, sourceName string
				for uid, section := range ls.Sections {
					sectionName := fmt.Sprintf("S%d", mapSectionUIDSectionOrder[uid]+1)
					// Lesson
					for uid, lesson := range section.Lessons {
						lessonName := fmt.Sprintf("%sL%d", sectionName, mapLessonUIDLessonOrder[uid]+1)
						if lesson.StartAt != 0 {
							date = util.ConvertToReadableTimeDate(lesson.StartAt)
							hour = util.ConvertToReadableTimeHour(lesson.StartAt)

							if data.IsNeedSource {
								trackings, ok := lo.Find(totalRefTrackings, func(t *communicationdto.TrackingResponse) bool {
									return t.ActorID == ls.UserID
								})
								if ok {
									refBy = trackings.ActorEmail
								} else {
									refBy = ""
								}

								trackings, ok = lo.Find(totalSourceTrackings, func(t *communicationdto.TrackingResponse) bool {
									return t.ActorID == ls.UserID
								})
								if ok {
									sourceName = trackings.ContextValue.(string)
								} else {
									sourceName = "direct"
								}
							}

							if user, ok := mapUserIDUser[ls.UserID]; ok {
								address := ""
								if len(mapUserIDWallet) > 0 {
									wallet, ok := mapUserIDWallet[user.ID]
									if ok {
										address = wallet.Address
									}
								}
								writeData := util.ExtendStruct(
									user,
									map[string]interface{}{"Date": date},
									map[string]interface{}{"Hour": hour},
									map[string]interface{}{"LessonName": lessonName},
									map[string]interface{}{"RefBy": refBy},
									map[string]interface{}{"SourceName": sourceName},
									map[string]interface{}{"Address": address},
								)

								learnerDatasMutex.Lock()
								learnerDatas = append(learnerDatas, writeData)
								learnerDatasMutex.Unlock()

								mapLessonNameUserMutex.Lock()
								mapLessonNameUser[lessonName] = append(mapLessonNameUser[lessonName],
									util.ExtendStruct(
										user,
										map[string]interface{}{"Date": date},
										map[string]interface{}{"Hour": hour},
										map[string]interface{}{"RefBy": refBy},
										map[string]interface{}{"SourceName": sourceName},
										map[string]interface{}{"Address": address}),
								)
								mapLessonNameUserMutex.Unlock()

							}
						}

					}
				}

			}
		}(lss)
	}
	wg.Wait()

	bytes, excelErr := handleReportCourseLearningStatusToExcel(valueUserRegister, learnerDatas, mapLessonNameUser)
	if excelErr != nil {
		return nil, e.NewError500(e.Report_user_enrollment_course_failed, excelErr.Error())
	}
	return bytes, nil
}

func handleReportCourseLearningStatusToExcel(userRegister reflect.Value, learnerDatas []reflect.Value, mapLessonNameUser map[string][]reflect.Value) ([]byte, error) {
	e := excel.New()
	sheetName := "Learner"
	if err := e.NewExcelWithSheet([]string{sheetName}); err != nil {
		return nil, err
	}
	if err := e.WriteData(sheetName, util.ConvertToReflectValueSlice(learnerDatas).Interface()); err != nil {
		return nil, err
	}

	sheetName = "UserRegister"
	if err := e.AddNewSheet(sheetName); err != nil {
		return nil, err
	}
	if err := e.WriteData(sheetName, userRegister.Interface()); err != nil {
		return nil, err
	}

	for lessonName, users := range mapLessonNameUser {
		sheetName := lessonName
		if err := e.AddNewSheet(sheetName); err != nil {
			return nil, err
		}
		if err := e.WriteData(sheetName, util.ConvertToReflectValueSlice(users).Interface()); err != nil {
			return nil, err
		}

	}

	return e.ToByte()
}
