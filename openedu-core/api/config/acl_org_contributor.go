package bootstrap

import (
	"net/http"
	v1 "openedu-core/api/v1"
)

var OrgContributorAcl = []*PermissionGrant{
	{Method: http.MethodPost, Path: "/api/v1/blogs", Controller: v1.CreateBlog, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/me", Controller: v1.FindPageMyBlog, Allow: true},

	// Exchange rates
	{Method: http.MethodGet, Path: "/api/v1/exchange-rates", Controller: v1.GetExchangeRate, Allow: true},
}
