package bootstrap

import (
	"net/http"
	v1 "openedu-core/api/v1"
)

var GuestAcl = []*PermissionGrant{
	{Method: http.MethodGet, Path: "/api/v1/auth/public", Controller: v1.<PERSON><PERSON><PERSON>, Allow: true},

	// Auth
	{Method: http.MethodPost, Path: "/api/v1/auth", Controller: v1.AuthSocial, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/login", Controller: v1.<PERSON>gin, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/register", Controller: v1.Register, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/refresh-token", Controller: v1.RefreshToken, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/resend-mail", Controller: v1.ResendMail, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/forgot-password", Controller: v1.ForgotPassword, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/reset-password", Controller: v1.ConfirmResetPassword, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/auth/verify", Controller: v1.Verify, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/external-register", Controller: v1.ExternalRegister, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/set-password", Controller: v1.SetPassword, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/verify-otp", Controller: v1.VerifyOTP, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/resend-otp", Controller: v1.ResendOTP, Allow: true},

	// Upload
	{Method: http.MethodPost, Path: "/api/v1/uploads", Controller: v1.UploadFile, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/uploads/videos/:id/status", Controller: v1.GetStatusVideoUploadFromBunny, Allow: true},

	// Organization
	{Method: http.MethodGet, Path: "/api/v1/admin/organizations", Controller: v1.FindPageOrgs, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/organizations/validate", Controller: v1.CheckOrgValidation, Allow: true},

	// Config
	{Method: http.MethodGet, Path: "/api/v1/configs", Controller: v1.GetConfig, Allow: true},

	// Course
	{Method: http.MethodGet, Path: "/api/v1/courses/publish", Controller: v1.GetPublishCourses, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id", Controller: v1.GetCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/partners", Controller: v1.GetCoursePartners, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/partners", Controller: v1.GetCoursePartners, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/outline", Controller: v1.GetCourseOutline, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/certificates", Controller: v1.GetCertificateLayerForCourse, Allow: true},

	// Quiz
	{Method: http.MethodGet, Path: "/api/v1/quizzes/:id/leaderboards", Controller: v1.GetQuizLeaderboards, Allow: true},

	// Category
	{Method: http.MethodGet, Path: "/api/v1/categories", Controller: v1.FindPageCategory, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/categories/tree", Controller: v1.GetCategoryTree, Allow: true},

	// Hashtag
	{Method: http.MethodGet, Path: "/api/v1/hashtags", Controller: v1.FindPageHashtag, Allow: true},

	// Form
	{Method: http.MethodGet, Path: "/api/v1/forms/:id", Controller: v1.FindFormByIdOrSlug, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-organization", Controller: v1.FindFormRegisterOrg, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-creator", Controller: v1.FindFormRegisterCreator, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-writer", Controller: v1.FindFormRegisterWriter, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/forms/:id/submit", Controller: v1.SubmitForm, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/by-uid/:id/submitted", Controller: v1.CheckFormSubmittedByUID, Allow: true},
	// System config
	{Method: http.MethodGet, Path: "/api/v1/system-configs", Controller: v1.GetSystemConfigs, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/system-configs/:id", Controller: v1.FindSystemConfigByID, Allow: true},

	// Creator
	{Method: http.MethodPost, Path: "/api/v1/creators/accept", Controller: v1.AcceptInvitation, Allow: true},

	{Method: http.MethodPost, Path: "/api/v1/users/accept", Controller: v1.AcceptInvitationUser, Allow: true},

	// User Agency
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id/publish-courses", Controller: v1.FindPublishCourseByCampaign, Allow: true},
	//Blog
	//Get
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags", Controller: v1.GetPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/top-viewed", Controller: v1.FindTopViewedBlog, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/top-blog-viewed", Controller: v1.FindTopUserBlogView, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/org-blogs", Controller: v1.FindPageUserOrgBlog, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags/recommend", Controller: v1.GetRecommendPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories", Controller: v1.GetPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/:id", Controller: v1.GetPublishBlogByCategoriesParent, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/recommend", Controller: v1.GetRecommendPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/personal", Controller: v1.FindBlogPersonByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/org", Controller: v1.FindBlogOrgByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs", Controller: v1.GetPublishBlogs, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/person-blogs", Controller: v1.FindPageUserPersonBlog, Allow: true},
	// User
	{Method: http.MethodGet, Path: "/api/v1/users/:id", Controller: v1.GetUserProfile, Allow: true},

	// referral links
	{Method: http.MethodGet, Path: "/api/v1/referral-links/by-code/:code", Controller: v1.GetReferralLinkByCode, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/referral-links/by-code/:code/validate", Controller: v1.ValidateReferralCode, Allow: true},

	{Method: http.MethodGet, Path: "/api/v1/users/top-blog-viewed", Controller: v1.FindTopUserBlogView, Allow: true},

	// Certificate
	{Method: http.MethodGet, Path: "/api/v1/certificates/:id", Controller: v1.GetCertificateByID, Allow: true},

	// Pricing Plans
	{Method: http.MethodGet, Path: "/api/v1/plans/:id", Controller: v1.GetPricingPlan, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/plans/all", Controller: v1.FindAllPricingPlan, Allow: true},

	// AI
	{Method: http.MethodGet, Path: "/api/v1/ai/models", Controller: v1.GetAvailableAIModel, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/ai/prompts", Controller: v1.FindPageAIPrompt, Allow: true},

	// featured content
	{Method: http.MethodGet, Path: "/api/v1/featured-contents/by-types", Controller: v1.FindFeaturedContentByType, Allow: true},

	// Launchpad
	{Method: http.MethodGet, Path: "/api/v1/launchpads", Controller: v1.FindPageLaunchpad, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads/:id", Controller: v1.GetLaunchpadByID, Allow: true},

	// Html Template
	{Method: http.MethodGet, Path: "/api/v1/html-templates/:id", Controller: v1.GetDetailTemplateByID, Allow: true},

	// OE referral leader board
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/leader-boards", Controller: v1.FindOELeaderBoardByCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/statistics/:id", Controller: v1.OEReferralStatistic, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/reports/:id/provinces", Controller: v1.OEReferralListProvinces, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/leader-boards", Controller: v1.FindOELeaderBoardByCampaign, Allow: true},

	//Report
	{Method: http.MethodGet, Path: "/api/v1/reports/:type", Controller: v1.FindStructuresReport, Allow: true},
	//Schedule
	{Method: http.MethodGet, Path: "/api/v1/schedules", Controller: v1.FindSchedules, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/event-schedules", Controller: v1.FindEventSchedule, Allow: true},

	// OE referral statistic
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/provinces", Controller: v1.GetListOERefProvinces, Allow: true},
}
