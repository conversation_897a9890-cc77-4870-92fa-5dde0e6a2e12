package bootstrap

import (
	"net/http"
	v1 "openedu-core/api/v1"
)

var OrgEditorAcl = []*PermissionGrant{
	{Method: http.MethodPost, Path: "/api/v1/users/roles", Controller: v1.AddRemoveRole, Allow: true},
	//Get
	{Method: http.MethodGet, Path: "/api/v1/admin/blogs", Controller: v1.FindPageBlogByAdmin, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/me", Controller: v1.FindPageMyBlog, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags", Controller: v1.GetPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags/recommend", Controller: v1.GetRecommendPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories", Controller: v1.GetPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/:id", Controller: v1.GetPublishBlogByCategoriesParent, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/recommend", Controller: v1.GetRecommendPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/blogs", Controller: v1.FindPageMyBlog, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/org", Controller: v1.FindBlogOrgByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs", Controller: v1.GetPublishBlogs, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/personal", Controller: v1.FindBlogPersonByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/ai/:id/rewrite", Controller: v1.GetRewriteBlogData, Allow: true},
	//POST
	{Method: http.MethodPost, Path: "/api/v1/blogs", Controller: v1.CreateBlog, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/blogs/:id/publish", Controller: v1.PublishBlog, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/blogs/ai", Controller: v1.GenBlogByAI, Allow: true},
	//PUT
	{Method: http.MethodPut, Path: "/api/v1/admin/blogs/:id", Controller: v1.UpdateBlogByAdmin, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/admin/blogs/:id/toggle-pin", Controller: v1.TogglePinBlogByAdmin, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/blogs/:id", Controller: v1.UpdateUserBlog, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/blogs/:id/toggle-pin", Controller: v1.TogglePinUserBlog, Allow: true},
	//DELETE
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id/publish-org", Controller: v1.UnPublishBlogOrg, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id/publish-person", Controller: v1.UnPublishBlogPerson, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id", Controller: v1.DeleteBlogById, Allow: true},

	// Approvals
	{Method: http.MethodPost, Path: "/api/v1/approvals", Controller: v1.CreateRequestApproval, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/approvals", Controller: v1.FindApproval, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/approvals/:id/approve", Controller: v1.ApproveRequest, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/approvals/:id/reject", Controller: v1.RejectRequest, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/admin/writers", Controller: v1.CreateWriter, Allow: true},

	// Exchange rates
	{Method: http.MethodGet, Path: "/api/v1/exchange-rates", Controller: v1.GetExchangeRate, Allow: true},
	//Findpage writer
	{Method: http.MethodGet, Path: "/api/v1/users/blog-managers", Controller: v1.FindPageBlogManagers, Allow: true},
}
