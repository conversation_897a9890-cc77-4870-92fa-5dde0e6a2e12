package bootstrap

import (
	"net/http"
	v1 "openedu-core/api/v1"
)

var OrgAdminAcl = []*PermissionGrant{
	{Method: http.MethodGet, Path: "/api/v1/auth/org-admin", Controller: v1.OrgAdminAPI, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/auth/org-mod", Controller: v1.OrgModAPI, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/auth/public", Controller: v1.GuestApi, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/auth/instructor", Controller: v1.InstructorAPI, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/auth/learner", Controller: v1.LearnerAPI, Allow: true},

	// Organization
	{Method: http.MethodPut, Path: "/api/v1/admin/organizations/:id", Controller: v1.UpdateOrg, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/admin/organizations/:id", Controller: v1.FindOrgByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/admin/organizations", Controller: v1.FindPageOrgs, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/organizations/validate", Controller: v1.CheckOrgValidation, Allow: true},

	// Config
	{Method: http.MethodGet, Path: "/api/v1/configs", Controller: v1.GetConfig, Allow: true},

	// Upload
	{Method: http.MethodPost, Path: "/api/v1/uploads", Controller: v1.UploadFile, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/uploads/videos/:id/status", Controller: v1.GetStatusVideoUploadFromBunny, Allow: true},

	// Course
	{Method: http.MethodPost, Path: "/api/v1/courses", Controller: v1.CreateCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses", Controller: v1.FindCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/publish", Controller: v1.GetPublishCourses, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/courses/:id", Controller: v1.UpdateCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id", Controller: v1.GetCourse, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/courses/:id", Controller: v1.DeleteCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/partners", Controller: v1.GetCoursePartners, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/partners", Controller: v1.UpdateCoursePartner, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/courses/:id/partners", Controller: v1.DeleteCoursePartner, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/outline", Controller: v1.GetCourseOutline, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/publish", Controller: v1.PublishCourse, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/courses/:id/publish", Controller: v1.CancelPublishCourseRequest, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/courses/:id/publish", Controller: v1.UnPublishCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/histories", Controller: v1.GetCourseVersionHistory, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/courses/:id/stage", Controller: v1.ChangeCourseStage, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/enroll", Controller: v1.EnrollCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/enrollments", Controller: v1.GetCourseEnrollments, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/forms", Controller: v1.FindPageFormRelationsByCourse, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/duplicate", Controller: v1.DuplicateCourse, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/courses/:id/reply-feedback", Controller: v1.ReplyFeedbackPublishCourseRequest, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/preview/:org_id", Controller: v1.GetCourseOutlinePreview, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/ai", Controller: v1.GenerateCourseByAI, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/nft/fees", Controller: v1.GetEstimatedFeePerMintNFT, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/nft/fees/deposit", Controller: v1.DepositSponsorGasToCourseForMintNFTs, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/nft/fees/withdraw", Controller: v1.WithdrawSponsorNftGasOfCourse, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/nft/deposit-gas-fee", Controller: v1.DepositSponsorGasToCourseForMintNFTs, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/nft/withdraw-gas-fee", Controller: v1.WithdrawSponsorNftGasOfCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/revenue-summary-reports", Controller: v1.GetCourseRevenueSummaryReport, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/revenue-detail-reports", Controller: v1.GetCourseRevenueDetailReport, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/revenue-graph-reports", Controller: v1.GetRevenueGraphReport, Allow: true},

	// publish-courses
	{Method: http.MethodGet, Path: "/api/v1/publish-courses", Controller: v1.FindPublishCourses, Allow: true},

	// Section
	{Method: http.MethodPost, Path: "/api/v1/segments", Controller: v1.CreateSection, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/segments", Controller: v1.FindSection, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/segments/:id", Controller: v1.UpdateSection, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/segments/:id", Controller: v1.GetSection, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/segments/:id", Controller: v1.DeleteSection, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/segments/bulk", Controller: v1.BulkUpdateSection, Allow: true},

	// Lesson
	{Method: http.MethodPost, Path: "/api/v1/lessons", Controller: v1.CreateLessonContent, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/lessons", Controller: v1.FindLesson, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/lessons/:id", Controller: v1.UpdateLesson, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/lessons/:id", Controller: v1.GetLesson, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/lessons/:id", Controller: v1.DeleteLesson, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/lessons/:id/learn", Controller: v1.GetLessonForLearning, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/lessons/:id/quizzes", Controller: v1.FindQuizzesByLessonContent, Allow: true},

	// Quiz
	{Method: http.MethodPost, Path: "/api/v1/quizzes", Controller: v1.CreateQuiz, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quizzes/:id", Controller: v1.FindQuizDetail, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/quizzes/:id", Controller: v1.UpdateQuiz, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/quizzes/:id", Controller: v1.DeleteQuiz, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/quizzes/:id/duplicate", Controller: v1.DuplicateQuiz, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quizzes/:id/leaderboards", Controller: v1.GetQuizLeaderboards, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quizzes/:id/submissions", Controller: v1.GetQuizSubmissionsByQuizUID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quizzes/by-uid/:id/submissions/latest-passed", Controller: v1.GetLatestPassedSubmissionByQuizUID, Allow: true},

	// Quiz Submission
	{Method: http.MethodPost, Path: "/api/v1/quiz-submissions", Controller: v1.CreateQuizSubmission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quiz-submissions/:id/questions/current", Controller: v1.GetCurrentQuestionBySubmission, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/quiz-submissions/:id/submit", Controller: v1.SubmitAnswerForSubmission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quiz-submissions/:id", Controller: v1.GetQuizSubmission, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/quiz-submissions/:id/status", Controller: v1.UpdateQuizSubmissionStatus, Allow: true},

	//Payment
	{Method: http.MethodGet, Path: "/api/v1/payments/:id", Controller: v1.FindPaymentByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/payments", Controller: v1.FindPagePayment, Allow: true},

	//Order
	{Method: http.MethodGet, Path: "/api/v1/orders", Controller: v1.FindMyOrders, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/orders", Controller: v1.CreateOrder, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/orders/:id/status", Controller: v1.CheckOrderStatus, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/orders/:id/payment", Controller: v1.PayOrderWithWallet, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/orders/:id/payment", Controller: v1.ChangePaymentMethodForOrder, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/orders/:id", Controller: v1.UpdateOrder, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/orders/:id", Controller: v1.FindOrderByID, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/orders/:id/coupons/:code", Controller: v1.UseCoupon, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/orders/:id/coupons", Controller: v1.RemoveCoupon, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/orders/:id/success", Controller: v1.MakeOrderSuccessByCoupon, Allow: true},
	//check state

	//Payment method
	//get page
	{Method: http.MethodGet, Path: "/api/v1/payment-methods", Controller: v1.FindPagePaymentMethod, Allow: true},
	// create
	{Method: http.MethodPost, Path: "/api/v1/payment-methods", Controller: v1.CreatePaymentMethod, Allow: true},
	//get page
	{Method: http.MethodGet, Path: "/api/v1/payment-methods", Controller: v1.FindPagePaymentMethod, Allow: true},
	// update
	{Method: http.MethodPut, Path: "/api/v1/payment-methods", Controller: v1.UpdatePaymentMethod, Allow: true},
	//delete
	{Method: http.MethodDelete, Path: "/api/v1/payment-methods", Controller: v1.DeletePaymentMethodById, Allow: true},

	//
	{Method: http.MethodGet, Path: "/api/v1/categories", Controller: v1.FindPageCategory, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/categories/tree", Controller: v1.GetCategoryTree, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/categories/bulk-upsert", Controller: v1.UpsertCategories, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/categories/bulk-delete", Controller: v1.DeleteCategories, Allow: true},

	// Hashtag
	{Method: http.MethodGet, Path: "/api/v1/hashtags", Controller: v1.FindPageHashtag, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/hashtags/entities", Controller: v1.FindPageHashtagRelation, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/hashtags", Controller: v1.DeleteHashtag, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/hashtags", Controller: v1.CreateHashtag, Allow: true},

	// User
	{Method: http.MethodGet, Path: "/api/v1/users/me", Controller: v1.GetMe, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/users/me", Controller: v1.UpdateProfile, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/orders", Controller: v1.FindMyOrders, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/me/change-password", Controller: v1.ChangePassword, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/permissions", Controller: v1.GetMyPermission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/actions", Controller: v1.FindPageMineAction, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/transactions", Controller: v1.GetMyTransactions, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/referrals", Controller: v1.FindReferralByUser, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/wallets", Controller: v1.GetMyWallets, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/affiliate-campaigns", Controller: v1.AffiliateCampaignByUser, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/me/wallets/:id/withdraw", Controller: v1.Request2Withdraw, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/me/wallets/:id/withdrawals", Controller: v1.WithdrawFromWallet, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/wallets/:id/claim-earning", Controller: v1.ClaimEarningsIntoWallet, Allow: true},

	// Chain
	{Method: http.MethodGet, Path: "/api/v1/chains/:network/accounts/:id", Controller: v1.GetCryptoAccountInfo, Allow: true},

	{Method: http.MethodGet, Path: "/api/v1/users/:id", Controller: v1.GetUserProfile, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/:id/follow", Controller: v1.FollowUser, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/users/:id/follow", Controller: v1.UnFollowUser, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/:id/block", Controller: v1.BlockUser, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/block", Controller: v1.BlockUsers, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/users/:id/block", Controller: v1.UnblockUser, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/actions", Controller: v1.FindPageUserAction, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/admin/users/:id/actions", Controller: v1.FindPageUserActionByAdmin, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/:id/report", Controller: v1.ReportUser, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/enrollments", Controller: v1.GetUserEnrollments, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/courses", Controller: v1.GetMyCourses, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/courses/count", Controller: v1.CountMyCourses, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/settings", Controller: v1.GetMySettingProfile, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/users/me/settings", Controller: v1.UpdateMySettingProfile, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/points/:id", Controller: v1.GetMyPoint, Allow: true},

	// User setting
	{Method: http.MethodPost, Path: "/api/v1/user-settings", Controller: v1.CreateManyUserSetting, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/user-settings", Controller: v1.FindUserSetting, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/user-settings", Controller: v1.UpdateManyUserSetting, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/user-settings", Controller: v1.DeleteManyUserSetting, Allow: true},

	// Coupon
	{Method: http.MethodGet, Path: "/api/v1/admin/coupons", Controller: v1.FindPageCouponsByPartner, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/coupons/:id", Controller: v1.FindDetailCoupon, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/coupons/:id", Controller: v1.DeleteCoupon, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/coupons/:id", Controller: v1.UpdateCoupon, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/coupons", Controller: v1.CreateCoupon, Allow: true},

	// Coupon history
	{Method: http.MethodGet, Path: "/api/v1/admin/coupon-histories", Controller: v1.FindPageCouponHistoryByAdmin, Allow: true},

	// Email
	{Method: http.MethodPost, Path: "/api/v1/emails/templates", Controller: v1.CreateEmailTemplate, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/emails/templates/:id", Controller: v1.UpdateEmailTemplate, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/emails/templates/:id", Controller: v1.GetEmailTemplate, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/emails/templates", Controller: v1.FindEmailTemplates, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/emails/templates/:id", Controller: v1.DeleteEmailTemplate, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/emails/templates/variables", Controller: v1.GetEmailTemplateVariables, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/emails/templates/:id/preview", Controller: v1.SendPreviewEmailTemplate, Allow: true},

	// Form
	{Method: http.MethodPost, Path: "/api/v1/forms", Controller: v1.CreateForm, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms", Controller: v1.FindPageForms, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/forms/:id", Controller: v1.UpdateForm, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/forms/:id/status", Controller: v1.UpdateFormStatus, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/:id", Controller: v1.FindFormByIdOrSlug, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-organization", Controller: v1.FindFormRegisterOrg, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-creator", Controller: v1.FindFormRegisterCreator, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/forms/:id/submit", Controller: v1.SubmitForm, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/by-uid/:id/submitted", Controller: v1.CheckFormSubmittedByUID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/:id/summary", Controller: v1.GetFormSummary, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/:id/sessions", Controller: v1.GetFormSessions, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/forms/:id/duplicate", Controller: v1.DuplicateForm, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/forms/:id", Controller: v1.DeleteForm, Allow: true},

	// Form relation
	{Method: http.MethodPost, Path: "/api/v1/form-relations", Controller: v1.CreateFormRelation, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/form-relations/:id", Controller: v1.UpdateFormRelation, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/form-relations/:id", Controller: v1.DeleteFormRelation, Allow: true},

	// Form question
	{Method: http.MethodGet, Path: "/api/v1/form-questions/:id/answers", Controller: v1.GetFormAnswerStatsByQuestion, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/form-questions/:id/sub-questions/:sub_id/answers", Controller: v1.GetFormAnswerStatsBySubQuestion, Allow: true},

	// System config
	{Method: http.MethodPost, Path: "/api/v1/admin/system-configs", Controller: v1.CreateSystemConfig, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/admin/system-configs/:id", Controller: v1.DeleteSystemConfig, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/system-configs", Controller: v1.GetSystemConfigs, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/system-configs/:id", Controller: v1.FindSystemConfigByID, Allow: true},

	// Blog
	//Get
	{Method: http.MethodGet, Path: "/api/v1/admin/blogs", Controller: v1.FindPageBlogByAdmin, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/personal", Controller: v1.FindBlogPersonByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/org", Controller: v1.FindBlogOrgByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/me", Controller: v1.FindPageMyBlog, Allow: true},
	//{Method: http.MethodGet, Path: "/api/v1/users/:id/blogs/:blogID", Controller: v1.FindUserBlogByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags", Controller: v1.GetPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/:id", Controller: v1.GetPublishBlogByCategoriesParent, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs", Controller: v1.GetPublishBlogs, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags/recommend", Controller: v1.GetRecommendPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories", Controller: v1.GetPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/recommend", Controller: v1.GetRecommendPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/blogs", Controller: v1.FindPageMyBlog, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/ai/:id/rewrite", Controller: v1.GetRewriteBlogData, Allow: true},
	//POST
	{Method: http.MethodPost, Path: "/api/v1/blogs", Controller: v1.CreateBlog, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/blogs/:id/publish", Controller: v1.PublishBlog, Allow: true},
	//PUT
	{Method: http.MethodPut, Path: "/api/v1/admin/blogs/:id", Controller: v1.UpdateBlogByAdmin, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/admin/blogs/:id/toggle-pin", Controller: v1.TogglePinBlogByAdmin, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/blogs/:id", Controller: v1.UpdateUserBlog, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/blogs/:id/toggle-pin", Controller: v1.TogglePinUserBlog, Allow: true},
	//DELETE
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id/publish-org", Controller: v1.UnPublishBlogOrg, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id/publish-person", Controller: v1.UnPublishBlogPerson, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id", Controller: v1.DeleteBlogById, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/blogs/ai", Controller: v1.GenBlogByAI, Allow: true},
	//Approval
	{Method: http.MethodPost, Path: "/api/v1/approvals", Controller: v1.CreateRequestApproval, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/approvals", Controller: v1.FindApproval, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/approvals/:id/approve", Controller: v1.ApproveRequest, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/approvals/:id/reject", Controller: v1.RejectRequest, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/approvals/:id/feedback", Controller: v1.SendApprovalFeedBack, Allow: true},

	//Creator
	{Method: http.MethodPost, Path: "/api/v1/admin/creators", Controller: v1.CreateCreator, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/admin/writers", Controller: v1.CreateWriter, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/admin/creators", Controller: v1.FindPageCreator, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/admin/creators/invite", Controller: v1.InviteCreator, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/admin/creators", Controller: v1.DeleteCreator, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/form-sessions/:id/reject", Controller: v1.RejectFormSession, Allow: true},
	// Users
	{Method: http.MethodGet, Path: "/api/v1/users", Controller: v1.FindPageUserOrg, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/roles", Controller: v1.AddRemoveRole, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/admin/users/mod-accounts", Controller: v1.FindModAccounts, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/admin/users/roles", Controller: v1.AddRemoveRole, Allow: true},

	// User Invitation
	{Method: http.MethodGet, Path: "/api/v1/creator-invitations", Controller: v1.FindPageInvitation, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/creator-invitations", Controller: v1.ResendInvitation, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/invite", Controller: v1.InviteUser, Allow: true},
	// Course Enrollment

	{Method: http.MethodPut, Path: "/api/v1/enrollments/:id", Controller: v1.UpdateCourseEnrollment, Allow: true},

	// Learning Progress
	{Method: http.MethodPost, Path: "/api/v1/learning-progresses", Controller: v1.CreateLearningProgress, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/learning-progresses/courses/:id", Controller: v1.GetUserLearningProgressByCourse, Allow: true},

	// Learning Progress
	{Method: http.MethodPost, Path: "/api/v1/learning-progresses", Controller: v1.CreateLearningProgress, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/learning-progresses/courses/:id", Controller: v1.GetUserLearningProgressByCourse, Allow: true},

	// Bookmark
	{Method: http.MethodPost, Path: "/api/v1/bookmarks", Controller: v1.CreateBookmark, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/bookmarks", Controller: v1.FindBookmarks, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/bookmarks/:id", Controller: v1.GetBookmarkByID, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/bookmarks/:id", Controller: v1.UpdateBookmark, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/bookmarks/:id", Controller: v1.DeleteBookmarkByID, Allow: true},
	// Certificate
	{Method: http.MethodPost, Path: "/api/v1/certificates", Controller: v1.CreateCertificate, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/certificates/check-condition", Controller: v1.CheckCertificateCondition, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/certificates", Controller: v1.FindPageCertificate, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/certificates/:id", Controller: v1.DeleteCertificateById, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/certificates/:id", Controller: v1.GetCertificateByID, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/certificates/:id/nft", Controller: v1.MintNFTForCertificate, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/certificates/:id/nft/fees", Controller: v1.EstimatedMintNFTFees, Allow: true},

	// Html template
	{Method: http.MethodPost, Path: "/api/v1/html-templates", Controller: v1.CreateHtmlTemplate, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/html-templates", Controller: v1.FindPageHtmlTemplate, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/html-templates/:id/enable", Controller: v1.EnableCertificateTemplateForOrg, Allow: true},

	// affiliate campaign
	{Method: http.MethodPost, Path: "/api/v1/affiliate-campaigns", Controller: v1.CreateAffiliateCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns", Controller: v1.FindAffiliateCampaign, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/affiliate-campaigns/:id", Controller: v1.UpdateAffiliateCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id", Controller: v1.FindOneAffiliateCampaign, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/affiliate-campaigns/:id", Controller: v1.DeleteAffiliateCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id/referrers", Controller: v1.FindReferrerCampaign, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/affiliate-campaigns/:id/courses", Controller: v1.AddCourses2AffiliateCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id/courses", Controller: v1.FindCourseByCampaign, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/affiliate-campaigns/:id/courses", Controller: v1.DeleteCoursesFromAffiliateCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id/links", Controller: v1.GetReferralLinks, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id/publish-courses", Controller: v1.FindPublishCourseByCampaign, Allow: true},

	// referrers
	{Method: http.MethodPost, Path: "/api/v1/referrers", Controller: v1.AddReferrer, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/referrers", Controller: v1.RemoveReferrer, Allow: true},

	// commissions
	{Method: http.MethodPost, Path: "/api/v1/commissions", Controller: v1.CreateCommission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/commissions", Controller: v1.FindCommissions, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/commissions", Controller: v1.DeleteCommission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/commissions/:id", Controller: v1.GetCommission, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/commissions/:id", Controller: v1.UpdateCommission, Allow: true},

	// referral links
	{Method: http.MethodPost, Path: "/api/v1/referral-links", Controller: v1.CreateReferralLink, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/referral-links/:id/extend", Controller: v1.CreateExtendReferralLink, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/referral-links/:id", Controller: v1.EditReferralLink, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/referral-links/by-code/:code", Controller: v1.GetReferralLinkByCode, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/referral-links/by-code/:code/validate", Controller: v1.ValidateReferralCode, Allow: true},

	// referral
	{Method: http.MethodGet, Path: "/api/v1/referrals", Controller: v1.FindReferrals, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/referrals/summaries", Controller: v1.ReferralSummaryReport, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/referrals/user-reports", Controller: v1.ReferralReportByUsers, Allow: true},

	// User Invitation
	{Method: http.MethodGet, Path: "/api/v1/user-invitations", Controller: v1.FindPageInvitation, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/user-invitations", Controller: v1.ResendInvitation, Allow: true},

	// Report
	{Method: http.MethodPost, Path: "/api/v1/reports/course-enrollments", Controller: v1.ReportCourseEnrollment, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/reports/course-referral", Controller: v1.ReportCourseReferral, Allow: true},

	// Exchange rates
	{Method: http.MethodGet, Path: "/api/v1/exchange-rates", Controller: v1.GetExchangeRate, Allow: true},

	// Subscription
	{Method: http.MethodGet, Path: "/api/v1/users/me/subscriptions", Controller: v1.GetCurrentSubscription, Allow: true},
	// EarnedPoint Campaign
	{Method: http.MethodPost, Path: "/api/v1/point-campaigns", Controller: v1.CreateRefUserCampaign, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/point-campaigns/:id", Controller: v1.UpdatePointCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/point-campaigns", Controller: v1.FindPagePointCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/point-campaigns/:id", Controller: v1.FindPointCampaignByID, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/point-campaigns/:id", Controller: v1.DeletePointCampaign, Allow: true},

	// Launchpad
	{Method: http.MethodPost, Path: "/api/v1/launchpads", Controller: v1.CreateLaunchpad, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads", Controller: v1.FindPageLaunchpad, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads/:id", Controller: v1.GetLaunchpadByID, Allow: true},
	{Method: http.MethodPatch, Path: "/api/v1/launchpads/:id", Controller: v1.UpdateLaunchpad, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/launchpads/:id", Controller: v1.DeleteLaunchpad, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads/:id/partners", Controller: v1.GetLaunchpadPartners, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/bookmark", Controller: v1.BookmarkLaunchpad, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/launchpads/:id/bookmark", Controller: v1.RemoveBookmarkLaunchpad, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/claim-refund", Controller: v1.ClaimLaunchpadRefund, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/publish", Controller: v1.PublishLaunchpad, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/launchpads/:id/publish", Controller: v1.CancelPublishLaunchpadRequest, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/milestones/:milestone_id/start", Controller: v1.StartVotingMilestoneLaunchpad, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/pools", Controller: v1.InitLaunchpadPool, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/launchpads/:id/funding-time", Controller: v1.SetLaunchFundingTime, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/cancel", Controller: v1.CancelLaunchpad, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/decide-voting", Controller: v1.CreatorDecideContinueVoting, Allow: true},

	// oe-referral
	{Method: http.MethodPost, Path: "/api/v1/oe-referrals/leader-boards", Controller: v1.AddLeaderBoardRecord, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/oe-referrals/leader-boards/:id", Controller: v1.OELeaderBoardUpdateRecord, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/oe-referrals/leader-boards/:id", Controller: v1.OELeaderBoardDeleteRecord, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/blog-managers", Controller: v1.FindPageBlogManagers, Allow: true},

	// OE referral statistic
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/statistics/widget", Controller: v1.GetOERefWidgetStatistic, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/statistics/learner-growth", Controller: v1.GetOERefLearnerGrowthStatistic, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/statistics/section-completion", Controller: v1.GetOERefSectionCompletionStatistic, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/statistics/provinces", Controller: v1.GetOERefLearnerCountByProvincesStatistic, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/statistics/provinces/details", Controller: v1.GetOERefDetailsByProvinceStatistic, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/learners", Controller: v1.GetOERefLearnersByCampaign, Allow: true},

}
