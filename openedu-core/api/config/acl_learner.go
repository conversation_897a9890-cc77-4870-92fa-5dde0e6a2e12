package bootstrap

import (
	"net/http"
	v1 "openedu-core/api/v1"
)

var LearnerAcl = []*PermissionGrant{
	// Auth
	{Method: http.MethodPost, Path: "/api/v1/auth/link", Controller: v1.LinkSnsAccount, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/auth/learner", Controller: v1.<PERSON>rner<PERSON>, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/auth/public", Controller: v1.GuestApi, Allow: true},

	// User
	{Method: http.MethodGet, Path: "/api/v1/auth/user", Controller: v1.<PERSON><PERSON>, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/auth/refresh-token", Controller: v1.RefreshToken, Allow: true},

	// Organization
	{Method: http.MethodGet, Path: "/api/v1/organizations", Controller: v1.FindPageOrgs, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/organizations/validate", Controller: v1.CheckOrgValidation, Allow: true},

	// Config
	{Method: http.MethodGet, Path: "/api/v1/configs", Controller: v1.GetConfig, Allow: true},

	// Upload
	{Method: http.MethodPost, Path: "/api/v1/uploads", Controller: v1.UploadFile, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/uploads/videos/:id/status", Controller: v1.GetStatusVideoUploadFromBunny, Allow: true},

	// Users
	//{Method: http.MethodGet, Path: "/api/v1/users", Controller: v1.GetUsers, Allow: true},
	//{Method: http.MethodGet, Path: "/api/v1/users/me", Controller: v1.GetMe, Allow: true},

	// Roles
	//{Method: http.MethodGet, Path: "/api/v1/roles", Controller: v1.GetRoles, Allow: true},

	{Method: http.MethodGet, Path: "/api/v1/lessons/:id", Controller: v1.GetLesson, Allow: true},

	// Course
	{Method: http.MethodGet, Path: "/api/v1/courses", Controller: v1.FindCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/publish", Controller: v1.GetPublishCourses, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id", Controller: v1.GetCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/partners", Controller: v1.GetCoursePartners, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/courses/:id/partners", Controller: v1.UpdateCoursePartner, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/outline", Controller: v1.GetCourseOutline, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/courses/:id/certificates", Controller: v1.GetCertificateLayerForCourse, Allow: true},

	// Lesson
	{Method: http.MethodGet, Path: "/api/v1/lessons/:id/learn", Controller: v1.GetLessonForLearning, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/lessons/:id/courses/:cid", Controller: v1.GetLessonForLearningByUID, Allow: true},

	// Quiz
	{Method: http.MethodGet, Path: "/api/v1/quizzes/:id", Controller: v1.FindQuizDetail, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quizzes/:id/leaderboards", Controller: v1.GetQuizLeaderboards, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quizzes/:id/submissions", Controller: v1.GetQuizSubmissionsByQuizUID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quizzes/by-uid/:id/submissions/latest-passed", Controller: v1.GetLatestPassedSubmissionByQuizUID, Allow: true},

	// Quiz Submission
	{Method: http.MethodPost, Path: "/api/v1/quiz-submissions", Controller: v1.CreateQuizSubmission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quiz-submissions/:id/questions/current", Controller: v1.GetCurrentQuestionBySubmission, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/quiz-submissions/:id/submit", Controller: v1.SubmitAnswerForSubmission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quiz-submissions/:id", Controller: v1.GetQuizSubmission, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/quiz-submissions/:id/status", Controller: v1.UpdateQuizSubmissionStatus, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/quiz-submissions/summary", Controller: v1.GetQuizSubmissionSummary, Allow: true},

	// Payment
	{Method: http.MethodGet, Path: "/api/v1/payments", Controller: v1.FindPagePayment, Allow: true},

	//Order
	{Method: http.MethodPost, Path: "/api/v1/orders", Controller: v1.CreateOrder, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/orders/:id/status", Controller: v1.CheckOrderStatus, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/orders/:id/payment", Controller: v1.PayOrderWithWallet, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/orders/:id/payment", Controller: v1.ChangePaymentMethodForOrder, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/orders/:id/coupons/:code", Controller: v1.UseCoupon, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/orders/:id/coupons", Controller: v1.RemoveCoupon, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/orders/:id", Controller: v1.UpdateOrder, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/orders", Controller: v1.FindMyOrders, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/orders/:id/success", Controller: v1.MakeOrderSuccessByCoupon, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/orders/:id", Controller: v1.FindOrderByID, Allow: true},

	//Payment method
	{Method: http.MethodGet, Path: "/api/v1/payment-methods", Controller: v1.FindPagePaymentMethod, Allow: true},

	//Order item
	{Method: http.MethodGet, Path: "/api/v1/order-items", Controller: v1.FindPageOrderItem, Allow: true},

	// Category
	{Method: http.MethodGet, Path: "/api/v1/categories", Controller: v1.FindPageCategory, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/categories/tree", Controller: v1.GetCategoryTree, Allow: true},

	// Hashtag
	{Method: http.MethodGet, Path: "/api/v1/hashtags", Controller: v1.FindPageHashtag, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/hashtags/entities", Controller: v1.FindPageHashtagRelation, Allow: true},

	// User
	{Method: http.MethodGet, Path: "/api/v1/users/me", Controller: v1.GetMe, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/users/me", Controller: v1.UpdateProfile, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/orders", Controller: v1.FindMyOrders, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/me/change-password", Controller: v1.ChangePassword, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/permissions", Controller: v1.GetMyPermission, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/actions", Controller: v1.FindPageMineAction, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/transactions", Controller: v1.GetMyTransactions, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/referrals", Controller: v1.FindReferralByUser, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/referrals/summaries", Controller: v1.ReferralSummaryReport, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/wallets", Controller: v1.GetMyWallets, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/affiliate-campaigns", Controller: v1.AffiliateCampaignByUser, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/approvals", Controller: v1.FindMyApproval, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/me/wallets/:id/withdraw", Controller: v1.Request2Withdraw, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/me/wallets/:id/withdrawals", Controller: v1.WithdrawFromWallet, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/wallets/:id/claim-earning", Controller: v1.ClaimEarningsIntoWallet, Allow: true},

	// Sponsor Wallet
	{Method: http.MethodPost, Path: "/api/v1/sponsor-wallets", Controller: v1.CreateSponsorWallet, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/sponsor-wallets", Controller: v1.GetSponsorWallet, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/sponsor-wallets/network", Controller: v1.GetSponsorWalletByNetwork, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/sponsor-wallets/sync", Controller: v1.SyncSponsorWallet, Allow: true},

	{Method: http.MethodGet, Path: "/api/v1/users/me/courses", Controller: v1.GetMyCourses, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/courses/count", Controller: v1.CountMyCourses, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/settings", Controller: v1.GetMySettingProfile, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/users/me/settings", Controller: v1.UpdateMySettingProfile, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/points/:id", Controller: v1.GetMyPoint, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/me/claim-points", Controller: v1.ClaimNewPoints, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/point-histories", Controller: v1.GetMyPointHistories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/oe-referrals", Controller: v1.GetMyOEReferral, Allow: true},

	// Chain
	{Method: http.MethodGet, Path: "/api/v1/chains/:network/accounts/:id", Controller: v1.GetCryptoAccountInfo, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id", Controller: v1.GetUserProfile, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/:id/follow", Controller: v1.FollowUser, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/users/:id/follow", Controller: v1.UnFollowUser, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/:id/block", Controller: v1.BlockUser, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/block", Controller: v1.BlockUsers, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/users/:id/block", Controller: v1.UnblockUser, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/actions", Controller: v1.FindPageUserAction, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/users/:id/report", Controller: v1.ReportUser, Allow: true},

	// User setting
	{Method: http.MethodPost, Path: "/api/v1/user-settings", Controller: v1.CreateManyUserSetting, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/user-settings", Controller: v1.FindUserSetting, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/user-settings", Controller: v1.UpdateManyUserSetting, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/user-settings", Controller: v1.DeleteManyUserSetting, Allow: true},

	// Coupon
	{Method: http.MethodPost, Path: "/api/v1/coupons/use", Controller: v1.UseCoupon, Allow: true},

	// Form
	{Method: http.MethodGet, Path: "/api/v1/forms/:id", Controller: v1.FindFormByIdOrSlug, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-organization", Controller: v1.FindFormRegisterOrg, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-creator", Controller: v1.FindFormRegisterCreator, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/register-writer", Controller: v1.FindFormRegisterWriter, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/forms/:id/submit", Controller: v1.SubmitForm, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/forms/by-uid/:id/submitted", Controller: v1.CheckFormSubmittedByUID, Allow: true},

	// System config
	{Method: http.MethodGet, Path: "/api/v1/system-configs", Controller: v1.GetSystemConfigs, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/system-configs/:id", Controller: v1.FindSystemConfigByID, Allow: true},

	// Blog
	{Method: http.MethodGet, Path: "/api/v1/blogs", Controller: v1.GetPublishBlogs, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/personal", Controller: v1.FindBlogPersonByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/org", Controller: v1.FindBlogOrgByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/me", Controller: v1.FindPageMyBlog, Allow: true},
	//{Method: http.MethodGet, Path: "/api/v1/users/:id/blogs/:blogID", Controller: v1.FindUserBlogByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags", Controller: v1.GetPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/hashtags/recommend", Controller: v1.GetRecommendPublishBlogByHashTags, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/:id", Controller: v1.GetPublishBlogByCategoriesParent, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories", Controller: v1.GetPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/categories/recommend", Controller: v1.GetRecommendPublishBlogByCategories, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/me/blogs", Controller: v1.FindPageMyBlog, Allow: true},
	//POST
	{Method: http.MethodPost, Path: "/api/v1/blogs", Controller: v1.CreateBlog, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/blogs/:id/publish", Controller: v1.PublishBlog, Allow: true},

	//PUT
	{Method: http.MethodPut, Path: "/api/v1/blogs/:id", Controller: v1.UpdateUserBlog, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/blogs/:id/toggle-pin", Controller: v1.TogglePinUserBlog, Allow: true},
	//DELETE
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id/publish-org", Controller: v1.UnPublishBlogOrg, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id/publish-person", Controller: v1.UnPublishBlogPerson, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/person-blogs", Controller: v1.FindPageUserPersonBlog, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/org-blogs", Controller: v1.FindPageUserOrgBlog, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/blogs/:id", Controller: v1.DeleteBlogById, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/blogs/:id/edit", Controller: v1.FindBlogByIDToEdit, Allow: true},

	//Approval
	{Method: http.MethodPost, Path: "/api/v1/approvals", Controller: v1.CreateRequestApproval, Allow: true},

	// Course Enrollment
	{Method: http.MethodPost, Path: "/api/v1/courses/:id/enroll", Controller: v1.EnrollCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/:id/enrollments", Controller: v1.GetUserEnrollments, Allow: true},

	// Learning Progress
	{Method: http.MethodPost, Path: "/api/v1/learning-progresses", Controller: v1.CreateLearningProgress, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/learning-progresses/courses/:id", Controller: v1.GetUserLearningProgressByCourse, Allow: true},

	// Learning Progress
	{Method: http.MethodPost, Path: "/api/v1/learning-progresses", Controller: v1.CreateLearningProgress, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/learning-progresses/courses/:id", Controller: v1.GetUserLearningProgressByCourse, Allow: true},

	// Bookmark
	{Method: http.MethodPost, Path: "/api/v1/bookmarks", Controller: v1.CreateBookmark, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/bookmarks", Controller: v1.FindBookmarks, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/bookmarks/:id", Controller: v1.GetBookmarkByID, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/bookmarks/:id", Controller: v1.UpdateBookmark, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/bookmarks/:id", Controller: v1.DeleteBookmarkByID, Allow: true},

	// Certificate
	{Method: http.MethodPost, Path: "/api/v1/certificates", Controller: v1.CreateCertificate, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/certificates/check-condition", Controller: v1.CheckCertificateCondition, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/certificates", Controller: v1.FindPageCertificate, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/certificates/:id", Controller: v1.GetCertificateByID, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/certificates/:id/nft", Controller: v1.MintNFTForCertificate, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/certificates/:id/nft/fees", Controller: v1.EstimatedMintNFTFees, Allow: true},

	// Html template
	{Method: http.MethodGet, Path: "/api/v1/html-templates", Controller: v1.FindPageHtmlTemplate, Allow: true},
	//{Method: http.MethodGet, Path: "/api/v1/html-templates/:id", Controller: v1.FindByHtmlTemplateId, Allow: true},

	// affiliate campaign
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id", Controller: v1.FindOneAffiliateCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id/links", Controller: v1.GetReferralLinks, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/affiliate-campaigns/:id/publish-courses", Controller: v1.FindPublishCourseByCampaign, Allow: true},

	// referral links
	{Method: http.MethodPost, Path: "/api/v1/referral-links", Controller: v1.CreateReferralLink, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/referral-links/:id/extend", Controller: v1.CreateExtendReferralLink, Allow: true},
	{Method: http.MethodPut, Path: "/api/v1/referral-links/:id", Controller: v1.EditReferralLink, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/referral-links/by-code/:code", Controller: v1.GetReferralLinkByCode, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/referral-links/by-code/:code/validate", Controller: v1.ValidateReferralCode, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/users/top-blog-viewed", Controller: v1.FindTopUserBlogView, Allow: true},

	// referral
	{Method: http.MethodGet, Path: "/api/v1/referrals", Controller: v1.FindReferrals, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/referrals/summaries", Controller: v1.ReferralSummaryReport, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/referrals/user-reports", Controller: v1.ReferralReportByUsers, Allow: true},

	// Exchange rates
	{Method: http.MethodGet, Path: "/api/v1/exchange-rates", Controller: v1.GetExchangeRate, Allow: true},

	// Pricing Plans
	{Method: http.MethodGet, Path: "/api/v1/plans/:id", Controller: v1.GetPricingPlan, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/plans/all", Controller: v1.FindAllPricingPlan, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/plans/:id/subscribes", Controller: v1.SubscribePlan, Allow: true},

	// Subscription
	{Method: http.MethodGet, Path: "/api/v1/users/me/subscriptions", Controller: v1.GetCurrentSubscription, Allow: true},

	//Game link
	{Method: http.MethodGet, Path: "/api/v1/learning-progresses/check-complete-course", Controller: v1.CheckCompleteCourse, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/trackings/user-stats-count", Controller: v1.CountTrackingByUserAndEvent, Allow: true},

	// featured content
	{Method: http.MethodGet, Path: "/api/v1/featured-contents/by-types", Controller: v1.FindFeaturedContentByType, Allow: true},

	// Launchpad
	{Method: http.MethodGet, Path: "/api/v1/launchpads", Controller: v1.FindPageLaunchpad, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads/:id", Controller: v1.GetLaunchpadByID, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads/:id/partners", Controller: v1.GetLaunchpadPartners, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/bookmark", Controller: v1.BookmarkLaunchpad, Allow: true},
	{Method: http.MethodDelete, Path: "/api/v1/launchpads/:id/bookmark", Controller: v1.RemoveBookmarkLaunchpad, Allow: true},
	{Method: http.MethodPost, Path: "/api/v1/launchpads/:id/claim-refund", Controller: v1.ClaimLaunchpadRefund, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads/:id/investments", Controller: v1.FindInvestmentsForLaunchpad, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/launchpads/investments/me", Controller: v1.GetMyLaunchpads, Allow: true},

	// Investment
	{Method: http.MethodPost, Path: "/api/v1/investments", Controller: v1.CreateInvestment, Allow: true},

	// Milestone
	{Method: http.MethodPost, Path: "/api/v1/milestones/:id/voting", Controller: v1.InvestmentVotingForLaunchpad, Allow: true},

	// OE Referral
	{Method: http.MethodPost, Path: "/api/v1/oe-referrals/invites", Controller: v1.OEReferralInviteReferee, Allow: true},

	// point campaign
	{Method: http.MethodGet, Path: "/api/v1/point-campaigns", Controller: v1.FindPagePointCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/point-campaigns/:id", Controller: v1.FindPointCampaignByID, Allow: true},

	// OE referral leader board
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/:id/leader-boards", Controller: v1.FindOELeaderBoardByCampaign, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/statistics/:id", Controller: v1.OEReferralStatistic, Allow: true},
	{Method: http.MethodGet, Path: "/api/v1/oe-referrals/reports/:id/provinces", Controller: v1.OEReferralListProvinces, Allow: true},
}
