package bootstrap

import (
	"fmt"
	"openedu-core/models"
	"openedu-core/pkg/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

type PermissionGrant struct {
	Controller func(c *gin.Context) `json:"controller_name"`
	Method     string               `json:"method"`
	Path       string               `json:"path"`
	Allow      bool                 `json:"allow"`
}

func defaultPermissionGrants() map[string][]*PermissionGrant {
	return map[string][]*PermissionGrant{
		models.SystemAdminRoleType:   SysAdminAcl,
		models.AdminRoleType:         AdminAcl,
		models.ModeratorRoleType:     ModeratorAcl,
		models.OrgAdminRoleType:      OrgAdminAcl,
		models.OrgModeratorRoleType:  OrgModeratorAcl,
		models.OrgModerator2RoleType: append(OrgModerator2Acl, LearnerAcl...),
		models.PartnerRoleType:       PartnerAcl,
		models.LearnerRoleType:       LearnerAcl,
		models.GuestRoleType:         GuestAcl,
		models.OrgEditorRoleType:     OrgEditorAcl,
		models.OrgWriterRoleType:     OrgWriterAcl,
	}
}

func MigratePermissions() error {
	roles, err := models.Repository.Role.FindAll()
	if err != nil {
		return err
	}

	dbPermissions, err := models.Repository.Permission.FetchAll()
	if err != nil {
		return err
	}

	// Use map for fast looking up
	roleByType := make(map[string]*models.Role)
	for _, role := range roles {
		roleByType[role.ID] = role
	}

	// Grant permissions for each roles
	var defaultPermissions []*models.Permission
	for roleType, permissionGrants := range defaultPermissionGrants() {
		role := roleByType[roleType]

		for _, permissionGrant := range permissionGrants {
			defaultPermissions = append(defaultPermissions, &models.Permission{
				Method:         permissionGrant.Method,
				Path:           permissionGrant.Path,
				ControllerName: util.NameOfFunction(permissionGrant.Controller),
				RoleID:         role.ID,
				Allow:          permissionGrant.Allow,
			})
		}
	}

	toCreate, toDelete := util.DifferenceBy(defaultPermissions, dbPermissions, func(permission *models.Permission) string {
		allow := "false"
		if permission.Allow {
			allow = "true"
		}
		return permission.Method + "::" + permission.Path + "::" + permission.ControllerName + "::" + permission.RoleID + allow
	})
	if len(toCreate) > 0 {
		if err = models.Repository.Permission.CreateMany(toCreate, nil); err != nil {
			return fmt.Errorf("failed to create new permissions to DB: %v", err)
		}
	}

	if len(toDelete) > 0 {
		toDeleteIDs := lo.Map(toDelete, func(permission *models.Permission, _ int) string {
			return permission.ID
		})
		_, err = models.Repository.Permission.DeleteMany(&models.PermissionQuery{
			IDIn: toDeleteIDs,
		}, nil)
		if err != nil {
			return fmt.Errorf("failed to delete unused permissions from DB: %v", err)
		}
	}

	return nil
}
