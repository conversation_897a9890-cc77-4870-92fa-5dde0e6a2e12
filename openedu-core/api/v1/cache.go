package v1

import (
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/setting"
	"strings"

	"github.com/gin-gonic/gin"
)

// DeleteCaches
//
//	@Summary		Delete all caches
//	@Description	Delete all caches
//
//	@Tags			cache
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referer	header		string	true	"X-referer"
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/caches [DELETE]
func DeleteCaches(c *gin.Context) {
	appG := app.Gin{C: c}
	if err := models.Cache.DeleteAll(); err != nil {
		appG.Response500(e.Cache_delete_all_failed, err.Error())
		return
	}
	appG.Response200("success")
}

// DeleteCacheByKey
//
//	@Summary		Delete cache by key
//	@Description	Delete cache by key
//
//	@Tags			cache
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referer	header		string	true	"X-referer"
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/caches/{key} [DELETE]
func DeleteCacheByKey(c *gin.Context) {
	appG := app.Gin{C: c}
	cacheKey := c.Param("key")
	var err error
	switch {
	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.UserCachePrefix):
		err = models.Cache.User.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.PermissionCachePrefix):
		err = models.Cache.Permission.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.SystemConfigCachePrefix):
		err = models.Cache.System.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.OrganizationCachePrefix):
		err = models.Cache.Organization.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.CategoryCachePrefix):
		err = models.Cache.Category.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.UserRoleCachePrefix):
		err = models.Cache.UserRole.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.FormCachePrefix):
		err = models.Cache.Form.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.WalletCachePrefix):
		err = models.Cache.Wallet.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.QuizCachePrefix):
		err = models.Cache.Quiz.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.QuizAnswerCachePrefix):
		err = models.Cache.QuizAnswer.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.BlogCachePrefix):
		err = models.Cache.Blog.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.CourseEnrollmentCachePrefix):
		err = models.Cache.CourseEnrollment.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.ExchangeRateCachePrefix):
		err = models.Cache.ExchangeRate.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.TransactionCachePrefix):
		err = models.Cache.Transaction.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.WalletEarningCachePrefix):
		err = models.Cache.WalletEarning.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.SubscriptionCachePrefix):
		err = models.Cache.Subscription.DeleteByKey(cacheKey)

	case strings.HasPrefix(cacheKey, setting.DatabaseSetting.TablePrefix+models.FormSessionCachePrefix):
		err = models.Cache.FormSession.DeleteByKey(cacheKey)
	}

	if err != nil {
		appG.Response500(e.Cache_delete_by_key_failed, err.Error())
		return
	}
	appG.Response200("success")
}
