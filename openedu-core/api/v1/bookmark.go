package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// CreateBookmark
//
//	@Summary		Create a bookmark
//	@Description	Create a bookmark
//
//	@Tags			bookmark
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string				true	"Origin"
//	@Param			X-referrer	header		string				true	"X-referrer"
//
//	@Param			Input		body		dto.BookmarkRequest	true	"Create bookmark input"
//
//	@Success		201			{object}	app.ResponseT[models.Bookmark]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/bookmarks [POST]
func CreateBookmark(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.BookmarkRequest{}
	user := appG.GetLoggedUser()
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	existingBookmark, _ := services.Bookmark.FindOne(&models.BookmarkQuery{
		EntityID:       &req.EntityID,
		EntityType:     &req.EntityType,
		UserID:         &user.ID,
		ParentID:       req.ParentID,
		IncludeDeleted: util.NewBool(false),
	}, nil)
	if existingBookmark != nil {
		appG.Response200(existingBookmark)
		return
	}

	req.UserID = user.ID
	bookmark, appErr := services.Bookmark.Create(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(bookmark)
}

// DeleteBookmarkByID
//
//	@Summary		Delete bookmark by ID
//	@Description	Delete bookmark by ID
//
//	@Tags			bookmark
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Bookmark ID"
//
//	@Success		200			{object}	app.ResponseT[string]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/bookmarks/{id} [DELETE]
func DeleteBookmarkByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(util.IDParamKey)

	bookmark, appErr := services.Bookmark.FindByID(id, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	user := appG.GetLoggedUser()
	if !user.CanDeleteBookmark(bookmark) {
		appG.Response403(e.Bookmark_need_permission, "You are not owner of bookmark")
		return
	}

	if appErr = services.Bookmark.Delete(bookmark); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// GetBookmarkByID
//
//	@Summary		Find bookmark by ID
//	@Description	Find bookmark by ID
//
//	@Tags			bookmark
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Bookmark ID"
//
//	@Success		200			{object}	app.ResponseT[models.Bookmark]
//	@Failure		404			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/bookmarks/{id} [GET]
func GetBookmarkByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(util.IDParamKey)
	user := appG.GetLoggedUser()
	query := models.BookmarkQuery{
		ID:     &id,
		UserID: &user.ID, // Only bookmark's owner can view
	}
	bookmark, appErr := services.Bookmark.FindOne(&query, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(bookmark)
}

// FindBookmarks
//
//	@Summary		Find bookmarks with pagination
//	@Description	Find bookmarks with pagination
//
//	@Tags			bookmark
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Success		200			{object}	app.ResponseT[dto.ListBookmarkResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/bookmarks [GET]
func FindBookmarks(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.BookmarkQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}
	query.UserID = &user.ID
	query.IncludeDeleted = util.NewBool(false)

	resp := dto.ListBookmarkResponse{
		Results:    []*models.Bookmark{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	bookmarks, pagination, err := services.Bookmark.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = bookmarks
	resp.Pagination = pagination
	appG.Response200(resp)
}

// UpdateBookmark
//
//	@Summary		Update saving
//	@Description	Update saving
//
//	@Tags			bookmark
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string				true	"Origin"
//	@Param			X-referrer	header		string				true	"X-referrer"
//
//	@Param			id			path		string				true	"Bookmark ID"
//
//	@Param			Input		body		dto.BookmarkRequest	true	"Update bookmark input"
//
//	@Success		200			{object}	app.ResponseT[models.Bookmark]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		404			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/bookmarks/{id} [PUT]
func UpdateBookmark(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(util.IDParamKey)
	user := appG.GetLoggedUser()
	query := models.BookmarkQuery{
		ID:     &id,
		UserID: &user.ID, // Only bookmark's owner can view
	}
	bookmark, appErr := services.Bookmark.FindOne(&query, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if !user.CanUpdateBookmark(bookmark) {
		appG.Response403(e.Bookmark_need_permission, "You are not owner of bookmark")
		return
	}

	req := dto.BookmarkRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	bookmark, appErr = services.Bookmark.Update(bookmark, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(bookmark)
}
