package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/services"
)

// GetCryptoAccountInfo
// @Summary		Get crypto account info
// @Description	Get crypto account info
//
// @Tags		Chain
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Success		200				{object}	app.ResponseT[dto.GetAccountInfoResponse]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/chains/:network/accounts/:id [GET]
func GetCryptoAccountInfo(c *gin.Context) {
	appG := app.Gin{C: c}
	network := models.BlockchainNetwork(c.Param(models.PathParamKeyNetwork))
	address := c.Param(models.PathParamKeyID)
	isMainnet := c.Query("is_mainnet") == "true"

	resp, aErr := services.Chain.GetAccountInfo(&dto.GetAccountInfoRequest{
		Network:   network,
		Address:   address,
		IsMainnet: isMainnet,
	})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(resp)
}
