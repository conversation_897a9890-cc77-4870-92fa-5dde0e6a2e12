package v1

import (
	"openedu-core/dto"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Check limit resource usage
// @Description	Check limit resource usage
//
// @Tags		subscription
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Param		request		query		dto.CheckLimitResourceRequest	true	"request"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/internal-v1/usages/limit [GET]
func CheckLimitUsage(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.CheckLimitResourceRequest
	if err := appG.C.BindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if req.Type == "" {
		appG.Response400(e.INVALID_PARAMS, "type required")
		return
	}

	if req.UserID == "" {
		appG.Response400(e.INVALID_PARAMS, "user_id required")
		return
	}

	user, uErr := services.User.FindByID(req.UserID, nil)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	usage, err := services.ResourceUsage.CheckValidUsage(user, req.Type, req.AIModelID, req.OrgID)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(usage)
}

// @Summary		Add AI usage
// @Description	Add AI usage
//
// @Tags		subscription
// @Accept		json
// @Produce		json
//
// @Param		X-ai-api-key	header		string	true	"X-ai-api-key"
//
// @Param		input		body		dto.AddAiUsage	true	"input"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/usages/ai [POST]
func AddAiUsage(c *gin.Context) {
	appG := app.Gin{C: c}
	// Get and validate AI API key
	apiKey := c.GetHeader(util.AIApiKey)
	if apiKey == "" {
		log.Error("Missing AI API key")
		appG.Response401(e.UNAUTHORIZED, "Missing API key")
		return
	}

	// Validate the API key against the system's expected key
	if apiKey != setting.AppSetting.AIApiKey {
		log.Error("Invalid AI API key")
		appG.Response401(e.UNAUTHORIZED, "Invalid API key")
		return
	}

	data := dto.AddAiUsage{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	user, uErr := services.User.FindByID(data.UserID, nil)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	err := services.ResourceUsage.AddAiUsage(user, data.Amount, data.AIModelName, data.OrgID)
	if err != nil {
		log.ErrorWithAlertf("AddAiUsage::Error: %#v", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}
