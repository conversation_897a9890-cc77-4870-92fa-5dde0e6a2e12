package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Find Page invitation
// @Description	Find Page invitation
//
// @Tags			invitation
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/user-invitation [GET]
func FindPageInvitation(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.UserTokenQuery

	//bind filter
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if !currentUser.IsSysAdmin() && !currentUser.IsOrgAdmin(org.ID) {
		appG.Response403(e.FORBIDDEN, "You are not allowed to access this resource")
		return
	}

	if !(currentUser.IsSysAdmin() && setting.IsSysAdminSite(appG.GetDomain().Domain)) {
		query.OrgID = &org.ID
	}

	tokens, pagination, err := services.UserToken.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp := dto.ListUserTokenInvitationResponse{
		Results:    []*dto.UserTokenInvitationResponse{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	resp.Results = lo.Map(tokens, func(token *models.UserToken, _ int) *dto.UserTokenInvitationResponse {
		return &dto.UserTokenInvitationResponse{
			Model:      token.Model,
			User:       token.User,
			UserID:     token.UserID,
			OrgID:      token.OrgID,
			Email:      token.Email,
			Token:      token.Token,
			Event:      token.Event,
			IsExpired:  token.IsExpired(),
			IsVerified: token.IsVerified(),
		}
	})

	resp.Pagination = pagination
	appG.Response200(resp)

}

// @Summary		Resend invitation
// @Description	Resend invitation
//
// @Tags			invitation
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/user-invitation [PUT]
func ResendInvitation(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	if !currentUser.IsSysAdmin() && !currentUser.IsOrgAdmin(org.ID) {
		appG.Response403(e.FORBIDDEN, "You are not allowed to access this resource")
		return
	}

	var data dto.ResendMailInvitationRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	validateError := e.HandleValidationError(data)
	if len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return
	}

	if err := services.UserToken.ResendMail(&data, org); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("Resend invitation successfully")
}
