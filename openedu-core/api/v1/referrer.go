package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// Add Referrers
//
//	@Summary		Add Referrers
//	@Description	Add Referrers
//
//	@Tags			Referrers
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string									true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string									true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string									true	"Bearer"
//
//	@Param			body			body		dto.AffiliateCampaignReferrerRequest	true	"body"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/referrers [POST]
func AddReferrer(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.AffiliateCampaignReferrerRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}
	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(reqBody.CampaignID),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if perErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(appG.GetLoggedUser(), campaign); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	reqBody.Org = appG.GetOrg()
	referrers, refErr := services.Referrer.CreateOrUpdateMany(campaign, &reqBody)
	if refErr != nil {
		appG.ResponseAppError(refErr)
		return
	}
	appG.Response200(referrers)
	return
}

// Remove many Referrers
//
//	@Summary		Remove many Referrers
//	@Description	Remove many Referrers
//
//	@Tags			Referrers
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string						true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string						true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string						true	"Bearer"
//
//	@Param			query			query		dto.RemoveReferrerRequest	true	"query"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/referrers [DELETE]
func RemoveReferrer(c *gin.Context) {
	appG := app.Gin{C: c}
	var data dto.RemoveReferrerRequest
	if err := appG.C.ShouldBindQuery(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	validateError := e.HandleValidationError(data)
	if len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return
	}

	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(data.CampaignID),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if perErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(appG.GetLoggedUser(), campaign); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	refErr := services.Referrer.DeleteMany(&data)
	if refErr != nil {
		appG.ResponseAppError(refErr)
		return
	}
	appG.Response200("success")
	return
}

// Find Referrers
//
//	@Summary		Find Referrers
//	@Description	Find Referrers
//
//	@Tags			Referrers
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			query			query		models.ReferrerQuery	true	"query"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/referrers [GET]
func FindReferrerCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.ReferrerQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	campaign, cErr := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{ID: util.NewString(id)}, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if query.OrgID == nil {
		query.OrgID = util.NewString(appG.GetOrg().ID)
	}

	resp := dto.ListReferrerResponse{
		Results:    []*models.Referrer{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	query.CampaignID = util.NewString(campaign.ID)
	referrers, pagination, cErr := services.Referrer.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = referrers
	resp.Pagination = pagination
	appG.Response200(resp)
}
