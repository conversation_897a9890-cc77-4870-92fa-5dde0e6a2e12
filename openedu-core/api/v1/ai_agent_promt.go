package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// FindPageAIPrompt
//
// @Summary		Find list prompts with pagination
// @Description	Find list prompts with pagination
//
// @Tags		ai-prompt
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
// @Params		options 	query 		models.FindPageOptions 	false  	"Find page options"
// @Param		filter		query		models.AIPromptQuery		false	"Find Page prompts"
//
// @Success		200				{object}	app.ResponseT[dto.ListAIPromptResponse]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/ai/prompts [GET]
func FindPageAIPrompt(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.AIPromptQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	options.Sort = append(options.Sort, models.OrderASC, models.IdDESC)
	resp := dto.ListAIPromptResponse{
		Results:    []*models.AIPrompt{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	prompts, pagination, err := services.AIAgentPrompt.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = prompts
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		CreateAIPrompt
// @Description	CreateAIPrompt
//
// @Tags		ai-prompt
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referer		header		string					true	"X-referer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Param		Input			body		dto.AIPromptRequest	    true	"Create prompt input"
//
// @Success		201				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/ai/prompts [POST]
func CreateAIPrompt(c *gin.Context) {
	appG := app.Gin{C: c}
	userID := appG.GetLoggedUser().ID

	data := dto.AIPromptsRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("CreateAIModel::Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	aErr := services.AIAgentPrompt.CreateMany(&data, userID)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response201("success")
}
