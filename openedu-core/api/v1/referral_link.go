package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// Create referral link
//
//	@Summary		Create referral link
//	@Description	Create referral link
//
//	@Tags			Referral Link
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			body			body		dto.CreateReferralLink	true	"body"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/referral-links [POST]
func CreateReferralLink(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()
	var reqBody dto.CreateReferralLink
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	commission, err := services.Commission.FindOne(&models.CommissionQuery{
		ID:     util.NewString(reqBody.CommissionID),
		Enable: util.NewBool(true),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	link, refErr := services.ReferralLink.CreateReferralLinkByCommission(commission, user, org)
	if refErr != nil {
		appG.ResponseAppError(refErr)
		return
	}
	appG.Response200(link)
}

// Create extend referral link
//
//	@Summary		Create extend referral link
//	@Description	Create extend referral link
//
//	@Tags			Referral Link
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string	true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string	true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"id"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/referral-links/:id/extend [POST]
func CreateExtendReferralLink(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id") // link_id
	user := appG.GetLoggedUser()
	link, err := services.ReferralLink.FindOne(&models.ReferralLinkQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	extendLink, refErr := services.ReferralLink.CreateExtendReferral(user, link)
	if refErr != nil {
		appG.ResponseAppError(refErr)
		return
	}
	appG.Response200(extendLink)
}

// Edit referral link
//
//	@Summary		Edit referral link
//	@Description	Edit referral link
//
//	@Tags			Referral Link
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			id				path		string					true	"id"
//	@Param			body			body		dto.UpdateReferralLink	true	"body"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/referral-links/:id [PUT]
func EditReferralLink(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()

	var reqBody *dto.UpdateReferralLink
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	link, err := services.ReferralLink.FindOne(&models.ReferralLinkQuery{ID: util.NewString(id)}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	edited, eErr := services.ReferralLink.UserEditRefLink(user, link, reqBody)
	if eErr != nil {
		appG.ResponseAppError(eErr)
		return
	}

	appG.Response200(edited)
}

// ValidateReferralCode
// @Summary		Validate a referral code
// @Description	Validate a referral code
//
// @Tags		Order
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.ValidateReferralCodeRequest		true	"Validate referral code input"
//
// @Success		201			{object}	app.ResponseT[string]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/referral-links/by-code/:code/validate [POST]
func ValidateReferralCode(c *gin.Context) {
	appG := app.Gin{C: c}
	code := c.Param(models.PathParamKeyCode)
	req := dto.ValidateReferralCodeRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	req.ReferralCode = code
	if _, refCodeErr := services.ReferralLink.ValidateReferralCode(&req); refCodeErr != nil {
		appG.ResponseAppError(refCodeErr)
		return
	}

	appG.Response200("success")
}

// Edit referral link
//
//	@Summary		Edit referral link
//	@Description	Edit referral link
//
//	@Tags			Referral Link
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			code				path		string					true	"code"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/referral-code/by-code/:code [GET]
func GetReferralLinkByCode(c *gin.Context) {
	appG := app.Gin{C: c}
	code := c.Param("code")

	link, err := services.ReferralLink.FindOne(&models.ReferralLinkQuery{RefCode: util.NewString(code)}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(link)
}

// Get referral link by campaign
//
//	@Summary		Get referral link by campaign
//	@Description	Get referral link by campaign
//
//	@Tags			Referral Link
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string	true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string	true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"campaign id"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/affiliate-campaign/:id/links [PUT]
func GetReferralLinks(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")
	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	results, err := services.ReferralLink.GetLinkByUserAndCampaign(user, campaign)

	appG.Response200(results)
}
