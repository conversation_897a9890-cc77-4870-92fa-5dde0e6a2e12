package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// GetFormAnswerStatsByQuestion
//
//	@Summary		Get question summary
//	@Description	Get question summary
//
//	@Tags			question
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//
//	@Param			id				path		string	true	"Question ID"
//
//	@Success		200				{object}	app.ResponseT[dto.ListFormAnswerStatsResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/form-questions/{id}/answers [GET]
func GetFormAnswerStatsByQuestion(c *gin.Context) {
	appG := app.Gin{C: c}
	questionUID := c.Param(util.IDParamKey)
	question, appErr := services.FormQuestion.FindOne(&models.FormQuestionQuery{
		UID: &questionUID,
	}, &models.FindOneOptions{
		Preloads: []string{util.SubQuestionsField, util.OptionsField},
		Sort:     []string{models.CreateAtDESC},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if question.IsTypeMultipleChoiceGrid() || question.IsTypeSingleChoiceGrid() {
		appG.Response400(e.INVALID_PARAMS, "The question is grid type")
		return
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	options.Preloads = append(options.Preloads, util.FilesField)

	if len(options.Sort) == 0 {
		options.Sort = []string{"update_at DESC"}
	}

	var query models.FormAnswerStatsQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	query.QuestionUID = &questionUID

	listAnswerStats, pagination, appErr := services.FormAnswer.FindPageAnswerStats(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(dto.ListFormAnswerStatsResponse{
		Results: lo.Map(listAnswerStats, func(answerStats *models.FormAnswerStats, _ int) *models.SimpleFormAnswerStats {
			return answerStats.ToSimple()
		}),
		Pagination: pagination,
	})
}

// GetFormAnswerStatsBySubQuestion
//
//	@Summary		Get question summary
//	@Description	Get question summary
//
//	@Tags			question
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//
//	@Param			id				path		string	true	"Question ID"
//
//	@Success		200				{object}	app.ResponseT[dto.ListFormAnswerStatsResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/form-questions/{id}/sub-questions/{sub_id}/answers [GET]
func GetFormAnswerStatsBySubQuestion(c *gin.Context) {
	appG := app.Gin{C: c}
	questionUID := c.Param(util.IDParamKey)
	subQuestionUID := c.Param("sub_id")
	question, appErr := services.FormQuestion.FindOne(&models.FormQuestionQuery{
		UID: &questionUID,
	}, &models.FindOneOptions{
		Preloads: []string{util.SubQuestionsField, util.OptionsField},
		Sort:     []string{models.CreateAtDESC},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	_, found := lo.Find(question.SubQuestions, func(subQuestion *models.FormQuestion) bool {
		return subQuestion.ID == subQuestionUID
	})
	if !found {
		appG.Response404(e.Form_sub_question_not_found, "Sub question not found, ID: "+subQuestionUID)
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	options.Preloads = append(options.Preloads, util.FilesField)

	if len(options.Sort) == 0 {
		options.Sort = []string{"update_at DESC"}
	}

	var query models.FormAnswerStatsQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	query.QuestionUID = &questionUID
	query.SubQuestionUID = &questionUID

	listAnswerStats, pagination, appErr := services.FormAnswer.FindPageAnswerStats(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(dto.ListFormAnswerStatsResponse{
		Results: lo.Map(listAnswerStats, func(answerStats *models.FormAnswerStats, _ int) *models.SimpleFormAnswerStats {
			return answerStats.ToSimple()
		}),
		Pagination: pagination,
	})
}
