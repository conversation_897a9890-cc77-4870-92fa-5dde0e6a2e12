package v1

import (
	"context"
	"encoding/json"
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"openedu-core/services/sns"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// @Summary		Auth
// @Description	Auth
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string			true	"Origin"
// @Param			X-referer	header		string			true	"X-referer"
// @Param			body		body		dto.AuthRequest	true	"Register request body"
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth [POST]
func AuthSocial(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.AuthRequest{}

	if err := appG.BindAndValidateJSON(&req); err != nil {
		log.Error("Api::Auth.Register Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	// verify code
	snsData, err := sns.GetSNSInfo(req.Provider, req.Code, req.CodeVerifier, "")
	if err != nil {
		d := map[string]string{
			"provider": string(req.Provider),
			"code":     req.Code,
			"err":      err.Error(),
		}
		app.MarkErrors(&err)
		appG.Response400(e.Error_auth_verify_sns_token_failed, d)
		return
	}

	// Encode SNSdata into a JSON byte
	b, err := json.Marshal(snsData.Data)
	if err != nil {
		appG.Response500(e.Error_parse_sns_account_profile_failed, err.Error())
		return
	}

	// Decode the JSON data from byte slice into profile
	var profile models.JSONB
	err = json.Unmarshal(b, &profile)
	if err != nil {
		appG.Response500(e.Error_parse_sns_account_profile_failed, err.Error())
		return
	}

	snsAccountInput := &models.SnsAccount{
		AccountID:    snsData.SnsId,
		Username:     snsData.Username,
		DisplayName:  snsData.DisplayName,
		Email:        snsData.Email,
		Avatar:       snsData.Avatar,
		Provider:     snsData.Provider,
		AccessToken:  snsData.AccessToken,
		RefreshToken: snsData.RefreshToken,
		Profile:      profile,
	}

	snsAccount, err := services.SnsAccount.UpsertSnsAccount(snsAccountInput)
	if err != nil {
		log.Error("[services.SnsAccount.UpsertSnsAccount]", err)
		appG.Response500(e.Error_upsert_sns_account_failed, "Upsert sns account error "+err.Error())
		return
	}

	org := appG.GetOrg()
	user, err := services.Auth.SocialSignUp(snsAccount, org)
	if err != nil {
		log.Error("[services.Auth.SocialSignUp]", err)
		appG.Response500(e.Error_auth_check_and_create_user_failed, "Check and create new user error "+err.Error())
		return
	}

	authToken, aErr := services.Auth.Authentication(&dto.AuthParams{User: user, Org: org, OriginUrl: appG.GetDomain().Domain, OTPVerified: true})
	if aErr != nil {
		log.Error("[services.Auth.Authentication]", err)
		appG.ResponseAppError(aErr)
		return
	}

	go func() {
		if appErr := services.Wallet.InitUserWallets(org, user); appErr != nil {
			log.Errorf("Init user wallet error: %v", appErr)
		}
	}()

	if req.RefCode != "" {
		go func() {
			if refErr := services.OEReferral(context.Background()).RefNewUserProgram(req.RefCode, user); refErr != nil {
				log.ErrorWithAlertf("AuthService.Verify::RefNewUserProgram failed: %v", refErr)
			}
		}()
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.ErrorWithAlertf("Recovered in apiV1::AuthSocial", r)
			}
		}()

		if appErr := services.Subscription.HandleAutoSubscribeAIPlan(org, user); appErr != nil {
			log.ErrorWithAlertf("AuthSocial::HandleAutoSubscribeAIPlan error: %v", appErr)
		}
		//TODO: push notification when user register
		req := &communicationdto.PushNotificationRequest{
			Code: communicationdto.CodeUserRegister,
			RuleTree: &communicationdto.TreeNodeRequest{
				Rule: &communicationdto.Rule{
					Subject:   communicationdto.NotiUser,
					Verb:      communicationdto.IsIn,
					ObjectIDs: []string{user.ID},
				},
			},
			Org: org.IntoComm(),
		}
		if err := communication.Notification.PushNotification(req); err != nil {

			log.Errorf("Push notification after register error: %v", err)
		}

	}()

	appG.Response200(authToken)
}

// Register
// @Summary		Register
// @Description	Register
//
// @Tags		auth
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string				true	"Origin"
// @Param		X-referer	header		string				true	"X-referer"
// @Param		body		body		dto.RegisterRequest	true	"Register request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router		/api/v1/auth/register [POST]
func Register(c *gin.Context) {
	appG := app.Gin{C: c}

	var reqBody dto.RegisterRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	registerParams := &dto.RegisterParams{
		DisplayName:     reqBody.DisplayName,
		Email:           reqBody.Email,
		Password:        reqBody.Password,
		Org:             appG.GetOrg(),
		OriginUrl:       appG.GetDomain().Domain,
		AllowFieldsData: appG.GetAllowFieldsData(),
		RefCode:         reqBody.RefCode,
	}
	authToken, aErr := services.Auth.Register(registerParams)
	if aErr != nil {
		if aErr.ErrCode == e.Error_auth_existing_normalized_email && authToken != nil {
			appG.Response(aErr.StatusCode, aErr.ErrCode, dto.ExistingNormalizedEmailResponse{
				NormalizedEmail: authToken.NormalizedEmail,
				ConflictedEmail: authToken.ConflictedEmail,
				Message:         aErr.Msg,
			})
			return
		}
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(authToken)
}

// @Summary		Auth
// @Description	Auth
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string				true	"Origin"
// @Param			X-referer	header		string				true	"X-referer"
// @Param			body		body		dto.LoginRequest	true	"Login request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/login [POST]
func Login(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.LoginRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	user, rErr := services.Auth.ValidateUserByEmail(req.Email)
	if rErr != nil {
		log.Error("[services.Auth.ValidateUserByEmail]", rErr)
		log.Debug(rErr.Msg)
		appG.ResponseAppError(rErr)
		return
	}

	if err := user.ComparePassword(req.Password); err != nil {
		appG.Response401(e.Error_auth_invalid_credential, "Invalid credential")
		return
	}

	originURL := appG.GetDomain().Domain
	org := appG.GetOrg()
	otpVerified := true

	if setting.IsSysAdminSite(originURL) && models.GetConfig[bool](models.VerifyOTP) {
		otpVerified = false
	}

	authToken, rErr := services.Auth.Authentication(&dto.AuthParams{User: user, Org: org, UserAgent: appG.GetUA(), OriginUrl: originURL, OTPVerified: otpVerified})
	if rErr != nil {
		log.Error("[services.Auth.Authentication]", rErr)
		log.Debug(rErr.Msg)
		appG.ResponseAppError(rErr)
		return
	}

	go func() {
		if !otpVerified {
			err := services.Auth.RequestOTP(user, org)
			if err != nil {
				log.Error("[services.Auth.Authentication]", err)
			} else {
				log.Debug("[services.Auth.Authentication] Request OTP success")
			}
		}
	}()

	go func() {
		if appErr := services.Wallet.InitUserWallets(appG.GetOrg(), user); appErr != nil {
			log.Errorf("Init user wallet error: %v", appErr)
		}
	}()

	go func() {
		if appErr := services.Subscription.HandleAutoSubscribeAIPlan(org, user); appErr != nil {
			log.ErrorWithAlertf("Login::HandleAutoSubscribeAIPlan error: %v", appErr)
		}
	}()

	appG.Response200(authToken)
}

// @Summary		Link Sns account
// @Description	Link SNS account
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Authorization	header		string			true	"Bearer"
// @Param			Origin			header		string			true	"Origin"
// @Param			X-referer		header		string			true	"X-referer"
// @Param			body			body		dto.AuthRequest	true	"This is provider"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		401				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/auth/link [POST]
func LinkSnsAccount(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.AuthRequest{}

	// Check if the user is logged in
	user := appG.GetLoggedUser()
	if user == nil {
		appG.Response401(e.UNAUTHORIZED, nil)
		return
	}

	if err := appG.BindAndValidateJSON(&req); err != nil {
		log.Error("Api::Auth.Link Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	existed, err := services.User.CheckProviderExisted(req.Provider, user.ID)
	if err != nil {
		appG.Response500(e.ERROR, "Check provider existed failed with"+err.Error())
		return
	}

	if existed {
		appG.Response400(e.Error_auth_provider_already_exist_in_user, "")
		return
	}

	// verify code
	snsData, err := sns.GetSNSInfo(req.Provider, req.Code, req.CodeVerifier, "")
	if err != nil {
		d := map[string]string{
			"provider": string(req.Provider),
			"code":     req.Code,
			"err":      err.Error(),
		}
		app.MarkErrors(&err)
		appG.Response400(e.Error_auth_verify_sns_token_failed, d)
		return
	}

	// Encode SNSdata into a JSON byte
	b, err := json.Marshal(snsData.Data)
	if err != nil {
		appG.Response500(e.Error_parse_sns_account_profile_failed, err.Error())
		return
	}

	// Decode the JSON data from byte slice into profile
	var profile models.JSONB
	err = json.Unmarshal(b, &profile)
	if err != nil {
		appG.Response500(e.Error_parse_sns_account_profile_failed, err.Error())
		return
	}

	snsAccountInput := &models.SnsAccount{
		AccountID:    snsData.SnsId,
		Username:     snsData.Username,
		Email:        snsData.Email,
		Avatar:       snsData.Avatar,
		Provider:     snsData.Provider,
		AccessToken:  snsData.AccessToken,
		RefreshToken: snsData.RefreshToken,
		Profile:      profile,
	}

	snsAccount, err := services.SnsAccount.UpsertSnsAccount(snsAccountInput)
	if err != nil {
		appG.Response500(e.Error_upsert_sns_account_failed, "Upsert sns account error "+err.Error())
		return
	}

	if snsAccount.UserID == "" {
		snsAccount.UserID = user.ID
	}

	log.Debug("snsAccount.UserID", snsAccount.UserID)
	log.Debug("user.ID", user.ID)

	_, snsUErr := models.Repository.SnsAccount.Update(snsAccount, nil)
	if snsUErr != nil {
		appG.Response500(e.Error_update_sns_account_by_id_failed, "Update userID for sns account error "+snsUErr.Error())
		return
	}

	// update email user if email's user is fake email && email's sns is not fake email
	if strings.Contains(user.Email, setting.DefaultEmailDomain()) && !strings.Contains(snsAccount.Email, setting.DefaultEmailDomain()) {
		user.Email = snsAccount.Email
		_, err := models.Repository.User.Update(user)
		if err != nil {
			appG.Response500(e.Error_user_update_failed, "Update email for user error "+err.Error())
			return
		}
	}

	appG.Response200(snsAccount.ToSimple())
}

// @Summary		Refresh access token
// @Description	Refresh access token
//
// @Tags			auth
// @Accept			json
// @Produce		json
// @Param			Origin		header		string					true	"Origin"
// @Param			X-referer	header		string					true	"X-referer"
// @Param			body		body		dto.RefreshTokenRequest	true	"Refresh Token request body"
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		401			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/refresh-token [POST]
func RefreshToken(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.RefreshTokenRequest{}

	if err := appG.BindAndValidateJSON(&req); err != nil {
		log.Error("Api::Auth.RefreshToken Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if req.RefreshToken == "" {
		appG.Response400(e.Error_auth_refresh_token_required, "Missing refresh token!")
		return
	}

	session, err := models.Repository.Session.FindByToken(req.RefreshToken)
	if err != nil {
		appG.Response404(e.Error_find_session_failed, "Find session error "+err.Error())
		return
	}
	if session == nil {
		appG.Response404(e.Error_auth_session_not_found, "Session not found, Do login again!")
		return
	}

	authToken, code := services.Auth.RefreshToken(&dto.RefreshTokenParams{
		RefreshToken: req.RefreshToken,
		OriginUrl:    appG.GetDomain().Domain,
		Org:          appG.GetOrg(),
	}, session)
	if code != e.SUCCESS {
		appG.Response400(code, "Refresh token failed")
		return
	}

	appG.Response200(authToken)
}

func GuestApi(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "guest",
	})
}

func LearnerAPI(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "learner",
	})
}

func OrgModAPI(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "org mod",
	})
}

func OrgAdminAPI(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "org admin",
	})
}

func InstructorAPI(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "instructor",
	})
}

func SysAdminAPI(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "sys admin",
	})
}

func AdminAPI(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "admin",
	})
}

func ModAPI(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.Response(http.StatusOK, e.SUCCESS, map[string]string{
		"message": "mod",
	})
}

// @Summary		Resend Email
// @Description	Resend Email
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string					true	"Origin"
// @Param			X-referer	header		string					true	"X-referer"
// @Param			body		body		dto.ResendMailRequest	true	"Resend Mail request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/resend-mail [POST]
func ResendMail(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.ResendMailRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.Resend Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	// Call auth resend email service
	resendParams := &dto.ResendMailPrams{
		Event:           reqBody.Event,
		Email:           reqBody.Email,
		AllowFieldsData: appG.GetAllowFieldsData(),
	}

	if err := services.Auth.ResendMail(appG.GetOrg(), resendParams); err != nil {
		log.Error("[services.Auth.ResendEmail]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("Success")
}

// @Summary		Verify User
// @Description	Verify User
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string	true	"Origin"
// @Param			X-referer	header		string	true	"X-referer"
// @Param			token		query		string	true	"Verify User request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/verify [GET]
func Verify(c *gin.Context) {
	appG := app.Gin{C: c}
	token := c.Query("token")
	//org := appG.GetOrg()

	if token == "" {
		log.Error("Api::Auth.Verify Bind failed", e.MsgFlags[e.INVALID_PARAMS])
		appG.Response400(e.INVALID_PARAMS, e.MsgFlags[e.INVALID_PARAMS])
		return
	}

	// Call auth verify service
	verifyParams := &dto.VerifyUserParams{
		Token:     token,
		Org:       appG.GetOrg(),
		OriginUrl: appG.GetDomain().Domain,
	}

	authToken, user, rErr := services.Auth.Verify(verifyParams)
	if rErr != nil {
		log.Error("[services.Auth.Verify]", rErr)
		appG.ResponseAppError(rErr)
		return
	}

	//aigov := models.GetConfig[models.AiGovVn2025Campaign](models.AIGovVN2025Campaign)
	//if org.ID == aigov.OrgID {
	//	go func() {
	//		//SendEmail
	//		emailReq := &communicationdto.SendEmailRequest{
	//			User: user.IntoComm(),
	//			Org:  org.IntoComm(),
	//			Code: util.NewT(communicationdto.EmailCodeRegisterUser),
	//			ExtendDatas: communicationdto.MapEmailParams{
	//				"user_name":  user.Username,
	//				"user_email": user.Email,
	//			},
	
	//			IsQueue: true,
	//			From:    org.Settings.SenderEmail,
	//		}
	//		if _, err := communication.Email.SendEmail(emailReq); err != nil {
	//			log.ErrorWithAlertf("UserRegister::Send email code: %v failed: %v", communicationdto.EmailCodeRegisterUser, err)
	//		}
	//	}()
	//
	//}

	go func() {
		if appErr := services.Subscription.HandleAutoSubscribeAIPlan(appG.GetOrg(), user); appErr != nil {
			log.ErrorWithAlertf("Verify::HandleAutoSubscribeAIPlan error: %v", appErr)
		}
	}()

	appG.Response200(authToken)
}

// @Summary		Forgot Password
// @Description	Forgot Password
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string						true	"Origin"
// @Param			X-referer	header		string						true	"X-referer"
// @Param			body		body		dto.ForgotPasswordRequest	true	"Forgot Password request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/forgot-password [POST]
func ForgotPassword(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.ForgotPasswordRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.Resend Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := services.Auth.ForgotPassword(appG.GetOrg(), reqBody.Email, appG.GetAllowFieldsData()); err != nil {
		log.Error("[services.Auth.ForgotPassword]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("Success")
}

// @Summary		Reset Password
// @Description	Reset Password
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string							true	"Origin"
// @Param			X-referer	header		string							true	"X-referer"
// @Param			body		body		dto.ConfirmResetPasswordRequest	true	"Reset Password request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/reset-password [POST]
func ConfirmResetPassword(c *gin.Context) {
	appG := app.Gin{C: c}

	var reqBody dto.ConfirmResetPasswordRequest

	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.ConfirmResetPassword Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	// Call auth confirm reset service
	confirmParams := &dto.ConfirmResetParams{
		Email:     reqBody.Email,
		Password:  reqBody.Password,
		Token:     reqBody.Token,
		Org:       appG.GetOrg(),
		OriginUrl: appG.GetDomain().Domain,
	}

	authToken, err := services.Auth.ConfirmResetPassword(confirmParams)
	if err != nil {
		log.Error("[services.Auth.ForgotPassword]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(authToken)
}

// @Summary		External Register
// @Description	External Register
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string						true	"Origin"
// @Param			X-referer	header		string						true	"X-referer"
// @Param			body		body		dto.ExternalRegisterRequest	true	"External Register request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/external-register [POST]
func ExternalRegister(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.ExternalRegisterRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.ExternalRegister Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	// Call auth external register service
	externalRegisterParams := &dto.ExternalRegisterParams{
		Email:           reqBody.Email,
		AllowFieldsData: appG.GetAllowFieldsData(),
	}

	if err := services.Auth.ExternalRegister(appG.GetOrg(), externalRegisterParams); err != nil {
		log.Error("[services.Auth.ExternalRegister]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("Success")
}

// @Summary		Set Password
// @Description	Set Password
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string					true	"Origin"
// @Param			X-referer	header		string					true	"X-referer"
// @Param			body		body		dto.SetPasswordRequest	true	"Set Password request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/set-password [POST]
func SetPassword(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.SetPasswordRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.SetPassword Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	// Call auth confirm reset service
	setPasswordParams := &dto.SetPasswordParams{
		Email:     reqBody.Email,
		Password:  reqBody.Password,
		Token:     reqBody.Token,
		Org:       appG.GetOrg(),
		OriginUrl: appG.GetDomain().Domain,
	}

	authToken, fErr := services.Auth.SetPassword(setPasswordParams)
	if fErr != nil {
		log.Error("[services.Auth.SetPassword]", fErr)
		appG.ResponseAppError(fErr)
		return
	}

	appG.Response200(authToken)

}

// @Summary		Resend OTP
// @Description	Resend OTP
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string					true	"Origin"
// @Param			X-referer	header		string					true	"X-referer"
// @Param			body		body		dto.ResendOTPRequest	true	"Resend OTP request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		401			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/resend-otp [POST]
func ResendOTP(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.ResendOTPRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.ResendOTP Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	user, appErr := services.Auth.ValidateUserByEmail(reqBody.Email)
	if appErr != nil {
		log.Error("[services.Auth.ValidateUserByEmail]", appErr)
		log.Debug(appErr.Msg)
		appG.ResponseAppError(appErr)
		return
	}

	org := appG.GetOrg()
	appErr = services.Auth.RequestOTP(user, org)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("Success")
}

// @Summary		Verify OTP
// @Description	Verify OTP
//
// @Tags			auth
// @Accept			json
// @Produce		json
//
// @Param			Origin		header		string					true	"Origin"
// @Param			X-referer	header		string					true	"X-referer"
// @Param			body		body		dto.VerifyOTPrequest	true	"Verify OTP request body"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/auth/verify-otp [POST]
func VerifyOTP(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.VerifyOTPrequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::Auth.Verify OTP Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	user, rErr := services.Auth.ValidateUserByEmail(reqBody.Email)
	if rErr != nil {
		log.Error("[services.Auth.ValidateUserByEmail]", rErr)
		log.Debug(rErr.Msg)
		appG.ResponseAppError(rErr)
		return
	}

	timeVerify := int(time.Now().UnixMilli())
	userToken, rErr := services.UserToken.FindOne(
		&models.UserTokenQuery{UserID: &user.ID, Event: util.NewT(models.EventSendOTP)}, &models.FindOneOptions{},
	)
	if rErr != nil {
		log.Error("[services.UserToken.FindOne]", rErr)
		log.Debugf("Verify OTP failed: %s", rErr.Msg)
		appG.ResponseAppError(rErr)
		return
	}

	// OTP incorrect
	if userToken.OTP != reqBody.OTP {
		appG.Response400(e.Error_auth_otp_incorrect, "OTP incorrect")
		return
	}

	// OTP expired
	if userToken.OTPExpireAt < timeVerify {
		appG.Response400(e.Error_auth_otp_expired, "OTP expires before use")
		return
	}

	authToken, rErr := services.Auth.Authentication(&dto.AuthParams{User: user, Org: appG.GetOrg(), UserAgent: appG.GetUA(), OriginUrl: appG.GetDomain().Domain, OTPVerified: true})
	if rErr != nil {
		log.Error("[services.Auth.Authentication]", rErr)
		log.Debug(rErr.Msg)
		appG.ResponseAppError(rErr)
		return
	}

	appG.Response200(authToken)
}
