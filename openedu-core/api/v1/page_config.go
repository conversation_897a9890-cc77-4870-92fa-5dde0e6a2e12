package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

func CreatePageConfig(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.BulkCreatePageConfigRequest{}
	user := appG.GetLoggedUser()
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if !user.IsSysAdmin() {
		appG.Response400(e.System_admin_required, "sysadmin or required")
		return
	}

	pageConfig, aErr := services.PageConfig.CreateOrUpdateMany(&data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return

	}

	appG.Response200(pageConfig)
}

func DeletePageConfigById(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")
	if !user.IsSysAdmin() {
		appG.Response400(e.System_admin_required, "sysadmin required")
		return
	}

	oErr := services.PageConfig.Delete(&models.PageConfig{ID: id})
	if oErr != nil {
		log.Error("find payment method by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200("success")
}

func FindPagePageConfig(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}

	//bind filter
	query := models.PageConfigQuery{
		IncludeDeleted: util.NewBool(false),
	}
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListPageConfigResponse{
		Results:    []*models.PageConfig{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	pageConfigs, pagination, err := services.PageConfig.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = pageConfigs
	resp.Pagination = pagination
	appG.Response200(resp)
}
