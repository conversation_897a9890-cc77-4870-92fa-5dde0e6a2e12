package v1

import (
	"context"
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/helpers"
	"openedu-core/pkg/log"
	"openedu-core/pkg/openedu_chain"
	chaindto "openedu-core/pkg/openedu_chain/dto"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Create course
// @Description	Create course
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			Input			body		dto.CreateCourseRequest	true	"Create course input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses [POST]
func CreateCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CreateCourseRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	course, cErr := services.Course.Create(appG.GetOrg(), appG.GetLoggedUser(), &data, true)

	if cErr != nil {
		log.Error("Create course errors", cErr)
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(course.Sanitize())
}

// @Summary		Update course
// @Description	Update course
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			id				path		string					true	"course id"
//
// @Param			Input			body		dto.UpdateCourseRequest	true	"Update course input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id} [PUT]
func UpdateCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	user := appG.GetLoggedUser()
	data := dto.UpdateCourseRequest{}
	id := c.Param("id")
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	//if data.Status != models.CourseSTTDraft && data.Status != models.CourseSTTUnPublic {
	//	appG.Response400(e.INVALID_PARAMS, fmt.Sprintf("update status `%s` not allow", data.Status))
	//	return
	//}

	course, cErr := services.Course.FindById(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, appG.GetLoggedUser(), models.CoursePerUpdate); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}
	// Check course owner can edit course
	_, cErr = services.Course.Update(course, org, user, &data)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(course.Sanitize())
}

// @Summary		Find course by ID
// @Description	Find course by ID
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id} [GET]
func GetCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	course, cErr := services.Course.FindById(id, false, &models.FindOneOptions{
		Preloads: []string{
			models.MediasField,
			models.DocsField,
			models.PartnersField,
			models.CategoriesField,
			models.LevelsField,
			models.ReviewingField,
			models.PublishedField,
			models.AICourseField,
		},
	})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	appG.Response200(course.Sanitize())
}

// FindCourse
//
//	@Summary		FindPage course
//	@Description	FindPage course
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Params			Input																																																																																																														query					models.FindPageOptions		 false		"find page options"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/courses [GET]
func FindCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CourseQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if setting.IsCreatorSite(appG.GetDomain().Uri) {
		query.Partner = util.NewString(appG.GetLoggedUser().ID)
	}

	query.IncludeDeleted = util.NewBool(false)
	query.Latest = util.NewBool(true)
	query.OrgID = util.NewString(appG.GetOrg().ID)

	resp := dto.ListCourseResponse{
		Results:    []*models.SimpleCourse{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	//options.Preloads = []string{models.MediasField, models.PartnersField, models.CategoriesField, models.PreviousVersionField}
	courses, pagination, err := services.Course.FindPageByPartner(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(courses, func(c *models.Course, _ int) *models.SimpleCourse {
		return c.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// DeleteCourse
// @Summary		Delete course
// @Description	Delete course
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id} [DELETE]
func DeleteCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	course, cErr := services.Course.FindById(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, appG.GetLoggedUser(), models.CoursePerUpdate); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}
	if dErr := services.Course.Delete(course); dErr != nil {
		appG.ResponseAppError(dErr)
		return
	}
	appG.Response200("success")
}

// @Summary		Get course partners
// @Description	Get course partners
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/partners [GET]
func GetCoursePartners(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	id := c.Param("id")
	course, cErr := services.Course.FindById(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	query := models.CoursePartnerQuery{
		CourseID: util.NewString(course.Cuid),
	}

	options.Preloads = []string{"Partner"}
	resp := dto.ListCoursePartnerResponse{
		Results:    []*models.SimplePartner{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	partners, pagination, err := services.Course.FindCoursePartners(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = lo.Map(partners, func(p *models.CoursePartner, _ int) *models.SimplePartner { return p.ToSimplePartner() })
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Update course partners
// @Description	Update course partners
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			id				path		string							true	"course id"
// @Param			input			body		dto.UpdateCoursePartnerRequest	true	"partners"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/partners [PUT]
func UpdateCoursePartner(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	user := appG.GetLoggedUser()
	id := c.Param("id")
	data := dto.UpdateCoursePartnerRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if data.Partners == nil || len(data.Partners) <= 0 {
		appG.Response400(e.Update_course_partner_failed, "partners required")
		return
	}

	course, cErr := services.Course.FindById(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, appG.GetLoggedUser(), models.CoursePerUpdate); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	partners, uErr := services.Course.UpdateCoursePartners(course, org, user, data.Partners)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}
	pts := lo.Map(partners, func(p *models.CoursePartner, _ int) *models.SimplePartner { return p.ToSimplePartner() })
	appG.Response200(pts)
}

// @Summary		Delete course partners
// @Description	Delete course partners
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			id				path		string							true	"course id"
// @Param			input			body		dto.UpdateCoursePartnerRequest	true	"partners"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/partners [DELETE]
func DeleteCoursePartner(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	user := appG.GetLoggedUser()
	id := c.Param("id")
	data := dto.DeleteCoursePartnerRequest{}
	if err := appG.C.ShouldBindQuery(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	if data.UserIds == nil || len(data.UserIds) <= 0 {
		appG.Response400(e.Update_course_partner_failed, "user_ids to delete are required")
		return
	}

	course, cErr := services.Course.FindById(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, appG.GetLoggedUser(), models.CoursePerUpdate); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	uErr := services.Course.DeleteCoursePartners(course, org, user, data.UserIds)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}
	appG.Response200("success")
}

// GetCourseOutline
//
//	@Summary		Get course outline
//	@Description	Get course outline
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			slug			path		string	true	"Course slug"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/courses/{slug}/outline [GET]
func GetCourseOutline(c *gin.Context) {
	appG := app.Gin{C: c}
	slug := c.Param("id")
	user := appG.GetLoggedUser()
	org := appG.GetOrg()
	publishCourse, appErr := services.PublishCourse.FindOne(&models.PublishCourseQuery{
		CourseSlug: &slug,
	}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	var options models.FindOneOptions
	if err := appG.C.ShouldBindQuery(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find one options failed: "+err.Error())
		return
	}
	options.Preloads = append(options.Preloads, models.CertificateTemplateField)

	course, appErr := services.Course.FindByIdForOutline(publishCourse.CourseID, false, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	course.Org = org.ToSimple()

	if user == nil {
		appG.Response200(course)
		return
	}

	if appErr = services.Course.ExtendResponseForOutline(course, appG.GetLoggedUser()); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	go func() {
		services.Certificate.PushNotificationReceiveCertificate(course, user, org)
	}()

	if err := services.Course.ExtendOutlineLearningProgressOverview(user, course); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(course)
}

// GetCourseOutlinePreview
//
//	@Summary		Get course outline preview
//	@Description	Get course outline preview by admdin
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			slug			path		string	true	"Course slug"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/courses/:id/preview/:org_id [GET]
func GetCourseOutlinePreview(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	orgId := c.Param("org_id")

	if orgId == "" {
		appG.Response400(e.INVALID_PARAMS, "Missing org_id")
		return
	}
	org, oErr := services.Organization.FindByID(orgId, false, nil)
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	var options models.FindOneOptions
	if err := appG.C.ShouldBindQuery(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find one options failed: "+err.Error())
		return
	}

	course, appErr := services.Course.FindOne(&models.CourseQuery{
		ID: util.NewString(id),
	}, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	course.Org = org.ToSimple()
	course, appErr = services.Course.GetOutline(course)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if appErr = services.Course.ExtendResponseForOutline(course, appG.GetLoggedUser()); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(course)
}

// @Summary		Request to publish course
// @Description	Request to publish course
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string						true	"Origin"
// @Param		X-referer		header		string						true	"X-referer"
// @Param		Authorization	header		string						true	"Bearer"
//
// @Param		id				path		string						true	"course id"
// @Param		Input			body		dto.PublishCourseRequest	true	"Approve approval input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/publish [POST]
func PublishCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")

	course, cErr := services.Course.FindById(id, false, &models.FindOneOptions{
		Preloads: []string{
			models.MediasField,
			models.DocsField,
			models.PartnersField,
			models.CategoriesField,
			models.LevelsField,
			models.ReviewingField,
			models.PublishedField,
			models.FormRelationsField,
			models.AICourseField,
		},
	})
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, user, models.CoursePerPublish); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	req := dto.PublishCourseRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if !lo.Contains([]string{string(models.CourseSTTPublic), string(models.CourseSTTPublicRoot)}, string(req.Status)) {
		appG.Response400(e.Course_invalid_publish_status, fmt.Sprintf("status %s invalid", req.Status))
		return
	}

	if req.Status == course.Status {
		appG.Response400(e.Course_status_already_existed, fmt.Sprintf("course status %s already updated", req.Status))
		return
	}

	if req.Status == models.CourseSTTReviewing {
		appG.Response400(e.Course_status_reviewing, "course reviewing")
		return
	}

	req.Course = course
	req.Org = appG.GetOrg()
	req.Requester = user
	if newCourse, reqErr := services.Course.RequestPublishCourse(&req); reqErr != nil {
		appG.ResponseAppError(reqErr)
		return
	} else {
		appG.Response200(newCourse.Sanitize())
		return
	}
}

// @Summary		Cancel publish course request
// @Description	Cancel publish course request
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referrer		header		string	true	"X-referrer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/publish [PUT]
func CancelPublishCourseRequest(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")

	course, cErr := services.Course.FindById(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, user, models.CoursePerCancel); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	if ccErr := services.Course.CancelRequest(course); ccErr != nil {
		appG.ResponseAppError(ccErr)
		return
	}
	appG.Response200("success")
}

// @Summary		Cancel publish course request
// @Description	Cancel publish course request
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referrer		header		string	true	"X-referrer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course id"
// @Param			input				body		dto.ApprovalFeedback	true	"reply feedback body"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/:id/reply-feedback [PUT]
func ReplyFeedbackPublishCourseRequest(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	id := c.Param("id")

	course, cErr := services.Course.FindById(id, false, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, currentUser, models.CoursePerCancel); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	data := dto.ApprovalFeedback{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	if data.ID == nil {
		appG.Response400(e.INVALID_PARAMS, "approval `ID` required")
		return
	}
	// Get Approval
	approval, aErr := services.Approval.FindOne(&models.ApprovalQuery{ID: data.ID}, &models.FindOneOptions{})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	if approval.Status == models.ApprovalStatusApprove {
		appG.Response400(e.Approval_approved, "request approved")
		return
	}

	if approval.Status == models.ApprovalStatusCancel {
		appG.Response400(e.Approval_cancelled, "request cancelled")
		return
	}

	// Handle approval
	data.IsAdminFeedback = false
	data.User = currentUser
	if newCourse, handleErr := services.Course.ReplyFeedbackPublishCourse(approval, course, &data); handleErr != nil {
		appG.ResponseAppError(handleErr)
		return
	} else {
		appG.Response200(newCourse)
		return
	}
}

// UnPublishCourse
// @Summary		Request to un-publish course
// @Description	Request to un-publish course
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referer		header		string	true	"X-referer"
// @Param		Authorization	header		string	true	"Bearer"
//
// @Param		id				path		string	true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/publish [DELETE]
func UnPublishCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id") // course cuid

	course, err := services.Course.FindOne(&models.CourseQuery{
		Cuid:   util.NewString(id),
		Latest: util.NewBool(true),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, appG.GetLoggedUser(), models.CoursePerCancel); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	if uErr := services.Course.UnPublishCourse(user, id, models.UnPublishCourseTargetAll); uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200("ok")
}

// @Summary		Get Publish courses
// @Description	Get Publish courses
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referer		header		string	true	"X-referer"
// @Param		Authorization	header		string	true	"Bearer"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/publish [GET]
func GetPublishCourses(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.PublishCourseQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	resp := dto.ListCourseItemsResponse{
		Results:    []*models.CourseListItem{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	courses, pagination, err := services.Course.FindCourseListItems(appG.GetLoggedUser(), appG.GetOrg(), &query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.If(courses != nil, courses).Else([]*models.CourseListItem{})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Get Course history versions
// @Description	Get Course history versions
//
// @Tags			course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/:id/histories [GET]
func GetCourseVersionHistory(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.CourseQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	query.Cuid = util.NewString(id)
	query.OrgID = util.NewString(appG.GetOrg().ID)

	resp := dto.ListCourseResponse{
		Results:    []*models.SimpleCourse{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	courses, pagination, err := services.Course.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(courses, func(c *models.Course, _ int) *models.SimpleCourse {
		return c.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// ChangeCourseStage
//
//	@Summary		Update course stage
//	@Description	Update course stage
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string							true	"Origin"
//	@Param			X-referer		header		string							true	"X-referer"
//	@Param			Authorization	header		string							true	"Bearer"
//
//	@Param			id				path		string							true	"course id"
//	@Param			input			body		dto.ChangeCourseStageRequest	true	"update course stager"
//
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//	@Router			/api/v1/courses/:id/stage [PUT]
func ChangeCourseStage(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()

	data := dto.ChangeCourseStageRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	vaErr := util.Validator.Struct(data)
	if vaErr != nil {
		log.Error("validation failed", vaErr)
		appG.Response400(e.INVALID_PARAMS, vaErr.Error())
		return
	}

	course, err := services.Course.FindOne(&models.CourseQuery{
		ID:   util.NewString(id),
		Cuid: util.NewString(data.Cuid),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if perErr := services.Course.CanUpdateCourse(course, user, models.CoursePerEnable); perErr != nil {
		appG.ResponseAppError(perErr)
		return
	}

	data.User = user
	if uErr := services.PublishCourse.ChangeStage(course, &data); uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}
	appG.Response200("success")
}

// FindPageFormRelationsByCourse
//
//	@Summary		Get form relations by course
//	@Description	Get form relations by course
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//
//	@Param			pagination	query		models.FindPageOptions	true	"Pagination query"
//	@Param			id			path		string					true	"Course ID"
//
//	@Success		200			{object}	app.ResponseT[dto.ListFormRelationResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/courses/{id}/form-relations [GET]
func FindPageFormRelationsByCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	courseID := c.Param("id")

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	options.Preloads = []string{models.FormField, models.QuestionsFormRelationField}

	query := models.FormRelationQuery{
		RelatedEntityID:   &courseID,
		RelatedEntityType: util.NewT(models.CourseModelName),
	}

	formRelations, pagination, appErr := services.FormRelation.FindPage(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	resp := dto.ListFormRelationResponse{
		Results:    lo.If(len(formRelations) == 0, []*models.FormRelation{}).Else(formRelations),
		Pagination: pagination,
	}
	appG.Response200(resp)
}

// GetMyCourses
//
//	@Summary		Get my courses by group
//	@Description	Get my courses by group
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string						true	"Origin"
//	@Param			X-referrer	header		string						true	"X-referrer"
//
//	@Param			filters		query		dto.GetMyCoursesRequest		true	"Filter query"
//	@Param			pagination	query		models.FindPageOptions		true	"Pagination query"
//
//	@Success		200			{object}	app.ResponseT[dto.ListCourseResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/users/me/courses [GET]
func GetMyCourses(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	if len(options.Sort) > 0 {
		options.Sort = []string{models.UpdateAtDESC}
	}

	var req dto.GetMyCoursesRequest
	if err := appG.C.ShouldBindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	req.Org = appG.GetOrg()
	req.User = appG.GetLoggedUser()

	resp := dto.ListCourseResponse{
		Results:    []*models.SimpleCourse{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	courses, pagination, appErr := services.Course.FindPageCoursesByUser(
		&req,
		&options,
	)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	resp.Results = lo.Map(courses, func(c *models.Course, _ int) *models.SimpleCourse {
		return c.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// CountMyCourses
//
//	@Summary		Get my courses by group
//	@Description	Get my courses by group
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			filters		query		dto.GetMyCoursesRequest	true	"Filter query"
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Success		200			{object}	app.ResponseT[dto.CountMyCoursesResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/users/me/courses/count [GET]
func CountMyCourses(c *gin.Context) {
	appG := app.Gin{C: c}

	var req dto.GetMyCoursesRequest
	if err := appG.C.ShouldBindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	var inProgressCount int64
	var completedCount int64
	var notStartedCount int64
	var wishlistCount int64

	errChan := make(chan *e.AppError, 1)
	go func() {
		count, appErr := services.Course.CountCoursesByUser(&dto.CountMyCoursesRequest{
			User:                    appG.GetLoggedUser(),
			Org:                     appG.GetOrg(),
			Group:                   models.CourseGroupInProgress,
			ExcludeUnPublishCourses: req.ExcludeUnPublishCourses,
		})
		errChan <- appErr
		inProgressCount = count
	}()

	go func() {
		count, appErr := services.Course.CountCoursesByUser(&dto.CountMyCoursesRequest{
			User:                    appG.GetLoggedUser(),
			Org:                     appG.GetOrg(),
			Group:                   models.CourseGroupCompleted,
			ExcludeUnPublishCourses: req.ExcludeUnPublishCourses,
		})
		errChan <- appErr
		completedCount = count
	}()

	go func() {
		count, appErr := services.Course.CountCoursesByUser(&dto.CountMyCoursesRequest{
			User:                    appG.GetLoggedUser(),
			Org:                     appG.GetOrg(),
			Group:                   models.CourseGroupNotStarted,
			ExcludeUnPublishCourses: req.ExcludeUnPublishCourses,
		})
		errChan <- appErr
		notStartedCount = count
	}()

	go func() {
		count, appErr := services.Course.CountCoursesByUser(&dto.CountMyCoursesRequest{
			User:                    appG.GetLoggedUser(),
			Org:                     appG.GetOrg(),
			Group:                   models.CourseGroupWishlist,
			ExcludeUnPublishCourses: req.ExcludeUnPublishCourses,
		})
		errChan <- appErr
		wishlistCount = count
	}()

	for i := 1; i <= 4; i++ {
		select {
		case appErr := <-errChan:
			if appErr != nil {
				appG.ResponseAppError(appErr)
				return
			}
		}
	}

	appG.Response200(&dto.CountMyCoursesResponse{
		InProgress: inProgressCount,
		Completed:  completedCount,
		NotStarted: notStartedCount,
		Wishlist:   wishlistCount,
	})
}

// FindPublishCourses
//
//	@Summary		Find publish courses
//	@Description	Find publish courses
//
//	@Tags			course
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Param			pagination	query		models.PublishCourseQuery	true	"Pagination query"
//
//	@Success		200			{object}	app.ResponseT[dto.ListCourseResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/publish-courses [GET]
func FindPublishCourses(c *gin.Context) {
	appG := app.Gin{C: c}
	domain := appG.GetDomain()
	org := appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	if len(options.Sort) > 0 {
		options.Sort = []string{models.CreateAtDESC}
	}

	var req models.PublishCourseQuery
	if err := appG.C.ShouldBindQuery(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if !setting.IsBaseDomain(domain.Domain) {
		req.OrgID = util.NewString(org.ID)
	}
	resp := dto.ListPublishCourseResponse{
		Results:    []*models.PublishCourse{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, models.TotalUserEnrollmentsField)
	req.Scope = util.NewT(models.ScopeAll)
	pubCourses, pagination, appErr := services.PublishCourse.FindPagePublishCourse(&req, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	resp.Results = pubCourses
	resp.Pagination = pagination
	appG.Response200(resp)
}

// GetCertificateLayerForCourse
//
// @Summary		Get Certificate Layer For Course
// @Description	Get Certificate Layer For Course
//
// @Tags		certificate
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/certificates [GET]
func GetCertificateLayerForCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	course, appErr := services.Course.FindById(id, false, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if appErr != nil {
		log.Error("find course by ID for certificate failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	template, appErr := services.HtmlTemplate.GetCertificateLayerForCourse(course)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if user := appG.GetLoggedUser(); user != nil {
		if user.DisplayName != "" {
			template.LearnerName = user.DisplayName
		} else {
			template.LearnerName = user.Username
		}
	}

	issueDate := time.Now().UnixMilli()
	template.IssueDate = int(issueDate)
	appG.Response200(template)
}

// EnableCertificateForCourse
//
// @Summary		Enable Certificate For Course
// @Description	Enable Certificate For Course
//
// @Tags		certificate
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/html-template/{template_id} [PUT]
func EnableCertificateForCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	templateID := c.Param("template_id")

	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	course, appErr := services.Course.FindById(id, false, nil)
	if appErr != nil {
		log.Error("find course by ID for certificate failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	data := dto.EnableCertificateRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("EnableCertificateForCourse::Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	// validate template id input
	template, appErr := services.HtmlTemplate.FindByID(templateID)
	if appErr != nil {
		log.Error("find html template by ID failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if user.IsCreator(org.ID) && !user.IsSysAdmin() {
		if perErr := services.Course.CanUpdateCourse(course, user, models.CoursePerUpdate); perErr != nil {
			appG.ResponseAppError(perErr)
			return
		}
	}

	appErr = services.HtmlTemplate.EnableCertificateLayer(template, course, user.ID, org.ID, &data)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// DuplicateCourse
//
// @Summary		Duplicate course
// @Description	Duplicate course
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/duplicate [POST]
func DuplicateCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	course, appErr := services.Course.FindById(id, false, &models.FindOneOptions{
		Preloads: []string{models.CategoriesField, models.LevelsField, models.FormRelationsField, models.DocsField},
	})
	if appErr != nil {
		log.Error("find course by ID for certificate failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	newCourse, err := services.Course.DuplicateCourse(org, course, user)
	if err != nil {
		log.Error("services.Course.DuplicateCourse failed", err)
		appG.ResponseAppError(err)
		return
	}
	appG.Response200(newCourse)
}

// DepositSponsorGasToCourseForMintNFTs
//
// @Summary		Deposit sponsor gas fee for course to support learners mint NFTs fee
// @Description	Deposit sponsor gas fee for course to support learners mint NFTs fee
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/nft/deposit-gas-fee [POST]
func DepositSponsorGasToCourseForMintNFTs(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	user := appG.GetLoggedUser()
	id := c.Param(models.PathParamKeyID)

	var req dto.DepositSponsorGasFeeRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	course, appErr := services.Course.FindById(id, false, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if course.Owner.ID != user.ID {
		appG.Response400(e.Not_course_owner, "Only the course owner can deposit and withdraw sponsor gas")
		return
	}

	network := course.CertMintNFTNetwork()

	// Set appropriate currency based on network
	currency := models.CryptoCurrencyNEAR
	if network == models.BlockchainNetworkBASE {
		currency = models.CryptoCurrencyETH
	}

	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{
		UserID:   &user.ID,
		Currency: util.NewT(currency),
		Network:  util.NewT(network),
	}, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	req.Course = course
	req.Wallet = wallet
	req.Org = org
	transaction, appErr := services.Transaction.DepositNftSponsorGasFee(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(transaction)
}

// WithdrawSponsorNftGasOfCourse
//
// @Summary		Withdraw sponsor NFT gas fee of course
// @Description	Withdraw sponsor NFT gas fee of course
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/nft/deposit-gas-fee [POST]
func WithdrawSponsorNftGasOfCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	user := appG.GetLoggedUser()
	id := c.Param(models.PathParamKeyID)

	var req dto.WithdrawSponsorGasFeeRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	course, appErr := services.Course.FindById(id, false, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if course.Owner.ID != user.ID {
		appG.Response400(e.Not_course_owner, "Only the course owner can deposit and withdraw sponsor gas")
		return
	}

	network := course.CertMintNFTNetwork()

	// Set appropriate currency based on network
	currency := helpers.GetCurrencyByNetwork(network)

	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{
		UserID:   &user.ID,
		Currency: util.NewT(currency),
		Network:  util.NewT(network),
	}, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	req.Course = course
	req.Wallet = wallet
	req.Org = org
	transaction, appErr := services.Transaction.WithdrawNftSponsorGasFee(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(transaction)
}

// GetEstimatedFeePerMintNFT
//
// @Summary		Get estimated fee per mint NFT
// @Description	Get estimated fee per mint NFT
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"Course ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/nft/fees [GET]
func GetEstimatedFeePerMintNFT(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param(models.PathParamKeyID)
	course, err := models.Repository.Course(context.TODO()).FindOne(&models.CourseQuery{
		ID: &id,
	}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if err != nil {
		appG.ResponseAppError(e.NewError500(e.Course_find_one_failed,
			"Find the course ID "+id+" error: "+err.Error()))
		return
	}

	network := course.CertMintNFTNetwork()

	// Set appropriate currency and fee based on network
	currency := helpers.GetCurrencyByNetwork(network)
	estimatedFee := helpers.GetEstimatedFeeByNetwork(network)

	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{
		UserID:   &course.Owner.ID,
		Currency: util.NewT(currency),
		Network:  util.NewT(network),
	}, &models.FindOneOptions{})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	sponsorBalance, bErr := openedu_chain.Wallet.GetGasSponsorBalance(&chaindto.GetWalletGasSponsorBalanceRequest{
		WalletID:   wallet.BlockchainWalletID,
		CourseCuid: course.Cuid,
		IsMainnet:  setting.OpenEduChainSetting.IsMainnet,
	})
	if bErr != nil {
		appG.ResponseAppError(e.NewError500(e.Course_find_one_failed, "Get sponsor sponsorBalance error: "+bErr.Error()))
		return
	}

	appG.Response200(dto.FeePerMintNFTResponse{
		SponsorBalance: sponsorBalance,
		EstimatedFee:   estimatedFee,
		Currency:       currency,
		Network:        network,
	})
}

// GetCourseRevenueDetailReport
//
// @Summary		Get course revenue detail report
// @Description	Get course revenue detail report
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		filter		query		models.CourseRevenueSummaryQuery	true	"query"
// @Param		pagination	query		models.FindPageOptions				true	"Pagination query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/revenue-detail-reports [GET]
func GetCourseRevenueDetailReport(c *gin.Context) {
	appG := app.Gin{C: c}

	user := appG.GetLoggedUser()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var req models.CourseRevenueDetailQuery
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	report, pagination, err := services.Course.GetCourseRevenueDetailReport(&req, user, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(dto.CourseRevenueReportResp{Results: report, Pagination: pagination})
}

// GetCourseRevenueReport
//
// @Summary		Get course revenue summary report
// @Description	Get course revenue summary report
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		filter	query	models.CourseRevenueSummaryQuery true "query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/revenue-summary-reports [GET]
func GetCourseRevenueSummaryReport(c *gin.Context) {
	appG := app.Gin{C: c}

	user := appG.GetLoggedUser()

	var req models.CourseRevenueSummaryQuery
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	report, err := services.Course.GetCourseRevenueSummaryReport(&req, user)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(report)
}

// GetRevenueGraphReport
//
// @Summary		Get course revenue graph report
// @Description	Get course revenue graph report
//
// @Tags		course
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		filter	query	dto.CourseRevenueGraphReq true "query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/revenue-graph-reports [GET]
func GetRevenueGraphReport(c *gin.Context) {
	appG := app.Gin{C: c}

	user := appG.GetLoggedUser()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var req dto.CourseRevenueGraphReq
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	points, pagination, err := services.Course.GetCourseRevenueGraph(&req, user, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(dto.CourseRevenueGraphResp{
		DataPoints: points,
		Pagination: pagination,
	})
}
