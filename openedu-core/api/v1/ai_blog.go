package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// GenBlogByAI
// @Summary		Generate a new blog by AI
// @Description	Generate a new blog by AI
//
// @Tags		Blog
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.AIBlogRequest	true	"AI generate blog input"
//
// @Success		201			{object}	app.Response
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/blogs/ai [POST]
func GenBlogByAI(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	data := dto.AIBlogRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := e.HandleValidationError(data); len(err) > 0 {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err)
		return
	}

	// Handle
	switch data.AIBlogRequestType {
	case models.AIBlogGenerateBlog:
		lo.ForEach(data.Blogs, func(blog dto.CreateBlogByAI, _ int) {
			if blog.BlogType == models.BlogTypeOrg && !currentUser.CanActBlog(org.ID) {
				appG.Response400(e.Blog_update_permission_required, "You must have permission and is on admin site")
				return
			}
		})
		if err := services.AIBlogRewrite.OfferHandleCreateBlogByAI(data.Blogs, org, currentUser, data.Locale, data.Tone); err != nil {
			appG.ResponseAppError(err)
			return
		} else {
			appG.Response200("success")
			return
		}
	case models.AIBlogRewriteParagraph:
		if data, err := services.AIBlogRewrite.OfferRewriteParagraph(data.Text, data.BlogCuid, org, currentUser); err != nil {
			appG.ResponseAppError(err)
			return
		} else {
			appG.Response200(data)
			return
		}
	case models.AIBlogRewriteFromLink:
		if data, err := services.AIBlogRewrite.OfferRewriteBlogFromLink(data.Link, data.Locale, data.BlogCuid, org, currentUser, data.Tone); err != nil {
			appG.ResponseAppError(err)
			return
		} else {
			appG.Response200(data)
			return
		}

	default:
		appG.Response400(e.INVALID_PARAMS, "Invalid AI blog request type")
	}

}

// @Summary		Get Rewrite Blog Data
// @Description	Get Rewrite Blog Data
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referrer		header		string	true	"X-referrer"
//
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"rewrite id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/blogs/ai/:id/rewrite [GET]
func GetRewriteBlogData(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	// org := appG.GetOrg()
	id := c.Param(util.IDParamKey)

	content, aErr := services.AIBlogRewrite.GetRewriteBlogData(id, currentUser)
	if aErr != nil {
		log.Error("[services.AIBlogRewrite.GetRewriteParagraph failed]", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(content)
}
