package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Create Subscription
// @Description	Create Subscription
//
// @Tags			Subscription
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.SubscriptionRequest	true	"create Subscription"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/subscriptions [POST]
func CreateSubscription(c *gin.Context) {
	appG := app.Gin{C: c}
	//data := dto.SubscriptionRequest{}
	//if err := appG.BindAndValidateJSON(&data); err != nil {
	//	log.Error("Bind JSON failed", err)
	//	appG.Response400(e.INVALID_PARAMS, err.Error())
	//	return
	//}
	//
	//plan, aErr := services.Subscription.Create(&data)
	//if aErr != nil {
	//	appG.ResponseAppError(aErr)
	//	return
	//}
	//
	//appG.Response200(plan)
	appG.Response200("success")
}

// @Summary		Update Subscription
// @Description	Update Subscription
//
// @Tags			Subscription
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"plan_id"
//
// @Param			Input			body		dto.SubscriptionRequest	true	"Update Subscription input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/subscriptions/{id} [PUT]
func UpdateSubscription(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	data := dto.SubscriptionRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	subscription, err := services.Subscription.FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	updated, aErr := services.Subscription.Update(subscription, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(updated)
}

// @Summary		Get Subscription
// @Description	Get Subscription
//
// @Tags			Subscription
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			id				path		string						true	"plan id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/subscriptions/{id} [GET]
func GetSubscription(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	var options models.FindOneOptions
	if err := appG.C.BindQuery(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind FindOneOptions failed: "+err.Error())
		return
	}
	subscription, err := services.Subscription.FindById(id, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(subscription)
}

// @Summary		Find Subscription
// @Description	Find Subscription
//
// @Tags			Subscription
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Query			query		models.SubscriptionQuery	true	"Find Subscription"
//
//	@Param			pagination	query		models.FindPageOptions	true	"Pagination query"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/subscriptions [GET]
func FindSubscription(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.SubscriptionQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListSubscriptionResponse{
		Results:    []*models.Subscription{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	subscriptions, pagination, err := services.Subscription.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = subscriptions
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Get current subscription info
// @Description	Get current subscription info
//
// @Tags			User
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/subscriptions [GET]
func GetCurrentSubscription(c *gin.Context) {
	appG := app.Gin{C: c}

	user := appG.GetLoggedUser()
	orgID := appG.GetOrg().ID

	subscription, err := services.Subscription.GetCurrentSubscriptionInfo(user, orgID)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(subscription)
}

// @Summary		Get current subscription info
// @Description	Get current subscription info
//
// @Tags			User
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/subscriptions [GET]
func SubscribePlanForUsers(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	org := appG.GetOrg()

	data := dto.SubscribeUsersRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	plan, err := services.PricingPlan.FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if len(data.UserIDs) > 0 {
		users, appErr := services.User.FindMany(&models.UserQuery{
			IDIn: &data.UserIDs,
		}, nil)
		if appErr != nil {
			appG.ResponseAppError(appErr)
			return
		}

		for _, user := range users {
			_, aErr := services.Subscription.Subscribe(&dto.SubscribePlanRequest{
				User:        user,
				Plan:        plan,
				SubType:     models.SubscriptionManual,
				Event:       data.Event,
				EnableEmail: data.EnableEmail,
				Org:         org,
			})
			if aErr != nil {
				appG.ResponseAppError(aErr)
				return
			}
		}
	}

	appG.Response200("success")
}
