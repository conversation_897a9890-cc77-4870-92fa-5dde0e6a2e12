package v1

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// @Summary		Find OE Referral leader board
// @Description	Find OE Referral leader board
//
// @Tags			OE Referral
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			query		models.OEReferralLeaderBoardQuery	true	"query"
// @Param			ID			param		string	true	"param"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/oe-referrals/:id/leader-boards [GET]
func FindOELeaderBoardByCampaign(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := c.Request.Context()
	id := c.Param("id")
	var query models.OEReferralLeaderBoardQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	query.CampaignKey = util.NewString(id)
	query.DisplayNameNe = util.NewString(util.ProvinceOther)
	options.Sort = []string{"register_count desc", "cert_count desc", "display_name asc"}
	resp := dto.ListOEReferralLeaderBoardResponse{
		Results:    []*models.OEReferralLeaderBoard{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	leaderBoards, pagination, listErr := services.OEReferralLeaderBoard(ctx).FindPage(&query, &options)
	if listErr != nil {
		appG.ResponseAppError(listErr)
		return
	}

	resp.Results = leaderBoards
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find OE Referral statictis
// @Description	Find OE Referral statictis
//
// @Tags			OE Referral
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			ID			param		string	true	"param"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/oe-referrals/statistics/:id [GET]
func OEReferralStatistic(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := c.Request.Context()
	id := c.Param("id")
	var query models.OEReferralLeaderBoardQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if query.LocalLevel == nil {
		query.LocalLevel = util.NewT(models.LocalLevelProvince)
	}

	statistics, err := services.OEReferralLeaderBoard(ctx).OEReferralStatistic(id, &query)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(statistics)
}

// @Summary		Find OE Referral statictis
// @Description	Find OE Referral statictis
//
// @Tags			OE Referral
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			ID			param		string	true	"param"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/oe-referrals/reports/:id/provinces [GET]
func OEReferralListProvinces(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := c.Request.Context()
	id := c.Param("id")

	var query models.OEReferralLeaderBoardQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	var options models.FindManyOptions
	if err := appG.BindAndValidateJSON(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	if query.LocalLevel == nil {
		query.LocalLevel = util.NewT(models.LocalLevelProvince)
	}

	results, err := services.OEReferralLeaderBoard(ctx).ListLeaderBoardByCampaignAndLevel(id, &query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200(results)
}

// @Summary		Create user with roles
// @Description	Create user with roles
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/admin/users/create-accounts [POST]
func AddLeaderBoardRecord(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.AdminCreateUserWithRole
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::User.AddLeaderBoardRecord Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		log.Error("Api::User.AddLeaderBoardRecord validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	resp, sErr := services.NewUser(c.Request.Context()).CreateAccountWithRole(&reqBody)
	if sErr != nil {
		appG.ResponseAppError(sErr)
		return
	}

	appG.Response200(resp)
}

// @Summary		Update leader board record
// @Description	Update leader board record
//
// @Tags			OE Referral
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			ID			param		string	true	"param"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/oe-referrals/leader-boards/:id [PUT]
func OELeaderBoardUpdateRecord(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := c.Request.Context()
	id := c.Param("id")

	var reqBody dto.OELeaderBoardAddMod
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::User.AddLeaderBoardRecord Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	loggedUser := app.GetLoggedUser(ctx) // use service.NewUser(context) for the valid context
	loggedUserRoles := loggedUser.MyRoles()
	if !models.CanCreateUserWithRole(loggedUserRoles, reqBody.OrgID, reqBody.RoleID) {
		appG.Response400(e.UserDoNotAllow2CreateAccount, fmt.Sprintf("logged user don't have permission to create account with role: %s", reqBody.RoleID))
		return
	}

	leader, err := services.OEReferralLeaderBoard(ctx).FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	nld, nErr := services.OEReferralLeaderBoard(ctx).UpdateInfo(leader, reqBody.DisplayName, reqBody.RefCode)
	if nErr != nil {
		appG.ResponseAppError(nErr)
		return
	}

	appG.Response200(nld)
}

// @Summary		Update leader board record
// @Description	Update leader board record
//
// @Tags			OE Referral
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			ID			param		string	true	"param"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/oe-referrals/leader-boards/:id [DELETE]
func OELeaderBoardDeleteRecord(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := c.Request.Context()
	id := c.Param("id")
	var query dto.OELeaderBoardAddMod
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	loggedUser := app.GetLoggedUser(ctx) // use service.NewUser(context) for the valid context
	loggedUserRoles := loggedUser.MyRoles()
	if !models.CanCreateUserWithRole(loggedUserRoles, query.OrgID, query.RoleID) {
		appG.Response400(e.UserDoNotAllow2CreateAccount, fmt.Sprintf("logged user don't have permission to create account with role: %s", query.RoleID))
		return
	}

	leader, err := services.OEReferralLeaderBoard(ctx).FindById(id, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	nErr := services.OEReferralLeaderBoard(ctx).DeleteRecord(leader)
	if nErr != nil {
		appG.ResponseAppError(nErr)
		return
	}

	appG.Response200("success")
}
