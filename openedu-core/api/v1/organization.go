package v1

import (
	"io"
	"net/http"
	"openedu-core/config/migrates"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CreateOrg
// @Summary		Create organization
// @Description	Create organization
//
// @Tags		organization
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referrer		header		string					true	"X-referrer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Param		Input			body		dto.CreateOrgRequest	true	"Create organization input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/admin/organizations [POST]
func CreateOrg(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CreateOrgRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response(http.StatusBadRequest, e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response(http.StatusBadRequest, e.INVALID_PARAMS, err.Error())
		return
	}

	var formSession *models.FormSession
	if data.FormSessionID != nil {
		if fSession, fErr := services.FormSession.FindOne(&models.FormSessionQuery{ID: data.FormSessionID}, nil); fErr != nil {
			appG.ResponseAppError(fErr)
			return
		} else {
			if fSession.Status == models.FormSessionsStatusApproved {
				appG.Response400(e.Form_session_approved, "form session already approved")
				return
			}

			if fSession.Status == models.FormSessionsStatusRejected {
				appG.Response400(e.Form_session_rejected, "form session already rejected")
				return
			}

			formSession = fSession
		}
	}

	data.DefaultOrgID = util.NewString(appG.GetOrg().ID)
	data.CreateByID = util.NewString(appG.GetLoggedUser().ID)
	org, aErr := services.Organization.Create(&data, true)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	if formSession != nil {
		if err2 := services.FormSession.UpdateStatus(appG.GetLoggedUser(), nil, appG.GetOrg(), formSession, models.FormSessionsStatusApproved, ""); err2 != nil {
			log.Error("Update form session failed ", err2)
		}
	}

	appG.Response200(org.Sanitize())
}

// UpdateOrg
// @Summary		Update Organization
// @Description	Update organization
//
// @Tags		organization
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referrer		header		string					true	"X-referrer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Param		id				path		string					true	"organization id"
//
// @Param		Input			body		dto.UpdateOrgRequest	true	"Update organization input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/admin/organizations/{id} [PUT]
func UpdateOrg(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.UpdateOrgRequest{}
	orgID := c.Param("id")
	currentUser := appG.GetLoggedUser()
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON and validate failed: "+err.Error())
		return
	}

	org, oErr := services.Organization.FindByID(orgID, true, nil)
	if oErr != nil {
		appG.ResponseAppError(oErr)
		return
	}

	if uErr := services.Organization.UpdateOrg(org, &req, currentUser); uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200(org.Sanitize())
}

// FindOrgByID
// @Summary		Find By ID
// @Description	Find By ID
//
// @Tags		organization
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	true	"Origin"
// @Param		X-referrer		header		string	true	"X-referrer"
// @Param		Authorization	header		string	true	"Bearer"
//
// @Param		id				path		string	true	"Organization ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router		/api/v1/admin/organizations/{id} [GET]
func FindOrgByID(c *gin.Context) {
	appG := app.Gin{C: c}
	orgID := c.Param("id")

	org, oErr := services.Organization.FindByID(orgID, false,
		&models.FindOneOptions{Preloads: []string{models.UserField, models.ThumbnailField, models.BannerField}})
	if oErr != nil {
		log.Error("find org by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	appG.Response200(org.Sanitize())
}

// FindPageOrgs
// @Summary		Find list organizations with pagination
// @Description	Find list organizations with pagination
//
// @Tags		organization
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referrer	header		string	true	"X-referrer"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router		/api/v1/admin/organizations [GET]
func FindPageOrgs(c *gin.Context) {
	appG := app.Gin{C: c}
	appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{models.UserField, models.ThumbnailField, models.BannerField}
	if len(options.Sort) == 0 {
		options.Sort = []string{"create_at desc"}
	}

	resp := dto.ListOrgsResponse{
		Results:    []*models.SimpleOrganization{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	var query models.OrganizationQuery
	if qErr := c.ShouldBindQuery(&query); qErr != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+qErr.Error())
		return
	}
	query.IncludeDeleted = util.NewBool(false)

	orgs, pagination, err := services.Organization.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map(orgs, func(c *models.Organization, _ int) *models.SimpleOrganization {
		return c.ToSimple()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// CheckOrgValidation
// @Summary		Check organization validation
// @Description	Check organization validation
//
// @Tags			organization
// @Accept			json
// @Produce		json
// @Security		BearerAuth
//
// @Param			Origin		header		string							true	"Origin"
// @Param			X-referrer	header		string							true	"X-referrer"
//
// @Param			input		body		dto.CheckOrgValidationRequest	true	"Check organization validation request"
//
// @Success		200			{object}	app.ResponseT[bool]
// @Failure		400			{object}	app.ResponseT[app.ErrorData]
// @Failure		500			{object}	app.ResponseT[app.ErrorData]
// @Router			/api/v1/organization/validate [POST]
func CheckOrgValidation(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.CheckOrgValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	if appErr := services.Organization.CheckValidation(&req); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	appG.Response200(true)
}

// FindOrgByDomain
// @Summary		Find org by domain
// @Description	Find org by domain
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referrer	header		string	true	"X-referrer"
//
// @Param		domain		query		string	true	"domain"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router		/api/internal-v1/organizations [GET]
func FindOrgByDomain(c *gin.Context) {
	appG := app.Gin{C: c}
	var query models.OrganizationQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if query.Domain == nil {
		appG.Response400(e.INVALID_PARAMS, "Domain query input null")
		return
	}

	org, qErr := services.Organization.FindOne(&query, nil)
	if qErr != nil {
		appG.ResponseAppError(qErr)
		return
	}

	appG.Response200(org)
}

func CalcRetroactiveForOrgAvail(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.CalcRetroactiveForAvailRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON and validate failed: "+err.Error())
		return
	}

	calcResp, appErr := services.Organization.CalcRetroactiveForAvail(req.AirdropAmount)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(calcResp)
}

func RetroactiveForOrgAvail(c *gin.Context) {
	appG := app.Gin{C: c}
	//user := appG.GetLoggedUser()
	org := appG.GetOrg()
	form, err := c.MultipartForm()
	if err != nil {
		appG.Response400(e.INVALID_PARAMS, "Parse multipart form failed: "+err.Error())
		return
	}

	walletID := form.Value["wallet_id"][0]
	if walletID == "" {
		appG.Response400(e.INVALID_PARAMS, "Wallet ID is required")
		return
	}

	file, err := c.FormFile("file")
	if err != nil {
		appG.Response400(e.INVALID_PARAMS, "No file uploaded: "+err.Error())
		return
	}

	if !strings.HasSuffix(strings.ToLower(file.Filename), ".xlsx") {
		appG.Response400(e.INVALID_PARAMS, "Invalid file format. Only .xlsx files are allowed")
		return
	}

	uploadedFile, err := file.Open()
	if err != nil {
		appG.Response500(e.ERROR, "Open uploaded file error: "+err.Error())
		return
	}
	defer uploadedFile.Close()

	fileContent, err := io.ReadAll(uploadedFile)
	if err != nil {
		appG.Response500(e.ERROR, "Reading file content error: "+err.Error())
		return
	}

	wallet, aErr := services.Wallet.FindOne(&models.WalletQuery{ID: &walletID}, nil)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	//if wallet.UserID != user.ID {
	//	appG.Response400(e.WalletOwnerRequired, "Wallet owner required")
	//}

	transaction, aErr := services.Transaction.RetroactiveForAvail(wallet, org, fileContent)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(transaction)
}

func TestSchema(c *gin.Context) {
	appG := app.Gin{C: c}

	migrates.MigrateSchemaData2Public()
	appG.Response200("success")
}
