package v1

import (
	"context"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"sync"

	"github.com/gin-gonic/gin"
)

// CronJobGenerateOERefReports handles the generation of AI blog posts via a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobGenerateOERefReports(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()

	var req dto.GenerateOEReferralReportsRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	if err := services.OEReferralReport(ctx).GenerateReports(&req); err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200("success")
}

// CronJobGenerateAIBlog handles the generation of AI blog posts via a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobGenerateAIBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	if err := services.AIBlogRewrite.CronJobHandleGenerateBlog(); err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200("success")
}

// CronJobCleanupCouponReservations handles the cleanup of expired coupon reservations via a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobCleanupCouponReservations(c *gin.Context) {
	appG := app.Gin{C: c}
	if err := services.CouponHistory.CleanupExpiredReservations(); err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200("success")
}

// CronJobExpiryOEPointNotification handles the point exipration.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobExpiryOEPointNotification(c *gin.Context) {
	appG := app.Gin{C: c}
	done := make(chan bool)
	ctx := c.Request.Context()

	// expired
	go func(done chan bool) {
		log.Debug("CronJobExpiryOEPointNotification.HandleExpiredPoint() RUNNING")
		if err := services.OEPointHistory(ctx).HandleExpiredPoint(); err != nil {
			appG.ResponseAppError(err)
			return
		}
		done <- true
	}(done)

	// expiry in 1 day
	go func(done chan bool) {
		log.Debug("CronJobExpiryOEPointNotification.PointExpirationReminder(1) RUNNING")
		if err := services.OEPointHistory(ctx).PointExpirationReminder(1); err != nil {
			appG.ResponseAppError(err)
			return
		}
		done <- true
	}(done)

	// expiry in 3 days
	go func(done chan bool) {
		log.Debug("CronJobExpiryOEPointNotification.PointExpirationReminder(3) RUNNING")
		if err := services.OEPointHistory(ctx).PointExpirationReminder(3); err != nil {
			appG.ResponseAppError(err)
			return
		}
		done <- true
	}(done)

	// expiry in 7 days
	go func(done chan bool) {
		log.Debug("CronJobExpiryOEPointNotification.PointExpirationReminder(7) RUNNING")
		if err := services.OEPointHistory(ctx).PointExpirationReminder(7); err != nil {
			appG.ResponseAppError(err)
			return
		}
		done <- true
	}(done)

	// expiry in 30 days
	go func(done chan bool) {
		log.Debug("CronJobExpiryOEPointNotification.PointExpirationReminder(30) RUNNING")
		if err := services.OEPointHistory(ctx).PointExpirationReminder(30); err != nil {
			appG.ResponseAppError(err)
			return
		}
		done <- true
	}(done)

	for i := 0; i < 5; i++ {
		<-done
	}
	appG.Response200("success")
}

//	func SeedOEData() {
//		// seed data
//		// create history bao gom 5 case o tren
//		now := time.Now()
//		numSlice := []int{-5, -3, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 27, 28, 29, 30, 31, 32, 33}
//		userIds := []string{"7OtKUUx5h8INZAnP", "<EMAIL>", "HxaoZjG85gv8U3Hy"}
//		for i := 1; i <= 100; i++ {
//			pickUser := userIds[rand.Intn(len(userIds))]
//			pick := numSlice[rand.Intn(len(numSlice))]
//			expire := now.AddDate(0, 0, pick)
//			amount := rand.Int63n(10-5+1) + 5 //rand.Intn(max - min + 1) + min
//			used := rand.Int63n(5-0+1) + 0
//			remaining := amount - used
//			history := &models.OEPointHistory{
//				UserID:     pickUser,
//				OrgID:      util.GenerateId(),
//				Status:     models.OEPointStatusReceived,
//				Amount:     decimal.NewFromInt(amount),
//				Used:       decimal.NewFromInt(used),
//				Remaining:  decimal.NewFromInt(remaining),
//				ExpireDate: int(expire.UnixMilli()),
//				Props:      models.JSONB{},
//				EntityType: models.UserModelName,
//				EntityID:   util.GenerateId(),
//				Note:       fmt.Sprintf("random history with expired: %d - %s", pick, expire.Format(time.RFC3339)),
//			}
//			if err := models.Repository.OEPointHistory.Create(history, nil); err != nil {
//				fmt.Println("Error qua troi: ", err)
//			}
//		}
//	}
//
// CronJobPushSubscriptionNotification handles sending subscription notifications via a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobRemindSubscription(c *gin.Context) {
	appG := app.Gin{C: c}

	errChan := make(chan *e.AppError, 4)
	done := make(chan struct{})

	periods := []int{util.EXPIRED, util.EXPIREIN1DAY, util.EXPIREIN3DAY, util.EXPIREIN7DAY}

	var wg sync.WaitGroup
	wg.Add(len(periods))

	for _, period := range periods {
		go func(days int) {
			defer wg.Done()

			var err *e.AppError
			if days == util.EXPIRED {
				err = services.Subscription.RemindExpiration()
			} else {
				err = services.Subscription.RemindExpirationByPeriod(days)
			}

			if err != nil {
				errChan <- err
			}
		}(period)
	}

	go func() {
		wg.Wait()
		close(done)
	}()
	<-done

	select {
	case err := <-errChan:
		appG.ResponseAppError(err)
		return
	default:
		appG.Response200("success")
	}

}

// CronJobStartFundingLaunchpad handles the start funding for launchpad when funding start date via a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobStartFundingLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.FundingLaunchpadRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := services.ClpLaunchpad.StartFunding(data.ID); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// CronJobEndFundingLaunchpad handles the switch funding to voting for launchpad when funding start end via a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobEndFundingLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.FundingLaunchpadRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	launchpad, cErr := services.ClpLaunchpad.FindByID(data.ID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if err := services.ClpLaunchpad.EndFunding(launchpad); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// CronJobEndVotingMilestoneLaunchpad handles the switch milestone running for launchpad when milestone end vote date via a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobEndVotingMilestoneLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.EndVotingMilestoneRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := services.ClpVotingMilestone.EndVoting(&data); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// CronJobCheckSetFundingTimeLaunchpad handles the time limit for waiting creator set funding time a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobCheckSettingFundingTimeLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.FundingLaunchpadRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	// service check funding time
	if err := services.ClpLaunchpad.CheckSettingFundingTime(&data); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// CronJobCheckCreatorContinueVotingLaunchpad handles the check creator continue voting a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobCheckCreatorContinueVotingLaunchpad(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.FundingLaunchpadRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	// service check creator continue voting after end funding
	if err := services.ClpLaunchpad.CheckCreatorContinueVotingLaunchpad(&data); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// CronJobCreateCourseRevenue handles the create course revenue a cron job.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobCreateCourseRevenue(c *gin.Context) {
	appG := app.Gin{C: c}

	current := util.GetCurrentTime()

	data := dto.CronJobCreateCourseRevenueReq{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	errChan := make(chan *e.AppError, len(data.Periods))

	var wg sync.WaitGroup
	wg.Add(len(data.Periods))

	for _, period := range data.Periods {
		go func(p *models.TimePeriod) {
			defer wg.Done()

			if err := services.Course.CronJobCreateCourseRevenue(int64(current), *p); err != nil {
				errChan <- err
			}
		}(period)
	}

	wg.Wait()

	select {
	case err := <-errChan:
		appG.ResponseAppError(err)
		return
	default:
		appG.Response200("success")
	}
}

// CronJobExpiredSubcription handles the expired subscription automation.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func ExpiredSubcription(c *gin.Context) {
	appG := app.Gin{C: c}

	appErr := services.Subscription.ExpiredSucriptions()
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// CronJobSummaryOEReferralReport handles the expired subscription automation.
// DO NOT WRITE SWAGGER DOCS. THIS API IS INTERNAL.
func CronJobSummaryOEReferralReport(c *gin.Context) {
	appG := app.Gin{C: c}
	services.OEReferral(context.TODO()).SummaryReferralReport(models.AIGovVN2025Campaign)
	appG.Response200("success")
}

func CronjobRemindLearned(c *gin.Context) {
	appG := app.Gin{C: c}

	go services.LearningStatus.HandleRemindLearner()

	appG.Response200("success")
}
