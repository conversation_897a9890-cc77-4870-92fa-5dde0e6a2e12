package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// @Summary		Create Course by AI
// @Description	Create Course by AI
//
// @Tags			Course
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			Input			body		dto.GenerateCourseFromAIRequest	true	"Create course input"
//
// @Success		201				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/ai [POST]
func GenerateCourseByAI(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	var reqBody dto.GenerateCourseFromAIRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		log.Error("Api::AICourse.GenerateCourseByAI Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	switch reqBody.Type {
	case models.AICourseYoutubePlaylistType:
		course, aErr := services.AICourse.GenerateCourseByYoutube(&dto.GenerateCourseFromYoutubeParams{
			Org:              org,
			User:             currentUser,
			PlaylistLink:     reqBody.PlaylistLink,
			Tone:             reqBody.Tone,
			Language:         reqBody.Language,
			QuizIncluded:     reqBody.QuizIncluded,
			QuizType:         reqBody.QuizType,
			NumberOfQuestion: reqBody.NumberOfQuestion,
			SummaryIncluded:  reqBody.SummaryIncluded,
		})
		if aErr != nil {
			appG.ResponseAppError(aErr)
			return
		} else {
			appG.Response200(course.Sanitize())
			return
		}
	case models.AICourseLearnerDescriptionType:
		switch reqBody.CurrentStep {
		case models.AICourseLearnerDescriptionGenerateStep:
			course, aErr := services.AICourse.GenerateCourseByLearnerDescription(&dto.GenerateCourseFromLearnerDescriptionParams{
				Org:          org,
				User:         currentUser,
				LearnerInfo:  reqBody.LearnerInfo,
				ContentInfo:  reqBody.ContentInfo,
				MaterialID:   reqBody.MaterialID,
				LevelID:      reqBody.LevelID,
				Language:     reqBody.Language,
				Duration:     reqBody.Duration,
				DurationType: reqBody.DurationType,
				StudyLoad:    reqBody.StudyLoad,
				CourseCuid:   reqBody.CourseCuid,
			})
			if aErr != nil {
				appG.ResponseAppError(aErr)
				return
			} else {
				appG.Response200(course.Sanitize())
				return
			}
		case models.AICourseThumbnailGenerateStep:
			course, aErr := services.AICourse.GenerateCourseThumbnail(&dto.GenerateCourseThumbnailParams{
				ThumbnailDescription: reqBody.ThumbnailDescription,
				ThumbnailQuantity:    reqBody.ThumbnailQuantity,
				ThumbnailStyle:       reqBody.ThumbnailStyle,
				CourseCuid:           reqBody.CourseCuid,
			})
			if aErr != nil {
				appG.ResponseAppError(aErr)
				return
			} else {
				appG.Response200(course.Sanitize())
				return
			}
		case models.AICourseOutlineGenerateStep:
			course, aErr := services.AICourse.GenerateOutlineFromLearnerDescription(&dto.GenerateOutlineFromLearnerDescriptionParams{
				CourseCuid:  reqBody.CourseCuid,
				Title:       reqBody.Title,
				Description: reqBody.Description,
				ThumbnailID: reqBody.ThumbnailID,
			})
			if aErr != nil {
				appG.ResponseAppError(aErr)
				return
			} else {
				appG.Response200(course.Sanitize())
				return
			}
		}
	default:
	}
	appG.Response400(e.INVALID_PARAMS, "Invalid AI Course request type")
	return
}

func CronJobGenerateAICourse(c *gin.Context) {
	appG := app.Gin{C: c}

	if err := services.AICourse.CronJobGenerateAICourse(); err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}
