package v1

import (
	"context"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"time"

	"github.com/gin-gonic/gin"
)

// CreateLearningProgress
//
//	@Summary		Create Learning Progress
//	@Description	Create Learning Progress
//
//	@Tags			learning progress
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin							header		string								true	"Origin"
//	@Param			X-referrer						header		string								true	"X-referrer"
//
//	@Param			Authorization					header		string								true	"Bearer"
//
//	@Param			createLearningProgressRequest	body		dto.CreateLearningProgressRequest	true	"Create Learning Progress request body"
//
//	@Success		200								{object}	app.Response
//	@Failure		400								{object}	app.Response
//	@Failure		500								{object}	app.Response
//	@Router			/api/v1/learning-progresses [POST]
func CreateLearningProgress(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	ctx := appG.GetCtx()
	org := appG.GetOrg()

	var reqBody dto.CreateLearningProgressRequest
	if err := appG.BindAndValidateJSON(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}
	if reqBody.Event == models.LatestLessonProgress {
		overview, _, appErr := services.NewLearningStatus(ctx).UpdateLatestLesson(&dto.UpdateCurrentLessonRequest{
			UserID:            currentUser.ID,
			CourseSlug:        reqBody.CourseSlug,
			CurrentSectionUID: reqBody.SectionUID,
			CurrentLessonUID:  reqBody.LessonUID,
			OrgID:             org.ID,
		})
		if appErr != nil {
			log.Error("[services.LearningProgress.Upsert]", appErr)
			appG.ResponseAppError(appErr)
			return
		}

		appG.Response200(overview)
		return
	}

	overview, course, appErr := services.NewLearningStatus(ctx).AddLearningStatus(&dto.CreateLearningProgressParams{
		UserID:           currentUser.ID,
		CourseSlug:       reqBody.CourseSlug,
		SectionUID:       reqBody.SectionUID,
		LessonUID:        reqBody.LessonUID,
		LessonContentUID: reqBody.LessonContentUID,
		CompleteAt:       reqBody.CompleteAt,
		StartAt:          reqBody.StartAt,
		PauseAt:          reqBody.PauseAt,
		OrgID:            org.ID,
		Org:              org,
		User:             currentUser,
	})
	if appErr != nil {
		log.Error("[services.LearningProgress.Upsert]", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	// Check certificate condition for learner can receive certificate
	go func() {
		services.Certificate.PushNotificationReceiveCertificate(course, currentUser, org)
	}()

	appG.Response200(overview)
	return
}

// GetUserLearningProgressByCourse
// @Summary		Get Learning Progress by Course
// @Description	Get Learning Progress by Course
//
// @Tags			learning progress
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referrer		header		string	true	"X-referrer"
//
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course slug"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/learning-progresses/courses/:id [GET]
func GetUserLearningProgressByCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	ctx := appG.GetCtx()
	slug := c.Param(util.IDParamKey)
	userLearningProgress, course, appErr := services.NewLearningStatus(ctx).
		GetUserLearningStatusByCourse(slug, currentUser.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	// Check certificate condition for learner can receive certificate
	go func() {
		services.Certificate.PushNotificationReceiveCertificate(course, currentUser, org)
	}()

	appG.Response200(userLearningProgress)
	return
}

// CheckCompleteCourse
// @Summary      Check course completion status for multiple users
// @Description  Check course completion status for multiple users
//
// @Tags         learning progress
// @Accept       json
// @Produce      json
//
// @Param        Origin        header      string  true    "Origin"
// @Param        X-referrer    header      string  true    "X-referrer"
//
// @Param        Authorization header      string  true    "Bearer"
//
// @Param        userIDs      query       []string  true    "List of user IDs"
// @Param        courseCuid   query       string    true    "Course CUID"
// @Param        status       query       string    false   "Filter by status (completed, in_progress, not_started)"
//
// @Success      200          {object}    app.Response
// @Failure      400          {object}    app.Response
// @Failure      500          {object}    app.Response
// @Router       /api/v1/learning-progresses/check-complete-course [GET]
func CheckCompleteCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	ctx := appG.GetCtx()
	var reqBody dto.CheckUserCompleteCourse
	if err := c.ShouldBindQuery(&reqBody); err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if lps, err := services.NewLearningStatus(ctx).CheckCompleteCourse(reqBody); err != nil {
		appG.ResponseAppError(err)
	} else {
		appG.Response200(lps)
	}
}

func MigrateLearningStatusStartAt(c *gin.Context) {
	appG := app.Gin{C: c}

	var req dto.MigrateLearningStatusStartAtRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	go func() {
		ctx := context.Background()
		page := 1
		perPage := 100
		for {
			lss, pagination, err := models.Repository.LearningStatus(ctx).FindPage(&models.LearningStatusQuery{
				CourseCuidIn: req.CourseCUIDs,
			}, &models.FindPageOptions{
				Page:    page,
				PerPage: perPage,
			})
			if err != nil {
				return
			}

			if len(lss) == 0 {
				break
			}

			for _, ls := range lss {
				for _, sectionStt := range ls.Sections {
					minStartAtLesson := int64(0)
					maxCompleteAtLesson := int64(0)

					for _, lessonStt := range sectionStt.Lessons {
						minStartAtContent := int64(0)
						maxCompleteAtContent := int64(0)
						for _, contentStt := range lessonStt.Contents {
							if contentStt.StartAt > 0 && contentStt.CompleteAt <= contentStt.StartAt {
								lessonContent, lErr := models.Repository.LessonContent(ctx).FindOne(&models.LessonContentQuery{
									UID: &contentStt.ContentUID,
								}, &models.FindOneOptions{Sort: []string{models.CreateAtASC}})
								if lErr != nil {
									return
								}

								switch lessonContent.Type {
								case models.LessonTypeText:
									textLength := len(lessonContent.Content) + len(lessonContent.JsonContent)
									jitterDuration := util.RandomDuration(2*time.Second, 92*time.Second)
									contentStt.StartAt = contentStt.CompleteAt - (int64(textLength) * 1000) - jitterDuration.Milliseconds() // Assuming 1 character = 1ms

								case models.LessonTypeVideo:
									jitterDuration := util.RandomDuration(3*time.Second, 298*time.Second)
									contentStt.StartAt = contentStt.CompleteAt - int64(lessonContent.Duration) - jitterDuration.Milliseconds()

								case models.LessonTypeQuiz:
									jitterDuration := util.RandomDuration(176*time.Second, 597*time.Second)
									contentStt.StartAt = contentStt.CompleteAt - jitterDuration.Milliseconds()

								default:
									jitterDuration := util.RandomDuration(55*time.Second, 421*time.Second)
									contentStt.StartAt = contentStt.CompleteAt - jitterDuration.Milliseconds()
								}
							}

							if minStartAtContent == 0 || contentStt.StartAt < minStartAtContent {
								minStartAtContent = contentStt.StartAt
							}

							if maxCompleteAtContent == 0 || contentStt.CompleteAt > maxCompleteAtContent {
								maxCompleteAtContent = contentStt.CompleteAt
							}
						}

						if lessonStt.StartAt > 0 && lessonStt.CompleteAt <= lessonStt.StartAt {
							lessonStt.StartAt = minStartAtContent
							lessonStt.CompleteAt = maxCompleteAtContent
						}

						if minStartAtLesson == 0 || lessonStt.StartAt < minStartAtLesson {
							minStartAtLesson = lessonStt.StartAt
						}

						if maxCompleteAtLesson == 0 || lessonStt.CompleteAt > maxCompleteAtLesson {
							maxCompleteAtLesson = lessonStt.CompleteAt
						}
					}

					if sectionStt.StartAt > 0 && sectionStt.CompleteAt <= sectionStt.StartAt {
						sectionStt.StartAt = minStartAtLesson
						sectionStt.CompleteAt = maxCompleteAtLesson
					}
				}

				models.Repository.LearningStatus(ctx).Update(ls, nil)
			}

			if !pagination.HasNextPage() {
				break
			}

			page++ // Move to the next page
		}
	}()

	appG.Response200("success")
	return
}
