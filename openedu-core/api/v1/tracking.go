package v1

import (
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Count ref for users
// @Description	Count ref for users
//
// @Tags		tracking
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/trackings [GET]

func CountTrackingByUserAndEvent(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query models.TrackingQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	network := c.Query("network")

	refCount, pagination, err := communication.Tracking.CountTrackingByUserAndEvent(query.IntoComm(), options.IntoComm())
	if err != nil {
		appG.ResponseAppError(e.NewError500(e.External_call_error, err.Error()))
		return
	}

	userIDs := lo.Map(refCount, func(item *communicationdto.CountRefUserResp, _ int) string { return item.UserID })

	users, err := services.User.FindMany(&models.UserQuery{IDIn: &userIDs}, &models.FindManyOptions{})

	mapUserIDUser := map[string]*models.User{}
	for _, u := range users {
		mapUserIDUser[u.ID] = u
	}

	trackings, err := services.Report.HandleFindTrackingByChunk(&query, 100)

	mapObjectIDTracking := lo.GroupBy(trackings, func(item *communicationdto.TrackingResponse) string {
		return item.ObjectID
	})

	for _, ref := range refCount {
		u, ok := mapUserIDUser[ref.UserID]
		if ok {
			ref.Email = u.Email
			ref.DislayName = u.DisplayName
			ref.Phone = u.Phone
			ref.Active = u.Active
		}
		trackings, ok := mapObjectIDTracking[ref.UserID]
		if ok {
			mActorIDTracking := map[string]*communicationdto.TrackingResponse{}
			for _, track := range trackings {
				mActorIDTracking[track.ActorID] = track
			}
			refByIds := lo.Map(trackings, func(item *communicationdto.TrackingResponse, _ int) string { return item.ActorID })
			users, _ := models.Repository.User.FindManySimpleUser(&models.UserQuery{IDIn: &refByIds}, &models.FindManyOptions{})
			// network
			if network != "" {
				mapUserIDUser := map[string]*models.BasicUserProfile{}
				for _, u := range users {
					mapUserIDUser[u.ID] = u
				}
				wallet, _ := models.Repository.Wallet.FindManySimple(&models.WalletQuery{Default: util.NewBool(true), Network: util.NewT(models.BlockchainNetwork(network))}, &models.FindManyOptions{})

				for _, w := range wallet {
					user, ok := mapUserIDUser[w.UserID]
					createAt := mActorIDTracking[user.ID].CreateAt
					if ok {
						ref.RefBy = append(ref.RefBy, map[string]interface{}{"address": w.Address, "email": user.Email, "create_at": createAt})
					}
				}
			} else {
				for _, user := range users {
					createAt := mActorIDTracking[user.ID].CreateAt
					ref.RefBy = append(ref.RefBy, map[string]interface{}{"email": user.Email, "create_at": createAt})
				}

			}

		}
	}

	resp := communicationdto.ListRefCountResponse{
		Results:    refCount,
		Pagination: pagination,
	}
	appG.Response200(resp)
}
