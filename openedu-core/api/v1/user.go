package v1

import (
	"errors"
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/communication"
	communicationdto "openedu-core/pkg/communication/dto"
	"openedu-core/pkg/e"

	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// @Summary		Get me
// @Description	Get me
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Param           is_full         query       string  true    "true if want get my profile page"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me [GET]
func GetMe(c *gin.Context) {
	appG := app.Gin{C: c}

	loggedUser := appG.GetLoggedUser()
	if loggedUser == nil {
		appG.Response404(e.Error_user_not_found, "User not found, require login")
		return
	}

	var query models.UserQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	// Ensure the IsFull flag is set to true if not already specified.
	if query.IsFull == nil {
		query.IsFull = util.NewBool(false)
	}

	user, sErr := services.User.GetUserByID(loggedUser.ID)
	if sErr != nil {
		appG.ResponseAppError(sErr)
		return
	}

	var mapProfile map[string]interface{}
	// get full information user for my profile page
	if *query.IsFull {
		userProfile, sErr := services.User.GetUserProfile(user, appG.GetOrg())
		if sErr != nil {
			appG.ResponseAppError(sErr)
			return
		}

		profile, mapErr := util.StructToMap(userProfile)
		if mapErr != nil {
			appG.Response500(e.Json_encode_error, mapErr.Error())
			return
		}
		mapProfile = profile
	} else {
		session, sErr := services.Session.FindOne(&models.SessionQuery{UserID: &loggedUser.ID}, &models.FindOneOptions{Sort: []string{"create_at desc"}})
		if sErr != nil && sErr.ErrCode != e.Error_auth_session_not_found {
			appG.Response400(sErr.ErrCode, sErr.Msg)
			return
		}

		simpleProfile := user.ToSimpleProfile()
		profile, mapErr := util.StructToMap(simpleProfile)
		if mapErr != nil {
			appG.Response500(e.Json_encode_error, mapErr.Error())
			return
		}

		mapProfile = profile
		if session != nil {
			mapProfile["last_login_session"] = session.Sanitize()
		} else {
			mapProfile["last_login_session"] = nil
		}
	}

	plan, appErr := services.Subscription.GetActivePlanByUser(user)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}
	mapProfile["pricing_plan"] = plan.Tier

	go func() {
		if appErr := services.Wallet.InitUserWallets(appG.GetOrg(), user); appErr != nil {
			log.Errorf("Init user wallet error: %v", appErr)
		}
	}()

	appG.Response200(mapProfile)
}

func UpdateUserRoles(c *gin.Context) {
	appG := app.Gin{C: c}
	type Us struct {
		UserId   string   `json:"user_id"`
		OrgId    string   `json:"org_id"`
		Roles    []string `json:"roles"`
		DelRoles []string `json:"del_roles"`
	}
	var us Us
	if err := c.ShouldBindJSON(&us); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	_, err := models.Repository.User.FindByID(us.UserId)
	if err != nil {
		appG.Response400(e.INVALID_PARAMS, "User not found")
		return
	}
	for _, v := range us.Roles {
		a := &models.UserRoleOrgQuery{
			UserID:         util.NewString(us.UserId),
			RoleID:         util.NewString(v),
			OrgID:          util.NewString(us.OrgId),
			IncludeDeleted: util.NewBool(true),
		}
		if o, err2 := models.Repository.UserRoleOrg.FindOne(a, nil); err2 != nil {
			if errors.Is(err2, gorm.ErrRecordNotFound) {
				models.Repository.User.UpdateRoles(&models.UserRoleOrg{
					UserID: us.UserId,
					RoleID: v,
					OrgID:  us.OrgId,
				})
			} else {
				appG.Response400(e.ERROR, "User ko co role nay de delete: "+err2.Error())
				return
			}
		} else {
			o.DeleteAt = 0
			if err3 := models.Repository.UserRoleOrg.Update(o, nil); err3 != nil {
				appG.Response400(e.ERROR, "Khong delete dc oy"+err2.Error())
				return
			}
		}
	}

	for _, v := range us.DelRoles {
		a := &models.UserRoleOrgQuery{
			UserID:         util.NewString(us.UserId),
			RoleID:         util.NewString(v),
			OrgID:          util.NewString(us.OrgId),
			IncludeDeleted: util.NewBool(true),
		}
		if o, err2 := models.Repository.UserRoleOrg.FindOne(a, nil); err2 != nil {
			appG.Response400(e.ERROR, "User ko co role nay de delete"+err2.Error())
			return
		} else {
			if err3 := models.Repository.UserRoleOrg.Delete(o.ID, nil); err3 != nil {
				appG.Response400(e.ERROR, "Khong delete dc oy"+err3.Error())
				return
			}
		}
	}
	models.Cache.UserRole.DeleteByUserID(us.UserId)
	appG.Response200("Oke nha")
}

// @Summary		Get User Profile By Id
// @Description	Get User Profile By Id
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Param		username	path		string	true	"Target User ID"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/users/:id [GET]
func GetUserProfile(c *gin.Context) {
	appG := app.Gin{C: c}
	username := c.Param("id")

	params := &dto.GetUserProfileByIDParams{
		TargetUsername: username,
	}
	var currentUser *models.User

	if appG.GetLoggedUser() != nil {
		currentUser = appG.GetLoggedUser()
		params.User = currentUser
	}

	userProfile, qErr := services.User.GetUserProfileByID(params, appG.GetOrg())
	if qErr != nil {
		appG.ResponseAppError(qErr)
		return
	}

	appG.Response200(userProfile)
}

// @Summary		Update User
// @Description	Update User
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			body			body		dto.UpdateProfileRequest	true	"Update user profile request body"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me [PUT]
func UpdateProfile(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.UpdateProfileRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::User.Update Profile Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		log.Error("Api::User.Update Profile validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	user := appG.GetLoggedUser()
	userFind, uErr := services.User.GetUserByID(user.ID)
	if uErr != nil {
		log.Error("[services.User.GetUserByID]", uErr)
		appG.ResponseAppError(uErr)
		return
	}

	if reqBody.DisplayName != nil {
		userFind.DisplayName = *reqBody.DisplayName
	}

	if reqBody.Avatar != nil {
		userFind.Avatar = *reqBody.Avatar
	}

	if reqBody.Phone != nil {
		userFind.Phone = *reqBody.Phone
	}

	if reqBody.Headline != nil {
		userFind.Headline = *reqBody.Headline
	}

	if reqBody.About != nil {
		userFind.About = *reqBody.About
	}

	if reqBody.Skills != nil {
		userFind.Skills = *reqBody.Skills
	}

	if reqBody.CoverPhoto != nil {
		userFind.CoverPhoto = *reqBody.CoverPhoto
	}

	if reqBody.Username != nil {
		ok, uErr := services.UserSetting.CheckUsernameInputValid(*reqBody.Username)
		if uErr != nil {
			log.Error("[services.UserSetting.CheckUsernameInputValid]", uErr)
			appG.ResponseAppError(uErr)
			return
		}

		if !ok {
			appG.Response400(e.Error_username_is_existed, "Username is invalid")
			return
		}

		uErr = services.UserSetting.UpsertSettingOldUsername(userFind.ID, userFind.Username)
		if uErr != nil {
			log.Error("[services.UserSetting.UpsertSettingOldUsername]", uErr)
			appG.ResponseAppError(uErr)
			return
		}
		userFind.Username = strings.TrimSpace(*reqBody.Username)
	}

	if reqBody.Props != nil {
		userFind.Props.Facebook = reqBody.Props.Facebook
		userFind.Props.Github = reqBody.Props.Github
		userFind.Props.Gmail = reqBody.Props.Gmail
		userFind.Props.LinkedIn = reqBody.Props.LinkedIn
		userFind.Props.Telegram = reqBody.Props.Telegram
		userFind.Props.Website = reqBody.Props.Website
	}

	userFind.UpdateAt = int(time.Now().UnixMilli())
	updatedUser, uErr := services.User.Update(userFind)
	if uErr != nil {
		log.Error("[services.User.UpdateProfile]", uErr)
		appG.ResponseAppError(uErr)
		return
	}

	userUpdated, uErr := services.User.GetUserByID(updatedUser.ID)
	if uErr != nil {
		log.Error("[services.User.GetUserByID]", uErr)
		appG.ResponseAppError(uErr)
		return
	}

	userUpdated.Password = ""
	appG.Response200(userUpdated)
}

// @Summary		Follow User
// @Description	Follow User
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"Target User ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/{id}/follow [POST]
func FollowUser(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	if err := services.UserAction.FollowUser(&dto.ActionParams{
		User:         appG.GetLoggedUser(),
		TargetUserID: id,
	}); err != nil {
		log.Error("[services.UserAction.FollowUser]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// @Summary		UnFollow User
// @Description	UnFollow User
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"Target User ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/{id}/unfollow [DELETE]
func UnFollowUser(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	if err := services.UserAction.UnFollowUser(&dto.ActionParams{
		User:         appG.GetLoggedUser(),
		TargetUserID: id,
	}); err != nil {
		log.Error("[services.UserAction.UnFollowUser]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// @Summary		Block User
// @Description	Block User
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"Target User ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/{id}/block [POST]
func BlockUser(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	if err := services.UserAction.BlockUser(&dto.ActionParams{
		User:         appG.GetLoggedUser(),
		TargetUserID: id,
	}); err != nil {
		log.Error("[services.UserAction.BlockUser]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// @Summary		Block Users
// @Description	Block Users
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string	 					true	"Origin"
// @Param		X-referer		header		string	 					true	"X-referer"
// @Param		Authorization	header		string	             		true	"Bearer"
//
// @Param		user_ids		body		dto.BlockListUsersRequest 	true	"Target User IDs"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/block [POST]
func BlockUsers(c *gin.Context) {
	appG := app.Gin{C: c}
	userLogged := appG.GetLoggedUser()

	var reqBody *dto.BlockListUsersRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("BlockUsers Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if len(reqBody.UserIDs) > 0 {
		blockUserIDs := lo.Uniq(reqBody.UserIDs)
		if err := services.UserAction.BlockListUsers(userLogged, blockUserIDs); err != nil {
			log.Error("[services.UserAction.BlockListUsers]", err)
			appG.ResponseAppError(err)
			return
		}
	}

	appG.Response200("success")
}

// @Summary		Unblock User
// @Description	Unblock User
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"Target User ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/{id}/unblock [DELETE]
func UnblockUser(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	if err := services.UserAction.UnblockUser(&dto.ActionParams{
		User:         appG.GetLoggedUser(),
		TargetUserID: id,
	}); err != nil {
		log.Error("[services.UserAction.UnblockUser]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// @Summary		Report User
// @Description	Report User
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"Target User ID"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/{id}/report [POST]
func ReportUser(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	var reqBody dto.ReportUserRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::User.Report Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		log.Error("Api::User.Report validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if err := services.UserAction.ReportUser(&dto.ReportUserParams{
		User:         appG.GetLoggedUser(),
		TargetUserID: id,
		Reason:       reqBody.Reason,
	}); err != nil {
		log.Error("[services.UserAction.ReportUser]", err)
		appG.ResponseAppError(err)
		return
	}

	appG.Response200("success")
}

// @Summary		Find Page Mine Action
// @Description	Find Page Mine Action
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
//
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			option			query		models.FindPageOptions			false	"Find Page User Action"
// @Param			query			query		dto.FindPageUserActionRequest	true	"Find Page User Action"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/actions [GET]
func FindPageMineAction(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		log.Error("Api::User.Find Page Mine Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query dto.FindPageUserActionRequest
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	var findPageQuery models.UserActionQuery
	findPageQuery.IncludeDeleted = util.NewBool(false)
	currentUser := appG.GetLoggedUser()
	switch query.Type {
	case models.Followers:
		findPageQuery.Action = util.NewT(models.Followed)
		findPageQuery.TargetUserID = &currentUser.ID
	case models.Following:
		findPageQuery.Action = util.NewT(models.Followed)
		findPageQuery.UserID = &currentUser.ID
	case models.Block:
		findPageQuery.Action = util.NewT(models.Blocked)
		findPageQuery.UserID = &currentUser.ID
	case models.Unblock:
		findPageQuery.Action = util.NewT(models.Blocked)
		findPageQuery.UserID = &currentUser.ID
	case models.Report:
		findPageQuery.Action = util.NewT(models.Reported)
		findPageQuery.UserID = &currentUser.ID
	default:
		appG.Response400(e.User_action_not_supported, e.MsgFlags[e.User_action_not_supported])
		return
	}
	options.Preloads = []string{"TargetUser"}
	// Call userAction find page service
	userActions, pagination, err := services.UserAction.FindPage(&findPageQuery, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if query.Type == models.Unblock {
		userResponse := dto.ListUserResponse{
			Results:    []*models.SimpleProfile{},
			Pagination: models.NewPagination(options.Page, options.PerPage, 0),
		}

		// List userIDs is blocked
		userIDs := lo.Map(userActions, func(ua *models.UserAction, _ int) string {
			return ua.TargetUserID
		})

		userIDs = append(userIDs, currentUser.ID)
		// Find list users not blocked
		// apply search for find user
		userQuery := &models.UserQuery{
			IDNotIn: lo.Uniq(userIDs),
		}
		if query.SearchTerm != nil && query.SearchCategories != nil {
			userQuery.SearchTerm = query.SearchTerm
			userQuery.SearchCategories = query.SearchCategories
		}

		options.Preloads = []string{}
		users, pagination, err := services.User.FindPage(userQuery, &options)
		if err != nil {
			appG.ResponseAppError(err)
			return
		}

		userResponse.Results = lo.Map(users, func(user *models.User, _ int) *models.SimpleProfile {
			return user.ToSimpleProfile()
		})
		userResponse.Pagination = pagination
		appG.Response200(userResponse)
		return
	}

	userResponse := dto.ListUserResponse{
		Results:    []*models.SimpleProfile{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	userResponse.Results = lo.Map(userActions, func(ua *models.UserAction, _ int) *models.SimpleProfile {
		return ua.TargetUser.ToSimpleProfile()
	})
	userResponse.Pagination = pagination
	appG.Response200(userResponse)
}

// @Summary		Find Page User Action
// @Description	Find Page User Action
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
//
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			id				path		string							true	"Target User ID"
//
// @Param			option			query		models.FindPageOptions			false	"Find Page User Action"
// @Param			query			query		dto.FindPageUserActionRequest	true	"Find Page User Action"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/{id}/actions [GET]
func FindPageUserAction(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		log.Error("Api::User.Find Page Mine Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query dto.FindPageUserActionRequest
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	var findPageQuery models.UserActionQuery
	findPageQuery.IncludeDeleted = util.NewBool(false)
	switch query.Type {
	case models.Followers:
		findPageQuery.Action = util.NewT(models.Followed)
		findPageQuery.TargetUserID = &id
	case models.Following:
		findPageQuery.Action = util.NewT(models.Followed)
		findPageQuery.UserID = &id
	default:
		appG.Response400(e.User_action_not_supported, e.MsgFlags[e.User_action_not_supported])
		return
	}

	resp := dto.ListUserActionResponse{
		Results:    []*models.SimpleUserAction{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	// Call UserAction find page service
	userActions, pagination, err := services.UserAction.FindPage(&findPageQuery, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = lo.Map(userActions, func(ua *models.UserAction, _ int) *models.SimpleUserAction {
		return ua.Sanitize()
	})

	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find Page User Action By Admin
// @Description	Find Page User Action By Admin
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
//
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			option			query		models.FindPageOptions			false	"Find Page User Action By Admin"
// @Param			query			query		dto.FindPageUserActionRequest	true	"Find Page User Action By Admin"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/users/:id/actions [GET]
func FindPageUserActionByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		log.Error("Api::User.Find Page Mine Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	var query dto.FindPageUserActionRequest
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	var findPageQuery models.UserActionQuery
	findPageQuery.IncludeDeleted = util.NewBool(false)
	switch query.Type {
	case models.Followers:
		findPageQuery.Action = util.NewT(models.Followed)
		findPageQuery.TargetUserID = &id
	case models.Following:
		findPageQuery.Action = util.NewT(models.Followed)
		findPageQuery.UserID = &id
	case models.Block:
		findPageQuery.Action = util.NewT(models.Blocked)
		findPageQuery.UserID = &id
	case models.Report:
		findPageQuery.Action = util.NewT(models.Reported)
		findPageQuery.UserID = &id
	default:
		appG.Response400(e.User_action_not_supported, e.MsgFlags[e.User_action_not_supported])
		return
	}

	resp := dto.ListUserActionResponse{
		Results:    []*models.SimpleUserAction{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	// Call UserAction find page service
	userActions, pagination, err := services.UserAction.FindPage(&findPageQuery, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = lo.Map(userActions, func(ua *models.UserAction, _ int) *models.SimpleUserAction {
		return ua.Sanitize()
	})

	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Change Password
// @Description	Change Password
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
//
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			body			body		dto.ChangePasswordRequest	true	"Change Password request body"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/me/change-password [POST]
func ChangePassword(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody dto.ChangePasswordRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::User.ChangePassword Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&reqBody)
	if err != nil {
		log.Error("Api::User.ChangePassword validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	user, fErr := services.User.FindByID(appG.GetLoggedUser().ID, &models.FindOneOptions{})
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	updatedUser, uErr := services.User.ChangePassword(&dto.ChangePasswordParams{
		User:        user,
		NewPassword: reqBody.NewPassword,
		OldPassword: reqBody.OldPassword,
	})

	if uErr != nil {
		log.Error("[services.User.ChangePassword]", uErr)
		appG.ResponseAppError(uErr)
		return
	}

	appG.Response200(updatedUser)
}

// @Summary		Create creator
// @Description	Create creator
//
// @Tags			creator
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.CreateCreatorRequest	true	"Create creator input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/creators [POST]
func CreateCreator(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CreateCreatorRequest{}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response(http.StatusBadRequest, e.INVALID_PARAMS, err.Error())
		return
	}

	if validateError := e.HandleValidationError(data); len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return

	}

	var formSession *models.FormSession
	if data.FormSessionID != nil {
		if fSession, fErr := services.FormSession.FindOne(&models.FormSessionQuery{ID: data.FormSessionID}, nil); fErr != nil {
			appG.ResponseAppError(fErr)
			return
		} else {
			if fSession.Status == models.FormSessionsStatusApproved {
				appG.Response400(e.Form_session_approved, "form session already approved")
				return
			}

			if fSession.Status == models.FormSessionsStatusRejected {
				appG.Response400(e.Form_session_rejected, "form session already rejected")
				return
			}

			formSession = fSession
		}
		// find form of formSession to get orgID
		form, fErr := services.Form.FindById(formSession.FormID)
		if fErr != nil {
			appG.Response400(e.Form_not_found, "Form not found:"+fErr.Msg)
			return
		}

		// Get org form
		orgForm, fErr := services.Organization.FindByID(form.OrgID, false, nil)
		if fErr != nil {
			appG.Response400(e.Organization_not_found, "organization not found:"+fErr.Msg)
			return
		}
		// check permission that orgadmin is exactly org admin of org form
		if !currentUser.CanApproveCreatorForm(org.ID, orgForm.ID) {
			appG.Response403(e.INVALID_PARAMS, "Only org admin or sys admin can approve creator")
			return
		}
		// check user is already org creator
		createdUser, _ := models.Repository.User.FindByEmailWithOpts(data.Email, nil)
		if createdUser != nil && createdUser.IsCreator(orgForm.ID) {
			appG.Response400(e.User_already_creator, "The user is already the creator")
			return
		}
		// handle approve creator
		user, _, aErr := services.User.HandleApproveCreator(orgForm, &data)
		if aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}
		if err2 := services.FormSession.UpdateStatus(currentUser, user, org, formSession, models.FormSessionsStatusApproved, ""); err2 != nil {
			log.Error("Update form session failed ", err2)
		}
		appG.Response200(user.Sanitize())
	} else {
		// Case create creator without form session
		user, _, aErr := services.User.HandleApproveCreator(org, &data)
		if aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}

		appG.Response200(user.Sanitize())

	}

}

// @Summary		Create writer
// @Description	Create writer
//
// @Tags			creator
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.CreateWriterRequest	true	"Create writer input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/writers [POST]
func CreateWriter(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.CreateWriterRequest{}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response(http.StatusBadRequest, e.INVALID_PARAMS, err.Error())
		return
	}

	if validateError := e.HandleValidationError(data); len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return

	}

	var formSession *models.FormSession
	if data.FormSessionID != nil {
		if fSession, fErr := services.FormSession.FindOne(&models.FormSessionQuery{ID: data.FormSessionID}, nil); fErr != nil {
			appG.ResponseAppError(fErr)
			return
		} else {
			if fSession.Status == models.FormSessionsStatusApproved {
				appG.Response400(e.Form_session_approved, "form session already approved")
				return
			}

			if fSession.Status == models.FormSessionsStatusRejected {
				appG.Response400(e.Form_session_rejected, "form session already rejected")
				return
			}

			formSession = fSession
		}
		// find form of formSession to get orgID
		form, fErr := services.Form.FindById(formSession.FormID)
		if fErr != nil {
			appG.Response400(e.Form_not_found, "Form not found:"+fErr.Msg)
			return
		}

		// Get org form
		orgForm, fErr := services.Organization.FindByID(form.OrgID, false, nil)
		if fErr != nil {
			appG.Response400(e.Organization_not_found, "organization not found:"+fErr.Msg)
			return
		}
		// check permission that orgadmin is exactly org admin of org form
		if !currentUser.CanApproveWriterForm(org.ID, orgForm.ID) {
			appG.Response403(e.INVALID_PARAMS, "Only org admin or sys admin can approve writer")
			return
		}
		// check user is already org writer
		createdUser, _ := models.Repository.User.FindByEmailWithOpts(data.Email, nil)
		if createdUser != nil && createdUser.IsOrgWriter(orgForm.ID) {
			appG.Response400(e.User_already_writer, "User is writer")
			return
		}
		// handle approve writer
		user, _, aErr := services.User.HandleApproveWriter(orgForm, &data)
		if aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}
		if err2 := services.FormSession.UpdateStatus(currentUser, user, org, formSession, models.FormSessionsStatusApproved, ""); err2 != nil {
			log.Error("Update form session failed ", err2)
		}
		appG.Response200(user.Sanitize())
	} else {
		// Case create creator without form session
		user, _, aErr := services.User.HandleApproveWriter(org, &data)
		if aErr != nil {
			appG.ResponseAppError(aErr)
			return
		}

		appG.Response200(user.Sanitize())

	}

}

// @Summary		Invite creator
// @Description	Invite creator
//
// @Tags			creator
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.InviteCreatorRequest	true	"Invite creator input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/invite [POST]
func InviteCreator(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.InviteCreatorRequest{}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	if !currentUser.CanInviteCreator(org.ID) {
		appG.Response403(e.INVALID_PARAMS, "Only org admin or sys admin can invite creator")
		return
	}

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if validateError := e.HandleValidationError(data); len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return
	}

	createdUser, createErr := models.Repository.User.FindManyMapEmail(data.CreatorEmails)
	if createErr != nil {
		appG.Response404(e.Error_user_not_found, "User not found")
		return
	}

	for _, u := range createdUser {
		if u != nil {
			if u.IsCreator(org.ID) {
				appG.Response400(e.User_already_creator, "The user is already the creator")
				return

			}
		}
	}
	data.AllowFieldsData = appG.GetAllowFieldsData()

	aErr := services.User.HandleInviteCreator(org, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}
	// users = lo.Map[*models.User, *models.User](users, func(u *models.User, _ int) *models.User {
	// 	return u.ToSimple()
	// })

	appG.Response200("Successful")
}

// @Summary		Find Page creator
// @Description	Find Page creator
//
// @Tags			creator
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/creators [GET]
func FindPageCreator(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	if !currentUser.CanFindCreator(org.ID) {
		appG.Response403(e.INVALID_PARAMS, "Only org admin or sys admin can find creator")
		return

	}

	options.Preloads = []string{}

	//bind filter
	query := models.UserQuery{
		IncludeDeleted: util.NewBool(false),
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListCreatorsResponse{
		Results:    []*models.User{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	//if query.CoursePartnerRoleID != nil {
	//	// Get user from table course partner to filter by partner role
	//	coursePartners, err := models.Repository.CoursePartner.FindMany(&models.CoursePartnerQuery{Role: query.CoursePartnerRoleID}, &models.FindManyOptions{})
	//	if err != nil {
	//		appG.Response500(e.ERROR, err.Error())
	//		return
	//	}
	//	userIds := lo.Map[*models.CoursePartner, string](coursePartners, func(c *models.CoursePartner, _ int) string {
	//		return c.PartnerID
	//	})
	//	query.IDIn = &userIds
	//	users, pagination, err := models.Repository.User.FindPage(&query, &options)
	//	if err != nil {
	//		appG.Response500(e.ERROR, err.Error())
	//		return
	//	}
	//	resp.Pagination = pagination
	//	resp.Results = lo.Map[*models.User, *models.User](users, func(u *models.User, _ int) *models.User { return u.ToSimple() })
	//	appG.Response200(resp)
	//	return
	//}

	usrQuery := &models.UserRoleOrgQuery{RoleID: util.NewString(models.PartnerRoleType), OrgID: &org.ID}

	if currentUser.IsSysAdmin() && setting.IsSysAdminSite(appG.GetDomain().Domain) {
		usrQuery.OrgID = nil
	}

	// Get user role org where role is partner
	partnerFromUserRoleOrg, findPartnerErr := models.Repository.UserRoleOrg.FindMany(usrQuery, nil)
	if findPartnerErr != nil {
		appG.Response500(e.Find_user_role_failed, findPartnerErr.Error())
	}

	userIds := lo.Map[*models.UserRoleOrg, string](partnerFromUserRoleOrg, func(c *models.UserRoleOrg, _ int) string {
		return c.UserID
	})
	query.IDIn = &userIds
	users, pagination, err := models.Repository.User.FindPage(&query, &options)
	if err != nil {
		appG.Response500(e.ERROR, err.Error())
		return
	}

	resp.Results = lo.Map[*models.User, *models.User](users, func(u *models.User, _ int) *models.User { return u.Sanitize() })
	resp.Pagination = pagination

	appG.Response200(resp)

}

// @Summary		Remove creator
// @Description	Remove creator
//
// @Tags			creator
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/creators [DELETE]
func DeleteCreator(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	if !currentUser.IsOrgAdmin(org.ID) && !currentUser.IsSysAdmin() {
		appG.Response403(e.INVALID_PARAMS, "Only org admin or sys admin can remove creator")
		return
	}

	var query models.UserQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if len(query.CreatorIds) == 0 {
		appG.Response400(e.INVALID_PARAMS, "creator_ids is required")
		return
	}
	queryUser := models.UserRoleOrgQuery{UserIDIn: query.CreatorIds, RoleID: util.NewString(models.PartnerRoleType), OrgID: query.OrgID}

	// if org admin then just delete in org
	if (currentUser.IsOrgAdmin(org.ID)) || (query.OrgID == nil) {
		query.OrgID = &org.ID
	}

	_, err := models.Repository.UserRoleOrg.DeleteMany(&queryUser, nil)
	if err != nil {
		appG.Response500(e.Error_remove_multiple_user_role, err.Error())
		return
	}

	appG.Response200("success")
}

// @Summary		Accept Invitation creator
// @Description	Create Invitation creator
//
// @Tags			creator
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			Input			body		dto.AcceptInviteRequest	true	"Create creator input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/creators/accept [POST]
func AcceptInvitation(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.AcceptInviteRequest{}
	org := appG.GetOrg()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response(http.StatusBadRequest, e.INVALID_PARAMS, err.Error())
		return
	}

	if validateError := e.HandleValidationError(data); len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return

	}

	data.User = appG.GetLoggedUser()
	createdUser, token, createErr := services.User.HandleAcceptInviteCreator(org, &data)
	if createErr != nil {
		appG.ResponseAppError(createErr)
		return
	}
	mapUser, mapErr := util.StructToMap(createdUser.Sanitize())
	if mapErr != nil {
		appG.Response500(e.Json_encode_error, mapErr.Error())
		return
	}

	mapUser["token"] = token // Token is used to set or reset password

	appG.Response200(mapUser)
}

// @Summary		Find Page user org
// @Description	Find Page user org
//
// @Tags			User
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																																																				models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
//
// @Router			/api/v1/users [GET]
func FindPageUserOrg(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.UserQuery

	//bind filter
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if query.RoleID == nil && query.NotRoleID == nil {
		query.RoleID = util.NewT(models.LearnerRoleType)
	}

	query.OrgID = util.NewString(org.ID)
	//query.Active = util.NewBool(true)
	//query.Blocked = util.NewBool(false)

	resp := dto.ListUserResponse{
		Results:    []*models.SimpleProfile{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	users, pagination, err := services.User.FindPageUserOrg(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = users
	resp.Pagination = pagination

	appG.Response200(resp)
}

// @Summary		Find Page org writer
// @Description	Find Page org writer
//
// @Tags			User
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																																																				models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
//
// @Router			/api/v1/users/blog-managers [GET]
func FindPageBlogManagers(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.UserQuery

	//bind filter
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if query.RoleID == nil && query.NotRoleID == nil {
		appG.Response400(e.INVALID_PARAMS, "Role can not be empty")
		return
	}

	if *query.RoleID != models.OrgWriterRoleType && *query.RoleID != models.OrgEditorRoleType {
		appG.Response403(e.FORBIDDEN, "You can not find other role")
		return

	}

	query.OrgID = util.NewString(org.ID)

	resp := dto.ListUserResponse{
		Results:    []*models.SimpleProfile{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	users, pagination, err := services.User.FindPageUserOrg(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = users
	resp.Pagination = pagination

	appG.Response200(resp)
}

// @Summary		Update user
//
// @Description	Update user
//
// @Tags			User
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			id				path		string					true	"user id"
//
// @Param			Input			body		dto.UpdateUserRequest	true	"Update user input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/admin/users/{id} [PUT]
func UpdateUser(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.UpdateUserRequest{}
	userId := c.Param("id")
	currentUser := appG.GetLoggedUser()

	if !currentUser.CanUpdateUser(appG.GetDomain().Domain) {
		appG.Response403(e.INVALID_PARAMS, "User is not sys admin or not in sysadmin site")
		return
	}

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response400(e.INVALID_PARAMS, nil)
		return
	}

	user, oErr := services.User.FindByID(userId, &models.FindOneOptions{})
	if oErr != nil {
		log.Error("find blog by ID failed", oErr)
		appG.ResponseAppError(oErr)
		return
	}

	user, aErr := services.User.UpdateByAdmin(user, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200(user.Sanitize())
}

// @Summary		Find Page user system
// @Description	Find Page user system
//
// @Tags			User
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																																																				models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
//
// @Router			/api/v1/admin/users [GET]
func FindPageUserSystem(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	if !currentUser.IsSysAdmin() {
		appG.Response400(e.INVALID_PARAMS, "User is not system admin")
		return
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.UserQuery

	//bind filter
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if query.RoleID == nil {
		query.RoleID = util.NewT(models.LearnerRoleType)
	}

	resp := dto.ListUserResponse{
		Results:    []*models.SimpleProfile{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	users, pagination, err := services.User.FindPageUserOrg(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = users
	resp.Pagination = pagination

	appG.Response200(resp)
}

// @Summary		Add or remove role
// @Description	Add or remove role
//
// @Tags			User
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.AddRemoveRoleRequest	true	"Add/Remove role user input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/roles [POST]
func AddRemoveRole(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()
	currentUser := appG.GetLoggedUser()

	data := dto.AddRemoveRoleRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Api::User.Update Profile Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	if data.OrgID == nil {
		data.OrgID = &org.ID
	}

	validateError := e.HandleValidationError(data)
	if len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return
	}
	if err := services.User.HandleAddRemoveUserRole(currentUser, &data); err != nil {
		appG.ResponseAppError(err)
		return
	}
	appG.Response200("success")
}

// @Summary		Invite user
// @Description	Invite user
//
// @Tags			user
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string					true	"Origin"
// @Param			X-referer		header		string					true	"X-referer"
// @Param			Authorization	header		string					true	"Bearer"
//
// @Param			Input			body		dto.InviteUserRequest	true	"Invite user input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/invite [POST]
func InviteUser(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.InviteUserRequest{}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	if !currentUser.IsOrgAdmin(org.ID) && !currentUser.IsSysAdmin() {
		appG.Response403(e.INVALID_PARAMS, "Only org admin or sys admin can invite user")
		return
	}

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if validateError := e.HandleValidationError(data); len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return
	}

	data.AllowFieldsData = appG.GetAllowFieldsData()
	aErr := services.User.HandleInviteUser(org, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response200("success")
}

// @Summary		Accept Invitation creator
// @Description	Create Invitation creator
//
// @Tags			creator
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string							true	"Origin"
// @Param			X-referer		header		string							true	"X-referer"
// @Param			Authorization	header		string							true	"Bearer"
//
// @Param			Input			body		dto.AcceptInviteRequest	true	"Create creator input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/users/accept [POST]
func AcceptInvitationUser(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.AcceptInviteUserRequest{}
	org := appG.GetOrg()

	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response(http.StatusBadRequest, e.INVALID_PARAMS, err.Error())
		return
	}

	if validateError := e.HandleValidationError(data); len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return

	}

	data.User = appG.GetLoggedUser()
	createdUser, token, createErr := services.User.HandleAcceptInvitationUser(org, &data)
	if createErr != nil {
		appG.ResponseAppError(createErr)
		return
	}
	mapUser, mapErr := util.StructToMap(createdUser.Sanitize())
	if mapErr != nil {
		appG.Response500(e.Json_encode_error, mapErr.Error())
		return
	}

	if token != "" {
		mapUser["token"] = token
	}

	appG.Response200(mapUser)
}

// @Summary		Find Page My Blog
// @Description	Find Page My Blog
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string		true	"Origin"
// @Param			X-referer		header					string		true	"X-referer"
// @Param			Authorization	header					string		true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions 	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/blogs/me [GET]
func FindPageMyBlog(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.BlogQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	query.AuthorID = &currentUser.ID
	query.Latest = util.NewBool(true)
	query.OrgID = util.NewString(appG.GetOrg().ID)
	options.Preloads = append(options.Preloads, models.BlogPreloadPublished, models.BlogPreloadUnPublished, models.BlogPreloadReviewing, models.BlogPreloadsHashTag, models.BlogPreloadsCategories, models.BlogPreloadAIInfo, models.BlogPreloadsUser)

	resp := dto.ListBlogsResponse{
		Results:    []*models.SimpleBlog{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	blogs, pagination, err := services.Blog.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map[*models.Blog, *models.SimpleBlog](blogs, func(p *models.Blog, _ int) *models.SimpleBlog {
		return p.Sanitize()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find top user blog view
// @Description	Find top user blog view
//
// @Tags			Blog
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string		true	"Origin"
// @Param			X-referer		header					string		true	"X-referer"
// @Param			Authorization	header					string		true	"Bearer"
//
// @Params			Input query 	models.FindPageOptions 	 false  	"find page options"
//
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
//
// @Router			/api/v1/users/top-blog-viewed [GET]
func FindTopUserBlogView(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.TrackingQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.TopUserBlogViewResponse{
		Results:    []*communicationdto.UserResp{},
		Pagination: communicationdto.NewPagination(options.Page, options.PerPage, 0),
	}

	users, pagination, err := communication.Tracking.FindTopUserBlogView(query.IntoComm(), options.IntoComm())
	if err != nil {
		appG.ResponseAppError(e.NewError500(e.External_call_error, err.Error()))
		return
	}

	if err := services.External.GetPreloadUserActionAndFollowers(appG.GetLoggedUser(), users); err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = users
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find User By ID
// @Description	Find User By ID
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Param		id			path		string	true	"user_id"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/internal-v1/users/:id [GET]
func FindUserByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	user, qErr := services.User.GetUserByID(id)
	if qErr != nil {
		appG.ResponseAppError(qErr)
		return
	}

	appG.Response200(user)
}

// @Summary		Add Default Role For User
// @Description	Add Default Role for user
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/internal-v1/users/roles [POST]
func AddDefaultRole(c *gin.Context) {
	appG := app.Gin{C: c}
	var data dto.RoleOrgRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Api::User.AddDefaultRole Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(&data)
	if err != nil {
		log.Error("Api::User.AddDefaultRole validation failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	ur, qErr := services.Auth.AddDefaultRole(data.UserID, data.OrgID)
	if qErr != nil {
		appG.ResponseAppError(qErr)
		return
	}

	appG.Response200(ur)
}

// @Summary		Find Mod users by org
// @Description	Find Mod users by org
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/admin/users/mod-accounts [POST]
func FindModAccounts(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	if !currentUser.IsSysAdmin() {
		appG.Response400(e.INVALID_PARAMS, "User is not system admin")
		return
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.UserQuery
	//bind filter
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if query.RoleID == nil {
		if currentUser.IsOrgMod(org.ID) {
			query.RoleID = util.NewT(models.OrgModerator2RoleType)
		} else {
			query.RoleIDIn = []string{models.OrgModeratorRoleType, models.OrgModerator2RoleType}
		}
	}

	query.OrgID = util.NewString(org.ID)

	resp := dto.ListUserResponse{
		Results:    []*models.SimpleProfile{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	users, pagination, err := services.User.FindPageUserOrg(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = users
	resp.Pagination = pagination

	appG.Response200(resp)
}

// @Summary		Find User By ID
// @Description	Find User By ID
//
// @Tags		user
// @Accept		json
// @Produce		json
//
// @Param		Origin		header		string	true	"Origin"
// @Param		X-referer	header		string	true	"X-referer"
//
// @Param		id			path		string	true	"user_id"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/internal-v1/users/:id [GET]
func FindUserAddr(c *gin.Context) {
	appG := app.Gin{C: c}

	type UserWalletAddrQuery struct {
		Emails  []string                  `form:"emails" json:"emails"`
		Network *models.BlockchainNetwork `form:"network" json:"network"`
		APIKey  string                    `form:"api-key" json:"api-key"`
	}

	type SimpleWalletResp struct {
		Address string `json:"address"`
		Network string `json:"network"`
	}

	type UserWalletAddrResp struct {
		UserID  string              `json:"user_id"`
		Email   string              `json:"email"`
		Wallets []*SimpleWalletResp `json:"wallets"`
	}

	var query UserWalletAddrQuery
	if err := c.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	if query.APIKey == "" {
		log.Error("Missing Blockchain API key")
		appG.Response401(e.UNAUTHORIZED, "Missing API key")
		return
	}

	// Validate the API key against the system's expected key
	if query.APIKey != setting.AppSetting.BlockchainApiKey {
		log.Error("Invalid Blockchain API key")
		appG.Response401(e.UNAUTHORIZED, "Invalid API key")
		return
	}

	if len(query.Emails) == 0 {
		appG.Response400(e.INVALID_PARAMS, "emails not found")
		return
	}

	if len(query.Emails) > 50 {
		appG.Response400(e.INVALID_PARAMS, "limit find for 50 emails")
	}

	users, err := services.User.FindManySimpleUser(&models.UserQuery{EmailIn: query.Emails}, &models.FindManyOptions{})
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	userIds := lo.Map(users, func(item *models.BasicUserProfile, _ int) string { return item.ID })
	mapUserIDUser := make(map[string]*models.BasicUserProfile)

	for _, u := range users {
		mapUserIDUser[u.ID] = u
	}

	walletQuery := &models.WalletQuery{
		UserIDIn: userIds,
		Network:  query.Network,
		Type:     util.NewT(models.AssetTypeCrypto),
		Default:  util.NewBool(true),
	}

	wallets, wErr := services.Wallet.FindMany(walletQuery, &models.FindManyOptions{})
	if err != nil {
		appG.ResponseAppError(wErr)
		return
	}

	responses := []*UserWalletAddrResp{}
	mapUserIDWallets := lo.GroupBy(wallets, func(item *models.Wallet) string { return item.UserID })
	for userID, wallets := range mapUserIDWallets {
		simpleWallets := lo.Map(wallets, func(item *models.Wallet, _ int) *SimpleWalletResp {
			return &SimpleWalletResp{
				Address: item.Address,
				Network: string(item.Network),
			}
		})
		u, ok := mapUserIDUser[userID]
		email := ""
		if ok {
			email = u.Email
		}
		responses = append(responses, &UserWalletAddrResp{
			UserID:  userID,
			Wallets: simpleWallets,
			Email:   email,
		})
	}

	appG.Response200(responses)
}
