package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// Create commission
//
//	@Summary		Create commission
//	@Description	Create commission
//
//	@Tags			Commission
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			body			body		dto.CommissionRequest	true	"body"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/commissions [POST]
func CreateCommission(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody *dto.CommissionRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("CreateCommission Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(reqBody.CampaignID),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	if checkErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(
		appG.GetLoggedUser(),
		campaign,
	); checkErr != nil {
		appG.ResponseAppError(checkErr)
		return
	}

	reqBody.Org = appG.GetOrg()
	reqBody.User = appG.GetLoggedUser()
	commission, refErr := services.Commission.Create(campaign, reqBody)
	if refErr != nil {
		appG.ResponseAppError(refErr)
		return
	}
	appG.Response200(commission)
	return
}

// Update commission
//
//	@Summary		Update commission
//	@Description	Update commission
//
//	@Tags			Commission
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			body			body		dto.CommissionRequest	true	"body"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/commissions/:id [PUT]
func UpdateCommission(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	var reqBody *dto.CommissionRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("UpdateCommission Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	commission, err := services.Commission.FindOne(&models.CommissionQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	campaign, cErr := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(reqBody.CampaignID),
	}, nil)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if checkErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(
		appG.GetLoggedUser(),
		campaign,
	); checkErr != nil {
		appG.ResponseAppError(checkErr)
		return
	}

	reqBody.Org = appG.GetOrg()
	reqBody.User = appG.GetLoggedUser()
	comm, refErr := services.Commission.Update(commission, reqBody)
	if refErr != nil {
		appG.ResponseAppError(refErr)
		return
	}
	appG.Response200(comm)
	return
}

// Get commission
//
//	@Summary		Get commission
//	@Description	Get commission
//
//	@Tags			Commission
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string	true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string	true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string	true	"Bearer"
//
//	@Param			id				path		string	true	"id"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/commissions/:id [GET]
func GetCommission(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	commission, err := services.Commission.FindOne(&models.CommissionQuery{
		ID: util.NewString(id),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(commission)
	return
}

// Find commission
//
//	@Summary		Find commission
//	@Description	Find commission
//
//	@Tags			Commission
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string					true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string					true	"Bearer"
//
//	@Param			query			query		models.CommissionQuery	true	"query"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/commissions [GET]
func FindCommissions(c *gin.Context) {
	appG := app.Gin{C: c}

	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.CommissionQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	if query.OrgID == nil {
		query.OrgID = util.NewString(appG.GetOrg().ID)
	}

	resp := dto.ListCommissionResponse{
		Results:    []*models.Commission{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	commissions, pagination, cErr := services.Commission.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}
	if len(commissions) > 0 {
		ids := lo.Map(commissions, func(item *models.Commission, _ int) *string {
			return &item.ID
		})
		bonuses, boErr := services.Commission.FindMany(&models.CommissionQuery{
			ParentIDIn: ids,
		}, nil)
		if boErr != nil {
			appG.ResponseAppError(boErr)
			return
		}

		lo.ForEach(commissions, func(item *models.Commission, index int) {
			bns := lo.Filter(bonuses, func(bonus *models.Commission, _ int) bool {
				return bonus.ParentID == item.ID
			})
			item.Bonuses = bns
		})
	}

	resp.Results = commissions
	resp.Pagination = pagination
	appG.Response200(resp)
}

// Delete many commission
//
//	@Summary		Delete many commission
//	@Description	Delete many commission
//
//	@Tags			Commission
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string						true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer		header		string						true	"X-referrer"	default(openedu101.com)
//	@Param			Authorization	header		string						true	"Bearer"
//
//	@Param			query			query		dto.CommissionDeleteRequest	true	"query"
//
//	@Success		201				{object}	app.Response
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/commissions [DELETE]
func DeleteCommission(c *gin.Context) {
	appG := app.Gin{C: c}
	var data *dto.CommissionDeleteRequest
	if err := appG.C.ShouldBindQuery(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	validateError := e.HandleValidationError(data)
	if len(validateError) > 0 {
		appG.Response400(e.INVALID_PARAMS, validateError)
		return
	}

	campaign, err := services.AffiliateCampaign.FindOne(&models.AffiliateCampaignQuery{
		ID: util.NewString(data.CampaignID),
	}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	if checkErr := services.AffiliateCampaign.CanUpdateAffiliateCampaign(
		appG.GetLoggedUser(),
		campaign,
	); checkErr != nil {
		appG.ResponseAppError(checkErr)
		return
	}

	refErr := services.Commission.Delete(data)
	if refErr != nil {
		appG.ResponseAppError(refErr)
		return
	}
	appG.Response200("success")
}
