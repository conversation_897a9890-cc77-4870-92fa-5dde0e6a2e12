package v1

import (
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CreateQuizSubmission
//
//	@Summary		Create quiz submission
//	@Description	Create quiz submission
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string							true	"Origin"
//	@Param			X-referrer	header		string							true	"X-referrer"
//
//	@Param			Input		body		dto.CreateQuizSubmissionRequest	true	"Create quiz submission input"
//
//	@Success		200			{object}	app.Response
//	@Success		201			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quiz-submissions [POST]
func CreateQuizSubmission(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.CreateQuizSubmissionRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	quiz, appErr := services.Quiz.FindByID(req.QuizID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	course, appErr := services.Course.FindById(req.CourseID, false, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	user := appG.GetLoggedUser()
	if user == nil {
		appG.Response401(e.UNAUTHORIZED, "Unauthorized: cannot get user from context")
		return
	}

	existingSubmission, appErr := services.QuizSubmission.FindOne(
		&models.QuizSubmissionQuery{
			UserID: &user.ID,
			QuizID: &quiz.ID,
			Status: util.NewT(models.QuizSubmissionStatusInProgress),
		},
		&models.FindOneOptions{
			Preloads: []string{util.AnswersField, util.AnswersQuestionField},
		},
	)
	if appErr != nil && appErr.ErrCode != e.Quiz_submission_not_found {
		appG.ResponseAppError(appErr)
		return
	}

	if existingSubmission != nil {
		done := false
		if existingSubmission.Status == models.QuizSubmissionStatusDone {
			done = true
		} else if existingSubmission.DeadlineAt != 0 {
			now := time.Now()
			if now.After(time.UnixMilli(int64(existingSubmission.DeadlineAt))) {
				existingSubmission.Status = models.QuizSubmissionStatusDone
				if _, appErr = services.QuizSubmission.Update(existingSubmission); appErr != nil {
					appG.ResponseAppError(appErr)
					return
				}
				done = true
			}
		}
		if !done {
			appG.Response200(existingSubmission.Sanitize(quiz))
			return
		}
	}

	if appErr = services.Quiz.CheckUserCanDoQuiz(user, quiz); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	createdSubmission, appErr := services.QuizSubmission.Create(user, quiz, course)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response201(createdSubmission.Sanitize(quiz))
}

// GetQuizSubmission
//
//	@Summary		Get quiz submission
//	@Description	Get quiz submission
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Quiz ID"
//
//	@Success		200			{object}	app.Response
//	@Success		201			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quiz-submissions/{id} [GET]
func GetQuizSubmission(c *gin.Context) {
	appG := app.Gin{C: c}
	submissionID := c.Param(util.IDParamKey)
	submission, appErr := services.QuizSubmission.FindByID(submissionID, &models.FindOneOptions{
		Preloads: []string{util.AnswersField, util.AnswersQuestionField},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	quiz, appErr := services.Quiz.FindByID(submission.QuizID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(submission.Sanitize(quiz))
}

// GetCurrentQuestionBySubmission
//
//	@Summary		Get current question for quiz submission
//	@Description	Get current question for quiz submission
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Quiz ID"
//
//	@Success		200			{object}	app.Response
//	@Success		201			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quiz-submissions/{id}/questions/current [GET]
func GetCurrentQuestionBySubmission(c *gin.Context) {
	appG := app.Gin{C: c}
	submissionID := c.Param(util.IDParamKey)
	submission, appErr := services.QuizSubmission.FindByID(submissionID, &models.FindOneOptions{
		Preloads: []string{"Answers"},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if submission.Status == models.QuizSubmissionStatusDone {
		appG.Response400(e.Quiz_submission_already_done, "This submission is already done")
		return
	}

	user := appG.GetLoggedUser()
	if user == nil {
		appG.Response401(e.UNAUTHORIZED, "Unauthorized: cannot get user from context")
		return
	}

	if user.ID != submission.UserID {
		appG.Response400(e.Quiz_submission_owner_required, "This submission is not yours")
		return
	}

	answer, question, appErr := services.QuizSubmission.FindCurrentQuestion(submission)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(dto.GetQuestionSubmissionResponse{
		HasNextQuestion:      answer.Order < len(submission.Answers),
		StartAt:              answer.StartAt,
		CurrentQuestionIndex: answer.Order,
		Question:             question.Sanitize(),
	})
}

// SubmitAnswerForSubmission
//
//	@Summary		Submit answer for quiz submission
//	@Description	Submit answer for quiz submission
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string								true	"Origin"
//	@Param			X-referrer	header		string								true	"X-referrer"
//
//	@Param			id			path		string								true	"Quiz ID"
//	@Param			Input		body		dto.QuizQuestionSubmissionRequest	true	"Submit answer for submission input"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quiz-submissions [POST]
func SubmitAnswerForSubmission(c *gin.Context) {
	appG := app.Gin{C: c}
	var req dto.QuizQuestionSubmissionRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	submissionID := c.Param(util.IDParamKey)
	submission, appErr := services.QuizSubmission.FindByID(submissionID, &models.FindOneOptions{
		Preloads: []string{"Answers"},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if submission.Status == models.QuizSubmissionStatusDone {
		appG.Response400(e.Quiz_submission_already_done, "This submission is already done")
		return
	}

	user := appG.GetLoggedUser()
	if user == nil {
		appG.Response401(e.UNAUTHORIZED, "Unauthorized: cannot get user from context")
		return
	}

	if user.ID != submission.UserID {
		appG.Response400(e.Quiz_submission_owner_required, "This submission is not yours")
		return
	}

	_, appErr = services.QuizSubmission.SubmitAnswer(submission, &req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response201("success")
}

// GetQuizLeaderboards
//
//	@Summary		Get quiz leaderboards
//	@Description	Get quiz leaderboards
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Quiz UID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes/{id}/leaderboards [GET]
func GetQuizLeaderboards(c *gin.Context) {
	appG := app.Gin{C: c}
	quizUID := c.Param(util.IDParamKey)
	// Check if quiz uid is valid
	_, appErr := services.Quiz.FindOne(&models.QuizQuery{UID: &quizUID}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	var userID string
	if user := appG.GetLoggedUser(); user != nil {
		userID = user.ID
	}

	var query dto.GetQuizSubmissionRanksRequest
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind request query failed: "+err.Error())
		return
	}

	if query.Top == 0 {
		query.Top = 10
	}

	if query.Top > 100 {
		appG.Response400(e.INVALID_PARAMS, "Maximum value for query parameter top is 100")
		return
	}

	ranks, appErr := services.QuizSubmission.FindRanksByQuizUID(quizUID, userID, query.Top)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(lo.Map(ranks, func(rank *models.QuizSubmissionRank, index int) *models.SimpleQuizSubmissionRank {
		return rank.ToSimple()
	}))
}

// GetQuizSubmissionsByQuizUID
//
//	@Summary		Get quiz leaderboards
//	@Description	Get quiz leaderboards
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"
//	@Param			X-referrer	header		string					true	"X-referrer"
//
//	@Param			pagination	query		models.FindPageOptions	true	"Pagination query"
//	@Param			id			path		string					true	"Form ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes/{id}/submissions [GET]
func GetQuizSubmissionsByQuizUID(c *gin.Context) {
	appG := app.Gin{C: c}
	quizUID := c.Param(util.IDParamKey)
	_, appErr := services.Quiz.FindOne(&models.QuizQuery{UID: &quizUID}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	var query models.QuizSubmissionQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	query.QuizUID = &quizUID

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	options.Preloads = []string{"Answers"}

	submissions, pagination, fErr := services.QuizSubmission.FindPage(&query, &options)
	if fErr != nil {
		appG.ResponseAppError(fErr)
		return
	}

	resp := dto.ListQuizSubmissionsResponse{
		Results:    []*models.SimpleQuizSubmission{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}
	resp.Results = lo.Map(submissions, func(s *models.QuizSubmission, _ int) *models.SimpleQuizSubmission {
		return s.ToSimple()
	})
	resp.Pagination = pagination
	appG.Response200(resp)
}

// UpdateQuizSubmissionStatus
//
//	@Summary		Update form status
//	@Description	Update form status
//
//	@Tags			form
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string									true	"Origin"
//	@Param			X-referrer	header		string									true	"X-referrer"
//
//	@Param			Input		body		dto.UpdateQuizSubmissionStatusRequest	true	"Update form status input"
//
//	@Param			id			path		string									true	"Form ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/forms/{id}/status [PUT]
func UpdateQuizSubmissionStatus(c *gin.Context) {
	appG := app.Gin{C: c}
	req := dto.UpdateQuizSubmissionStatusRequest{}
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	submissionID := c.Param(util.IDParamKey)
	submission, appErr := services.QuizSubmission.FindByID(submissionID, &models.FindOneOptions{
		Preloads: []string{"Answers"},
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if appG.GetLoggedUser() == nil || appG.GetLoggedUser().ID != submission.UserID {
		appG.Response400(e.Quiz_submission_owner_required, "Quiz submission is not yours")
		return
	}

	newStatus := models.QuizSubmissionStatus(req.Status)
	switch newStatus {
	case models.QuizSubmissionStatusDone:
		if submission.Status == newStatus {
			appG.Response200(submission)
			return
		}
	case models.QuizSubmissionStatusInProgress:
		appG.Response400(e.Quiz_submission_invalid_status, "You can update status to in-progress")
		return
	default:
		appG.Response400(e.Quiz_submission_invalid_status, "Invalid quiz submission status")
		return
	}

	if _, appErr = services.QuizSubmission.UpdateStatus(submission, newStatus); appErr != nil {
		appG.Response500(e.Quiz_submission_update_failed, fmt.Sprintf("Update quiz submission status failed: %v", appErr))
		return
	}

	appG.Response200(submission)
}

// GetQuizSubmissionSummary
// Find Quiz Submission Summary
//
//	@Summary		Find quiz submission summary
//	@Description	Find quiz submission summary
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quiz-submissions/summary [GET]
func GetQuizSubmissionSummary(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	quizIDs := c.QueryArray("id")

	if len(quizIDs) == 0 {
		appG.Response400(e.INVALID_PARAMS, "Query parameter id is required")
		return
	}

	count, appErr := services.QuizSubmission.GetSubmissionSummary(user, quizIDs)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(count)
}
