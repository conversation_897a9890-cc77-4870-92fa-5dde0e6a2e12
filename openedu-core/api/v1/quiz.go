package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// CreateQuiz
//
//	@Summary		Create quiz
//	@Description	Create quiz
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string			true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer	header		string			true	"X-referrer"	default(openedu101.com)
//
//	@Param			Input		body		dto.QuizRequest	true	"Create quiz input"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes [POST]
func CreateQuiz(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.QuizRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSO<PERSON> failed: "+err.Error())
		return
	}

	data.OrgID = appG.GetOrg().ID
	quiz, quizRelation, appErr := services.Quiz.Create(appG.GetLoggedUser(), &data)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	quiz, appErr = services.Quiz.FindByID(quiz.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response201(models.NewQuizWithRelation(quiz, quizRelation))
}

// FindQuizDetail
//
//	@Summary		Find quiz detail
//	@Description	Find quiz detail
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Quiz ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes/{id} [GET]
func FindQuizDetail(c *gin.Context) {
	appG := app.Gin{C: c}
	quizID := c.Param(util.IDParamKey)
	quiz, cErr := services.Quiz.FindByID(quizID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if !quiz.CanViewQuiz(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Quiz_view_need_permission, "Need permission for view")
		return
	}

	appG.Response200(quiz)
}

// UpdateQuiz
//
//	@Summary		Update quiz
//	@Description	Update quiz
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string			true	"Origin"
//	@Param			X-referrer	header		string			true	"X-referrer"
//
//	@Param			Input		body		dto.QuizRequest	true	"Update quiz input"
//
//	@Param			id			path		string			true	"Quiz ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes/{id} [PUT]
func UpdateQuiz(c *gin.Context) {
	appG := app.Gin{C: c}
	quizRequest := dto.QuizRequest{}
	quizID := c.Param(util.IDParamKey)
	if err := appG.BindAndValidateJSON(&quizRequest); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	quiz, cErr := services.Quiz.FindByID(quizID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if !quiz.CanUpdateQuiz(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Quiz_update_need_permission, "Need permission for update")
		return
	}

	quizRequest.ID = quizID
	quiz, quizRelation, uErr := services.Quiz.Update(&quizRequest)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	quiz, cErr = services.Quiz.FindByID(quizID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}
	appG.Response200(models.NewQuizWithRelation(quiz, quizRelation))
}

// DuplicateQuiz
//
//	@Summary		Duplicate quiz
//	@Description	Duplicate quiz
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string			true	"Origin"
//	@Param			X-referrer	header		string			true	"X-referrer"
//
//	@Param			Input		body		dto.QuizRequest	true	"Update quiz input"
//
//	@Param			id			path		string			true	"Quiz ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes/{id}/duplicate [POST]
func DuplicateQuiz(c *gin.Context) {
	appG := app.Gin{C: c}
	quizRelationRequest := dto.QuizRelationRequest{}
	quizID := c.Param(util.IDParamKey)
	if err := appG.BindAndValidateJSON(&quizRelationRequest); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}
	quiz, cErr := services.Quiz.FindByID(quizID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	user := appG.GetLoggedUser()
	if user == nil {
		appG.Response401(e.UNAUTHORIZED, "Unauthorized: cannot get user from context")
		return
	}

	if !quiz.CanDuplicateQuiz(user, appG.GetOrg()) {
		appG.Response400(e.Quiz_duplicate_need_permission, "Need permission for update")
		return
	}

	duplicatedQuiz, quizRelation, uErr := services.Quiz.Duplicate(user, quiz, &quizRelationRequest)
	if uErr != nil {
		appG.ResponseAppError(uErr)
		return
	}

	duplicatedQuiz, cErr = services.Quiz.FindByID(duplicatedQuiz.ID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}
	appG.Response201(models.NewQuizWithRelation(duplicatedQuiz, quizRelation))
}

// FindQuizzesByLessonContent
//
//	@Summary		Get quizzes by lesson content
//	@Description	Get quizzes by lesson content
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Lesson ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/lessons/{id}/quizzes [GET]
func FindQuizzesByLessonContent(c *gin.Context) {
	appG := app.Gin{C: c}
	lessonContentID := c.Param(util.IDParamKey)
	query := models.LessonContentQuery{
		ID:             util.NewString(lessonContentID),
		IncludeDeleted: util.NewBool(false),
	}
	options := models.FindOneOptions{Preloads: []string{models.FilesField}}
	lessonContent, appErr := services.LessonContent.FindOne(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	quizRelations, appErr := services.Quiz.FindQuizRelationByLessonContent(lessonContent)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response201(quizRelations)
}

// DeleteQuiz
//
//	@Summary		Delete quiz
//	@Description	Delete quiz
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Param			id			path		string	true	"Quiz ID"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes/{id} [DELETE]
func DeleteQuiz(c *gin.Context) {
	appG := app.Gin{C: c}
	quizID := c.Param(util.IDParamKey)
	quiz, cErr := services.Quiz.FindByID(quizID)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	if !quiz.CanDeleteQuiz(appG.GetLoggedUser(), appG.GetOrg()) {
		appG.Response400(e.Quiz_delete_need_permission, "Need permission for delete")
		return
	}

	if appErr := services.Quiz.Delete(quiz); appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// GetLatestPassedSubmissionByQuizUID
// Find latest passed quiz submission by quiz UID
//
//	@Summary		Find latest passed quiz submission by quiz UID
//	@Description	Find latest passed quiz submission by quiz UID
//
//	@Tags			quiz
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string	true	"Origin"
//	@Param			X-referrer	header		string	true	"X-referrer"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/quizzes/by-uid/{quizUID}/submissions/latest-passed [GET]
func GetLatestPassedSubmissionByQuizUID(c *gin.Context) {
	appG := app.Gin{C: c}
	quizUID := c.Param(util.IDParamKey)
	user := appG.GetLoggedUser()
	if user == nil {
		appG.Response401(e.UNAUTHORIZED, "Unauthorized: cannot get user from context")
		return
	}

	submission, appErr := services.QuizSubmission.FindLatestPassedByQuizUID(quizUID, user.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(submission)
}
