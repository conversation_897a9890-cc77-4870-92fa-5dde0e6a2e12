package v1

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"openedu-core/pkg/app"
	"openedu-core/pkg/setting"
	"os"
	"strconv"
)

var CommitHash string

func CheckHealth(c *gin.Context) {
	appG := app.Gin{C: c}
	buildName := setting.ServerSetting.BuildName
	port := setting.ServerSetting.HttpPort
	portStr := os.Getenv("PORT")
	if portStr != "" {
		port, _ = strconv.Atoi(portStr)
	}
	appG.C.JSON(200, fmt.Sprintf("api v1.0.0, build %s, commit %s running on port: %d", buildName, CommitHash, port))
	appG.C.Next()
	return
}
