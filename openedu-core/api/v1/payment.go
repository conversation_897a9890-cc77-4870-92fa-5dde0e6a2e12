package v1

import (
	"net/http"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/payment"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// @Summary		Find By ID
// @Description	Find By ID
//
// @Tags			payment
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
//
// @Param			id				path		string	true	"payment id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/payments/{id} [GET]
func FindPaymentByID(c *gin.Context) {
	appG := app.Gin{C: c}
	paymentID := c.Param("id")

	payment, pErr := services.Payment.FindByID(paymentID, false, &models.FindOneOptions{})
	if pErr != nil {
		log.Error("find payment by ID failed", pErr)
		appG.ResponseAppError(pErr)
		return
	}

	appG.Response200(payment.Sanitize())
}

// @Summary		Find Page
// @Description	Find Page
//
// @Tags			payment
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string	true	"Origin"
// @Param			X-referer		header					string	true	"X-referer"
// @Param			Authorization	header					string	true	"Bearer"
// @Params			Input query 	models.FindPageOptions 					 false  	"find page options"
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
// @Router			/api/v1/payments [GET]
func FindPagePayment(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}
	//bind filter
	query := models.PaymentQuery{
		IncludeDeleted: util.NewBool(false),
		UserID:         &currentUser.ID,
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListPaymentResponse{
		Results:    []*models.SimplePayment{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	payments, pagination, err := services.Payment.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map[*models.Payment, *models.SimplePayment](payments, func(p *models.Payment, _ int) *models.SimplePayment { return p.Sanitize() })
	resp.Pagination = pagination
	appG.Response200(resp)
}

func SepayWebhookHandle(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.SepayWebhookRequest{}

	if err := appG.BindAndValidateJSON(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind JSON failed: "+err.Error())
		return
	}

	stage := os.Getenv("STAGE")
	if stage == "" {
		stage = "local"
	}
	code := strings.ToUpper(data.Code)
	stage = strings.ToUpper(stage)
	if (setting.IsProduction() && len(code) <= 17) || strings.HasSuffix(code, stage) {
		aErr := services.Payment.Verify(payment.PaymentServiceSepay, &data)
		if aErr != nil {
			if aErr.StatusCode == http.StatusOK {
				appG.Response200(util.NewString("ok"))
				return
			}
			appG.Response500(e.Sepay_webhook_failed, aErr)
			return
		}
	} else {
		appG.Response400(-1, "wrong environment")
		return
	}

	appG.Response200(util.NewString("ok"))
}

// @Summary		Find Page
// @Description	Find Page
//
// @Tags			payment
// @Accept			json
// @Produce		json
//
// @Param			Origin			header					string	true	"Origin"
// @Param			X-referer		header					string	true	"X-referer"
// @Param			Authorization	header					string	true	"Bearer"
// @Params			Input query		models.FindPageOptions 					 false  	"find page options"
// @Success		200				{object}				app.Response
// @Failure		400				{object}				app.Response
// @Failure		500				{object}				app.Response
// @Router			/api/v1/admin/payments [GET]
func FindPagePaymentByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{}
	//bind filter
	query := models.PaymentQuery{
		IncludeDeleted: util.NewBool(false),
	}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	resp := dto.ListPaymentResponse{
		Results:    []*models.SimplePayment{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	payments, pagination, err := services.Payment.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = lo.Map[*models.Payment, *models.SimplePayment](payments, func(p *models.Payment, _ int) *models.SimplePayment { return p.Sanitize() })
	resp.Pagination = pagination
	appG.Response200(resp)
}
