package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// CreateHtmlTemplate
//
//	@Summary		Create a html template
//	@Description	Create a html template
//
//	@Tags			html_template
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin		header		string					true	"Origin"		default(http://localhost:8000)
//	@Param			X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
//	@Param			Input		body		dto.HtmlTemplateRequest	true	"Create html template input"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/html-templates [POST]
func CreateHtmlTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	orgID := appG.GetOrg().ID

	data := dto.HtmlTemplateRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("CreateHtmlTemplate::Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	data.Enable = util.NewBool(false)
	httpTemplate, aErr := services.HtmlTemplate.CheckPermAndCreateHTMLTemplates(&data, user, orgID)
	if aErr != nil {
		log.Error("CheckPermCreateHTMLTemplates", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	templateCreated, aErr := services.HtmlTemplate.FindByID(httpTemplate.ID)
	if aErr != nil {
		log.Error("create failed", aErr)
		appG.ResponseAppError(aErr)
		return
	}

	if templateCreated.Type == models.CertificateTemplate {
		isDefault := false
		if data.IsDefault != nil {
			isDefault = *data.IsDefault
		}

		aErr := services.HtmlTemplate.UpdateTemplateConfig(templateCreated.ID, orgID, isDefault)
		if aErr != nil {
			log.ErrorWithAlertf("Update certificate template to system config failed: %v", aErr)
			appG.ResponseAppError(aErr)
			return
		}
	}

	appG.Response200(templateCreated)
}

// DeleteHtmlTemplateById
//
// @Summary		Delete a html template
// @Description	Delete a html template
//
// @Tags		html_template
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101dev.com)
//
// @Param		id			path		string		true	"template id"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/html-templates/{id} [DELETE]
func DeleteHtmlTemplateById(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")
	org := appG.GetOrg()

	template, appErr := services.HtmlTemplate.FindByID(id)
	if appErr != nil {
		log.Error("find html template by ID failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	appErr = services.HtmlTemplate.CheckPermDeleteHTMLTemplates(template, user, org)
	if appErr != nil {
		log.Error("CheckPermDeleteHTMLTemplates", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}

// FindPageHtmlTemplate
//
// @Summary		Find Page
// @Description	Find Page
//
// @Tags		html_template
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referrer		header		string					true	"X-referrer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Params		options 		query 		models.FindPageOptions 	false  	"Find page options"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/html-templates [GET]
func FindPageHtmlTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	org := appG.GetOrg()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	options.Preloads = []string{models.FilesField}
	options.Sort = []string{models.CreateAtASC}
	resp := dto.ListHtmlTemplateResponse{
		Results:    []*models.HtmlTemplate{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	htmlTemplates, pagination, err := services.HtmlTemplate.FindPage(org, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = htmlTemplates
	resp.Pagination = pagination
	appG.Response200(resp)
}

// UpdateHtmlTemplate
//
// @Summary		Update a html template
// @Description	Update a html template
//
// @Tags		html_template
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string					true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string					true	"X-referrer"	default(openedu101dev.com)
//
// @Param		id			path		string		            true	"template id"
// @Param		Input		body		dto.UpdateHtmlTemplateRequest	true	"Update html template input"
//
// @Success		200			{object}	app.Response
// @Failure		400			{object}	app.Response
// @Failure		500			{object}	app.Response
// @Router			/api/v1/html-templates/{id} [PUT]
func UpdateHtmlTemplate(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	id := c.Param("id")
	orgID := appG.GetOrg().ID

	template, appErr := services.HtmlTemplate.FindByID(id)
	if appErr != nil {
		log.Error("find html template by ID failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	data := dto.UpdateHtmlTemplateRequest{}
	if err := appG.BindAndValidateJSON(&data); err != nil {
		log.Error("UpdateHtmlTemplate::Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	appErr = services.HtmlTemplate.CheckPermUpdateHTMLTemplates(template, user, orgID)
	if appErr != nil {
		log.Error("CheckPermUpdateHTMLTemplates", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	appErr = services.HtmlTemplate.Update(data, template)
	if appErr != nil {
		log.Error("Update html template failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if template.Type == models.CertificateTemplate {
		isDefault := false
		if data.IsDefault != nil {
			isDefault = *data.IsDefault
		}

		aErr := services.HtmlTemplate.UpdateTemplateConfig(template.ID, orgID, isDefault)
		if aErr != nil {
			log.ErrorWithAlertf("Update certificate template to system config failed: %v", aErr)
			appG.ResponseAppError(aErr)
			return
		}
	}

	appG.Response200("success")
}

// FindPageCertificateTemplateForCourse
//
// @Summary		Find Page certificate template
// @Description	Find Page certificate template
//
// @Tags		html_template
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referrer		header		string					true	"X-referrer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Params		options 		query 		models.FindPageOptions 	false  	"Find page options"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/courses/{id}/html-templates [GET]
func FindPageCertificateTemplateForCourse(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	course, appErr := services.Course.FindOne(&models.CourseQuery{ID: util.NewString(id)}, &models.FindOneOptions{
		Preloads: []string{models.OwnerField},
	})
	if appErr != nil {
		log.Error("find course by ID for certificate failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if !user.IsSysAdmin() {
		if perErr := services.Course.CanUpdateCourse(course, user, models.CoursePerUpdate); perErr != nil {
			appG.ResponseAppError(perErr)
			return
		}
	}

	options.Preloads = []string{models.FilesField}
	options.Sort = append(options.Sort, models.CreateAtASC)
	resp := dto.ListHtmlTemplateResponse{
		Results:    []*models.HtmlTemplate{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	htmlTemplates, pagination, err := services.HtmlTemplate.FindPageCertificateTemplateForCourse(course, &options, appG.GetOrg())
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = htmlTemplates
	resp.Pagination = pagination
	appG.Response200(resp)
}

// GetDetailTemplateByID
//
// @Summary		GetDetailTemplateByID
// @Description	GetDetailTemplateByID
//
// @Tags		html_template
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string					true	"Origin"
// @Param		X-referrer		header		string					true	"X-referrer"
// @Param		Authorization	header		string					true	"Bearer"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/html-templates/{id} [GET]
func GetDetailTemplateByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	htmlTemplates, err := services.HtmlTemplate.FindByID(id)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	appG.Response200(htmlTemplates)
}

// EnableCertificateTemplateForOrg
//
// @Summary		Enable Certificate Template For Org
// @Description	Enable Certificate Template For Org
//
// @Tags		certificate
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		id				path		string		true	"course id"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/html-templates/{id}/enable [POST]
func EnableCertificateTemplateForOrg(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	// validate template id input
	template, appErr := services.HtmlTemplate.FindByID(id)
	if appErr != nil {
		log.Error("find html template by ID failed", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	if !user.IsOrgAdmin(org.ID) {
		appG.Response403(e.FORBIDDEN, "Not org admin")
		return
	}

	appErr = services.HtmlTemplate.EnableCertificateTemplateForOrg(template.ID, org, user)
	if appErr != nil {
		log.Error("enable certificate template for org failed: ", appErr)
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200("success")
}
