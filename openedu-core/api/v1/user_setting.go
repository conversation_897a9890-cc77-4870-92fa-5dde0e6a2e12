package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

func CreateManyUserSetting(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody *dto.CreateSettingRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	settings, cErr := services.UserSetting.CreateMany(appG.GetOrg(), appG.GetLoggedUser(), reqBody)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}
	appG.Response200(settings)
	return
}

func UpdateManyUserSetting(c *gin.Context) {
	appG := app.Gin{C: c}
	var reqBody *dto.CreateSettingRequest
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		log.Error("Api::Auth.FinalizeSignUp Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	settings, cErr := services.UserSetting.UpdateMany(reqBody)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}
	appG.Response200(settings)
	return
}

func DeleteManyUserSetting(c *gin.Context) {
	appG := app.Gin{C: c}
	data := dto.DeleteUserSettingRequest{}
	if err := appG.C.ShouldBindQuery(&data); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	cErr := services.UserSetting.DeleteMany(data.IDs)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}
	appG.Response200("success")
	return
}

func FindUserSetting(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions

	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}
	var query models.UserSettingQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}
	//if query.OrgID == nil {
	//	query.OrgID = util.NewString(appG.GetOrg().ID)
	//}

	resp := dto.ListUserSettingResponse{
		Results:    []*models.UserSetting{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	query.UserID = util.NewString(appG.GetLoggedUser().ID)
	settings, pagination, cErr := services.UserSetting.FindPage(&query, &options)
	if cErr != nil {
		appG.ResponseAppError(cErr)
		return
	}

	resp.Results = settings
	resp.Pagination = pagination
	appG.Response200(resp)
}

// GetMySettingProfile
//
//	@Summary		Get my setting profile
//	@Description	Get my setting profile
//
//	@Tags			setting
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string	true			"Origin"
//	@Param			X-referrer		header		string	true			"X-referrer"
//	@Param          type       	    query       string  true    		"type of setting ex:courses"
//	@Param			pagination	    query		models.FindPageOptions	true	"Pagination query"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		401			{object}	app.Response
//	@Failure		404			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/users/me/settings [GET]
func GetMySettingProfile(c *gin.Context) {
	appG := app.Gin{C: c}

	loggedUser := appG.GetLoggedUser()
	if loggedUser == nil {
		appG.Response404(e.Error_user_not_found, "User not found, require login")
		return
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.UserSettingQuery
	if err := appG.C.ShouldBindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page query failed: "+err.Error())
		return
	}

	var typeSetting string
	if query.Type == nil {
		appG.Response400(e.INVALID_PARAMS, "Required type of setting")
		return
	} else {
		typeSetting = *query.Type
	}

	options.Sort = []string{models.CreateAtDESC}
	switch typeSetting {
	case string(models.UserSettingTypeCertificates):
		options.Preloads = []string{models.FilesField}
		settings, appErr := services.UserSetting.BuildCertificateSetting(&loggedUser.ID, appG.GetOrg(), &options)
		if appErr != nil {
			appG.ResponseAppError(appErr)
			return
		}

		appG.Response200(settings)
		return

	case string(models.UserSettingTypeCourses):
		settings, appErr := services.UserSetting.BuildCourseSetting(loggedUser, appG.GetOrg(), &options)
		if appErr != nil {
			appG.ResponseAppError(appErr)
			return
		}

		appG.Response200(settings)
		return

	case string(models.UserSettingTypeBlogs):
		settings, appErr := services.UserSetting.BuildBlogSetting(&loggedUser.ID, appG.GetOrg(), &options)
		if appErr != nil {
			appG.ResponseAppError(appErr)
			return
		}

		appG.Response200(settings)
		return

	default:
		appG.Response400(e.INVALID_PARAMS, "Type of setting not supported")
		return
	}
}

// UpdateMySettingProfile
//
//	@Summary		Update my setting profile
//	@Description	Update my setting profile
//
//	@Tags			setting
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referrer		header		string	true	"X-referrer"
//	@Param          input       	body        string  true    "UpdateSettingProfileRequest"
//
//	@Success		200			{object}	app.Response
//	@Failure		400			{object}	app.Response
//	@Failure		401			{object}	app.Response
//	@Failure		404			{object}	app.Response
//	@Failure		500			{object}	app.Response
//	@Router			/api/v1/users/me/settings [PUT]
func UpdateMySettingProfile(c *gin.Context) {
	appG := app.Gin{C: c}

	loggedUser := appG.GetLoggedUser()
	if loggedUser == nil {
		appG.Response404(e.Error_user_not_found, "User not found, require login")
		return
	}

	data := dto.UpdateSettingProfileRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	err := util.Validator.Struct(data)
	if err != nil {
		log.Error("validation failed", err)
		appG.Response500(e.INVALID_PARAMS, err.Error())
		return
	}

	switch data.Type {
	case models.UserSettingTypeCertificates, models.UserSettingTypeBlogs, models.UserSettingTypeCourses:
		appErr := services.UserSetting.UpsertSettingProfile(loggedUser.ID, appG.GetOrg(), &data)
		if appErr != nil {
			appG.ResponseAppError(appErr)
			return
		}

	default:
		appG.Response400(e.INVALID_PARAMS, "Type of user setting profile not supported")
		return
	}

	appG.Response200("success")
}
