package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CreateSponsorWallet
//
// @Summary		Create sponsor wallet for creator
// @Description	Create sponsor wallet for gas fee sponsorship (creator role required)
//
// @Tags		sponsor-wallet
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Param		request			body		dto.CreateSponsorWalletRequest	true	"Create sponsor wallet request"
//
// @Success		201				{object}	app.Response{data=dto.CreateSponsorWalletResponse}
// @Failure		400				{object}	app.Response
// @Failure		403				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sponsor-wallets [POST]
func CreateSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	var req dto.CreateSponsorWalletRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	// Check if user has creator role
	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		appG.Response500(e.Error_user_role_org_find_failed, "Failed to get user roles: "+err.Error())
		return
	}

	// Filter active roles only
	activeRoles := lo.Filter(userRoles, func(item *models.UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0
	})

	// Check if user has creator role in any organization
	hasCreatorRole := lo.ContainsBy(activeRoles, func(item *models.UserRoleOrg) bool {
		return models.IsCreatorRole(item.RoleID)
	})

	if !hasCreatorRole {
		appG.Response403(e.Error_auth_invalid_permission, "Only creators can create sponsor wallets")
		return
	}

	// Find user's BASE wallet
	baseWallet, appErr := services.Wallet.FindOne(&models.WalletQuery{
		UserID:   &user.ID,
		Currency: util.NewT(models.CryptoCurrencyETH),
		Network:  util.NewT(models.BlockchainNetworkBASE),
	}, &models.FindOneOptions{})

	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	// Check if wallet has blockchain_wallet_id (is synced)
	if baseWallet.BlockchainWalletID == "" {
		appG.Response400(e.WalletNotSynced, "BASE wallet not yet synced with blockchain")
		return
	}

	// Set request data
	req.User = user
	req.Wallet = baseWallet
	req.Org = org

	// Create sponsor wallet
	response, appErr := services.Wallet.CreateSponsorWallet(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response201(response)
}

// GetSponsorWallet
//
// @Summary		Get sponsor wallet information
// @Description	Get sponsor wallet details and balance (creator role required)
//
// @Tags		sponsor-wallet
// @Accept		json
// @Produce		json
//
// @Param		Origin			header		string		true	"Origin"
// @Param		X-referrer		header		string		true	"X-referrer"
// @Param		Authorization	header		string		true	"Bearer"
//
// @Success		200				{object}	app.Response{data=dto.GetSponsorWalletResponse}
// @Failure		400				{object}	app.Response
// @Failure		403				{object}	app.Response
// @Failure		404				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/sponsor-wallets [GET]
func GetSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()

	// Check if user has creator role
	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		appG.Response500(e.Error_user_role_org_find_failed, "Failed to get user roles: "+err.Error())
		return
	}

	// Filter active roles only
	activeRoles := lo.Filter(userRoles, func(item *models.UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0
	})

	// Check if user has creator role in any organization
	hasCreatorRole := lo.ContainsBy(activeRoles, func(item *models.UserRoleOrg) bool {
		return models.IsCreatorRole(item.RoleID)
	})

	if !hasCreatorRole {
		appG.Response403(e.Error_auth_invalid_permission, "Only creators can access sponsor wallets")
		return
	}

	// Get sponsor wallet info
	response, appErr := services.Wallet.GetSponsorWallet(user.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}
