package v1

import (
	"fmt"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// Supported Sponsor Wallet Networks
var SupportedSponsorWalletNetworks = map[models.BlockchainNetwork][]models.Currency{
	models.BlockchainNetworkBASE: {models.CryptoCurrencyETH},
	// models.BlockchainNetworkPolygon: {models.CryptoCurrencyETH, models.CryptoCurrencyUSDT},
	// models.BlockchainNetworkNEAR:    {models.CryptoCurrencyNEAR},
}

// checkUserHasCreatorRole checks if user has creator role
func checkUserHasCreatorRole(userID string, orgID string) (bool, error) {
	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(userID)
	if err != nil {
		return false, err
	}

	return models.ExactlyOrgCreatorRoles(userRoles, orgID), nil
}

// CreateSponsorWallet creates a new sponsor wallet
//
//	@Summary		Create sponsor wallet
//	@Description	Create a new sponsor wallet for gas fee sponsoring
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string							true	"Origin"
//	@Param			X-referrer		header		string							true	"X-referrer"
//	@Param			Authorization	header		string							true	"Bearer"
//	@Param			currency		query		string							false	"Currency (default: ETH)"
//	@Param			network			query		string							false	"Network (default: base). Can also be specified in request body."
//	@Param			request			body		dto.CreateSponsorWalletRequest	true	"Create sponsor wallet request"
//	@Success		200				{object}	app.ResponseT[dto.CreateSponsorWalletResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [POST]
func CreateSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	var req dto.CreateSponsorWalletRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	// Get currency from query params with default
	currencyParam := c.DefaultQuery(models.SponsorWalletCurrencyParam, string(models.DefaultSponsorWalletCurrency))
	currency := models.Currency(currencyParam)

	// Get network - prioritize body request, fallback to query param, then default
	var network models.BlockchainNetwork
	if req.Network != "" {
		network = req.Network
	} else {
		networkParam := c.DefaultQuery(models.SponsorWalletNetworkParam, string(models.DefaultSponsorWalletNetwork))
		network = models.BlockchainNetwork(networkParam)
	}

	// Validate network and currency combination
	supportedCurrencies, networkSupported := SupportedSponsorWalletNetworks[network]
	if !networkSupported {
		appG.Response400(e.INVALID_PARAMS, fmt.Sprintf(models.ErrMsgUnsupportedNetwork, string(network)))
		return
	}

	if !lo.Contains(supportedCurrencies, currency) {
		appG.Response400(e.INVALID_PARAMS, fmt.Sprintf("Currency %s is not supported on %s network for sponsor wallets", string(currency), string(network)))
		return
	}

	// Check if user has creator role
	hasCreatorRole, err := checkUserHasCreatorRole(user.ID, org.ID)
	if err != nil {
		appG.Response500(e.Find_user_role_failed, "Failed to check user role: "+err.Error())
		return
	}
	if !hasCreatorRole {
		appG.Response403(e.FORBIDDEN, models.ErrMsgCreatorRoleRequired)
		return
	}

	// Get user's wallet for specified currency and network
	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{
		UserID:   &user.ID,
		Currency: util.NewT(currency),
		Network:  util.NewT(network),
	}, &models.FindOneOptions{})
	if appErr != nil {
		appG.Response404(e.WalletNotFound, fmt.Sprintf(models.ErrMsgWalletNotFound, string(currency), string(network)))
		return
	}

	// Check if wallet is synced with blockchain
	if wallet.BlockchainWalletID == "" {
		appG.Response400(e.WalletNotSynced, models.ErrMsgWalletNotSynced)
		return
	}

	// Set user context (keep network from body or set from param)
	req.User = user
	req.Org = org
	req.Wallet = wallet
	if req.Network == "" {
		req.Network = network
	}

	response, appErr := services.Wallet.CreateSponsorWallet(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}

// GetSponsorWallet gets sponsor wallet information for the current user
//
//	@Summary		Get sponsor wallet
//	@Description	Get sponsor wallet information for the current user
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referrer		header		string	true	"X-referrer"
//	@Param			Authorization	header		string	true	"Bearer"
//	@Param			currency		query		string	false	"Currency (default: ETH)"
//	@Param			network			query		string	false	"Network (default: base)"
//	@Success		200				{object}	app.ResponseT[dto.GetSponsorWalletResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		404				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [GET]
func GetSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	// Check if user has creator role
	hasCreatorRole, err := checkUserHasCreatorRole(user.ID, org.ID)
	if err != nil {
		appG.Response500(e.Find_user_role_failed, "Failed to check user role: "+err.Error())
		return
	}
	if !hasCreatorRole {
		appG.Response403(e.FORBIDDEN, models.ErrMsgCreatorAccessRequired)
		return
	}

	// Get currency and network from query params with defaults
	currencyParam := c.DefaultQuery(models.SponsorWalletCurrencyParam, string(models.DefaultSponsorWalletCurrency))
	networkParam := c.DefaultQuery(models.SponsorWalletNetworkParam, string(models.DefaultSponsorWalletNetwork))

	currency := models.Currency(currencyParam)
	network := models.BlockchainNetwork(networkParam)

	// Validate network and currency combination
	supportedCurrencies, networkSupported := SupportedSponsorWalletNetworks[network]
	if !networkSupported {
		appG.Response400(e.INVALID_PARAMS, fmt.Sprintf(models.ErrMsgUnsupportedNetwork, string(network)))
		return
	}

	if !lo.Contains(supportedCurrencies, currency) {
		appG.Response400(e.INVALID_PARAMS, fmt.Sprintf("Currency %s is not supported on %s network for sponsor wallets", string(currency), string(network)))
		return
	}

	response, appErr := services.Wallet.GetSponsorWallet(user.ID, currency, network)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}
