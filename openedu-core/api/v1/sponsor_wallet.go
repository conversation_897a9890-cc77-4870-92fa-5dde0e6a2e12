package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// CreateSponsorWallet creates a new sponsor wallet
//
//	@Summary		Create sponsor wallet
//	@Description	Create a new sponsor wallet for gas fee sponsoring
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string							true	"Origin"
//	@Param			X-referrer		header		string							true	"X-referrer"
//	@Param			Authorization	header		string							true	"Bearer"
//	@Param			request			body		dto.CreateSponsorWalletRequest	true	"Create sponsor wallet request"
//	@Success		200				{object}	app.ResponseT[dto.CreateSponsorWalletResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [POST]
func CreateSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	var req dto.CreateSponsorWalletRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	// Set user context
	req.User = user
	req.Org = org

	response, appErr := services.Wallet.CreateSponsorWallet(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}

// GetSponsorWallet gets sponsor wallet information for the current user
//
//	@Summary		Get sponsor wallet
//	@Description	Get sponsor wallet information for the current user
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referrer		header		string	true	"X-referrer"
//	@Param			Authorization	header		string	true	"Bearer"
//	@Success		200				{object}	app.ResponseT[dto.GetSponsorWalletResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		404				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [GET]
func GetSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()

	response, appErr := services.Wallet.GetSponsorWallet(user.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}

// GetSponsorWalletByNetwork gets sponsor wallet information for specific network
//
//	@Summary		Get sponsor wallet by network
//	@Description	Get sponsor wallet information for the current user on specific network
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referrer		header		string	true	"X-referrer"
//	@Param			Authorization	header		string	true	"Bearer"
//	@Param			network			query		string	true	"Blockchain network (BASE, NEAR)"
//	@Success		200				{object}	app.ResponseT[dto.GetSponsorWalletResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		404				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets/network [GET]
func GetSponsorWalletByNetwork(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()

	networkStr := c.Query("network")
	if networkStr == "" {
		appG.Response400(e.INVALID_PARAMS, "Network parameter is required")
		return
	}

	network := models.BlockchainNetwork(networkStr)
	if network != models.BlockchainNetworkBASE && network != models.BlockchainNetworkNEAR {
		appG.Response400(e.INVALID_PARAMS, "Invalid network. Supported: BASE, NEAR")
		return
	}

	// Get currency for the network
	var currency models.Currency
	switch network {
	case models.BlockchainNetworkBASE:
		currency = models.CryptoCurrencyETH
	case models.BlockchainNetworkNEAR:
		currency = models.CryptoCurrencyNEAR
	default:
		currency = models.CryptoCurrencyNEAR
	}

	response, appErr := services.Wallet.GetSponsorWalletWithNetwork(user.ID, currency, network)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}

// SyncSponsorWallet syncs sponsor wallet transaction from blockchain
//
//	@Summary		Sync sponsor wallet transaction
//	@Description	Sync sponsor wallet transaction from blockchain service
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string							true	"Origin"
//	@Param			X-referrer		header		string							true	"X-referrer"
//	@Param			Authorization	header		string							true	"Bearer"
//	@Param			request			body		dto.SponsorWalletSyncRequest	true	"Sync sponsor wallet request"
//	@Success		200				{object}	app.ResponseT[models.Transaction]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets/sync [POST]
func SyncSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}

	var req dto.SponsorWalletSyncRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	transaction, appErr := services.Wallet.SyncSponsorWallet(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(transaction)
}
