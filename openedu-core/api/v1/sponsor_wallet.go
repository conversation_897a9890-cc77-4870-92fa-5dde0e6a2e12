package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/gin-gonic/gin"
)

// checkUserHasCreatorRole checks if user has creator role
func checkUserHasCreatorRole(userID string, orgID string) (bool, error) {
	userRoles, err := models.Repository.UserRoleOrg.FindByUserId(userID)
	if err != nil {
		return false, err
	}

	return models.ExactlyOrgCreatorRoles(userRoles, orgID), nil
}

// CreateSponsorWallet creates a new sponsor wallet
//
//	@Summary		Create sponsor wallet
//	@Description	Create a new sponsor wallet for gas fee sponsoring (base network only)
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string							true	"Origin"
//	@Param			X-referrer		header		string							true	"X-referrer"
//	@Param			Authorization	header		string							true	"Bearer"
//	@Param			request			body		dto.CreateSponsorWalletRequest	true	"Create sponsor wallet request (network must be 'base')"
//	@Success		200				{object}	app.ResponseT[dto.CreateSponsorWalletResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [POST]
func CreateSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	var req dto.CreateSponsorWalletRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	// Validate network - only base is supported
	if req.Network != models.BlockchainNetworkBASE {
		appG.Response400(e.INVALID_PARAMS, "Only 'base' network is supported for sponsor wallets")
		return
	}

	// Check if user has creator role
	hasCreatorRole, err := checkUserHasCreatorRole(user.ID, org.ID)
	if err != nil {
		appG.Response500(e.Find_user_role_failed, "Failed to check user role: "+err.Error())
		return
	}
	if !hasCreatorRole {
		appG.Response403(e.FORBIDDEN, "Only creators can create sponsor wallets")
		return
	}

	// Get user's BASE wallet
	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{
		UserID:   &user.ID,
		Currency: util.NewT(models.CryptoCurrencyETH),
		Network:  util.NewT(models.BlockchainNetworkBASE),
	}, &models.FindOneOptions{})
	if appErr != nil {
		appG.Response404(e.WalletNotFound, "BASE wallet not found. Please create a BASE wallet first.")
		return
	}

	// Check if wallet is synced with blockchain
	if wallet.BlockchainWalletID == "" {
		appG.Response400(e.WalletNotSynced, "BASE wallet not yet synced with blockchain. Please wait for sync to complete.")
		return
	}

	// Set user context
	req.User = user
	req.Org = org
	req.Wallet = wallet

	response, appErr := services.Wallet.CreateSponsorWallet(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}

// GetSponsorWallet gets sponsor wallet information for the current user
//
//	@Summary		Get sponsor wallet
//	@Description	Get sponsor wallet information for the current user (BASE network only)
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referrer		header		string	true	"X-referrer"
//	@Param			Authorization	header		string	true	"Bearer"
//	@Success		200				{object}	app.ResponseT[dto.GetSponsorWalletResponse]
//	@Failure		400				{object}	app.ResponseT[app.ErrorData]
//	@Failure		404				{object}	app.ResponseT[app.ErrorData]
//	@Failure		500				{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [GET]
func GetSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	user := appG.GetLoggedUser()
	org := appG.GetOrg()

	// Check if user has creator role
	hasCreatorRole, err := checkUserHasCreatorRole(user.ID, org.ID)
	if err != nil {
		appG.Response500(e.Find_user_role_failed, "Failed to check user role: "+err.Error())
		return
	}
	if !hasCreatorRole {
		appG.Response403(e.FORBIDDEN, "Only creators can access sponsor wallets")
		return
	}

	response, appErr := services.Wallet.GetSponsorWallet(user.ID)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(response)
}
