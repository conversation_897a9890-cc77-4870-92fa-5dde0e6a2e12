package v1

import (
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"openedu-core/services"

	"github.com/samber/lo"

	"github.com/gin-gonic/gin"
)

// @Summary		Create approval request
// @Description	Create approval request
//
// @Tags			Approval
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string						true	"Origin"
// @Param			X-referer		header		string						true	"X-referer"
// @Param			Authorization	header		string						true	"Bearer"
//
// @Param			Input			body		dto.CreateApprovalRequest	true	"Create approval input"
//
// @Success		201				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/approvals [POST]
func CreateRequestApproval(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()

	data := dto.CreateApprovalRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}

	if validateErr := e.HandleValidationError(data); validateErr != nil {
		appG.Response400(e.INVALID_PARAMS, validateErr)
		return

	}
	// Check if user has permission to create approval publish request
	if permErr := services.Approval.CanRequest(currentUser, &data, org); permErr != nil {
		appG.ResponseAppError(permErr)
		return
	}

	approval, aErr := services.Approval.Create(org, currentUser, &data)
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	appG.Response201(approval.Sanitize())
}

// @Summary		Approve request
// @Description	Approve request
//
// @Tags			Approval
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string				true	"Origin"
// @Param			X-referer		header		string				true	"X-referer"
// @Param			Authorization	header		string				true	"Bearer"
//
// @Param			Input			body		dto.ApprovalRequest	true	"Approve approval input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/approvals/{id}/approve [POST]
func ApproveRequest(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	id := c.Param("id")

	data := dto.ApprovalRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	// Get Approval
	approval, aErr := services.Approval.FindOne(&models.ApprovalQuery{ID: &id}, &models.FindOneOptions{Preloads: []string{models.RequesterField}})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	if approval.Status == models.ApprovalStatusApprove {
		appG.Response400(e.Approval_approved, "request approved")
		return
	}

	if approval.Status == models.ApprovalStatusCancel {
		appG.Response400(e.Approval_cancelled, "request cancelled")
		return
	}

	// Check if user has permission to approve approval
	data.Org = appG.GetOrg()
	if permErr := services.Approval.CanApprove(approval, currentUser, &data); permErr != nil {
		appG.ResponseAppError(permErr)
		return
	}

	// Handle approval
	approval.Note = data.Note
	if data.Value == nil {
		approval.ApproveValue = ""
	} else {
		approval.ApproveValue = *data.Value
	}
	approval.Files = lo.If(data.Files == nil, []*models.File{}).Else(data.Files)
	if handleErr := services.Approval.Approve(currentUser, approval); handleErr != nil {
		appG.ResponseAppError(handleErr)
		return
	}

	appG.Response200(approval.Sanitize())
}

// @Summary		Approve request
// @Description	Approve request
//
// @Tags			Approval
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string				true	"Origin"
// @Param			X-referer		header		string				true	"X-referer"
// @Param			Authorization	header		string				true	"Bearer"
//
// @Param			Input			body		dto.ApprovalFeedback	true	"Feedback approval input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/approvals/{id}/feedback [POST]
func SendApprovalFeedBack(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	id := c.Param("id")

	data := dto.ApprovalFeedback{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	// Get Approval
	approval, aErr := services.Approval.FindOne(&models.ApprovalQuery{ID: &id}, &models.FindOneOptions{})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	if approval.Status == models.ApprovalStatusApprove {
		appG.Response400(e.Approval_approved, "request approved")
		return
	}

	if approval.Status == models.ApprovalStatusCancel {
		appG.Response400(e.Approval_cancelled, "request cancelled")
		return
	}

	// Check if user has permission to approve approval
	if permErr := services.Approval.CanModifyApproval(approval, currentUser); permErr != nil {
		appG.ResponseAppError(permErr)
		return
	}

	data.IsAdminFeedback = true
	// Handle approval
	if handleErr := services.Approval.ApprovalSendFeedback(approval, currentUser, &data); handleErr != nil {
		appG.ResponseAppError(handleErr)
		return
	}

	appG.Response200(approval)
}

// @Summary		Reject request
// @Description	Reject request
//
// @Tags			Approval
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string				true	"Origin"
// @Param			X-referer		header		string				true	"X-referer"
// @Param			Authorization	header		string				true	"Bearer"
//
// @Param			Input			body		dto.ApprovalRequest	true	"Approve approval input"
//
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
// @Router			/api/v1/approvals/{id}/reject [POST]
func RejectRequest(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	id := c.Param("id")

	data := dto.ApprovalRequest{}
	if err := c.ShouldBindJSON(&data); err != nil {
		log.Error("Bind JSON failed", err)
		appG.Response400(e.INVALID_PARAMS, err.Error())
		return
	}
	// Get Approval
	approval, aErr := services.Approval.FindOne(&models.ApprovalQuery{ID: &id}, &models.FindOneOptions{})
	if aErr != nil {
		appG.ResponseAppError(aErr)
		return
	}

	if approval.Status == models.ApprovalStatusApprove {
		appG.Response400(e.Approval_approved, "request approved")
		return
	}

	if approval.Status == models.ApprovalStatusCancel {
		appG.Response400(e.Approval_cancelled, "request cancelled")
		return
	}

	// Check if user has permission to approve approval
	data.Org = appG.GetOrg()
	if permErr := services.Approval.CanApprove(approval, currentUser, &data); permErr != nil {
		appG.ResponseAppError(permErr)
		return
	}

	// Handle approval
	approval.Note = data.Note
	if handleErr := services.Approval.Reject(currentUser, approval); handleErr != nil {
		appG.ResponseAppError(handleErr)
		return
	}

	appG.Response200(approval.Sanitize())
}

//	@Summary		Find Page
//	@Description	Find Page
//
//	@Tags			Approval
//	@Accept			json
//	@Produce		json
//
//	@Param			Origin			header		string	true	"Origin"
//	@Param			X-referer		header		string	true	"X-referer"
//	@Param			Authorization	header		string	true	"Bearer"
//	@Params			Input query 																																																																																																																models.FindPageOptions 			 false  	"find page options"
//	@Success		200				{object}	app.Response
//	@Failure		400				{object}	app.Response
//	@Failure		500				{object}	app.Response
//
// @Router			/api/v1/users/me/approvals [GET]
func FindMyApproval(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.ApprovalQuery

	//bind filter
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	query.RequesterID = util.NewString(currentUser.ID)
	query.OrgID = util.NewString(org.ID)
	if query.Type == nil && len(query.TypeIn) <= 0 {
		query.Type = util.NewT(models.ApproveTypePublishOrg)
	}

	// Handle query

	resp := dto.ListApprovalResponse{
		Results:    []*models.Approval{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, "Requester", "Org", "ConfirmBy", "Entity")

	approvals, pagination, err := services.Approval.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = approvals
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find Page
// @Description	Find Page
//
// @Tags			Approval
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																																																models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
//
// @Router			/api/v1/approvals [GET]
func FindApproval(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	if !services.Approval.CanGetListApprovals(currentUser, org) {
		appG.Response403(e.FORBIDDEN, "Permission denied")
		return
	}

	var query models.ApprovalQuery

	//bind filter
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	query.OrgID = util.NewString(org.ID)
	if query.Type == nil && len(query.TypeIn) <= 0 {
		query.Type = util.NewT(models.ApproveTypePublishOrg)
	}

	if *query.Type == models.ApproveTypePublishRoot {
		query.OrgID = nil
	}

	// Handle query

	resp := dto.ListApprovalResponse{
		Results:    []*models.Approval{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, models.RequesterField, models.OrgField, models.ConfirmByField, models.EntityField)

	approvals, pagination, err := services.Approval.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = approvals
	resp.Pagination = pagination
	appG.Response200(resp)
}

// @Summary		Find Page by admin
// @Description	Find Page by admin
//
// @Tags			Approval
// @Accept			json
// @Produce		json
//
// @Param			Origin			header		string	true	"Origin"
// @Param			X-referer		header		string	true	"X-referer"
// @Param			Authorization	header		string	true	"Bearer"
// @Params			Input query 																																																																																																																models.FindPageOptions 			 false  	"find page options"
// @Success		200				{object}	app.Response
// @Failure		400				{object}	app.Response
// @Failure		500				{object}	app.Response
//
// @Router			/api/v1/admin/approvals [GET]
func FindApprovalByAdmin(c *gin.Context) {
	appG := app.Gin{C: c}
	currentUser := appG.GetLoggedUser()
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	if !currentUser.IsSysAdmin() && !setting.IsSysAdminSite(appG.GetDomain().Domain) {
		appG.Response403(e.FORBIDDEN, "Permission denied")
		return
	}

	var query models.ApprovalQuery

	//bind filter
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}

	// Handle query
	resp := dto.ListApprovalResponse{
		Results:    []*models.Approval{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	options.Preloads = append(options.Preloads, models.RequesterField, models.OrgField, models.ConfirmByField, models.EntityField)

	approvals, pagination, err := services.Approval.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}
	resp.Results = approvals
	resp.Pagination = pagination
	appG.Response200(resp)
}
