package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"openedu-core/dto"
	"openedu-core/models"
	"openedu-core/pkg/app"
	"openedu-core/pkg/e"
	"openedu-core/pkg/util"
	"openedu-core/services"
)

// GetMyWallets
// @Summary		Get my wallets
// @Description	Get my wallets
//
// @Tags		User
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Success		200				{object}	app.ResponseT[[]models.Wallet]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/users/me/wallets [GET]
func GetMyWallets(c *gin.Context) {
	appG := app.Gin{C: c}
	loggedUser := appG.GetLoggedUser()
	if loggedUser == nil {
		appG.Response404(e.Error_user_not_found, "User not found, require login")
		return
	}
	wallets, err := services.Wallet.GetWalletsByUserID(loggedUser.ID)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp := make([]*models.Wallet, len(wallets))
	copy(resp, wallets)
	walletType := models.AssetType(c.Query("type"))
	switch walletType {
	case models.AssetTypeFiat:
		resp = lo.Filter(resp, func(wallet *models.Wallet, _ int) bool {
			return wallet.Type == models.AssetTypeFiat
		})

	case models.AssetTypeCrypto:
		resp = lo.Filter(resp, func(wallet *models.Wallet, _ int) bool {
			return wallet.Type == models.AssetTypeCrypto
		})

	case models.AssetTypePoint:
		resp = lo.Filter(resp, func(wallet *models.Wallet, _ int) bool {
			return wallet.Type == models.AssetTypePoint
		})
	}

	if currencyType := models.Currency(c.Query("currency")); currencyType != "" {
		resp = lo.Filter(resp, func(wallet *models.Wallet, _ int) bool {
			return wallet.Currency == currencyType
		})
	}

	if network := models.BlockchainNetwork(c.Query("network")); network != "" {
		resp = lo.Filter(resp, func(wallet *models.Wallet, _ int) bool {
			return wallet.Network == network
		})
	}

	appG.Response200(resp)
}

// GetMyTransactions
// @Summary		Get my transactions
// @Description	Get my transactions
//
// @Tags		User
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Success		200				{object}	app.ResponseT[dto.ListTransactionResponse]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/users/me/transactions [GET]
func GetMyTransactions(c *gin.Context) {
	appG := app.Gin{C: c}
	loggedUser := appG.GetLoggedUser()
	if loggedUser == nil {
		appG.Response404(e.Error_user_not_found, "User not found, require login")
		return
	}

	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind find page options failed: "+err.Error())
		return
	}

	query := models.TransactionQuery{}
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind query failed: "+err.Error())
		return
	}
	query.UserID = util.NewString(loggedUser.ID)

	resp := dto.ListTransactionResponse{
		Results:    []*models.Transaction{},
		Pagination: models.NewPagination(options.Page, options.PerPage, 0),
	}

	transactions, pagination, err := services.Transaction.FindPage(&query, &options)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	resp.Results = transactions
	resp.Pagination = pagination

	appG.Response200(resp)
}

// Request2Withdraw
// @Summary		Request to withdraw
// @Description	Request to withdraw
//
// @Tags		User
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.WalletWithdrawalRequest		true	"Body"
// @Param		id			path		string							true	"Wallet ID"
//
// @Success		200				{object}	app.ResponseT[string]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/users/me/wallets/:id/withdraw [POST]
func Request2Withdraw(c *gin.Context) {
	appG := app.Gin{C: c}
	loggedUser := appG.GetLoggedUser()
	org := appG.GetOrg()
	id := c.Param(models.PathParamKeyID)

	var req dto.CreateWithdrawRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	if req.Amount.LessThanOrEqual(decimal.Zero) {
		appG.Response400(e.INVALID_PARAMS, "Invalid amount: amount must be greater than 0")
		return
	}

	wallet, err := services.Wallet.FindOne(&models.WalletQuery{ID: util.NewString(id)}, nil)
	if err != nil {
		appG.ResponseAppError(err)
		return
	}

	req.Wallet = wallet
	req.User = loggedUser
	req.Org = org
	reqErr := services.Wallet.RequestToWithdraw(&req)
	if reqErr != nil {
		appG.ResponseAppError(reqErr)
		return
	}

	appG.Response200(app.MsgSuccess)
}

// WithdrawFromWallet
// @Summary		Withdraw money/tokens from a wallet
// @Description	Withdraw money/tokens from a wallet
//
// @Tags		user
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		Input		body		dto.WalletWithdrawalRequest		true	"Body"
// @Param		id			path		string							true	"Wallet ID"
//
// @Success		200				{object}	app.ResponseT[models.Transaction]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/users/me/wallets/:id/withdrawals [POST]
func WithdrawFromWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	walletID := c.Param(models.PathParamKeyID)
	org := appG.GetOrg()
	user := appG.GetLoggedUser()

	var req dto.WalletWithdrawalRequest
	if err := appG.BindAndValidateJSON(&req); err != nil {
		appG.Response400(e.INVALID_PARAMS, "Bind and validate JSON failed: "+err.Error())
		return
	}

	if req.Amount.LessThanOrEqual(decimal.Zero) {
		appG.Response400(e.INVALID_PARAMS, "Invalid amount: amount must be greater than 0")
		return
	}

	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{ID: util.NewString(walletID)}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	if wallet.UserID != user.ID {
		appG.Response400(e.WalletOwnerRequired, "Wallet owner required")
		return
	}

	transaction, appErr := services.Transaction.Withdraw(&dto.CreateTransactionRequest{
		OrgID:      org.ID,
		Wallet:     wallet,
		ToAddress:  req.ToAddress,
		Network:    req.Network,
		Amount:     req.Amount,
		Currency:   req.Currency,
		ContractID: req.ContractID,
		EntityType: models.UserModelName,
		EntityID:   user.ID,
		Note:       req.Note,
		IsMainnet:  req.IsMainnet,
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(transaction)
}

// ClaimEarningsIntoWallet
// @Summary		Claim earning from the wallet
// @Description	Claim earning from the wallet
//
// @Tags		User
// @Accept		json
// @Produce		json
// @Security	BearerAuth
//
// @Param		Origin		header		string		true	"Origin"		default(http://localhost:8000)
// @Param		X-referrer	header		string		true	"X-referrer"	default(openedu101.com)
//
// @Param		id			path		string							true	"Wallet ID"
//
// @Success		200				{object}	app.ResponseT[models.Transaction]
// @Failure		400				{object}	app.ResponseT[app.ErrorData]
// @Failure		500				{object}	app.ResponseT[app.ErrorData]
// @Router		/api/v1/wallets/:id/claim-earning [POST]
func ClaimEarningsIntoWallet(c *gin.Context) {
	appG := app.Gin{C: c}
	walletID := c.Param(models.PathParamKeyID)
	org := appG.GetOrg()
	user := appG.GetLoggedUser()

	wallet, appErr := services.Wallet.FindOne(&models.WalletQuery{ID: util.NewString(walletID)}, nil)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	transaction, appErr := services.Transaction.ClaimEarning(&dto.ClaimEarningsRequest{
		Org:    org,
		User:   user,
		Wallet: wallet,
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(transaction)
}
