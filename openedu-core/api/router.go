package api

import (
	"net/http"
	v1 "openedu-core/api/v1"
	"openedu-core/middleware"
	"openedu-core/models"
	"openedu-core/pkg/metrics"
	"openedu-core/pkg/setting"
	"os"

	"github.com/gin-contrib/gzip"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// InitRouter initialize routing information
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(middleware.GinLogger())
	r.Use(middleware.GinRecovery())
	r.Use(gzip.Gzip(gzip.DefaultCompression, gzip.WithExcludedPaths([]string{"/metrics"})))

	// Health check
	r.GET("/api/v1/health", v1.CheckHealth)

	r.Use(middleware.CORSMiddleware())
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	r.<PERSON>aticFS("/uploads", http.Dir("runtime/upload"))

	// Metrics
	stage := os.Getenv("STAGE")
	if stage == "" {
		stage = "local"
	}
	metricsBuilder := metrics.NewMetricsBuilder(setting.AppSetting.Name, stage).
		WithSystemMetrics().
		WithSqlDbMetrics(models.DB, setting.DatabaseSetting.Type)

	metricsMiddleware := metricsBuilder.Build()
	r.Use(metricsMiddleware)
	r.GET("/metrics", gin.WrapH(promhttp.Handler()))

	publicApiV1 := r.Group("")
	{
		certificate := publicApiV1.Group("/certificates")
		certificate.GET("/:id/image", v1.GetCertificateImage)
		// Resource usage
		usage := publicApiV1.Group("usages")
		usage.POST("/ai", v1.AddAiUsage) // POST /usages/ai
		// User Wallet Addr
		user := publicApiV1.Group("users")
		user.GET("/wallets", v1.FindUserAddr)
	}

	apiV1 := r.Group("/api/v1")
	//WS

	webhook := apiV1.Group("/webhook")
	webhook.POST("/bunny", v1.BunnyVideoWebhook)

	apiV1.Use(middleware.BeforeInterceptor())

	{

		// Admin system
		admin := apiV1.Group("/admin")
		admin.POST("/organizations", v1.CreateOrg)
		admin.PUT("/organizations/:id", v1.UpdateOrg)
		admin.GET("/organizations/:id", v1.FindOrgByID)
		admin.GET("/organizations", v1.FindPageOrgs)

		// Organization
		organization := apiV1.Group("/organizations")
		organization.POST("/validate", v1.CheckOrgValidation)
		organization.GET("/test-schema", v1.TestSchema)

		// Creators
		admin.POST("/creators", v1.CreateCreator)
		admin.POST("/creators/invite", v1.InviteCreator)
		admin.GET("/creators", v1.FindPageCreator)
		admin.DELETE("/creators", v1.DeleteCreator)

		// Writer
		admin.POST("/writers", v1.CreateWriter)

		// Order
		admin.GET("/orders", v1.FindPageOrdersByAdmin)
		// Order item
		admin.GET("/order-items", v1.FindPageOrderItemByAdmin)
		// Payment
		admin.GET("/payments", v1.FindPagePaymentByAdmin)
		//Coupon
		admin.GET("/coupons", v1.FindPageCouponsByPartner)
		//CouponHistory
		admin.GET("/coupon-histories", v1.FindPageCouponHistoryByAdmin)
		admin.POST("/system-configs", v1.CreateSystemConfig)
		admin.PUT("/system-configs/:id", v1.UpdateSystemConfig)
		admin.DELETE("/system-configs/:id", v1.DeleteSystemConfig)
		//User
		admin.GET("/users/mod-accounts", v1.FindModAccounts)
		admin.GET("/users/:id/actions", v1.FindPageUserActionByAdmin)

		//System User
		admin.GET("/users", v1.FindPageUserSystem)
		admin.PUT("/users/:id", v1.UpdateUser)
		//Approval
		admin.GET("/approvals", v1.FindApprovalByAdmin)
		//Blog
		admin.GET("/blogs", v1.FindPageBlogByAdmin)
		admin.PUT("/blogs/:id", v1.UpdateBlogByAdmin)
		admin.PUT("/blogs/:id/toggle-pin", v1.TogglePinBlogByAdmin)

		apiV1.POST("/auth", v1.AuthSocial)
		auth := apiV1.Group("/auth")
		auth.Use(middleware.ExtractAllowedFields())
		{
			// Auth
			auth.POST("/register", v1.Register)          // POST /api/v1/auth/register
			auth.POST("/login", v1.Login)                // POST /api/v1/auth/login
			auth.POST("/link", v1.LinkSnsAccount)        // POST /api/v1/auth/link
			auth.POST("/refresh-token", v1.RefreshToken) // POST /api/v1/auth/refresh-token
			auth.POST("/resend-mail", v1.ResendMail)
			auth.POST("/forgot-password", v1.ForgotPassword)
			auth.POST("/reset-password", v1.ConfirmResetPassword)
			auth.GET("/verify", v1.Verify)
			auth.POST("/external-register", v1.ExternalRegister)
			auth.POST("/set-password", v1.SetPassword)
			auth.POST("/verify-otp", v1.VerifyOTP) // POST /api/v1/auth/verify-otp
			auth.POST("/resend-otp", v1.ResendOTP) // POST /api/v1/auth/resend-otp
		}

		auth.GET("/public", v1.GuestApi)
		auth.GET("/learner", v1.LearnerAPI)
		auth.GET("/instructor", v1.InstructorAPI)
		auth.GET("/org-mod", v1.OrgModAPI)
		auth.GET("/org-admin", v1.OrgAdminAPI)
		auth.GET("/mod", v1.ModAPI)
		auth.GET("/admin", v1.AdminAPI)
		auth.GET("/sysadmin", v1.SysAdminAPI)

		// Upload
		upload := apiV1.Group("/uploads")
		upload.POST("", v1.UploadFile)                                     // POST /api/v1/uploads
		upload.GET("/files", v1.GetFiles)                                  // GET /api/v1/uploads/files
		upload.GET("/files/:id", v1.GetFile)                               // GET /api/v1/uploads/files/:id
		upload.DELETE("/files/:id", v1.DeleteFile)                         // DELETE /api/v1/uploads/files/:id
		upload.GET("/videos/:id/status", v1.GetStatusVideoUploadFromBunny) // GET /api/v1/uploads/videos/:id/status

		// system config
		config := apiV1.Group("/configs")
		config.GET("", v1.GetConfig) // GET /api/v1/configs

		// Course
		course := apiV1.Group("/courses")
		course.POST("", v1.CreateCourse)                                              // POST /api/v1/courses
		course.GET("", v1.FindCourse)                                                 // GET /api/v1/courses
		course.GET("/publish", v1.GetPublishCourses)                                  // GET /api/v1/courses/publish
		course.GET("/:id", v1.GetCourse)                                              // GET /api/v1/courses/:id
		course.PUT("/:id", v1.UpdateCourse)                                           // PUT /api/v1/courses/:id
		course.DELETE("/:id", v1.DeleteCourse)                                        // DEL /api/v1/courses/:id
		course.GET("/:id/partners", v1.GetCoursePartners)                             // GET /api/v1/courses/:id/partners
		course.PUT("/:id/partners", v1.UpdateCoursePartner)                           // PUT /api/v1/courses/:id/partners
		course.DELETE("/:id/partners", v1.DeleteCoursePartner)                        // DEL /api/v1/courses/:id/partners
		course.GET("/:id/outline", v1.GetCourseOutline)                               // GET /api/v1/courses/:id/outline
		course.POST("/:id/publish", v1.PublishCourse)                                 // POST /api/v1/courses/:id/publish
		course.PUT("/:id/publish", v1.CancelPublishCourseRequest)                     // PUT /api/v1/courses/:id/publish
		course.DELETE("/:id/publish", v1.UnPublishCourse)                             // DEL /api/v1/courses/:id/publish
		course.GET("/:id/histories", v1.GetCourseVersionHistory)                      // DEL /api/v1/courses/:id/histories
		course.PUT("/:id/stage", v1.ChangeCourseStage)                                // PUT /api/v1/courses/:id/stage
		course.POST("/:id/enroll", v1.EnrollCourse)                                   // POST /api/v1/courses/:id/enroll
		course.GET("/:id/enrollments", v1.GetCourseEnrollments)                       // GET /api/v1/courses/:id/enrollments
		course.GET("/:id/forms", v1.FindPageFormRelationsByCourse)                    // GET /api/v1/courses/:id/forms
		course.PUT("/:id/reply-feedback", v1.ReplyFeedbackPublishCourseRequest)       // PUT /api/v1/courses/:id/reply-feedback
		course.GET("/:id/certificates", v1.GetCertificateLayerForCourse)              // GET /api/v1/courses/:id/certificates
		course.POST("/:id/duplicate", v1.DuplicateCourse)                             // GET /api/v1/courses/:id/duplicate
		course.GET("/:id/html-templates", v1.FindPageCertificateTemplateForCourse)    // GET /api/v1/courses/:id/html-templates
		course.PUT("/:id/html-templates/:template_id", v1.EnableCertificateForCourse) // GET /api/v1/courses/:id/html-templates/:template_id
		course.GET("/:id/preview/:org_id", v1.GetCourseOutlinePreview)                // GET /api/v1/courses/:id/preview/:org_id
		course.GET("/:id/nft/fees", v1.GetEstimatedFeePerMintNFT)                     // POST /api/v1/courses/:id/nft/fees
		course.POST("/:id/nft/fees/deposit", v1.DepositSponsorGasToCourseForMintNFTs) // POST /api/v1/courses/:id/nft/fees/deposit
		course.POST("/:id/nft/fees/withdraw", v1.WithdrawSponsorNftGasOfCourse)       // POST /api/v1/courses/:id/nft/fees/withdraw
		course.POST("/ai", v1.GenerateCourseByAI)
		course.GET("/revenue-detail-reports", v1.GetCourseRevenueDetailReport)
		course.GET("/revenue-summary-reports", v1.GetCourseRevenueSummaryReport)
		course.GET("/revenue-graph-reports", v1.GetRevenueGraphReport)

		// Publish courses
		pubCourse := apiV1.Group("/publish-courses")
		pubCourse.GET("", v1.FindPublishCourses)

		// Section
		section := apiV1.Group("/segments")
		section.POST("", v1.CreateSection)         // POST /api/v1/sections
		section.GET("", v1.FindSection)            // GET /api/v1/sections
		section.GET("/:id", v1.GetSection)         // GET /api/v1/sections/:id
		section.PUT("/bulk", v1.BulkUpdateSection) // PUT /api/v1/sections/bulk
		section.PUT("/:id", v1.UpdateSection)      // PUT /api/v1/sections/:id
		section.DELETE("/:id", v1.DeleteSection)   // DELETE /api/v1/sections/:id

		// Lesson
		lesson := apiV1.Group("/lessons")
		lesson.POST("", v1.CreateLessonContent)                       // POST /api/v1/lessons
		lesson.GET("", v1.FindLesson)                                 // GET /api/v1/lessons
		lesson.GET("/:id", v1.GetLesson)                              // GET /api/v1/lessons/:id
		lesson.PUT("/:id", v1.UpdateLesson)                           // PUT /api/v1/lessons/:id
		lesson.DELETE("/:id", v1.DeleteLesson)                        // DELETE /api/v1/lessons/:id
		lesson.GET("/:id/learn", v1.GetLessonForLearning)             // GET /api/v1/lessons/:id/learn
		lesson.GET("/:id/quizzes", v1.FindQuizzesByLessonContent)     // GET /api/v1/lessons/:id/quizzes
		lesson.GET("/:id/courses/:cid", v1.GetLessonForLearningByUID) // GET /api/v1/lessons/:id/courses/:cid

		// Quiz
		quiz := apiV1.Group("/quizzes")
		quiz.POST("", v1.CreateQuiz)                                                             // POST /api/v1/quizzes
		quiz.GET("/:id", v1.FindQuizDetail)                                                      // GET /api/v1/quizzes/:id
		quiz.PUT("/:id", v1.UpdateQuiz)                                                          // PUT /api/v1/quizzes/:id
		quiz.DELETE("/:id", v1.DeleteQuiz)                                                       // DELETE /api/v1/quizzes/:id
		quiz.POST("/:id/duplicate", v1.DuplicateQuiz)                                            // POST /api/v1/quizzes/:id/duplicate
		quiz.GET("/:id/leaderboards", v1.GetQuizLeaderboards)                                    // GET /api/v1/quizzes/:uid/leaderboards
		quiz.GET("/:id/submissions", v1.GetQuizSubmissionsByQuizUID)                             // GET /api/v1/quizzes/:uid/submissions
		quiz.GET("/by-uid/:id/submissions/latest-passed", v1.GetLatestPassedSubmissionByQuizUID) // GET /api/v1/quizzes/by-uid/{quizUID}/submissions/latest-passed

		// Quiz Submission
		quizSubmission := apiV1.Group("/quiz-submissions")
		quizSubmission.POST("", v1.CreateQuizSubmission)                                // POST /api/v1/quiz-submissions
		quizSubmission.GET("/:id", v1.GetQuizSubmission)                                // GET /api/v1/quiz-submissions/:id
		quizSubmission.PUT("/:id/status", v1.UpdateQuizSubmissionStatus)                // GET /api/v1/quiz-submissions/:id/status
		quizSubmission.GET("/:id/questions/current", v1.GetCurrentQuestionBySubmission) // GET /api/v1/quiz-submissions/:id/questions/current
		quizSubmission.POST("/:id/submit", v1.SubmitAnswerForSubmission)                // POST /api/v1/quiz-submissions/:id/submit
		quizSubmission.GET("/summary", v1.GetQuizSubmissionSummary)                     // GET /api/v1/quiz-submissions/:id/count

		// Category
		category := apiV1.Group("/categories")
		category.GET("", v1.FindPageCategory)
		category.GET("/tree", v1.GetCategoryTree)
		category.POST("/bulk-upsert", v1.UpsertCategories)
		category.POST("/bulk-delete", v1.DeleteCategories)

		// Hashtag
		hashtag := apiV1.Group("/hashtags")
		hashtag.POST("", v1.CreateHashtag)
		hashtag.GET("", v1.FindPageHashtag)
		hashtag.DELETE("", v1.DeleteHashtag)
		hashtag.GET("/entities", v1.FindPageHashtagRelation)

		// Payment
		payment := apiV1.Group("/payments")
		payment.GET("/:id", v1.FindPaymentByID)       // GET /api/v1/payments/:id
		payment.GET("", v1.FindPagePayment)           // GET /api/v1/payments
		payment.POST("/sepay", v1.SepayWebhookHandle) //POST /api/v1/payments

		// Order
		order := apiV1.Group("/orders")
		order.GET("/:id", v1.FindOrderByID)
		order.POST("", v1.CreateOrder)
		order.POST("/:id/payment", v1.PayOrderWithWallet)
		order.PUT("/:id/payment", v1.ChangePaymentMethodForOrder)
		order.POST("/:id/coupons/:code", v1.UseCoupon)
		order.DELETE("/:id/coupons", v1.RemoveCoupon)
		order.GET("/:id/status", v1.CheckOrderStatus)
		order.POST("/:id/success", v1.MakeOrderSuccessByCoupon)
		order.PUT("/:id", v1.UpdateOrder)

		// Order item
		orderItem := apiV1.Group("/order-items")
		orderItem.GET("", v1.FindPageOrderItem)

		// Payment method
		paymentMethod := apiV1.Group("/payment-methods")
		paymentMethod.POST("", v1.CreatePaymentMethod)
		paymentMethod.GET("", v1.FindPagePaymentMethod)
		paymentMethod.GET("/:id", v1.FindPaymentMethodByID)
		paymentMethod.PUT("/:id", v1.UpdatePaymentMethod)
		paymentMethod.DELETE("/:id", v1.DeletePaymentMethodById)

		// User
		user := apiV1.Group("/users")
		user.GET("/top-blog-viewed", v1.FindTopUserBlogView)
		user.GET("/blog-managers", v1.FindPageBlogManagers)
		user.GET("", v1.FindPageUserOrg)
		user.GET("/me", v1.GetMe) // GET /api/v1/users/me
		user.PUT("/me", v1.UpdateProfile)
		user.GET("/me/blogs", v1.FindPageMyBlog)
		user.POST("/me/change-password", v1.ChangePassword)
		user.GET("/me/permissions", v1.GetMyPermission)
		user.GET("/me/orders", v1.FindMyOrders)
		user.GET("/me/wallets", v1.GetMyWallets)
		user.POST("/me/wallets/:id/withdraw", v1.Request2Withdraw)
		user.POST("/me/wallets/:id/withdrawals", v1.WithdrawFromWallet)
		user.GET("/me/approvals", v1.FindMyApproval)
		user.GET("/me/transactions", v1.GetMyTransactions)
		user.GET("/me/referrals", v1.FindReferralByUser)
		user.GET("/me/referrals/summaries", v1.ReferralSummaryReport)
		user.GET("/me/affiliate-campaigns", v1.AffiliateCampaignByUser)
		user.GET("/me/actions", v1.FindPageMineAction)
		user.GET("/me/courses/count", v1.CountMyCourses)
		user.GET("/me/courses", v1.GetMyCourses)
		user.GET("/me/settings", v1.GetMySettingProfile)
		user.PUT("/me/settings", v1.UpdateMySettingProfile)
		user.GET("/me/subscriptions", v1.GetCurrentSubscription)
		user.GET("/me/points/:id", v1.GetMyPoint)
		user.GET("/me/point-histories", v1.GetMyPointHistories)
		user.GET("/me/oe-referrals", v1.GetMyOEReferral)
		user.POST("/me/claim-points", v1.ClaimNewPoints)
		user.PUT("/role-updater", v1.UpdateUserRoles)
		user.POST("/roles", v1.AddRemoveRole)
		user.POST("/invite", v1.InviteUser)
		user.POST("/accept", v1.AcceptInvitationUser)
		user.POST("/block", v1.BlockUsers)
		user.GET("/:id", v1.GetUserProfile)
		user.POST("/:id/follow", v1.FollowUser)
		user.DELETE("/:id/follow", v1.UnFollowUser)
		user.POST("/:id/block", v1.BlockUser)
		user.DELETE("/:id/block", v1.UnblockUser)
		user.POST("/:id/report", v1.ReportUser)
		user.GET("/:id/actions", v1.FindPageUserAction)
		user.GET("/:id/enrollments", v1.GetUserEnrollments)
		user.GET("/:id/person-blogs", v1.FindPageUserPersonBlog)
		user.GET("/:id/org-blogs", v1.FindPageUserOrgBlog)
		//	user.GET("/:id/blogs/:blogID", v1.FindUserBlogByID)

		// Wallet
		wallet := apiV1.Group("wallets")
		wallet.POST("/:id/claim-earning", v1.ClaimEarningsIntoWallet)

		// Sponsor Wallet
		sponsorWallet := apiV1.Group("sponsor-wallets")
		sponsorWallet.POST("", v1.CreateSponsorWallet)
		sponsorWallet.GET("", v1.GetSponsorWallet)

		// Chain
		chain := apiV1.Group("chains")
		chain.GET("/:network/accounts/:id", v1.GetCryptoAccountInfo)

		// User agency
		userSettings := apiV1.Group("user-settings")
		userSettings.POST("", v1.CreateManyUserSetting)
		userSettings.GET("", v1.FindUserSetting)
		userSettings.PUT("", v1.UpdateManyUserSetting)
		userSettings.DELETE("", v1.DeleteManyUserSetting)

		// Coupon
		coupon := apiV1.Group("/coupons")
		coupon.POST("", v1.CreateCoupon)
		coupon.PUT("/:id", v1.UpdateCoupon)
		coupon.DELETE("/:id", v1.DeleteCoupon)
		coupon.GET("/partners", v1.FindPageCouponsByPartner)
		coupon.GET(":id", v1.FindDetailCoupon)

		// Email
		email := apiV1.Group("/emails")
		template := email.Group("/templates")
		template.POST("", v1.CreateEmailTemplate)
		template.GET("/variables", v1.GetEmailTemplateVariables)
		template.PUT("/:id", v1.UpdateEmailTemplate)
		template.GET("/:id", v1.GetEmailTemplate)
		template.GET("", v1.FindEmailTemplates)
		template.DELETE("/:id", v1.DeleteEmailTemplate)
		template.POST("/:id/preview", v1.SendPreviewEmailTemplate)

		// Form
		form := apiV1.Group("/forms")
		form.POST("", v1.CreateForm)                                  // POST /api/v1/forms
		form.GET("", v1.FindPageForms)                                // GET /api/v1/forms
		form.GET("/register-organization", v1.FindFormRegisterOrg)    // GET /api/v1/forms/register-organization
		form.GET("/register-creator", v1.FindFormRegisterCreator)     // GET /api/v1/forms/register-creator
		form.GET("/register-writer", v1.FindFormRegisterWriter)       // GET /api/v1/forms/register-creator
		form.GET("/:id", v1.FindFormByIdOrSlug)                       // GET /api/v1/forms/:id
		form.POST("/:id/submit", v1.SubmitForm)                       // POST /api/v1/forms/:id/submit
		form.PUT("/:id/status", v1.UpdateFormStatus)                  // PUT /api/v1/forms/:id/status
		form.PUT("/:id", v1.UpdateForm)                               // PUT /api/v1/forms/:id
		form.DELETE("/:id", v1.DeleteForm)                            // DELETE /api/v1/forms/:id
		form.GET("/:id/summary", v1.GetFormSummary)                   // GET /api/v1/forms/:id/summary
		form.GET("/:id/sessions", v1.GetFormSessions)                 // GET /api/v1/forms/:id/sessions
		form.POST("/:id/duplicate", v1.DuplicateForm)                 // POST /api/v1/forms/:id/duplicate
		form.GET("/by-uid/:id/submitted", v1.CheckFormSubmittedByUID) // GET /api/v1/forms/by-uid/:id/submitted

		question := apiV1.Group("/form-questions")
		question.GET("/:id/answers", v1.GetFormAnswerStatsByQuestion)                          // GET /api/v1/form-questions/:id/answers
		question.GET("/:id/sub-questions/:sub_id/answers", v1.GetFormAnswerStatsBySubQuestion) // GET /api/v1/form-questions/:id/sub-questions/:sub_id/answers

		// Form relation
		formRelation := apiV1.Group("/form-relations")
		formRelation.POST("", v1.CreateFormRelation)       // POST /api/v1/form-relations
		formRelation.PUT("/:id", v1.UpdateFormRelation)    // PUT /api/v1/form-relations/:id
		formRelation.DELETE("/:id", v1.DeleteFormRelation) // DELETE /api/v1/form-relations/:id

		// Form session
		formSession := apiV1.Group("/form-sessions")
		formSession.GET("/:id", v1.FindFormSessionByID)      // GET /api/v1/forms-sessions/:id
		formSession.PUT("/:id/reject", v1.RejectFormSession) // PUT /api/v1/forms-sessions/:id/reject

		// System config
		sysConfig := apiV1.Group("/system-configs")
		sysConfig.GET("", v1.GetSystemConfigs)
		sysConfig.GET("/:id", v1.FindSystemConfigByID)

		// Blog
		blog := apiV1.Group("/blogs")
		blog.POST("", v1.CreateBlog)
		blog.GET("", v1.GetPublishBlogs)
		blog.POST("/ai", v1.GenBlogByAI)
		blog.GET("/ai/:id/rewrite", v1.GetRewriteBlogData)
		blog.GET("/categories/recommend", v1.GetRecommendPublishBlogByCategories)
		blog.GET("/categories/:id", v1.GetPublishBlogByCategoriesParent)
		blog.GET("/categories", v1.GetPublishBlogByCategories)
		blog.GET("/hashtags/recommend", v1.GetRecommendPublishBlogByHashTags)
		blog.GET("/hashtags", v1.GetPublishBlogByHashTags)
		blog.POST("/:id/publish", v1.PublishBlog)
		blog.GET("/:id/edit", v1.FindBlogByIDToEdit)
		blog.GET("/:id/org", v1.FindBlogOrgByID)
		blog.GET("/:id/personal", v1.FindBlogPersonByID)
		blog.PUT("/:id", v1.UpdateUserBlog)
		blog.PUT("/:id/toggle-pin", v1.TogglePinUserBlog)
		blog.DELETE("/:id", v1.DeleteBlogById)
		blog.DELETE("/:id/publish-org", v1.UnPublishBlogOrg)
		blog.DELETE("/:id/publish-person", v1.UnPublishBlogPerson)
		blog.GET("/top-viewed", v1.FindTopViewedBlog)

		// Approval
		approval := apiV1.Group("/approvals")
		approval.POST("", v1.CreateRequestApproval)
		approval.POST("/:id/approve", v1.ApproveRequest)
		approval.POST("/:id/reject", v1.RejectRequest)
		approval.POST("/:id/feedback", v1.SendApprovalFeedBack)
		approval.GET("", v1.FindApproval)

		// Cache
		cache := apiV1.Group("/caches")
		cache.DELETE("", v1.DeleteCaches)
		cache.DELETE("/:key", v1.DeleteCacheByKey)

		// Creator
		creator := apiV1.Group("/creators")
		creator.POST("/accept", v1.AcceptInvitation)

		// Course enrollment
		courseEnrollment := apiV1.Group("/enrollments")
		courseEnrollment.PUT("/:id", v1.UpdateCourseEnrollment)

		// Page config
		pageConfig := apiV1.Group("/page-configs")
		pageConfig.POST("", v1.CreatePageConfig)           // POST /api/v1/page-configs
		pageConfig.GET("", v1.FindPagePageConfig)          // GET /api/v1/page-configs
		pageConfig.DELETE("/:id", v1.DeletePageConfigById) // DEL /api/v1/page-configs/:id

		// Page access
		pageAccess := apiV1.Group("/page-accesses")
		pageAccess.POST("", v1.CreateUpdatePageAccess)
		pageAccess.GET("", v1.FindPagePageAccess)

		// Custom role
		role := apiV1.Group("/roles")
		role.POST("", v1.CreateRole)
		role.GET("", v1.FindPageRole)
		role.PUT("/:id", v1.UpdateRole)
		role.DELETE("/:id", v1.DeleteRole)

		// User Invitation
		userInvitation := apiV1.Group("/user-invitations")
		userInvitation.GET("", v1.FindPageInvitation)
		userInvitation.PUT("", v1.ResendInvitation)

		// Learning progress
		learningProgress := apiV1.Group("/learning-progresses")
		learningProgress.POST("", v1.CreateLearningProgress)
		learningProgress.POST("/migrate", v1.MigrateLearningStatusStartAt)
		learningProgress.GET("/courses/:id", v1.GetUserLearningProgressByCourse)
		learningProgress.GET("/check-complete-course", v1.CheckCompleteCourse)

		// Bookmark
		bookmark := apiV1.Group("/bookmarks")
		bookmark.POST("", v1.CreateBookmark)
		bookmark.GET("", v1.FindBookmarks)
		bookmark.GET("/:id", v1.GetBookmarkByID)
		bookmark.PUT("/:id", v1.UpdateBookmark)
		bookmark.DELETE("/:id", v1.DeleteBookmarkByID)

		// Certificate
		certificate := apiV1.Group("/certificates")
		certificate.POST("", v1.CreateCertificate)
		certificate.POST("/issue", v1.IssueCertificate)
		certificate.POST("/check-condition", v1.CheckCertificateCondition)
		certificate.GET("", v1.FindPageCertificate)
		certificate.GET("/:id", v1.GetCertificateByID)
		certificate.DELETE("/:id", v1.DeleteCertificateById)
		certificate.POST("/:id/nft", v1.MintNFTForCertificate)
		certificate.GET("/:id/nft/fees", v1.EstimatedMintNFTFees)

		// Html template
		htmlTemplate := apiV1.Group("/html-templates")
		htmlTemplate.POST("", v1.CreateHtmlTemplate)
		htmlTemplate.GET("", v1.FindPageHtmlTemplate)
		htmlTemplate.GET("/:id", v1.GetDetailTemplateByID)
		htmlTemplate.PUT("/:id", v1.UpdateHtmlTemplate)
		htmlTemplate.DELETE("/:id", v1.DeleteHtmlTemplateById)
		htmlTemplate.POST("/:id/enable", v1.EnableCertificateTemplateForOrg)

		// Affiliate campaigns
		affiliateCampaign := apiV1.Group("affiliate-campaigns")
		affiliateCampaign.POST("", v1.CreateAffiliateCampaign)
		affiliateCampaign.GET("", v1.FindAffiliateCampaign)
		affiliateCampaign.GET("/:id", v1.FindOneAffiliateCampaign)
		affiliateCampaign.PUT("/:id", v1.UpdateAffiliateCampaign)
		affiliateCampaign.DELETE("/:id", v1.DeleteAffiliateCampaign)
		affiliateCampaign.GET("/:id/referrers", v1.FindReferrerCampaign)
		affiliateCampaign.POST("/:id/courses", v1.AddCourses2AffiliateCampaign)
		affiliateCampaign.GET("/:id/courses", v1.FindCourseByCampaign)
		affiliateCampaign.DELETE("/:id/courses", v1.DeleteCoursesFromAffiliateCampaign)
		affiliateCampaign.GET("/:id/links", v1.GetReferralLinks)
		affiliateCampaign.GET("/:id/publish-courses", v1.FindPublishCourseByCampaign)

		// Referrer
		referrer := apiV1.Group("referrers")
		referrer.POST("", v1.AddReferrer)
		referrer.DELETE("", v1.RemoveReferrer)

		// Commission
		commission := apiV1.Group("commissions")
		commission.POST("", v1.CreateCommission)
		commission.GET("", v1.FindCommissions)
		commission.DELETE("", v1.DeleteCommission)
		commission.GET("/:id", v1.GetCommission)
		commission.PUT("/:id", v1.UpdateCommission)

		referralLink := apiV1.Group("referral-links")
		referralLink.GET("/by-code/:code", v1.GetReferralLinkByCode)
		referralLink.POST("/by-code/:code/validate", v1.ValidateReferralCode)
		referralLink.POST("", v1.CreateReferralLink)
		referralLink.POST("/:id/extend", v1.CreateExtendReferralLink)
		referralLink.PUT("/:id", v1.EditReferralLink)

		referral := apiV1.Group("referrals")
		referral.GET("", v1.FindReferrals)
		referral.GET("/summaries", v1.ReferralSummaryReport)
		referral.GET("/user-reports", v1.ReferralReportByUsers)

		// Report
		report := apiV1.Group("reports")
		report.POST("/course-enrollments", v1.ReportCourseEnrollment)
		report.POST("/course-referral", v1.ReportCourseReferral)
		report.POST("/course-engagement", v1.ReportLearningProgress)
		report.POST("/course-quizzes", v1.ReportQuizSubmission)
		report.POST("/ai-usage", v1.ReportAIUsage)
		report.GET("/:type", v1.FindStructuresReport)

		// Cron
		cronJob := apiV1.Group("cron-jobs")
		cronJob.POST("/generate-blog", v1.CronJobGenerateAIBlog)                    // POST /api/v1/cron-jobs/generate-blog
		cronJob.DELETE("/coupon-reservations", v1.CronJobCleanupCouponReservations) // DELETE /api/v1/cron-jobs/coupon-reservations
		cronJob.POST("/generate-course", v1.CronJobGenerateAICourse)
		cronJob.POST("/point-expirations", v1.CronJobExpiryOEPointNotification)
		cronJob.POST("/remind-subscriptions", v1.CronJobRemindSubscription)
		cronJob.POST("/start-funding", v1.CronJobStartFundingLaunchpad)
		cronJob.POST("/end-funding", v1.CronJobEndFundingLaunchpad)
		cronJob.POST("/end-voting", v1.CronJobEndVotingMilestoneLaunchpad)
		cronJob.POST("/check-setting-funding-time", v1.CronJobCheckSettingFundingTimeLaunchpad)
		cronJob.POST("/check-creator-continue-voting", v1.CronJobCheckCreatorContinueVotingLaunchpad)
		cronJob.POST("/create-course-revenue", v1.CronJobCreateCourseRevenue)
		cronJob.POST("/expired-subscriptions", v1.ExpiredSubcription)
		cronJob.POST("/oe-referral-reports", v1.CronJobGenerateOERefReports)
		cronJob.POST("/oe-referral-summary-reports", v1.CronJobSummaryOEReferralReport)
		cronJob.POST("/remind-learned", v1.CronjobRemindLearned)

		// Exchange rate
		exchangeRate := apiV1.Group("exchange-rates")
		exchangeRate.GET("", v1.GetExchangeRate) // GET /api/v1/exchange-rates

		pricingPlan := apiV1.Group("plans")
		pricingPlan.POST("", v1.CreatePricingPlan)
		pricingPlan.PUT("/:id", v1.UpdatePricingPlan)
		pricingPlan.GET("/:id", v1.GetPricingPlan)
		pricingPlan.GET("", v1.FindPricingPlan)
		pricingPlan.GET("/all", v1.FindAllPricingPlan)
		pricingPlan.DELETE("/:id", v1.DeletePricingPlan)
		pricingPlan.POST("/:id/enable", v1.EnablePricingPlan)
		pricingPlan.POST("/:id/disable", v1.DisablePricingPlan)
		pricingPlan.POST("/:id/subscribes", v1.SubscribePlan)

		subscription := apiV1.Group("subscriptions")
		subscription.POST("", v1.CreateSubscription)
		subscription.PUT("/:id", v1.UpdateSubscription)
		subscription.GET("", v1.FindSubscription)
		subscription.GET("/:id", v1.GetSubscription)

		//Tracking
		tracking := apiV1.Group("trackings")
		tracking.GET("/user-stats-count", v1.CountTrackingByUserAndEvent)

		// OEPointCampaign
		oePointCampaign := apiV1.Group("point-campaigns")
		oePointCampaign.POST("", v1.CreateRefUserCampaign)
		oePointCampaign.PUT("/:id", v1.UpdatePointCampaign)
		oePointCampaign.GET("", v1.FindPagePointCampaign)
		oePointCampaign.GET("/:id", v1.FindPointCampaignByID)
		oePointCampaign.DELETE("/:id", v1.DeletePointCampaign)

		// AI Model
		ai := apiV1.Group("ai")
		ai.GET("/models", v1.GetAvailableAIModel)
		ai.POST("/models", v1.CreateAIModel)
		ai.GET("/prompts", v1.FindPageAIPrompt)
		ai.POST("prompts", v1.CreateAIPrompt)

		// featured contents
		featuredContent := apiV1.Group("featured-contents")
		featuredContent.POST("", v1.UpdateFeaturedContent)
		featuredContent.GET("", v1.FindPageFeaturedContent)
		featuredContent.GET("/by-types", v1.FindFeaturedContentByType)

		// Launchpad
		launchpad := apiV1.Group("launchpads")
		launchpad.POST("", v1.CreateLaunchpad)
		launchpad.GET("", v1.FindPageLaunchpad)
		launchpad.GET("/:id", v1.GetLaunchpadByID)
		launchpad.PATCH("/:id", v1.UpdateLaunchpad)
		launchpad.DELETE("/:id", v1.DeleteLaunchpad)
		launchpad.GET("/:id/partners", v1.GetLaunchpadPartners)
		launchpad.POST("/:id/bookmark", v1.BookmarkLaunchpad)
		launchpad.DELETE("/:id/bookmark", v1.RemoveBookmarkLaunchpad)
		launchpad.POST("/:id/publish", v1.PublishLaunchpad)
		launchpad.PUT("/:id/publish", v1.CancelPublishLaunchpadRequest)
		launchpad.POST("/:id/milestones/:milestone_id/start", v1.StartVotingMilestoneLaunchpad) // POST /api/v1/launchpad/:id/milestones/:milestone_id/voting
		launchpad.GET("/:id/investments", v1.FindInvestmentsForLaunchpad)
		launchpad.POST("/:id/pools", v1.InitLaunchpadPool)
		launchpad.PUT("/:id/funding-time", v1.SetLaunchFundingTime)
		launchpad.POST("/:id/cancel", v1.CancelLaunchpad)
		launchpad.POST("/:id/claim-refund", v1.ClaimLaunchpadRefund)
		launchpad.GET("/investments/me", v1.GetMyLaunchpads)
		launchpad.POST("/:id/decide-voting", v1.CreatorDecideContinueVoting)

		// Investment
		investment := apiV1.Group("investments")
		investment.POST("", v1.CreateInvestment)

		milestone := apiV1.Group("milestones")
		milestone.POST("/:id/voting", v1.InvestmentVotingForLaunchpad)

		// OE referral
		oeReferral := apiV1.Group("oe-referrals")
		oeReferral.GET("/:id/leader-boards", v1.FindOELeaderBoardByCampaign)
		oeReferral.POST("/invites", v1.OEReferralInviteReferee)
		oeReferral.GET("/statistics/:id", v1.OEReferralStatistic)
		oeReferral.GET("/reports/:id/provinces", v1.OEReferralListProvinces)
		oeReferral.POST("/leader-boards", v1.AddLeaderBoardRecord)
		oeReferral.PUT("/leader-boards/:id", v1.OELeaderBoardUpdateRecord)
		oeReferral.DELETE("/leader-boards/:id", v1.OELeaderBoardDeleteRecord)
		oeReferral.GET("/leader-boards", v1.FindOELeaderBoardByCampaign)
		oeReferral.GET("/:id/provinces", v1.GetListOERefProvinces)
		oeReferral.GET("/:id/statistics/widget", v1.GetOERefWidgetStatistic)
		oeReferral.GET("/:id/statistics/learner-growth", v1.GetOERefLearnerGrowthStatistic)
		oeReferral.GET("/:id/statistics/section-completion", v1.GetOERefSectionCompletionStatistic)
		oeReferral.GET("/:id/statistics/provinces", v1.GetOERefLearnerCountByProvincesStatistic)
		oeReferral.GET("/:id/statistics/provinces/details", v1.GetOERefDetailsByProvinceStatistic)
		oeReferral.GET("/:id/learners", v1.GetOERefLearnersByCampaign)

		//Schedule
		schedules := apiV1.Group("schedules")
		schedules.POST("", v1.CreateSchedule)
		schedules.DELETE("/:id", v1.DeleteSchedule)
		schedules.GET("", v1.FindSchedules)
		schedules.PUT("/:id", v1.UpdateSchedule)

		eventSchedules := apiV1.Group("event-schedules")
		eventSchedules.POST("", v1.CreateEventSchedule)
		eventSchedules.DELETE("/:id", v1.DeleteEventSchedule)
		eventSchedules.GET("", v1.FindEventSchedule)
		eventSchedules.PUT("/:id", v1.UpdateEventSchedule)
	}

	internalApiV1 := r.Group("/api/internal-v1")
	internalApiV1.Use(middleware.BeforeInterceptor())
	{
		// User
		user := internalApiV1.Group("/users")
		user.GET("/:id", v1.FindUserByID)
		user.POST("/roles", v1.AddDefaultRole)

		// Role
		role := internalApiV1.Group("/roles")
		role.GET("", v1.FindAllRoles)

		// Organization
		org := internalApiV1.Group("/organizations")
		org.GET("", v1.FindOrgByDomain)
		org.POST("/avail/retroactive/calculate", v1.CalcRetroactiveForOrgAvail) // POST /api/internal-v1/organizations/avail/retroactive/calculate
		org.POST("/avail/retroactive", v1.RetroactiveForOrgAvail)               // POST /api/internal-v1/organizations/avail/retroactive

		// Resource usage
		usage := internalApiV1.Group("usages")
		usage.GET("/limit", v1.CheckLimitUsage) // GET /api/internal-v1/usages/limit

		// AI model
		aimodel := internalApiV1.Group("ai")
		aimodel.GET("/models", v1.FindOneAvailableAIModel)

		// pricing-plan
		plan := internalApiV1.Group("/plans")
		plan.POST("/:id/subscribes", v1.SubscribePlanForUsers)
	}

	return r
}
