package models

import (
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"
	"unicode"

	"gorm.io/gorm"
)

type ReportType string

const (
	ReportCourseEnrollment      ReportType = "rp_course_enrollment"
	ReportCourseOrderedEnrolled ReportType = "rp_course_ordered_enrolled"
	ReportCourseReferral        ReportType = "rp_course_referral"
	ReportCourseLearningStatus  ReportType = "rp_course_learning_status"
)

type ColReport string

const (
	ColPhone    ColReport = "Phone"
	ColFullName ColReport = "Fullname"
	ColParentID ColReport = "ParentID"
)

type ReportCourseLearningStatusRequest struct {
	//Filter
	CourseCuid *string             `json:"course_cuid" validate:"validateReportType"`
	StartAt    *int64              `json:"start_at"`
	EndAt      *int64              `json:"end_at"`
	ReportType ReportType          `json:"report_type" validate:"required"`
	Form       []FormReportRequest `json:"form"`
	ExceptKey  []ColReport         `json:"except_key"`
	OrgID      string              `json:"org_id"`
	Network    BlockchainNetwork   `json:"network"`
}

type ReportCourseEnrollmentRequest struct {
	//Filter
	CourseCuid        *string             `json:"course_cuid" validate:"validateReportType"`
	StartAt           *int64              `json:"start_at"`
	EndAt             *int64              `json:"end_at"`
	ReportType        ReportType          `json:"report_type" validate:"required"`
	Form              []FormReportRequest `json:"form"`
	ExceptKey         []ColReport         `json:"except_key"`
	OrgID             string              `json:"org_id"`
	NetworkWalletAddr BlockchainNetwork   `json:"network"`
	Currency          Currency            `json:"currency"`
	IsNeedSource      bool                `json:"is_source"`
}

type ReportReferralLinkByUserRequest struct {
	Emails            []string          `json:"emails,omitempty"`
	CourseCuid        string            `json:"course_cuid,omitempty"`
	StartAt           *int64            `json:"start_at"`
	EndAt             *int64            `json:"end_at"`
	ReportType        ReportType        `json:"report_type" validate:"required"`
	NetworkWalletAddr BlockchainNetwork `json:"network"`
	Currency          Currency          `json:"currency"`
	ExceptKey         []ColReport       `json:"except_key"`
}

type ReportAIUserUsageRequest struct {
	Data      []string    `json:"data,omitempty"`
	StartAt   *int64      `json:"start_at"`
	EndAt     *int64      `json:"end_at"`
	ExceptKey []ColReport `json:"except_key"`
}

type FormReportRequest struct {
	FormUID   string             `form:"form_uid" json:"form_uid"`
	Questions []QuestionColModel `form:"question_col_model" json:"question_col_model"`
}

type QuestionColModel struct {
	QuestionUID string    `json:"question_uid"`
	Key         ColReport `json:"key"`
}

type QuizSubmissionReport struct {
	CreateAt                      string
	QuizUID                       string               `json:"quiz_uid"`
	QuizID                        string               `json:"quiz_id" gorm:"type:varchar(20);not null"`
	UserID                        string               `json:"user_id" gorm:"type:varchar(20)"`
	CourseCuid                    string               `json:"course_cuid" gorm:"type:varchar(20)"`
	Status                        QuizSubmissionStatus `json:"status"`
	Passed                        bool                 `json:"passed"`
	StartAt                       string               `json:"start_at"`
	EndAt                         string               `json:"end_at"`
	DeadlineAt                    string               `json:"deadline_at"`
	TimeToCompleteInMilliSeconds  int64                `json:"time_to_complete_in_milli_seconds"`
	ArchivedPoints                int                  `json:"archived_points"`
	HighestPointsOnSingleQuestion int                  `json:"highest_points_on_single_question"`
	HighestStreak                 int                  `json:"highest_streak"`
	NumCorrectAnswers             int                  `json:"num_correct_answers" gorm:"-"`
	NumQuestions                  int                  `json:"num_questions" gorm:"-"`
}

type LearningProgressReport struct {
	CreateAt         string
	UserID           string            `json:"user_id" gorm:"not null;type:varchar(20)"`
	CourseCuid       string            `json:"course_cuid" gorm:"not null;type:varchar(20)"`
	SectionUID       string            `json:"section_uid" gorm:"not null;type:varchar(20)"`
	LessonUID        string            `json:"lesson_uid" gorm:"not null;type:varchar(20)"`
	LessonContentUID string            `json:"lesson_content_uid" gorm:"type:varchar(20)"`
	ContentType      LessonContentType `json:"content_type" gorm:"type:varchar(20)"`
	CompleteAt       string            `json:"complete_at,omitempty" gorm:"default:0"`
	PauseAt          string            `json:"pause_at,omitempty" gorm:"default:0"`
	StartAt          string            `json:"start_at,omitempty" gorm:"default:0"`
	Event            ProgressEvent     `json:"event" gorm:"type:varchar(30);default:'lesson_content_progress'"`
}

func (query *ReportCourseEnrollmentRequest) CustomApply(joinkey string) string {
	qb := "true"

	if query.CourseCuid != nil {
		qb = fmt.Sprintf(`%s AND %scourse_cuid = '%s'`, qb, joinkey, *query.CourseCuid)
	}

	if query.StartAt != nil {
		qb = fmt.Sprintf(`%s AND %screate_at >= %d`, qb, joinkey, *query.StartAt)
	}

	if query.EndAt != nil {
		qb = fmt.Sprintf(`%s AND %screate_at <= %d`, qb, joinkey, *query.EndAt)
	}

	return qb
}

type ReportTrackingEnrolledAndOrdered struct {
	Email       string `json:"email" excel:"Email"`
	FullName    string `json:"full_name" excel:"Full Name"`
	FromName    string `json:"from_name" excel:"From Name"`
	UserID      string `json:"user_id" excel:"User ID"`
	Date        string `json:"date" excel:"Create Date"`
	CreateAt    string `json:"create_at" excel:"Create Hour"`
	IsAffiliate bool   `json:"is_affiliate" excel:"Affiliate"`
}

type UserReport struct {
	Email      string `json:"email" gorm:"column:email"`
	FullName   string `json:"full_name" gorm:"column:full_name"`
	Username   string `json:"username" gorm:"column:username"`
	SourceName string `json:"source_name"`
	Phone      string `json:"phone" gorm:"column:phone"`
	// FullNameForm string `json:"full_name_form" excel:"Full Name Form"`
	UserID    string `json:"user_id" gorm:"column:user_id"`
	CreatedAt string `json:"created_at" gorm:"column:created_at"`
	RefBy     string `json:"ref_by"`
}

type CourseStatsReport struct {
	TotalUserCompleted     int
	DropOffPointLessonUID  string
	DropOffPointLessonName string
	CourseProgressionRate  float64
	MostWatchedVideoUID    string
	MostWatchedVideoName   string
	VideoCompletionRate    float64
}

func SanitizeSheetName(title string) string {
	if len(title) >= 29 {
		title = title[:25]
	}

	invalidChars := []string{":", "\\", "/", "?", "*", "[", "]"}
	result := title
	for _, char := range invalidChars {
		result = strings.ReplaceAll(result, char, "-")
	}

	return result
}

func GetAvailableExceptKeys(structs ...interface{}) ([]ColReport, error) {
	fieldNames := make(map[string]bool)
	result := make([]ColReport, 0)

	for i, s := range structs {
		t := reflect.TypeOf(s)
		if t == nil {
			return nil, fmt.Errorf("struct %d is nil", i)
		}
		if t.Kind() == reflect.Ptr {
			t = t.Elem()
		}
		if t.Kind() != reflect.Struct {
			return nil, fmt.Errorf("argument %d is not a struct", i)
		}

		for j := 0; j < t.NumField(); j++ {
			field := t.Field(j)

			if field.PkgPath != "" {
				continue
			}

			jsonTag := field.Tag.Get("json")
			var fieldName string
			if jsonTag != "" && jsonTag != "-" {
				parts := strings.SplitN(jsonTag, ",", 2)
				fieldName = parts[0]
			} else {
				fieldName = field.Name
			}

			if fieldNames[fieldName] {
				continue
			}

			if fieldName != "" {
				fieldNames[fieldName] = true
				result = append(result, ColReport(fieldName))
			}
		}
	}

	sort.Slice(result, func(i, j int) bool {
		return string(result[i]) < string(result[j])
	})

	return result, nil
}

func FormatExceptKeysForDisplay(keys []ColReport) []string {
	result := make([]string, len(keys))
	for i, key := range keys {
		result[i] = formatKeyName(string(key))
	}
	return result
}

func formatKeyName(key string) string {
	isSnakeCase := strings.Contains(key, "_")

	var result strings.Builder
	var previousWasUnderscore bool

	for i, r := range key {
		if r == '_' {
			previousWasUnderscore = true
			continue
		}

		if i == 0 || previousWasUnderscore {
			result.WriteRune(unicode.ToUpper(r))
			previousWasUnderscore = false
			continue
		}

		if !isSnakeCase && unicode.IsUpper(r) {
			result.WriteRune(r)
		} else {
			result.WriteRune(r)
		}
	}

	return result.String()
}

func (r *ReportRepository) ReportUserEnrollmentCourse(query *ReportCourseEnrollmentRequest, trans *gorm.DB) (users []*UserReport, sectionEnrollments map[string][]*UserReport, lessonEnrollments map[string][]*UserReport, courseID *string, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
				err = fmt.Errorf("panic: %v", r)
			} else if err != nil {
				tx.Rollback()
			} else {
				err = tx.Commit().Error
			}
		}()
	}

	courseCuid := query.CourseCuid
	orgID := query.OrgID

	// Get user enrollment course

	courseIDQuery := fmt.Sprintf(
		`SELECT id 
		FROM %s 
		WHERE cuid = ? 
		order by "version" desc 
		limit 1`, GetTblName(CourseTbl))
	if err = tx.Raw(courseIDQuery, courseCuid).Scan(&courseID).Error; err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get course id: %w", err)
	}

	reportQuery := fmt.Sprintf(
		`SELECT u.email AS email, u.display_name AS full_name, u.username, u.phone AS phone,
    	 u.id AS user_id, to_timestamp(ce.create_at / 1000) AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' AS created_at
		FROM %s ce
		LEFT JOIN
		%s u
		ON ce.user_id = u.id
		WHERE %s 
		ORDER BY ce.create_at DESC
		`,
		GetTblName(CourseEnrollmentTbl), GetTblName(UserTbl), query.CustomApply("ce."))

	err = tx.Raw(reportQuery).Scan(&users).Error
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get user enrollment course: %w", err)
	}

	//Get user enrollment course each section
	sectionQuery := `
		SELECT uid 
		FROM openedu_sections os 
		WHERE org_id = ? and course_id = ? and parent_id = '' order by "order" asc 
	`
	var sectionIDs []string
	if err = tx.Raw(sectionQuery, orgID, courseID).Scan(&sectionIDs).Error; err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get section ids: %w", err)
	}

	querySectionEnrollQuery := fmt.Sprintf(`
		SELECT u.email AS email, u.display_name AS full_name, u.username, u.phone AS phone, u.id AS user_id, to_timestamp(olp.create_at / 1000) AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' AS created_at
		FROM openedu_learning_progresses olp
		LEFT JOIN 
		openedu_users u on u.id = olp.user_id
		WHERE %s and olp.section_uid = ? and event = 'section_progress';

	`, query.CustomApply("olp."))

	var mapSectionIdxUserEnrollments = make(map[string][]*UserReport)
	for _, uid := range sectionIDs {
		var sectionUsers []*UserReport
		if err = tx.Raw(querySectionEnrollQuery, uid).Scan(&sectionUsers).Error; err != nil {
			return nil, nil, nil, nil, fmt.Errorf("failed to get user enrollment course each section: %w", err)
		}
		mapSectionIdxUserEnrollments[uid] = sectionUsers
	}
	//Get user enrollment lesson each section
	lessonQuery := `
		SELECT uid 
		FROM openedu_sections os 
		WHERE org_id = ? and course_id = ? and parent_id <> '' order by "order" asc 
	`

	var lessonIDs []string
	if err = tx.Raw(lessonQuery, orgID, courseID).Scan(&lessonIDs).Error; err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get section ids: %w", err)
	}

	queryLessonEnrollQuery := fmt.Sprintf(`
		SELECT u.email AS email, u.display_name AS full_name, u.username, u.phone AS phone, u.id AS user_id, to_timestamp(olp.create_at / 1000) AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' AS created_at
		FROM %s olp
		LEFT JOIN 
		%s u on u.id = olp.user_id
		WHERE %s and olp.lesson_uid = ? and event = 'lesson_progress';

	`, GetTblName(LearningProgressTbl), GetTblName(UserTbl), query.CustomApply("olp."))

	var mapLessonIdxUserEnrollments = make(map[string][]*UserReport)
	for _, uid := range lessonIDs {
		var lessonUsers []*UserReport
		if err = tx.Raw(queryLessonEnrollQuery, uid).Scan(&lessonUsers).Error; err != nil {
			return nil, nil, nil, nil, fmt.Errorf("failed to get user enrollment course each section: %w", err)
		}
		mapLessonIdxUserEnrollments[uid] = lessonUsers
	}

	sectionEnrollments = mapSectionIdxUserEnrollments
	lessonEnrollments = mapLessonIdxUserEnrollments
	return
}

func GetLearningProgressReport() ([]LearningProgressReport, error) {
	var reports []LearningProgressReport

	query := DB.Table("openedu_learning_progresses as lp").
		Select(`
			lp.create_at as create_at,
			lp.user_id,
			lp.course_cuid,
			lp.section_uid,
			lp.lesson_uid,
			lp.lesson_content_uid,
			lp.content_type,
			lp.complete_at,
			lp.pause_at,
			lp.start_at,
			lp.event,
			section.title as section_name,
			lesson.title as lesson_name,
			lc.title as lesson_content_name
		`).
		Joins("LEFT JOIN openedu_sections as section ON lp.section_uid = section.uid").
		Joins("LEFT JOIN openedu_sections as lesson ON lp.lesson_uid = lesson.uid").
		Joins("LEFT JOIN openedu_lesson_contents as lc ON lp.lesson_content_uid = lc.uid")

	query = query.Where("lp.event IN ?", []ProgressEvent{
		CourseProgress,
		SectionProgress,
		LessonProgress,
		LessonContentProgress,
	})

	err := query.Find(&reports).Error
	if err != nil {
		return nil, err
	}

	for i := range reports {
		completeAtStr := reports[i].CompleteAt
		if completeAtStr == "0" || completeAtStr == "" {
			reports[i].CompleteAt = ""
		} else {
			var completeAtInt int64
			fmt.Sscanf(completeAtStr, "%d", &completeAtInt)

			if completeAtInt > 9999999999 {
				completeAtInt = completeAtInt / 1000
			}

			completeTime := time.Unix(completeAtInt, 0)
			reports[i].CompleteAt = completeTime.Format("02-01-2006 15:04:05")
		}
	}

	return reports, nil
}

func (r *ReportRepository) ReportUserEnrollmentCourseLearningStatus(query *ReportCourseEnrollmentRequest, trans *gorm.DB) (users []*UserReport, courseID *string, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
				err = fmt.Errorf("panic: %v", r)
			} else if err != nil {
				tx.Rollback()
			} else {
				err = tx.Commit().Error
			}
		}()
	}

	courseCuid := query.CourseCuid

	// Get user enrollment course

	courseIDQuery := fmt.Sprintf(
		`SELECT id 
		FROM %s 
		WHERE cuid = ? 
		order by "version" desc 
		limit 1`, GetTblName(CourseTbl))
	if err = tx.Raw(courseIDQuery, courseCuid).Scan(&courseID).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to get course id: %w", err)
	}

	reportQuery := fmt.Sprintf(
		`SELECT u.email AS email, u.display_name AS full_name, u.username, u.phone AS phone,
    	 u.id AS user_id, to_timestamp(ce.create_at / 1000) AT TIME ZONE 'Asia/Ho_Chi_Minh' AS created_at
		FROM %s ce
		LEFT JOIN
		%s u
		ON ce.user_id = u.id
		WHERE %s 
		ORDER BY ce.create_at DESC
		`,
		GetTblName(CourseEnrollmentTbl), GetTblName(UserTbl), query.CustomApply("ce."))

	err = tx.Raw(reportQuery).Scan(&users).Error
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get user enrollment course: %w", err)
	}

	return
}
