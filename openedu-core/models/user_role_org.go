package models

import (
	"github.com/samber/lo"
	"openedu-core/pkg/util"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserRoleOrg struct {
	UserID string `json:"user_id"`
	RoleID string `json:"role_id"`
	OrgID  string `json:"org_id"`
	User   *User  `json:"user"`
	Role   *Role  `json:"role"`
	Model

	OrgDomain string `json:"org_domain" gorm:"-"`
}

type SimpleUserRole struct {
	RoleID    string `json:"role_id"`
	OrgID     string `json:"org_id"`
	OrgDomain string `json:"org_domain"`
	OrgName   string `json:"org_name"`
}

func (u *UserRoleOrg) ToSimple() *SimpleUserRole {
	return &SimpleUserRole{
		RoleID:    u.RoleID,
		OrgID:     u.OrgID,
		OrgDomain: u.OrgDomain,
	}
}

type UserRoleOrgQuery struct {
	UserIDIn       []string
	UserID         *string
	RoleID         *string
	RoleIDIn       []string
	OrgID          *string
	CombineKey     *string  // combine_key = user_id + role_id + org_id
	CombineKeyIN   []string // combine_key = user_id + role_id + org_id
	IncludeDeleted *bool
}

func (query *UserRoleOrgQuery) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	if query.UserID != nil {
		qb = qb.Where("user_id = ?", query.UserID)
	}

	if len(query.UserIDIn) > 0 {
		qb = qb.Where("user_id IN (?)", query.UserIDIn)
	}

	if query.CombineKey != nil {
		qb = qb.Where("user_id || role_id || org_id = ?", query.CombineKey)
	}

	if len(query.CombineKeyIN) > 0 {
		qb = qb.Where("user_id || role_id || org_id IN (?)", query.CombineKeyIN)
	}

	if query.RoleID != nil {
		qb = qb.Where("role_id = ?", query.RoleID)
	}

	if query.RoleIDIn != nil {
		qb = qb.Where("role_id IN (?)", query.RoleIDIn)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", query.OrgID)
	}

	if query.UserIDIn != nil {
		qb = qb.Where("user_id IN (?)", query.UserIDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *UserRoleOrgRepository) Create(entity *UserRoleOrg) error {
	if err := create(UserRoleOrgTbl, &entity, nil); err != nil {
		return err
	}
	Cache.UserRole.DeleteByUserID(entity.UserID)
	return nil
}

func (r *UserRoleOrgRepository) FindOne(query *UserRoleOrgQuery, options *FindOneOptions) (*UserRoleOrg, error) {
	return findOne[UserRoleOrg](UserRoleOrgTbl, query, options)
}

func (r *UserRoleOrgRepository) Update(c *UserRoleOrg, trans *gorm.DB) error {
	if err := update(UserRoleOrgTbl, c, trans); err != nil {
		return err
	}
	Cache.UserRole.DeleteByUserID(c.UserID)
	return nil
}

func (r *UserRoleOrgRepository) FindByUserId(id string) ([]*UserRoleOrg, error) {
	var urs []*UserRoleOrg
	var ursCache []interface{}
	cErr := Cache.UserRole.GetByUserID(id, &ursCache)
	if cErr == nil && len(ursCache) > 0 {
		if err := Cache.Convert(ursCache, &urs); err == nil {
			return urs, nil
		}
	}

	if len(urs) > 0 {
		return urs, nil
	}

	urs, err := findMany[UserRoleOrg](UserRoleOrgTbl, &UserRoleOrgQuery{
		UserID:         &id,
		IncludeDeleted: util.NewBool(false),
	}, nil)
	if err != nil {
		return nil, err
	}

	cacheItems := lo.Map(urs, func(ur *UserRoleOrg, _ int) interface{} {
		return ur
	})
	Cache.UserRole.SetUserRole(id, cacheItems)
	return urs, err
}

func (r *UserRoleOrgRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[UserRoleOrg](UserRoleOrgTbl, id, trans)
}

func (r *UserRoleOrgRepository) FindPage(query *UserRoleOrgQuery, options *FindPageOptions) ([]*UserRoleOrg, *Pagination, error) {
	return findPage[UserRoleOrg](UserRoleOrgTbl, query, options)
}

func (r *UserRoleOrgRepository) FindMany(query *UserRoleOrgQuery, options *FindManyOptions) ([]*UserRoleOrg, error) {
	return findMany[UserRoleOrg](UserRoleOrgTbl, query, options)
}

func (r *UserRoleOrgRepository) DeleteMany(query *UserRoleOrgQuery, transaction *gorm.DB) (int64, error) {
	num, err := deleteMany[UserRoleOrg](UserRoleOrgTbl, query, transaction)
	if err != nil {
		return 0, err
	}
	Cache.UserRole.Flush()
	return num, nil
}

func (r *UserRoleOrgRepository) FirstOrCreate(role *UserRoleOrg) (*UserRoleOrg, error) {
	err := GetDb(UserRoleOrgTbl).Where(UserRoleOrg{
		UserID: role.UserID,
		RoleID: role.RoleID,
		OrgID:  role.OrgID,
	}).FirstOrCreate(&role).Error
	return role, err
}

func (r *UserRoleOrgRepository) UpsertManyRole(role []*UserRoleOrg, trans *gorm.DB) error {
	var tx *gorm.DB
	// Begin trans
	if trans != nil {
		tx = trans
	} else {
		tx = GetDb(UserRoleOrgTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	if err := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "role_id"}, {Name: "org_id"}},
		DoUpdates: clause.Assignments(map[string]interface{}{"delete_at": 0}),
	}).Create(&role).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit tx
	return tx.Commit().Error
}
