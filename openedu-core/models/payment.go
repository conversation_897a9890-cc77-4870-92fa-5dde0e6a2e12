package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type PaymentStatus string

const (
	PaymentStatusPending PaymentStatus = "pending"
	PaymentStatusSuccess PaymentStatus = "success"
	PaymentStatusFailed  PaymentStatus = "failed"
)

type Payment struct {
	Model
	UserID          string          `json:"user_id" gorm:"type:varchar(20);not null"`
	OrderID         string          `json:"order_id" gorm:"type:varchar(20);not null"`
	PaymentMethodID string          `json:"payment_method_id" gorm:"type:varchar(20)"`
	PaymentInfo     PaymentInfo     `json:"payment_info" gorm:"type:jsonb"`
	Amount          decimal.Decimal `json:"amount" gorm:"type:numeric(19,4);not null;default:0"`
	Status          PaymentStatus   `json:"status" gorm:"default:pending"`
	InvoiceId       string          `json:"invoice_id"`
	Payload         JSONB           `json:"payload" gorm:"type:jsonb"`
	Currency        Currency        `json:"currency" gorm:"default:VND"`

	OrgID        string `json:"org_id" gorm:"type:varchar(20);not null"`
	PayFromOrgID string `json:"pay_from_org_id" gorm:"type:varchar(20);not null"`
}

type SimplePayment struct {
	Model
	UserID      string          `json:"user_id"`
	OrderID     string          `json:"order_id"`
	PaymentInfo PaymentInfo     `json:"payment_info"`
	Amount      decimal.Decimal `json:"amount"`
	Status      PaymentStatus   `json:"status"`
	Payload     JSONB           `json:"payload"`
	Currency    Currency        `json:"currency"`
}

type PaymentQuery struct {
	ID              *string         `form:"id" json:"id"`
	IDIn            []string        `form:"id_in" json:"id_in"`
	OrgId           *string         `form:"org_id" json:"org_id"`
	OrderID         *string         `form:"order_id" json:"order_id"`
	OrderIDIn       []string        `form:"order_id_in" json:"order_id_in"`
	UserID          *string         `form:"user_id" json:"user_id"`
	InvoiceId       *string         `form:"invoice_id" json:"invoice_id"`
	PaymentMethodID *string         `form:"payment_method_id" json:"payment_method_id"`
	StatusIn        []PaymentStatus `form:"status_in" json:"status"`
	StatusNotIn     []PaymentStatus `form:"status_not_in" json:"status_not_in"`
	IncludeDeleted  *bool           `form:"include_deleted" json:"include_deleted"`
}

type PaymentInfo struct {
	Acc    string          `json:"acc"`
	Bank   string          `json:"bank"`
	Amount decimal.Decimal `json:"amount"`
	Des    string          `json:"des"`
}

func (p *Payment) Sanitize() *SimplePayment {
	return &SimplePayment{
		Model:       p.Model,
		UserID:      p.UserID,
		OrderID:     p.OrderID,
		Amount:      p.Amount,
		Status:      p.Status,
		Payload:     p.Payload,
		PaymentInfo: p.PaymentInfo,
	}
}

func (p PaymentInfo) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (p *PaymentInfo) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("Type assertion to []byte failed")
	}
	err := json.Unmarshal(b, &p)
	if err != nil {
		return err
	}

	return nil
}

func (query *PaymentQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.OrderID != nil {
		qb = qb.Where("order_id = ?", *query.OrderID)
	}

	if len(query.OrderIDIn) > 0 {
		qb = qb.Where("order_id IN (?)", *&query.OrderIDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.PaymentMethodID != nil {
		qb = qb.Where("payment_method_id = ?", *query.PaymentMethodID)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", *&query.StatusIn)
	}

	if len(query.StatusNotIn) > 0 {
		qb = qb.Where("status NOT IN (?)", *&query.StatusNotIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.InvoiceId != nil {
		qb = qb.Where("invoice_id = ?", *query.InvoiceId)
	}

	return qb
}

func (r *PaymentRepository) Create(p *Payment, trans *gorm.DB) error {
	return create(PaymentTbl, p, trans)
}

func (r *PaymentRepository) CreateMany(p []*Payment, trans *gorm.DB) error {
	return createMany(PaymentTbl, p, trans)
}

func (r *PaymentRepository) Update(p *Payment, trans *gorm.DB) error {
	return update(PaymentTbl, p, trans)
}

func (r *PaymentRepository) FindByID(id string, options *FindOneOptions) (*Payment, error) {
	return findByID[Payment](PaymentTbl, id, options)
}

func (r *PaymentRepository) FindOne(query *PaymentQuery, options *FindOneOptions) (*Payment, error) {
	return findOne[Payment](PaymentTbl, query, options)
}

func (r *PaymentRepository) FindMany(query *PaymentQuery, options *FindManyOptions) ([]*Payment, error) {
	return findMany[Payment](PaymentTbl, query, options)
}

func (p *PaymentRepository) FindPage(query *PaymentQuery, options *FindPageOptions) ([]*Payment, *Pagination, error) {
	return findPage[Payment](PaymentTbl, query, options)
}

func (p *PaymentRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Payment](PaymentTbl, id, trans)
}

func (p *PaymentRepository) DeleteMany(query *PaymentQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Payment](PaymentTbl, query, trans)
}
