package models

import (
	"fmt"
	"strings"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type AffiliateCampaign struct {
	Model
	UserID    string `json:"user_id" gorm:"type:varchar(20);not null"`
	OrgID     string `json:"org_id" gorm:"type:varchar(20);not null"`
	Name      string `json:"name" gorm:"required"`
	StartDate int    `json:"start_date" gorm:"type:int8;default:0"`
	EndDate   int    `json:"end_date" gorm:"type:int8;default:0"`
	Enable    bool   `json:"enable" gorm:"default:true"`

	UserCanApplicableComms []*Commission `json:"can_applicable_comms" gorm:"-"`
	UserBoughtFrom         *ReferralLink `json:"bought_from" gorm:"-"`
	UserExtendLink         *ReferralLink `json:"user_extend_link" gorm:"-"`
	Org                    *Organization `json:"org" gorm:"-"`
	Commissions            []*Commission `json:"commissions" gorm:"-"`
}

type UserCampaign struct {
	ID            string          `json:"id"`
	Name          string          `json:"name"`
	CreateAt      int             `json:"create_at"`
	StartDate     int             `json:"start_date"`
	EndDate       int             `json:"end_date"`
	UserID        string          `json:"user_id"`
	OrgID         string          `json:"org_id"`
	CourseSlug    string          `json:"course_slug"`
	CourseCuid    string          `json:"course_cuid"`
	CourseName    string          `json:"course_name"`
	Price         decimal.Decimal `json:"price"`
	OrgDomain     string          `json:"org_domain"`
	CourseOwnerID string          `json:"course_owner_id"`
	ThumbnailID   string          `json:"thumbnail_id"`
	Currency      Currency        `json:"currency"`

	BaseRateMin         float32 `json:"base_rate_min"`
	BaseRateMax         float32 `json:"base_rate_max"`
	BaseRateRef2        float32 `json:"base_rate_ref2"`
	LearnerRateMin      float32 `json:"learner_rate_min"`
	LearnerRateMax      float32 `json:"learner_rate_max"`
	PremiumRateMin      float32 `json:"premium_rate_min"`
	PremiumRateMax      float32 `json:"premium_rate_max"`
	PremiumShareRateMax float32 `json:"premium_share_rate_max"`
}

type UserCamapaignQuery struct {
	ID               *string `json:"id" form:"id"`
	CourseCuid       *string `json:"course_cuid" form:"course_cuid"`
	CourseSlug       *string `json:"course_slug" form:"course_slug"`
	StartDateLt      *int64  `json:"start_date_lt" form:"start_date_lt"`
	EndDateGt        *int64  `json:"end_date_gt" form:"end_date_gt"`
	SearchTerm       *string `json:"search_term" form:"search_term"`
	SearchCategories *string `json:"search_categories" form:"search_categories"`
}

type AffiliateCampaignQuery struct {
	ID                 *string   `json:"id" form:"id"`
	IDIn               []*string `json:"id_in" form:"id_in"`
	UserID             *string   `json:"user_id" form:"user_id"`
	OrgID              *string   `json:"org_id" form:"org_id"`
	CourseCuid         *string   `json:"course_cuid" form:"course_cuid"`
	IncludeDeleted     *bool     `form:"include_deleted"`
	Name               *string   `json:"name" form:"name"`
	SearchTerm         *string   `json:"search_term" form:"search_term"`
	SearchCategories   *string   `json:"search_categories" form:"search_categories"`
	JoinCourseCampaign *bool     `json:"join_course_campaign"`
	StartDateLt        *int64    `json:"start_date_lt" form:"start_date_lt"`
	EndDateGt          *int64    `json:"end_date_gt" form:"end_date_gt"`
	Enable             *bool     `json:"enable" form:"enable"`
}

func (query *AffiliateCampaignQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		for _, category := range categories {
			switch category {
			case "name":
				qb = qb.Where("name ~* ?", *query.SearchTerm)
			case "id":
				qb = qb.Where("id ~* ?", *query.SearchTerm)
			default:
				break
			}
		}
	}
	return qb
}

func (r *AffiliateCampaignRepository) Create(e *AffiliateCampaign, trans *gorm.DB) error {
	return create(AffiliateCampaignTbl, e, trans)
}

func (r *AffiliateCampaignRepository) CreateMany(ts []*AffiliateCampaign, trans *gorm.DB) error {
	return createMany(AffiliateCampaignTbl, ts, trans)
}

func (r *AffiliateCampaignRepository) Update(f *AffiliateCampaign, trans *gorm.DB) error {
	return update(AffiliateCampaignTbl, f, trans)
}

func (r *AffiliateCampaignRepository) FindOne(query *AffiliateCampaignQuery, options *FindOneOptions) (*AffiliateCampaign, error) {
	return findOne[AffiliateCampaign](AffiliateCampaignTbl, query, options)
}

func (r *AffiliateCampaignRepository) FindPage(query *AffiliateCampaignQuery, options *FindPageOptions) ([]*AffiliateCampaign, *Pagination, error) {
	return findPage[AffiliateCampaign](AffiliateCampaignTbl, query, options)
}

func (r *AffiliateCampaignRepository) FindMany(query *AffiliateCampaignQuery, options *FindManyOptions) ([]*AffiliateCampaign, error) {
	return findMany[AffiliateCampaign](AffiliateCampaignTbl, query, options)
}

func (r *AffiliateCampaignRepository) Count(query *AffiliateCampaignQuery) (int64, error) {
	return count[AffiliateCampaign](AffiliateCampaignTbl, query)
}

func (r *AffiliateCampaignRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[AffiliateCampaign](AffiliateCampaignTbl, id, trans)
}

func (r *AffiliateCampaignRepository) GetCampaignsWithApplicableCommissions(userID string,
	query *AffiliateCampaignQuery,
	options *FindPageOptions,
) ([]*AffiliateCampaign, *Pagination, error) {
	/**
	SELECT DISTINCT ac.*, c.id as cid
	FROM openedu_affiliate_campaigns ac
	INNER JOIN openedu_referrers r ON ac.id = r.campaign_id
	LEFT JOIN openedu_commissions c ON ac.id = c.campaign_id
	WHERE r.user_id = 'uo43vgMCpg3emTud'
	  AND ac.enable = true AND ac.delete_at = 0
	  AND r.enable = true AND r.delete_at = 0
	  AND c.enable = true AND c.delete_at = 0
	  AND c.parent_id = ''
	  AND (
	    c.referrer_ids ?| ARRAY['uo43vgMCpg3emTud']
	    OR
	    c.referrer_types ?| ARRAY[r.type]
	    OR c.referrer_types ?| ARRAY['user']
	  );
	*/
	if options == nil {
		options = &FindPageOptions{
			Page:    1,
			PerPage: 10,
			Sort:    []string{CreateAtDESC},
		}
	}
	limit := options.PerPage
	offset := (options.Page - 1) * options.PerPage
	var campaigns []*AffiliateCampaign
	var totalCount int64
	var err error
	actbl := GetTblName(AffiliateCampaignTbl)
	rtbl := GetTblName(ReferrerTbl)
	ctbl := GetTblName(CommissionTbl)
	cctbl := GetTblName(CourseCampaignTbl)
	entitiesChan := make(chan []*AffiliateCampaign)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		qb := DB.Debug().Distinct(fmt.Sprintf("%s.*", actbl)).
			Joins(fmt.Sprintf("LEFT JOIN %s ON %s.id = %s.campaign_id", rtbl, actbl, rtbl))
		if query.JoinCourseCampaign != nil ||
			(query.SearchCategories != nil && lo.Contains([]string{"course_name", "course_slug"}, *query.SearchCategories)) {
			qb = qb.Joins(fmt.Sprintf("LEFT JOIN %s ON %s.id = %s.campaign_id", cctbl, actbl, cctbl))
		}
		qb = qb.Joins(fmt.Sprintf("LEFT JOIN %s ON %s.id = %s.campaign_id", ctbl, actbl, ctbl)).
			Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0", actbl, actbl)).
			//Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0", rtbl, rtbl)).
			Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0 AND %s.parent_id = ''", ctbl, ctbl, ctbl)).
			Where(
				fmt.Sprintf(
					"(%s.user_id = '%s' AND %s.enable = true AND %s.delete_at = 0 AND %s.referrer_types ?| ARRAY[%s.type]) OR %s.referrer_ids ?| ARRAY['%s'] OR %s.referrer_types ?| ARRAY['user']", rtbl, userID, rtbl, rtbl, ctbl, rtbl, ctbl, userID, ctbl),
			)

		if query.JoinCourseCampaign != nil {
			qb = qb.Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0", rtbl, rtbl))
			if query.CourseCuid != nil {
				qb = qb.Where(fmt.Sprintf("%s.course_cuid = '%s'", cctbl, *query.CourseCuid))
			}
		}

		if query.StartDateLt != nil {
			qb = qb.Where(fmt.Sprintf("(%s.start_date = 0 OR %s.start_date < %d)", actbl, actbl, *query.StartDateLt))
		}

		if query.EndDateGt != nil {
			qb = qb.Where(fmt.Sprintf("(%s.end_date = 0 OR %s.end_date > %d)", actbl, actbl, *query.EndDateGt))
		}

		if query.Name != nil {
			qb = qb.Where(fmt.Sprintf("%s.name = '%s'", actbl, *query.Name))
		}

		if query.OrgID != nil {
			qb = qb.Where(fmt.Sprintf("%s.org_id = '%s'", actbl, *query.OrgID))
		}

		if query.SearchTerm != nil && query.SearchCategories != nil {
			categories := strings.Split(*query.SearchCategories, ",")
			for _, category := range categories {
				switch category {
				case "name":
					qb = qb.Where(fmt.Sprintf("%s.name ~* '%s'", actbl, *query.SearchTerm))
				case "id":
					qb = qb.Where(fmt.Sprintf("%s.id ~* '%s'", actbl, *query.SearchTerm))
				case "course_name":
					qb = qb.Where(fmt.Sprintf("%s.course_name ~* '%s'", cctbl, *query.SearchTerm))
				case "course_slug":
					qb = qb.Where(fmt.Sprintf("%s.course_slug ~* '%s'", cctbl, *query.SearchTerm))
				default:
					break
				}
			}
		}

		sortJoin := lo.Reduce(options.Sort, func(agg string, item string, _ int) string {
			return agg + " " + actbl + "." + item
		}, "")
		result := qb.
			Order(sortJoin).
			Limit(limit).
			Offset(offset).
			Find(&campaigns)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- campaigns
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		var c int64
		qb := DB.Debug().Distinct(fmt.Sprintf("%s.id", actbl)).
			Joins(fmt.Sprintf("LEFT JOIN %s ON %s.id = %s.campaign_id", rtbl, actbl, rtbl))
		if query.JoinCourseCampaign != nil ||
			(query.SearchCategories != nil && lo.Contains([]string{"course_name", "course_slug"}, *query.SearchCategories)) {
			qb = qb.Joins(fmt.Sprintf("LEFT JOIN %s ON %s.id = %s.campaign_id", cctbl, actbl, cctbl))
		}
		qb = qb.
			Joins(fmt.Sprintf("LEFT JOIN %s ON %s.id = %s.campaign_id", ctbl, actbl, ctbl)).
			Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0", actbl, actbl)).
			//Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0", rtbl, rtbl)).
			Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0 AND %s.parent_id = ''", ctbl, ctbl, ctbl)).
			Where(
				fmt.Sprintf(
					"(%s.user_id = '%s' AND %s.enable = true AND %s.delete_at = 0 AND %s.referrer_types ?| ARRAY[%s.type]) OR %s.referrer_ids ?| ARRAY['%s'] OR %s.referrer_types ?| ARRAY['user']", rtbl, userID, rtbl, rtbl, ctbl, rtbl, ctbl, userID, ctbl),
			)

		if query.JoinCourseCampaign != nil {
			qb = qb.Where(fmt.Sprintf("%s.enable = true AND %s.delete_at = 0", rtbl, rtbl))
			if query.CourseCuid != nil {
				qb = qb.Where(fmt.Sprintf("%s.course_cuid = '%s'", cctbl, *query.CourseCuid))
			}
		}

		if query.StartDateLt != nil {
			qb = qb.Where(fmt.Sprintf("(%s.start_date = 0 OR %s.start_date < %d)", actbl, actbl, *query.StartDateLt))
		}

		if query.EndDateGt != nil {
			qb = qb.Where(fmt.Sprintf("(%s.end_date = 0 OR %s.end_date > %d)", actbl, actbl, *query.EndDateGt))
		}
		if query.Name != nil {
			qb = qb.Where(fmt.Sprintf("%s.name = '%s'", actbl, *query.Name))
		}

		if query.OrgID != nil {
			qb = qb.Where(fmt.Sprintf("%s.org_id = '%s'", actbl, *query.OrgID))
		}

		if query.SearchTerm != nil && query.SearchCategories != nil {
			categories := strings.Split(*query.SearchCategories, ",")
			for _, category := range categories {
				switch category {
				case "name":
					qb = qb.Where(fmt.Sprintf("%s.name ~* '%s'", actbl, *query.SearchTerm))
				case "id":
					qb = qb.Where(fmt.Sprintf("%s.id ~* '%s'", actbl, *query.SearchTerm))
				case "course_name":
					qb = qb.Where(fmt.Sprintf("%s.course_name ~* '%s'", cctbl, *query.SearchTerm))
				case "course_slug":
					qb = qb.Where(fmt.Sprintf("%s.course_slug ~* '%s'", cctbl, *query.SearchTerm))
				default:
					break
				}
			}
		}
		totalErr := qb.Model(&AffiliateCampaign{}).Count(&c).Error
		if totalErr != nil {
			errorChan <- totalErr
			return
		}
		countChan <- totalCount
	}()

	// Wait for all goroutines to finish
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case campaigns = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return campaigns, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

func (r *AffiliateCampaignRepository) GetCampaignByUser(
	userID string,
	query *UserCamapaignQuery,
	options *FindPageOptions,
) ([]*UserCampaign, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{
			Page:    1,
			PerPage: 10,
			Sort:    []string{"ac.create_at desc"},
		}
	}
	limit := options.PerPage
	offset := (options.Page - 1) * options.PerPage
	var campaigns []*UserCampaign
	var totalCount int64
	var err error
	actbl := GetTblName(AffiliateCampaignTbl)
	pctbl := GetTblName(PublishCourseTbl)
	rtbl := GetTblName(ReferrerTbl)
	ctbl := GetTblName(CommissionTbl)
	cctbl := GetTblName(CourseCampaignTbl)
	entitiesChan := make(chan []*UserCampaign)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	optionWhereStatement := ""
	if query.ID != nil {
		optionWhereStatement += fmt.Sprintf(" AND ac.id = '%s' ", *query.ID)
	}

	if query.CourseCuid != nil {
		optionWhereStatement += fmt.Sprintf(" AND cc.course_cuid = '%s' ", *query.CourseCuid)
	}

	if query.CourseSlug != nil {
		optionWhereStatement += fmt.Sprintf(" AND cc.course_slug = '%s' ", *query.CourseSlug)
	}

	if query.StartDateLt != nil {
		optionWhereStatement += fmt.Sprintf(" AND ac.start_date = 0 OR ac.start_date < %d ", *query.StartDateLt)
	}

	if query.EndDateGt != nil {
		optionWhereStatement += fmt.Sprintf(" AND ac.end_date = 0 OR ac.end_date > %d ", *query.EndDateGt)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		for _, category := range categories {
			switch category {
			case "name":
				optionWhereStatement += fmt.Sprintf(" AND ac.name ~* '%s' ", *query.SearchTerm)
			case "id":
				optionWhereStatement += fmt.Sprintf(" AND ac.id ~* '%s' ", *query.SearchTerm)
			case "course_name":
				optionWhereStatement += fmt.Sprintf(" AND pc.name ~* '%s' ", *query.SearchTerm)
			case "course_slug":
				optionWhereStatement += fmt.Sprintf(" AND cc.course_slug ~* '%s' ", *query.SearchTerm)
			default:
				break
			}
		}
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		// add sort
		sortStr := ""
		if options.Sort != nil {
			lo.ForEach(options.Sort, func(item string, _ int) {
				sortStr += ", " + item
			})
		}

		mainQuery := fmt.Sprintf(`
        SELECT DISTINCT ON (ac.id, cc.course_cuid)
            ac.id,
            ac."name",
            ac.create_at,
            ac.start_date,
            ac.end_date,
            ac.user_id,
            ac.org_id,
            cc.course_slug,
            cc.course_cuid,
            pc."name" as course_name,
            pc.price, 
            pc.org_domain,
            pc.user_id as course_owner_id,
            pc.thumbnail_id,
            'VND' as currency
        FROM
            %[1]s AS cc
            INNER JOIN %[2]s ac ON cc.campaign_id = ac.id
            INNER JOIN %[3]s pc ON pc.course_cuid = cc.course_cuid
            LEFT JOIN %[4]s ref on ref.campaign_id = ac.id and ref."enable" = true and ref.delete_at = 0
            LEFT JOIN %[5]s AS com ON com.campaign_id = cc.campaign_id AND com."enable" = TRUE AND com.delete_at = 0
        WHERE 
            ac."enable" = TRUE and ac.delete_at = 0 AND 
            (
                (ref.user_id = '%[6]s' and com.referrer_types ?| ARRAY[ref.type])
                OR com.referrer_ids ?| ARRAY['%[6]s']
                OR com.referrer_types ?| ARRAY['user']
            )
        	%[10]s
        ORDER BY ac.id, cc.course_cuid 
        %[9]s 
        LIMIT %[7]d OFFSET %[8]d`,
			cctbl, actbl, pctbl, rtbl, ctbl, userID, limit, offset,
			sortStr, optionWhereStatement,
		)

		result := DB.Debug().Raw(mainQuery).Scan(&campaigns)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- campaigns
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		countQuery := fmt.Sprintf(`
SELECT COUNT(DISTINCT(ac.id, cc.course_cuid)) as count
FROM
	%[1]s AS cc
	INNER JOIN %[2]s ac ON cc.campaign_id = ac.id
	INNER JOIN %[3]s pc ON pc.course_cuid = cc.course_cuid
	LEFT JOIN %[4]s ref on ref.campaign_id = ac.id and ref."enable" = true and ref.delete_at = 0
	LEFT JOIN %[5]s AS com ON com.campaign_id = cc.campaign_id AND com. "enable" = TRUE AND com.delete_at = 0
WHERE 
 	ac."enable" = TRUE and ac.delete_at = 0 AND 
	(
		(ref.user_id = '%[6]s' and com.referrer_types ?| ARRAY[ref.type])
		OR com.referrer_ids ?| ARRAY['%[6]s']
		OR com.referrer_types ?| ARRAY['user']
	) 
	%[7]s 
`,
			cctbl, actbl, pctbl, rtbl, ctbl, userID, optionWhereStatement,
		)

		qb := DB.Debug()
		totalErr := qb.Raw(countQuery).Count(&totalCount).Error
		if totalErr != nil {
			errorChan <- totalErr
			return
		}
		countChan <- totalCount
	}()

	// Wait for all goroutines to finish
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case campaigns = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return campaigns, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}
