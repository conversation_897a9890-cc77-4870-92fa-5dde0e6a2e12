package models

import "gorm.io/gorm"

type Team struct {
	Model
	Name   string      `json:"name" gorm:"type:varchar(255);not null;unique_index"`
	UserID StringArray `json:"user_id" gorm:"type:jsonb"`
	Users  []User      `json:"users"`
}

type TeamQuery struct{}

func (query *TeamQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	return qb
}

func (r *TeamRepository) Create(t *Team, trans *gorm.DB) error {
	return create(TeamTbl, t, trans)
}

func (r *TeamRepository) CreateMany(t []*Team, trans *gorm.DB) error {
	return createMany(TeamTbl, t, trans)
}

func (r *TeamRepository) Update(t *Team, trans *gorm.DB) error {
	return update(TeamTbl, t, trans)
}

func (r *TeamRepository) FindByID(id string, options *FindOneOptions) (*Team, error) {
	return findByID[Team](TeamTbl, id, options)
}

func (r *TeamRepository) FindOne(query *TeamQuery, options *FindOneOptions) (*Team, error) {
	return findOne[Team](TeamTbl, query, options)
}

func (r *TeamRepository) FindMany(query *TeamQuery, options *FindManyOptions) ([]*Team, error) {
	return findMany[Team](TeamTbl, query, options)
}

func (r *TeamRepository) FindPage(query *TeamQuery, options *FindPageOptions) ([]*Team, *Pagination, error) {
	return findPage[Team](TeamTbl, query, options)
}

func (r *TeamRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Team](TeamTbl, id, trans)
}

func (r *TeamRepository) DeleteMany(query *TeamQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Team](TeamTbl, query, trans)
}
