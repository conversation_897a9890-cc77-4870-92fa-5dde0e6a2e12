package models

import (
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"gorm.io/gorm"
)

type SnsAccount struct {
	Model
	UserID       string           `json:"user_id"`
	AccountID    string           `json:"account_id"`
	Username     string           `json:"username"`
	DisplayName  string           `json:"display_name"`
	Email        string           `json:"email"`
	Avatar       string           `json:"avatar"`
	Profile      JSONB            `json:"profile" gorm:"type:jsonb"`
	AccessToken  string           `json:"access_token"`
	RefreshToken string           `json:"refresh_token"`
	Provider     util.SNSProvider `json:"provider"`
	IsDefault    bool             `json:"is_default" gorm:"default:0"`
	LastLogin    int64            `json:"last_login" gorm:"type:int8"`
}

type SimpleSnsAccount struct {
	Model
	UserID      string           `json:"user_id"`
	AccountID   string           `json:"account_id"`
	Username    string           `json:"username"`
	DisplayName string           `json:"display_name"`
	Email       string           `json:"email"`
	Avatar      string           `json:"avatar"`
	Provider    util.SNSProvider `json:"provider"`
	IconURL     *string          `json:"icon_url,omitempty"`
	IsDefault   bool             `json:"is_default"`
}

type FindSnsAccountsQuery struct {
	UserID    *string          `json:"user_id"`
	UserIDIn  []string         `json:"user_id_in"`
	IsDefault *bool            `json:"is_default"`
	IsDeleted *bool            `json:"is_deleted"`
	Provider  util.SNSProvider `json:"provider"`
}

func (s *SnsAccount) ToSimple() *SimpleSnsAccount {
	email := s.Email
	if strings.HasSuffix(email, setting.DefaultEmailDomain()) {
		email = ""
	}

	return &SimpleSnsAccount{
		Model:       s.Model,
		UserID:      s.UserID,
		AccountID:   s.AccountID,
		Username:    s.Username,
		DisplayName: s.DisplayName,
		Email:       email,
		Avatar:      s.Avatar,
		Provider:    s.Provider,
		IsDefault:   s.IsDefault,
	}
}

func (r *SnsAccountRepository) Create(snsAccount *SnsAccount) (*SnsAccount, error) {
	result := GetDb(SnsAccountTbl).Create(&snsAccount)
	if result.Error != nil {
		return nil, result.Error
	}

	return snsAccount, nil
}

func (r *SnsAccountRepository) CreateMany(snsAccounts []*SnsAccount, trans *gorm.DB) error {
	return createMany(SnsAccountTbl, snsAccounts, trans)
}

func (r *SnsAccountRepository) Update(snsAccount *SnsAccount, transaction *gorm.DB) (*SnsAccount, error) {
	var tx *gorm.DB
	if transaction != nil {
		tx = transaction

	} else {
		tx = GetDb(SnsAccountTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	// parse struct to map
	account, err := util.StructToMap(snsAccount)
	if err != nil {
		return nil, err
	}

	account["profile"] = snsAccount.Profile
	result := tx.Debug().Model(&snsAccount).Updates(account)
	if result.Error != nil {
		return nil, result.Error
	}

	// Do not commit when use external transaction
	if transaction != nil {
		return snsAccount, nil
	}

	return snsAccount, tx.Commit().Error
}

func (r *SnsAccountRepository) FindByAccountIDAndProvider(accountId string, provider string) (*SnsAccount, error) {
	var snsAccount *SnsAccount
	result := GetDb(SnsAccountTbl).Where("account_id = ? and delete_at=0 and provider=?", accountId, provider).First(&snsAccount)
	if result.Error != nil {
		return nil, result.Error
	}

	return snsAccount, nil
}

func (r *SnsAccountRepository) FindMany(query *FindSnsAccountsQuery) ([]*SnsAccount, error) {
	var linkedSnsAccounts []*SnsAccount
	qb := r.buildFindQuery(GetDb(SnsAccountTbl), query)
	result := qb.Find(&linkedSnsAccounts)
	if err := result.Error; err != nil {
		return nil, err
	}

	return linkedSnsAccounts, nil
}

func (r *SnsAccountRepository) buildFindQuery(tx *gorm.DB, query *FindSnsAccountsQuery) *gorm.DB {
	qb := tx
	if query != nil {
		if query.UserID != nil {
			qb = qb.Where("user_id = ?", *query.UserID)
		}

		if len(query.UserIDIn) > 0 {
			qb = qb.Where("user_id IN (?)", query.UserIDIn)
		}

		if query.IsDeleted != nil {
			if *query.IsDeleted {
				qb = qb.Where("delete_at <> 0")
			} else {
				qb = qb.Where("delete_at = 0")
			}
		}

		if query.IsDefault != nil {
			qb = qb.Where("is_default = ?", *query.IsDefault)
		}

		if query.Provider != "" {
			qb = qb.Where("provider = ?", query.Provider)
		}
	}
	return qb
}

func (r *SnsAccountRepository) FindByID(id string) (*SnsAccount, error) {
	var snsAccount *SnsAccount
	result := GetDb(SnsAccountTbl).Where("id = ? and delete_at=0", id).First(&snsAccount)
	if result.Error != nil {
		return nil, result.Error
	}

	return snsAccount, nil
}

func (r *SnsAccountRepository) DeleteByID(id string) error {
	now := time.Now().UnixMilli()
	result := GetDb(SnsAccountTbl).Model(&SnsAccount{}).Where("id = ? and delete_at=0", id).Updates(map[string]interface{}{
		"delete_at": now,
		"update_at": now,
	})
	if err := result.Error; err != nil {
		return err
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

// FindOne finds one sns account by query conditions
func (r *SnsAccountRepository) FindOne(query *FindSnsAccountsQuery) (*SnsAccount, error) {
	var snsAccount SnsAccount
	qb := r.buildFindQuery(GetDb(SnsAccountTbl), query)
	if err := qb.Debug().First(&snsAccount).Error; err != nil {
		return nil, err
	}

	return &snsAccount, nil
}

func (r *SnsAccountRepository) DeleteMany(query *FindSnsAccountsQuery, transaction *gorm.DB) (int64, error) {
	var tx *gorm.DB
	if transaction != nil {
		tx = transaction

	} else {
		tx = GetDb(SnsAccountTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	tx = r.buildFindQuery(tx, query)
	now := time.Now().UnixMilli()
	result := tx.Debug().Model(&SnsAccount{}).Updates(map[string]interface{}{
		"delete_at": now,
		"update_at": now,
	})
	if err := result.Error; err != nil {
		return 0, err
	}

	// Do not commit when use external transaction
	if transaction != nil {
		return result.RowsAffected, nil
	}

	return result.RowsAffected, tx.Commit().Error
}
