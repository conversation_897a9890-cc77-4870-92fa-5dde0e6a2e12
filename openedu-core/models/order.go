package models

import (
	"database/sql/driver"
	"encoding/json"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"strings"
)

type OrderStatus string

const (
	OrderStatusNew          OrderStatus = "new"
	OrderStatusInsufficient OrderStatus = "insufficient"
	OrderStatusSuccess      OrderStatus = "success"
	OrderStatusFailed       OrderStatus = "failed"
)

type Order struct {
	Model
	UserID          string          `json:"user_id" gorm:"type:varchar(20)"`
	Amount          decimal.Decimal `json:"amount" gorm:"type:numeric(19,4);not null;default:0"`
	ActualAmount    decimal.Decimal `json:"actual_amount" gorm:"type:numeric(19,4);not null;default:0"`
	Paid            decimal.Decimal `json:"paid" gorm:"type:numeric(19,4);not null;default:0"`
	MissingAmount   decimal.Decimal `json:"missing_amount" gorm:"type:numeric(19,4);not null;default:0"`
	DiscountAmount  decimal.Decimal `json:"discount_amount" gorm:"type:numeric(19,4);not null;default:0"`
	Code            string          `json:"code"`
	Status          OrderStatus     `json:"status"`
	Currency        Currency        `json:"currency" gorm:"default:VND"`
	PaymentMethodID string          `json:"payment_method_id" gorm:"type:varchar(20)"`
	OrderNumber     int64           `json:"order_number" gorm:"autoIncrement"`
	PurchaseAt      int64           `json:"purchase_at" gorm:"type:int8;not null;default:0"`

	ReferralCode           string          `json:"referral_code"`
	ReferralDiscountAmount decimal.Decimal `json:"referral_discount_amount" gorm:"type:numeric(19,4);not null;default:0"`

	OrgID        string `json:"org_id" gorm:"type:varchar(20);not null"`
	PayFromOrgID string `json:"pay_from_org_id" gorm:"type:varchar(20);not null"`

	Props OrderProps `json:"props" gorm:"type:jsonb"`

	Coupon *Coupon `json:"coupon" gorm:"-"`
}

type OrderProps struct {
	DiscountedAmount decimal.Decimal `json:"discounted_amount" gorm:"type:numeric(19,4)"` // DiscountedAmount = Amount - DiscountAmount
}

func (j OrderProps) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *OrderProps) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type OrderWithCourseID struct {
	Order
	CourseID StringArray `json:"course_id" gorm:"column:courses"`
}

type SimpleOrder struct {
	Model
	UserID          string          `json:"user_id"`
	PaymentMethodID string          `json:"payment_method_id"`
	Amount          decimal.Decimal `json:"amount"`
	ActualAmount    decimal.Decimal `json:"actual_amount"`
	MissingAmount   decimal.Decimal `json:"missing_amount"`
	DiscountAmount  decimal.Decimal `json:"discount_amount"`
	Paid            decimal.Decimal `json:"paid"`
	Currency        Currency        `json:"currency"`

	Status      OrderStatus `json:"status"`
	OrderNumber int64       `json:"order_number"`
}

func (o *Order) Sanitize() *SimpleOrder {
	return &SimpleOrder{
		Model:           o.Model,
		UserID:          o.UserID,
		PaymentMethodID: o.PaymentMethodID,
		Amount:          o.Amount,
		ActualAmount:    o.ActualAmount,
		MissingAmount:   o.MissingAmount,
		DiscountAmount:  o.DiscountAmount,
		Paid:            o.Paid,
		Status:          o.Status,
		OrderNumber:     o.OrderNumber,
		Currency:        o.Currency,
	}
}

func (o *Order) IsNew() bool {
	return o.Status == OrderStatusNew
}

func (o *Order) IsSuccess() bool {
	return o.Status == OrderStatusSuccess
}

func (o *Order) IsFailed() bool {
	return o.Status == OrderStatusFailed
}

func (o *Order) IsInsufficient() bool {
	return o.Status == OrderStatusInsufficient
}

func (o *Order) IsOwnedBy(user *User) bool {
	return user != nil && o.UserID == user.ID
}

type OrderQuery struct {
	OrgID              *string       `json:"org_id" form:"org_id"`
	ID                 *string       `json:"id" form:"id"`
	IDIn               []string      `json:"id_in" form:"id_in"`
	UserIDIn           []string      `json:"user_id_in" form:"user_id_in"`
	UserId             *string       `json:"user_id" form:"user_id"`
	Code               *string       `json:"code" form:"code"`
	StatusIn           []OrderStatus `json:"status_in" form:"status_in"`
	Status             *OrderStatus  `json:"status" form:"status"`
	StatusNotIn        []OrderStatus `json:"status_not_in" form:"status_not_in"`
	NotStatus          *OrderStatus  `json:"not_status" form:"not_status"`
	PaymentMethodId    *string       `json:"payment_method_id" form:"payment_method_id"`
	ReferralCodeNotNil *bool         `json:"referral_code_not_nil"`
	ReferralCodeNil    *bool         `json:"referral_code_nil"`
	IncludeDeleted     *bool         `json:"include_deleted" form:"include_deleted"`
}

func (query *OrderQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if len(query.UserIDIn) > 0 {
		qb = qb.Where("user_id IN (?)", *&query.UserIDIn)
	}

	if query.UserId != nil {
		qb = qb.Where("user_id = ?", *query.UserId)
	}

	if query.Code != nil {
		qb = qb.Where("code = ?", strings.ToUpper(*query.Code))
	}

	if query.NotStatus != nil {
		qb = qb.Where("status != ?", *query.NotStatus)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", *&query.StatusIn)
	}
	if len(query.StatusNotIn) > 0 {
		qb = qb.Where("status NOT IN (?)", *&query.StatusNotIn)
	}

	if query.PaymentMethodId != nil {
		qb = qb.Where("payment_method_id = ?", *query.PaymentMethodId)
	}

	if query.ReferralCodeNotNil != nil && *query.ReferralCodeNotNil {
		qb = qb.Where("referral_code != '' ")
	}

	if query.ReferralCodeNil != nil && *query.ReferralCodeNil {
		qb = qb.Where("referral_code= '' ")
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *OrderRepository) Create(o *Order, trans *gorm.DB) error {
	return create(OrderTbl, o, trans)
}

func (r *OrderRepository) CreateMany(o []*Order, trans *gorm.DB) error {
	return createMany(OrderTbl, o, trans)
}

func (r *OrderRepository) Update(o *Order, trans *gorm.DB) error {
	return update(OrderTbl, o, trans)
}

func (r *OrderRepository) FindByID(id string, options *FindOneOptions) (*Order, error) {
	return findByID[Order](OrderTbl, id, options)
}

func (r *OrderRepository) FindOne(query *OrderQuery, options *FindOneOptions) (*Order, error) {
	return findOne[Order](OrderTbl, query, options)
}

func (r *OrderRepository) FindMany(query *OrderQuery, options *FindManyOptions) ([]*Order, error) {
	return findMany[Order](OrderTbl, query, options)
}

func (r *OrderRepository) FindPage(query *OrderQuery, options *FindPageOptions) ([]*Order, *Pagination, error) {
	return findPage[Order](OrderTbl, query, options)
}

func (r *OrderRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Order](OrderTbl, id, trans)
}

func (r *OrderRepository) DeleteMany(query *OrderQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Order](OrderTbl, query, trans)
}

func (r *OrderRepository) FindCourseIDsByOrderID(id string) ([]string, error) {

	orderItems, err := Repository.OrderItem.FindMany(&OrderItemQuery{OrderID: &id}, &FindManyOptions{})
	if err != nil {
		return nil, err
	}

	courseIDs := lo.Map[*OrderItem, string](orderItems, func(orderItem *OrderItem, _ int) string {
		return orderItem.EntityCuid
	})

	return courseIDs, nil

}
