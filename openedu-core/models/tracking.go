package models

type VerbType string

const (
	Created  VerbType = "created"
	Updated  VerbType = "updated"
	Deleted  VerbType = "deleted"
	Started  VerbType = "started"
	Stopped  VerbType = "stopped"
	Paid     VerbType = "paid"
	Enrolled VerbType = "enrolled"
	View     VerbType = "view"
	Ordered  VerbType = "ordered"
	Referred VerbType = "referred"
)

type TrackingContext string

const (
	SourceContext         TrackingContext = "source"
	BlogViewContext       TrackingContext = "blog_view"
	AuthorBlogViewContext TrackingContext = "author_blog_view"
	CourseContext         TrackingContext = "course_context"
)

type TrackingQuery struct {
	ID                *string          `form:"id" json:"id,omitempty"`
	ActorID           *string          `form:"actor_id" json:"actor_id,omitempty"`
	ActorIDIn         []string         `form:"actor_id_in,omitempty" json:"actor_id_in,omitempty"`
	Verb              *VerbType        `form:"verb" json:"verb,omitempty"`
	Object            *ModelName       `form:"object" json:"object,omitempty"`
	ObjectID          *string          `form:"object_id" json:"object_id,omitempty"`
	ObjectIDIn        []string         `form:"object_id_in,omitempty" json:"object_id_in,omitempty"`
	Context           *TrackingContext `form:"context" json:"context,omitempty"`
	ContextValue      *string          `form:"context_value" json:"context_value,omitempty"`
	IncludeDeleted    *bool            `form:"include_deleted" json:"include_deleted,omitempty"`
	TrackingDateStart *int64           `form:"tracking_date_start" json:"tracking_date_start,omitempty"`
	TrackingDateEnd   *int64           `form:"tracking_date_end" json:"tracking_date_end,omitempty"`
	IDIn              []string         `form:"id_in" json:"id_in,omitempty"`
	IsValid           *bool            `form:"is_valid" json:"is_valid,omitempty"`
}

type MessageQuery struct {
	ID             *string  `form:"id,omitempty" json:"id,omitempty"`
	UserID         *string  `form:"user_id,omitempty" json:"user_id,omitempty"`
	ConversationID *string  `form:"conversation_id,omitempty" json:"conversation_id,omitempty"`
	ContextID      *string  `form:"context_id,omitempty" json:"context_id,omitempty"`
	SenderID       *string  `form:"sender_id,omitempty" json:"sender_id,omitempty"`
	IsEdited       *bool    `form:"is_edited,omitempty" json:"is_edited,omitempty"`
	IsAI           *bool    `form:"is_ai,omitempty" json:"is_ai,omitempty"`
	IDIn           []string `form:"id_in,omitempty" json:"id_in,omitempty"`
	UserIDIn       []string `form:"user_id_in,omitempty" json:"user_id_in,omitempty"`
	SenderIDIn     []string `form:"sender_id_in,omitempty" json:"sender_id_in,omitempty"`
	OrgID          *string  `form:"org_id,omitempty" json:"org_id,omitempty"`
	CreateAtLte    *int64   `form:"create_at_lte,omitempty" json:"create_at_lte,omitempty"`
	CreateAtGte    *int64   `form:"create_at_gte,omitempty" json:"create_at_gte,omitempty"`
	IDNotIn        []string `form:"id_not_in,omitempty" json:"id_not_in,omitempty"`
	CreateAtLt     *int64   `form:"create_at_lt,omitempty" json:"create_at_lt,omitempty"`
	CreateAtGt     *int64   `form:"create_at_gt,omitempty" json:"create_at_gt,omitempty"`
	IncludeDeleted *bool    `form:"include_deleted,omitempty" json:"include_deleted,omitempty"`
}

type SourceType string

const (
	Email    SourceType = "email"
	Zalo     SourceType = "zalo"
	FB       SourceType = "fb"
	Ads      SourceType = "ads"
	Direct   SourceType = "direct"
	JobFair  SourceType = "job_fair"
	Codemely SourceType = "code_mely"
	Discord  SourceType = "discord"
)

func GetValidSourceType(source string) string {
	if source == "" {
		return string(Direct)
	}
	switch SourceType(source) {
	case Email, Zalo, FB, Ads, Direct, JobFair, Codemely, Discord:
		return source
	default:
		return string(Direct)
	}
}
