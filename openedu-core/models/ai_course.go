package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"gorm.io/gorm"
)

type AICourseDurationType string

const (
	AICourseWeekTypeDuration AICourseDurationType = "week"
	AICourseDayTypeDuration  AICourseDurationType = "day"
)

type AICourse struct {
	Model
	PlaylistID       string           `json:"playlist_id" gorm:"column:playlist_id;type:varchar(50)"`
	PlaylistLink     string           `json:"playlist_link" gorm:"column:playlist_link;type:varchar(1000)"`
	OfferID          string           `json:"offer_id" gorm:"column:offer_id;type:varchar(50)"`
	CourseID         string           `json:"course_id" gorm:"column:course_id;type:varchar(20)"`
	CourseCuid       string           `json:"course_cuid" gorm:"column:course_cuid;type:varchar(20)"`
	Status           AIStatus         `json:"status" gorm:"column:status;type:varchar(20);default:pending"`
	TotalCost        float64          `json:"total_cost" gorm:"column:total_cost;type:decimal(20,2)"`
	Language         string           `json:"language" gorm:"column:language;type:varchar(20);default:'English'"`
	Tone             AITone           `json:"tone" gorm:"column:tone;type:varchar(20);default:'normal'"`
	OrgID            string           `json:"org_id" gorm:"column:org_id;type:varchar(20)"`
	OrgSchema        string           `json:"org_schema" gorm:"column:org_schema;type:varchar(20)"`
	ThumbnailID      string           `json:"thumbnail_id" gorm:"column:thumbnail_id;type:varchar(20)"`
	UserID           string           `json:"user_id" gorm:"column:user_id;type:varchar(20)"`
	RetryCount       int16            `json:"retry_count" gorm:"column:retry_count;type:int2;default:0"`
	OfferType        AIOfferType      `json:"offer_type" gorm:"column:offer_type;type:varchar(50)"`
	Title            string           `json:"course_title" gorm:"column:course_title;type:varchar(100)"`
	Description      string           `json:"course_description" gorm:"column:course_description;type:text"`
	Slug             string           `json:"slug" gorm:"column:slug;type:varchar(255)"`
	SummaryIncluded  bool             `json:"summary_included" gorm:"column:summary_included;type:boolean;default:false"`
	QuizIncluded     bool             `json:"quiz_included" gorm:"column:quiz_included;type:boolean;default:false"`
	QuizType         QuizQuestionType `json:"quiz_type" gorm:"column:quiz_type;type:varchar(50)"`
	QuizStatus       AIStatus         `json:"quiz_status" gorm:"column:quiz_status;type:varchar(20);default:manual"`
	QuizDetails      QuizDetails      `json:"quiz_details" gorm:"column:quiz_details;type:jsonb"`
	NumberOfQuestion int16            `json:"number_of_question" gorm:"column:number_of_question;type:int2;default:0"`
	Error            *DetailAIError   `json:"error" gorm:"column:error;type:jsonb"`
	CurrentStep      AIGenerateStep   `json:"current_step" gorm:"column:current_step;type:varchar(50)"`

	// generate with learner info
	GeneralInfoStatus AIStatus             `json:"general_info_status" gorm:"column:general_info_status;type:varchar(20);default:manual"`
	LearnerInfo       string               `json:"learner_info" gorm:"column:learner_info;type:text"`
	ContentInfo       string               `json:"content_info" gorm:"column:content_info;type:text"`
	MaterialID        string               `json:"material_id" gorm:"column:material_id;type:varchar(20)"`
	LevelID           string               `json:"level_id" gorm:"column:level_id;type:varchar(20)"`
	DurationType      AICourseDurationType `json:"duration_type" gorm:"column:duration_type;type:varchar(20)"`
	Duration          int16                `json:"duration" gorm:"column:duration;type:int2;default:0"`
	StudyLoad         int16                `json:"study_load" gorm:"column:study_load;type:int2;default:0"`

	// generate thumbnail
	ThumbnailGenerateCount int16            `json:"thumbnail_generate_count" gorm:"column:thumbnail_generate_count;type:int2;default:0"`
	ThumbnailStatus        AIStatus         `json:"thumbnail_status" gorm:"column:thumbnail_status;type:varchar(20)"`
	ThumbnailError         *DetailAIError   `json:"thumbnail_error" gorm:"column:thumbnail_error;type:jsonb"`
	ThumbnailDescription   string           `json:"thumbnail_description" gorm:"column:thumbnail_description;type:text"`
	ThumbnailStyle         AIThumbnailStyle `json:"thumbnail_style" gorm:"column:thumbnail_style;type:varchar(50);default:general"`
	ThumbnailQuantity      int16            `json:"thumbnail_quantity" gorm:"column:thumbnail_quantity;type:int2;default:0"`
	GeneratedThumbnailIDs  StringArray      `json:"generated_thumbnail_ids" gorm:"column:generated_thumbnail_ids;type:jsonb"`
	ThumbnailGenerated     []*File          `json:"thumbnail_generated" gorm:"-"`
	Material               *File            `json:"material" gorm:"-"`

	// store history ai
	GenerateHistories []*AIHistory `json:"generate_histories" gorm:"-"`
}

func (s DetailAIError) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	return string(val), err
}

func (s *DetailAIError) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &s)
	if err != nil {
		return err
	}

	return nil
}

type QuizDetail struct {
	Status  AIStatus `json:"status"`
	OfferID string   `json:"offer_id"`
	QuizID  string   `json:"quiz_id"`
}

func (s QuizDetail) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	return string(val), err
}

func (s *QuizDetail) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &s)
	if err != nil {
		return err
	}

	return nil
}

type QuizDetails []*QuizDetail

func (s QuizDetails) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *QuizDetails) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

type AICourseQuery struct {
	PlaylistID       *string           `json:"playlist_id,omitempty" form:"playlist_id"`
	OfferID          *string           `json:"offer_id,omitempty" form:"offer_id"`
	CourseID         *string           `json:"course_id,omitempty" form:"course_id"`
	CourseCuid       *string           `json:"course_cuid,omitempty" form:"course_cuid"`
	Status           *AIStatus         `json:"status,omitempty" form:"status"`
	TotalCost        *float64          `json:"total_cost,omitempty" form:"total_cost"`
	Language         *string           `json:"language,omitempty" form:"language"`
	Tone             *AITone           `json:"tone,omitempty" form:"tone"`
	CourseDetails    *string           `json:"course_details,omitempty" form:"course_details"`
	ThumbnailID      *string           `json:"thumbnail_id,omitempty" form:"thumbnail_id"`
	OrgID            *string           `json:"org_id,omitempty" form:"org_id"`
	UserID           *string           `json:"user_id,omitempty" form:"user_id"`
	PlaylistIDIn     []string          `json:"playlist_id_in,omitempty" form:"playlist_id_in"`
	IDIn             []string          `json:"id_in,omitempty" form:"id_in"`
	CourseIDIn       []string          `json:"course_id_in,omitempty" form:"course_id_in"`
	StatusIn         []*AIStatus       `json:"status_in,omitempty" form:"status_in"`
	CourseCuidIn     []string          `json:"course_cuid_in,omitempty" form:"course_cuid_in"`
	CurrentStepNotIn []*AIGenerateStep `json:"current_step_not_in,omitempty" form:"current_step_not_in"`
	CreateAtLt       *int64            `json:"create_at_lt" form:"create_at_lt"`
}

type SimpleAICourse struct {
	Model
	PlaylistID       string           `json:"playlist_id"`
	PlaylistLink     string           `json:"playlist_link"`
	OfferID          string           `json:"offer_id"`
	CourseID         string           `json:"course_id"`
	CourseCuid       string           `json:"course_cuid"`
	Status           AIStatus         `json:"status"`
	TotalCost        float64          `json:"total_cost"`
	Language         string           `json:"language"`
	Tone             AITone           `json:"tone"`
	OrgID            string           `json:"org_id"`
	ThumbnailID      string           `json:"thumbnail_id"`
	UserID           string           `json:"user_id"`
	RetryCount       int16            `json:"retry_count"`
	OfferType        AIOfferType      `json:"offer_type"`
	Title            string           `json:"course_title"`
	Description      string           `json:"course_description"`
	Slug             string           `json:"slug"`
	SummaryIncluded  bool             `json:"summary_included"`
	QuizIncluded     bool             `json:"quiz_included"`
	QuizType         QuizQuestionType `json:"quiz_type"`
	NumberOfQuestion int16            `json:"number_of_question"`
	Error            *DetailAIError   `json:"error"`

	GeneralInfoStatus AIStatus             `json:"general_info_status"`
	LearnerInfo       string               `json:"learner_info"`
	ContentInfo       string               `json:"content_info"`
	MaterialID        string               `json:"material_id"`
	LevelID           string               `json:"level_id"`
	DurationType      AICourseDurationType `json:"duration_type"`
	Duration          int16                `json:"duration"`
	StudyLoad         int16                `json:"study_load"`

	ThumbnailGenerateCount int16            `json:"thumbnail_generate_count"`
	ThumbnailStatus        AIStatus         `json:"thumbnail_status"`
	ThumbnailDescription   string           `json:"thumbnail_description"`
	ThumbnailStyle         AIThumbnailStyle `json:"thumbnail_style"`
	ThumbnailQuantity      int16            `json:"thumbnail_quantity"`
	GeneratedThumbnailIDs  []string         `json:"generated_thumbnail_ids"`
	ThumbnailGenerated     []*File          `json:"thumbnail_generated"`
	Material               *File            `json:"material"`

	GenerateHistories []*AIHistory `json:"generate_histories" gorm:"-"`
}

func (c *AICourse) ToSimple() *SimpleAICourse {
	language := Repository.AIHistory.GetKeyFromLanguage(c.Language)
	return &SimpleAICourse{
		Model:                  c.Model,
		PlaylistID:             c.PlaylistID,
		PlaylistLink:           c.PlaylistLink,
		OfferID:                c.OfferID,
		CourseID:               c.CourseID,
		CourseCuid:             c.CourseCuid,
		Status:                 c.Status,
		TotalCost:              c.TotalCost,
		Language:               language,
		Tone:                   c.Tone,
		OrgID:                  c.OrgID,
		ThumbnailID:            c.ThumbnailID,
		UserID:                 c.UserID,
		RetryCount:             c.RetryCount,
		OfferType:              c.OfferType,
		Title:                  c.Title,
		Description:            c.Description,
		Slug:                   c.Slug,
		SummaryIncluded:        c.SummaryIncluded,
		QuizIncluded:           c.QuizIncluded,
		QuizType:               c.QuizType,
		NumberOfQuestion:       c.NumberOfQuestion,
		Error:                  c.Error,
		GeneralInfoStatus:      c.GeneralInfoStatus,
		LearnerInfo:            c.LearnerInfo,
		ContentInfo:            c.ContentInfo,
		MaterialID:             c.MaterialID,
		LevelID:                c.LevelID,
		DurationType:           c.DurationType,
		Duration:               c.Duration,
		StudyLoad:              c.StudyLoad,
		ThumbnailGenerateCount: c.ThumbnailGenerateCount,
		ThumbnailStatus:        c.ThumbnailStatus,
		ThumbnailDescription:   c.ThumbnailDescription,
		ThumbnailStyle:         c.ThumbnailStyle,
		ThumbnailQuantity:      c.ThumbnailQuantity,
		GeneratedThumbnailIDs:  c.GeneratedThumbnailIDs,
		ThumbnailGenerated:     c.ThumbnailGenerated,
		Material:               c.Material,
	}
}

func (query *AICourseQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.PlaylistID != nil {
		qb = qb.Where("playlist_id = ?", *query.PlaylistID)
	}

	if query.OfferID != nil {
		qb = qb.Where("offer_id = ?", *query.OfferID)
	}

	if query.CourseID != nil {
		qb = qb.Where("course_id = ?", *query.CourseID)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *query.CourseCuid)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.Language != nil {
		qb = qb.Where("language = ?", *query.Language)
	}

	if query.Tone != nil {
		qb = qb.Where("tone = ?", *query.Tone)
	}

	if query.CourseDetails != nil {
		qb = qb.Where("course_details = ?", *query.CourseDetails)
	}

	if query.ThumbnailID != nil {
		qb = qb.Where("thumbnail_id = ?", *query.ThumbnailID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if len(query.PlaylistIDIn) > 0 {
		qb = qb.Where("playlist_id IN ?", query.PlaylistIDIn)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN ?", query.IDIn)
	}

	if len(query.CourseIDIn) > 0 {
		qb = qb.Where("course_id IN ?", query.CourseIDIn)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN ?", query.StatusIn)
	}

	if len(query.CourseCuidIn) > 0 {
		qb = qb.Where("course_cuid IN ?", query.CourseCuidIn)
	}

	if len(query.CurrentStepNotIn) > 0 {
		qb = qb.Where("current_step NOT IN ?", query.CurrentStepNotIn)
	}

	if query.CreateAtLt != nil {
		qb = qb.Where("create_at < ?", *query.CreateAtLt)
	}

	return qb
}

// Create inserts a aiCourse to database, transaction is optional
func (r *AICourseRepository) Create(ac *AICourse, trans *gorm.DB) error {
	return create(AICourseTbl, ac, trans)
}

func (r *AICourseRepository) CreateMany(acs []*AICourse, trans *gorm.DB) error {
	return createMany(AICourseTbl, acs, trans)
}

// Update updates a aiCourse by ID in database, transaction is optional
func (r *AICourseRepository) Update(ac *AICourse, trans *gorm.DB) error {
	return update(AICourseTbl, ac, trans)
}

// FindByID finds a aiCourse by ID with given find options, transaction is optional
func (r *AICourseRepository) FindByID(id string, options *FindOneOptions) (*AICourse, error) {
	return findByID[AICourse](AICourseTbl, id, options)
}

// FindOne finds one aiCourse with given find queries and options, transaction is optional
func (r *AICourseRepository) FindOne(query *AICourseQuery, options *FindOneOptions) (*AICourse, error) {
	return findOne[AICourse](AICourseTbl, query, options)
}

// FindMany finds userTokens by query conditions with give find options
func (r *AICourseRepository) FindMany(query *AICourseQuery, options *FindManyOptions) ([]*AICourse, error) {
	return findMany[AICourse](AICourseTbl, query, options)
}

// FindPage returns userTokens and pagination by query conditions and find options, transaction is optional
func (r *AICourseRepository) FindPage(query *AICourseQuery, options *FindPageOptions) ([]*AICourse, *Pagination, error) {
	return findPage[AICourse](AICourseTbl, query, options)
}

// Delete perform soft deletion to a userTokens by ID, transaction is optional
func (r *AICourseRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[AICourse](AICourseTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *AICourseRepository) DeleteMany(query *AICourseQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[AICourse](AICourseTbl, query, trans)
}
