package models

import (
	"errors"
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"slices"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CategoryRelation struct {
	Model
	CategoryID  string    `json:"category_id" gorm:"type:varchar(20);not null"`
	RelatedID   string    `json:"related_id" gorm:"type:varchar(20);not null"`
	RelatedType ModelName `json:"related_type" gorm:"type:varchar(20);not null"`
	Field       string    `json:"field" gorm:"type:varchar(20)"`
	Order       int       `json:"order" gorm:"type:int8;not null;default:0"`
	Category    Category  `json:"category"`
}

type CategoryRelationQuery struct {
	ID             *string    `json:"id,omitempty" form:"id"`
	CategoryID     *string    `json:"category_id,omitempty" form:"category_id"`
	RelatedID      *string    `json:"related_id,omitempty" form:"related_id"`
	RelatedType    *ModelName `json:"related_type,omitempty" form:"related_type"`
	Field          *string    `json:"field,omitempty" form:"field"`
	FieldIn        []string   `json:"field_in,omitempty" form:"field_in"`
	IncludeDeleted *bool      `json:"include_deleted,omitempty" form:"include_deleted"`
	RelatedIDIn    []string   `json:"related_id_in,omitempty" form:"related_id_in"`
	CategoryIDIn   []string   `json:"category_id_in,omitempty" form:"category_id_in"`
	IDIn           []string   `json:"id_in,omitempty" form:"id_in"`
	BlogCuid       *string    `form:"blog_cuid"`
	BlogCuidIn     []string   `form:"blog_cuid_in"`
	Locale         *string    `json:"locale" form:"locale"`
}

func (query *CategoryRelationQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	cateRelationTblName := setting.DatabaseSetting.TablePrefix + string(CategoryRelationTbl)
	if query.ID != nil {
		qb = qb.Where(cateRelationTblName+".id = ?", *query.ID)
	}

	if query.CategoryID != nil {
		qb = qb.Where(cateRelationTblName+".category_id = ?", *query.CategoryID)
	}

	if query.RelatedID != nil {
		qb = qb.Where(cateRelationTblName+".related_id = ?", *query.RelatedID)
	}

	if query.RelatedType != nil {
		qb = qb.Where(cateRelationTblName+".related_type = ?", *query.RelatedType)
	}

	if query.Field != nil {
		qb = qb.Where(cateRelationTblName+".field = ?", *query.Field)
	}

	if len(query.FieldIn) > 0 {
		qb = qb.Where(cateRelationTblName+".field IN (?)", query.FieldIn)
	}

	if len(query.RelatedIDIn) > 0 {
		qb = qb.Where(cateRelationTblName+".related_id IN (?)", query.RelatedIDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where(cateRelationTblName + ".delete_at = 0")
	}

	if len(query.CategoryIDIn) > 0 {
		qb = qb.Where(cateRelationTblName+".category_id IN (?)", query.CategoryIDIn)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where(cateRelationTblName+".id IN (?)", query.IDIn)
	}

	return qb
}

func (query *CategoryRelationQuery) ApplyJoinBlog(tx *gorm.DB) *gorm.DB {
	qb := tx

	if query.ID != nil {
		qb = qb.Where("cr.id = ?", *query.ID)
	}

	if query.CategoryID != nil {
		qb = qb.Where("cr.category_id = ?", *query.CategoryID)
	}

	if query.RelatedID != nil {
		qb = qb.Where("cr.related_id = ?", *query.RelatedID)
	}

	if query.RelatedType != nil {
		qb = qb.Where("cr.related_type = ?", *query.RelatedType)
	}

	if query.Field != nil {
		qb = qb.Where("cr.field = ?", *query.Field)
	}

	if len(query.RelatedIDIn) > 0 {
		qb = qb.Where("cr.related_id IN (?)", query.RelatedIDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("cr.delete_at = 0")
	}

	if query.BlogCuid != nil {
		qb = qb.Where("b.blog_cuid = ?", *query.BlogCuid)
	}

	if len(query.BlogCuidIn) > 0 {
		qb = qb.Where("b.blog_cuid IN (?)", query.BlogCuidIn)
	}

	if len(query.CategoryIDIn) > 0 {
		qb = qb.Where("cr.category_id IN (?)", query.CategoryIDIn)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("cr.id IN (?)", query.IDIn)
	}

	if query.Locale != nil {
		qb = qb.Where("b.locale = ?", *query.Locale)
	}

	return qb
}

// Create inserts a CategoryRelation to database, transaction is optional
func (r *CategoryRelationRepository) Create(ce *CategoryRelation, trans *gorm.DB) error {
	return create(CategoryRelationTbl, ce, trans)
}

func (r *CategoryRelationRepository) CreateMany(ces []*CategoryRelation, trans *gorm.DB) error {
	return createMany(CategoryRelationTbl, ces, trans)
}

// Update updates a CategoryRelation by ID in database, transaction is optional
func (r *CategoryRelationRepository) Update(ce *CategoryRelation, trans *gorm.DB) error {
	return update(CategoryRelationTbl, ce, trans)
}

func (r *CategoryRelationRepository) UpsertMany(ces []*CategoryRelation, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "category_id"}, {Name: "related_id"}, {Name: "field"}},
		DoUpdates: clause.AssignmentColumns([]string{"related_type", "order", "update_at", "delete_at"}),
	}).Create(&ces).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit tx
	return
}

// FindByID finds a CategoryRelation by ID with given find options, transaction is optional
func (r *CategoryRelationRepository) FindByID(id string, options *FindOneOptions) (*CategoryRelation, error) {
	return findByID[CategoryRelation](CategoryRelationTbl, id, options)
}

// FindOne finds one CategoryRelation with given find queries and options, transaction is optional
func (r *CategoryRelationRepository) FindOne(query *CategoryRelationQuery, options *FindOneOptions) (*CategoryRelation, error) {
	return findOne[CategoryRelation](CategoryRelationTbl, query, options)
}

// FindMany finds CategoryRelations by query conditions with give find options
func (r *CategoryRelationRepository) FindMany(query *CategoryRelationQuery, options *FindManyOptions) ([]*CategoryRelation, error) {
	return findMany[CategoryRelation](CategoryRelationTbl, query, options)
}

// FindPage returns CategoryRelations and pagination by query conditions and find options, transaction is optional
func (r *CategoryRelationRepository) FindPage(query *CategoryRelationQuery, options *FindPageOptions) ([]*CategoryRelation, *Pagination, error) {
	return findPage[CategoryRelation](CategoryRelationTbl, query, options)
}

func (r *CategoryRelationRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[CategoryRelation](CategoryRelationTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *CategoryRelationRepository) DeleteMany(query *CategoryRelationQuery, trans *gorm.DB) (deletedCount int64, err error) {
	return deleteMany[CategoryRelation](CategoryRelationTbl, query, trans)
}

// Count returns number of CategoryRelations by query conditions, transaction is optional
func (r *CategoryRelationRepository) Count(query *CategoryRelationQuery) (int64, error) {
	return count[CategoryRelation](CategoryRelationTbl, query)
}

func GetCategoryParent(id string, categoryMap map[string]*Category, result []string) []string {
	if category, ok := categoryMap[id]; ok && !slices.Contains(result, id) {
		if category.ParentID != nil {
			parentResult := GetCategoryParent(*category.ParentID, categoryMap, result)
			result = append(result, parentResult...)
		}
		result = append(result, id)
	}
	return result
}

func (r *CategoryRelationRepository) AddCategories(model ModelName, entityID string, field string, categories []*Category, trans *gorm.DB) (err error) {
	// TODO optimize
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	// Get all categories
	allCategories, err := Repository.Category.FindMany(&CategoryQuery{}, &FindManyOptions{})
	if err != nil {
		return err
	}

	// Convert all Categories to map[string]*models.Category
	allCategoriesByIDs := make(map[string]*Category)
	for _, category := range allCategories {
		allCategoriesByIDs[category.ID] = category
	}

	categoryIDs := lo.Map(categories, func(item *Category, _ int) string {
		return item.ID
	})

	// Use Recursion to fetch all relate category parent id in queryCategories
	// storeCategoryIds use as a checker to verify if duplicate categoryId
	// resultCategoryIds use as a result after Recursion finish
	var resultCategoryIds []string
	var storeCategoryIds []string
	for _, inputCategory := range categories {
		resultCategoryIds = append(resultCategoryIds, GetCategoryParent(inputCategory.ID, allCategoriesByIDs, storeCategoryIds)...)
	}

	resultCategoryIds = util.RemoveDuplicateValues(resultCategoryIds)

	var toCreate []*CategoryRelation
	// Loop all resultCategoryIds and Append processed entity to toCreate and start new loop
	order := 1
	for _, resultCategoryId := range resultCategoryIds {
		elem := &CategoryRelation{
			RelatedID:   entityID,
			RelatedType: model,
			CategoryID:  resultCategoryId,
			Field:       field,
			Order:       0,
		}
		// if input category use input order
		if lo.Contains(categoryIDs, resultCategoryId) {
			elem.Order = order
			order++
		}
		toCreate = append(toCreate, elem)
	}

	toCreateMap := make(map[string]bool)
	for _, n := range toCreate {
		toCreateMap[n.CategoryID] = true
	}

	query := CategoryRelationQuery{
		RelatedID:   util.NewString(entityID),
		RelatedType: util.NewT(model),
		Field:       util.NewString(field),
	}
	var exists []*CategoryRelation
	if existRelations, err := Repository.CategoryRelation.FindMany(&query, nil); err != nil {
		return err
	} else {
		exists = existRelations
	}

	existsMap := make(map[string]bool)
	for _, exist := range exists {
		existsMap[exist.CategoryID] = true
	}

	// exists - toCreate = toDelete
	var toDeleteIds []string
	var decreaseCategoryIDs []string
	for _, exist := range exists {
		if _, found := toCreateMap[exist.CategoryID]; !found {
			toDeleteIds = append(toDeleteIds, exist.ID)
			decreaseCategoryIDs = append(decreaseCategoryIDs, exist.CategoryID)
		}
	}

	// find not exists for increase use_count
	var increaseCategoryIDs []string
	for _, n := range toCreate {
		if _, found := existsMap[n.CategoryID]; !found {
			increaseCategoryIDs = append(increaseCategoryIDs, n.CategoryID)
		}
	}

	// upsert many toCreate
	if len(toCreate) > 0 {
		if err := Repository.CategoryRelation.UpsertMany(toCreate, tx); err != nil {
			return err
		}
	}

	// delete many toDelete
	if len(toDeleteIds) > 0 {
		if _, err := Repository.CategoryRelation.DeleteMany(&CategoryRelationQuery{IDIn: toDeleteIds}, tx); err != nil {
			log.Error("Api::CategoryRelation.DeleteMany failed", err)
			return err
		}
	}

	// increase many increaseCategoryIDs
	if len(increaseCategoryIDs) > 0 {
		if err := Repository.Category.IncreaseManyUseCount(&CategoryQuery{
			IDIn: increaseCategoryIDs,
		}, util.CategoryCount, util.CategoryUnit, tx); err != nil {
			log.Error("Api::Category.IncreaseManyUseCount failed", err)
			return err
		}
	}

	// increase many decreaseCategoryIDs
	if len(decreaseCategoryIDs) > 0 {
		if err := Repository.Category.DecreaseManyUseCount(&CategoryQuery{
			IDIn: decreaseCategoryIDs,
		}, util.CategoryCount, util.CategoryUnit, tx); err != nil {
			log.Error("Api::Category.DecreaseManyUseCount failed", err)
			return err
		}
	}
	return nil

	//if categories == nil {
	//	categories = []*Category{}
	//}
	//query := CategoryRelationQuery{
	//	RelatedID:   util.NewString(entityID),
	//	RelatedType: util.NewT(model),
	//	Field:       util.NewString(field),
	//}
	//var exists []*CategoryRelation
	//if relations, err := Repository.CategoryRelation.FindMany(&query, nil); err != nil {
	//	return err
	//} else {
	//	exists = relations
	//}
	//
	//var relations []*CategoryRelation
	//for idx, c := range categories {
	//	relations = append(relations, &CategoryRelation{
	//		CategoryID:  c.ID,
	//		RelatedID:   entityID,
	//		RelatedType: model,
	//		Field:       field,
	//		Order:       idx + 1,
	//	})
	//}
	//
	//toCreate := lo.Filter(relations, func(fl *CategoryRelation, _ int) bool {
	//	_, ok := lo.Find(exists, func(e *CategoryRelation) bool {
	//		return e.CategoryID == fl.CategoryID
	//	})
	//	return !ok
	//})
	//
	//log.Debug("toCreate: ", len(toCreate))
	//if len(toCreate) > 0 {
	//	if rErr := Repository.CategoryRelation.CreateMany(toCreate, nil); rErr != nil {
	//		log.Debug(rErr)
	//		return errors.New(fmt.Sprintf("Update `%s` for `%s` failed", field, model))
	//	}
	//}
	//
	//toDelete := lo.Filter(exists, func(e *CategoryRelation, index int) bool {
	//	_, ok := lo.Find(relations, func(r *CategoryRelation) bool {
	//		return r.CategoryID == e.CategoryID
	//	})
	//	return !ok
	//})
	//
	//log.Debug("toDelete: ", len(toCreate))
	//
	//// DeleteMany
	//lo.ForEach(toDelete, func(item *CategoryRelation, _ int) {
	//	delQuery := &CategoryRelationQuery{
	//		CategoryID: util.NewString(item.CategoryID),
	//		RelatedID:  util.NewString(item.RelatedID),
	//		Field:      util.NewString(field),
	//	}
	//	Repository.CategoryRelation.DeleteMany(delQuery, nil)
	//})
	//return nil
}

func (r *CategoryRelationRepository) GetCategories(model ModelName, entityID string, field string) ([]*Category, error) {
	query := CategoryRelationQuery{
		RelatedID:   util.NewString(entityID),
		RelatedType: util.NewT(model),
		Field:       util.NewString(field),
	}
	option := FindManyOptions{
		Preloads: []string{CategoryField},
		Sort:     []string{OrderASC},
	}
	if relations, rErr := Repository.CategoryRelation.FindMany(&query, &option); rErr != nil {
		return nil, errors.New("get categories relation failed: " + rErr.Error())
	} else {
		return lo.Map(relations, func(item *CategoryRelation, _ int) *Category {
			return &item.Category
		}), nil
	}
}

func (r *CategoryRelationRepository) GetCategoriesByEntities(model ModelName, entitiesID []string, field string) (map[string][]*Category, error) {
	relations, err := Repository.CategoryRelation.FindMany(&CategoryRelationQuery{
		RelatedIDIn: lo.Uniq(entitiesID),
		Field:       util.NewString(field),
		RelatedType: util.NewT(model),
	}, &FindManyOptions{
		Joins: []string{CategoryField},
		Sort:  []string{OrderASC},
	})
	if err != nil {
		return nil, err
	}

	relationsByEntitiesIDs := map[string][]*Category{}
	lo.ForEach(relations, func(fr *CategoryRelation, _ int) {
		if _, found := relationsByEntitiesIDs[fr.RelatedID]; found {
			relationsByEntitiesIDs[fr.RelatedID] = append(relationsByEntitiesIDs[fr.RelatedID], &fr.Category)
		} else {
			relationsByEntitiesIDs[fr.RelatedID] = []*Category{&fr.Category}
		}
	})

	return relationsByEntitiesIDs, nil
}

func (r *CategoryRelationRepository) GetSimpleCategoriesByEntities(model ModelName, entitiesID []string, field string) (map[string][]*SimpleCategory, error) {
	relations, err := Repository.CategoryRelation.FindMany(&CategoryRelationQuery{
		RelatedIDIn: lo.Uniq(entitiesID),
		Field:       util.NewString(field),
		RelatedType: util.NewT(model),
	}, &FindManyOptions{
		Preloads: []string{CategoryField},
		Sort:     []string{OrderASC},
	})
	if err != nil {
		return nil, err
	}

	relationsByEntitiesIDs := map[string][]*SimpleCategory{}
	lo.ForEach(relations, func(fr *CategoryRelation, _ int) {
		relationsByEntitiesIDs[fr.RelatedID] = append(relationsByEntitiesIDs[fr.RelatedID], fr.Category.Sanitize())
	})

	return relationsByEntitiesIDs, nil
}

func (r *CategoryRelationRepository) FindPageJoinBlog(query *CategoryRelationQuery, options *FindPageOptions) ([]*CategoryRelation, *Pagination, error) {
	entitiesChan := make(chan []*CategoryRelation)
	countChan := make(chan int64)
	errorChan := make(chan error)
	var err error
	defer func() {
		if re := recover(); re != nil {
			err = fmt.Errorf("panic: %v", re)
		}
	}()

	go func() {
		defer func() {
			if re := recover(); re != nil {
				errorChan <- fmt.Errorf("panic: %v", re)
			}
		}()

		var entities []*CategoryRelation
		qb := query.ApplyJoinBlog(DB)
		lo.ForEach(options.Sort, func(clause string, _ int) {
			qb = qb.Order("cr." + clause)
		})
		qb = qb.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)

		result := qb.Table(GetTblName(CategoryRelationTbl) + " as cr").Debug().
			Select("cr.*").
			Joins("join " + GetTblName(PublishBlogTbl) + " as b on b.blog_cuid = cr.related_id").
			Find(&entities)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if re := recover(); re != nil {
				errorChan <- fmt.Errorf("panic: %v", re)
			}
		}()

		var c int64
		var entity CategoryRelation
		qb := query.ApplyJoinBlog(DB)
		result := qb.Table(GetTblName(CategoryRelationTbl) + " as cr").Debug().
			Joins("join " + GetTblName(PublishBlogTbl) + " as b on b.blog_cuid = cr.related_id").
			Model(&entity).Count(&c)
		if err := result.Error; err != nil {
			errorChan <- err
			return
		}
		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*CategoryRelation
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

func (r *CategoryRelationRepository) FindManyJoinBlog(query *CategoryRelationQuery, options *FindManyOptions) ([]*CategoryRelation, error) {
	var entities []*CategoryRelation

	qb := query.ApplyJoinBlog(DB)
	lo.ForEach(options.Sort, func(clause string, _ int) {
		qb = qb.Order("cr." + clause)
	})

	err := qb.Table(GetTblName(CategoryRelationTbl) + " as cr").Debug().
		Select("cr.*").
		Joins("join " + GetTblName(PublishBlogTbl) + " as b on b.blog_cuid = cr.related_id").
		Find(&entities).Error

	if err != nil {
		return nil, err
	}

	return entities, nil
}
