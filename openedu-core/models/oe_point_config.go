package models

import "github.com/shopspring/decimal"

type OEPointSystemConfig struct {
	Name     string          `json:"name"`
	Point    decimal.Decimal `json:"point"`
	Currency Currency        `json:"currency"`
	Rate     decimal.Decimal `json:"rate"` // 1P = rate USD

	PointExpiration  bool       `json:"point_expiration" gorm:"default:true"`
	ExpirationPeriod TimePeriod `json:"expiration_period" gorm:"default:0"`
	ExpirationVal    int        `json:"expiration_val" gorm:"default:0"`

	GracePeriod TimePeriod `json:"grace_period" gorm:"default:0"`
	GraceVal    int        `json:"grace_val" gorm:"default:0"`
}
