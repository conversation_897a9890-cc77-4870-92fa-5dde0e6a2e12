package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
)

type Duration time.Duration

func (d *Duration) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	parts := strings.Split(s, ":")
	if len(parts) != 3 {
		return fmt.Errorf("invalid time format, should be hh:mm:ss")
	}

	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return fmt.Errorf("invalid hour format: %v", err)
	}

	minutes, err := strconv.Atoi(parts[1])
	if err != nil {
		return fmt.Errorf("invalid minute format: %v", err)
	}

	seconds, err := strconv.Atoi(parts[2])
	if err != nil {
		return fmt.Errorf("invalid second format: %v", err)
	}

	*d = Duration(time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute + time.Duration(seconds)*time.Second)
	return nil
}

func (d Duration) MarshalJSON() ([]byte, error) {
	totalSeconds := int(time.Duration(d).Seconds())
	hours := totalSeconds / 3600
	minutes := (totalSeconds % 3600) / 60
	seconds := totalSeconds % 60
	s := fmt.Sprintf("\"%02d:%02d:%02d\"", hours, minutes, seconds)
	return []byte(s), nil
}

//func (d Duration) MarshalJSON() ([]byte, error) {
//	return json.Marshal(time.Duration(d).String())
//}
//
//func (d *Duration) UnmarshalJSON(b []byte) error {
//	var v interface{}
//	if err := json.Unmarshal(b, &v); err != nil {
//		return err
//	}
//	switch value := v.(type) {
//	case float64:
//		*d = Duration(time.Duration(value))
//		return nil
//	case string:
//		tmp, err := time.ParseDuration(value)
//		if err != nil {
//			return err
//		}
//		*d = Duration(tmp)
//		return nil
//	default:
//		return errors.New("invalid duration")
//	}
//}

func (d Duration) Value() (driver.Value, error) {
	val, err := json.Marshal(d)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (d *Duration) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, d)
}
