package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type QuizPassCriteria string

const (
	QuizPassCriteriaPercentage     = "percentage"
	QuizPassCriteriaCorrectAnswers = "correct_answers"
)

var QuizPassCriterias = map[QuizPassCriteria]struct{}{
	QuizPassCriteriaPercentage:     {},
	QuizPassCriteriaCorrectAnswers: {},
}

type QuizStreakBonusType string

const (
	QuizStreakBonusTypePercentage = "percentage"
	QuizStreakBonusTypePoints     = "points"
)

var QuizStreakBonusTypes = map[QuizStreakBonusType]struct{}{
	QuizStreakBonusTypePercentage: {},
	QuizStreakBonusTypePoints:     {},
}

type QuizTimeLimitType string

const (
	QuizTimeLimitTypeOverall     = "overall"
	QuizTimeLimitTypePerQuestion = "per_question"
)

type Quiz struct {
	Model
	OrgID       string          `json:"org_id"`
	UID         string          `json:"uid"`
	Version     int             `json:"version"`
	Title       string          `json:"title" gorm:"type:varchar(255)"`
	Description string          `json:"description"`
	CreatorID   string          `json:"creator_id" gorm:"type:varchar(20)"`
	Files       []*File         `json:"files" gorm:"-"`
	Settings    QuizSettings    `json:"settings" gorm:"type:jsonb"`
	Questions   []*QuizQuestion `json:"questions" gorm:"foreignKey:QuizID"`
}

type QuizSettings struct {
	ShowCorrectAnswersEnabled      bool                `json:"show_correct_answers_enabled"`
	ShuffleQuestionsEnabled        bool                `json:"shuffle_questions_enabled"`
	ShuffleChoicesEnabled          bool                `json:"shuffle_choices_enabled"`
	TimeLimitEnabled               bool                `json:"time_limit_enabled"`
	TimeLimitType                  QuizTimeLimitType   `json:"time_limit_type"` // overall, per_question
	TimeLimit                      Duration            `json:"time_limit"`
	SubmissionLimitEnabled         bool                `json:"submission_limit_enabled"`
	SubmissionLimit                int                 `json:"submission_limit"`
	PassCriteria                   QuizPassCriteria    `json:"pass_criteria"` // percentage, correct_answers
	MinPercentageToPass            float64             `json:"min_percentage_to_pass"`
	MinCorrectAnswersToPass        int                 `json:"min_correct_answers_to_pass"`
	TimeBonusPointsEnabled         bool                `json:"time_bonus_points_enabled"`
	TimeBonusPointsPerSecond       int                 `json:"time_bonus_points_per_second"`
	StreakBonusEnabled             bool                `json:"streak_bonus_enabled"`
	StreakBonusType                QuizStreakBonusType `json:"streak_bonus_type"` // percentage, points
	StreakBonusPercentageIncrement float64             `json:"streak_bonus_percentage_increment"`
	StreakBonusMaxPercentage       float64             `json:"streak_bonus_max_percentage"`
	PenaltyPointsEnabled           bool                `json:"penalty_points_enabled"`
	PenaltyPointsPerWrongAnswer    int                 `json:"penalty_points_per_wrong_answer"`
}

func (s QuizSettings) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *QuizSettings) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

type SimpleQuiz struct {
	Model
	Title        string  `json:"title"`
	Description  string  `json:"description"`
	CreatorID    string  `json:"creator_id"`
	SourceQuizID *string `json:"source_quiz_id"`
	Settings     QuizSettings
}

func (q *Quiz) Sanitize() *Quiz {
	sanitizedQuiz := q.DeepCopy()
	sanitizedQuiz.Questions = nil
	return sanitizedQuiz
}

func (q *Quiz) DeepCopy() *Quiz {
	return &Quiz{
		Model:       q.Model,
		UID:         q.UID,
		Version:     q.Version,
		Title:       q.Title,
		Description: q.Description,
		CreatorID:   q.CreatorID,
		Files: lo.Map(q.Files, func(file *File, _ int) *File {
			return file.DeepCopy()
		}),
		Settings:  q.Settings,
		Questions: q.Questions,
	}
}

func (q *Quiz) ToSimple() *SimpleQuiz {
	return &SimpleQuiz{
		Model:       q.Model,
		Title:       q.Title,
		Description: q.Description,
		CreatorID:   q.CreatorID,
		Settings:    q.Settings,
	}
}

func (q *Quiz) GetID() string {
	return q.ID
}

func (q *Quiz) IsTimeLimitEnabled() bool {
	return q.Settings.TimeLimitEnabled
}

func (q *Quiz) IsTimeLimitOverall() bool {
	return q.Settings.TimeLimitType == QuizTimeLimitTypeOverall
}

func (q *Quiz) IsTimeLimitPerQuestion() bool {
	return q.Settings.TimeLimitType == QuizTimeLimitTypePerQuestion
}

func (q *Quiz) IsTimeBonusPointsEnabled() bool {
	return q.Settings.TimeBonusPointsEnabled
}

func (q *Quiz) IsStreakBonusEnabled() bool {
	return q.Settings.StreakBonusEnabled
}

func (q *Quiz) IsPenaltyPointsEnabled() bool {
	return q.Settings.PenaltyPointsEnabled
}

func (q *Quiz) IsShuffleChoicesEnabled() bool {
	return q.Settings.ShuffleChoicesEnabled
}

func (q *Quiz) ShuffleQuestionChoices() {
	for _, question := range q.Questions {
		question.ShuffleChoices()
	}
}

func (q *Quiz) IsShuffleQuestionsEnabled() bool {
	return q.Settings.ShuffleQuestionsEnabled
}

func (q *Quiz) ShuffleQuestions() {
	q.Questions = lo.Shuffle(q.Questions)
}

func (q *Quiz) CheckValid() error {
	//if q.Title == "" {
	//	return errors.New("title is required")
	//}

	if q.CreatorID == "" {
		return errors.New("creator_id is required")
	}

	if q.Settings.PassCriteria == "" {
		return errors.New("settings.pass_criteria is required")
	}

	if _, ok := QuizPassCriterias[q.Settings.PassCriteria]; !ok {
		return errors.New("settings.pass_criteria is invalid")
	}

	return nil
}

func (q *Quiz) CanUpdateQuiz(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	if user.ID == q.CreatorID {
		return true
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Quiz:CanUpdateQuiz: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
		return IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType
	})
}

func (q *Quiz) CanViewQuiz(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	if user.ID == q.CreatorID {
		return true
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Quiz:CanViewQuiz: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
		return IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType
	})
}

func (q *Quiz) CanDuplicateQuiz(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	if user.ID == q.CreatorID {
		return true
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Quiz:CanUpdateQuiz: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
		return IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType
	})
}

func (q *Quiz) CanDeleteQuiz(user *User, org *Organization) bool {
	if user == nil || org == nil {
		return false
	}

	if user.ID == q.CreatorID {
		return true
	}

	roles, err := Repository.UserRoleOrg.FindByUserId(user.ID)
	if err != nil {
		log.Error("Model:Quiz:CanDeleteQuiz: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return lo.ContainsBy(roles, func(role *UserRoleOrg) bool {
		return IsOrgAdminRole(role.RoleID) || role.RoleID == PartnerRoleType
	})
}

type QuizQuery struct {
	ID             *string
	OrgID          *string
	IDIn           []string
	UID            *string
	CreatorID      *string
	IncludeDeleted *bool
}

func (query *QuizQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UID != nil {
		qb = qb.Where("uid = ?", *query.UID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.CreatorID != nil {
		qb = qb.Where("creator_id = ?", *query.CreatorID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a Quiz to database, transaction is optional
func (r *QuizRepository) Create(quiz *Quiz, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Table(GetTblName(QuizTbl)).Debug().Create(&quiz).Error; err != nil {
		return
	}

	if quiz.Files != nil && len(quiz.Files) > 0 {
		if err = Repository.FileRelation.AddFiles(QuizModelName, quiz.ID, FilesField, quiz.Files); err != nil {
			return
		}
	}
	return
}

func (r *QuizRepository) CreateMany(quizzes []*Quiz, trans *gorm.DB) (err error) {
	if len(quizzes) == 0 {
		return nil
	}

	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Table(GetTblName(QuizTbl)).Debug().Create(&quizzes).Error; err != nil {
		return
	}

	for _, quiz := range quizzes {
		if quiz.Files != nil && len(quiz.Files) > 0 {
			if err = Repository.FileRelation.AddFiles(QuizModelName, quiz.ID, util.FilesField, quiz.Files); err != nil {
				return err
			}
		}
	}
	return
}

// Update updates a Quiz by ID in database, transaction is optional
func (r *QuizRepository) Update(quiz *Quiz, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Table(GetTblName(QuizTbl)).Debug().Select("*").Omit("id", "create_at", "Questions").Updates(&quiz).Error; err != nil {
		return
	}

	if quiz.Files != nil && len(quiz.Files) > 0 {
		if err = Repository.FileRelation.AddFiles(QuizModelName, quiz.ID, FilesField, quiz.Files); err != nil {
			return err
		}
	}
	Cache.Quiz.DeleteByQuizID(quiz.ID)
	return
}

func (r *QuizRepository) preloadQuestions(quizzes []*Quiz) error {
	quizIDs := lo.Map(quizzes, func(quiz *Quiz, _ int) string {
		return quiz.ID
	})
	questions, err := Repository.QuizQuestion.FindMany(
		&QuizQuestionQuery{
			QuizIDIn:       quizIDs,
			IncludeDeleted: util.NewBool(false),
		},
		&FindManyOptions{
			Sort:     []string{`"order" ASC`},
			Preloads: []string{util.FilesField, util.ItemsFilesField},
		},
	)
	if err != nil {
		return err
	}

	questionsByQuizIDs := make(map[string][]*QuizQuestion)
	for _, question := range questions {
		if _, found := questionsByQuizIDs[question.QuizID]; !found {
			questionsByQuizIDs[question.QuizID] = []*QuizQuestion{question}
			continue
		}
		questionsByQuizIDs[question.QuizID] = append(questionsByQuizIDs[question.QuizID], question)
	}
	for _, quiz := range quizzes {
		quiz.Questions = questionsByQuizIDs[quiz.ID] // No need sort because already sort by `order ASC` when FindMany questions
	}
	return nil
}

// FindByID finds a Quiz by ID with full preloads, transaction is optional
func (r *QuizRepository) FindByID(id string) (*Quiz, error) {
	var quizCache Quiz
	Cache.Quiz.GetByQuizID(id, &quizCache)
	if quizCache.ID != "" {
		return &quizCache, nil
	}

	options := &FindOneOptions{}
	quiz, err := findByID[Quiz](QuizTbl, id, options)
	if err != nil {
		return nil, err
	}

	// Preload field Files
	quizIDs := []string{quiz.ID}
	if filesByQuizIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizModelName, quizIDs, util.FilesField); mErr != nil {
		return nil, mErr
	} else {
		quiz.Files = filesByQuizIDs[quiz.ID]
	}

	// Preload field Questions
	if err = r.preloadQuestions([]*Quiz{quiz}); err != nil {
		return nil, err
	}

	Cache.Quiz.SetQuizByID(quiz.ID, quiz)
	return quiz, nil
}

// FindByIDWithOptions finds a Quiz by ID with given find options, transaction is optional
func (r *QuizRepository) FindByIDWithOptions(id string, options *FindOneOptions) (*Quiz, error) {
	hasPreloadFiles := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadQuestions := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.QuestionsField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.QuestionsField)
	}

	quiz, err := findByID[Quiz](QuizTbl, id, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadFiles {
		if filesByQuizIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizModelName, []string{quiz.ID}, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			quiz.Files = filesByQuizIDs[quiz.ID]
		}
	}

	if hasPreloadQuestions {
		if err = r.preloadQuestions([]*Quiz{quiz}); err != nil {
			return nil, err
		}
	}
	return quiz, nil
}

// FindOne finds one Quiz with given find queries and options, transaction is optional
func (r *QuizRepository) FindOne(query *QuizQuery, options *FindOneOptions) (*Quiz, error) {
	hasPreloadFiles := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadQuestions := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.QuestionsField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.QuestionsField)
	}

	quiz, err := findOne[Quiz](QuizTbl, query, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadFiles {
		quizIDs := []string{quiz.ID}
		if filesByQuizIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizModelName, quizIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			quiz.Files = filesByQuizIDs[quiz.ID]
		}
	}

	if hasPreloadQuestions {
		if err = r.preloadQuestions([]*Quiz{quiz}); err != nil {
			return nil, err
		}
	}
	return quiz, nil
}

// FindMany finds Quizzes by query conditions with give find options
func (r *QuizRepository) FindMany(query *QuizQuery, options *FindManyOptions) ([]*Quiz, error) {
	hasPreloadFiles := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadQuestions := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, util.QuestionsField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.QuestionsField)
	}

	quizzes, err := findMany[Quiz](QuizTbl, query, options)
	if err != nil {
		return nil, err
	}
	if hasPreloadFiles {
		quizIDs := lo.Map(quizzes, func(quiz *Quiz, _ int) string {
			return quiz.ID
		})
		if filesByQuizIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizModelName, quizIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			lo.ForEach(quizzes, func(quiz *Quiz, _ int) {
				quiz.Files = filesByQuizIDs[quiz.ID]
			})
		}
	}

	if hasPreloadQuestions {
		if err = r.preloadQuestions(quizzes); err != nil {
			return nil, err
		}
	}
	return quizzes, err
}

// FindPage returns Quizzes and pagination by query conditions and find options, transaction is optional
func (r *QuizRepository) FindPage(query *QuizQuery, options *FindPageOptions) ([]*Quiz, *Pagination, error) {
	hasPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadQuestions := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.QuestionsField) {
		hasPreloadQuestions = true
		options.Preloads = util.RemoveElement(options.Preloads, util.QuestionsField)
	}

	quizzes, pagination, err := findPage[Quiz](QuizTbl, query, options)
	if err != nil {
		return nil, nil, err
	}
	if hasPreloadFiles {
		quizIDs := lo.Map(quizzes, func(quiz *Quiz, _ int) string {
			return quiz.ID
		})
		if filesByQuizIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizModelName, quizIDs, util.FilesField); mErr != nil {
			return nil, nil, mErr
		} else {
			lo.ForEach(quizzes, func(quiz *Quiz, _ int) {
				quiz.Files = filesByQuizIDs[quiz.ID]
			})
		}
	}

	if hasPreloadQuestions {
		if err = r.preloadQuestions(quizzes); err != nil {
			return nil, nil, err
		}
	}
	return quizzes, pagination, err
}

// Delete perform soft deletion to a Quizzes by ID, transaction is optional
func (r *QuizRepository) Delete(id string, trans *gorm.DB) error {
	if err := deleteByID[Quiz](QuizTbl, id, trans); err != nil {
		return err
	}
	Cache.Quiz.DeleteByQuizID(id)
	return nil
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *QuizRepository) DeleteMany(query *QuizQuery, trans *gorm.DB) (int64, error) {
	deletedCount, err := deleteMany[Quiz](QuizTbl, query, trans)
	if err != nil {
		return 0, err
	}
	Cache.Quiz.Flush()
	return deletedCount, nil
}

// Count returns number of Quizzes by query conditions, transaction is optional
func (r *QuizRepository) Count(query *QuizQuery) (int64, error) {
	return count[Quiz](QuizTbl, query)
}
