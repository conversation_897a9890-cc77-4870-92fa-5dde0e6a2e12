package models

import (
	"fmt"

	"gorm.io/gorm"
)

type UserSettingType string

const (
	UserSettingTypeBankAccount  UserSettingType = "bank_account"
	UserSettingTypeBlogs        UserSettingType = "blogs"
	UserSettingTypeCertificates UserSettingType = "certificates"
	UserSettingTypeCourses      UserSettingType = "courses"
	UserSettingTypeOldUsername  UserSettingType = "old_usernames"
	SettingCertificateKey                       = "certificate_ids"
	SettingCourseKey                            = "course_ids"
	SettingBlogKey                              = "blog_ids"
	SettingOldUsernameKey                       = "usernames"
)

type UserSetting struct {
	UserID string          `json:"user_id" gorm:"type:varchar(20);not null"`
	OrgID  string          `json:"org_id" gorm:"type:varchar(20);not null"`
	Type   UserSettingType `json:"type" gorm:"default:agency"`
	Enable bool            `json:"enable" gorm:"default:true"`
	Value  JSONB           `json:"value" gorm:"type:jsonb"`
	Model
}

type UserSettingQuery struct {
	ID               *string   `json:"id" form:"id"`
	IDIn             []*string `json:"id_in" form:"id_in"`
	UserID           *string   `json:"user_id" form:"user_id"`
	OrgID            *string   `json:"org_id" form:"org_id"`
	Type             *string   `json:"type" form:"type"`
	Enable           *bool     `json:"enable" form:"enable"`
	IncludeDeleted   *bool     `form:"include_deleted"`
	SearchTerm       *string   `json:"search_term" form:"search_term"`
	SearchCategories *string   `json:"search_categories" form:"search_categories"`
}

func (query *UserSettingQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *UserSettingRepository) Create(e *UserSetting, trans *gorm.DB) error {
	return create(UserSettingTbl, e, trans)
}

func (r *UserSettingRepository) CreateMany(ts []*UserSetting, trans *gorm.DB) error {
	return createMany(UserSettingTbl, ts, trans)
}

func (r *UserSettingRepository) Update(f *UserSetting, trans *gorm.DB) error {
	return update(UserSettingTbl, f, trans)
}

func (r *UserSettingRepository) FindOne(query *UserSettingQuery, options *FindOneOptions) (*UserSetting, error) {
	return findOne[UserSetting](UserSettingTbl, query, options)
}

func (r *UserSettingRepository) FindPage(query *UserSettingQuery, options *FindPageOptions) ([]*UserSetting, *Pagination, error) {
	return findPage[UserSetting](UserSettingTbl, query, options)
}

func (r *UserSettingRepository) FindMany(query *UserSettingQuery, options *FindManyOptions) ([]*UserSetting, error) {
	return findMany[UserSetting](UserSettingTbl, query, options)
}

func (r *UserSettingRepository) Count(query *UserSettingQuery) (int64, error) {
	return count[UserSetting](UserSettingTbl, query)
}

func (r *UserSettingRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[UserSetting](UserSettingTbl, id, trans)
}

func (r *UserSettingRepository) CanUseUsername(username string, trans *gorm.DB) (bool, error) {
	userSettingTbl := GetTblName(UserSettingTbl)
	userTbl := GetTblName(UserTbl)
	queryStr := fmt.Sprintf(`
	WITH us AS (
		SELECT COALESCE(COUNT(1), 0) AS count
        FROM %[1]s
        WHERE value->'usernames' @> '["%[2]s"]'::jsonb
	), u AS (
		SELECT COALESCE(COUNT(1), 0) AS count
        FROM %[3]s
        WHERE username = '%[2]s'
	)
	SELECT 
    CASE 
        WHEN (us.count > 0 OR u.count > 0) THEN false
        ELSE true
    END AS result
    FROM us, u;`, userSettingTbl, username, userTbl)

	var result bool
	err := DB.Debug().Raw(queryStr).Scan(&result).Error
	return result, err
}
