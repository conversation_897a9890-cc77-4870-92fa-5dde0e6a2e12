package models

import (
	"fmt"
	"gorm.io/gorm"
	"openedu-core/pkg/log"
)

type QuizAnswer struct {
	Model
	SubmissionID     string               `json:"submission_id" gorm:"type:varchar(20);not null"`
	QuizID           string               `json:"quiz_id" gorm:"type:varchar(20);not null"`
	UserID           string               `json:"user_id" gorm:"type:varchar(20)"`
	QuestionID       string               `json:"question_id" gorm:"type:varchar(20);not null"`
	Order            int                  `json:"order"`
	AnsweredItemSets QuizQuestionItemSets `json:"answered_item_sets" gorm:"type:jsonb"`
	StartAt          int64                `json:"start_at"`
	EndAt            int64                `json:"end_at"`
	Correct          bool                 `json:"correct"`
	ArchivedPoints   int                  `json:"archived_points"`
	Question         *QuizQuestion        `json:"questions"`
}

type SimpleQuizAnswer struct {
	Model
	SubmissionID     string               `json:"submission_id" gorm:"type:varchar(20),not null"`
	QuizID           string               `json:"quiz_id" gorm:"type:varchar(20),not null"`
	UserID           string               `json:"user_id" gorm:"type:varchar(20)"`
	QuestionID       string               `json:"question_id" gorm:"type:varchar(20),not null"`
	Correct          bool                 `json:"correct"`
	AnsweredItemSets QuizQuestionItemSets `json:"answered_item_sets"`
	StartAt          int64                `json:"start_at"`
	EndAt            int64                `json:"end_at"`
	ArchivedPoints   int                  `json:"archived_points"`
}

func (a *QuizAnswer) Sanitize() *SimpleQuizAnswer {
	return &SimpleQuizAnswer{
		Model:            a.Model,
		SubmissionID:     a.SubmissionID,
		QuizID:           a.QuizID,
		UserID:           a.UserID,
		QuestionID:       a.QuestionID,
		Correct:          a.Correct,
		AnsweredItemSets: a.AnsweredItemSets,
		StartAt:          a.StartAt,
		EndAt:            a.EndAt,
		ArchivedPoints:   a.ArchivedPoints,
	}
}

type QuizAnswerQuery struct {
	QuizID         *string  `form:"quiz_id" json:"quiz_id"`
	SubmissionID   *string  `form:"submission_id" json:"submission_id"`
	SubmissionIDIn []string `form:"submission_id_in" json:"submission_id_in"`
	QuestionID     *string  `form:"question_id" json:"question_id"`
	UserID         *string  `form:"user_id" json:"user_id"`
	IncludeDeleted *bool
}

func (query *QuizAnswerQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.QuizID != nil {
		qb = qb.Where("quiz_id = ?", *query.QuizID)
	}

	if query.SubmissionID != nil {
		qb = qb.Where("submission_id = ?", *query.SubmissionID)
	}

	if len(query.SubmissionIDIn) > 0 {
		qb = qb.Where("submission_id IN (?)", query.SubmissionIDIn)
	}

	if query.QuestionID != nil {
		qb = qb.Where("question_id = ?", *query.QuestionID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a Quiz Answer to database, transaction is optional
func (r *QuizAnswerRepository) Create(answer *QuizAnswer, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Omit("Question").Table(GetTblName(QuizAnswerTbl)).Debug().Create(&answer).Error

	if cErr := Cache.QuizAnswer.DeleteBySubmissionID(answer.SubmissionID); cErr != nil {
		log.Errorf("QuizAnswerRepository.Create::Clear cache quiz answers by submission ID error: %v", cErr)
	}
	return
}

func (r *QuizAnswerRepository) CreateMany(qs []*QuizAnswer, trans *gorm.DB) error {
	if err := createMany(QuizAnswerTbl, qs, trans); err != nil {
		return err
	}

	for _, answer := range qs {
		if cErr := Cache.QuizAnswer.DeleteBySubmissionID(answer.SubmissionID); cErr != nil {
			log.Errorf("QuizAnswerRepository.CreateMany::Clear cache quiz answers by submission ID error: %v", cErr)
		}
	}
	return nil
}

// Update updates a Quiz Answers by ID in database, transaction is optional
func (r *QuizAnswerRepository) Update(answer *QuizAnswer, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Table(GetTblName(QuizAnswerTbl)).Debug().Select("*").Omit("id", "create_at", "Question").Updates(&answer).Error; err != nil {
		return
	}

	if cErr := Cache.QuizAnswer.DeleteBySubmissionID(answer.SubmissionID); cErr != nil {
		log.Errorf("QuizAnswerRepository.Update::Clear cache quiz answers by submission ID error: %v", cErr)
	}
	return
}

// FindByID finds a Quiz Answer by ID with given find options, transaction is optional
func (r *QuizAnswerRepository) FindByID(id string, options *FindOneOptions) (*QuizAnswer, error) {
	return findByID[QuizAnswer](QuizAnswerTbl, id, options)
}

// FindOne finds one Quiz Answer with given find queries and options, transaction is optional
func (r *QuizAnswerRepository) FindOne(query *QuizAnswerQuery, options *FindOneOptions) (*QuizAnswer, error) {
	return findOne[QuizAnswer](QuizAnswerTbl, query, options)
}

// FindMany finds Quiz Answers by query conditions with give find options
func (r *QuizAnswerRepository) FindMany(query *QuizAnswerQuery, options *FindManyOptions) ([]*QuizAnswer, error) {
	return findMany[QuizAnswer](QuizAnswerTbl, query, options)
}

// FindPage returns Quiz Answers and pagination by query conditions and find options, transaction is optional
func (r *QuizAnswerRepository) FindPage(query *QuizAnswerQuery, options *FindPageOptions) ([]*QuizAnswer, *Pagination, error) {
	return findPage[QuizAnswer](QuizAnswerTbl, query, options)
}

// Delete perform soft deletion to a Quiz Answers by ID, transaction is optional
func (r *QuizAnswerRepository) Delete(id string, trans *gorm.DB) error {
	if err := deleteByID[QuizAnswer](QuizAnswerTbl, id, trans); err != nil {
		return err
	}

	if cErr := Cache.QuizAnswer.Flush(); cErr != nil {
		log.Errorf("QuizAnswerRepository.Delete::Clear cache all quiz answers error: %v", cErr)
	}
	return nil
}

// DeleteMany performs soft deletion to Quiz Answers by query conditions
func (r *QuizAnswerRepository) DeleteMany(query *QuizAnswerQuery, trans *gorm.DB) (int64, error) {
	deletedCount, err := deleteMany[QuizAnswer](QuizAnswerTbl, query, trans)
	if err != nil {
		return 0, err
	}

	if cErr := Cache.QuizAnswer.Flush(); cErr != nil {
		log.Errorf("QuizAnswerRepository.DeleteMany::Clear cache all quiz answers error: %v", cErr)
	}
	return deletedCount, nil
}

// Count returns number of Quiz Answers by query conditions, transaction is optional
func (r *QuizAnswerRepository) Count(query *QuizAnswerQuery) (int64, error) {
	return count[QuizAnswer](QuizAnswerTbl, query)
}
