package models

import (
	"context"
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strings"

	"gorm.io/gorm/clause"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type CourseEnrollment struct {
	Model
	CourseCuid               string                          `json:"course_cuid" gorm:"type:varchar(20)"`
	Course                   *Course                         `json:"course" gorm:"-"`
	UserID                   string                          `json:"user_id" gorm:"type:varchar(20)"`
	OrgID                    string                          `json:"org_id" gorm:"type:varchar(20)"`
	User                     *User                           `json:"user" gorm:"-"`
	Blocked                  bool                            `json:"blocked" gorm:"default:false"`
	BlockedReason            string                          `json:"blocked_reason"`
	LearningProgressOverview *SimpleLearningProgressOverview `json:"learning_progress_overview,omitempty" gorm:"-"`
}

type SimpleCourseEnrollment struct {
	Model
	CourseCuid               string                          `json:"course_cuid"`
	UserID                   string                          `json:"user_id"`
	OrgID                    string                          `json:"org_id"`
	Blocked                  bool                            `json:"blocked"`
	BlockedReason            string                          `json:"blocked_reason"`
	Course                   *Course                         `json:"course"`
	User                     *User                           `json:"user"`
	LearningProgressOverview *SimpleLearningProgressOverview `json:"learning_progress_overview,omitempty" `
}

func (c *CourseEnrollment) Sanitize() *SimpleCourseEnrollment {
	return &SimpleCourseEnrollment{
		Model:                    c.Model,
		Course:                   c.Course,
		CourseCuid:               c.CourseCuid,
		UserID:                   c.UserID,
		OrgID:                    c.OrgID,
		Blocked:                  c.Blocked,
		BlockedReason:            c.BlockedReason,
		User:                     c.User,
		LearningProgressOverview: c.LearningProgressOverview,
	}
}

type CourseEnrollmentQuery struct {
	ID                      *string  `json:"id,omitempty" form:"id"`
	IDIn                    []string `json:"id_in,omitempty" form:"id_in"`
	CourseCuid              *string  `json:"course_id,omitempty"`
	CourseCuidIn            []string `json:"course_id_in,omitempty" `
	UserID                  *string  `json:"user_id,omitempty"`
	OrgID                   *string  `json:"org_id,omitempty"`
	EnrollmentDateStart     *int     `json:"enrollment_date_start,omitempty" form:"enrollment_date_start"`
	EnrollmentDateEnd       *int     `json:"enrollment_date_end,omitempty" form:"enrollment_date_end"`
	Blocked                 *bool    `json:"blocked,omitempty" form:"blocked"`
	IncludeDeleted          *bool    `json:"include_deleted,omitempty" form:"include_deleted"`
	SearchTerm              *string  `json:"search_term" form:"search_term"`
	SearchCategories        *string  `json:"search_categories" form:"search_categories"`
	ExcludeDeletedCourses   *bool    `form:"exclude_deleted_courses" json:"exclude_deleted_courses"`
	ExcludeUnPublishCourses *bool    `form:"exclude_unpublish_courses" json:"exclude_unpublish_courses"`
	ExcludeStartedLearning  *bool    `form:"exclude_started_learning" json:"exclude_started_learning"`
}

func (query *CourseEnrollmentQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	ceTblName := GetTblName(CourseEnrollmentTbl)
	if query.ID != nil {
		qb = qb.Where(ceTblName+".id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where(ceTblName+".id IN (?)", query.IDIn)

	}

	if len(query.CourseCuidIn) > 0 {
		cuids := lo.Map(query.CourseCuidIn, func(cuid string, _ int) string {
			return "'" + cuid + "'"
		})
		qb = qb.Where(ceTblName + ".course_cuid = ANY(ARRAY[" + strings.Join(cuids, ",") + "])")
	}

	if query.CourseCuid != nil {
		qb = qb.Where(ceTblName+".course_cuid = ?", *query.CourseCuid)
	}

	if query.UserID != nil {
		qb = qb.Where(ceTblName+".user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where(ceTblName+".org_id = ?", *query.OrgID)
	}

	if query.EnrollmentDateStart != nil {
		qb = qb.Where(ceTblName+".enrollment_date > ?", *query.EnrollmentDateStart)
	}

	if query.EnrollmentDateEnd != nil {
		qb = qb.Where(ceTblName+".enrollment_date < ?", *query.EnrollmentDateEnd)
	}

	if query.Blocked != nil {
		if !*query.Blocked {
			qb = qb.Where("NOT " + ceTblName + ".blocked")
		} else {
			qb = qb.Where(ceTblName+".blocked = ?", *query.Blocked)
		}
	}

	shouldJoinPublishCourseTbl := false
	pcSubQuery := db.Table(GetTblName(PublishCourseTbl)).
		Select("course_cuid").
		Group("course_cuid")

	if query.ExcludeUnPublishCourses != nil && *query.ExcludeUnPublishCourses {
		shouldJoinPublishCourseTbl = true
		pcSubQuery.Where("pub_date <> 0")
	}

	if query.ExcludeDeletedCourses != nil && *query.ExcludeDeletedCourses {
		shouldJoinPublishCourseTbl = true
		pcSubQuery.Where("delete_at = 0")
	}

	if shouldJoinPublishCourseTbl {
		qb = qb.Joins(fmt.Sprintf("INNER JOIN (?) AS pc ON %[1]s.course_cuid = pc.course_cuid", ceTblName), pcSubQuery)
	}

	if query.ExcludeStartedLearning != nil {
		lpSubQuery := db.Table(GetTblName(LearningStatusTbl)).
			Select("user_id, course_cuid").
			Where("delete_at = 0")
		if query.UserID != nil {
			lpSubQuery = lpSubQuery.Where("user_id = ?", *query.UserID)
		}
		qb = qb.Joins(fmt.Sprintf("LEFT JOIN (?) AS lp ON %[1]s.course_cuid = lp.course_cuid AND %[1]s.user_id = lp.user_id", ceTblName), lpSubQuery).
			Where("lp.course_cuid IS NULL")
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where(ceTblName + ".delete_at = 0")
	}

	qb = qb.Joins(fmt.Sprintf("INNER JOIN %[1]s ON %[1]s.id = %[2]s.user_id", GetTblName(UserTbl), ceTblName))

	searchStr := ""
	var searchParams []any
	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		validCates, err := VerifySearchCategory(CourseEnrollment{}, []string{}, categories)
		if err != nil {
			return qb
		}

		for _, category := range categories {
			switch category {
			case "email":
				qb = qb.Where(fmt.Sprintf("%[1]s.email ~* '%[2]s'", GetTblName(UserTbl), *query.SearchTerm))
			default:
				break
			}
		}

		for idx, category := range validCates {
			if idx == 0 {
				searchStr += fmt.Sprintf("%s ~* ?", ceTblName+"."+category)
			} else {
				searchStr += fmt.Sprintf(" OR %s ~* ?", ceTblName+"."+category)
			}
			searchParams = append(searchParams, *query.SearchTerm)
		}
		qb = qb.Where(searchStr, searchParams...)

	}

	return qb
}

func (r *CourseEnrollmentRepository) Create(enrollment *CourseEnrollment, trans *gorm.DB) error {
	Cache.Course.Flush()
	return create(CourseEnrollmentTbl, enrollment, trans)
}

func (r *CourseEnrollmentRepository) CreateMany(enrollments []*CourseEnrollment, trans *gorm.DB) error {
	Cache.Course.Flush()
	return createMany(CourseEnrollmentTbl, enrollments, trans)
}

func (r *CourseEnrollmentRepository) Upsert(enrollment *CourseEnrollment, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "user_id"},
			{Name: "course_cuid"},
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"org_id",
			"blocked",
			"blocked_reason",
			"update_at",
			"delete_at",
		}),
	}).Create(&enrollment).Error
	Cache.Course.Flush()
	return
}

func (r *CourseEnrollmentRepository) Update(courseEnrollment *CourseEnrollment, trans *gorm.DB) error {
	Cache.Course.Flush()
	return update(CourseEnrollmentTbl, courseEnrollment, trans)
}

func (r *CourseEnrollmentRepository) FindOne(query *CourseEnrollmentQuery, options *FindOneOptions) (*CourseEnrollment, error) {
	if options == nil {
		options = &FindOneOptions{}
	}

	shouldPreloadCourse := false
	shouldPreloadUser := false

	if lo.Contains(options.Preloads, CourseField) {
		shouldPreloadCourse = true
		options.Preloads = util.RemoveElement(options.Preloads, CourseField)
	}

	if lo.Contains(options.Preloads, UserField) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, UserField)
	}

	enrollment, err := findOne[CourseEnrollment](CourseEnrollmentTbl, query, options)
	if err != nil {
		return nil, err
	}

	if shouldPreloadCourse {
		if pubCourse, cErr := Repository.PublishCourse(r.ctx).FindOne(&PublishCourseQuery{CourseCuid: &enrollment.CourseCuid}, &FindOneOptions{}); cErr != nil {
			return enrollment, cErr
		} else {
			course, cErr := Repository.Course(r.ctx).FindByID(pubCourse.CourseID, nil)
			if cErr != nil {
				return enrollment, cErr
			}
			enrollment.Course = course
		}
	}

	if shouldPreloadUser {
		if user, uErr := Repository.User.FindByID(enrollment.UserID); uErr != nil {
			return enrollment, uErr
		} else {
			enrollment.User = user.Sanitize()
		}
	}

	return enrollment, nil
}

func (r *CourseEnrollmentRepository) FindMany(query *CourseEnrollmentQuery, options *FindManyOptions) ([]*CourseEnrollment, error) {
	return findMany[CourseEnrollment](CourseEnrollmentTbl, query, options)
}

func (r *CourseEnrollmentRepository) FirstOrCreate(courseEnrollment *CourseEnrollment) (*CourseEnrollment, error) {
	err := DB.Table(GetTblName(CourseEnrollmentTbl)).Where("course_cuid = ? and user_id = ?", courseEnrollment.CourseCuid, courseEnrollment.UserID).FirstOrCreate(&courseEnrollment).Error

	Cache.Course.Flush()
	return courseEnrollment, err
}

func (r *CourseEnrollmentRepository) FindPage(query *CourseEnrollmentQuery, options *FindPageOptions) ([]*CourseEnrollment, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}

	shouldPreloadCourse := false
	shouldPreloadUser := false
	shouldPreloadLearningProgress := false

	if lo.Contains(options.Preloads, CourseField) {
		shouldPreloadCourse = true
		options.Preloads = util.RemoveElement(options.Preloads, CourseField)
	}

	if lo.Contains(options.Preloads, UserField) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, UserField)
	}

	if lo.Contains(options.Preloads, LearningProgressOverviewField) {
		shouldPreloadLearningProgress = true
		options.Preloads = util.RemoveElement(options.Preloads, LearningProgressOverviewField)
	}

	enrollments, pagination, err := findPage[CourseEnrollment](CourseEnrollmentTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	mapCourseIDCourse := map[string]*Course{}
	mapUserIDUser := map[string]*User{}

	if shouldPreloadCourse {
		courseCuids := lo.Map(enrollments, func(enrollment *CourseEnrollment, _ int) string {
			return enrollment.CourseCuid
		})
		if len(courseCuids) != 0 {
			pubCourses, cErr := Repository.PublishCourse(r.ctx).FindMany(&PublishCourseQuery{CourseCuidIn: courseCuids}, nil)
			if cErr != nil {
				return enrollments, pagination, cErr
			}

			courseIds := lo.Map(pubCourses, func(item *PublishCourse, _ int) string {
				return item.CourseID
			})

			ces, cErr := Repository.Course(r.ctx).FindMany(&CourseQuery{IDIn: courseIds}, nil)
			if cErr != nil {
				return enrollments, pagination, cErr
			}

			for _, course := range ces {
				mapCourseIDCourse[course.Cuid] = course
			}

			for _, enrollment := range enrollments {
				enrollment.Course = mapCourseIDCourse[enrollment.CourseCuid]
			}
		}
	}

	if shouldPreloadUser {
		userIDs := lo.Map(enrollments, func(enrollment *CourseEnrollment, _ int) string {
			return enrollment.UserID
		})

		if users, uErr := Repository.User.FindMany(&UserQuery{IDIn: &userIDs}, nil); uErr != nil {
			return enrollments, pagination, uErr
		} else {
			for _, user := range users {
				mapUserIDUser[user.ID] = user
			}

			for _, enrollment := range enrollments {
				if mapUserIDUser[enrollment.UserID] != nil {
					enrollment.User = mapUserIDUser[enrollment.UserID].Sanitize()
				}
			}
		}
	}

	if shouldPreloadUser && shouldPreloadCourse && shouldPreloadLearningProgress {
		if err := preloadLearningProgress(enrollments); err != nil {
			return enrollments, pagination, err
		}
	}

	return enrollments, pagination, nil

}

func (r *CourseEnrollmentRepository) CountByCourseCUIDs(query *CourseEnrollmentQuery) (map[string]int64, error) {
	cacheKey := util.StructToCacheKey("count_by_course_cuids", query)
	type CountData struct {
		CourseCuid string
		Count      int64
	}

	var resCache []CountData
	if cErr := Cache.CourseEnrollment.Get1M(cacheKey, &resCache); cErr == nil && len(resCache) > 0 {
		result := map[string]int64{}
		for _, data := range resCache {
			result[data.CourseCuid] = data.Count
		}
		return result, nil
	}
	var res []CountData
	qb := ApplyQueryOptions(DB, query)
	if err := qb.Table(GetTblName(CourseEnrollmentTbl)).Select("course_cuid, count(user_id)").Group("course_cuid").Debug().Find(&res).Error; err != nil {
		return nil, err
	}

	if cErr := Cache.CourseEnrollment.Set1M(cacheKey, res); cErr != nil {
		log.Errorf("cache set course enrollment count error: %v", cErr)
	}

	enrollCountsByCourseCUIDs := map[string]int64{}

	for _, data := range res {
		enrollCountsByCourseCUIDs[data.CourseCuid] = data.Count
	}

	return enrollCountsByCourseCUIDs, nil

}

func (r *CourseEnrollmentRepository) Count(query *CourseEnrollmentQuery) (int64, error) {
	return count[CourseEnrollment](CourseEnrollmentTbl, query)
}

func preloadLearningProgress(ces []*CourseEnrollment) error {
	if len(ces) == 0 {
		return nil
	}

	listUser := lo.Map(ces, func(ce *CourseEnrollment, _ int) *User {
		return ce.User
	})

	userIDs := lo.Map(listUser, func(u *User, _ int) string {
		return u.ID
	})

	course := ces[0].Course

	completedLessonCounts, err := Repository.LearningStatus(context.TODO()).CountCompletedLessonsByUsers([]string{course.ID}, userIDs)
	if err != nil {
		return err
	}
	mapCompletedLessonCountsByUserID := make(map[string]*CompletedLessonCountByUser)
	for _, completedLessonCount := range completedLessonCounts {
		mapCompletedLessonCountsByUserID[completedLessonCount.UserID] = completedLessonCount
	}

	currentSections, err := Repository.LearningStatus(context.TODO()).FindCurrentSectionsAndLessonsByUsers(course.ID, course.Cuid, userIDs)
	if err != nil {
		return err
	}

	currentSectionsByUserID := make(map[string]*Section)
	currentLessonsByUserID := make(map[string]*Section)

	for userID, listSection := range currentSections {
		for _, section := range listSection {
			if section.IsLesson() {
				currentLessonsByUserID[userID] = section
			} else {
				currentSectionsByUserID[userID] = section

			}
		}
	}

	for _, ce := range ces {
		ce.LearningProgressOverview = &SimpleLearningProgressOverview{}
		if completedLessonCount, found := mapCompletedLessonCountsByUserID[ce.UserID]; found {
			ce.LearningProgressOverview.TotalLesson = completedLessonCount.TotalLessons
			ce.LearningProgressOverview.CompletedLesson = completedLessonCount.CompletedLessons
		}

		if section, found := currentSectionsByUserID[ce.UserID]; found {
			ce.LearningProgressOverview.CurrentSection = section
		}

		if lesson, found := currentLessonsByUserID[ce.UserID]; found {
			ce.LearningProgressOverview.CurrentLesson = lesson
		}

	}

	return nil
}

type LearnerCountHolder interface {
	GetCourseCUID() string
	SetLearnerCount(learnerCount int64)
}

func PreloadLeanerCount[T LearnerCountHolder](ctx context.Context, items []T) error {
	courseCUIDs := lo.Map(items, func(course T, index int) string {
		return course.GetCourseCUID()
	})
	query := &CourseEnrollmentQuery{
		CourseCuidIn: lo.Uniq(courseCUIDs),
		Blocked:      util.NewBool(false),
	}

	learnerCountsByCourseCUIDs, err := Repository.CourseEnrollment(ctx).CountByCourseCUIDs(query)
	if err != nil {
		return err
	}

	for _, course := range items {
		if learnerCount, found := learnerCountsByCourseCUIDs[course.GetCourseCUID()]; found {
			course.SetLearnerCount(learnerCount)
		} else {
			course.SetLearnerCount(0)
		}
	}
	return nil
}
