package models

import (
	"context"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type BaseRepository struct {
	ctx context.Context
}

type AppRepository struct {
	User               UserRepositoryIface
	Role               RoleRepositoryIface
	UserRoleOrg        UserRoleOrgRepositoryIface
	Permission         PermissionRepositoryIface
	SnsAccount         SnsAccountRepositoryIface
	Session            SessionRepositoryIface
	System             SystemConfigRepositoryIface
	Organization       OrganizationRepositoryIface
	File               FileRepositoryIface
	FileRelation       FileRelationRepositoryIface
	Payment            PaymentRepositoryIface
	PaymentMethod      PaymentMethodRepositoryIface
	CoursePartner      CoursePartnerRepositoryIface
	CoursePrice        CoursePriceRepositoryIface
	UserToken          UserTokenRepositoryIface
	EmailTemplate      EmailTemplateRepositoryIface
	Order              OrderRepositoryIface
	OrderItem          OrderItemRepositoryIface
	Section            SectionRepositoryIface
	Category           CategoryRepositoryIface
	CategoryRelation   CategoryRelationRepositoryIface
	Hashtag            HashtagRepositoryIface
	HashtagRelation    HashtagRelationRepositoryIface
	Coupon             CouponRepositoryIface
	CouponHistory      CouponHistoryRepositoryIface
	Team               TeamRepositoryIface
	Form               FormRepositoryIface
	FormQuestion       FormQuestionRepositoryIface
	FormQuestionOption FormQuestionOptionRepositoryIface
	FormAnswer         FormAnswerRepositoryIface
	FormSession        FormSessionRepositoryIface
	Blog               BlogRepositoryIface
	Approval           ApprovalRepositoryIface
	PublishBlog        PublishBlogRepositoryIface
	UserAction         UserActionRepositoryIface
	PageConfig         PageConfigRepositoryIface
	PageAccess         PageAccessRepositoryIface
	Wallet             WalletRepositoryIface
	Transaction        TransactionRepositoryIface
	Quiz               QuizRepositoryIface
	QuizRelation       QuizRelationRepositoryIface
	QuizQuestion       QuizQuestionRepositoryIface
	QuizSubmission     QuizSubmissionRepositoryIface
	QuizAnswer         QuizAnswerRepositoryIface
	AffiliateCampaign  AffiliateCampaignIface
	Commission         CommissionIface
	CourseCampaign     CourseCampaignIface
	Referral           ReferralIface
	ReferralLink       ReferralLinkIface
	Referrer           ReferrerIface
	UserSetting        UserSettingRepositoryIface
	Bookmark           BookmarkRepositoryIface
	FormRelation       FormRelationRepositoryIface
	FormAnswerStats    FormAnswerStatsRepositoryIface
	Certificate        CertificateRepositoryIface
	HtmlTemplate       HtmlTemplateRepositoryIface
	AIBlogRewrite      AIBlogRewriteRepositoryIface
	UserSummary        UserSummaryRepositoryIface
	Report             ReportRepositoryIface
	AICourse           AICourseRepositoryIface
	AIHistory          AIHistoryRepositoryIface
	PricingPlan        PricingPlanRepositoryIface
	Subscription       SubscriptionRepositoryIface
	ResourceUsage      ResourceUsageRepositoryIface
	AIModel            AIModelRepositoryIface
	FeaturedContent    FeaturedContentRepositoryIface
	ClpVotingMilestone ClpVotingMilestoneRepositoryIface
	ClpVotingPhase     ClpVotingPhaseRepositoryIface
	AIPrompt           AIPromptRepositoryIface
}
type UserRepository struct{}

type RoleRepository struct{}

type UserRoleOrgRepository struct{}

type PermissionRepository struct{}

type SnsAccountRepository struct{}

type SessionRepository struct{}

type SystemConfigRepository struct{}

type OrganizationRepository struct{}

type FileRepository struct{}

type FileRelationRepository struct{}

type CourseRepository struct {
	BaseRepository
}

type CoursePartnerRepository struct{}

type CoursePriceRepository struct{}

type UserTokenRepository struct{}

type SentEmailRepository struct{}

type EmailTemplateRepository struct{}

type PaymentRepository struct{}

type PaymentMethodRepository struct{}

type OrderRepository struct{}

type CategoryRepository struct{}

type CategoryRelationRepository struct{}

type HashtagRepository struct{}

type HashtagRelationRepository struct{}

type SectionRepository struct{}

type LessonContentRepository struct {
	BaseRepository
}

type OrderItemRepository struct{}

type CouponRepository struct{}

type CouponHistoryRepository struct{}

type TeamRepository struct{}

type FormRepository struct{}

type FormQuestionRepository struct{}

type FormQuestionOptionRepository struct{}

type FormAnswerRepository struct{}

type FormSessionRepository struct{}

type BlogRepository struct{}

type ApprovalRepository struct {
	BaseRepository
}

type PublishCourseRepository struct {
	BaseRepository
}

type PublishBlogRepository struct{}

type UserActionRepository struct{}

type PageConfigRepository struct{}

type PageAccessRepository struct{}

type WalletRepository struct{}

type TransactionRepository struct{}

type CourseEnrollmentRepository struct {
	BaseRepository
}

type QuizRepository struct{}

type QuizRelationRepository struct{}

type QuizQuestionRepository struct{}

type QuizSubmissionRepository struct{}

type QuizAnswerRepository struct{}

type AffiliateCampaignRepository struct{}

type CommissionRepository struct{}

type CourseCampaignRepository struct{}

type ReferralRepository struct{}

type UserSettingRepository struct{}

type ReferralLinkRepository struct{}

type ReferrerRepository struct{}

type LearningProgressRepository struct{}

type FormRelationRepository struct{}

type FormAnswerStatsRepository struct{}

type BookmarkRepository struct{}

type CertificateRepository struct{}

type CertificateLayerRepository struct{}

type CertificateTemplateRepository struct{}

type HtmlTemplateRepository struct{}

type AIBlogRewriteRepository struct{}

type UserSummaryRepository struct{}

type ReportRepository struct{}

type AICourseRepository struct{}

type AIHistoryRepository struct{}

type PricingPlanRepository struct{}

type SubscriptionRepository struct{}

type ResourceUsageRepository struct{}

type AIModelRepository struct{}

type ReminderRepository struct{}

type FeaturedContentRepository struct{}

type ClpLaunchpadRepository struct {
	BaseRepository
}

type ClpVotingMilestoneRepository struct{}

type ClpCourseLaunchpadRepository struct {
	BaseRepository
}

type ClpInvestmentRepository struct {
	BaseRepository
}

type ClpVotingPhaseRepository struct{}

type ClpVotingLaunchpadRepository struct {
	BaseRepository
}

type ReferralHistoryRepository struct{}

type ReferralProgramSettingRepository struct{}

type CourseRevenuePointRepository struct {
	BaseRepository
}

type OEPointHistoryRepository struct {
	BaseRepository
}

type OEPointCampaignRepository struct {
	BaseRepository
}

type OEReferralCodeRepository struct {
	BaseRepository
}

type OEReferralRepository struct {
	BaseRepository
}

type LearningStatusRepository struct {
	BaseRepository
}

type OEReferralLeaderBoardRepository struct {
	ctx context.Context
}

type OECampaignAccountRepository struct {
	ctx context.Context
}

type OEReferralReportRepository struct {
	ctx context.Context
}

type ScheduleRepository struct {
	BaseRepository
}

type EventScheduleRepository struct {
	BaseRepository
}

func (r *AppRepository) Schedule(ctx context.Context) *ScheduleRepository {
	return &ScheduleRepository{BaseRepository{ctx}}
}

func (r *AppRepository) EventSchedule(ctx context.Context) *EventScheduleRepository {
	return &EventScheduleRepository{BaseRepository{ctx}}
}

func (r *AppRepository) OEPointHistory(ctx context.Context) *OEPointHistoryRepository {
	return &OEPointHistoryRepository{BaseRepository{ctx}}
}

func (r *AppRepository) OEPointCampaign(ctx context.Context) *OEPointCampaignRepository {
	return &OEPointCampaignRepository{BaseRepository{ctx}}
}

func (r *AppRepository) OEReferralCode(ctx context.Context) *OEReferralCodeRepository {
	return &OEReferralCodeRepository{BaseRepository{ctx}}
}

func (r *AppRepository) OEReferral(ctx context.Context) *OEReferralRepository {
	return &OEReferralRepository{BaseRepository{ctx}}
}

func (r *AppRepository) LessonContent(ctx context.Context) LessonContentRepositoryIface {
	return &LessonContentRepository{BaseRepository{ctx}}
}

func (r *AppRepository) Course(ctx context.Context) CourseRepositoryIface {
	return &CourseRepository{BaseRepository{ctx}}
}

func (r *AppRepository) LearningStatus(ctx context.Context) LearningStatusRepositoryIface {
	return &LearningStatusRepository{BaseRepository{ctx}}
}

func (r *AppRepository) OEReferralLeaderBoard(ctx context.Context) *OEReferralLeaderBoardRepository {
	return &OEReferralLeaderBoardRepository{ctx}
}

func (r *AppRepository) OECampaignAccount(ctx context.Context) *OECampaignAccountRepository {
	return &OECampaignAccountRepository{ctx}
}

func (r *AppRepository) OEReferralReport(ctx context.Context) *OEReferralReportRepository {
	return &OEReferralReportRepository{ctx}
}

type AIPromptRepository struct{}

func (r *AppRepository) PublishCourse(ctx context.Context) PublishCourseRepositoryIface {
	return &PublishCourseRepository{BaseRepository{ctx}}
}

func (r *AppRepository) ClpLaunchpad(ctx context.Context) ClpLaunchpadRepositoryIface {
	return &ClpLaunchpadRepository{BaseRepository{ctx}}
}

func (r *AppRepository) ClpCourseLaunchpad(ctx context.Context) ClpCourseLaunchpadRepositoryIface {
	return &ClpCourseLaunchpadRepository{BaseRepository{ctx}}
}

func (r *AppRepository) ClpInvestment(ctx context.Context) ClpInvestmentRepositoryIface {
	return &ClpInvestmentRepository{BaseRepository{ctx}}
}

func (r *AppRepository) ClpVotingLaunchpad(ctx context.Context) ClpVotingLaunchpadRepositoryIface {
	return &ClpVotingLaunchpadRepository{BaseRepository{ctx}}
}

func (r *AppRepository) CourseEnrollment(ctx context.Context) CourseEnrollmentRepositoryIface {
	return &CourseEnrollmentRepository{BaseRepository{ctx}}
}

func (r *AppRepository) CourseRevenue(ctx context.Context) CourseRevenuePointRepositoryIface {
	return &CourseRevenuePointRepository{BaseRepository{ctx}}
}

type UserRepositoryIface interface {
	Create(user *User) error
	CreateMany(users []*User, trans *gorm.DB) error
	FindPage(query *UserQuery, options *FindPageOptions) ([]*User, *Pagination, error)
	FindByID(id string) (*User, error)
	FindOne(query *UserQuery, options *FindOneOptions) (*User, error)
	FindByIDWithOpts(id string, opts *FindOneOptions) (*User, error)
	FindByEmailWithOpts(email string, opts *FindOneOptions) (*User, error)
	FindByUsernameWithOpts(username string, opts *FindOneOptions) (*User, error)
	FindMany(query *UserQuery, options *FindManyOptions) ([]*User, error)
	Update(user *User) (*User, error)
	UpdatePassword(user *User) (*User, error)
	Delete(id string, trans *gorm.DB) error
	Count(query *UserQuery) (int64, error)
	WithdrawUser(id string, email string, transaction *gorm.DB) error
	CanRegister(username, email string) (bool, error)
	UpdateRoles(role *UserRoleOrg) error
	UpsertMany(user []*User, trans *gorm.DB) error
	UpdateByAdmin(user *User) (*User, error)
	FindUserFromRoles(query *UserQuery, options *FindPageOptions) ([]*User, *Pagination, error)
	FindManyMapEmail(emails []string) (map[string]*User, error)
	FindManySimpleUser(query *UserQuery, options *FindManyOptions) ([]*BasicUserProfile, error)
}

type RoleRepositoryIface interface {
	CreateOrUpdate(role *Role) (*Role, error)
	CreateRole(role *Role) (*Role, error)
	CreateRoles(roles []*Role) ([]*Role, error)
	FindRoles(pageNum int, PerPage int, maps any) ([]*Role, error)
	FindAll() ([]*Role, error)
	FindById(id string) (*Role, error)
	CountTotalRoles() (int64, error)
	FindMapAllRoleToLevel() (map[string]int, error)
	Update(r *Role, trans *gorm.DB) error
	Delete(id string, org *string, trans *gorm.DB) error
	FindPage(query *RoleQuery, options *FindPageOptions) ([]*Role, *Pagination, error)
}

type UserRoleOrgRepositoryIface interface {
	Create(entity *UserRoleOrg) error
	FindByUserId(id string) ([]*UserRoleOrg, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *UserRoleOrgQuery, transaction *gorm.DB) (int64, error)
	FindOne(query *UserRoleOrgQuery, options *FindOneOptions) (*UserRoleOrg, error)
	FindPage(query *UserRoleOrgQuery, options *FindPageOptions) ([]*UserRoleOrg, *Pagination, error)
	FindMany(query *UserRoleOrgQuery, options *FindManyOptions) ([]*UserRoleOrg, error)
	Update(c *UserRoleOrg, trans *gorm.DB) error
	FirstOrCreate(role *UserRoleOrg) (*UserRoleOrg, error)
	UpsertManyRole(role []*UserRoleOrg, trans *gorm.DB) error
}

type PermissionRepositoryIface interface {
	Create(permission *Permission, trans *gorm.DB) error
	CreateMany(permissions []*Permission, trans *gorm.DB) error
	FetchAll() ([]*Permission, error)
	FindAll() ([]*SimplePermission, error)
	FindMany(query *PermissionQuery, options *FindManyOptions) ([]*Permission, error)
	FindAccesses(method, path, controllerName string) ([]*RoleAccess, error)
	Update(permission *Permission, trans *gorm.DB) error
	Delete(permission *Permission, trans *gorm.DB) error
	DeleteMany(query *PermissionQuery, trans *gorm.DB) (int64, error)
	Count(query *PermissionQuery) (int64, error)
}

type SnsAccountRepositoryIface interface {
	Create(snsAccount *SnsAccount) (*SnsAccount, error)
	CreateMany(snsAccounts []*SnsAccount, trans *gorm.DB) error
	Update(snsAccount *SnsAccount, transaction *gorm.DB) (*SnsAccount, error)
	FindByAccountIDAndProvider(accountId string, provider string) (*SnsAccount, error)
	FindMany(query *FindSnsAccountsQuery) ([]*SnsAccount, error)
	FindByID(id string) (*SnsAccount, error)
	DeleteByID(id string) error
	FindOne(query *FindSnsAccountsQuery) (*SnsAccount, error)
	DeleteMany(query *FindSnsAccountsQuery, transaction *gorm.DB) (int64, error)
}

type SystemConfigRepositoryIface interface {
	Upsert(c *SystemConfig) error
	FindAll() ([]*SystemConfig, error)
	FindByKey(key string) (*SystemConfig, error)
	FindOne(query *SystemConfigQuery, options *FindOneOptions) (*SystemConfig, error)
	Create(c *SystemConfig, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*SystemConfig, error)
	FindMany(query *SystemConfigQuery, options *FindManyOptions) ([]*SystemConfig, error)
	FindPage(query *SystemConfigQuery, options *FindPageOptions) ([]*SystemConfig, *Pagination, error)
	Update(c *SystemConfig, trans *gorm.DB) error
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *SystemConfigQuery, trans *gorm.DB) (int64, error)
}

type SessionRepositoryIface interface {
	Create(session *Session) error
	Update(session *Session) (*Session, error)
	FindById(id string) (*Session, error)
	FindByToken(token string) (*Session, error)
	DeleteByUserID(userID string, transaction *gorm.DB) error
	FindOne(query *SessionQuery, options *FindOneOptions) (*Session, error)
	Upsert(session *Session, trans *gorm.DB) error
}

type OrganizationRepositoryIface interface {
	Create(c *Organization, trans *gorm.DB) error
	CreateMany(orgs []*Organization, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Organization, error)
	FindByDomain(domain string) (*Organization, error)
	FindOne(query *OrganizationQuery, options *FindOneOptions) (*Organization, error)
	FindMany(query *OrganizationQuery, options *FindManyOptions) ([]*Organization, error)
	FindPage(query *OrganizationQuery, options *FindPageOptions) ([]*Organization, *Pagination, error)
	Update(c *Organization, trans *gorm.DB) error
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OrganizationQuery, trans *gorm.DB) (int64, error)
	Count(query *OrganizationQuery) (int64, error)
	FindByIDs(ids []string) ([]*Organization, error)
}

type FileRelationRepositoryIface interface {
	CreateMany(entities []*FileRelation, trans *gorm.DB) error
	FindMany(query *FileRelationQuery, options *FindManyOptions) ([]*FileRelation, error)
	FindPage(query *FileRelationQuery, options *FindPageOptions) ([]*FileRelation, *Pagination, error)
	FindOne(query *FileRelationQuery, options *FindOneOptions) (*FileRelation, error)
	DeleteMany(query *FileRelationQuery, trans *gorm.DB) (err error)
	AddFiles(model ModelName, entityID string, field string, files []*File) error
	GetFiles(model ModelName, entityID string, field string) ([]*File, error)
	GetFilesByEntities(model ModelName, entitiesID []string, field string) (map[string][]*File, error)
}

type FileRepositoryIface interface {
	Create(f *File, trans *gorm.DB) error
	CreateMany(files []*File, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*File, error)
	FindOne(query *FileQuery, options *FindOneOptions) (*File, error)
	FindMany(query *FileQuery, options *FindManyOptions) ([]*File, error)
	FindPage(query *FileQuery, options *FindPageOptions) ([]*File, *Pagination, error)
	Update(f *File, trans *gorm.DB) error
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FileQuery, trans *gorm.DB) (int64, error)
	Count(query *FileQuery) (int64, error)
}

type CourseRepositoryIface interface {
	Create(c *Course, trans *gorm.DB) error
	CreateMany(orgs []*Course, trans *gorm.DB) error
	Update(c *Course, trans *gorm.DB) error
	UpdateBasic(c *Course, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Course, error)
	FindOne(query *CourseQuery, options *FindOneOptions) (*Course, error)
	FindMany(query *CourseQuery, options *FindManyOptions) ([]*Course, error)
	FindPage(query *CourseQuery, options *FindPageOptions) ([]*Course, *Pagination, error)
	FindPageByPartner(query *CourseQuery, options *FindPageOptions) ([]*Course, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CourseQuery, trans *gorm.DB) (int64, error)
	Count(query *CourseQuery) (int64, error)
	UpdateLatestVersions(cuid string) error
	FindManyListItems(query *CourseQuery, options *FindManyOptions) ([]*CourseListItem, error)
	FindUserStats(user *User, courseCUIDs []string) ([]*UserCourseStats, error)
	GetCoursesRevenueSummary(query *CourseRevenueSummaryQuery) (*CourseRevenueSummary, error)
	GetCourseRevenueDetail(query *CourseRevenueDetailQuery, options *FindPageOptions) ([]*CourseRevenueOrderDetail, *Pagination, error)
	FindCoursePropsByCourseID(courseID string) (CourseProps, error)
}

type CoursePartnerRepositoryIface interface {
	Create(entity *CoursePartner, trans *gorm.DB) error
	CreateMany(entities []*CoursePartner, trans *gorm.DB) error
	Update(entity *CoursePartner, trans *gorm.DB) error
	FindOne(query *CoursePartnerQuery, options *FindOneOptions) (*CoursePartner, error)
	FindMany(query *CoursePartnerQuery, options *FindManyOptions) ([]*CoursePartner, error)
	FindPage(query *CoursePartnerQuery, options *FindPageOptions) ([]*CoursePartner, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CoursePartnerQuery, trans *gorm.DB) (int64, error)
	Count(query *CoursePartnerQuery) (int64, error)
	FirstOrCreate(partner *CoursePartner) (*CoursePartner, error)
}

type CoursePriceRepositoryIface interface {
	Create(entity *CoursePrice, trans *gorm.DB) error
	CreateMany(entities []*CoursePrice, trans *gorm.DB) error
	Update(entity *CoursePrice, trans *gorm.DB) error
	Upsert(p *CoursePrice, trans *gorm.DB) error
	FindOne(query *CoursePriceQuery, options *FindOneOptions) (*CoursePrice, error)
	FindMany(query *CoursePriceQuery, options *FindManyOptions) ([]*CoursePrice, error)
	FindPage(query *CoursePriceQuery, options *FindPageOptions) ([]*CoursePrice, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CoursePriceQuery, trans *gorm.DB) (int64, error)
	Count(query *CoursePriceQuery) (int64, error)
}

type UserTokenRepositoryIface interface {
	Create(tk *UserToken, trans *gorm.DB) error
	CreateMany(tks []*UserToken, trans *gorm.DB) error
	Update(tk *UserToken, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*UserToken, error)
	FindOne(query *UserTokenQuery, options *FindOneOptions) (*UserToken, error)
	FindMany(query *UserTokenQuery, options *FindManyOptions) ([]*UserToken, error)
	FindPage(query *UserTokenQuery, options *FindPageOptions) ([]*UserToken, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *UserTokenQuery, trans *gorm.DB) (int64, error)
	Count(query *UserTokenQuery) (int64, error)
}

type EmailTemplateRepositoryIface interface {
	Create(e *EmailTemplate, trans *gorm.DB) error
	Update(e *EmailTemplate, trans *gorm.DB) error
	FindOne(query *EmailTemplateQuery, options *FindOneOptions) (*EmailTemplate, error)
	FindPage(query *EmailTemplateQuery, options *FindPageOptions) ([]*EmailTemplate, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	Count(query *EmailTemplateQuery) (int64, error)
}

type PaymentRepositoryIface interface {
	Create(p *Payment, trans *gorm.DB) error
	CreateMany(p []*Payment, trans *gorm.DB) error
	Update(c *Payment, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Payment, error)
	FindOne(query *PaymentQuery, options *FindOneOptions) (*Payment, error)
	FindMany(query *PaymentQuery, options *FindManyOptions) ([]*Payment, error)
	FindPage(query *PaymentQuery, options *FindPageOptions) ([]*Payment, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *PaymentQuery, trans *gorm.DB) (int64, error)
}

type PaymentMethodRepositoryIface interface {
	Create(p *PaymentMethod, trans *gorm.DB) error
	CreateMany(p []*PaymentMethod, trans *gorm.DB) error
	Update(p *PaymentMethod, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*PaymentMethod, error)
	FindOne(query *PaymentMethodQuery, options *FindOneOptions) (*PaymentMethod, error)
	FindMany(query *PaymentMethodQuery, options *FindManyOptions) ([]*PaymentMethod, error)
	FindPage(query *PaymentMethodQuery, options *FindPageOptions) ([]*PaymentMethod, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *PaymentMethodQuery, trans *gorm.DB) (int64, error)
}

type OrderRepositoryIface interface {
	Create(o *Order, trans *gorm.DB) error
	CreateMany(o []*Order, trans *gorm.DB) error
	Update(o *Order, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Order, error)
	FindOne(query *OrderQuery, options *FindOneOptions) (*Order, error)
	FindMany(query *OrderQuery, options *FindManyOptions) ([]*Order, error)
	FindPage(query *OrderQuery, options *FindPageOptions) ([]*Order, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OrderQuery, trans *gorm.DB) (int64, error)
	FindCourseIDsByOrderID(id string) ([]string, error)
}

type SectionRepositoryIface interface {
	Create(entity *Section, trans *gorm.DB) error
	CreateMany(entities []*Section, trans *gorm.DB) error
	Update(entity *Section, trans *gorm.DB) error
	FindOne(query *SectionQuery, options *FindOneOptions) (*Section, error)
	FindMany(query *SectionQuery, options *FindManyOptions) ([]*Section, error)
	FindPage(query *SectionQuery, options *FindPageOptions) ([]*Section, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	Count(query *SectionQuery) (int64, error)
	FindDistinctMany(query *SectionQuery, options *FindManyOptions) (entities []*Section, err error)
	CountCourseStats(courseID string) (*CourseStats, error)
}

type LessonContentRepositoryIface interface {
	Create(entity *LessonContent, trans *gorm.DB) error
	CreateMany(entities []*LessonContent, trans *gorm.DB) error
	Update(entity *LessonContent, trans *gorm.DB) error
	UpdateMany(entities []*LessonContent, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*LessonContent, error)
	FindOne(query *LessonContentQuery, options *FindOneOptions) (*LessonContent, error)
	FindMany(query *LessonContentQuery, options *FindManyOptions) ([]*LessonContent, error)
	FindPage(query *LessonContentQuery, options *FindPageOptions) ([]*LessonContent, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	Count(query *LessonContentQuery) (int64, error)
	UpdateManyDurationFromWebhook(entities []*LessonContent, trans *gorm.DB) error
	CountEachTypeForSection(section *Section, lessonIDs []string) (map[LessonContentType]int, error)
}

type CategoryRepositoryIface interface {
	Create(e *Category, trans *gorm.DB) error
	CreateMany(es []*Category, trans *gorm.DB) error
	Update(e *Category, trans *gorm.DB) error
	DeactivateMany(query *CategoryQuery, trans *gorm.DB) error
	UpsertMany(cs []*Category, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Category, error)
	FindOne(query *CategoryQuery, options *FindOneOptions) (*Category, error)
	FindMany(query *CategoryQuery, options *FindManyOptions) ([]*Category, error)
	FindPage(query *CategoryQuery, options *FindPageOptions) ([]*Category, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CategoryQuery, trans *gorm.DB) (int64, error)
	Count(query *CategoryQuery) (int64, error)
	IncreaseUseCount(category *Category, field string, amount int, trans *gorm.DB) error
	DecreaseUseCount(category *Category, field string, amount int, trans *gorm.DB) error
	IncreaseManyUseCount(query *CategoryQuery, field string, amount int, trans *gorm.DB) (err error)
	DecreaseManyUseCount(query *CategoryQuery, field string, amount int, trans *gorm.DB) (err error)
	GetFullChildIdsByParentId(parentID string) ([]*Category, error)
}

type CategoryRelationRepositoryIface interface {
	Create(entity *CategoryRelation, trans *gorm.DB) error
	CreateMany(entities []*CategoryRelation, trans *gorm.DB) error
	Update(entity *CategoryRelation, trans *gorm.DB) error
	UpsertMany(ces []*CategoryRelation, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*CategoryRelation, error)
	FindOne(query *CategoryRelationQuery, options *FindOneOptions) (*CategoryRelation, error)
	FindMany(query *CategoryRelationQuery, options *FindManyOptions) ([]*CategoryRelation, error)
	FindPage(query *CategoryRelationQuery, options *FindPageOptions) ([]*CategoryRelation, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CategoryRelationQuery, trans *gorm.DB) (int64, error)
	Count(query *CategoryRelationQuery) (int64, error)
	AddCategories(model ModelName, entityID string, field string, categories []*Category, trans *gorm.DB) error
	GetCategories(model ModelName, entityID string, field string) ([]*Category, error)
	GetCategoriesByEntities(model ModelName, entitiesID []string, field string) (map[string][]*Category, error)
	GetSimpleCategoriesByEntities(model ModelName, entitiesID []string, field string) (map[string][]*SimpleCategory, error)
	FindManyJoinBlog(query *CategoryRelationQuery, options *FindManyOptions) ([]*CategoryRelation, error)
	FindPageJoinBlog(query *CategoryRelationQuery, options *FindPageOptions) ([]*CategoryRelation, *Pagination, error)
}

type HashtagRepositoryIface interface {
	Create(e *Hashtag, trans *gorm.DB) error
	CreateMany(es []*Hashtag, trans *gorm.DB) error
	Update(e *Hashtag, trans *gorm.DB) error
	UpsertMany(hs []*Hashtag, trans *gorm.DB) (err error)
	FindOne(query *HashtagQuery, options *FindOneOptions) (*Hashtag, error)
	FindMany(query *HashtagQuery, options *FindManyOptions) ([]*Hashtag, error)
	FindPage(query *HashtagQuery, options *FindPageOptions) ([]*Hashtag, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *HashtagQuery, trans *gorm.DB) (int64, error)
	Count(query *HashtagQuery) (int64, error)
	IncreaseHashtag(hashtag *Hashtag, field string, amount int, trans *gorm.DB) error
	DecreaseHashtag(hashtag *Hashtag, field string, amount int, trans *gorm.DB) error
}

type HashtagRelationRepositoryIface interface {
	Create(entity *HashtagRelation, trans *gorm.DB) error
	CreateMany(entities []*HashtagRelation, trans *gorm.DB) error
	Update(entity *HashtagRelation, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*HashtagRelation, error)
	FindOne(query *HashtagRelationQuery, options *FindOneOptions) (*HashtagRelation, error)
	FindMany(query *HashtagRelationQuery, options *FindManyOptions) ([]*HashtagRelation, error)
	FindPage(query *HashtagRelationQuery, options *FindPageOptions) ([]*HashtagRelation, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *HashtagRelationQuery, trans *gorm.DB) (int64, error)
	Count(query *HashtagRelationQuery) (int64, error)
	GetSimpleHashtagByEntities(model ModelName, entitiesID []string) (map[string][]*SimpleHashtag, error)
	FindManyJoinBlog(query *HashtagRelationQuery, options *FindManyOptions) ([]*HashtagRelation, error)
	FindPageJoinBlog(query *HashtagRelationQuery, options *FindPageOptions) ([]*HashtagRelation, *Pagination, error)
}

type OrderItemRepositoryIface interface {
	Create(o *OrderItem, trans *gorm.DB) error
	CreateMany(o []*OrderItem, trans *gorm.DB) error
	Update(o *OrderItem, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OrderItem, error)
	FindOne(query *OrderItemQuery, options *FindOneOptions) (*OrderItem, error)
	FindMany(query *OrderItemQuery, options *FindManyOptions) ([]*OrderItem, error)
	FindManyOnSchemas(schemas []string, query *OrderItemQuery, options *FindManyOptions) ([]*OrderItem, error)
	FindPage(query *OrderItemQuery, options *FindPageOptions) ([]*OrderItem, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OrderItemQuery, trans *gorm.DB) (int64, error)
}

type CouponRepositoryIface interface {
	Create(c *Coupon, trans *gorm.DB) error
	CreateMany(c []*Coupon, trans *gorm.DB) error
	Update(c *Coupon, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Coupon, error)
	FindOne(query *CouponQuery, options *FindOneOptions) (*Coupon, error)
	FindMany(query *CouponQuery, options *FindManyOptions) ([]*Coupon, error)
	FindPage(query *CouponQuery, options *FindPageOptions) ([]*Coupon, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CouponQuery, trans *gorm.DB) (int64, error)
	Increment(c *Coupon, field string, amount int, trans *gorm.DB) error
	Decrement(c *Coupon, field string, amount int, trans *gorm.DB) error
}

type CouponHistoryRepositoryIface interface {
	Create(c *CouponHistory, trans *gorm.DB) error
	CreateMany(c []*CouponHistory, trans *gorm.DB) error
	Update(c *CouponHistory, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*CouponHistory, error)
	FindOne(query *CouponHistoryQuery, options *FindOneOptions) (*CouponHistory, error)
	FindMany(query *CouponHistoryQuery, options *FindManyOptions) ([]*CouponHistory, error)
	FindPage(query *CouponHistoryQuery, options *FindPageOptions) ([]*CouponHistory, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CouponHistoryQuery, trans *gorm.DB) (int64, error)
}

type TeamRepositoryIface interface {
	Create(t *Team, trans *gorm.DB) error
	CreateMany(t []*Team, trans *gorm.DB) error
	Update(t *Team, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Team, error)
	FindOne(query *TeamQuery, options *FindOneOptions) (*Team, error)
	FindMany(query *TeamQuery, options *FindManyOptions) ([]*Team, error)
	FindPage(query *TeamQuery, options *FindPageOptions) ([]*Team, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *TeamQuery, trans *gorm.DB) (int64, error)
}

type FormRepositoryIface interface {
	Create(f *Form, trans *gorm.DB) error
	CreateMany(f []*Form, trans *gorm.DB) error
	Update(f *Form, trans *gorm.DB) error
	FindByID(id string) (*Form, error)
	FindBySlug(slug string) (*Form, error)
	FindByIDWithOptions(id string, options *FindOneOptions) (*Form, error)
	FindOne(query *FormQuery, options *FindOneOptions) (*Form, error)
	FindMany(query *FormQuery, options *FindManyOptions) ([]*Form, error)
	FindPage(query *FormQuery, options *FindPageOptions) ([]*Form, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FormQuery, trans *gorm.DB) (int64, error)
}

type FormQuestionRepositoryIface interface {
	Create(q *FormQuestion, trans *gorm.DB) error
	CreateMany(q []*FormQuestion, trans *gorm.DB) error
	Update(q *FormQuestion, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*FormQuestion, error)
	FindOne(query *FormQuestionQuery, options *FindOneOptions) (*FormQuestion, error)
	FindMany(query *FormQuestionQuery, options *FindManyOptions) ([]*FormQuestion, error)
	FindPage(query *FormQuestionQuery, options *FindPageOptions) ([]*FormQuestion, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FormQuestionQuery, trans *gorm.DB) (int64, error)
}

type FormQuestionOptionRepositoryIface interface {
	Create(qc *FormQuestionOption, trans *gorm.DB) error
	CreateMany(qc []*FormQuestionOption, trans *gorm.DB) error
	Update(qc *FormQuestionOption, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*FormQuestionOption, error)
	FindOne(query *FormQuestionOptionQuery, options *FindOneOptions) (*FormQuestionOption, error)
	FindMany(query *FormQuestionOptionQuery, options *FindManyOptions) ([]*FormQuestionOption, error)
	FindPage(query *FormQuestionOptionQuery, options *FindPageOptions) ([]*FormQuestionOption, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FormQuestionOptionQuery, trans *gorm.DB) (int64, error)
}

type FormAnswerRepositoryIface interface {
	Create(a *FormAnswer, trans *gorm.DB) error
	CreateMany(a []*FormAnswer, trans *gorm.DB) error
	Update(a *FormAnswer, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*FormAnswer, error)
	FindOne(query *FormAnswerQuery, options *FindOneOptions) (*FormAnswer, error)
	FindMany(query *FormAnswerQuery, options *FindManyOptions) ([]*FormAnswer, error)
	FindPage(query *FormAnswerQuery, options *FindPageOptions) ([]*FormAnswer, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FormAnswerQuery, trans *gorm.DB) (int64, error)
	FindAnswerCountsByForm(formID string) ([]*FormAnswerCount, error)
	FindAnswerCountsByQuestion(questionID string) ([]*FormAnswerCount, error)
	FindSimpleQuestionAnwser(query *FormAnswerQuery) ([]*VerySimpleFormAnswer, error)
	FindFormAnswerGroupByFormID(formUID string) (map[string][]*FormAnswer, error)
}

type FormSessionRepositoryIface interface {
	Create(s *FormSession, trans *gorm.DB) error
	CreateMany(s []*FormSession, trans *gorm.DB) error
	Update(s *FormSession, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*FormSession, error)
	FindOne(query *FormSessionQuery, options *FindOneOptions) (*FormSession, error)
	Count(query *FormSessionQuery) (int64, error)
	FindMany(query *FormSessionQuery, options *FindManyOptions) ([]*FormSession, error)
	FindPage(query *FormSessionQuery, options *FindPageOptions) ([]*FormSession, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FormSessionQuery, trans *gorm.DB) (int64, error)
	UpdateOutDatedStatus(query *FormSessionQuery, trans *gorm.DB) (count int64, err error)
}

type BlogRepositoryIface interface {
	Create(b *Blog, trans *gorm.DB) error
	CreateMany(b []*Blog, trans *gorm.DB) error
	Update(b *Blog, trans *gorm.DB) error
	UpdateMany(query *BlogQuery, data map[string]interface{}, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Blog, error)
	FindOne(query *BlogQuery, options *FindOneOptions) (*Blog, error)
	FindMany(query *BlogQuery, options *FindManyOptions) ([]*Blog, error)
	FindPage(query *BlogQuery, options *FindPageOptions) ([]*Blog, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *BlogQuery, trans *gorm.DB) (int64, error)
	GetPublishedPersonalBlogsWithHighestVersion(*BlogQuery, *FindPageOptions) (entities []*Blog, pagination *Pagination, err error)
	Count(query *BlogQuery) (int64, error)
	CountByCuid(query *BlogQuery, trans *gorm.DB) (int64, error)
}

type ApprovalRepositoryIface interface {
	Create(a *Approval, trans *gorm.DB) error
	CreateMany(a []*Approval, trans *gorm.DB) error
	Update(a *Approval, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Approval, error)
	FindOne(query *ApprovalQuery, options *FindOneOptions) (*Approval, error)
	FindMany(query *ApprovalQuery, options *FindManyOptions) ([]*Approval, error)
	FindPage(query *ApprovalQuery, options *FindPageOptions) ([]*Approval, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *ApprovalQuery, trans *gorm.DB) (int64, error)
	FindEntityRelation(entityType ModelName, entityID string) (EntityApproval, error)
	FindManyEntityRelation(entityType ModelName, entityID map[string][]string) ([]EntityApproval, error)
	CancelRequestByEntity(newID string, entityType ModelName, entityID string) (err error)
}

type PublishCourseRepositoryIface interface {
	Create(pubCourse *PublishCourse, trans *gorm.DB) error
	Update(pubCourse *PublishCourse, trans *gorm.DB) error
	FindOne(query *PublishCourseQuery, options *FindOneOptions) (*PublishCourse, error)
	FindMany(query *PublishCourseQuery, options *FindManyOptions) ([]*PublishCourse, error)
	FindPage(query *PublishCourseQuery, options *FindPageOptions) ([]*PublishCourse, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	Count(query *PublishCourseQuery) (int64, error)
	FindPageCourseIDs(query *PublishCourseQuery, options *FindPageOptions) ([]string, *Pagination, error)
	FindBySlug(slug string, options *FindOneOptions) (*PublishCourse, error)
}

type PublishBlogRepositoryIface interface {
	Create(pubBlog *PublishBlog, trans *gorm.DB) error
	Update(pubBlog *PublishBlog, trans *gorm.DB) error
	FindOne(query *PublishBlogQuery, options *FindOneOptions) (*PublishBlog, error)
	FindMany(query *PublishBlogQuery, options *FindManyOptions) ([]*PublishBlog, error)
	FindPage(query *PublishBlogQuery, options *FindPageOptions) ([]*PublishBlog, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	Upsert(pubBlog *PublishBlog, trans *gorm.DB) error
	Count(query *PublishBlogQuery) (int64, error)
}

type UserActionRepositoryIface interface {
	Create(entity *UserAction, trans *gorm.DB) error
	CreateMany(entities []*UserAction, trans *gorm.DB) error
	Update(entity *UserAction, trans *gorm.DB) error
	Upsert(ua *UserAction, trans *gorm.DB) (*bool, error)
	FindByID(id string, options *FindOneOptions) (*UserAction, error)
	FindOne(query *UserActionQuery, options *FindOneOptions) (*UserAction, error)
	FindMany(query *UserActionQuery, options *FindManyOptions) ([]*UserAction, error)
	FindPage(query *UserActionQuery, options *FindPageOptions) ([]*UserAction, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *UserActionQuery, trans *gorm.DB) (int64, error)
	DeleteHard(id string, trans *gorm.DB) error
	Count(query *UserActionQuery) (int64, error)
	UpsertMany(userActions []*UserAction, trans *gorm.DB) (err error)
	DeleteHardMultiple(ids []string, trans *gorm.DB) error
}

type PageConfigRepositoryIface interface {
	Create(pageconfig *PageConfig, tran *gorm.DB) error
	CreateMany(entities []*PageConfig, trans *gorm.DB) error
	Update(p *PageConfig, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*PageConfig, error)
	FindOne(query *PageConfigQuery, options *FindOneOptions) (*PageConfig, error)
	FindMany(query *PageConfigQuery, options *FindManyOptions) ([]*PageConfig, error)
	FindPage(query *PageConfigQuery, options *FindPageOptions) ([]*PageConfig, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *PageConfigQuery, trans *gorm.DB) (int64, error)
	UpdateMany(entities []*PageConfig, trans *gorm.DB) error
}

type PageAccessRepositoryIface interface {
	Create(pageAccess *PageAccess, tran *gorm.DB) error
	CreateMany(entities []*PageAccess, trans *gorm.DB) error
	Update(p *PageAccess, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*PageAccess, error)
	FindOne(query *PageAccessQuery, options *FindOneOptions) (*PageAccess, error)
	FindMany(query *PageAccessQuery, options *FindManyOptions) ([]*PageAccess, error)
	FindPage(query *PageAccessQuery, options *FindPageOptions) ([]*PageAccess, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *PageAccessQuery, trans *gorm.DB) (int64, error)
	FindAll() ([]*PageAccess, error)
	UpdateMany(entities []*PageAccess, trans *gorm.DB) error
}

type WalletRepositoryIface interface {
	Create(e *Wallet, trans *gorm.DB) error
	CreateMany(wallets []*Wallet, trans *gorm.DB) error
	IncreaseBalance(wallet *Wallet, amount decimal.Decimal, trans *gorm.DB) error
	IncreaseAvailableBalance(wallet *Wallet, amount decimal.Decimal, trans *gorm.DB) error
	FindOne(query *WalletQuery, options *FindOneOptions) (*Wallet, error)
	FindByID(id string, options *FindOneOptions) (*Wallet, error)
	FindPage(query *WalletQuery, options *FindPageOptions) ([]*Wallet, *Pagination, error)
	FindMany(query *WalletQuery, options *FindManyOptions) ([]*Wallet, error)
	Count(query *WalletQuery) (int64, error)
	Update(w *Wallet, trans *gorm.DB) error
	FindManySimple(query *WalletQuery, options *FindManyOptions) ([]*SimpleWallet, error)
}

type TransactionRepositoryIface interface {
	Create(e *Transaction, trans *gorm.DB) error
	CreateMany(txs []*Transaction, trans *gorm.DB) error
	Update(e *Transaction, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Transaction, error)
	FindOne(query *TransactionQuery, options *FindOneOptions) (*Transaction, error)
	FindPage(query *TransactionQuery, options *FindPageOptions) ([]*Transaction, *Pagination, error)
	FindMany(query *TransactionQuery, options *FindManyOptions) ([]*Transaction, error)
	Count(query *TransactionQuery) (int64, error)
	CreateAndUpdateBalance(transaction *Transaction, shouldUpdateAvailableBalance bool) error
	Delete(id string, trans *gorm.DB) error
}

type CourseEnrollmentRepositoryIface interface {
	Create(courseEnrollment *CourseEnrollment, trans *gorm.DB) error
	CreateMany(courseEnrollment []*CourseEnrollment, trans *gorm.DB) error
	Update(courseEnrollment *CourseEnrollment, trans *gorm.DB) error
	FindOne(query *CourseEnrollmentQuery, options *FindOneOptions) (*CourseEnrollment, error)
	FindMany(query *CourseEnrollmentQuery, options *FindManyOptions) ([]*CourseEnrollment, error)
	FindPage(query *CourseEnrollmentQuery, options *FindPageOptions) ([]*CourseEnrollment, *Pagination, error)
	FirstOrCreate(courseEnrollment *CourseEnrollment) (enrollment *CourseEnrollment, err error)
	Count(query *CourseEnrollmentQuery) (int64, error)
	CountByCourseCUIDs(query *CourseEnrollmentQuery) (map[string]int64, error)
	Upsert(enrollment *CourseEnrollment, trans *gorm.DB) error
}

type QuizRepositoryIface interface {
	Create(q *Quiz, trans *gorm.DB) error
	CreateMany(qs []*Quiz, trans *gorm.DB) error
	Update(q *Quiz, trans *gorm.DB) error
	FindByID(id string) (*Quiz, error)
	FindByIDWithOptions(id string, options *FindOneOptions) (*Quiz, error)
	FindOne(query *QuizQuery, options *FindOneOptions) (*Quiz, error)
	FindMany(query *QuizQuery, options *FindManyOptions) ([]*Quiz, error)
	FindPage(query *QuizQuery, options *FindPageOptions) ([]*Quiz, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *QuizQuery, trans *gorm.DB) (int64, error)
	Count(query *QuizQuery) (int64, error)
}

type QuizRelationRepositoryIface interface {
	Create(q *QuizRelation, trans *gorm.DB) error
	CreateMany(qs []*QuizRelation, trans *gorm.DB) error
	Update(q *QuizRelation, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*QuizRelation, error)
	FindOne(query *QuizRelationQuery, options *FindOneOptions) (*QuizRelation, error)
	FindMany(query *QuizRelationQuery, options *FindManyOptions) ([]*QuizRelation, error)
	FindPage(query *QuizRelationQuery, options *FindPageOptions) ([]*QuizRelation, *Pagination, error)
	DeleteMany(query *QuizRelationQuery, trans *gorm.DB) (int64, error)
	Count(query *QuizRelationQuery) (int64, error)
}

type FormRelationRepositoryIface interface {
	Create(q *FormRelation, trans *gorm.DB) error
	CreateMany(qs []*FormRelation, trans *gorm.DB) error
	Update(q *FormRelation, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*FormRelation, error)
	FindOne(query *FormRelationQuery, options *FindOneOptions) (*FormRelation, error)
	FindMany(query *FormRelationQuery, options *FindManyOptions) ([]*FormRelation, error)
	FindPage(query *FormRelationQuery, options *FindPageOptions) ([]*FormRelation, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	//DeleteMany(query *FormRelationQuery, trans *gorm.DB) (int64, error)
	//Count(query *FormRelationQuery) (int64, error)
}

type FormAnswerStatsRepositoryIface interface {
	Create(q *FormAnswerStats, trans *gorm.DB) error
	CreateMany(qs []*FormAnswerStats, trans *gorm.DB) error
	Update(q *FormAnswerStats, trans *gorm.DB) error
	Upsert(answerStats *FormAnswerStats, trans *gorm.DB) error
	UpsertMany(entities []*FormAnswerStats, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*FormAnswerStats, error)
	FindOne(query *FormAnswerStatsQuery, options *FindOneOptions) (*FormAnswerStats, error)
	FindMany(query *FormAnswerStatsQuery, options *FindManyOptions) ([]*FormAnswerStats, error)
	FindPage(query *FormAnswerStatsQuery, options *FindPageOptions) ([]*FormAnswerStats, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FormAnswerStatsQuery, trans *gorm.DB) (int64, error)
	Count(query *FormAnswerStatsQuery) (int64, error)
	CountTotalByQuestionUIDs(questionUIDs []string) ([]*FormQuestionTotalCount, error)
}

type QuizQuestionRepositoryIface interface {
	Create(q *QuizQuestion, trans *gorm.DB) error
	CreateMany(qs []*QuizQuestion, trans *gorm.DB) error
	Update(q *QuizQuestion, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*QuizQuestion, error)
	FindOne(query *QuizQuestionQuery, options *FindOneOptions) (*QuizQuestion, error)
	FindMany(query *QuizQuestionQuery, options *FindManyOptions) ([]*QuizQuestion, error)
	FindPage(query *QuizQuestionQuery, options *FindPageOptions) ([]*QuizQuestion, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *QuizQuestionQuery, trans *gorm.DB) (int64, error)
	Count(query *QuizQuestionQuery) (int64, error)
}

type QuizSubmissionRepositoryIface interface {
	Create(q *QuizSubmission, trans *gorm.DB) error
	CreateMany(qs []*QuizSubmission, trans *gorm.DB) error
	Update(q *QuizSubmission, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*QuizSubmission, error)
	FindOne(query *QuizSubmissionQuery, options *FindOneOptions) (*QuizSubmission, error)
	FindMany(query *QuizSubmissionQuery, options *FindManyOptions) ([]*QuizSubmission, error)
	FindPage(query *QuizSubmissionQuery, options *FindPageOptions) ([]*QuizSubmission, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *QuizSubmissionQuery, trans *gorm.DB) (int64, error)
	Count(query *QuizSubmissionQuery) (int64, error)
	FindRanksByQuizUID(quizUID, userID string, top int) ([]*QuizSubmissionRank, error)
	FindTotalPointForUserByCourse(courseCuid, userID string) (int, error)
	CountQuizSubmisison(user *User, quizIDs []string) (map[string]int, error)
}

type QuizAnswerRepositoryIface interface {
	Create(q *QuizAnswer, trans *gorm.DB) error
	CreateMany(qs []*QuizAnswer, trans *gorm.DB) error
	Update(q *QuizAnswer, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*QuizAnswer, error)
	FindOne(query *QuizAnswerQuery, options *FindOneOptions) (*QuizAnswer, error)
	FindMany(query *QuizAnswerQuery, options *FindManyOptions) ([]*QuizAnswer, error)
	FindPage(query *QuizAnswerQuery, options *FindPageOptions) ([]*QuizAnswer, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *QuizAnswerQuery, trans *gorm.DB) (int64, error)
	Count(query *QuizAnswerQuery) (int64, error)
}

type BookmarkRepositoryIface interface {
	Create(c *Bookmark, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Bookmark, error)
	FindOne(query *BookmarkQuery, options *FindOneOptions) (*Bookmark, error)
	FindMany(query *BookmarkQuery, options *FindManyOptions) ([]*Bookmark, error)
	FindPage(query *BookmarkQuery, options *FindPageOptions) ([]*Bookmark, *Pagination, error)
	Update(c *Bookmark, trans *gorm.DB) error
	Delete(id string, trans *gorm.DB) error
}

type CertificateRepositoryIface interface {
	Create(cert *Certificate, trans *gorm.DB) error
	Update(cert *Certificate, trans *gorm.DB) error
	FindOne(query *CertificateQuery, options *FindOneOptions) (*Certificate, error)
	FindPage(query *CertificateQuery, options *FindPageOptions) ([]*Certificate, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	FindMany(query *CertificateQuery, options *FindManyOptions) ([]*Certificate, error)
}

type HtmlTemplateRepositoryIface interface {
	Create(template *HtmlTemplate, trans *gorm.DB) error
	FindOne(query *HtmlTemplateQuery, options *FindOneOptions) (*HtmlTemplate, error)
	FindPage(query *HtmlTemplateQuery, options *FindPageOptions) ([]*HtmlTemplate, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	FindByID(id string) (*HtmlTemplate, error)
	Update(template *HtmlTemplate, trans *gorm.DB) error
	FindPageCertificateTemplateForCourse(courseCuid string, templateRootID string, org *Organization, options *FindPageOptions) ([]*HtmlTemplate, *Pagination, error)
	FindPageCertificateTemplate(templateRootID string, org *Organization, options *FindPageOptions) ([]*HtmlTemplate, *Pagination, error)
}

type UserSettingRepositoryIface interface {
	Create(e *UserSetting, trans *gorm.DB) error
	CreateMany(ts []*UserSetting, trans *gorm.DB) error
	Update(f *UserSetting, trans *gorm.DB) error
	FindOne(query *UserSettingQuery, options *FindOneOptions) (*UserSetting, error)
	FindPage(query *UserSettingQuery, options *FindPageOptions) ([]*UserSetting, *Pagination, error)
	FindMany(query *UserSettingQuery, options *FindManyOptions) ([]*UserSetting, error)
	Count(query *UserSettingQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
	CanUseUsername(username string, trans *gorm.DB) (bool, error)
}

type AffiliateCampaignIface interface {
	Create(e *AffiliateCampaign, trans *gorm.DB) error
	CreateMany(ts []*AffiliateCampaign, trans *gorm.DB) error
	Update(f *AffiliateCampaign, trans *gorm.DB) error
	FindOne(query *AffiliateCampaignQuery, options *FindOneOptions) (*AffiliateCampaign, error)
	FindPage(query *AffiliateCampaignQuery, options *FindPageOptions) ([]*AffiliateCampaign, *Pagination, error)
	FindMany(query *AffiliateCampaignQuery, options *FindManyOptions) ([]*AffiliateCampaign, error)
	Count(query *AffiliateCampaignQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
	GetCampaignsWithApplicableCommissions(userID string,
		query *AffiliateCampaignQuery,
		options *FindPageOptions,
	) ([]*AffiliateCampaign, *Pagination, error)
	GetCampaignByUser(
		userID string,
		query *UserCamapaignQuery,
		options *FindPageOptions,
	) ([]*UserCampaign, *Pagination, error)
}

type CommissionIface interface {
	Create(e *Commission, trans *gorm.DB) error
	CreateMany(ts []*Commission, trans *gorm.DB) error
	Update(f *Commission, trans *gorm.DB) error
	FindOne(query *CommissionQuery, options *FindOneOptions) (*Commission, error)
	FindPage(query *CommissionQuery, options *FindPageOptions) ([]*Commission, *Pagination, error)
	FindMany(query *CommissionQuery, options *FindManyOptions) ([]*Commission, error)
	Count(query *CommissionQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type CourseCampaignIface interface {
	Create(e *CourseCampaign, trans *gorm.DB) error
	CreateMany(ts []*CourseCampaign, trans *gorm.DB) error
	Update(f *CourseCampaign, trans *gorm.DB) error
	FindOne(query *CourseCampaignQuery, options *FindOneOptions) (*CourseCampaign, error)
	FindPage(query *CourseCampaignQuery, options *FindPageOptions) ([]*CourseCampaign, *Pagination, error)
	FindMany(query *CourseCampaignQuery, options *FindManyOptions) ([]*CourseCampaign, error)
	Count(query *CourseCampaignQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *CourseCampaignQuery, trans *gorm.DB) (int64, error)
}

type ReferralIface interface {
	Create(e *Referral, trans *gorm.DB) error
	CreateMany(ts []*Referral, trans *gorm.DB) error
	Update(f *Referral, trans *gorm.DB) error
	FindOne(query *ReferralQuery, options *FindOneOptions) (*Referral, error)
	FindPage(query *ReferralQuery, options *FindPageOptions) ([]*Referral, *Pagination, error)
	FindMany(query *ReferralQuery, options *FindManyOptions) ([]*Referral, error)
	Count(query *ReferralQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
	GetTotalReferralAmountByUser(userID string, query *ReferralSummaryReport) (*TotalReferralAmountReport, error)
	SumReferralAmounts(query *ReferralSummaryReport) (*BaseReferralReport, error)
	GetReferralReportByUser(userID string, query *ReferralReportByUserQuery, options *FindPageOptions) ([]*ReferralReportByUser, *Pagination, error)
}

type ReferralLinkIface interface {
	Create(e *ReferralLink, trans *gorm.DB) error
	CreateMany(ts []*ReferralLink, trans *gorm.DB) error
	Update(f *ReferralLink, trans *gorm.DB) error
	FindOne(query *ReferralLinkQuery, options *FindOneOptions) (*ReferralLink, error)
	FindPage(query *ReferralLinkQuery, options *FindPageOptions) ([]*ReferralLink, *Pagination, error)
	FindMany(query *ReferralLinkQuery, options *FindManyOptions) ([]*ReferralLink, error)
	Count(query *ReferralLinkQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
	MaxShareRateByCampaign(campaignID string) (float32, error)
}

type ReferrerIface interface {
	Create(e *Referrer, trans *gorm.DB) error
	CreateMany(ts []*Referrer, trans *gorm.DB) error
	Update(f *Referrer, trans *gorm.DB) error
	FindOne(query *ReferrerQuery, options *FindOneOptions) (*Referrer, error)
	FindPage(query *ReferrerQuery, options *FindPageOptions) ([]*Referrer, *Pagination, error)
	FindMany(query *ReferrerQuery, options *FindManyOptions) ([]*Referrer, error)
	Count(query *ReferrerQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type AIBlogRewriteRepositoryIface interface {
	Create(entity *AIBlogRewrite, trans *gorm.DB) error
	CreateMany(entity []*AIBlogRewrite, trans *gorm.DB) error
	FindMany(query *AIBlogRewriteQuery, options *FindManyOptions) ([]*AIBlogRewrite, error)
	FindPage(query *AIBlogRewriteQuery, options *FindPageOptions) ([]*AIBlogRewrite, *Pagination, error)
	FindOne(query *AIBlogRewriteQuery, options *FindOneOptions) (*AIBlogRewrite, error)
	Update(entity *AIBlogRewrite, trans *gorm.DB) error
}

type UserSummaryRepositoryIface interface {
	Create(us *UserSummary, trans *gorm.DB) error
	CreateMany(uss []*UserSummary, trans *gorm.DB) error
	Update(us *UserSummary, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*UserSummary, error)
	FindOne(query *UserSummaryQuery, options *FindOneOptions) (*UserSummary, error)
	FindMany(query *UserSummaryQuery, options *FindManyOptions) ([]*UserSummary, error)
	FindPage(query *UserSummaryQuery, options *FindPageOptions) ([]*UserSummary, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *UserSummaryQuery, trans *gorm.DB) (int64, error)
	Count(query *UserSummaryQuery) (int64, error)
	Increase(query *UserSummaryQuery, field string, amount int, trans *gorm.DB) (err error)
	Decrease(query *UserSummaryQuery, field string, amount int, trans *gorm.DB) (err error)
	Upsert(us *UserSummary, trans *gorm.DB) error
}

type ReportRepositoryIface interface {
	ReportUserEnrollmentCourse(query *ReportCourseEnrollmentRequest, trans *gorm.DB) (users []*UserReport, sectionEnrollments map[string][]*UserReport, lessonEnrollments map[string][]*UserReport, courseID *string, err error)
	ReportUserEnrollmentCourseLearningStatus(query *ReportCourseEnrollmentRequest, trans *gorm.DB) (users []*UserReport, courseID *string, err error)
}

type AICourseRepositoryIface interface {
	Create(ac *AICourse, trans *gorm.DB) error
	CreateMany(acs []*AICourse, trans *gorm.DB) error
	Update(ac *AICourse, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*AICourse, error)
	FindOne(query *AICourseQuery, options *FindOneOptions) (*AICourse, error)
	FindMany(query *AICourseQuery, options *FindManyOptions) ([]*AICourse, error)
	FindPage(query *AICourseQuery, options *FindPageOptions) ([]*AICourse, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *AICourseQuery, trans *gorm.DB) (int64, error)
}

type AIHistoryRepositoryIface interface {
	Create(ah *AIHistory, trans *gorm.DB) error
	CreateMany(ahs []*AIHistory, trans *gorm.DB) error
	Update(ah *AIHistory, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*AIHistory, error)
	FindOne(query *AIHistoryQuery, options *FindOneOptions) (*AIHistory, error)
	FindMany(query *AIHistoryQuery, options *FindManyOptions) ([]*AIHistory, error)
	FindPage(query *AIHistoryQuery, options *FindPageOptions) ([]*AIHistory, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *AIHistoryQuery, trans *gorm.DB) (int64, error)
	GetLanguageFromKey(key string) string
	GetKeyFromLanguage(language string) string
}

type PricingPlanRepositoryIface interface {
	Create(q *PricingPlan, trans *gorm.DB) error
	CreateMany(qs []*PricingPlan, trans *gorm.DB) error
	Update(q *PricingPlan, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*PricingPlan, error)
	FindOne(query *PricingPlanQuery, options *FindOneOptions) (*PricingPlan, error)
	FindMany(query *PricingPlanQuery, options *FindManyOptions) ([]*PricingPlan, error)
	FindPage(query *PricingPlanQuery, options *FindPageOptions) ([]*PricingPlan, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
}

type SubscriptionRepositoryIface interface {
	Create(q *Subscription, trans *gorm.DB) error
	CreateMany(qs []*Subscription, trans *gorm.DB) error
	Update(q *Subscription, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Subscription, error)
	FindOne(query *SubscriptionQuery, options *FindOneOptions) (*Subscription, error)
	FindMany(query *SubscriptionQuery, options *FindManyOptions) ([]*Subscription, error)
	FindPage(query *SubscriptionQuery, options *FindPageOptions) ([]*Subscription, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	GetExpirationByPeriod(period int, batchSize int) ([]*Subscription, error)
	GetExpiration(batchSize int) ([]*Subscription, error)
	ExpiredManySubscriptions(entities []*Subscription, trans *gorm.DB) (err error)
}

type ResourceUsageRepositoryIface interface {
	Create(q *ResourceUsage, trans *gorm.DB) error
	CreateMany(qs []*ResourceUsage, trans *gorm.DB) error
	Update(q *ResourceUsage, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*ResourceUsage, error)
	FindOne(query *ResourceUsageQuery, options *FindOneOptions) (*ResourceUsage, error)
	FindMany(query *ResourceUsageQuery, options *FindManyOptions) ([]*ResourceUsage, error)
	FindPage(query *ResourceUsageQuery, options *FindPageOptions) ([]*ResourceUsage, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
}

type AIModelRepositoryIface interface {
	Create(am *AIModel, trans *gorm.DB) error
	CreateMany(ams []*AIModel, trans *gorm.DB) error
	Update(am *AIModel, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*AIModel, error)
	FindOne(query *AIModelQuery, options *FindOneOptions) (*AIModel, error)
	FindMany(query *AIModelQuery, options *FindManyOptions) ([]*AIModel, error)
	FindPage(query *AIModelQuery, options *FindPageOptions) ([]*AIModel, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *AIModelQuery, trans *gorm.DB) (int64, error)
}

type OEPointHistoryRepositoryIface interface {
	Create(q *OEPointHistory, trans *gorm.DB) error
	CreateMany(qs []*OEPointHistory, trans *gorm.DB) error
	Update(q *OEPointHistory, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OEPointHistory, error)
	FindOne(query *OEPointHistoryQuery, options *FindOneOptions) (*OEPointHistory, error)
	FindMany(query *OEPointHistoryQuery, options *FindManyOptions) ([]*OEPointHistory, error)
	FindPage(query *OEPointHistoryQuery, options *FindPageOptions) ([]*OEPointHistory, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	ExpirePoints(expireTime decimal.Decimal, chunkSize int) error
	Count(query *OEPointHistoryQuery) (int64, error)
	Update2Expired(expireDate int, batchSize int) ([]*OEPointHistory, error)
	GetUserActivePoint(userID string) (decimal.Decimal, error)
	GetUserPointByStatusAndSources(userID string, status OEPointStatus, sources []PointSource) (OEPointProps, error)
	UpdateNotifiedByPeriod(period int, batchSize int) ([]*OEPointHistory, error)
	GetUserActiveReferralEarnedPoint(userID string) (int, error)
}

type OEPointCampaignRepositoryIface interface {
	Create(q *OEPointCampaign, trans *gorm.DB) error
	CreateMany(qs []*OEPointCampaign, trans *gorm.DB) error
	Update(q *OEPointCampaign, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OEPointCampaign, error)
	FindOne(query *OEPointCampaignQuery, options *FindOneOptions) (*OEPointCampaign, error)
	FindMany(query *OEPointCampaignQuery, options *FindManyOptions) ([]*OEPointCampaign, error)
	FindPage(query *OEPointCampaignQuery, options *FindPageOptions) ([]*OEPointCampaign, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	Count(query *OEPointCampaignQuery) (int64, error)
}

type OEReferralIface interface {
	Create(q *OEReferral, trans *gorm.DB) error
	CreateMany(qs []*OEReferral, trans *gorm.DB) error
	Update(ah *OEReferral, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OEReferral, error)
	FindOne(query *OEReferralQuery, options *FindOneOptions) (*OEReferral, error)
	FindMany(query *OEReferralQuery, options *FindManyOptions) ([]*OEReferral, error)
	FindPage(query *OEReferralQuery, options *FindPageOptions) ([]*OEReferral, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OEReferralQuery, trans *gorm.DB) (int64, error)
	Count(query *OEReferralQuery) (int64, error)
}

type OEReferralCodeIface interface {
	Create(ah *OEReferralCode, trans *gorm.DB) error
	CreateMany(ahs []*OEReferralCode, trans *gorm.DB) error
	Update(ah *OEReferralCode, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OEReferralCode, error)
	FindOne(query *OEReferralCodeQuery, options *FindOneOptions) (*OEReferralCode, error)
	FindMany(query *OEReferralCodeQuery, options *FindManyOptions) ([]*OEReferralCode, error)
	FindPage(query *OEReferralCodeQuery, options *FindPageOptions) ([]*OEReferralCode, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OEReferralCodeQuery, trans *gorm.DB) (int64, error)
}

type ReminderRepositoryIface interface {
	Create(q *Reminder, trans *gorm.DB) error
	CreateMany(qs []*Reminder, trans *gorm.DB) error
	Update(q *Reminder, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*Reminder, error)
	FindOne(query *ReminderQuery, options *FindOneOptions) (*Reminder, error)
	FindMany(query *ReminderQuery, options *FindManyOptions) ([]*Reminder, error)
	FindPage(query *ReminderQuery, options *FindPageOptions) ([]*Reminder, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
}

type FeaturedContentRepositoryIface interface {
	Create(am *FeaturedContent, trans *gorm.DB) error
	CreateMany(ams []*FeaturedContent, trans *gorm.DB) error
	Update(am *FeaturedContent, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*FeaturedContent, error)
	FindOne(query *FeaturedContentQuery, options *FindOneOptions) (*FeaturedContent, error)
	FindMany(query *FeaturedContentQuery, options *FindManyOptions) ([]*FeaturedContent, error)
	FindPage(query *FeaturedContentQuery, options *FindPageOptions) ([]*FeaturedContent, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *FeaturedContentQuery, trans *gorm.DB) (int64, error)
}

type ClpLaunchpadRepositoryIface interface {
	Create(c *ClpLaunchpad, trans *gorm.DB) error
	Update(c *ClpLaunchpad, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*ClpLaunchpad, error)
	FindOne(query *LaunchpadQuery, options *FindOneOptions) (*ClpLaunchpad, error)
	FindMany(query *LaunchpadQuery, options *FindManyOptions) ([]*ClpLaunchpad, error)
	FindPage(query *LaunchpadQuery, options *FindPageOptions) ([]*ClpLaunchpad, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	Count(query *LaunchpadQuery) (int64, error)
	ValidateFields(fields []string) error
}

type ClpVotingMilestoneRepositoryIface interface {
	Create(e *ClpVotingMilestone, trans *gorm.DB) error
	CreateMany(ts []*ClpVotingMilestone, trans *gorm.DB) error
	Update(f *ClpVotingMilestone, trans *gorm.DB) error
	FindOne(query *ClpVotingMilestoneQuery, options *FindOneOptions) (*ClpVotingMilestone, error)
	FindPage(query *ClpVotingMilestoneQuery, options *FindPageOptions) ([]*ClpVotingMilestone, *Pagination, error)
	FindMany(query *ClpVotingMilestoneQuery, options *FindManyOptions) ([]*ClpVotingMilestone, error)
	Count(query *ClpVotingMilestoneQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type ClpCourseLaunchpadRepositoryIface interface {
	Create(e *ClpCourseLaunchpad, trans *gorm.DB) error
	CreateMany(ts []*ClpCourseLaunchpad, trans *gorm.DB) error
	Update(f *ClpCourseLaunchpad, trans *gorm.DB) error
	FindOne(query *ClpCourseLaunchpadQuery, options *FindOneOptions) (*ClpCourseLaunchpad, error)
	FindPage(query *ClpCourseLaunchpadQuery, options *FindPageOptions) ([]*ClpCourseLaunchpad, *Pagination, error)
	FindMany(query *ClpCourseLaunchpadQuery, options *FindManyOptions) ([]*ClpCourseLaunchpad, error)
	Count(query *ClpCourseLaunchpadQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type ClpInvestmentRepositoryIface interface {
	Create(e *ClpInvestment, trans *gorm.DB) error
	CreateMany(ts []*ClpInvestment, trans *gorm.DB) error
	Update(f *ClpInvestment, trans *gorm.DB) error
	FindOne(query *ClpInvestmentQuery, options *FindOneOptions) (*ClpInvestment, error)
	FindPage(query *ClpInvestmentQuery, options *FindPageOptions) ([]*ClpInvestment, *Pagination, error)
	FindMany(query *ClpInvestmentQuery, options *FindManyOptions) ([]*ClpInvestment, error)
	Count(query *ClpInvestmentQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
	FindPageWithStats(query *ClpInvestmentQuery, options *FindPageOptions) ([]*ClpInvestment, *Pagination, *InvestmentStats, error)
	GetInvestmentStats(query *ClpInvestmentQuery) (*InvestmentStats, error)
	IncreaseAmount(investment *ClpInvestment, amount decimal.Decimal, trans *gorm.DB) (err error)
	GetInvestmentStatsByLaunchpadIDs(launchpadIDs []string) ([]*LaunchpadStats, error)
	UpdateManyInvestmentStatus(status StatusInvestment, launchpadID string, trans *gorm.DB) (err error)
}

type ClpVotingPhaseRepositoryIface interface {
	Create(e *ClpVotingPhase, trans *gorm.DB) error
	CreateMany(ts []*ClpVotingPhase, trans *gorm.DB) error
	Update(f *ClpVotingPhase, trans *gorm.DB) error
	FindOne(query *ClpVotingPhaseQuery, options *FindOneOptions) (*ClpVotingPhase, error)
	FindPage(query *ClpVotingPhaseQuery, options *FindPageOptions) ([]*ClpVotingPhase, *Pagination, error)
	FindMany(query *ClpVotingPhaseQuery, options *FindManyOptions) ([]*ClpVotingPhase, error)
	Count(query *ClpVotingPhaseQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type ClpVotingLaunchpadRepositoryIface interface {
	Create(e *ClpVotingLaunchpad, trans *gorm.DB) error
	CreateMany(ts []*ClpVotingLaunchpad, trans *gorm.DB) error
	Update(f *ClpVotingLaunchpad, trans *gorm.DB) error
	FindOne(query *ClpVotingLaunchpadQuery, options *FindOneOptions) (*ClpVotingLaunchpad, error)
	FindPage(query *ClpVotingLaunchpadQuery, options *FindPageOptions) ([]*ClpVotingLaunchpad, *Pagination, error)
	FindMany(query *ClpVotingLaunchpadQuery, options *FindManyOptions) ([]*ClpVotingLaunchpad, error)
	Count(query *ClpVotingLaunchpadQuery) (int64, error)
	Delete(id string, trans *gorm.DB) error
}

type CourseRevenuePointRepositoryIface interface {
	Create(c *CourseRevenuePoint, trans *gorm.DB) error
	CreateMany(points []*CourseRevenuePoint, trans *gorm.DB) error
	Update(c *CourseRevenuePoint, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*CourseRevenuePoint, error)
	FindOne(query *CourseRevenuePointQuery, options *FindOneOptions) (*CourseRevenuePoint, error)
	FindMany(query *CourseRevenuePointQuery, options *FindManyOptions) ([]*CourseRevenuePoint, error)
	FindPage(query *CourseRevenuePointQuery, options *FindPageOptions) ([]*CourseRevenuePoint, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	AddCourseRevenuePoint(timestamp int64, period TimePeriod) error
}

type AIPromptRepositoryIface interface {
	Create(p *AIPrompt, trans *gorm.DB) error
	CreateMany(entities []*AIPrompt, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*AIPrompt, error)
	Update(p *AIPrompt, trans *gorm.DB) error
	FindOne(query *AIPromptQuery, options *FindOneOptions) (*AIPrompt, error)
	FindMany(query *AIPromptQuery, options *FindManyOptions) ([]*AIPrompt, error)
	FindPage(query *AIPromptQuery, options *FindPageOptions) ([]*AIPrompt, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *AIPromptQuery, trans *gorm.DB) (int64, error)
}

type LearningStatusRepositoryIface interface {
	Create(t *LearningStatus, trans *gorm.DB) error
	CreateMany(ts []*LearningStatus, trans *gorm.DB) error
	Update(t *LearningStatus, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*LearningStatus, error)
	FindOne(query *LearningStatusQuery, options *FindOneOptions) (*LearningStatus, error)
	FindMany(query *LearningStatusQuery, options *FindManyOptions) ([]*LearningStatus, error)
	FindPage(query *LearningStatusQuery, options *FindPageOptions) ([]*LearningStatus, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *LearningStatusQuery, trans *gorm.DB) (int64, error)
	Count(query *LearningStatusQuery) (int64, error)
	FindCurrentSectionsAndLessonsByUsers(courseID, courseCuid string, userIDs []string) (map[string][]*Section, error)
	CountCompletedLessonsByUsers(courseIDs []string, userIDs []string) ([]*CompletedLessonCountByUser, error)
	FindCurrentSectionsAndLessonsByUser(courseIDs []string, user *User) ([]*Section, error)
	FindLatestLearningStatusManyUser(query *LearningStatusQuery, options *FindManyOptions) (entities []*LearningStatus, err error)
}

type OEReferralLeaderBoardRepositoryIface interface {
	Create(e *OEReferralLeaderBoard, trans *gorm.DB) error
	CreateMany(entities []*OEReferralLeaderBoard, trans *gorm.DB) error
	Update(p *OEReferralLeaderBoard, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OEReferralLeaderBoard, error)
	FindOne(query *OEReferralLeaderBoardQuery, options *FindOneOptions) (*OEReferralLeaderBoard, error)
	FindMany(query *OEReferralLeaderBoardQuery, options *FindManyOptions) ([]*OEReferralLeaderBoard, error)
	FindPage(query *OEReferralLeaderBoardQuery, options *FindPageOptions) ([]*OEReferralLeaderBoard, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OEReferralLeaderBoardQuery, trans *gorm.DB) (int64, error)
	Count(query *OEReferralLeaderBoardQuery) (int64, error)
}

type OECampaignAccountRepositoryIface interface {
	Create(entity *OECampaignAccount, trans *gorm.DB) error
	CreateMany(entities []*OECampaignAccount, trans *gorm.DB) error
	Update(a *OECampaignAccount, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OECampaignAccount, error)
	FindOne(query *OECampaignAccountQuery, options *FindOneOptions) (*OECampaignAccount, error)
	FindMany(query *OECampaignAccountQuery, options *FindManyOptions) ([]*OECampaignAccount, error)
	FindPage(query *OECampaignAccountQuery, options *FindPageOptions) ([]*OECampaignAccount, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OECampaignAccountQuery, trans *gorm.DB) (int64, error)
	Count(query *OECampaignAccountQuery) (int64, error)
}

type OEReferralReportRepositoryIface interface {
	Create(entity *OEReferralReport, trans *gorm.DB) error
	CreateMany(entities []*OEReferralReport, trans *gorm.DB) error
	Update(a *OEReferralReport, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*OEReferralReport, error)
	FindOne(query *OEReferralReportQuery, options *FindOneOptions) (*OEReferralReport, error)
	FindMany(query *OEReferralReportQuery, options *FindManyOptions) ([]*OEReferralReport, error)
	FindPage(query *OEReferralReportQuery, options *FindPageOptions) ([]*OEReferralReport, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
	DeleteMany(query *OEReferralReportQuery, trans *gorm.DB) (int64, error)
	Count(query *OEReferralReportQuery) (int64, error)
	GenReportsFromRegisterEvent(campaignKey string, orgID string, from int, to int) error
	GenReportsFromEnrollEvent(campaignKey string, orgID string, from int, to int, courseCuids []string)
	GenReportsFromLearningStatus(campaignKey string, orgID string, from int, to int, courseCuids []string) error
	GenReportsFromCertificates(campaignKey string, orgID string, from int, to int, courseCuids []string) error
	FindGeneralStats(
		fromDate int64,
		toDate int64,
		campaignKey string,
		courseCUIDs []string,
	) (*OERefGeneralStats, error)
	FindSectionStatsByCourse(
		fromDate int64,
		toDate int64,
		campaignKey string,
		courseOutline *Course,
	) ([]*OERefSectionStats, error)
	FindStatsByProvinces(
		fromDate int64,
		toDate int64,
		campaignKey string,
		provinces []string,
		courseCUIDs []string,
	) ([]*OERefProvinceStats, error)
	FindSectionStatsByProvinces(
		fromDate, toDate int64,
		campaignKey string,
		provinces []string,
		courseCUIDs []string,
	) (map[string]map[string]*OERefSectionByProvinceStats, error)
}

type ScheduleRepositoryIface interface {
	Create(p *Schedule, trans *gorm.DB) error
	Update(p *Schedule, trans *gorm.DB) error
	FindMany(query *ScheduleQuery, options *FindManyOptions) ([]*Schedule, error)
	FindByID(id string, options *FindOneOptions) (*EventSchedule, error)
	Delete(id string, trans *gorm.DB) error
}

type EventScheduleRepositoryIface interface {
	Create(p *EventSchedule, trans *gorm.DB) error
	FindByID(id string, options *FindOneOptions) (*EventSchedule, error)
	Update(p *EventSchedule, trans *gorm.DB) error
	FindOne(query *EventScheduleQuery, options *FindOneOptions) (*EventSchedule, error)
	FindMany(query *EventScheduleQuery, options *FindManyOptions) ([]*EventSchedule, error)
	FindPage(query *EventScheduleQuery, options *FindPageOptions) ([]*EventSchedule, *Pagination, error)
	Delete(id string, trans *gorm.DB) error
}
