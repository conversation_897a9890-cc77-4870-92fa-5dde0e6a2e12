package models

import (
	"openedu-core/pkg/util"

	"github.com/shopspring/decimal"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type CouponType string

type CouponScope string

type CouponMethod string

const (
	DiscountVoucher CouponMethod = "voucher"
	DiscountCoupon  CouponMethod = "coupon"

	CouponScopeCreator CouponScope = "creator"
	CouponScopeOrg     CouponScope = "organization"
	CouponScopeSystem  CouponScope = ""

	CouponPercentage CouponType = "percent"
	CouponFlat       CouponType = "flat"
)

type Coupon struct {
	Model
	OrgID       string       `json:"org_id" gorm:"type:varchar(20)"`
	CreatedBy   string       `json:"created_by" gorm:"type:varchar(20)"`
	Name        string       `json:"name"`
	Description string       `json:"description"`
	CouponCode  string       `json:"coupon_code"`
	Type        CouponType   `json:"type" gorm:"type:varchar(100)"`
	Method      CouponMethod `json:"method" gorm:"type:varchar(100)"`
	Scope       CouponScope  `json:"scope" gorm:"type:varchar(50)"`
	StartDate   int64        `json:"start_date"`
	EndDate     int64        `json:"end_date"`
	IsActive    bool         `json:"is_active" gorm:"default:false"`

	FiatDiscountEnabled      bool            `json:"fiat_discount_enabled" gorm:"default:false"`
	FiatDiscountPercentage   decimal.Decimal `json:"fiat_discount_percentage" gorm:"type:numeric(19,4);not null;default:0"`
	FiatMinAmountToUse       decimal.Decimal `json:"fiat_min_amount_to_use" gorm:"type:numeric(19,4);not null;default:0"`
	FiatAllowMaximumDiscount decimal.Decimal `json:"fiat_allow_maximum_discount" gorm:"type:numeric(19,4);not null;default:0"`
	FiatDiscountAmount       decimal.Decimal `json:"fiat_discount_amount" gorm:"type:numeric(19,4);not null;default:0"`

	CryptoDiscountEnabled      bool            `json:"crypto_discount_enabled" gorm:"default:false"`
	CryptoDiscountPercentage   decimal.Decimal `json:"crypto_discount_percentage" gorm:"type:numeric(19,4);not null;default:0"`
	CryptoMinAmountToUse       decimal.Decimal `json:"crypto_min_amount_to_use" gorm:"type:numeric(19,4);not null;default:0"`
	CryptoAllowMaximumDiscount decimal.Decimal `json:"crypto_allow_maximum_discount" gorm:"type:numeric(19,4);not null;default:0"`
	CryptoDiscountAmount       decimal.Decimal `json:"crypto_discount_amount" gorm:"type:numeric(19,4);not null;default:0"`

	TotalUsed         int16 `json:"total_used" gorm:"default:0"`
	MaximumTotalUsage int16 `json:"maximum_total_usage"`

	AllowCourses StringArray `json:"allow_courses" gorm:"type:jsonb"`
	AllowTeams   StringArray `json:"allow_teams" gorm:"type:jsonb"`

	Course     []*Course        `json:"courses" gorm:"-"`
	AllowUsers []*SimpleProfile `json:"users" gorm:"-"`
}

type SimpleCoupon struct {
	Model
	OrgID       string       `json:"org_id"`
	Name        string       `json:"name"`
	Description string       `json:"description"`
	CouponCode  string       `json:"coupon_code"`
	Type        CouponType   `json:"type"`
	Method      CouponMethod `json:"method"`
	Scope       CouponScope  `json:"scope"`
	StartDate   int64        `json:"start_date"`
	EndDate     int64        `json:"end_date"`

	FiatDiscountEnabled      bool            `json:"fiat_discount_enabled"`
	FiatDiscountPercentage   decimal.Decimal `json:"fiat_discount_percentage"`
	FiatMinAmountToUse       decimal.Decimal `json:"fiat_min_amount_to_use"`
	FiatAllowMaximumDiscount decimal.Decimal `json:"fiat_allow_maximum_discount"`
	FiatDiscountAmount       decimal.Decimal `json:"fiat_discount_amount"`

	CryptoDiscountEnabled      bool            `json:"crypto_discount_enabled"`
	CryptoDiscountPercentage   decimal.Decimal `json:"crypto_discount_percentage"`
	CryptoMinAmountToUse       decimal.Decimal `json:"crypto_min_amount_to_use"`
	CryptoAllowMaximumDiscount decimal.Decimal `json:"crypto_allow_maximum_discount"`
	CryptoDiscountAmount       decimal.Decimal `json:"crypto_discount_amount"`

	MaximumTotalUsage int16 `json:"maximum_total_usage"`

	AllowCourses StringArray `json:"allow_courses"`
	AllowTeams   StringArray `json:"allow_teams"`

	IsActive   bool             `json:"is_active"`
	Course     []*Course        `json:"courses" gorm:"-"`
	AllowUsers []*SimpleProfile `json:"users" gorm:"-"`
	CreatedBy  string           `json:"created_by" gorm:"type:varchar(20)"`
}

type CouponQuery struct {
	ID             *string       `json:"id" form:"id"`
	OrgID          *string       `json:"org_id" form:"org_id"`
	OrgIDIn        []string      `json:"org_id_in" form:"org_id_in"`
	AllowCourses   []string      `json:"allow_courses" form:"allow_courses"`
	AllowTeams     []string      `json:"allow_teams" form:"allow_teams"`
	CourseIDs      []string      `json:"course_ids" form:"course_ids"`
	CouponCode     *string       `json:"coupon_code" form:"coupon_code"`
	Type           *CouponType   `json:"type" form:"type"`
	Method         *CouponMethod `json:"method" form:"method"`
	Scope          *CouponScope  `json:"scope" form:"scope"`
	CreatedBy      *string       `json:"created_by" form:"created_by"`
	IsActive       *bool         `json:"is_active" form:"is_active"`
	IncludeDeleted *bool         `json:"include_deleted" form:"include_deleted"`
	BaseSearchQuery
}

func (query *CouponQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if len(query.OrgIDIn) > 0 {
		qb = qb.Where("org_id IN (?)", query.OrgIDIn)
	}

	if query.CouponCode != nil {
		qb = qb.Where("coupon_code = ?", *query.CouponCode)
	}

	if len(query.AllowCourses) > 0 {
		qb = qb.Where("allow_courses && ?", StringArray(query.AllowCourses))
	}

	if len(query.AllowTeams) > 0 {
		qb = qb.Where("allow_teams && ?", StringArray(query.AllowTeams))
	}

	if query.IsActive != nil {
		qb = qb.Where("is_active = ?", *query.IsActive)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.Method != nil {
		qb = qb.Where("method = ?", *query.Method)
	}

	if query.Scope != nil {
		qb = qb.Where("scope = ?", *query.Scope)
	}

	if query.CreatedBy != nil {
		qb = qb.Where("created_by = ?", *query.CreatedBy)
	}

	if len(query.CourseIDs) > 0 {
		qb = qb.Where("EXISTS (SELECT 1 FROM jsonb_array_elements_text(allow_courses) course_id WHERE course_id IN (?))", query.CourseIDs)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, Coupon{}, nil)
	}

	return qb
}

func (c *Coupon) ToSimple() *SimpleCoupon {
	return &SimpleCoupon{
		Model:                      c.Model,
		OrgID:                      c.OrgID,
		Name:                       c.Name,
		Description:                c.Description,
		CouponCode:                 c.CouponCode,
		Type:                       c.Type,
		Method:                     c.Method,
		StartDate:                  c.StartDate,
		EndDate:                    c.EndDate,
		FiatDiscountEnabled:        c.FiatDiscountEnabled,
		FiatDiscountPercentage:     c.FiatDiscountPercentage,
		FiatDiscountAmount:         c.FiatDiscountAmount,
		FiatAllowMaximumDiscount:   c.FiatAllowMaximumDiscount,
		FiatMinAmountToUse:         c.FiatMinAmountToUse,
		CryptoDiscountEnabled:      c.CryptoDiscountEnabled,
		CryptoDiscountPercentage:   c.CryptoDiscountPercentage,
		CryptoMinAmountToUse:       c.CryptoMinAmountToUse,
		CryptoDiscountAmount:       c.CryptoDiscountAmount,
		CryptoAllowMaximumDiscount: c.CryptoAllowMaximumDiscount,
		MaximumTotalUsage:          c.MaximumTotalUsage,
		AllowCourses:               c.AllowCourses,
		AllowTeams:                 c.AllowTeams,
		IsActive:                   c.IsActive,
		Course:                     c.Course,
		CreatedBy:                  c.CreatedBy,
		AllowUsers:                 c.AllowUsers,
	}
}

func (r *CouponRepository) Create(c *Coupon, trans *gorm.DB) error {
	return create(CouponTbl, c, trans)
}

func (r *CouponRepository) CreateMany(c []*Coupon, trans *gorm.DB) error {
	return createMany(CouponTbl, c, trans)
}

func (r *CouponRepository) Update(c *Coupon, trans *gorm.DB) error {
	return update(CouponTbl, c, trans)
}

func (r *CouponRepository) FindByID(id string, options *FindOneOptions) (*Coupon, error) {
	return findByID[Coupon](CouponTbl, id, options)
}

func (r *CouponRepository) FindOne(query *CouponQuery, options *FindOneOptions) (*Coupon, error) {
	shouldPreloadCourse := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, "Course") {
		shouldPreloadCourse = true
		options.Preloads = util.RemoveElement(options.Preloads, "Course")
	}
	coupon, err := findOne[Coupon](CouponTbl, query, options)
	if err != nil {
		return nil, err
	}

	if shouldPreloadCourse {
		// todo: preload by publish_courses
		//courses, err := Repository.PublishCourse.FindMany(
		//	&PublishCourseQuery{
		//		CourseCuidIn: coupon.AllowCourses,
		//	},
		//	nil)
		//if err != nil {
		//	return nil, err
		//}
		//coupon.Course = courses
	}

	return coupon, nil
}

func (r *CouponRepository) FindMany(query *CouponQuery, options *FindManyOptions) ([]*Coupon, error) {
	return findMany[Coupon](CouponTbl, query, options)
}

func (r *CouponRepository) FindPage(query *CouponQuery, options *FindPageOptions) ([]*Coupon, *Pagination, error) {
	shouldPreloadUser := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, "User") {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, "User")
	}

	coupons, pagination, err := findPage[Coupon](CouponTbl, query, options)
	if err != nil {
		return nil, nil, err
	}
	if shouldPreloadUser {
		if preloadErr := preloadCouponUser(coupons); preloadErr != nil {
			return nil, nil, preloadErr
		}
	}

	return coupons, pagination, nil
}

func (r *CouponRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Coupon](CouponTbl, id, trans)
}

func (r *CouponRepository) DeleteMany(query *CouponQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Coupon](CouponTbl, query, trans)
}

func (r *CouponRepository) Increment(c *Coupon, field string, amount int, trans *gorm.DB) error {
	return increment[Coupon](CouponTbl, c.ID, field, amount, trans)
}

func (r *CouponRepository) Decrement(c *Coupon, field string, amount int, trans *gorm.DB) error {
	return decrement[Coupon](CouponTbl, c.ID, field, amount, trans)
}

func preloadCouponUser(coupons []*Coupon) error {
	var allowUsers []string
	lo.ForEach(coupons, func(coupon *Coupon, _ int) {
		allowUsers = append(allowUsers, coupon.AllowTeams...)
	})

	if users, findErr := Repository.User.FindMany(&UserQuery{IDIn: &allowUsers}, &FindManyOptions{}); findErr != nil {
		return findErr
	} else {
		mapUserByUserID := map[string]*User{}
		lo.ForEach(users, func(user *User, _ int) {
			mapUserByUserID[user.ID] = user
		})

		lo.ForEach(coupons, func(coupon *Coupon, _ int) {
			coupon.AllowUsers = lo.Map(coupon.AllowTeams, func(userID string, _ int) *SimpleProfile {
				if user, ok := mapUserByUserID[userID]; ok {
					return user.ToSimpleProfile()
				}
				return nil
			})
		})
	}
	return nil
}
