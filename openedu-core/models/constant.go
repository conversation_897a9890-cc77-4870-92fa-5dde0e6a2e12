package models

import "github.com/shopspring/decimal"

type AssetType string

type BlockchainNetwork string

var AllBlockchainNetworks = []BlockchainNetwork{
	BlockchainNetworkAVAIL,
	BlockchainNetworkNEAR,
}

type Currency string

type WalletWithdrawalType string

type PathParamKey string

type PaymentService string

type PaymentMethodType string

type RetrieveWalletDetailsType string

type Participant string

var EstimatedNEARsToMintNFT = decimal.NewFromFloat(0.015)
var EstimatedETHToMintNFT = decimal.NewFromFloat(0.0005)

var ValidInitLaunchpadPoolCurrencies = []Currency{CryptoCurrencyNEAR}

type CourseCompleteStatus string

type NumberValueType string

var NEARCryptoCurrencies = [...]Currency{CryptoCurrencyUSDT, CryptoCurrencyUSDC, CryptoCurrencyOpenEdu}
var EVMCryptoCurrencies = [...]Currency{CryptoCurrencyUSDC}

type OEReferralSource string

const (
	ViLocale = "vi"
	EnLocale = "en"

	SchemaOpenEdu = "openedu"
	SchemaVBI     = "vbi"
	SchemaAvail   = "avail"
	SchemaAIGov   = "aigov"
	AIGovDomain   = "phocap.ai"

	EmptyParentIDLesson = ""

	PathParamKeyID          = "id"
	PathParamKeyCode        = "code"
	PathParamKeyNetwork     = "network"
	PathParamKeyMilestoneID = "milestone_id"

	BlockchainNetworkNEAR  BlockchainNetwork = "near"
	BlockchainNetworkAVAIL BlockchainNetwork = "avail"
	BlockchainNetworkBASE  BlockchainNetwork = "base"

	AssetTypeFiat   AssetType = "fiat"
	AssetTypeCrypto AssetType = "crypto"
	AssetTypePoint  AssetType = "point"

	FiatCurrencyUSD Currency = "USD"
	FiatCurrencyVND Currency = "VND"

	CryptoCurrencyNEAR    Currency = "NEAR"
	CryptoCurrencyUSDT    Currency = "USDT"
	CryptoCurrencyUSDC    Currency = "USDC"
	CryptoCurrencyOpenEdu Currency = "OPENEDU"
	CryptoCurrencyAVAIL   Currency = "AVAIL"
	CryptoCurrencyETH     Currency = "ETH"

	PointCurrencyP Currency = "P"

	DefaultSystemCurrency = PointCurrencyP
	DefaultFiatCurrency   = FiatCurrencyVND

	UserCachePrefix               = "user_"
	PermissionCachePrefix         = "permission_"
	SystemConfigCachePrefix       = "system_config_"
	OrganizationCachePrefix       = "organization_config_"
	CategoryCachePrefix           = "category_config_"
	UserRoleCachePrefix           = "user_role_"
	FormCachePrefix               = "form_"
	FormSessionCachePrefix        = "form-session_"
	PageAccessCachePrefix         = "page_access_"
	WalletCachePrefix             = "wallet_"
	QuizCachePrefix               = "quiz_"
	QuizAnswerCachePrefix         = "quiz-answer_"
	QuizSubmissionCachePrefix     = "quiz-submission_"
	QuizRelationCachePrefix       = "quiz-relation_"
	BlogCachePrefix               = "blog_"
	CourseEnrollmentCachePrefix   = "enrollment_"
	ExchangeRateCachePrefix       = "exchange_rate_"
	TransactionCachePrefix        = "transaction_"
	WalletEarningCachePrefix      = "wallet_earning_"
	PricingPlanCachePrefix        = "pricing_plan_"
	FeaturedContentCachePrefix    = "featured_content_"
	CourseCachePrefix             = "course_"
	OEReferralCodeCachePrefix     = "oe_referral_code_"
	OEReferralCampaignCachePrefix = "oe_referral_campaign_"
	PublishCourseCachePrefix      = "publish_course_"
	LessonContentCachePrefix      = "lesson_content_"
	SubscriptionCachePrefix       = "subscription_"
	CoursePropsCachePrefix        = "course_props_"

	NftCertificateDefaultTitle       = "OpenEdu Certificate"
	NftCertificateDefaultDescription = "This certificate is awarded for outstanding achievement in the OpenEdu program"

	AvailAirDropDistributionSheetName = "Retroactive Distribution"

	PaymentServiceSepay  PaymentService = "sepay"
	PaymentServiceCrypto PaymentService = "crypto"

	PaymentMethodTypeBankTransfer  PaymentMethodType = "bank_transfer"
	PaymentMethodTypeOpenEduWallet PaymentMethodType = "openedu_wallet"

	RetrieveWalletDetailsTypeEarnings RetrieveWalletDetailsType = "earnings"

	Platform    Participant = "platform"
	Creator     Participant = "creator"
	Learner     Participant = "learner"
	CourseOwner Participant = "course_owner"
	Paymaster   Participant = "paymaster"

	BackupPrefix  = "backup_"
	CurrentPrefix = "current_"

	ConfigPathPrefix = "configs"

	Completed  CourseCompleteStatus = "completed"
	InProgress CourseCompleteStatus = "in-progress"

	FixedValue      NumberValueType = "fixed"
	PercentageValue NumberValueType = "percentage"

	OERefSourceNone    = "none"
	OERefSourceForm    = "form"
	OERefSourceRefLink = "ref-link"
)

var BlockchainNetworks = []BlockchainNetwork{
	BlockchainNetworkNEAR, BlockchainNetworkAVAIL, BlockchainNetworkBASE,
}

type TableName string

const (
	UserTbl                   TableName = "users"
	SystemConfigTbl           TableName = "system_configs"
	SnsAccountTbl             TableName = "sns_accounts"
	SessionTbl                TableName = "sessions"
	RoleTbl                   TableName = "roles"
	UserRoleOrgTbl            TableName = "user_role_orgs"
	PermissionTbl             TableName = "permissions"
	OrganizationTbl           TableName = "organizations"
	FileTbl                   TableName = "files"
	FileRelationTbl           TableName = "file_relations"
	CourseTbl                 TableName = "courses"
	CoursePartnerTbl          TableName = "course_partners"
	CoursePriceTbl            TableName = "course_prices"
	UserTokenTbl              TableName = "user_tokens"
	SentEmailTbl              TableName = "sent_emails"
	EmailTemplateTbl          TableName = "email_templates"
	PaymentTbl                TableName = "payments"
	PaymentMethodTbl          TableName = "payment_methods"
	OrderTbl                  TableName = "orders"
	SectionTbl                TableName = "sections"
	LessonContentTbl          TableName = "lesson_contents"
	OrderItemTbl              TableName = "order_items"
	CategoryTbl               TableName = "categories"
	CategoryRelationTbl       TableName = "category_relations"
	HashtagTbl                TableName = "hashtags"
	HashtagRelationTbl        TableName = "hashtag_relations"
	CouponTbl                 TableName = "coupons"
	CouponHistoryTbl          TableName = "coupon_histories"
	TeamTbl                   TableName = "teams"
	FormTbl                   TableName = "forms"
	FormQuestionTbl           TableName = "form_questions"
	FormQuestionOptionTbl     TableName = "form_question_options"
	FormAnswerTbl             TableName = "form_answers"
	FormSessionTbl            TableName = "form_sessions"
	FormRelationTbl           TableName = "form_relations"
	FormAnswerStatsTbl        TableName = "form_answer_stats"
	BlogTbl                   TableName = "blogs"
	ApprovalTbl               TableName = "approvals"
	PublishCourseTbl          TableName = "publish_courses"
	PublishBlogTbl            TableName = "publish_blogs"
	UserActionTbl             TableName = "user_actions"
	PageConfigTbl             TableName = "page_configs"
	PageAccessTbl             TableName = "page_accesses"
	WalletTbl                 TableName = "wallets"
	TransactionTbl            TableName = "transactions"
	CourseEnrollmentTbl       TableName = "course_enrollments"
	QuizTbl                   TableName = "quizzes"
	QuizRelationTbl           TableName = "quiz_relations"
	QuizQuestionTbl           TableName = "quiz_questions"
	QuizSubmissionTbl         TableName = "quiz_submissions"
	QuizAnswerTbl             TableName = "quiz_answers"
	AffiliateCampaignTbl      TableName = "affiliate_campaigns"
	CommissionTbl             TableName = "commissions"
	CourseCampaignTbl         TableName = "course_campaigns"
	ReferralTbl               TableName = "referrals"
	ReferralLinkTbl           TableName = "referral_links"
	ReferrerTbl               TableName = "referrers"
	LearningProgressTbl       TableName = "learning_progresses"
	BookmarkTbl               TableName = "bookmarks"
	CertificateTbl            TableName = "certificates"
	HtmlTemplateTbl           TableName = "html_templates"
	UserSettingTbl            TableName = "user_settings"
	AIBlogRewriteTbl          TableName = "ai_blog_rewrites"
	UserSummaryTbl            TableName = "user_summaries"
	AICourseTbl               TableName = "ai_courses"
	AIHistoryTbl              TableName = "ai_histories"
	PricingPlanTbl            TableName = "pricing_plans"
	SubscriptionTbl           TableName = "subscriptions"
	ResourceUsageTbl          TableName = "resource_usages"
	AIModelTbl                TableName = "ai_models"
	ReminderTbl               TableName = "reminders"
	FeaturedContentTbl        TableName = "featured_contents"
	ClpLaunchpadTbl           TableName = "clp_launchpads"
	ClpVotingMilestoneTbl     TableName = "clp_voting_milestones"
	ClpCourseLaunchpadTbl     TableName = "clp_course_launchpads"
	ClpInvestmentTbl          TableName = "clp_investments"
	ClpVotingPhaseTbl         TableName = "clp_voting_phases"
	ClpVotingLaunchpadTbl     TableName = "clp_voting_launchpads"
	ReferralHistoryTbl        TableName = "referral_histories"
	ReferralProgramSettingTbl TableName = "referral_program_settings"
	CourseRevenuePointTbl     TableName = "course_revenue_points"
	OEPointHistoryTbl         TableName = "oe_point_histories"
	OEPointCampaignTbl        TableName = "oe_point_campaigns"
	OEReferralTbl             TableName = "oe_referrals"
	OEReferralCodeTbl         TableName = "oe_referral_codes"
	LearningStatusTbl         TableName = "learning_statuses"
	AIPromptTbl               TableName = "ai_prompts"
	OEReferralLeaderBoardTbl  TableName = "oe_referral_leader_boards"
	OECampaignAccountTbl      TableName = "oe_campaign_accounts"
	OEReferralReportTbl       TableName = "oe_referral_reports"
	ScheduleTbl               TableName = "schedules"
	EventScheduleTbl          TableName = "event_schedules"
)

var SharedTables = [...]TableName{
	UserTbl, RoleTbl, UserRoleOrgTbl,
	SystemConfigTbl, SnsAccountTbl, PermissionTbl,
	OrganizationTbl, SessionTbl, FileTbl, FileRelationTbl,
	UserTokenTbl, SentEmailTbl, EmailTemplateTbl,
	CategoryTbl, CategoryRelationTbl, HashtagTbl, HashtagRelationTbl,
	TeamTbl, CouponHistoryTbl, CouponTbl,
	FormTbl, FormQuestionTbl, FormQuestionOptionTbl, FormAnswerTbl, FormSessionTbl, FormAnswerStatsTbl, FormRelationTbl,
	ApprovalTbl, PublishCourseTbl, UserActionTbl, PageConfigTbl, PageAccessTbl,
	WalletTbl, TransactionTbl, PaymentMethodTbl, PaymentTbl, OrderTbl, OrderItemTbl, PaymentMethodTbl,
	AffiliateCampaignTbl, CommissionTbl, CourseCampaignTbl, ReferralTbl, ReferralLinkTbl, ReferrerTbl, LearningProgressTbl, BookmarkTbl, CertificateTbl, HtmlTemplateTbl,
	UserSettingTbl, CourseEnrollmentTbl, PublishBlogTbl,
	AIBlogRewriteTbl, UserSummaryTbl, AICourseTbl, AIHistoryTbl,
	PricingPlanTbl, SubscriptionTbl, ResourceUsageTbl,
	CourseTbl, SectionTbl, LessonContentTbl, CoursePartnerTbl, CoursePriceTbl,
	QuizTbl, QuizRelationTbl, QuizQuestionTbl, QuizSubmissionTbl, QuizAnswerTbl,
	BlogTbl, AIModelTbl, OEPointHistoryTbl, OEPointCampaignTbl, ReminderTbl,
	FeaturedContentTbl, ClpLaunchpadTbl, ClpVotingMilestoneTbl, ClpCourseLaunchpadTbl,
	ClpInvestmentTbl, ClpVotingPhaseTbl, ClpVotingLaunchpadTbl,
	CourseRevenuePointTbl, AIPromptTbl, LearningStatusTbl,
	ReferralHistoryTbl, ReferralProgramSettingTbl,
	OEReferralTbl, OEReferralCodeTbl, OEReferralLeaderBoardTbl, OECampaignAccountTbl, OEReferralReportTbl, ScheduleTbl, EventScheduleTbl,
}

var OrganizationTables = [...]TableName{}

type ModelName string

const (
	UserModelName               ModelName = "user"
	OrganizationModelName       ModelName = "organization"
	CourseModelName             ModelName = "course"
	SectionModelName            ModelName = "section"
	LessonModelName             ModelName = "lesson"
	FormAnswerModelName         ModelName = "form_answer"
	BlogModelName               ModelName = "blog"
	QuizModelName               ModelName = "quiz"
	QuizQuestionModelName       ModelName = "quiz_question"
	QuizQuestionItemModelName   ModelName = "quiz_question_item"
	CertificateModelName        ModelName = "certificate"
	HTMLTemplateModelName       ModelName = "html_template"
	ReferrerModelName           ModelName = "referrer"
	ReferralModelName           ModelName = "referral"
	WalletModelName             ModelName = "wallet"
	ApprovalModelName           ModelName = "approval"
	OrderModelName              ModelName = "order"
	TransactionModelName        ModelName = "transaction"
	SystemConfigModelName       ModelName = "system_config"
	FileModelName               ModelName = "file"
	ClpLaunchpadModelName       ModelName = "clp_launchpad"
	ClpVotingMilestoneModelName ModelName = "clp_voting_milestone"
	ClpCourseLaunchpadModelName ModelName = "clp_course_launchpad"
	ClpVotingPhaseModelName     ModelName = "clp_voting_phase"
	ClpVotingLaunchpadModelName ModelName = "clp_voting_launchpad"
	ClpInvestmentModelName      ModelName = "clp_investment"
	AIPromptModelName           ModelName = "ai_prompt"
)

var ModelNames = []ModelName{
	UserModelName,
	OrganizationModelName,
	CourseModelName,
	LessonModelName,
	FormAnswerModelName,
	BlogModelName,
	QuizModelName,
	QuizQuestionModelName,
	QuizQuestionItemModelName,
	CertificateModelName,
	HTMLTemplateModelName,
	ReferrerModelName,
	ReferralModelName,
	WalletModelName,
	ApprovalModelName,
	OrderModelName,
}

// Field name in string
const (
	MediasField                   = "Medias"
	DocsField                     = "Docs"
	FileField                     = "File"
	PartnerField                  = "Partner"
	PriceSettingsField            = "PriceSettings"
	PartnersField                 = "Partners"
	FilesField                    = "Files"
	ImageField                    = "Image"
	CategoriesField               = "Categories"
	LevelsField                   = "Levels"
	CategoryField                 = "Category"
	ReviewingField                = "Reviewing"
	PublishedField                = "Published"
	LessonField                   = "lessons"
	CourseField                   = "Course"
	CoursesField                  = "Courses"
	UserField                     = "User"
	FormField                     = "Form"
	FormRelationsField            = "FormRelations"
	AnswersField                  = "Answers"
	AnswersOptionField            = "Answers.Option"
	AnswersQuestionField          = "Answers.Question"
	AnswersQuestionOptionsField   = "Answers.Question.Options"
	CampaignField                 = "Campaign"
	CommissionField               = "Commission"
	CouponField                   = "Coupon"
	ConfirmByField                = "ConfirmBy"
	RequesterField                = "Requester"
	OrgField                      = "Org"
	EntityField                   = "Entity"
	UserCommissionField           = "UserCommission"
	LearningProgressOverviewField = "LearningProgressOverview"
	HashTagField                  = "Hashtag"
	QuestionsFormRelationField    = "Form.Questions"
	OwnerField                    = "Owner"
	FollowingField                = "following"
	FollowersField                = "followers"
	TotalBlogsField               = "total_blogs"
	TotalCoursesField             = "total_courses"
	SummaryField                  = "Summary"
	TotalUserEnrollmentsField     = "total_user_enrollments"
	AICourseField                 = "AICourse"
	PricingPlanField              = "Plan"
	CertificateTemplateField      = "certificate_template"
	ClpLaunchpadField             = "ClpLaunchpad"
	VotingMilestonesField         = "VotingMilestones"
	OwnerProfileField             = "OwnerProfile"
	OutlineField                  = "Outline"
	InvestmentField               = "Investment"
	ThumbnailField                = "Thumbnail"
	BannerField                   = "Banner"
	PaymentField                  = "Payment"
	OrderItemsField               = "OrderItems"
	ReferrerField                 = "Referrer"
	ScheduleEventField            = "EventSchedule"
)

const (
	IDJoinKey      = "ID"
	UserIDJoinKey  = "UserID"
	ActorIDJoinKey = "ActorID"
	TextKey        = "Text"
	ReferralKey    = "Referral_Info"
)

// Sorting options
const (
	IdDESC            = "id desc"
	UserIDASC         = "user_id asc"
	UserIDDESC        = "user_id desc"
	LastAccessDESC    = "last_access desc"
	CreateAtASC       = "create_at asc"
	CreateAtDESC      = "create_at desc"
	UpdateAtASC       = "update_at asc"
	UpdateAtDESC      = "update_at desc"
	LikeCountDESC     = "like_count desc"
	OrderASC          = `"order" asc`
	TextAsc           = "text asc"
	TargetFundingASC  = "target_funding asc"
	TargetFundingDESC = "target_funding desc"
)

const (
	String       string = "string"
	Int          string = "int"
	TimeDuration string = "time_duration"
	JsonB        string = "jsonb"
	JsonArr      string = "json_array"
)

type DefaultStatus string

const (
	DefaultStatusPending  DefaultStatus = "pending"
	DefaultStatusAccepted DefaultStatus = "accepted"
	DefaultStatusExisted  DefaultStatus = "existed"
)

// Blog

const (
	BlogPreloadReviewing   string = "Reviewing"
	BlogPreloadPublished   string = "Published"
	BlogPreloadUnPublished string = "UnPublished"
	BlogPreloadsCategories string = "Categories"
	BlogPreloadsHashTag    string = "HashTags"
	BlogPreloadsUser       string = "User"
	BlogPreloadsOrg        string = "Organization"
	BlogPreloadAIInfo      string = "AIInfo"
)

type BlogStatus string

const (
	BlogStatusPending        BlogStatus = "pending"
	BlogStatusDraft          BlogStatus = "draft"
	BlogStatusPublish        BlogStatus = "publish"
	BlogStatusReviewing      BlogStatus = "reviewing"
	BlogStatusRejected       BlogStatus = "rejected"
	BlogStatusUnPublish      BlogStatus = "unpublish"
	BlogStatusGenerating     BlogStatus = "generating"
	BlogStatusGenerateFailed BlogStatus = "failed"
)

type BlogType string

const (
	BlogTypePersonal BlogType = "personal"
	BlogTypeOrg      BlogType = "org"
)

var CreateBlogType = []BlogType{
	BlogTypePersonal,
	BlogTypeOrg,
}

type AIStatus string

const (
	AIStatusManual     AIStatus = "manual"
	AIStatusPending    AIStatus = "pending"
	AIStatusGenerating AIStatus = "generating"
	AIStatusWaiting    AIStatus = "waiting"
	AIStatusCompleted  AIStatus = "completed"
	AIStatusFailed     AIStatus = "failed"
)

type AITone string

const (
	ToneProfessional AITone = "professional"
	ToneHumorous     AITone = "humorous"
	ToneNormal       AITone = "normal"
)

type AIThumbnailStyle string

const (
	GeneralThumbnailStyle      AIThumbnailStyle = "general"
	AnimeThumbnailStyle        AIThumbnailStyle = "anime"
	CreativeThumbnailStyle     AIThumbnailStyle = "creative"
	DynamicThumbnailStyle      AIThumbnailStyle = "dynamic"
	EnvironmentThumbnailStyle  AIThumbnailStyle = "environment"
	IllustrationThumbnailStyle AIThumbnailStyle = "illustration"
	PhotographyThumbnailStyle  AIThumbnailStyle = "photography"
	RayTrace3DThumbnailStyle   AIThumbnailStyle = "ray_trace_3d"
	Render3DThumbnailStyle     AIThumbnailStyle = "render_3d"
	SketchBWThumbnailStyle     AIThumbnailStyle = "sketch_bw"
	SketchColorThumbnailStyle  AIThumbnailStyle = "sketch_color"
)

type AIOfferType string

const (
	AICourseYoutubePlaylistType    AIOfferType = "youtube_playlist"
	AICourseLearnerDescriptionType AIOfferType = "learner_description"
	AIBlogGenerateBlog             AIOfferType = "generate_blog"
	AIBlogRewriteParagraph         AIOfferType = "rewrite_paragraph"
	AIBlogRewriteFromLink          AIOfferType = "rewrite_from_link"
)

type AIGenerateStep string

const (
	AICourseYoutubePlaylistGenerateStep    AIGenerateStep = "youtube_playlist_generate"
	AICourseQuizGenerateStep               AIGenerateStep = "quiz_generate"
	AICourseLearnerDescriptionGenerateStep AIGenerateStep = "learner_description_generate"
	AICourseThumbnailGenerateStep          AIGenerateStep = "thumbnail_generate"
	AICourseOutlineGenerateStep            AIGenerateStep = "outline_generate"
	AIBlogGenerateBlogStep                 AIGenerateStep = "generate_blog_generate"
	AIBlogRewriteParagraphStep             AIGenerateStep = "rewrite_paragraph_generate"
	AIBlogRewriteFromLinkStep              AIGenerateStep = "rewrite_from_link_generate"
)

type TimePeriod string

const (
	TimePeriodDay     TimePeriod = "seven_day"
	TimePeriodWeek    TimePeriod = "two_week"
	TimePeriodMonth   TimePeriod = "one_month"
	TimePeriodYear    TimePeriod = "one_year"
	TimePeriodUnLimit TimePeriod = "un_limit"
	TimePeriodCustom  TimePeriod = "custom"
)
