package models

import (
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CoursePrice struct {
	CourseID string `json:"course_id" dbscan:"course_id" gorm:"primaryKey;type:varchar(20)"`

	IsPay bool `json:"is_pay" dbscan:"is_pay" gorm:"default:false"`

	FiatCurrency      Currency        `json:"fiat_currency" dbscan:"fiat_currency"`
	FiatPrice         decimal.Decimal `json:"fiat_price" dbscan:"fiat_price" gorm:"type:numeric(19,4);not null;default:0"`
	FiatDiscountPrice decimal.Decimal `json:"fiat_discount_price" dbscan:"fiat_discount_price" gorm:"type:numeric(19,4);not null;default:0"`

	CryptoPaymentEnabled bool            `json:"crypto_payment_enabled" dbscan:"crypto_payment_enabled"`
	CryptoCurrency       Currency        `json:"crypto_currency" dbscan:"crypto_currency"`
	CryptoPrice          decimal.Decimal `json:"crypto_price" dbscan:"crypto_price" gorm:"type:numeric(19,4);not null;default:0"`
	CryptoDiscountPrice  decimal.Decimal `json:"crypto_discount_price" dbscan:"crypto_discount_price" gorm:"type:numeric(19,4);not null;default:0"`
}

type CoursePriceQuery struct {
	CourseID   *string
	CourseIDIn []string
}

func (query *CoursePriceQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.CourseID != nil {
		qb = qb.Where("course_id = ?", *query.CourseID)
	}

	if len(query.CourseIDIn) > 0 {
		qb = qb.Where("course_id IN (?)", query.CourseIDIn)
	}

	return qb
}

func (r *CoursePriceRepository) Create(p *CoursePrice, trans *gorm.DB) error {
	return create(CoursePriceTbl, p, trans)
}

func (r *CoursePriceRepository) CreateMany(ps []*CoursePrice, trans *gorm.DB) error {
	return createMany(CoursePriceTbl, ps, trans)
}

func (r *CoursePriceRepository) Update(p *CoursePrice, trans *gorm.DB) error {
	return update(CoursePriceTbl, p, trans)
}

func (r *CoursePriceRepository) Upsert(p *CoursePrice, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(CoursePriceTbl)).Debug().Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "course_id"},
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"is_pay",
			"fiat_currency",
			"fiat_price",
			"fiat_discount_price",
			"crypto_payment_enabled",
			"crypto_currency",
			"crypto_price",
			"crypto_discount_price",
		}),
	}).Create(&p).Error
	return
}

func (r *CoursePriceRepository) FindOne(query *CoursePriceQuery, options *FindOneOptions) (*CoursePrice, error) {
	return findOne[CoursePrice](CoursePriceTbl, query, options)
}

func (r *CoursePriceRepository) FindMany(query *CoursePriceQuery, options *FindManyOptions) ([]*CoursePrice, error) {
	return findMany[CoursePrice](CoursePriceTbl, query, options)
}

func (r *CoursePriceRepository) FindPage(query *CoursePriceQuery, options *FindPageOptions) ([]*CoursePrice, *Pagination, error) {
	return findPage[CoursePrice](CoursePriceTbl, query, options)
}

func (r *CoursePriceRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[CoursePrice](CoursePriceTbl, id, trans)
}

func (r *CoursePriceRepository) DeleteMany(query *CoursePriceQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[CoursePrice](CoursePriceTbl, query, trans)
}

func (r *CoursePriceRepository) Count(query *CoursePriceQuery) (int64, error) {
	return count[CoursePrice](CoursePriceTbl, query)
}
