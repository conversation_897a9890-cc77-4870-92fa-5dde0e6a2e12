package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Session struct {
	Model
	OrgId       string `json:"org_id" gorm:"not null;type:varchar(20)"`
	Url         string `json:"url" gorm:"not null;type:varchar(256)"`
	UserId      string `json:"user_id" gorm:"not null;type:varchar(20)"`
	Token       string `json:"token"  gorm:"type:varchar(1000)"`
	UserAgent   string `json:"user_agent" gorm:"type:varchar(256)"`
	ExpireAt    int64  `json:"expire_at" gorm:"type:int8"`
	Props       JSONB  `json:"props" gorm:"type:jsonb"`
	OTPVerified bool   `json:"otp_verified"`
}

type SimpleSession struct {
	Model
	OrgId     string `json:"org_id" gorm:"not null;type:varchar(20)"`
	Url       string `json:"url" gorm:"not null;type:varchar(256)"`
	UserId    string `json:"user_id" gorm:"not null;type:varchar(20)"`
	UserAgent string `json:"user_agent" gorm:"type:varchar(256)"`
	ExpireAt  int64  `json:"expire_at" gorm:"type:int8"`
}

func (s *Session) Sanitize() *SimpleSession {
	return &SimpleSession{
		Model:     s.Model,
		OrgId:     s.OrgId,
		Url:       s.Url,
		UserId:    s.UserId,
		ExpireAt:  s.ExpireAt,
		UserAgent: s.UserAgent,
	}
}

type SessionQuery struct {
	OrgID     *string
	UserID    *string
	Token     *string
	UserAgent *string
}

func (query *SessionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}
	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}
	if query.Token != nil {
		qb = qb.Where("token = ?", *query.Token)
	}
	if query.UserAgent != nil {
		qb = qb.Where("user_agent = ?", *query.UserAgent)
	}

	return qb
}

// Create insert session data to database
func (r *SessionRepository) Create(session *Session) error {
	result := GetDb(SessionTbl).Create(&session)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// Update update session data to database
func (r *SessionRepository) Update(session *Session) (*Session, error) {
	result := GetDb(SessionTbl).Updates(&session)
	if result.Error != nil {
		return nil, result.Error
	}

	return session, nil
}

func (r *SessionRepository) FindById(id string) (*Session, error) {
	var session Session
	result := GetDb(SessionTbl).First(&session, "id = ? and delete_at = 0", id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &session, nil
}

func (r *SessionRepository) FindByToken(token string) (*Session, error) {
	var session Session
	result := GetDb(SessionTbl).First(&session, "token = ? and delete_at = 0", token)
	if result.Error != nil {
		return nil, result.Error
	}
	return &session, nil
}

func (r *SessionRepository) DeleteByUserID(userID string, transaction *gorm.DB) error {
	var tx *gorm.DB
	if transaction != nil {
		tx = transaction

	} else {
		tx = GetDb(SessionTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	now := time.Now().UnixMilli()
	result := tx.Model(&Session{}).Where("user_id = ? AND delete_at = 0", userID).Updates(map[string]interface{}{
		"delete_at": now,
		"update_at": now,
	})
	if err := result.Error; err != nil {
		return err
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	// Do not commit when use external transaction
	if transaction != nil {
		return nil
	}

	return tx.Commit().Error
}

func (r *SessionRepository) FindOne(query *SessionQuery, options *FindOneOptions) (*Session, error) {
	return findOne[Session](SessionTbl, query, options)
}

func (r *SessionRepository) Upsert(session *Session, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Table(GetTblName(SessionTbl)).Debug().Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "org_id"}, {Name: "user_agent"}},
		UpdateAll: true,
	}).Create(&session).Error
	return
}
