package models

import "gorm.io/gorm"

type OECampaignAccount struct {
	Model
	UserID     string `json:"user_id"`
	CampaignID string `json:"campaign_id"`
	ParentID   string `json:"parent_id"`
	Active     bool   `json:"active" default:"true"`

	User   *User `json:"user"`
	Parent *User `json:"parent"`
}

type OECampaignAccountQuery struct {
	ID         *string `json:"id" form:"id"`
	UserID     *string `json:"user_id" form:"user_id"`
	CampaignID *string `json:"campaign_id" form:"campaign_id"`
	ParentID   *string `json:"parent_id" form:"parent_id"`
	Active     *bool   `json:"active" form:"active"`
	Deleted    *bool   `json:"deleted" form:"deleted"`
}

func (q OECampaignAccountQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if q.ID != nil {
		qb = qb.Where("id = ?", *q.ID)
	}

	if q.UserID != nil {
		qb = qb.Where("user_id = ?", *q.UserID)
	}

	if q.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *q.CampaignID)
	}

	if q.ParentID != nil {
		qb = qb.Where("parent_id = ?", *q.ParentID)
	}

	if q.Active != nil {
		qb = qb.Where("active = ?", *q.Active)
	}

	if q.Deleted == nil || !*q.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *OECampaignAccountRepository) Create(entity *OECampaignAccount, trans *gorm.DB) error {
	return create(OECampaignAccountTbl, entity, trans)
}

func (r *OECampaignAccountRepository) CreateMany(entities []*OECampaignAccount, trans *gorm.DB) error {
	return createMany(OECampaignAccountTbl, entities, trans)
}

func (r *OECampaignAccountRepository) Update(entity *OECampaignAccount, trans *gorm.DB) error {
	return update(OECampaignAccountTbl, entity, trans)
}

func (r *OECampaignAccountRepository) FindByID(id string, options *FindOneOptions) (*OECampaignAccount, error) {
	return findByID[OECampaignAccount](OECampaignAccountTbl, id, options)
}

func (r *OECampaignAccountRepository) FindOne(query *OECampaignAccountQuery, options *FindOneOptions) (*OECampaignAccount, error) {
	return findOne[OECampaignAccount](OECampaignAccountTbl, query, options)
}

func (r *OECampaignAccountRepository) FindMany(query *OECampaignAccountQuery, options *FindManyOptions) ([]*OECampaignAccount, error) {
	return findMany[OECampaignAccount](OECampaignAccountTbl, query, options)
}

func (r *OECampaignAccountRepository) FindPage(query *OECampaignAccountQuery, options *FindPageOptions) ([]*OECampaignAccount, *Pagination, error) {
	return findPage[OECampaignAccount](OECampaignAccountTbl, query, options)
}

func (r *OECampaignAccountRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OECampaignAccount](OECampaignAccountTbl, id, trans)
}

func (r *OECampaignAccountRepository) DeleteMany(query *OECampaignAccountQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[OECampaignAccount](OECampaignAccountTbl, query, trans)
}

// Count returns number of UserActions by query conditions, transaction is optional
func (r *OECampaignAccountRepository) Count(query *OECampaignAccountQuery) (int64, error) {
	return count[OECampaignAccount](OECampaignAccountTbl, query)
}
