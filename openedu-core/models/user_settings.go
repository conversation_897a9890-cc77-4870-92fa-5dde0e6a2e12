package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type UserSettingsOption struct {
	Locale                 string `json:"locale"`
	Notification           string `json:"notification,omitempty"`
	LinkedIn               string `json:"linkedin"`
	Github                 string `json:"github"`
	Website                string `json:"website"`
	Facebook               string `json:"facebook"`
	Telegram               string `json:"telegram"`
	X                      string `json:"x"`
	Gmail                  string `json:"gmail"`
	NewUserSurveyCompleted bool   `json:"new_user_survey_completed"`
	RefCode                string `json:"ref_code"`
	MyRefCode              string `json:"my_ref_code"`
	TempPassword           string `json:"temp_password"`
	FirstCompleteCourse    bool   `json:"fist_complete_course" default:"false"`
	FirstDepositFiat       bool   `json:"first_deposit_fiat" default:"false"`
	FirstDepositCrypto     bool   `json:"first_deposit_crypto" default:"false"`
	LearningStatusMigrated bool   `json:"learning_status_migrated" gorm:"default:false"`
}

func (p UserSettingsOption) Value() (driver.Value, error) {
	// Marshal the struct to JSON
	userSettingsJSON, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(userSettingsJSON), err
}

func (p *UserSettingsOption) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &p)
	if err != nil {
		return err
	}

	return nil
}
