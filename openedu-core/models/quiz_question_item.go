package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type QuizQuestionItemType string

const (
	QuizQuestionItemTypeChoice       = "choice_item"
	QuizQuestionItemTypeText         = "text_item"
	QuizQuestionItemTypeMatching     = "matching_item"
	QuizQuestionItemTypeOrdering     = "ordering_item"
	QuizQuestionItemTypeFillInBlanks = "fill_in_blanks_item"
)

type QuizQuestionItem struct {
	ID     string               `json:"id"`
	Type   QuizQuestionItemType `json:"type"`
	Text   string               `json:"text,omitempty"`
	FileID string               `json:"file_id,omitempty"`
	File   *File                `json:"file,omitempty"`
	Side   int                  `json:"side,omitempty"`
	Order  int                  `json:"order"`
}

type SimpleQuizQuestionItem struct {
	ID     string               `json:"id"`
	Type   QuizQuestionItemType `json:"type"`
	Text   string               `json:"text,omitempty"`
	FileID string               `json:"file_id,omitempty"`
	File   *SimpleFile          `json:"file,omitempty"`
	Side   int                  `json:"side,omitempty"`
	Order  int                  `json:"order,omitempty"`
}

func (i *QuizQuestionItem) DeepCopy() *QuizQuestionItem {
	clone := &QuizQuestionItem{
		ID:     i.ID,
		Type:   i.Type,
		Text:   i.Text,
		FileID: i.FileID,
		Side:   i.Side,
		Order:  i.Order,
	}
	if i.File != nil {
		clone.File = i.File.DeepCopy()
	}
	return clone
}

func (i *QuizQuestionItem) ToSimple() *SimpleQuizQuestionItem {
	simpleItem := &SimpleQuizQuestionItem{
		ID:     i.ID,
		Type:   i.Type,
		Text:   i.Text,
		FileID: i.FileID,
		Side:   i.Side,
		Order:  i.Order,
	}
	if i.File != nil {
		simpleItem.File = i.File.Sanitize()
	}
	return simpleItem
}

func (i QuizQuestionItem) Value() (driver.Value, error) {
	// Do not save FileID, because it's saved in FileRelation
	i.FileID = ""
	i.File = nil
	val, err := json.Marshal(i)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (i *QuizQuestionItem) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, i)
}

type QuizQuestionItems []*QuizQuestionItem

func (s QuizQuestionItems) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *QuizQuestionItems) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

type QuizQuestionItemSets []QuizQuestionItems

func (s QuizQuestionItemSets) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *QuizQuestionItemSets) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}
