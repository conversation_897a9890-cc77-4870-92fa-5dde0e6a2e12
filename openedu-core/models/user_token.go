package models

import (
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"openedu-core/pkg/util"
	"strings"
)

type UserToken struct {
	Model
	User        *User     `json:"user,omitempty"`
	UserID      *string   `json:"user_id,omitempty"`
	OrgID       string    `json:"org_id,omitempty"`
	Email       string    `json:"email,omitempty"`
	Token       string    `json:"token,omitempty"`
	Event       EventType `json:"event,omitempty"`
	ExpiryTime  int       `json:"expiry_time,omitempty" gorm:"type:int8;not null"`
	SendEmail   int       `json:"send_email,omitempty" gorm:"type:int8;not null"`
	VerifyDate  int       `json:"verify_date" gorm:"type:int8;default:0" `
	RedirectUrl string    `json:"redirect_url,omitempty" gorm:"redirect_url"`
	Props       JSONB     `json:"props" gorm:"type:jsonb"`
	OTP         string    `json:"otp"`
	OTPExpireAt int       `json:"otp_expire_at"`
}

type UserTokenQuery struct {
	ID         *string      `form:"id"`
	IDIn       *[]string    `form:"id_in"`
	UserID     *string      `form:"user_id"`
	OrgID      *string      `form:"org_id"`
	Email      *string      `form:"email"`
	EmailIn    *[]string    `form:"email_in"`
	Token      *string      `form:"token"`
	EventIn    *[]EventType `json:"event_in,omitempty" gorm:"event"`
	Event      *EventType   `form:"event"`
	ExpiryTime *int         `form:"expiry_time"`
	SendEmail  *int         `form:"send_email"`
	VerifyDate *int         `form:"verify_date"`
	//User             *User        `form:"user"`
	SearchTerm       *string `json:"search_term" form:"search_term"`
	SearchCategories *string `json:"search_categories" form:"search_categories"`
	IsVerified       *bool   `json:"is_verified" form:"is_verified"`
	OTP              *string `form:"otp"`
}

func (ut *UserToken) IsExpired() bool {
	return ut.ExpiryTime < util.GetCurrentTime()
}

func (ut *UserToken) IsVerified() bool {
	return ut.VerifyDate > 0
}

func (ut *UserToken) GenerateEmailToken() string {
	// SentEmail send link will contain this format {"email": "<EMAIL>", "token": "abcXyz"}
	// base64 encode above format
	emailToken := EmailToken{
		Email: ut.Email,
		Token: ut.Token,
	}
	jsonData, _ := json.Marshal(emailToken)
	base64TokenEmail := util.Base64Encode(string(jsonData))
	return base64TokenEmail
}

func (query *UserTokenQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}
	if query.IDIn != nil {
		qb = qb.Where("id IN (?)", *query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Email != nil {
		qb = qb.Where("email = ?", *query.Email)
	}

	if query.EmailIn != nil {
		qb = qb.Where("email IN (?)", *query.EmailIn)
	}

	if query.Token != nil {
		qb = qb.Where("token = ?", *query.Token)
	}

	if query.Event != nil {
		qb = qb.Where("event = ?", *query.Event)
	}

	if query.EventIn != nil {
		qb = qb.Where("event IN (?)", *query.EventIn)
	}

	if query.ExpiryTime != nil {
		qb = qb.Where("expiry_time = ?", *query.ExpiryTime)
	}

	if query.SendEmail != nil {
		qb = qb.Where("send_email = ?", *query.SendEmail)
	}

	if query.VerifyDate != nil {
		qb = qb.Where("verify_date = ?", *query.VerifyDate)
	}

	if query.OTP != nil {
		qb = qb.Where("otp = ?", *query.OTP)
	}

	if query.IsVerified != nil {
		if *query.IsVerified { // verified must be not null and greater than 0
			qb = qb.Where("verify_date > 0")
		} else { // non-verified must be null or 0
			qb = qb.Where("verify_date = 0")
		}
	}

	searchStr := ""
	searchParams := []any{}
	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		validCates, err := VerifySearchCategory(UserToken{}, []string{}, categories)
		if err != nil {
			return qb
		}
		for idx, category := range validCates {
			if idx == 0 {
				searchStr += fmt.Sprintf("%s ~ ?", category)
			} else {
				searchStr += fmt.Sprintf(" OR %s ~* ?", category)
			}
			searchParams = append(searchParams, *query.SearchTerm)
		}
		qb = qb.Where(searchStr, searchParams...)

	}

	return qb
}

// Create inserts a userToken to database, transaction is optional
func (r *UserTokenRepository) Create(tk *UserToken, trans *gorm.DB) error {
	return create(UserTokenTbl, tk, trans)
}

func (r *UserTokenRepository) CreateMany(tks []*UserToken, trans *gorm.DB) error {
	return createMany(UserTokenTbl, tks, trans)
}

// Update updates a userToken by ID in database, transaction is optional
func (r *UserTokenRepository) Update(tk *UserToken, trans *gorm.DB) error {
	return update(UserTokenTbl, tk, trans)
}

// FindByID finds a userToken by ID with given find options, transaction is optional
func (r *UserTokenRepository) FindByID(id string, options *FindOneOptions) (*UserToken, error) {
	return findByID[UserToken](UserTokenTbl, id, options)
}

// FindOne finds one userToken with given find queries and options, transaction is optional
func (r *UserTokenRepository) FindOne(query *UserTokenQuery, options *FindOneOptions) (*UserToken, error) {
	return findOne[UserToken](UserTokenTbl, query, options)
}

// FindMany finds userTokens by query conditions with give find options
func (r *UserTokenRepository) FindMany(query *UserTokenQuery, options *FindManyOptions) ([]*UserToken, error) {
	return findMany[UserToken](UserTokenTbl, query, options)
}

// FindPage returns userTokens and pagination by query conditions and find options, transaction is optional
func (r *UserTokenRepository) FindPage(query *UserTokenQuery, options *FindPageOptions) ([]*UserToken, *Pagination, error) {
	return findPage[UserToken](UserTokenTbl, query, options)
}

// Delete perform soft deletion to a userTokens by ID, transaction is optional
func (r *UserTokenRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[UserToken](UserTokenTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *UserTokenRepository) DeleteMany(query *UserTokenQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[UserToken](UserTokenTbl, query, trans)
}

// Count returns number of userTokens by query conditions, transaction is optional
func (r *UserTokenRepository) Count(query *UserTokenQuery) (int64, error) {
	return count[UserToken](UserTokenTbl, query)
}
