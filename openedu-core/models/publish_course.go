package models

import (
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"

	"github.com/samber/lo"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type UnPublishCourseTarget string

const (
	UnPublishCourseTargetOrg  = "org"
	UnPublishCourseTargetRoot = "root"
	UnPublishCourseTargetAll  = "all"
)

type PublishScope string

const (
	ScopeAll        PublishScope = "all"
	ScopeInvestment PublishScope = "investment"
)

type PublishCourse struct {
	OrgID      string `json:"org_id" gorm:"not null;type:varchar(20)"`
	OrgSchema  string `json:"org_schema"`
	OrgDomain  string `json:"org_domain"`
	UserID     string `json:"user_id"`
	CourseCuid string `json:"course_cuid" gorm:"not null;type:varchar(20)"`
	CourseSlug string `json:"course_slug"`

	Name        string `json:"name"`
	Description string `json:"description"`
	ShortDesc   string `json:"short_desc"`
	ThumbnailID string `json:"thumbnail_id"`
	Version     int    `json:"version" gorm:"default:1"`

	IsPay       bool            `json:"is_pay" gorm:"default:false"`
	Price       decimal.Decimal `json:"price" gorm:"type:numeric(19,4);not null;default:0"`
	CryptoPrice decimal.Decimal `json:"crypto_price" gorm:"type:numeric(19,4);not null;default:0"`

	CourseID        string `json:"course_id" gorm:"not null;type:varchar(20)"`
	PubDate         int    `json:"pub_date" gorm:"type:int8;default:0"`
	Enable          bool   `json:"enable" gorm:"default:true"`
	PubRootDate     int    `json:"pub_root_date" gorm:"type:int8;default:0"`
	EnableRoot      bool   `json:"enable_root" gorm:"default:false"`
	IsRoot          bool   `json:"is_root" gorm:"default:false"`
	StartDate       int    `json:"start_date" gorm:"type:int8;default:0"`
	EndDate         int    `json:"end_date" gorm:"type:int8;default:0"`
	MarkAsCompleted bool   `json:"mark_as_completed" gorm:"default:false"`

	Scope PublishScope `json:"scope"`

	Props JSONB `json:"props" gorm:"type:jsonb"`

	User                *SimpleProfile `json:"user" gorm:"-"`
	Thumbnail           *File          `json:"thumbnail" gorm:"-"`
	Categories          []*Category    `json:"categories" gorm:"-"`
	TotalUserEnrollment int64          `json:"total_user_enrollment" gorm:"-"`

	Model
}

type SimplePublishCourse struct {
	Model
	CourseSlug      string          `json:"course_slug"`
	CourseCuid      string          `json:"course_cuid"`
	CourseID        string          `json:"course_id"`
	OrgID           string          `json:"org_id"`
	Name            string          `json:"name"`
	IsPay           bool            `json:"is_pay"`
	Price           decimal.Decimal `json:"price"`
	Description     string          `json:"description"`
	Version         int             `json:"version"`
	MarkAsCompleted bool            `json:"mark_as_completed"`
	Scope           PublishScope    `json:"scope"`

	User       *SimpleProfile `json:"user" gorm:"-"`
	Thumbnail  *File          `json:"thumbnail" gorm:"-"`
	Categories []*Category    `json:"categories" gorm:"-"`

	TotalUserEnrollment int64 `json:"total_user_enrollment" gorm:"-"`
}

func (p *PublishCourse) ToSimplePublishCourse() *SimplePublishCourse {
	return &SimplePublishCourse{
		Model:               p.Model,
		CourseSlug:          p.CourseSlug,
		CourseCuid:          p.CourseCuid,
		CourseID:            p.CourseID,
		OrgID:               p.OrgID,
		Name:                p.Name,
		IsPay:               p.IsPay,
		Price:               p.Price,
		Description:         p.Description,
		Thumbnail:           p.Thumbnail,
		Version:             p.Version,
		MarkAsCompleted:     p.MarkAsCompleted,
		User:                p.User,
		Categories:          p.Categories,
		TotalUserEnrollment: p.TotalUserEnrollment,
		Scope:               p.Scope,
	}
}

type PublishCourseQuery struct {
	OrgID        *string       `json:"org_id" form:"org_id"`
	OrgIDIn      []string      `json:"org_id_in" form:"org_id_in"`
	OrgIDNot     *string       `json:"org_id_not" form:"org_id_not"`
	CourseID     *string       `json:"course_id" form:"course_id"`
	CourseCuid   *string       `json:"course_cuid" form:"course_cuid"`
	CourseCuidIn []string      `json:"course_cuid_in" form:"course_cuid_in"`
	UserID       *string       `json:"user_id" form:"user_id"`
	Name         *string       `json:"name" form:"name"`
	IsRoot       *bool         `json:"is_root" form:"is_root"`
	Enable       *bool         `json:"enable" form:"enable"`
	EnableRoot   *bool         `json:"enable_root" form:"enable_root"`
	EnableAll    *bool         `json:"enable_all" form:"enable_all"`
	Scope        *PublishScope `json:"scope"`

	IncludeDeleted *bool
	CourseSlug     *string `json:"course_slug" form:"course_slug"`
	GteStartDate   *int    `json:"gte_start_date" form:"gte_start_date"`
	LteEndDate     *int    `json:"lte_end_date" form:"lte_end_date"`

	MarkAsCompleted  *bool            `json:"mark_as_completed" form:"mark_as_completed"`
	CompleteStatusIn []string         `json:"complete_status_in" form:"complete_status_in"`
	IsPub            *bool            `json:"is_pub" form:"is_pub"`
	IsPay            *bool            `json:"is_pay" form:"is_pay"`
	MinPrice         *decimal.Decimal `json:"min_price" form:"min_price"`
	MaxPrice         *decimal.Decimal `json:"max_price" form:"max_price"`

	BookmarkedBy *string `json:"bookmarked_by" form:"bookmarked_by"`

	CategoryIDIn []string `json:"category_id_in" form:"category_id_in"`
	LevelIDIn    []string `json:"level_id_in" form:"level_id_in"`
	BaseSearchQuery
}

func (query *PublishCourseQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.OrgIDIn != nil {
		qb = qb.Where("org_id IN (?)", query.OrgIDIn)
	}

	if query.OrgIDNot != nil {
		qb = qb.Where("org_id <> ?", *query.OrgIDNot)
	}

	if query.CourseID != nil {
		qb = qb.Where("course_id = ?", *query.CourseID)
	}

	if query.CourseSlug != nil {
		qb = qb.Where("course_slug = ?", *query.CourseSlug)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *query.CourseCuid)
	}

	if len(query.CourseCuidIn) > 0 {
		qb = qb.Where("course_cuid IN (?)", query.CourseCuidIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.IsRoot != nil {
		qb = qb.Where("is_root = ?", *query.IsRoot)
	}

	if query.IsPay != nil {
		qb = qb.Where("is_pay = ?", *query.IsPay)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.EnableRoot != nil {
		qb = qb.Where("enable_root = ?", *query.EnableRoot)
	}

	if query.EnableAll != nil {
		qb = qb.Where("enable = true OR enable_root = true")
	}

	if query.GteStartDate != nil {
		qb = qb.Where("start_date <= ? ", *query.GteStartDate)
	}

	if query.LteEndDate != nil {
		qb = qb.Where("end_date >= ? ", *query.LteEndDate)
	}

	if query.IsPub != nil && *query.IsPub {
		qb = qb.Where("pub_date > 0")
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.MinPrice != nil {
		qb = qb.Where("price > ?", *query.MinPrice)
	}

	if query.MaxPrice != nil {
		qb = qb.Where("price < ?", *query.MaxPrice)
	}

	if query.Scope != nil {
		qb = qb.Where("scope = ?", *query.Scope)
	}

	if query.MarkAsCompleted != nil {
		qb = qb.Where("mark_as_completed = ?", *query.MarkAsCompleted)
	}

	if len(query.CompleteStatusIn) > 0 {
		var args []bool
		for _, status := range query.CompleteStatusIn {
			switch CourseCompleteStatus(status) {
			case Completed:
				args = append(args, true)
			case InProgress:
				args = append(args, false)
			}
		}
		qb = qb.Where("mark_as_completed IN (?)", args)
	}

	if query.BookmarkedBy != nil {
		subQuery := db.Table(GetTblName(BookmarkTbl)).
			Select("entity_id").
			Where("user_id = ? AND entity_type = ? AND delete_at = 0", *query.BookmarkedBy, CourseModelName).
			Group("entity_id")
		qb = qb.Joins(fmt.Sprintf("INNER JOIN (?) AS b ON %[1]s.course_cuid = b.entity_id", GetTblName(PublishCourseTbl)), subQuery)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, &PublishBlog{}, nil)
	}
	return qb
}

func (query *PublishCourseQuery) ApplyJoin(db *gorm.DB) *gorm.DB {
	qb := db
	if query.OrgID != nil {
		qb = qb.Where("p.org_id = ?", *query.OrgID)
	}

	if query.OrgIDIn != nil {
		qb = qb.Where("org_id IN (?)", query.OrgIDIn)
	}

	if query.OrgIDNot != nil {
		qb = qb.Where("p.org_id <> ?", *query.OrgIDNot)
	}

	if query.CourseID != nil {
		qb = qb.Where("p.course_id = ?", *query.CourseID)
	}

	if query.CourseSlug != nil {
		qb = qb.Where("p.course_slug = ?", *query.CourseSlug)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("p.course_cuid = ?", *query.CourseCuid)
	}

	if len(query.CourseCuidIn) > 0 {
		qb = qb.Where("p.course_cuid IN (?)", query.CourseCuidIn)
	}

	if query.UserID != nil {
		qb = qb.Where("p.user_id = ?", *query.UserID)
	}

	if query.Name != nil {
		qb = qb.Where("p.name = ?", *query.Name)
	}

	if query.IsRoot != nil {
		qb = qb.Where("p.is_root = ?", *query.IsRoot)
	}

	if query.IsPay != nil {
		qb = qb.Where("p.is_pay = ?", *query.IsPay)
	}

	if query.Enable != nil {
		qb = qb.Where("p.enable = ?", *query.Enable)
	}

	if query.EnableRoot != nil {
		qb = qb.Where("p.enable_root = ?", *query.EnableRoot)
	}

	if query.EnableAll != nil {
		qb = qb.Where("p.enable = true OR p.enable_root = true")
	}

	if query.GteStartDate != nil {
		qb = qb.Where("p.start_date <= ? ", *query.GteStartDate)
	}

	if query.LteEndDate != nil {
		qb = qb.Where("p.end_date >= ? ", *query.LteEndDate)
	}

	if query.IsPub != nil && *query.IsPub {
		qb = qb.Where("p.pub_date > 0")
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("p.delete_at = 0")
	}

	if query.MinPrice != nil {
		qb = qb.Where("p.price > ?", *query.MinPrice)
	}

	if query.MaxPrice != nil {
		qb = qb.Where("p.price < ?", *query.MaxPrice)
	}

	if query.BookmarkedBy != nil {
		subQuery := db.Table(GetTblName(BookmarkTbl)).
			Select("entity_id").
			Where("user_id = ? AND entity_type = ? AND delete_at = 0", *query.BookmarkedBy, CourseModelName).
			Group("entity_id")
		qb = qb.Joins("INNER JOIN (?) AS b ON p.course_cuid = b.entity_id", subQuery)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, &PublishCourse{}, util.NewString("p"))
	}

	if len(query.CategoryIDIn) > 0 {
		qb = qb.Where("r.category_id IN (?) AND r.delete_at = 0", query.CategoryIDIn)
	}

	if len(query.LevelIDIn) > 0 {
		qb = qb.Where("l.category_id IN (?) AND l.delete_at = 0", query.LevelIDIn)
	}

	if len(query.CompleteStatusIn) > 0 {
		var args []bool
		for _, status := range query.CompleteStatusIn {
			switch CourseCompleteStatus(status) {
			case Completed:
				args = append(args, true)
			case InProgress:
				args = append(args, false)
			}
		}
		qb = qb.Where("mark_as_completed IN (?)", args)
	}
	return qb
}

func preloadPublishCourseCategories(pubCourses []*PublishCourse) error {
	cuids := lo.Map(pubCourses, func(pub *PublishCourse, _ int) string {
		return pub.CourseCuid
	})
	cuids = lo.Uniq(cuids)
	if categoriesByCoursesIDs, mErr := Repository.CategoryRelation.GetCategoriesByEntities(CourseModelName, cuids, CategoriesField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(pubCourses, func(pub *PublishCourse, _ int) {
			pub.Categories = categoriesByCoursesIDs[pub.CourseID]
		})
	}
	return nil
}

func (r *PublishCourseRepository) Create(pubCourse *PublishCourse, trans *gorm.DB) error {
	return create(PublishCourseTbl, pubCourse, trans)
}

func (r *PublishCourseRepository) Update(pubCourse *PublishCourse, trans *gorm.DB) error {
	return update(PublishCourseTbl, pubCourse, trans)
}

func (r *PublishCourseRepository) FindOne(query *PublishCourseQuery, options *FindOneOptions) (*PublishCourse, error) {
	return findOne[PublishCourse](PublishCourseTbl, query, options)
}

func (r *PublishCourseRepository) makeCacheKeyFindBySlug(slug string, options *FindOneOptions) string {
	key := slug
	if options != nil {
		key += options.ToCacheKey()
	}
	return key
}

func (r *PublishCourseRepository) FindBySlug(slug string, options *FindOneOptions) (*PublishCourse, error) {
	var cachedPubCourse PublishCourse
	key := r.makeCacheKeyFindBySlug(slug, options)
	if cErr := Cache.PublishCourse.GetByKey(key, &cachedPubCourse); cErr == nil && cachedPubCourse.ID != "" {
		return &cachedPubCourse, nil
	}

	pubCourse, err := r.FindOne(&PublishCourseQuery{
		CourseSlug: &slug,
	}, options)
	if err != nil {
		return nil, err
	}

	if cErr := Cache.PublishCourse.SetByKey(key, pubCourse); cErr != nil {
		log.Errorf("PublishCourseRepository::FindBySlug Set pubish course to cache failed: %v", cErr)
	}

	return pubCourse, nil
}

func (r *PublishCourseRepository) FindMany(query *PublishCourseQuery, options *FindManyOptions) ([]*PublishCourse, error) {
	return findMany[PublishCourse](PublishCourseTbl, query, options)
}

func (r *PublishCourseRepository) FindPageCourseIDs(query *PublishCourseQuery, options *FindPageOptions) ([]string, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}

	courseIDs, pagination, err := findPagePublishCourseIDs(query, options)
	if err != nil {
		return nil, nil, err
	}

	return courseIDs, pagination, err
}

func findPagePublishCourseIDs(query *PublishCourseQuery, options *FindPageOptions) ([]string, *Pagination, error) {
	var c int64
	var entity PublishCourse
	countQb := query.ApplyJoin(DB)
	countQb = countQb.Table(GetTblName(PublishCourseTbl) + " as p").Debug()
	countQb = countQb.Select("count(DISTINCT(p.course_cuid)) as count")
	if len(query.CategoryIDIn) > 0 {
		countQb = countQb.Joins("join " + GetTblName(CategoryRelationTbl) + " as r on p.course_cuid = r.related_id")
	}
	if len(query.LevelIDIn) > 0 {
		countQb = countQb.Joins("join " + GetTblName(CategoryRelationTbl) + " as l on p.course_cuid = l.related_id")
	}
	countResult := countQb.Model(&entity).Debug().Count(&c)
	if err := countResult.Error; err != nil {
		return nil, nil, err
	}

	var courseIDs []string
	subQb := DB.Table(GetTblName(PublishCourseTbl) + " as p")
	subQb = subQb.Select("DISTINCT ON (p.course_cuid) p.course_id, p.course_cuid, p.create_at")
	subQb = subQb.Order("p.course_cuid")
	if len(query.CategoryIDIn) > 0 {
		subQb = subQb.Joins("join " + GetTblName(CategoryRelationTbl) + " as r on p.course_cuid = r.related_id")
	}

	if len(query.LevelIDIn) > 0 {
		subQb = subQb.Joins("join " + GetTblName(CategoryRelationTbl) + " as l on p.course_cuid = l.related_id")
	}
	subQb = query.ApplyJoin(subQb)

	findQb := DB.Table("(?) as distinct_courses", subQb)
	lo.ForEach(options.Sort, func(clause string, _ int) {
		findQb = findQb.Order(clause)
	})
	findQb = findQb.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)
	findQb = findQb.Debug()
	findQb = findQb.Select("course_id")
	findResult := findQb.Debug().Find(&courseIDs)
	if err := findResult.Error; err != nil {
		return nil, nil, err
	}

	return courseIDs, NewPagination(options.Page, options.PerPage, int(c)), nil
}

func (r *PublishCourseRepository) FindPage(query *PublishCourseQuery, options *FindPageOptions) ([]*PublishCourse, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}

	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := getExPreload(preloads)
	options.Preloads = newPreloads

	pubCourses, pagination, err := findPagePublishCourseByCategories(query, options)
	if err != nil {
		return nil, nil, err
	}

	thumbIds := lo.Map(pubCourses, func(item *PublishCourse, _ int) string {
		return item.ThumbnailID
	})
	if len(thumbIds) > 0 {
		if thumbs, tErr := Repository.File.FindMany(&FileQuery{IDIn: thumbIds}, nil); tErr != nil {
			return nil, nil, tErr
		} else {
			lo.ForEach(pubCourses, func(pub *PublishCourse, _ int) {
				t, ok := lo.Find(thumbs, func(item *File) bool {
					return item.ID == pub.ThumbnailID
				})
				if ok {
					pub.Thumbnail = t
				}
			})
		}
	}

	if exPreload.User {
		userIds := lo.Map(pubCourses, func(item *PublishCourse, _ int) string {
			return item.UserID
		})
		if len(userIds) > 0 {
			userIds = lo.Uniq(userIds)
			users, userErr := Repository.User.FindMany(&UserQuery{IDIn: &userIds}, nil)
			if userErr != nil {
				return pubCourses, pagination, userErr
			}
			lo.ForEach(pubCourses, func(item *PublishCourse, _ int) {
				user, ok := lo.Find(users, func(u *User) bool {
					return u.ID == item.UserID
				})
				if ok {
					item.User = user.ToSimpleProfile()
				}
			})
		}
	}

	if exPreload.Categories {
		if catErr := preloadPublishCourseCategories(pubCourses); catErr != nil {
			return pubCourses, pagination, catErr
		}
	}

	if exPreload.TotalUserEnrollments {
		if err = r.preloadTotalUserEnrollment(pubCourses); err != nil {
			return pubCourses, pagination, err
		}
	}
	return pubCourses, pagination, err
}

func (r *PublishCourseRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[PublishCourse](PublishCourseTbl, id, trans)
}

func (r *PublishCourseRepository) Count(query *PublishCourseQuery) (int64, error) {
	return count[PublishCourse](PublishCourseTbl, query)
}

func findPagePublishCourseByCategories(query *PublishCourseQuery, options *FindPageOptions) ([]*PublishCourse, *Pagination, error) {
	entitiesChan := make(chan []*PublishCourse)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		close(entitiesChan)
		close(countChan)
		close(errorChan)
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		var entities []*PublishCourse

		qb := query.ApplyJoin(DB)
		qb = qb.Order("p.course_cuid")
		lo.ForEach(options.Sort, func(clause string, _ int) {
			qb = qb.Order("p." + clause)
		})
		qb = qb.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)
		qb = qb.Table(GetTblName(PublishCourseTbl) + " as p").Debug()
		qb = qb.Select("DISTINCT on (p.course_cuid) course_cuid, p.*")
		if len(query.CategoryIDIn) > 0 {
			qb = qb.Joins("join " + GetTblName(CategoryRelationTbl) + " as r on p.course_cuid = r.related_id")
		}
		result := qb.Debug().Find(&entities)

		if err := result.Error; err != nil {
			errorChan <- err
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		var c int64
		var entity PublishCourse
		qb := query.ApplyJoin(DB)
		qb = qb.Table(GetTblName(PublishCourseTbl) + " as p").Debug()
		qb = qb.Select("count(DISTINCT(p.course_cuid)) as count")
		if len(query.CategoryIDIn) > 0 {
			qb = qb.Joins("join " + GetTblName(CategoryRelationTbl) + " as r on p.course_cuid = r.related_id")
		}
		result := qb.Model(&entity).Debug().Count(&c)
		if err := result.Error; err != nil {
			errorChan <- err
			return
		}

		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*PublishCourse
	var entitiesCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entitiesCount = <-countChan:
		case err := <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entitiesCount)), nil
}

func (r *PublishCourseRepository) preloadTotalUserEnrollment(pubCourses []*PublishCourse) error {
	cuids := lo.Map(pubCourses, func(pub *PublishCourse, _ int) string {
		return pub.CourseCuid
	})
	cuids = lo.Uniq(cuids)
	//Get cache
	mapCount, err := Cache.CourseEnrollment.GetManyCourseEnrollment(cuids)
	if err == nil {
		log.Debugf("preloadTotalUserEnrollment from cache")
		lo.ForEach(pubCourses, func(pub *PublishCourse, _ int) {
			pub.TotalUserEnrollment = mapCount[pub.CourseCuid]
		})
	} else {
		totalUserEnrollments, mErr := Repository.CourseEnrollment(r.ctx).CountByCourseCUIDs(&CourseEnrollmentQuery{CourseCuidIn: cuids})
		if mErr != nil {
			return mErr
		}
		lo.ForEach(pubCourses, func(pub *PublishCourse, _ int) {
			if _, ok := totalUserEnrollments[pub.CourseCuid]; !ok {
				totalUserEnrollments[pub.CourseCuid] = 0
			}
			pub.TotalUserEnrollment = totalUserEnrollments[pub.CourseCuid]
		})
		if err := Cache.CourseEnrollment.SetManyCourseEnrollment(totalUserEnrollments); err != nil {
			return err
		}
	}
	return nil
}
