package models

import (
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type LessonStatus string
type LessonContentType string

const (
	LessonTypeText     LessonContentType = "text"
	LessonTypeVideo    LessonContentType = "video"
	LessonTypePDF      LessonContentType = "pdf"
	LessonTypePP       LessonContentType = "pp"
	LessonTypeDoc      LessonContentType = "doc"
	LessonTypeQuiz     LessonContentType = "quiz"
	LessonTypeEmbedded LessonContentType = "embedded"
)

type LessonContent struct {
	OrgID       string              `json:"org_id"`
	UID         string              `json:"uid"`
	CourseID    string              `json:"course_id"`
	UserID      string              `json:"user_id"`
	SectionID   string              `json:"section_id"`
	LessonID    string              `json:"lesson_id"`
	Title       string              `json:"title"`
	Content     string              `json:"content"  gorm:"type:text"`
	JsonContent JSONB               `json:"json_content" gorm:"type:jsonb"`
	Order       int                 `json:"order"`
	Type        LessonContentType   `json:"type"`
	Duration    int                 `json:"duration"`
	Files       []*File             `json:"files" gorm:"-"`
	Quizzes     []*QuizWithRelation `json:"quizzes" gorm:"-"`
	Model

	LessonIDV2 string `json:"lesson_id_v2"`
}

type SimpleLessonContent struct {
	Model
	OrgID       string                    `json:"org_id"`
	UID         string                    `json:"uid"`
	LessonID    string                    `json:"lesson_id"`
	Title       string                    `json:"title"`
	Content     string                    `json:"content"`
	JsonContent JSONB                     `json:"json_content"`
	Files       []*SimpleFile             `json:"files"`
	Order       int                       `json:"order"`
	Type        LessonContentType         `json:"type"`
	Duration    int                       `json:"duration"`
	Quizzes     []*SimpleQuizWithRelation `json:"quizzes"`
}

func (lc *LessonContent) Sanitize() *LessonContent {
	return &LessonContent{
		OrgID:       lc.OrgID,
		UID:         lc.UID,
		CourseID:    lc.CourseID,
		UserID:      lc.UserID,
		SectionID:   lc.SectionID,
		LessonID:    lc.LessonID,
		Title:       lc.Title,
		Content:     lc.Content,
		JsonContent: lc.JsonContent,
		Order:       lc.Order,
		Type:        lc.Type,
		Duration:    lc.Duration,
		Files:       lc.Files,
		Quizzes: lo.Map(lc.Quizzes, func(quiz *QuizWithRelation, _ int) *QuizWithRelation {
			return quiz.Sanitize()
		}),
		Model: lc.Model,
	}
}

func (lc *LessonContent) ToSimple() *SimpleLessonContent {
	simpleLessonContent := &SimpleLessonContent{
		Model:       lc.Model,
		OrgID:       lc.OrgID,
		UID:         lc.UID,
		LessonID:    lc.LessonID,
		Title:       lc.Title,
		Content:     lc.Content,
		JsonContent: lc.JsonContent,
		Files: lo.Map(lc.Files, func(file *File, _ int) *SimpleFile {
			return file.Sanitize()
		}),
		Order:    lc.Order,
		Type:     lc.Type,
		Duration: lc.Duration,
		Quizzes: lo.Map(lc.Quizzes, func(quiz *QuizWithRelation, _ int) *SimpleQuizWithRelation {
			return quiz.ToSimple()
		}),
	}
	return simpleLessonContent
}

func (lc *LessonContent) IsQuizLesson() bool {
	return lc.Type == LessonTypeQuiz
}

func (lc *LessonContent) IsPDFLesson() bool {
	return lc.Type == LessonTypeVideo
}

func (lc *LessonContent) IsVideoLesson() bool {
	return lc.Type == LessonTypeVideo
}

type LessonContentQuery struct {
	OrgID          *string  `json:"org_id" form:"org_id"`
	ID             *string  `json:"id" form:"id"`
	UID            *string  `json:"uid" form:"uid"`
	UIDIn          []string `json:"uid_in" form:"uid_in"`
	CourseID       *string  `json:"course_id" form:"course_id"`
	SectionID      *string  `json:"section_id" form:"section_id"`
	LessonID       *string  `json:"lesson_id" form:"lesson_id"`
	LessonIDIn     []string `json:"lesson_id_in" form:"lesson_id_in"`
	Title          *string  `json:"title" form:"title"`
	Free           *bool    `json:"free" form:"free"`
	Type           *string  `json:"type" form:"type"`
	IncludeDeleted *bool
	IDIn           []string
}

func (query *LessonContentQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.UID != nil {
		qb = qb.Where("uid = ?", *query.UID)
	}

	if len(query.UIDIn) > 0 {
		qb = qb.Where("uid IN (?)", query.UIDIn)
	}

	if query.CourseID != nil {
		qb = qb.Where("course_id = ?", *query.CourseID)
	}

	if query.LessonID != nil {
		qb = qb.Where("lesson_id = ?", *query.LessonID)
	}

	if query.LessonIDIn != nil {
		qb = qb.Where("lesson_id IN (?)", query.LessonIDIn)
	}

	if query.SectionID != nil {
		qb = qb.Where("section_id = ?", *query.SectionID)
	}
	if query.Title != nil {
		qb = qb.Where("title = ?", *query.Title)
	}
	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}
	if query.Free != nil {
		qb = qb.Where("free = ?", *query.Free)
	}
	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.IDIn != nil {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	return qb
}

func (query *LessonContentQuery) ToCacheKey() string {
	key := ""

	if query.ID != nil {
		key += fmt.Sprintf("_id:%s", *query.ID)
	}

	if query.OrgID != nil {
		key += fmt.Sprintf("_org_id:%s", *query.OrgID)
	}

	if query.UID != nil {
		key += fmt.Sprintf("_uid:%s", *query.UID)
	}

	if query.CourseID != nil {
		key += fmt.Sprintf("_course_id:%s", *query.CourseID)
	}

	if query.LessonID != nil {
		key += fmt.Sprintf("_lesson_id:%s", *query.LessonID)
	}

	if query.LessonIDIn != nil {
		key += fmt.Sprintf("_lesson_id_in:%s", strings.Join(query.LessonIDIn, "_"))
	}

	if query.SectionID != nil {
		key += fmt.Sprintf("_section_id:%s", *query.SectionID)
	}

	if query.Title != nil {
		key += fmt.Sprintf("_title:%s", *query.Title)
	}

	if query.Type != nil {
		key += fmt.Sprintf("_type:%s", *query.Type)
	}

	if query.Free != nil {
		key += fmt.Sprintf("_free:%v", *query.Free)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		key += fmt.Sprintf("_delete_at:0")
	}

	if query.IDIn != nil {
		key += fmt.Sprintf("_id_in:%s", strings.Join(query.IDIn, "_"))
	}

	return key
}

// Create inserts a lesson to database, transaction is optional
func (r *LessonContentRepository) Create(entity *LessonContent, trans *gorm.DB) error {
	if err := create(LessonContentTbl, entity, trans); err != nil {
		return err
	}
	if entity.Files != nil {
		if fErr := Repository.FileRelation.AddFiles(LessonModelName, entity.ID, FilesField, entity.Files); fErr != nil {
			return fErr
		}
	}

	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("LessonContentRepository.Create::Clear cache lesson contents error: %v", cErr)
	}
	return nil
}

func (r *LessonContentRepository) CreateMany(entities []*LessonContent, trans *gorm.DB) error {
	if err := createMany(LessonContentTbl, entities, trans); err != nil {
		return err
	}
	for _, e := range entities {
		if e.Files != nil {
			if fErr := Repository.FileRelation.AddFiles(LessonModelName, e.ID, FilesField, e.Files); fErr != nil {
				return fErr
			}
		}
	}
	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("LessonContentRepository.CreateMany::Clear cache lesson contents error: %v", cErr)
	}
	return nil
}

func (r *LessonContentRepository) Update(entity *LessonContent, trans *gorm.DB) error {
	if err := update(LessonContentTbl, entity, trans); err != nil {
		return err
	}
	if entity.Files != nil {
		if fErr := Repository.FileRelation.AddFiles(LessonModelName, entity.ID, FilesField, entity.Files); fErr != nil {
			return fErr
		}
	}
	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("LessonContentRepository.Update::Clear cache lesson contents error: %v", cErr)
	}
	return nil
}

func (r *LessonContentRepository) UpdateMany(entities []*LessonContent, trans *gorm.DB) error {
	if err := updateMany(LessonContentTbl, entities, trans); err != nil {
		return err
	}
	for _, e := range entities {
		if e.Files != nil {
			if fErr := Repository.FileRelation.AddFiles(LessonModelName, e.ID, FilesField, e.Files); fErr != nil {
				return fErr
			}
		}
	}
	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("LessonContentRepository.UpdateMany::Clear cache lesson contents error: %v", cErr)
	}
	return nil
}

func (r *LessonContentRepository) UpdateManyDurationFromWebhook(entities []*LessonContent, trans *gorm.DB) error {
	var tx *gorm.DB
	// Begin trans
	if trans != nil {
		tx = trans

	} else {
		tx = GetDb(LessonContentTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}
	for _, lc := range entities {
		if err := tx.Model(&LessonContent{}).Where("id = ?", lc.ID).Updates(map[string]interface{}{
			"duration":  lc.Duration,
			"update_at": util.GetCurrentTime(),
		}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("LessonContentRepository.UpdateManyDurationFromWebhook::Clear cache lesson contents error: %v", cErr)
	}
	return tx.Commit().Error
}

func (r *LessonContentRepository) FindByID(id string, options *FindOneOptions) (*LessonContent, error) {
	shouldPreloadContents := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, FilesField) {
		shouldPreloadContents = true
		options.Preloads = util.RemoveElement(options.Preloads, FilesField)
	}
	lesson, err := findByID[LessonContent](LessonContentTbl, id, options)
	if err != nil {
		return nil, err
	}
	if shouldPreloadContents {
		if files, mErr := Repository.FileRelation.GetFiles(LessonModelName, lesson.ID, FilesField); mErr != nil {
			return lesson, mErr
		} else {
			lesson.Files = files
		}
	}
	return lesson, nil
}

func (r *LessonContentRepository) FindOne(query *LessonContentQuery, options *FindOneOptions) (*LessonContent, error) {
	shouldPreloadContents := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, FilesField) {
		shouldPreloadContents = true
		options.Preloads = util.RemoveElement(options.Preloads, FilesField)
	}
	lesson, err := findOne[LessonContent](LessonContentTbl, query, options)
	if err != nil {
		return nil, err
	}
	if shouldPreloadContents {
		if files, mErr := Repository.FileRelation.GetFiles(LessonModelName, lesson.ID, FilesField); mErr != nil {
			return lesson, mErr
		} else {
			lesson.Files = files
		}
	}
	return lesson, nil
}

func (r *LessonContentRepository) makeCacheKeyFindMany(query *LessonContentQuery, options *FindManyOptions) string {
	key := ""
	if query != nil {
		key += query.ToCacheKey()
	}
	if options != nil {
		key += options.ToCacheKey()
	}
	return key
}

func (r *LessonContentRepository) FindMany(query *LessonContentQuery, options *FindManyOptions) ([]*LessonContent, error) {
	var cachedContents []interface{}
	cacheKey := r.makeCacheKeyFindMany(query, options)
	if cErr := Cache.LessonContent.GetByKey(cacheKey, &cachedContents); cErr == nil && len(cachedContents) > 0 {
		var contents []*LessonContent
		if err := Cache.Convert(cachedContents, &contents); err == nil {
			return contents, nil
		}
	}

	shouldPreloadContents := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, FilesField) {
		shouldPreloadContents = true
		options.Preloads = util.RemoveElement(options.Preloads, FilesField)
	}

	contents, err := findMany[LessonContent](LessonContentTbl, query, options)
	if err != nil {
		return nil, err
	}
	if shouldPreloadContents {
		lessonIDs := lo.Map(contents, func(lesson *LessonContent, _ int) string {
			return lesson.ID
		})
		if contentsByLessonsIDs, mErr := Repository.FileRelation.GetFilesByEntities(LessonModelName, lessonIDs, FilesField); mErr != nil {
			return nil, mErr
		} else {
			lo.ForEach(contents, func(lesson *LessonContent, _ int) {
				lesson.Files = contentsByLessonsIDs[lesson.ID]
			})
		}
	}

	if cErr := Cache.LessonContent.SetByKey(cacheKey, lo.Map(contents, func(c *LessonContent, _ int) interface{} {
		return c
	})); cErr != nil {
		log.Errorf("LessonContentRepository::FindMany Set lesson contents to cache failed: %v", cErr)
	}

	return contents, err
}

func (r *LessonContentRepository) FindPage(query *LessonContentQuery, options *FindPageOptions) ([]*LessonContent, *Pagination, error) {
	shouldPreloadContents := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, FilesField) {
		shouldPreloadContents = true
		options.Preloads = util.RemoveElement(options.Preloads, FilesField)
	}

	contents, pagination, err := findPage[LessonContent](LessonContentTbl, query, options)
	if err != nil {
		return nil, nil, err
	}
	if shouldPreloadContents {
		lessonIDs := lo.Map(contents, func(lesson *LessonContent, _ int) string {
			return lesson.ID
		})
		if contentsByLessonsIDs, mErr := Repository.FileRelation.GetFilesByEntities(LessonModelName, lessonIDs, FilesField); mErr != nil {
			return nil, nil, mErr
		} else {
			lo.ForEach(contents, func(lesson *LessonContent, _ int) {
				lesson.Files = contentsByLessonsIDs[lesson.ID]
			})
		}
	}
	return contents, pagination, err
}

// Delete perform soft deletion to a org by ID, transaction is optional
func (r *LessonContentRepository) Delete(id string, trans *gorm.DB) error {
	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("LessonContentRepository.Delete::Clear cache lesson contents error: %v", cErr)
	}
	return deleteByID[LessonContent](LessonContentTbl, id, trans)
}

// Count returns number of lessons by query conditions, transaction is optional
func (r *LessonContentRepository) Count(query *LessonContentQuery) (int64, error) {
	return count[LessonContent](LessonContentTbl, query)
}

func (r *LessonContentRepository) CountEachTypeForSection(segment *Section, lessonIDs []string) (map[LessonContentType]int, error) {
	tx := GetDb(LessonContentTbl).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var results []struct {
		Type  LessonContentType `json:"type" gorm:"column:type"`
		Count int               `json:"count" gorm:"column:count"`
	}

	query := tx.Model(&LessonContent{}).
		Select("type, COUNT(*) as count")
	if segment.IsSection() {
		query.Where("section_id = ?", segment.ID) // If segment is a section, use its own ID
	} else {
		query.Where("section_id = ?", segment.ParentID) // If segment is a lesson, use its parent section ID
	}

	if len(lessonIDs) > 0 {
		query.Where("lesson_id IN (?)", lessonIDs)
	} else {
		query.Where("lesson_id = ?", segment.ID) // If no lesson IDs provided, segment is a lesson, use its own ID
	}

	query = query.Debug()

	if err := query.Where("delete_at = 0").Group("type").
		Scan(&results).Error; err != nil {
		query.Rollback()
		return nil, err
	}

	if err := query.Commit().Error; err != nil {
		return nil, err
	}

	mapCount := make(map[LessonContentType]int)
	for _, result := range results {
		mapCount[result.Type] = result.Count
	}

	return mapCount, nil

}
