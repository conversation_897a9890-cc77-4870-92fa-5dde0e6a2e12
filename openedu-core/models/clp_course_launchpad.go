package models

import (
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type ClpCourseLaunchpad struct {
	Model
	ClpLaunchpadID string        `json:"clp_launchpad_id" gorm:"not null"`
	ClpLaunchpad   *ClpLaunchpad `json:"clp_launchpad" gorm:"-"`
	CourseCuid     string        `json:"course_cuid" gorm:"not null"`
	CourseID       string        `json:"course_id"`
	Enable         bool          `json:"enable"`
}

type ClpCourseLaunchpadQuery struct {
	ID               *string  `json:"id" form:"id"`
	IDIn             []string `json:"id_in" form:"id_in"`
	ClpLaunchpadID   *string  `json:"clp_launchpad_id" form:"clp_launchpad_id"`
	ClpLaunchpadIDNe *string  `json:"clp_launchpad_id_ne" form:"clp_launchpad_id_ne"`
	ClpLaunchpadIDIn []string `json:"clp_launchpad_id_in" form:"clp_launchpad_id_in"`
	CourseCuid       *string  `json:"course_cuid" form:"course_cuid"`
	CourseCuidIn     []string `json:"course_cuid_in" form:"course_cuid_in"`
	CourseID         *string  `json:"course_id" form:"course_id"`
	CourseIDIn       []string `json:"course_id_in" form:"course_id_in"`
	Enable           *bool    `json:"enable" form:"enable"`
	IncludeDeleted   *bool    `json:"include_deleted" form:"include_deleted"`
}

func (query *ClpCourseLaunchpadQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if len(query.CourseCuidIn) > 0 {
		qb = qb.Where("course_cuid IN (?)", query.CourseCuidIn)
	}

	if len(query.ClpLaunchpadIDIn) > 0 {
		qb = qb.Where("clp_launchpad_id IN (?)", query.ClpLaunchpadIDIn)
	}

	if query.ClpLaunchpadID != nil {
		qb = qb.Where("clp_launchpad_id = ?", *query.ClpLaunchpadID)
	}

	if query.ClpLaunchpadIDNe != nil {
		qb = qb.Where("clp_launchpad_id <> ?", *query.ClpLaunchpadIDNe)
	}

	if query.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *query.CourseCuid)
	}

	if query.CourseID != nil {
		qb = qb.Where("course_id = ?", *query.CourseID)
	}

	if len(query.CourseIDIn) > 0 {
		qb = qb.Where("course_id IN (?)", query.CourseIDIn)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

type ClpCourseLaunchpadPreload struct {
	ClpLaunchpad bool
}

func (r *ClpCourseLaunchpadRepository) getExPreload(preloads []string) (*ClpCourseLaunchpadPreload, []string) {
	shouldPreloadClpLaunchpad := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, ClpLaunchpadField) {
		shouldPreloadClpLaunchpad = true
		newPreloads = util.RemoveElement(newPreloads, ClpLaunchpadField)
	}

	return &ClpCourseLaunchpadPreload{
		ClpLaunchpad: shouldPreloadClpLaunchpad,
	}, newPreloads
}

func (r *ClpCourseLaunchpadRepository) preloadClpLaunchpad(courseLaunchpads []*ClpCourseLaunchpad) error {
	if len(courseLaunchpads) == 0 {
		return nil
	}

	clpLaunchpadIDs := lo.Map(courseLaunchpads, func(courseLaunchpad *ClpCourseLaunchpad, _ int) string {
		return courseLaunchpad.ClpLaunchpadID
	})

	clpLaunchpads, err := Repository.ClpLaunchpad(r.ctx).FindMany(&LaunchpadQuery{
		IDIn: clpLaunchpadIDs,
	}, nil)
	if err != nil {
		return err
	}

	clpLaunchpadMap := make(map[string]*ClpLaunchpad)
	for _, launchpad := range clpLaunchpads {
		clpLaunchpadMap[launchpad.ID] = launchpad
	}

	for _, courseLaunchpad := range courseLaunchpads {
		if launchpad, ok := clpLaunchpadMap[courseLaunchpad.ClpLaunchpadID]; ok {
			courseLaunchpad.ClpLaunchpad = launchpad
		}
	}

	return nil
}

func (r *ClpCourseLaunchpadRepository) FindMany(query *ClpCourseLaunchpadQuery, options *FindManyOptions) ([]*ClpCourseLaunchpad, error) {
	if options == nil {
		options = &FindManyOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := r.getExPreload(preloads)
	options.Preloads = newPreloads

	clpCourseLaunchpads, err := findMany[ClpCourseLaunchpad](ClpCourseLaunchpadTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.ClpLaunchpad {
		if err := r.preloadClpLaunchpad(clpCourseLaunchpads); err != nil {
			return nil, err
		}
	}

	return clpCourseLaunchpads, nil
}

func (r *ClpCourseLaunchpadRepository) Create(e *ClpCourseLaunchpad, trans *gorm.DB) error {
	return create(ClpCourseLaunchpadTbl, e, trans)
}

func (r *ClpCourseLaunchpadRepository) CreateMany(ts []*ClpCourseLaunchpad, trans *gorm.DB) error {
	return createMany(ClpCourseLaunchpadTbl, ts, trans)
}

func (r *ClpCourseLaunchpadRepository) Update(f *ClpCourseLaunchpad, trans *gorm.DB) error {
	return update(ClpCourseLaunchpadTbl, f, trans)
}

func (r *ClpCourseLaunchpadRepository) FindOne(query *ClpCourseLaunchpadQuery, options *FindOneOptions) (*ClpCourseLaunchpad, error) {
	return findOne[ClpCourseLaunchpad](ClpCourseLaunchpadTbl, query, options)
}

func (r *ClpCourseLaunchpadRepository) FindPage(query *ClpCourseLaunchpadQuery, options *FindPageOptions) ([]*ClpCourseLaunchpad, *Pagination, error) {
	return findPage[ClpCourseLaunchpad](ClpCourseLaunchpadTbl, query, options)
}

// func (r *ClpCourseLaunchpadRepository) FindMany(query *ClpCourseLaunchpadQuery, options *FindManyOptions) ([]*ClpCourseLaunchpad, error) {
// 	shouldPreloadClpLaunchpad := false
// 	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, ClpLaunchpadField) {
// 		shouldPreloadClpLaunchpad = true
// 		options.Preloads = util.RemoveElement(options.Preloads, ClpLaunchpadField)
// 	}

// 	clpCourseLaunchpads, err := findMany[ClpCourseLaunchpad](ClpCourseLaunchpadTbl, query, options)
// 	if err != nil {
// 		return nil, err
// 	}

// 	if shouldPreloadClpLaunchpad && len(clpCourseLaunchpads) > 0 {
// 		clpLaunchpadIDs := lo.Map(clpCourseLaunchpads, func(courseLaunchpad *ClpCourseLaunchpad, _ int) string {
// 			return courseLaunchpad.ClpLaunchpadID
// 		})

// 		clpLaunchpads, err := Repository.ClpLaunchpad.FindMany(&LaunchpadQuery{
// 			IDIn: clpLaunchpadIDs,
// 		}, nil)
// 		if err != nil {
// 			return nil, err
// 		}

// 		clpLaunchpadMap := make(map[string]*ClpLaunchpad)
// 		for _, launchpad := range clpLaunchpads {
// 			clpLaunchpadMap[launchpad.ID] = launchpad
// 		}

// 		for _, courseLaunchpad := range clpCourseLaunchpads {
// 			if launchpad, ok := clpLaunchpadMap[courseLaunchpad.ClpLaunchpadID]; ok {
// 				courseLaunchpad.ClpLaunchpad = launchpad
// 			}
// 		}
// 	}

// 	return clpCourseLaunchpads, nil
// }

func (r *ClpCourseLaunchpadRepository) Count(query *ClpCourseLaunchpadQuery) (int64, error) {
	return count[ClpCourseLaunchpad](ClpCourseLaunchpadTbl, query)
}

func (r *ClpCourseLaunchpadRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ClpCourseLaunchpad](ClpCourseLaunchpadTbl, id, trans)
}
