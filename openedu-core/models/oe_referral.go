package models

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type OEReferral struct {
	Model
	UserID        string            `json:"user_id"  gorm:"type:varchar(20)"`
	RefereeID     string            `json:"referee_id" gorm:"type:varchar(20)"`
	Code          string            `json:"code"`
	RefCodeID     string            `json:"ref_code_id"  gorm:"type:varchar(20)"`
	CampaignID    string            `json:"campaign_id"  gorm:"type:varchar(20)"`
	CampaignType  ReferralType      `json:"campaign_type"`
	PointReward   PointReward       `json:"point_reward" gorm:"type:jsonb"`
	Amount        decimal.Decimal   `json:"amount" gorm:"type:numeric(19,4);not null;default:0"`
	RefereeReward PointReward       `json:"referee_reward" gorm:"type:jsonb"`
	RefereeAmount decimal.Decimal   `json:"referee_amount" gorm:"type:numeric(19,4);not null;default:0"`
	Trigger       OEReferralTrigger `json:"trigger"`

	User     *User            `json:"user"`
	Campaign *OEPointCampaign `json:"campaign"`
	RefCode  *OEReferralCode  `json:"ref_code"`
}

type OEReferralQuery struct {
	ID           *string            `json:"id" form:"id"`
	UserID       *string            `json:"user_id" form:"user_id"`
	RefereeID    *string            `json:"referee_id" form:"referee_id"`
	Code         *string            `json:"code" form:"code"`
	RefCodeID    *string            `json:"ref_code_id" form:"ref_code_id"`
	CampaignID   *string            `json:"campaign_id" form:"campaign_id"`
	CampaignType *ReferralType      `json:"campaign_type" form:"campaign_type"`
	Trigger      *OEReferralTrigger `json:"trigger" form:"trigger"`
	Deleted      *bool              `json:"deleted" form:"deleted"`
	StartDateLt  *int               `json:"start_date" form:"start_date"`
	EndDateGt    *int               `json:"end_date" form:"end_date"`
}

func (q *OEReferralQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if q.ID != nil {
		qb = qb.Where("id = ?", *q.ID)
	}

	if q.UserID != nil {
		qb = qb.Where("user_id = ?", *q.UserID)
	}

	if q.RefereeID != nil {
		qb = qb.Where("referee_id = ?", *q.RefereeID)
	}

	if q.Code != nil {
		qb = qb.Where("code = ?", *q.Code)
	}

	if q.RefCodeID != nil {
		qb = qb.Where("ref_code_id = ?", *q.RefCodeID)
	}

	if q.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *q.CampaignID)
	}

	if q.CampaignType != nil {
		qb = qb.Where("campaign_type = ?", *q.CampaignType)
	}

	if q.Trigger != nil {
		qb = qb.Where("trigger = ?", *q.Trigger)
	}

	if q.StartDateLt != nil {
		qb = qb.Where("start_date = 0 OR start_date < ?", *q.StartDateLt)
	}

	if q.EndDateGt != nil {
		qb = qb.Where("end_date = 0 OR end_date > ?", *q.EndDateGt)
	}

	if q.Deleted == nil || *q.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *OEReferralRepository) Create(entity *OEReferral, trans *gorm.DB) error {
	return create(OEReferralTbl, entity, trans)
}

func (r *OEReferralRepository) CreateMany(entities []*OEReferral, trans *gorm.DB) error {
	return createMany(OEReferralTbl, entities, trans)
}

func (r *OEReferralRepository) Update(entity *OEReferral, trans *gorm.DB) error {
	return update(OEReferralTbl, entity, trans)
}

func (r *OEReferralRepository) FindByID(id string, options *FindOneOptions) (*OEReferral, error) {
	return findByID[OEReferral](OEReferralTbl, id, options)
}

func (r *OEReferralRepository) FindOne(query *OEReferralQuery, options *FindOneOptions) (*OEReferral, error) {
	return findOne[OEReferral](OEReferralTbl, query, options)
}

func (r *OEReferralRepository) FindMany(query *OEReferralQuery, options *FindManyOptions) ([]*OEReferral, error) {
	return findMany[OEReferral](OEReferralTbl, query, options)
}

func (r *OEReferralRepository) FindPage(query *OEReferralQuery, options *FindPageOptions) ([]*OEReferral, *Pagination, error) {
	return findPage[OEReferral](OEReferralTbl, query, options)
}

func (r *OEReferralRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OEReferral](OEReferralTbl, id, trans)
}

func (r *OEReferralRepository) DeleteMany(query *OEReferralQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[OEReferral](OEReferralTbl, query, trans)
}

// Count returns number of UserActions by query conditions, transaction is optional
func (r *OEReferralRepository) Count(query *OEReferralQuery) (int64, error) {
	return count[OEReferral](OEReferralTbl, query)
}
