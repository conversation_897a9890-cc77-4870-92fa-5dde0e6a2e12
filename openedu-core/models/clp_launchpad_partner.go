package models

type LaunchpadPartner struct {
	ID           string             `json:"id"`
	<PERSON><PERSON><PERSON>     string             `json:"username"`
	Email        string             `json:"email"`
	Active       bool               `json:"active"`
	Blocked      bool               `json:"blocked"`
	DisplayName  string             `json:"display_name"`
	Avatar       string             `json:"avatar"`
	Roles        []*SimpleUserRole  `json:"roles"`
	CoverPhoto   string             `json:"cover_photo"`
	Skills       []string           `json:"skills"`
	Headline     string             `json:"headline"`
	About        string             `json:"about"`
	Phone        string             `json:"phone"`
	Position     string             `json:"position"`
	Following    int64              `json:"following"`
	Followers    int64              `json:"followers"`
	Props        UserSettingsOption `json:"props"`
	TotalCourses int64              `json:"total_courses"`
	TotalBlogs   int64              `json:"total_blogs"`
}
