package models

import communicationdto "openedu-core/pkg/communication/dto"

type EmailCodeType string

type EmailTemplateVarName string

type EmailLinkType string

type EmailParamField string

type MapEmailParams map[EmailParamField]interface{}

func (m MapEmailParams) IntoComm() communicationdto.MapEmailParams {
	var res communicationdto.MapEmailParams

	for k, v := range m {
		res[communicationdto.EmailParamField(k)] = v
	}
	return res
}

const (
	EmailParamFullName              EmailParamField = "full_name"
	EmailParamEmail                 EmailParamField = "email"
	EmailParamPassword              EmailParamField = "password"
	EmailParamPhone                 EmailParamField = "phone_number"
	EmailParamOrgName               EmailParamField = "org_name"
	EmailParamOrgURL                EmailParamField = "org_url"
	EmailParamOrgAdminURL           EmailParamField = "org_admin_url"
	EmailParamCreatorURL            EmailParamField = "creator_url"
	EmailParamVerifyEmailLink       EmailParamField = "verify_email_link"
	EmailParamResetPasswordLink     EmailParamField = "reset_password_link"
	EmailParamSetPasswordLink       EmailParamField = "set_password_link"
	EmailParamInviteCreatorLink     EmailParamField = "invite_creator_link"
	EmailParamInviteUserLink        EmailParamField = "invite_user_link"
	EmailParamRejectionReason       EmailParamField = "rejection_reason"
	EmailParamPolicyURL             EmailParamField = "policy_url"
	EmailParamOpenEduSupportEmail   EmailParamField = "openedu_support_email"
	EmailParamOpenEduSupportPhone   EmailParamField = "openedu_support_phone"
	EmailParamOTPCode               EmailParamField = "otp"
	EmailParamRole                  EmailParamField = "role"
	EmailParamCourseURL             EmailParamField = "course_url"
	EmailParamCourseName            EmailParamField = "course_name"
	EmailParamCreatorName           EmailParamField = "creator_name"
	EmailParamZaloChannel           EmailParamField = "zalo_channel"
	EmailParamTelegramChannel       EmailParamField = "telegram_channel"
	EmailParamUserUrl               EmailParamField = "user_url"
	EmailParamAffiliateCode         EmailParamField = "affiliate_code"
	EmailParamPercentageRate        EmailParamField = "percentage_rate"
	EmailParamAffiliateLink         EmailParamField = "affiliate_link"
	EmailParamAffiliateDashboardUrl EmailParamField = "affiliate_dashboard_url"
	EmailParamPaymentAmount         EmailParamField = "payment_amount"
	EmailParamEvent                 EmailParamField = "event"
	EmailParamUser                  EmailParamField = "user"
	EmailParamUserToken             EmailParamField = "user_token"
	EmailParamOrg                   EmailParamField = "org"
	EmailParamCourse                EmailParamField = "course"
)

type EmailTemplate struct {
	Model
	OrgID   string        `json:"org_id" gorm:"not null;type:varchar(20)"`
	UserID  string        `json:"user_id"`
	User    *User         `json:"user"`
	Code    EmailCodeType `json:"code"`
	Name    string        `json:"name"`
	Subject string        `json:"subject" gorm:"type:varchar(1000)"`
	Html    string        `json:"html" gorm:"type:text"`
	Json    string        `json:"json" gorm:"type:text"`
}

type EmailTemplateQuery struct {
	ID             *string       `json:"id" form:"id"`
	Code           EmailCodeType `json:"code" form:"code"`
	Name           *string       `json:"name" form:"name"`
	OrgID          *string       `json:"org_id" form:"org_id"`
	UserID         *string       `json:"user_id" form:"user_id"`
	IncludeDeleted *bool         `json:"include_deleted" form:"include_deleted"`
}

type EmailTemplateDataParams struct {
	FullName            string `json:"full_name"`
	Email               string `json:"email"`
	Password            string `json:"password"`
	Phone               string `json:"phone_number"`
	OrgName             string `json:"org_name"`
	OrgURL              string `json:"org_url"`
	OrgAdminURL         string `json:"org_admin_url"`
	CreatorURL          string `json:"creator_url"`
	VerifyEmailLink     string `json:"verify_email_link"`
	ResetPasswordLink   string `json:"reset_password_link"`
	SetPasswordLink     string `json:"set_password_link"`
	InviteCreatorLink   string `json:"invite_creator_link"`
	InviteUserLink      string `json:"invite_user_link"`
	RejectionReason     string `json:"rejection_reason"`
	PolicyURL           string `json:"policy_url"`
	OpenEduSupportEmail string `json:"openedu_support_email"`
	OpenEduSupportPhone string `json:"openedu_support_phone"`
	OTP                 string `json:"otp"`
}

func (e EmailTemplateDataParams) IntoComm() communicationdto.EmailTemplateDataParams {
	return communicationdto.EmailTemplateDataParams{
		FullName:            e.FullName,
		Email:               e.Email,
		Password:            e.Password,
		Phone:               e.Phone,
		OrgName:             e.OrgName,
		OrgURL:              e.OrgURL,
		OrgAdminURL:         e.OrgAdminURL,
		CreatorURL:          e.CreatorURL,
		VerifyEmailLink:     e.VerifyEmailLink,
		ResetPasswordLink:   e.ResetPasswordLink,
		SetPasswordLink:     e.SetPasswordLink,
		InviteCreatorLink:   e.InviteCreatorLink,
		InviteUserLink:      e.InviteUserLink,
		RejectionReason:     e.RejectionReason,
		PolicyURL:           e.PolicyURL,
		OpenEduSupportEmail: e.OpenEduSupportEmail,
		OpenEduSupportPhone: e.OpenEduSupportPhone,
		OTP:                 e.OTP,
	}
}
