package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"regexp"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type Blog struct {
	Model
	OrgID            string      `json:"org_id" gorm:"type: varchar(20)"`
	AuthorID         string      `json:"author_id,omitempty" gorm:"type: varchar(20)"`
	BannerID         string      `json:"banner_id,omitempty" gorm:"type: varchar(20)"`
	Banner           *File       `json:"banner,omitempty" gorm:"-"`
	ImageDescription string      `json:"image_description,omitempty" gorm:"type:text"`
	Description      string      `json:"description,omitempty" gorm:"type:text"`
	Title            string      `json:"title,omitempty"`
	HashTagID        StringArray `json:"hashtag_id,omitempty" gorm:"type:jsonb"`
	Slug             string      `json:"slug,omitempty"`
	Content          string      `json:"content,omitempty" gorm:"type:text"`
	JsonContent      JSONB       `json:"json_content" gorm:"type:jsonb"`
	TimeRead         int16       `json:"time_read,omitempty"`
	Status           BlogStatus  `json:"status,omitempty" gorm:"default:draft"`
	ScheduleAt       int64       `gorm:"type:int8;not null;default:0" json:"schedule_at,omitempty"`
	BlogType         BlogType    `json:"blog_type"`
	AIBlogID         string      `json:"ai_blog_id,omitempty" gorm:"type:varchar(20)"`
	///Stats
	VoteCount    int64 `json:"vote_count,omitempty" gorm:"-"`
	CommentCount int64 `json:"comment_count,omitempty" gorm:"-"`
	ViewCount    int64 `json:"view_count,omitempty" gorm:"-"`
	// Handle version
	Cuid          string    `json:"cuid" gorm:"not null;type:varchar(20)"` // blog unique ID
	Version       int       `json:"version" gorm:"type:int8;default:0"`
	Latest        bool      `json:"latest" gorm:"default:true"`
	Props         BlogProps `json:"props,omitempty" gorm:"type:jsonb"`
	PubDate       int       `json:"pub_date" gorm:"type:int8;default:0"`
	PubRejectDate int       `json:"pub_reject_date" gorm:"type:int8;default:0"`
	IsPin         bool      `json:"is_pin" gorm:"type:boolean"`
	// Preloads
	Reviewing       *SimpleBlogStatus    `json:"reviewing,omitempty" gorm:"-"`
	Author          *SimpleUser          `json:"author,omitempty" gorm:"-"`
	Org             *Organization        `json:"org,omitempty"`
	AIGeneratedInfo *SimpleAIBlogRewrite `json:"ai_generated_info,omitempty" gorm:"-"`
	Published       []*SimpleBlogStatus  `json:"published,omitempty" gorm:"-"`
	UnPublished     []*SimpleBlogStatus  `json:"unpublished,omitempty" gorm:"-"`
	Categories      []*SimpleCategory    `json:"categories" gorm:"-"`
	Hashtag         []*SimpleHashtag     `json:"hashtag" gorm:"-"`
	// Flag
	IsAIGenerated bool `json:"is_ai_generated" gorm:"type:boolean"`
	IsOriginDraft bool `json:"is_origin_draft" gorm:"type:boolean; default:false"`

	Locale string `json:"locale" gorm:"default:'en'"`
}

type BlogProps struct {
	PreviousVersion int    `json:"previous_version,omitempty"`
	PreviousID      string `json:"previous_id,omitempty"`

	RejectOrgReason string `json:"reject_org_reason"`
}

func (j BlogProps) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *BlogProps) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type SimpleBlog struct {
	Model
	OrgID            string               `json:"org_id"`
	AuthorID         string               `json:"author_id,omitempty"`
	BannerID         string               `json:"banner_id,omitempty"`
	ImageDescription string               `json:"image_description,omitempty"`
	Cuid             string               `json:"cuid"`
	Title            string               `json:"title,omitempty"`
	Slug             string               `json:"slug,omitempty"`
	Description      string               `json:"description,omitempty"`
	Content          string               `json:"content,omitempty" `
	VoteCount        int64                `json:"vote_count,omitempty" `
	JsonContent      JSONB                `json:"json_content"`
	CommentCount     int64                `json:"comment_count,omitempty"`
	TimeRead         int16                `json:"time_read,omitempty"`
	Status           BlogStatus           `json:"status,omitempty"`
	Latest           bool                 `json:"latest" `
	BlogType         BlogType             `json:"blog_type"`
	Version          int                  `json:"version"`
	Reviewing        *SimpleBlogStatus    `json:"reviewing_blog,omitempty"`
	Banner           *File                `json:"banner,omitempty"`
	AIGeneratedInfo  *SimpleAIBlogRewrite `json:"ai_generated_info,omitempty"`
	Author           *SimpleUser          `json:"author,omitempty" `
	Org              *Organization        `json:"org,omitempty" `
	Published        []*SimpleBlogStatus  `json:"published_blog,omitempty" `
	UnPublished      []*SimpleBlogStatus  `json:"unpublished,omitempty" `
	Categories       []*SimpleCategory    `json:"categories"`
	Hashtag          []*SimpleHashtag     `json:"hashtag" `
	IsAIGenerated    bool                 `json:"is_ai_generated" gorm:"type:boolean"`
	Locale           string               `json:"locale"`
	AIGenerateStatus AIStatus             `json:"ai_generate_status"`
}

type SimpleBlogStatus struct {
	ID            string     `json:"id,omitempty"`
	Title         string     `json:"title,omitempty"`
	Version       int        `json:"version,omitempty"`
	Latest        bool       `json:"latest,omitempty"`
	Status        BlogStatus `json:"status,omitempty"`
	PubDate       int        `json:"pub_date,omitempty"`
	PubRejectDate int        `json:"pub_reject_date,omitempty"`
}

func (b *Blog) ToSimpleBlogStatus() *SimpleBlogStatus {
	return &SimpleBlogStatus{
		ID:            b.ID,
		Title:         b.Title,
		Version:       b.Version,
		Latest:        b.Latest,
		Status:        b.Status,
		PubDate:       b.PubDate,
		PubRejectDate: b.PubRejectDate,
	}
}

func (b *Blog) GetUserID() string {
	return b.AuthorID
}

func (b Blog) GetID() string {
	return b.ID
}

func (b Blog) GetSlug() string {
	return b.Slug
}

func (b *Blog) IsStatusDraft() bool {
	return b.Status == BlogStatusDraft
}

func (b *Blog) IsStatusReviewing() bool {
	return b.Status == BlogStatusReviewing
}

func (b *Blog) Sanitize() *SimpleBlog {
	return &SimpleBlog{
		Model:            b.Model,
		OrgID:            b.OrgID,
		Title:            b.Title,
		Slug:             b.Slug,
		AuthorID:         b.AuthorID,
		BannerID:         b.BannerID,
		VoteCount:        b.VoteCount,
		CommentCount:     b.CommentCount,
		TimeRead:         b.TimeRead,
		Status:           b.Status,
		Content:          b.Content,
		JsonContent:      b.JsonContent,
		Latest:           b.Latest,
		Cuid:             b.Cuid,
		Description:      b.Description,
		Version:          b.Version,
		BlogType:         b.BlogType,
		Reviewing:        b.Reviewing,
		Published:        b.Published,
		UnPublished:      b.UnPublished,
		Categories:       b.Categories,
		Hashtag:          b.Hashtag,
		ImageDescription: b.ImageDescription,
		Banner:           b.Banner,
		Author:           b.Author,
		Org:              b.Org,
		IsAIGenerated:    b.IsAIGenerated,
		AIGeneratedInfo:  b.AIGeneratedInfo,
		Locale:           b.Locale,
	}
}

type BlogQuery struct {
	ID                *string      `json:"id,omitempty" form:"id"`
	OrgID             *string      `json:"org_id" form:"org_id"`
	Cuid              *string      `json:"cuid" form:"cuid"`
	IDIn              []string     `json:"id_in,omitempty" form:"id_in"`
	CuidIn            []string     `json:"cuid_in" form:"cuid_in"`
	IDOrSlug          *string      `json:"id_or_slug,omitempty" form:"slug"`
	AuthorID          *string      `json:"author_id,omitempty" form:"author_id"`
	CategoryID        []string     `json:"category_id,omitempty" form:"category_id"`
	HashTagID         []string     `json:"hashtag_id,omitempty" form:"hashtag_id"`
	BannerID          *string      `json:"banner_id,omitempty" form:"banner_id"`
	Status            *BlogStatus  `json:"status,omitempty" form:"status"`
	OrStatus          *BlogStatus  `json:"or_status,omitempty" form:"or_status"`
	OrStatusIn        []BlogStatus `json:"or_status_in,omitempty" form:"or_status_in"`
	NotStatus         *BlogStatus  `json:"not_status,omitempty" form:"not_status"`
	StatusIn          []BlogStatus `json:"status_in,omitempty" form:"status_in"`
	StatusNotIn       []BlogStatus `json:"status_not_in,omitempty" form:"status_not_in"`
	OrSlug            *string      `json:"or_slug,omitempty" form:"or_slug"`
	IncludeDeleted    *bool        `json:"include_deleted,omitempty" form:"include_deleted"`
	ScheduleAtLower   *int64       `json:"schedule_at_lower,omitempty" form:"schedule_at"`
	ScheduleAtGreater *int64       `json:"schedule_at_greater,omitempty" form:"schedule_at"`
	Latest            *bool        `json:"latest" form:"latest"`
	BlogType          *BlogType    `json:"blog_type" form:"blog_type"`
	IsPin             *bool        `json:"is_pin" form:"is_pin"`
	IsAIGenerated     *bool        `json:"is_ai_generated" form:"is_ai_generated"`
	IsOriginDraft     *bool        `json:"is_origin_draft" form:"is_origin_draft"`
	Locale            *string      `json:"locale" form:"locale"`
	BaseSearchQuery
}

func (query *BlogQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Cuid != nil {
		qb = qb.Where("cuid = ?", *query.Cuid)
	}

	if len(query.CuidIn) > 0 {
		qb = qb.Where("cuid IN (?)", query.CuidIn)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)

	}

	if query.IDOrSlug != nil {
		qb = qb.Where("(id = ? or slug = ?)", *query.IDOrSlug, *query.IDOrSlug)
	}

	if query.AuthorID != nil {
		qb = qb.Where("author_id = ?", *query.AuthorID)
	}

	if len(query.CategoryID) > 0 {
		qb = qb.Where("category_id @> ?", StringArray(query.CategoryID))
	}

	if len(query.HashTagID) > 0 {
		qb = qb.Where("hashtag_id @> ?", StringArray(query.HashTagID))
	}

	if query.BannerID != nil {
		qb = qb.Where("banner_id = ?", *query.BannerID)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.OrStatus != nil {
		qb = qb.Or("status = ?", *query.OrStatus)
	}

	if query.OrSlug != nil {
		qb = qb.Or("slug = ?", *query.OrSlug)
	}

	if query.IsPin != nil {
		qb = qb.Where("is_pin = ?", *query.IsPin)
	}

	if query.IsAIGenerated != nil {
		qb = qb.Where("is_ai_generated = ?", *query.IsAIGenerated)
	}

	if len(query.OrStatusIn) > 0 {
		qb = qb.Or("status IN (?)", query.OrStatusIn)
	}

	if query.NotStatus != nil {
		qb = qb.Where("status != ?", *query.NotStatus)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", query.StatusIn)

	}

	if len(query.StatusNotIn) > 0 {
		qb = qb.Where("status NOT IN (?)", query.StatusNotIn)

	}

	if query.ScheduleAtLower != nil {
		qb = qb.Where("schedule_at < ?", *query.ScheduleAtLower)
	}

	if query.ScheduleAtGreater != nil {
		qb = qb.Where("schedule_at > ?", *query.ScheduleAtGreater)
	}

	if query.BlogType != nil {
		qb = qb.Where("blog_type = ?", *query.BlogType)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.Latest != nil {
		qb = qb.Where("latest = ?", *query.Latest)
	}

	if query.IsOriginDraft != nil {
		qb = qb.Where("is_origin_draft = ?", *query.IsOriginDraft)
	}

	if query.Locale != nil {
		qb = qb.Where("locale = ?", *query.Locale)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, &Blog{}, nil)
	}

	return qb
}

func (b *Blog) ProcessBlogContentBunnyUrl() {
	regexStr := fmt.Sprintf(`<div\s+src="(%s/(\d+)/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}))"\s+data-type="video"\s*></div>`, setting.UploadSetting.UploadBunnyIFrameURL)
	re := regexp.MustCompile(regexStr)

	matches := re.FindAllStringSubmatch(b.Content, -1)

	for _, match := range matches {
		fullMatch := match[0]
		originalURL := match[1]
		libraryID := match[2]
		videoID := match[3]

		if libraryID == setting.UploadSetting.UploadPrivateBunnyLibraryID {
			nowTime := time.Now()
			expireTime := nowTime.Add(time.Duration(setting.UploadSetting.UploadBunnyIFrameTokenExpireIn) * timeUnit).UnixMilli()
			expireTimeStr := fmt.Sprintf("%d", expireTime)

			iFrameAuthKey := setting.UploadSetting.UploadPrivateBunnyIFrameAuthKey
			signInput := iFrameAuthKey + videoID + expireTimeStr
			sha256Hash := util.HashSHA256(signInput)

			signBunnyIFrame := fmt.Sprintf("%s?token=%s&expires=%s", originalURL, sha256Hash, expireTimeStr)

			newDiv := strings.Replace(fullMatch, originalURL, signBunnyIFrame, 1)
			b.Content = strings.Replace(b.Content, fullMatch, newDiv, 1)
		}
	}
}

func (r *BlogRepository) Create(b *Blog, trans *gorm.DB) error {
	return create(BlogTbl, b, trans)
}

func (r *BlogRepository) CreateMany(b []*Blog, trans *gorm.DB) error {
	return createMany(BlogTbl, b, trans)
}

func (r *BlogRepository) Update(b *Blog, trans *gorm.DB) error {
	return update(BlogTbl, b, trans)
}

func (r *BlogRepository) FindByID(id string, options *FindOneOptions) (*Blog, error) {
	return findByID[Blog](BlogTbl, id, options)
}

func (r *BlogRepository) FindOne(query *BlogQuery, options *FindOneOptions) (*Blog, error) {
	shouldPreloadCategories := false
	shouldPreloadHashtag := false
	shouldPreloadUser := false

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsCategories) {
		shouldPreloadCategories = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsCategories)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsHashTag) {
		shouldPreloadHashtag = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsHashTag)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsUser) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsUser)
	}

	blog, err := findOne[Blog](BlogTbl, query, options)
	if err != nil {
		return nil, err
	}

	if shouldPreloadCategories {
		if preErr := preloadBlogCategories([]*Blog{blog}); preErr != nil {
			return blog, preErr
		}
	}

	if shouldPreloadHashtag {
		if preErr := preloadBlogHashTag([]*Blog{blog}); preErr != nil {
			return blog, preErr
		}
	}

	if shouldPreloadUser {
		if preErr := preloadUser([]*Blog{blog}); preErr != nil {
			return blog, preErr
		}

	}

	if blog.BannerID != "" {
		if banner, tErr := Repository.File.FindByID(blog.BannerID, nil); tErr != nil {
			if !errors.Is(tErr, gorm.ErrRecordNotFound) {
				return nil, errors.New("Find banner failed: " + tErr.Error())
			}
		} else {
			blog.Banner = banner
		}
	}
	return blog, nil
}

func (r *BlogRepository) FindMany(query *BlogQuery, options *FindManyOptions) ([]*Blog, error) {
	shouldPreloadReviewing := false
	shouldPreloadPublished := false
	shouldPreloadCategories := false
	shouldPreloadHashtag := false
	shouldPreloadUser := false
	shouldPreloadUnPublished := false

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadReviewing) {
		shouldPreloadReviewing = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadReviewing)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadPublished) {
		shouldPreloadPublished = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadPublished)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsCategories) {
		shouldPreloadCategories = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsCategories)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsHashTag) {
		shouldPreloadHashtag = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsHashTag)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsUser) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsUser)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadUnPublished) {
		shouldPreloadUnPublished = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadUnPublished)
	}

	blogs, err := findMany[Blog](BlogTbl, query, options)
	if err != nil {
		return nil, err
	}

	if shouldPreloadReviewing {
		if preErr := preloadReviewingBlog(blogs); preErr != nil {
			return blogs, preErr
		}
	}

	if shouldPreloadPublished {
		if preErr := preloadPublishedBlog(blogs); preErr != nil {
			return blogs, preErr
		}
	}

	if shouldPreloadUnPublished {
		if preErr := preloadUnPublishedBlog(blogs); preErr != nil {
			return blogs, preErr
		}
	}

	if shouldPreloadCategories {
		if preErr := preloadBlogCategories(blogs); preErr != nil {
			return blogs, preErr
		}
	}

	if shouldPreloadHashtag {
		if preErr := preloadBlogHashTag(blogs); preErr != nil {
			return blogs, preErr
		}
	}

	if shouldPreloadUser {
		if preErr := preloadUser(blogs); preErr != nil {
			return blogs, preErr
		}
	}

	mBlogByBannerID := map[string]*Blog{}

	bannerIds := lo.Map(blogs, func(item *Blog, _ int) string {
		mBlogByBannerID[item.BannerID] = item
		return item.BannerID
	})

	if len(bannerIds) > 0 {
		if banners, fbErr := Repository.File.FindMany(&FileQuery{IDIn: bannerIds}, &FindManyOptions{}); fbErr != nil {
			return blogs, fbErr
		} else {
			lo.ForEach(banners, func(banner *File, _ int) {
				if blog, ok := mBlogByBannerID[banner.ID]; ok {
					blog.Banner = banner
				}
			})
		}
	}

	return blogs, nil

}

func (r *BlogRepository) FindPage(query *BlogQuery, options *FindPageOptions) ([]*Blog, *Pagination, error) {
	shouldPreloadReviewing := false
	shouldPreloadPublished := false
	shouldPreloadCategories := false
	shouldPreloadHashtag := false
	shouldPreloadUser := false
	shouldPreloadUnPublished := false
	shouldPreloadAIInfo := false

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadReviewing) {
		shouldPreloadReviewing = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadReviewing)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadPublished) {
		shouldPreloadPublished = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadPublished)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsCategories) {
		shouldPreloadCategories = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsCategories)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsHashTag) {
		shouldPreloadHashtag = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsHashTag)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsUser) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsUser)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadAIInfo) {
		shouldPreloadAIInfo = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadAIInfo)
	}

	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadUnPublished) {
		shouldPreloadUnPublished = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadUnPublished)
	}

	blogs, pagination, err := findPage[Blog](BlogTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if shouldPreloadReviewing {
		if preErr := preloadReviewingBlog(blogs); preErr != nil {
			return blogs, pagination, preErr
		}
	}

	if shouldPreloadPublished {
		if preErr := preloadPublishedBlog(blogs); preErr != nil {
			return blogs, pagination, preErr
		}
	}

	if shouldPreloadUnPublished {
		if preErr := preloadUnPublishedBlog(blogs); preErr != nil {
			return blogs, pagination, preErr
		}
	}

	if shouldPreloadCategories {
		if preErr := preloadBlogCategories(blogs); preErr != nil {
			return blogs, pagination, preErr
		}
	}

	if shouldPreloadHashtag {
		if preErr := preloadBlogHashTag(blogs); preErr != nil {
			return blogs, pagination, preErr
		}
	}

	if shouldPreloadUser {
		if preErr := preloadUser(blogs); preErr != nil {
			return blogs, pagination, preErr
		}
	}

	if shouldPreloadAIInfo {
		if preErr := preloadAIInfo(blogs); preErr != nil {
			return blogs, pagination, preErr
		}
	}

	mBlogByBannerID := map[string]*Blog{}

	bannerIds := lo.Map(blogs, func(item *Blog, _ int) string {
		mBlogByBannerID[item.BannerID] = item
		return item.BannerID
	})

	if len(bannerIds) > 0 {
		if banners, fbErr := Repository.File.FindMany(&FileQuery{IDIn: bannerIds}, &FindManyOptions{}); fbErr != nil {
			return blogs, pagination, fbErr
		} else {
			lo.ForEach(banners, func(banner *File, _ int) {
				if blog, ok := mBlogByBannerID[banner.ID]; ok {
					blog.Banner = banner
				}
			})
		}
	}

	return blogs, pagination, nil
}

func (r *BlogRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Blog](BlogTbl, id, trans)
}

func (r *BlogRepository) DeleteMany(query *BlogQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[Blog](BlogTbl, query, trans)
}

func (r *BlogRepository) UpdateMany(query *BlogQuery, data map[string]interface{}, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	result := qb.Table(GetTblName(BlogTbl)).Debug().Model(&Blog{}).Updates(data)

	err = result.Error
	return

}

func preloadReviewingBlog(blogs []*Blog) error {
	preIds := lo.Map(blogs, func(item *Blog, _ int) string {
		return item.Props.PreviousID
	})
	preIds = lo.Uniq(preIds)
	if len(preIds) > 0 {
		if preBlogs, preErr := Repository.Blog.FindMany(&BlogQuery{IDIn: preIds}, &FindManyOptions{}); preErr != nil {
			return errors.New("find previous blogs failed: " + preErr.Error())
		} else {
			lo.ForEach(blogs, func(item *Blog, _ int) {
				preBlog, ok := lo.Find(preBlogs, func(p *Blog) bool {
					return p.ID == item.Props.PreviousID
				})
				if ok && preBlog.Status == BlogStatusReviewing {
					item.Reviewing = preBlog.ToSimpleBlogStatus()
				}
			})
		}
	}
	return nil
}

func preloadUnPublishedBlog(blogs []*Blog) error {
	cuids := lo.Map(blogs, func(item *Blog, _ int) string {
		return item.Cuid
	})
	cuids = lo.Uniq(cuids)
	if len(cuids) > 0 {
		unpublished, findErr := Repository.Blog.FindMany(&BlogQuery{CuidIn: cuids, Status: util.NewT(BlogStatusUnPublish)}, &FindManyOptions{})
		if findErr != nil {
			return findErr
		}

		mapCuidBlog := lo.GroupBy(unpublished, func(item *Blog) string {
			return item.Cuid
		})
		for _, blog := range blogs {
			unpublished, ok := mapCuidBlog[blog.Cuid]
			if ok {
				blog.UnPublished = lo.Map(unpublished, func(item *Blog, _ int) *SimpleBlogStatus {
					return item.ToSimpleBlogStatus()
				})
			}
		}
	}

	return nil
}

func preloadPublishedBlog(blogs []*Blog) error {
	cuids := lo.Map(blogs, func(item *Blog, _ int) string {
		return item.Cuid
	})
	cuids = lo.Uniq(cuids)

	pubBlogs, findErr := Repository.Blog.FindMany(&BlogQuery{CuidIn: cuids, Status: util.NewT(BlogStatusPublish)}, &FindManyOptions{})
	if findErr != nil {
		return findErr
	}

	mapCuidBlog := lo.GroupBy(pubBlogs, func(item *Blog) string {
		return item.Cuid
	})

	for _, blog := range blogs {
		published, ok := mapCuidBlog[blog.Cuid]
		if ok {
			blog.Published = lo.Map(published, func(item *Blog, _ int) *SimpleBlogStatus {
				return item.ToSimpleBlogStatus()
			})
		}
	}
	return nil
}

func preloadBlogCategories(blogs []*Blog) error {
	cuids := lo.Map(blogs, func(item *Blog, _ int) string {
		return item.Cuid
	})
	if categories, findErr := Repository.CategoryRelation.GetSimpleCategoriesByEntities(BlogModelName, cuids, CategoriesField); findErr != nil {
		return findErr
	} else {
		lo.ForEach(blogs, func(item *Blog, _ int) {
			item.Categories = categories[item.Cuid]
		})
	}
	return nil
}

func preloadBlogHashTag(blogs []*Blog) error {
	cuids := lo.Map(blogs, func(item *Blog, _ int) string {
		return item.Cuid
	})

	if hashTags, findErr := Repository.HashtagRelation.GetSimpleHashtagByEntities(BlogModelName, cuids); findErr != nil {
		return findErr
	} else {
		lo.ForEach(blogs, func(item *Blog, _ int) {
			item.Hashtag = hashTags[item.Cuid]
		})
	}
	return nil
}

func preloadUser(blogs []*Blog) error {
	authorIDs := lo.Map(blogs, func(item *Blog, _ int) string {
		return item.AuthorID
	})
	authorIDs = lo.Uniq(authorIDs)
	if len(authorIDs) <= 0 {
		return nil
	}

	authors, err := Repository.User.FindMany(&UserQuery{IDIn: &authorIDs}, &FindManyOptions{})
	if err != nil {
		return err
	}

	authorsByIDs := make(map[string]*User)
	for _, author := range authors {
		authorsByIDs[author.ID] = author
	}

	for _, blog := range blogs {
		if author, found := authorsByIDs[blog.AuthorID]; found {
			blog.Author = author.ToSimpleUser()
		}
	}
	return nil
}

func preloadAIInfo(blogs []*Blog) error {
	blogIDs := []string{}

	for _, blog := range blogs {
		if blog.IsAIGenerated {
			blogIDs = append(blogIDs, blog.ID)
		}
	}

	if len(blogIDs) == 0 {
		return nil
	}

	aiBLog, err := Repository.AIBlogRewrite.FindMany(&AIBlogRewriteQuery{BlogIDIn: blogIDs, LinkNotNull: util.NewBool(true)}, &FindManyOptions{})
	if err != nil {
		return err
	}

	mapBlogIDAI := map[string]*AIBlogRewrite{}
	for _, item := range aiBLog {
		mapBlogIDAI[item.BlogID] = item
	}

	for _, blog := range blogs {
		if ai, ok := mapBlogIDAI[blog.ID]; ok {
			blog.AIGeneratedInfo = ai.ToSimple()
		}
	}

	return nil
}

func (r *BlogRepository) GetPublishedPersonalBlogsWithHighestVersion(query *BlogQuery, options *FindPageOptions) (entities []*Blog, pagination *Pagination, err error) {
	entitiesChan := make(chan []*Blog)
	countChan := make(chan int64)
	errorChan := make(chan error)

	//Preloads
	shouldPreloadCategories := false
	shouldPreloadHashtag := false
	shouldPreloadUser := false

	if options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsCategories) {
		shouldPreloadCategories = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsCategories)
	}

	if options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsHashTag) {
		shouldPreloadHashtag = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsHashTag)
	}

	if options.Preloads != nil && lo.Contains(options.Preloads, BlogPreloadsUser) {
		shouldPreloadUser = true
		options.Preloads = util.RemoveElement(options.Preloads, BlogPreloadsUser)
	}

	//query.Status = util.NewT(BlogStatusPublish)
	query.BlogType = util.NewT(BlogTypePersonal)
	//options.Sort = append(options.Sort, "cuid, version DESC")

	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		subQuery := DB.Table(GetTblName(BlogTbl)).Debug().
			Select("DISTINCT ON (cuid) *").
			Where("status != 'draft'").
			Order("cuid, version DESC")

		qb := ApplyQueryOptions(DB, query, options)
		result := qb.Table("(?) as sub", subQuery).Debug().
			Select("*").
			Find(&entities)

		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		var c int64
		qb := ApplyQueryOptions(DB, query, options)
		result := qb.Table(GetTblName(BlogTbl)).Debug().
			Distinct("cuid").
			Count(&c)

		cErr := result.Error
		if cErr != nil {
			errorChan <- cErr
			return
		}
		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	pagination = NewPagination(options.Page, options.PerPage, int(entityCount))

	if shouldPreloadCategories {
		if preErr := preloadBlogCategories(entities); preErr != nil {
			return entities, pagination, preErr
		}
	}

	if shouldPreloadHashtag {
		if preErr := preloadBlogHashTag(entities); preErr != nil {
			return entities, pagination, preErr
		}
	}

	if shouldPreloadUser {
		if preErr := preloadUser(entities); preErr != nil {
			return entities, pagination, preErr
		}
	}

	mBlogByBannerID := map[string]*Blog{}

	bannerIds := lo.Map(entities, func(item *Blog, _ int) string {
		mBlogByBannerID[item.BannerID] = item
		return item.BannerID
	})

	if len(bannerIds) > 0 {
		if banners, fbErr := Repository.File.FindMany(&FileQuery{IDIn: bannerIds}, &FindManyOptions{}); fbErr != nil {
			return entities, pagination, fbErr
		} else {
			lo.ForEach(banners, func(banner *File, _ int) {
				if blog, ok := mBlogByBannerID[banner.ID]; ok {
					blog.Banner = banner
				}
			})
		}
	}
	return entities, pagination, nil
}

func (r *BlogRepository) Count(query *BlogQuery) (int64, error) {
	return count[Blog](BlogTbl, query)
}

func (r *BlogRepository) CountByCuid(query *BlogQuery, trans *gorm.DB) (int64, error) {
	var tx *gorm.DB
	var err error
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)

	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	var c int64
	result := qb.Table(GetTblName(BlogTbl)).Debug().
		Distinct("cuid").
		Count(&c)

	err = result.Error
	if err != nil {
		return 0, err
	}

	return c, err
}
