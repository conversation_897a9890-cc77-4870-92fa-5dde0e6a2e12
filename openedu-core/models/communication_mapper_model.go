package models

import communicationdto "openedu-core/pkg/communication/dto"

func (m Model) IntoComm() communicationdto.Model {
	return communicationdto.Model{
		ID:       m.ID,
		CreateAt: m.Create<PERSON>t,
		DeleteAt: m.Delete<PERSON>t,
		UpdateAt: m.UpdateAt,
	}
}

func (o FindPageOptions) IntoComm() *communicationdto.FindPageOptions {
	return &communicationdto.FindPageOptions{
		Preloads:       o.Preloads,
		CustomPreloads: o.CustomPreloads,
		Sort:           o.Sort,
		Page:           o.Page,
		PerPage:        o.PerPage,
	}
}

func (o FindOneOptions) IntoComm() *communicationdto.FindOneOptions {
	return &communicationdto.FindOneOptions{
		Preloads:       o.Preloads,
		CustomPreloads: o.CustomPreloads,
		Sort:           o.Sort,
	}
}

func (o FindManyOptions) IntoComm() *communicationdto.FindManyOptions {
	return &communicationdto.FindManyOptions{
		Preloads:       o.Preloads,
		CustomPreloads: o.CustomPreloads,
		Sort:           o.Sort,
		Limit:          o.Limit,
		Offset:         o.Offset,
	}
}

func (p Pagination) IntoComm() communicationdto.Pagination {
	return communicationdto.Pagination{
		Page:       p.Page,
		PerPage:    p.PerPage,
		TotalPages: p.TotalPages,
		TotalItems: p.TotalItems,
	}
}

func (js JSONB) IntoComm() communicationdto.JSONB {
	return communicationdto.JSONB(js)
}

func (sa StringArray) IntoComm() communicationdto.StringArray {
	return communicationdto.StringArray(sa)
}

func (u *User) IntoComm() *communicationdto.User {
	if u == nil {
		return nil
	}
	return &communicationdto.User{
		Model:              u.Model.IntoComm(),
		Username:           u.Username,
		Email:              u.Email,
		Phone:              u.Phone,
		Active:             u.Active,
		Blocked:            u.Blocked,
		CoverPhoto:         u.CoverPhoto,
		Avatar:             u.Avatar,
		DisplayName:        u.DisplayName,
		Headline:           u.Headline,
		About:              u.About,
		Position:           u.Position,
		RequireSetPassword: u.RequireSetPassword,
	}
}

func (o *Organization) IntoComm() *communicationdto.Organization {
	if o == nil {
		return nil
	}
	return &communicationdto.Organization{
		Model:       o.Model.IntoComm(),
		User:        o.User.IntoComm(),
		ThumbnailID: o.ThumbnailID,
		Schema:      o.Schema,
		Name:        o.Name,
		Domain:      o.Domain,
		AltDomain:   o.AltDomain,
		Active:      o.Active,
	}
}

func (e EventType) IntoComm() communicationdto.EventType {
	return communicationdto.EventType(e)
}

func (e EmailCodeType) IntoComm() communicationdto.EmailCodeType {
	return communicationdto.EmailCodeType(e)
}

func (c *Course) IntoComm() *communicationdto.Course {
	if c == nil {
		return nil
	}
	return &communicationdto.Course{
		Model:       c.Model.IntoComm(),
		Cuid:        c.Cuid,
		Name:        c.Name,
		Slug:        c.Slug,
		Description: c.Description,
		ShortDesc:   c.ShortDesc,
		ThumbnailID: c.ThumbnailID,
		LearnMethod: c.LearnMethod,
		UserID:      c.UserID,
		Props:       c.Props.IntoComm(),
		PubDate:     c.PubDate,
		StartDate:   c.StartDate,
		EndDate:     c.EndDate,
	}
}

func (cp CourseProps) IntoComm() communicationdto.CourseProps {
	return communicationdto.CourseProps{
		SupportChannel:  cp.SupportChannel.IntoComm(),
		DefaultLanguage: cp.DefaultLanguage,
		TelegramChannel: cp.TelegramChannel,
		PrivateChannels: cp.PrivateChannels.IntoComm(),
	}
}

func (vt *VerbType) IntoComm() *communicationdto.VerbType {
	if vt == nil {
		return nil
	}
	result := communicationdto.VerbType(*vt)
	return &result
}

func (mn *ModelName) IntoComm() *communicationdto.ModelName {
	if mn == nil {
		return nil
	}
	result := communicationdto.ModelName(*mn)
	return &result
}

func (tc *TrackingContext) IntoComm() *communicationdto.Context {
	if tc == nil {
		return nil
	}
	result := communicationdto.Context(*tc)
	return &result
}

func (tq *TrackingQuery) IntoComm() *communicationdto.TrackingQuery {
	if tq == nil {
		return nil
	}
	return &communicationdto.TrackingQuery{
		ID:                tq.ID,
		ActorID:           tq.ActorID,
		ActorIDIn:         tq.ActorIDIn,
		Verb:              tq.Verb.IntoComm(),
		Object:            tq.Object.IntoComm(),
		ObjectID:          tq.ObjectID,
		ObjectIDIn:        tq.ObjectIDIn,
		Context:           tq.Context.IntoComm(),
		ContextValue:      tq.ContextValue,
		IncludeDeleted:    tq.IncludeDeleted,
		TrackingDateStart: tq.TrackingDateStart,
		TrackingDateEnd:   tq.TrackingDateEnd,
		IDIn:              tq.IDIn,
		IsValid:           tq.IsValid,
	}
}

func (mq *MessageQuery) IntoComm() *communicationdto.MessageQuery {
	if mq == nil {
		return nil
	}
	return &communicationdto.MessageQuery{
		ID:             mq.ID,
		UserID:         mq.UserID,
		UserIDIn:       mq.UserIDIn,
		ConversationID: mq.ConversationID,
		ContextID:      mq.ContextID,
		SenderID:       mq.SenderID,
		IsEdited:       mq.IsEdited,
		IsAI:           mq.IsAI,
		IDIn:           mq.IDIn,
		SenderIDIn:     mq.SenderIDIn,
		OrgID:          mq.OrgID,
		CreateAtLte:    mq.CreateAtLte,
		CreateAtGte:    mq.CreateAtGte,
		IDNotIn:        mq.IDNotIn,
		CreateAtLt:     mq.CreateAtLt,
		CreateAtGt:     mq.CreateAtGt,
		IncludeDeleted: mq.IncludeDeleted,
	}
}

func (etq *EmailTemplateQuery) IntoComm() *communicationdto.EmailTemplateQuery {
	if etq == nil {
		return nil
	}
	return &communicationdto.EmailTemplateQuery{
		ID:             etq.ID,
		Code:           communicationdto.EmailCodeType(etq.Code),
		Name:           etq.Name,
		OrgID:          etq.OrgID,
		UserID:         etq.UserID,
		IncludeDeleted: etq.IncludeDeleted,
	}
}
