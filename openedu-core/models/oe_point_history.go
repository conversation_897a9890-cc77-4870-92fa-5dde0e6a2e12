package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type OEPointStatus string

type PointSource string

const (
	OEPointStatusNew      OEPointStatus = "new"
	OEPointStatusReceived OEPointStatus = "received"
	OEPointStatusUsed     OEPointStatus = "used"
	OEPointStatusExpired  OEPointStatus = "expired"

	ReferralUserSource                   PointSource = "referral_user"
	RefereeUserSource                    PointSource = "referee_user"
	ReferralMilestoneSource              PointSource = "referral_milestone"
	ReferralStreakWeeklySource           PointSource = "referral_weekly_streak"
	ReferralStreakMonthlySource          PointSource = "referral_monthly_streak"
	ReferralFeatureDiscoveryCourseSource PointSource = "referral_feature_course_discovery"
	ReferralFeatureDiscoveryFiatSource   PointSource = "referral_feature_fiat_discovery"
	ReferralFeatureDiscoveryCryptoSource PointSource = "referral_feature_crypto_discovery"
	ReferralTimeBaseBonusSource          PointSource = "referral_time_base_bonus"
)

type OEPointHistory struct {
	Model
	UserID         string              `json:"user_id"`
	Status         OEPointStatus       `json:"status"`
	Amount         decimal.Decimal     `json:"amount" gorm:"type:numeric(19,4);not null;default:0"`
	Used           decimal.Decimal     `json:"used" gorm:"type:numeric(19,4);not null;default:0"`
	Remaining      decimal.Decimal     `json:"remaining" gorm:"type:numeric(19,4);not null;default:0"`
	ExpireDate     int                 `json:"expire_date" gorm:"default:0"`
	Props          OEPointHistoryProps `json:"props" gorm:"type:jsonb"`
	EntityType     ModelName           `json:"entity_type"`
	EntityID       string              `json:"entity_id"`
	Note           string              `json:"note"`
	OrgID          string              `json:"org_id"`
	NotifiedPeriod int                 `json:"notified_period" gorm:"default:0"`
	Source         PointSource         `json:"source"`
	ClaimDate      int                 `json:"claim_date" gorm:"default:0"`
	CampaignID     string              `json:"campaign_id"`

	Campaign *OEPointCampaign `json:"campaign"`
	User     *User            `json:"user"`
}

type OEPointHistoryQuery struct {
	ID                      *string        `json:"id" form:"id"`
	UserID                  *string        `json:"user_id" form:"user_id"`
	Status                  *OEPointStatus `json:"status" form:"status"`
	ExpireDateGte           *int           `json:"expire_date_gte" form:"expire_date_gte"`
	Deleted                 *bool          `json:"deleted"`
	ExpireDateLte           *int           `json:"expire_date_lte" form:"expire_date_lte"`
	EntityType              *ModelName     `json:"entity_type" form:"entity_type"`
	OrgID                   *string        `form:"org_id" form:"org_id"`
	EntityIDNotNull         *bool          `json:"entity_id_not_null" form:"entity_id_not_null"`
	RefFromUserIDNotNull    *bool          `json:"ref_from_user_id_not_null" form:"ref_from_user_id_not_null"`
	IncludeDeleted          *bool          `json:"include_deleted,omitempty" form:"include_deleted"`
	RefFromUserID           *string        `json:"ref_from_user_id" form:"ref_from_user_id"`
	MilestoneReachedNotNull *bool          `json:"milestone_reached_not_null" form:"milestone_reached_not_null"`
	Source                  *PointSource   `json:"source" form:"source"`
	SourceIn                []PointSource  `json:"source_in"`
	CampaignID              *string        `json:"campaign_id" form:"campaign_id"`
	CreateAtLte             *int           `json:"create_at_lte" form:"create_at_lte"`
	CreateAtGte             *int           `json:"create_at_gte" form:"create_at_gte"`
}

type OEPointHistoryProps struct {
	RefFromUserID    string `json:"ref_from_user_id"`
	UsedKey          string `json:"used_key"`
	Used             string `json:"used"`
	MilestoneReached int64  `json:"milestone_reached"`
	Note             string `json:"note"`
	Trigger          string `json:"trigger"`
	BonusFor         string `json:"bonus_for"`
}

type OEPointProps struct {
	Amount    decimal.Decimal `json:"amount"`
	Used      decimal.Decimal `json:"used"`
	Remaining decimal.Decimal `json:"remaining"`
}

func GetReferralUserSources() []PointSource {
	return []PointSource{
		ReferralUserSource,
		RefereeUserSource,
		ReferralMilestoneSource,
		ReferralStreakWeeklySource,
		ReferralStreakMonthlySource,
		ReferralFeatureDiscoveryCourseSource,
		ReferralFeatureDiscoveryFiatSource,
		ReferralFeatureDiscoveryCryptoSource,
		ReferralTimeBaseBonusSource,
	}
}

func (j OEPointHistoryProps) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *OEPointHistoryProps) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

func (query *OEPointHistoryQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.ExpireDateGte != nil {
		qb = qb.Where("expire_date >= ?", *query.ExpireDateGte)
	}

	if query.ExpireDateLte != nil {
		qb = qb.Where("expire_date <= ?", *query.ExpireDateLte)
	}

	if query.Deleted == nil || !*query.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.EntityType != nil {
		qb = qb.Where("entity_type = ?", *query.EntityType)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.RefFromUserIDNotNull != nil && *query.RefFromUserIDNotNull {
		qb = qb.Where("props->>'ref_from_user_id' is not null")
	}

	if query.EntityIDNotNull != nil && *query.EntityIDNotNull {
		qb = qb.Where("entity_id is not null")
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.RefFromUserID != nil {
		qb = qb.Where("props->>'ref_from_user_id' = ?", *query.RefFromUserID)
	}

	if query.RefFromUserIDNotNull != nil && *query.RefFromUserIDNotNull {
		qb = qb.Where("props->>'milestone_reached' is not null")
	}

	if query.Source != nil {
		qb = qb.Where("source = ?", *query.Source)
	}

	if query.SourceIn != nil {
		qb = qb.Where("source IN (?)", query.SourceIn)
	}

	if query.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *query.CampaignID)
	}

	if query.CreateAtLte != nil {
		qb = qb.Where("create_at < ?", *query.CreateAtLte)
	}

	if query.CreateAtGte != nil {
		qb = qb.Where("create_at > ?", *query.CreateAtGte)
	}

	return qb
}

// Create inserts a OEPointHistory to database, transaction is optional
func (r *OEPointHistoryRepository) Create(q *OEPointHistory, trans *gorm.DB) error {
	return create(OEPointHistoryTbl, q, trans)
}

func (r *OEPointHistoryRepository) CreateMany(qs []*OEPointHistory, trans *gorm.DB) error {
	return createMany(OEPointHistoryTbl, qs, trans)
}

// Update updates a OEPointHistory by ID in database, transaction is optional
func (r *OEPointHistoryRepository) Update(q *OEPointHistory, trans *gorm.DB) error {
	return update(OEPointHistoryTbl, q, trans)
}

// FindByID finds a OEPointHistory by ID with given find options, transaction is optional
func (r *OEPointHistoryRepository) FindByID(id string, options *FindOneOptions) (*OEPointHistory, error) {
	return findByID[OEPointHistory](OEPointHistoryTbl, id, options)
}

// FindOne finds one OEPointHistory with given find queries and options, transaction is optional
func (r *OEPointHistoryRepository) FindOne(query *OEPointHistoryQuery, options *FindOneOptions) (*OEPointHistory, error) {
	return findOne[OEPointHistory](OEPointHistoryTbl, query, options)
}

// FindMany finds OEPointHistorys by query conditions with give find options
func (r *OEPointHistoryRepository) FindMany(query *OEPointHistoryQuery, options *FindManyOptions) ([]*OEPointHistory, error) {
	return findMany[OEPointHistory](OEPointHistoryTbl, query, options)
}

// FindPage returns OEPointHistorys and pagination by query conditions and find options, transaction is optional
func (r *OEPointHistoryRepository) FindPage(query *OEPointHistoryQuery, options *FindPageOptions) ([]*OEPointHistory, *Pagination, error) {
	return findPage[OEPointHistory](OEPointHistoryTbl, query, options)
}

// Delete perform soft deletion to a OEPointHistory by ID, transaction is optional
func (r *OEPointHistoryRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OEPointHistory](OEPointHistoryTbl, id, trans)
}

// Count returns number of users by query conditions, transaction is optional
func (r *OEPointHistoryRepository) Count(query *OEPointHistoryQuery) (int64, error) {
	return count[OEPointHistory](OEPointHistoryTbl, query)
}

func (r *OEPointHistoryRepository) ExpirePoints(expireTime decimal.Decimal, chunkSize int) error {
	tx := DB.Begin()
	var err error
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	hTbl := GetTblName(OEPointHistoryTbl)
	wTbl := GetTblName(WalletTbl)
	query := fmt.Sprintf(`
CREATE TEMPORARY TABLE sum_points (
    user_id text,
    total_amount int
) ON COMMIT DROP;


CREATE TEMPORARY TABLE points_to_expire (
    id text,
    user_id text,
    amount numeric(19,4),
    remaining numeric(19,4)
) ON COMMIT DROP;


-- Lấy các point history cần update và lock chúng
INSERT INTO points_to_expire
SELECT ph.id, ph.user_id, ph.amount, ph.remaining
FROM %[1]s ph
WHERE ph.expire_date < %[2]s 
AND ph.status = '%[3]s'
LIMIT %[7]d
FOR UPDATE;

INSERT INTO sum_points (user_id, total_amount)
SELECT user_id, SUM(remaining) as total_amount
FROM points_to_expire
GROUP BY user_id;


-- Update status của point history
UPDATE %[1]s ph
SET "status" = '%[4]s'
FROM points_to_expire ep
WHERE ph.id = ep.id
AND ph.status = '%[3]s';



-- Update wallet balance
UPDATE %[5]s w
SET balance = w.balance - ap.total_amount
FROM sum_points ap
WHERE w.user_id = ap.user_id and w."type" = '%[6]s';
    `, hTbl, expireTime, OEPointStatusReceived, OEPointStatusExpired, wTbl, AssetTypePoint, chunkSize)

	// Execute queries
	err = tx.Debug().Exec(query).Error
	return nil
}

func (r *OEPointHistoryRepository) Update2Expired(expireDate int, batchSize int) ([]*OEPointHistory, error) {
	var records []*OEPointHistory
	tx := DB.Begin()
	hTbl := GetTblName(OEPointHistoryTbl)
	query := fmt.Sprintf(`
WITH batch_to_expires AS (SELECT * FROM %[1]s 
WHERE "status" = '%[2]s' and expire_date <= %[3]d
ORDER BY expire_date ASC
LIMIT %[5]d
FOR UPDATE SKIP LOCKED)

UPDATE %[1]s ph
SET "status" = '%[4]s', update_at = %[6]d
FROM batch_to_expires be
WHERE ph.id = be.id and ph."status" = '%[2]s'
RETURNING ph.*;
`, hTbl, OEPointStatusReceived, expireDate, OEPointStatusExpired, batchSize, int(time.Now().UnixMilli()))

	err := tx.Debug().Raw(query).Scan(&records).Error

	if r := recover(); r != nil {
		err = fmt.Errorf("panic: %v", r)
	}

	if err != nil {
		tx.Rollback()
		return nil, err
	}
	err = tx.Commit().Error
	return records, err
}

func (r *OEPointHistoryRepository) GetUserActivePoint(userID string) (decimal.Decimal, error) {
	tx := DB.Begin()
	var err error
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	hTbl := GetTblName(OEPointHistoryTbl)

	query := fmt.Sprintf(`
SELECT COALESCE(sum(remaining), 0) FROM %[1]s WHERE 
user_id = '%[2]s' and "status" = '%[3]s'
`, hTbl, userID, OEPointStatusReceived)

	var totalAmount decimal.Decimal
	err = tx.Debug().Raw(query).Scan(&totalAmount).Error
	if err != nil {
		return decimal.Zero, err
	}

	return totalAmount, nil
}

func (r *OEPointHistoryRepository) GetUserPointByStatusAndSources(userID string, status OEPointStatus, sources []PointSource) (OEPointProps, error) {
	tx := DB.Begin()
	var err error
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	hTbl := GetTblName(OEPointHistoryTbl)

	query := fmt.Sprintf(`
SELECT 
    COALESCE(sum(amount), 0) amount,
    COALESCE(sum(used), 0) used,
    COALESCE(sum(remaining), 0) remaining
FROM %[1]s WHERE 
user_id = '%[2]s' and "status" = '%[3]s'
`, hTbl, userID, status)

	if len(sources) == 0 {
		var slice []string
		for _, s := range sources {
			slice = append(slice, string(s))
		}
		strSource := strings.Join(slice, ",")
		query += fmt.Sprintf(" AND source in (%s)", strSource)
	}

	var amountProps OEPointProps
	err = tx.Debug().Raw(query).Scan(&amountProps).Error
	if err != nil {
		return OEPointProps{}, err
	}

	return amountProps, nil
}

func (r *OEPointHistoryRepository) GetUserActiveReferralEarnedPoint(userID string) (int, error) {
	tx := DB.Begin()
	var err error
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	hTbl := GetTblName(OEPointHistoryTbl)

	query := fmt.Sprintf(`
SELECT COALESCE(sum(amount), 0) FROM %[1]s WHERE 
user_id = '%[2]s' and "status" = '%[3]s' and "entity_type" = '%[4]s' and "entity_id" is not null and props->>'ref_from_user_id' is not null
`, hTbl, userID, OEPointStatusReceived, UserModelName)

	var totalAmount int
	err = tx.Debug().Raw(query).Scan(&totalAmount).Error
	if err != nil {
		return 0, err
	}

	return totalAmount, nil
}

func (r *OEPointHistoryRepository) UpdateNotifiedByPeriod(period int, batchSize int) ([]*OEPointHistory, error) {
	var records []*OEPointHistory
	tx := DB.Begin()
	hTbl := GetTblName(OEPointHistoryTbl)
	now := time.Now()
	startDate := now.AddDate(0, 0, period)
	endDate := now.AddDate(0, 0, period+1)
	query := fmt.Sprintf(`
WITH batch_to_notifies AS 
(SELECT * FROM %[1]s
WHERE expire_date >= %[2]d and expire_date <= %[3]d and notified_period <> %[4]d
ORDER BY expire_date ASC
LIMIT %[5]d
FOR UPDATE SKIP LOCKED)

UPDATE %[1]s ph
SET notified_period= %[4]d, update_at = %[6]d
FROM batch_to_notifies be
WHERE ph.id = be.id and ph.notified_period <> %[4]d
RETURNING ph.*;
`, hTbl, startDate.UnixMilli(), endDate.UnixMilli(), period, batchSize, now.UnixMilli())

	err := tx.Debug().Raw(query).Scan(&records).Error

	if r := recover(); r != nil {
		err = fmt.Errorf("panic: %v", r)
	}

	if err != nil {
		tx.Rollback()
		return nil, err
	}
	err = tx.Commit().Error
	return records, err
}
