package models

type QuizWithRelation struct {
	*Quiz
	RelatedEntityType QuizRelationEntity    `json:"related_entity_type"`
	RelatedEntityID   string                `json:"related_entity_id"`
	RelationType      QuizRelationType      `json:"relation_type"`
	TriggerConditions QuizTriggerConditions `json:"trigger_conditions"`
	SubmissionCount   int                   `json:"submission_count"`
}

type SimpleQuizWithRelation struct {
	*SimpleQuiz
	RelatedEntityType QuizRelationEntity    `json:"related_entity_type"`
	RelatedEntityID   string                `json:"related_entity_id"`
	RelationType      QuizRelationType      `json:"relation_type"`
	TriggerConditions QuizTriggerConditions `json:"trigger_conditions"`
}

func NewQuizWithRelation(quiz *Quiz, relation *QuizRelation) *QuizWithRelation {
	return &QuizWithRelation{
		Quiz:              quiz,
		RelatedEntityType: relation.RelatedEntityType,
		RelatedEntityID:   relation.RelatedEntityID,
		RelationType:      relation.RelationType,
		TriggerConditions: relation.TriggerConditions,
	}
}

func (q *QuizWithRelation) DeepCopy() *QuizWithRelation {
	clone := &QuizWithRelation{
		RelatedEntityType: q.RelatedEntityType,
		RelatedEntityID:   q.RelatedEntityID,
		RelationType:      q.RelationType,
		TriggerConditions: q.TriggerConditions,
	}
	if q.Quiz != nil {
		clone.Quiz = q.Quiz.DeepCopy()
	}
	return clone
}

func (q *QuizWithRelation) Sanitize() *QuizWithRelation {
	clone := q.DeepCopy()
	clone.Questions = nil
	return clone
}

func (q *QuizWithRelation) ToSimple() *SimpleQuizWithRelation {
	simple := &SimpleQuizWithRelation{
		RelatedEntityType: q.RelatedEntityType,
		RelatedEntityID:   q.RelatedEntityID,
		RelationType:      q.RelationType,
		TriggerConditions: q.TriggerConditions,
	}
	if q.Quiz != nil {
		simple.SimpleQuiz = q.Quiz.ToSimple()
	}
	return simple
}
