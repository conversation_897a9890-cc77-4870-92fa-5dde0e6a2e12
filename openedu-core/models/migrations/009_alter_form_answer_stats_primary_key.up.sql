ALTER TABLE public.openedu_form_answer_stats
    ADD COLUMN IF NOT EXISTS combined_hash TEXT;

ALTER TABLE public.openedu_form_answer_stats
    DROP CONSTRAINT IF EXISTS openedu_form_answer_stats_pk;

UPDATE public.openedu_form_answer_stats
SET combined_hash = md5(form_id || '_' || form_uid || '_' || question_uid || '_' || sub_question_uid || '_' || option_uid || '_' || text || '_' || file_ids)
WHERE combined_hash = '' OR combined_hash IS NULL;

ALTER TABLE public.openedu_form_answer_stats
    ADD CONSTRAINT openedu_form_answer_stats_pk
        UNIQUE (
                form_id,
                form_uid,
                question_uid,
                sub_question_uid,
                option_uid,
                combined_hash
            );
