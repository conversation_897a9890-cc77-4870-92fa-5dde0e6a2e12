package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"openedu-core/pkg/util"
	"sort"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type QuizQuestionType string

const (
	QuizQuestionTypeSingleChoice   = "single_choice"
	QuizQuestionTypeMultipleChoice = "multiple_choice"
	QuizQuestionTypeText           = "text"
	QuizQuestionTypeMatching       = "matching"
	QuizQuestionTypeOrdering       = "ordering"
	QuizQuestionTypeFillInBlanks   = "fill_in_blanks"
)

type QuizQuestion struct {
	Model
	QuizID          string               `json:"quiz_id"`
	Title           string               `json:"title"`
	Description     string               `json:"description"`
	Text            string               `json:"text"`
	Files           []*File              `json:"files" gorm:"-"`
	Type            QuizQuestionType     `json:"type"`
	Items           QuizQuestionItems    `json:"items" gorm:"type:jsonb"`
	CorrectItemSets QuizQuestionItemSets `json:"correct_item_sets,omitempty" gorm:"type:jsonb"`
	Explanation     string               `json:"explanation"`
	Points          int                  `json:"points"`
	Order           int                  `json:"order"`
	Settings        QuizQuestionSettings `json:"settings" gorm:"type:jsonb"`
}

type SimpleQuizQuestion struct {
	Model
	QuizID          string                      `json:"quiz_id"`
	Title           string                      `json:"title"`
	Description     string                      `json:"description"`
	Text            string                      `json:"text"`
	Files           []*SimpleFile               `json:"files"`
	Type            QuizQuestionType            `json:"type"`
	Items           []*SimpleQuizQuestionItem   `json:"items"`
	CorrectItemSets [][]*SimpleQuizQuestionItem `json:"correct_item_sets"`
	Explanation     string                      `json:"explanation"`
	Points          int                         `json:"points"`
	Order           int                         `json:"order"`
	Settings        QuizQuestionSettings        `json:"settings"`
}

func (q *QuizQuestion) DeepCopy() *QuizQuestion {
	return &QuizQuestion{
		Model:       q.Model,
		QuizID:      q.QuizID,
		Title:       q.Title,
		Description: q.Description,
		Text:        q.Text,
		Files: lo.Map(q.Files, func(file *File, _ int) *File {
			return file.DeepCopy()
		}),
		Type: q.Type,
		Items: lo.Map(q.Items, func(item *QuizQuestionItem, index int) *QuizQuestionItem {
			return item.DeepCopy()
		}),
		CorrectItemSets: lo.Map(q.CorrectItemSets, func(items QuizQuestionItems, index int) QuizQuestionItems {
			return lo.Map(items, func(item *QuizQuestionItem, index int) *QuizQuestionItem {
				return item.DeepCopy()
			})
		}),
		Explanation: q.Explanation,
		Points:      q.Points,
		Order:       q.Order,
		Settings:    q.Settings,
	}
}

func (q *QuizQuestion) Sanitize() *QuizQuestion {
	sanitizedQuestion := q.DeepCopy()
	sanitizedQuestion.Explanation = ""
	sanitizedQuestion.CorrectItemSets = nil
	return sanitizedQuestion
}

func (q *QuizQuestion) ToSimple() *SimpleQuizQuestion {
	return &SimpleQuizQuestion{
		Model:       q.Model,
		QuizID:      q.QuizID,
		Title:       q.Title,
		Description: q.Description,
		Text:        q.Text,
		Files: lo.Map(q.Files, func(file *File, _ int) *SimpleFile {
			return file.Sanitize()
		}),
		Type: q.Type,
		Items: lo.Map(q.Items, func(item *QuizQuestionItem, index int) *SimpleQuizQuestionItem {
			return item.ToSimple()
		}),
		CorrectItemSets: lo.Map(q.CorrectItemSets, func(items QuizQuestionItems, index int) []*SimpleQuizQuestionItem {
			return lo.Map(items, func(item *QuizQuestionItem, index int) *SimpleQuizQuestionItem {
				return item.ToSimple()
			})
		}),
		Explanation: q.Explanation,
		Points:      q.Points,
		Order:       q.Order,
		Settings:    q.Settings,
	}
}

type QuizQuestionSettings struct {
	TimeLimit Duration `json:"time_limit"`
}

func (s QuizQuestionSettings) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *QuizQuestionSettings) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

type QuizQuestionQuery struct {
	IDIn           []string
	QuizID         *string
	QuizIDIn       []string
	IncludeDeleted *bool
}

func (q *QuizQuestion) ShuffleChoices() {
	switch q.Type {
	case QuizQuestionTypeSingleChoice,
		QuizQuestionTypeMultipleChoice,
		QuizQuestionTypeOrdering:
		q.Items = lo.Shuffle(q.Items)

	case QuizQuestionTypeMatching:
		itemsGroupBySides := lo.GroupBy(q.Items, func(item *QuizQuestionItem) int {
			return item.Side
		})

		q.Items = []*QuizQuestionItem{}
		for _, items := range itemsGroupBySides {
			q.Items = append(q.Items, lo.Shuffle(items)...)
		}
	}

	for idx, item := range q.Items {
		item.Order = idx + 1
	}
}

func (q *QuizQuestion) IsCorrectAnswer(answer *QuizAnswer) bool {
	// If user didn't submit answers, it is incorrect answer
	if len(answer.AnsweredItemSets) == 0 || len(answer.AnsweredItemSets[0]) == 0 {
		return false
	}

	// If no correct answers is setting, user's answers is always correct
	if len(q.CorrectItemSets) == 0 || len(q.CorrectItemSets[0]) == 0 {
		return true
	}

	if len(answer.AnsweredItemSets) != len(q.CorrectItemSets) {
		return false
	}

	// Check answer is correct by question type
	switch q.Type {
	case QuizQuestionTypeSingleChoice,
		QuizQuestionTypeMultipleChoice:
		answeredItems := answer.AnsweredItemSets[0]
		correctItems := q.CorrectItemSets[0]
		if len(answeredItems) != len(correctItems) {
			return false
		}

		answeredItemsByIDs := map[string]*QuizQuestionItem{}
		for _, item := range answeredItems {
			answeredItemsByIDs[item.ID] = item
		}

		for _, correctItem := range correctItems {
			if _, found := answeredItemsByIDs[correctItem.ID]; !found {
				return false
			}
		}

	case QuizQuestionTypeText,
		QuizQuestionTypeFillInBlanks:
		answeredItems := answer.AnsweredItemSets[0]
		correctItems := q.CorrectItemSets[0]
		if len(answeredItems) != len(correctItems) {
			return false
		}

		// Sort answered items by order ASC, e.g 1, 2, 3
		sort.Slice(answeredItems, func(i, j int) bool {
			return answeredItems[i].Order < answeredItems[j].Order
		})

		// Sort correct items by order ASC, e.g 1, 2, 3
		sort.Slice(correctItems, func(i, j int) bool {
			return correctItems[i].Order < correctItems[j].Order
		})

		for idx, correctItem := range correctItems {
			if correctItem.Text != answeredItems[idx].Text {
				return false
			}
		}

	case QuizQuestionTypeOrdering:
		answeredItems := answer.AnsweredItemSets[0]
		correctItems := q.CorrectItemSets[0]
		if len(answeredItems) != len(correctItems) {
			return false
		}

		// Sort answered items by order ASC, e.g 1, 2, 3
		sort.Slice(answeredItems, func(i, j int) bool {
			return answeredItems[i].Order < answeredItems[j].Order
		})

		// Sort correct items by order ASC, e.g 1, 2, 3
		sort.Slice(correctItems, func(i, j int) bool {
			return correctItems[i].Order < correctItems[j].Order
		})

		for idx := 0; idx < len(answeredItems); idx++ {
			if answeredItems[idx].ID != correctItems[idx].ID {
				return false
			}
		}

	case QuizQuestionTypeMatching:
		seenCorrectItemCombinedIDs := map[string]struct{}{}
		for _, correctItems := range q.CorrectItemSets {
			itemIDs := lo.Map(correctItems, func(item *QuizQuestionItem, _ int) string {
				return item.ID
			})
			sort.Strings(itemIDs)
			seenCorrectItemCombinedIDs[strings.Join(itemIDs, "__")] = struct{}{}
		}

		for _, answeredItems := range answer.AnsweredItemSets {
			itemIDs := lo.Map(answeredItems, func(item *QuizQuestionItem, _ int) string {
				return item.ID
			})
			sort.Strings(itemIDs)
			if _, ok := seenCorrectItemCombinedIDs[strings.Join(itemIDs, "__")]; !ok {
				return false
			}
		}
	}
	return true
}

func (query *QuizQuestionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.QuizID != nil {
		qb = qb.Where("quiz_id = ?", *query.QuizID)
	}

	if len(query.QuizIDIn) > 0 {
		qb = qb.Where("quiz_id IN (?)", query.QuizIDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a Quiz Question to database, transaction is optional
func (r *QuizQuestionRepository) Create(question *QuizQuestion, trans *gorm.DB) error {
	if err := create(QuizQuestionTbl, question, trans); err != nil {
		return err
	}
	if len(question.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(QuizQuestionModelName, question.ID, FilesField, question.Files); fErr != nil {
			return fErr
		}
	}
	for _, item := range question.Items {
		if item.FileID == "" {
			continue
		}
		if fErr := Repository.FileRelation.AddFiles(QuizQuestionItemModelName, item.ID, FileField, []*File{{Model: Model{ID: item.FileID}}}); fErr != nil {
			return fErr
		}
	}
	return nil
}

func (r *QuizQuestionRepository) CreateMany(questions []*QuizQuestion, trans *gorm.DB) error {
	if err := createMany(QuizQuestionTbl, questions, trans); err != nil {
		return err
	}
	for _, question := range questions {
		if len(question.Files) > 0 {
			if fErr := Repository.FileRelation.AddFiles(QuizQuestionModelName, question.ID, util.FilesField, question.Files); fErr != nil {
				return fErr
			}
		}
		for _, item := range question.Items {
			if item.FileID == "" {
				continue
			}
			if fErr := Repository.FileRelation.AddFiles(QuizQuestionItemModelName, item.ID, FileField, []*File{{Model: Model{ID: item.FileID}}}); fErr != nil {
				return fErr
			}
		}
	}
	return nil
}

// Update updates a Quiz Question by ID in database, transaction is optional
func (r *QuizQuestionRepository) Update(question *QuizQuestion, trans *gorm.DB) error {
	if err := update(QuizQuestionTbl, question, trans); err != nil {
		return err
	}
	if len(question.Files) > 0 {
		if fErr := Repository.FileRelation.AddFiles(QuizQuestionModelName, question.ID, FilesField, question.Files); fErr != nil {
			return fErr
		}
	}
	for _, item := range question.Items {
		if item.FileID == "" {
			continue
		}
		if fErr := Repository.FileRelation.AddFiles(QuizQuestionItemModelName, item.ID, FileField, []*File{{Model: Model{ID: item.FileID}}}); fErr != nil {
			return fErr
		}
	}
	return nil
}

// FindByID finds a Quiz Question by ID with given find options, transaction is optional
func (r *QuizQuestionRepository) FindByID(id string, options *FindOneOptions) (*QuizQuestion, error) {
	hasPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadItemFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.ItemsFilesField) {
		hasPreloadItemFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.ItemsFilesField)
	}

	question, err := findByID[QuizQuestion](QuizQuestionTbl, id, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadFiles {
		questionIDs := []string{question.ID}
		if filesByQuestionIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionModelName, questionIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			question.Files = filesByQuestionIDs[question.ID]
		}
	}

	if hasPreloadItemFiles {
		itemIDs := lo.Map(question.Items, func(item *QuizQuestionItem, _ int) string {
			return item.ID
		})
		if filesByItemIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionItemModelName, itemIDs, util.FileField); mErr != nil {
			return nil, mErr
		} else {
			for _, item := range question.Items {
				if len(filesByItemIDs[item.ID]) > 0 {
					item.File = filesByItemIDs[item.ID][0]
				}
			}
		}
	}
	return question, nil
}

// FindOne finds one Quiz Question with given find queries and options, transaction is optional
func (r *QuizQuestionRepository) FindOne(query *QuizQuestionQuery, options *FindOneOptions) (*QuizQuestion, error) {
	hasPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadItemFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.ItemsFilesField) {
		hasPreloadItemFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.ItemsFilesField)
	}

	question, err := findOne[QuizQuestion](QuizQuestionTbl, query, options)
	if err != nil {
		return nil, err
	}

	if hasPreloadFiles {
		questionIDs := []string{question.ID}
		if filesByQuestionIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionModelName, questionIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			question.Files = filesByQuestionIDs[question.ID]
		}
	}

	if hasPreloadItemFiles {
		itemIDs := lo.Map(question.Items, func(item *QuizQuestionItem, _ int) string {
			return item.ID
		})
		if filesByItemIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionItemModelName, itemIDs, util.FileField); mErr != nil {
			return nil, mErr
		} else {
			for _, item := range question.Items {
				if len(filesByItemIDs[item.ID]) > 0 {
					item.File = filesByItemIDs[item.ID][0]
				}
			}
		}
	}
	return question, nil
}

// FindMany finds Quiz Questions by query conditions with give find options
func (r *QuizQuestionRepository) FindMany(query *QuizQuestionQuery, options *FindManyOptions) ([]*QuizQuestion, error) {
	hasPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadItemFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.ItemsFilesField) {
		hasPreloadItemFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.ItemsFilesField)
	}

	questions, err := findMany[QuizQuestion](QuizQuestionTbl, query, options)
	if err != nil {
		return nil, err
	}
	if hasPreloadFiles {
		quizIDs := lo.Map(questions, func(question *QuizQuestion, _ int) string {
			return question.ID
		})
		if filesByQuestionIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionModelName, quizIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			lo.ForEach(questions, func(question *QuizQuestion, _ int) {
				question.Files = filesByQuestionIDs[question.ID]
			})
		}
	}

	if hasPreloadItemFiles {
		var itemIDs []string
		for _, question := range questions {
			for _, item := range question.Items {
				if item.FileID != "" {
					itemIDs = append(itemIDs, item.ID)
				}
			}
		}
		if filesByItemIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionItemModelName, itemIDs, util.FileField); mErr != nil {
			return nil, mErr
		} else {
			for _, question := range questions {
				for _, item := range question.Items {
					if len(filesByItemIDs[item.ID]) > 0 {
						item.File = filesByItemIDs[item.ID][0]
					}
				}
			}
		}
	}
	return questions, err
}

// FindPage returns Quiz Questions and pagination by query conditions and find options, transaction is optional
func (r *QuizQuestionRepository) FindPage(query *QuizQuestionQuery, options *FindPageOptions) ([]*QuizQuestion, *Pagination, error) {
	hasPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.FilesField) {
		hasPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.FilesField)
	}

	hasPreloadItemFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.ItemsFilesField) {
		hasPreloadItemFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.ItemsFilesField)
	}

	questions, pagination, err := findPage[QuizQuestion](QuizQuestionTbl, query, options)
	if err != nil {
		return nil, nil, err
	}
	if hasPreloadFiles {
		quizIDs := lo.Map(questions, func(question *QuizQuestion, _ int) string {
			return question.ID
		})
		if filesByQuestionIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionModelName, quizIDs, util.FilesField); mErr != nil {
			return nil, nil, mErr
		} else {
			lo.ForEach(questions, func(question *QuizQuestion, _ int) {
				question.Files = filesByQuestionIDs[question.ID]
			})
		}
	}

	if hasPreloadItemFiles {
		var itemIDs []string
		for _, question := range questions {
			for _, item := range question.Items {
				if item.FileID != "" {
					itemIDs = append(itemIDs, item.ID)
				}
			}
		}
		if filesByItemIDs, mErr := Repository.FileRelation.GetFilesByEntities(QuizQuestionItemModelName, itemIDs, util.FileField); mErr != nil {
			return nil, nil, mErr
		} else {
			for _, question := range questions {
				for _, item := range question.Items {
					if len(filesByItemIDs[item.ID]) > 0 {
						item.File = filesByItemIDs[item.ID][0]
					}
				}
			}
		}
	}
	return questions, pagination, err
}

// Delete perform soft deletion to a Quiz Question by ID, transaction is optional
func (r *QuizQuestionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[QuizQuestion](QuizQuestionTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *QuizQuestionRepository) DeleteMany(query *QuizQuestionQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[QuizQuestion](QuizQuestionTbl, query, trans)
}

// Count returns number of Quiz Questions by query conditions, transaction is optional
func (r *QuizQuestionRepository) Count(query *QuizQuestionQuery) (int64, error) {
	return count[QuizQuestion](QuizQuestionTbl, query)
}
