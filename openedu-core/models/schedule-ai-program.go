package models

import (
	"context"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type EventScheduleType string

const (
	EventCourse    EventScheduleType = "course"
	EventOnline    EventScheduleType = "online"
	EventOffline   EventScheduleType = "offline"
	EventTrainning EventScheduleType = "trainning"
)

type Schedule struct {
	Model
	Name        string           `json:"name"`
	Description string           `json:"description"`
	StartAt     int64            `json:"start_at"`
	EndAt       int64            `json:"end_at"`
	OrgID       string           `json:"org_id"`
	Events      []*EventSchedule `json:"events"`
}

type ScheduleQuery struct {
	ID             *string `json:"id" form:"id"`
	OrgID          *string `json:"org_id" form:"org_id"`
	IncludeDeleted *bool   `json:"include_deleted,omitempty" form:"include_deleted"`
}

func (q *ScheduleQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if q.ID != nil {
		qb = qb.Where("id = ?", *q.ID)
	}

	if q.OrgID != nil {
		qb = qb.Where("org_id = ?", *q.OrgID)
	}

	if q.IncludeDeleted == nil || !*q.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

type EventSchedule struct {
	Model
	ScheduleID  string            `json:"schedule_id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Location    string            `json:"location"`
	StartAt     int64             `json:"start_at"`
	EndAt       int64             `json:"end_at"`
	JoinLink    string            `json:"join_link"`
	EventType   EventScheduleType `json:"event_type"`
}

type EventScheduleQuery struct {
	ScheduleID      *string            `json:"schedule_id" form:"schedule_id"`
	StartAtGte      *int64             `json:"start_at_gte" form:"start_at_gte"`
	StartAtLte      *int64             `json:"start_at_lte" form:"start_at_lte"`
	EndAtGte        *int64             `json:"end_at_gte" form:"end_at_gte"`
	EndAtLte        *int64             `json:"end_at_lte" form:"end_at_lte"`
	EventType       *EventScheduleType `json:"event_type" form:"event_type"`
	IncludeDeleted  *bool              `json:"include_deleted,omitempty" form:"include_deleted"`
	BaseSearchQuery `json:"base_search_query" form:"base_search_query"`
}

func (q *EventScheduleQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if q.ScheduleID != nil {
		qb = qb.Where("schedule_id = ?", *q.ScheduleID)
	}
	if q.StartAtGte != nil {
		qb = qb.Where("start_at >= ?", *q.StartAtGte)
	}
	if q.StartAtLte != nil {
		qb = qb.Where("start_at <= ?", *q.StartAtLte)
	}
	if q.EndAtGte != nil {
		qb = qb.Where("end_at >= ?", *q.EndAtGte)
	}
	if q.EndAtLte != nil {
		qb = qb.Where("end_at <= ?", util.GetEndOfDay(*q.EndAtLte))
	}
	if q.EventType != nil {
		qb = qb.Where("event_type = ?", *q.EventType)
	}

	if q.IncludeDeleted == nil || !*q.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if q.SearchTerm != nil && q.SearchCategories != nil {
		Search.ApplySearch(qb, q, &EventSchedule{}, nil)
	}

	return qb
}

func (r *ScheduleRepository) Create(p *Schedule, trans *gorm.DB) error {
	return create(ScheduleTbl, p, trans)
}

func (r *ScheduleRepository) FindByID(id string, options *FindOneOptions) (*Schedule, error) {
	return findByID[Schedule](ScheduleTbl, id, options)
}

func (r *ScheduleRepository) Update(p *Schedule, trans *gorm.DB) error {
	return update(ScheduleTbl, p, trans)
}

func (r *ScheduleRepository) FindMany(query *ScheduleQuery, options *FindManyOptions) ([]*Schedule, error) {
	shouldPreloadEvent := false
	if options != nil && options.Preloads != nil && lo.Contains(options.Preloads, ScheduleEventField) {
		shouldPreloadEvent = true
		options.Preloads = util.RemoveElement(options.Preloads, ScheduleEventField)
	}

	schedules, err := findMany[Schedule](ScheduleTbl, query, options)
	if err != nil {
		return nil, err
	}

	if shouldPreloadEvent {
		if err := preloadEvent(schedules); err != nil {
			return schedules, err
		}
	}

	return schedules, nil
}

func (r *ScheduleRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Schedule](ScheduleTbl, id, trans)
}

func (r *EventScheduleRepository) Create(p *EventSchedule, trans *gorm.DB) error {
	return create(EventScheduleTbl, p, trans)
}

func (r *EventScheduleRepository) FindByID(id string, options *FindOneOptions) (*EventSchedule, error) {
	return findByID[EventSchedule](EventScheduleTbl, id, options)
}

func (r *EventScheduleRepository) Update(p *EventSchedule, trans *gorm.DB) error {
	return update(EventScheduleTbl, p, trans)
}

func (r *EventScheduleRepository) FindOne(query *EventScheduleQuery, options *FindOneOptions) (*EventSchedule, error) {
	return findOne[EventSchedule](EventScheduleTbl, query, options)
}

func (r *EventScheduleRepository) FindMany(query *EventScheduleQuery, options *FindManyOptions) ([]*EventSchedule, error) {
	return findMany[EventSchedule](EventScheduleTbl, query, options)
}

func (r *EventScheduleRepository) FindPage(query *EventScheduleQuery, options *FindPageOptions) ([]*EventSchedule, *Pagination, error) {
	return findPage[EventSchedule](EventScheduleTbl, query, options)
}

func (r *EventScheduleRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[EventSchedule](EventScheduleTbl, id, trans)
}

func preloadEvent(schedules []*Schedule) error {
	for _, s := range schedules {
		events, err := Repository.EventSchedule(context.Background()).FindMany(&EventScheduleQuery{ScheduleID: &s.ID}, &FindManyOptions{})
		if err != nil {
			return err
		}
		s.Events = events
	}
	return nil
}
