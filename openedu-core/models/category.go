package models

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"sort"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CategoryType string

const (
	TypeCategoryBlog   CategoryType = "blog"
	TypeCategoryCourse CategoryType = "course"
	TypeLevel          CategoryType = "level"
	TypeCategoryPrompt CategoryType = "prompt"
)

var DefaultCourseLevels = []*Category{
	{
		Name:   "Beginner",
		Active: true,
		Order:  1,
		Type:   TypeLevel,
	},
	{
		Name:   "Intermediate",
		Active: true,
		Order:  2,
		Type:   TypeLevel,
	},
	{
		Name:   "Advanced",
		Active: true,
		Order:  3,
		Type:   TypeLevel,
	},
}

var DefaultCategoryPrompt = []*Category{
	{
		Name:   "Education",
		Active: true,
		Order:  1,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Marketing",
		Active: true,
		Order:  2,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Finance",
		Active: true,
		Order:  3,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Technology",
		Active: true,
		Order:  4,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Sales",
		Active: true,
		Order:  5,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Data Analysis",
		Active: true,
		Order:  6,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Project Management",
		Active: true,
		Order:  7,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Business Strategy",
		Active: true,
		Order:  8,
		Type:   TypeCategoryPrompt,
	},
	{
		Name:   "Content Creation",
		Active: true,
		Order:  9,
		Type:   TypeCategoryPrompt,
	},
}

type Category struct {
	Model
	Name      string       `json:"name" validate:"required" gorm:"type:varchar(50);not null"`
	Active    bool         `json:"active" gorm:"default:true"`
	ParentID  *string      `json:"parent_id" gorm:"type:varchar(20);null"`
	Order     int          `json:"order" validate:"required" gorm:"default:1"`
	Type      CategoryType `json:"type" gorm:"type:varchar(20)"`
	OrgID     string       `json:"org_id" gorm:"type:varchar(20)"`
	UseCount  int          `json:"use_count"  gorm:"default:0"`
	Formatted string       `json:"formatted" gorm:"type:varchar(50)"`
	SubName   string       `json:"sub_name"`
}

type TreeCategory struct {
	ID        string          `json:"id"`
	Name      string          `json:"name"`
	Type      CategoryType    `json:"type"`
	Order     int             `json:"order"`
	OrgID     string          `json:"org_id"`
	UseCount  int             `json:"use_count"`
	Formatted string          `json:"formatted"`
	Child     []*TreeCategory `json:"child"`
}

type CategoryQuery struct {
	ID             *string         `json:"id,omitempty" form:"id"`
	Name           *string         `json:"name,omitempty" form:"name"`
	Active         *bool           `json:"active,omitempty" form:"active"`
	ParentID       *string         `json:"parent_id,omitempty" form:"parent_id"`
	Order          *int            `json:"order,omitempty" form:"order"`
	Type           *CategoryType   `json:"type,omitempty" form:"type" validate:"omitempty,oneof=blog course level"`
	OrgID          *string         `json:"org_id,omitempty" form:"org_id"`
	Formatted      *string         `json:"formatted,omitempty" form:"formatted"`
	UseCount       *int            `json:"use_count,omitempty" form:"use_count"`
	IncludeDeleted *bool           `json:"include_deleted,omitempty" form:"-"`
	IDIn           []string        `json:"id_in,omitempty" form:"id_in"`
	IDNotIn        []string        `json:"id_not_in,omitempty" form:"id_not_in"`
	NameIn         []string        `json:"name_in,omitempty" form:"name_in"`
	TypeIn         []*CategoryType `json:"type_in,omitempty" form:"type_in" validate:"omitempty,oneof=blog course level"`
}

type SimpleCategory struct {
	Model
	Name     string       `json:"name"`
	Active   bool         `json:"active"`
	ParentID *string      `json:"parent_id"`
	OrgID    string       `json:"org_id"`
	Order    int          `json:"order"`
	Type     CategoryType `json:"type"`
	UseCount int          `json:"use_count" form:"use_count"`
	SubName  string       `json:"sub_name"`
}

type CategoriesPage struct {
	Categories []*Category `json:"categories"`
	Pagination *Pagination `json:"pagination"`
}

func (c *Category) Sanitize() *SimpleCategory {
	return &SimpleCategory{
		Model:    c.Model,
		Name:     c.Name,
		Active:   c.Active,
		ParentID: c.ParentID,
		OrgID:    c.OrgID,
		Order:    c.Order,
		Type:     c.Type,
		UseCount: c.UseCount,
		SubName:  c.SubName,
	}
}

func (c *Category) GetID() string {
	return c.ID
}

func (query *CategoryQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Active != nil {
		qb = qb.Where("active = ?", *query.Active)
	}

	if query.ParentID != nil {
		qb = qb.Where("parent_id = ?", *query.ParentID)
	}

	if query.Order != nil {
		qb = qb.Where("order = ?", *query.Order)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Formatted != nil {
		qb = qb.Where("formatted = ?", *query.Formatted)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if len(query.IDNotIn) > 0 {
		qb = qb.Where("id NOT IN (?)", query.IDNotIn)
	}

	if len(query.NameIn) > 0 {
		qb = qb.Where("name IN (?)", query.NameIn)
	}

	return qb
}

// Create inserts a Category to database, transaction is optional
func (r *CategoryRepository) Create(c *Category, trans *gorm.DB) error {
	if err := Cache.Category.Flush(); err != nil {
		log.Errorf("CategoryRepository.Create::DeleteMany all category caches error: %v", err)
	}
	return create(CategoryTbl, c, trans)
}

func (r *CategoryRepository) CreateMany(cs []*Category, trans *gorm.DB) error {
	if err := Cache.Category.Flush(); err != nil {
		log.Errorf("CategoryRepository.CreateMany::DeleteMany all category caches error: %v", err)
	}
	return createMany(CategoryTbl, cs, trans)
}

// Update updates a Category by ID in database, transaction is optional
func (r *CategoryRepository) Update(c *Category, trans *gorm.DB) error {
	if err := Cache.Category.Flush(); err != nil {
		log.Errorf("CategoryRepository.Update::DeleteMany all category caches error: %v", err)
	}
	return update(CategoryTbl, c, trans)
}

func (r *CategoryRepository) DeactivateMany(query *CategoryQuery, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	tx = ApplyQueryOptions(tx, query)
	if err = tx.Model(&Category{}).Updates(map[string]interface{}{"active": false}).Error; err != nil {
		return err
	}

	if cErr := Cache.Category.Flush(); cErr != nil {
		log.Errorf("CategoryRepository.DeactivateMany::DeleteMany all category caches error: %v", cErr)
	}
	return nil
}

func (r *CategoryRepository) UpsertMany(categories []*Category, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	if err = tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"name", "active", "parent_id", "order", "type", "formatted", "update_at", "delete_at"}),
	}).Create(&categories).Error; err != nil {
		return err
	}

	if cErr := Cache.Category.Flush(); cErr != nil {
		log.Errorf("CategoryRepository.UpsertMany::DeleteMany all category caches error: %v", cErr)
	}
	return nil
}

// FindByID finds a Category by ID with given find options, transaction is optional
func (r *CategoryRepository) FindByID(id string, options *FindOneOptions) (*Category, error) {
	return findByID[Category](CategoryTbl, id, options)
}

// FindOne finds one Category with given find queries and options, transaction is optional
func (r *CategoryRepository) FindOne(query *CategoryQuery, options *FindOneOptions) (*Category, error) {
	return findOne[Category](CategoryTbl, query, options)
}

func (r *CategoryRepository) makeCacheKeyFindMany(query *CategoryQuery, options *FindManyOptions) (string, error) {
	d := struct {
		Query   *CategoryQuery   `json:"query,omitempty"`
		Options *FindManyOptions `json:"options,omitempty"`
	}{}
	if query != nil {
		d.Query = query
	}

	if options != nil {
		d.Options = &FindManyOptions{
			Joins:    options.Joins,
			Preloads: options.Preloads,
			Sort:     options.Sort,
			Limit:    options.Limit,
			Offset:   options.Offset,
		}
	}

	str, err := json.Marshal(d)
	if err != nil {
		return "", fmt.Errorf("make cache key for find many failed: %w", err)
	}

	return "find_many:" + base64.StdEncoding.EncodeToString(str), nil
}

// FindMany finds Categories by query conditions with give find options
func (r *CategoryRepository) FindMany(query *CategoryQuery, options *FindManyOptions) ([]*Category, error) {
	cacheKey, err := r.makeCacheKeyFindMany(query, options)
	if err != nil {
		return nil, err
	}

	var categoryCache []*Category
	if cErr := Cache.Category.GetList(cacheKey, &categoryCache); cErr == nil && len(categoryCache) > 0 {
		log.Debugf("CategoryRepository.FindMany::Hit cache with key %s: %#v", cacheKey, categoryCache)
		return categoryCache, nil
	}

	log.Debugf("CategoryRepository.FindMany::Miss cache with key %s", cacheKey)
	categories, err := findMany[Category](CategoryTbl, query, options)
	if err != nil {
		return nil, err
	}

	if err = Cache.Category.SetList(cacheKey, categories); err != nil {
		log.Errorf("CategoryRepository.FindMany::Set cache for all levels error: %v", err)
	}

	return categories, nil
}

// FindPage returns Categories and pagination by query conditions and find options, transaction is optional
func (r *CategoryRepository) FindPage(query *CategoryQuery, options *FindPageOptions) ([]*Category, *Pagination, error) {
	//if query.Active == util.NewBool(true) && *query.Type == TypeCategory {
	//	if items := Cache.Category.GetAllCategories(); items != nil {
	//		categories := lo.Map(items, func(item cache_clients.CategoryIface, _ int) *Category {
	//			return item.(*Category)
	//		})
	//		return categories, nil, nil
	//	}
	//}

	categories, pagination, err := findPage[Category](CategoryTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	//if query.Active == util.NewBool(true) && *query.Type == TypeCategory {
	//	cachedItems := lo.Map(categories, func(category *Category, _ int) cache_clients.CategoryIface {
	//		return category
	//	})
	//	Cache.Category.SetAllCategories(cachedItems)
	//}

	return categories, pagination, nil
}

// Delete perform soft deletion to a Categories by ID, transaction is optional
func (r *CategoryRepository) Delete(id string, trans *gorm.DB) error {
	err := deleteByID[Category](CategoryTbl, id, trans)
	if cErr := Cache.Category.Flush(); cErr != nil {
		log.Errorf("CategoryRepository.UpsertMany::DeleteMany all category caches error: %v", cErr)
	}
	return err
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *CategoryRepository) DeleteMany(query *CategoryQuery, trans *gorm.DB) (int64, error) {
	if cErr := Cache.Category.Flush(); cErr != nil {
		log.Errorf("CategoryRepository.UpsertMany::DeleteMany all category caches error: %v", cErr)
	}
	return deleteMany[Category](CategoryTbl, query, trans)
}

// Count returns number of Categories by query conditions, transaction is optional
func (r *CategoryRepository) Count(query *CategoryQuery) (int64, error) {
	return count[Category](CategoryTbl, query)
}

func (r *CategoryRepository) IncreaseUseCount(category *Category, field string, amount int, trans *gorm.DB) error {
	return increment[Category](CategoryTbl, category.ID, field, amount, trans)
}

func (r *CategoryRepository) DecreaseUseCount(category *Category, field string, amount int, trans *gorm.DB) error {
	return decrement[Category](CategoryTbl, category.ID, field, amount, trans)
}

func (r *CategoryRepository) IncreaseManyUseCount(query *CategoryQuery, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = qb.Table(GetTblName(CategoryTbl)).Debug().UpdateColumn(field, gorm.Expr("? + ?", gorm.Expr(field), amount)).Error
	if cErr := Cache.Category.Flush(); cErr != nil {
		log.Errorf("CategoryRepository.UpsertMany::DeleteMany all category caches error: %v", cErr)
	}
	return
}

func (r *CategoryRepository) DecreaseManyUseCount(query *CategoryQuery, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = qb.Table(GetTblName(CategoryTbl)).Debug().UpdateColumn(field, gorm.Expr("? - ?", gorm.Expr(field), amount)).Error
	if cErr := Cache.Category.Flush(); cErr != nil {
		log.Errorf("CategoryRepository.UpsertMany::DeleteMany all category caches error: %v", cErr)
	}
	return
}

func (r *CategoryRepository) GetFullChildIdsByParentId(parentID string) ([]*Category, error) {
	query := `
		WITH RECURSIVE category_tree AS (
			SELECT 
				*
			FROM openedu_categories
			WHERE id = ?
			UNION 
			SELECT
				c.*
				FROM openedu_categories c
			JOIN category_tree ct ON c.parent_id = ct.id
		)
		SELECT * FROM category_tree AS ct 
	`
	var ids []*Category
	qb := DB.Raw(query, parentID)

	err := qb.Select("*").Scan(&ids).Error

	if err != nil {
		return nil, err
	}

	return ids, nil
}

type CategoriesAndLevelsHolder interface {
	GetCUID() string
	SetLevels(levels []*Category)
	SetCategories(categories []*Category)
	AppendLevels(levels []*Category)
	AppendCategories(categories []*Category)
}

func PreloadCategoriesAndLevel[T CategoriesAndLevelsHolder](
	items []T,
	modelName ModelName,
	preloadCategories bool,
	preloadLevels bool,
) error {
	var fields []string
	if preloadCategories {
		fields = append(fields, CategoriesField)
	}

	if preloadLevels {
		fields = append(fields, LevelsField)
	}

	if len(fields) == 0 || len(items) == 0 {
		return nil
	}

	var ids []string
	itemsByCUIDs := map[string]T{}
	for _, item := range items {
		ids = append(ids, item.GetCUID())
		itemsByCUIDs[item.GetCUID()] = item
	}

	query := &CategoryRelationQuery{
		RelatedIDIn: lo.Uniq(ids),
		FieldIn:     fields,
		RelatedType: util.NewT(modelName),
	}
	options := &FindManyOptions{
		Joins: []string{CategoryField},
	}
	cateRelations, err := Repository.CategoryRelation.FindMany(query, options)
	if err != nil {
		return err
	}

	// Ascending sort (0 -> n)
	sort.Slice(cateRelations, func(i, j int) bool {
		return cateRelations[i].Order < cateRelations[j].Order
	})

	for _, cateRelation := range cateRelations {
		course, found := itemsByCUIDs[cateRelation.RelatedID]
		if !found {
			continue
		}
		switch cateRelation.Field {
		case CategoriesField:
			course.AppendCategories([]*Category{&cateRelation.Category})

		case LevelsField:
			course.AppendLevels([]*Category{&cateRelation.Category})
		}
	}

	return nil
}
