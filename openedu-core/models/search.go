package models

import (
	"errors"
	"fmt"
	"openedu-core/pkg/util"
	"reflect"
	"regexp"
	"strings"
	"unicode/utf8"

	"gorm.io/gorm"
)

const (
	MaxSearchLength   = 100
	MinSearchLength   = 1
	MaxWildcardRepeat = 2
)

var (
	sqlInjectionPattern  = regexp.MustCompile(`(?i)(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|CREATE|ALTER|TRUNCATE|EXEC|DECLARE)\s?`)
	dangerousChars       = regexp.MustCompile(`['";\\]`)
	controlChars         = regexp.MustCompile(`[\x00-\x1F\x7F]`)
	consecutiveWildcards = regexp.MustCompile(`[%_]{2,}`)
)

type SearchTermError struct {
	Code    string
	Message string
}

func (e SearchTermError) Error() string {
	return e.Message
}

type SearchableQuery interface {
	GetSearchTerm() *string
	GetSearchCategories() *string
}

type BaseSearchQuery struct {
	SearchTerm       *string `json:"search_term" form:"search_term"`
	SearchCategories *string `json:"search_categories" form:"search_categories"`
}

func (q *BaseSearchQuery) GetSearchTerm() *string {
	return q.SearchTerm
}

func (q *BaseSearchQuery) GetSearchCategories() *string {
	return q.SearchCategories
}

type SearchBuilder struct {
}

func NewSearchBuilder() *SearchBuilder {
	return &SearchBuilder{}
}

type SearchConditions struct {
	Conditions []string
	Params     []interface{}
}

func (s *SearchBuilder) ApplySearch(db *gorm.DB, query SearchableQuery, model interface{}, prefix *string) *gorm.DB {
	if query.GetSearchTerm() == nil || query.GetSearchCategories() == nil {
		return db
	}

	conditions, err := s.buildSearchConditions(query, model, prefix)
	if err != nil {
		return db
	}

	if len(conditions.Conditions) > 0 {
		return db.Where(
			strings.Join(conditions.Conditions, " OR "),
			conditions.Params...,
		)
	}

	return db
}

func (s *SearchBuilder) buildSearchConditions(query SearchableQuery, model interface{}, prefix *string) (SearchConditions, error) {
	conditions := SearchConditions{
		Conditions: []string{},
		Params:     []interface{}{},
	}

	categories := strings.Split(*query.GetSearchCategories(), ",")
	validCates, verifyCatesErr := VerifySearchCategory(model, []string{}, categories)
	if verifyCatesErr != nil {
		return conditions, verifyCatesErr
	}

	_, verifyTermErr := VerifySearchTerm(*query.GetSearchTerm())
	if verifyTermErr != nil {
		return conditions, verifyTermErr
	}

	searchTerm := strings.TrimSpace(*query.GetSearchTerm())
	if searchTerm == "" {
		return conditions, nil
	}

	baseTerm := extractBaseTerm(searchTerm)

	for _, category := range validCates {
		// Add prefix to category if prefix is provided
		columnName := category
		if prefix != nil {
			columnName = fmt.Sprintf("%s.%s", *prefix, category)
		}

		conditions.Conditions = append(conditions.Conditions,
			fmt.Sprintf("LOWER(%s) LIKE LOWER(?)", columnName))
		conditions.Params = append(conditions.Params,
			fmt.Sprintf("%%%s%%", searchTerm))

		if baseTerm != searchTerm {
			conditions.Conditions = append(conditions.Conditions,
				fmt.Sprintf("LOWER(%s) LIKE LOWER(?)", columnName))
			conditions.Params = append(conditions.Params,
				fmt.Sprintf("%%%s%%", baseTerm))
		}
	}

	return conditions, nil
}

func buildSearchVariations(term string) []string {
	var variations []string
	seenTerms := make(map[string]bool)

	addUniqueTerm := func(term string) {
		if term != "" && !seenTerms[term] {
			variations = append(variations, term)
			seenTerms[term] = true
		}
	}

	addUniqueTerm(term)

	sanitizedTerm := sanitizeSearchTerm(term)
	if sanitizedTerm != term {
		addUniqueTerm(sanitizedTerm)
	}

	if containsSpaceOrSpecial(term) {
		words := splitSearchTerms(term)
		for _, word := range words {
			addUniqueTerm(word)
			sanitizedWord := sanitizeSearchTerm(word)
			if sanitizedWord != word {
				addUniqueTerm(sanitizedWord)
			}
		}
	}

	return variations
}

// Helper
func sanitizeSearchTerm(term string) string {
	reg := regexp.MustCompile(`[^\w\s]`)
	return strings.TrimSpace(reg.ReplaceAllString(term, " "))
}

func splitSearchTerms(term string) []string {
	reg := regexp.MustCompile(`[\s@.#$-]+`)
	words := reg.Split(term, -1)

	var result []string
	for _, word := range words {
		if word != "" {
			result = append(result, word)
		}
	}
	return result
}

func containsSpaceOrSpecial(term string) bool {
	return strings.ContainsAny(term, " @.#$-")
}

func extractBaseTerm(term string) string {
	reg := regexp.MustCompile(`[^a-zA-Z0-9\s]`)
	return strings.TrimSpace(reg.ReplaceAllString(term, ""))
}

func VerifySearchTerm(searchTerm string) (string, error) {
	searchTerm = strings.TrimSpace(searchTerm)
	if searchTerm == "" {
		return "", &SearchTermError{
			Code:    "EMPTY_SEARCH",
			Message: "Search term cannot be empty",
		}
	}

	length := utf8.RuneCountInString(searchTerm)
	if length < MinSearchLength {
		return "", &SearchTermError{
			Code:    "TOO_SHORT",
			Message: "Search term is too short",
		}
	}
	if length > MaxSearchLength {
		return "", &SearchTermError{
			Code:    "TOO_LONG",
			Message: "Search term exceeds maximum length",
		}
	}

	searchTermLower := strings.ToLower(searchTerm)

	if sqlInjectionPattern.MatchString(searchTermLower) {
		return "", &SearchTermError{
			Code:    "POTENTIAL_SQL_INJECTION",
			Message: "Search term contains potentially dangerous SQL keywords",
		}
	}

	if controlChars.MatchString(searchTerm) {
		return "", &SearchTermError{
			Code:    "INVALID_CHARS",
			Message: "Search term contains invalid control characters",
		}
	}

	if dangerousChars.MatchString(searchTerm) {
		return "", &SearchTermError{
			Code:    "DANGEROUS_CHARS",
			Message: "Search term contains potentially dangerous characters",
		}
	}

	if strings.Count(searchTerm, "%")+strings.Count(searchTerm, "_") > MaxWildcardRepeat {
		return "", &SearchTermError{
			Code:    "EXCESSIVE_WILDCARDS",
			Message: "Search term contains too many wildcard characters",
		}
	}

	if consecutiveWildcards.MatchString(searchTerm) {
		return "", &SearchTermError{
			Code:    "CONSECUTIVE_WILDCARDS",
			Message: "Search term contains consecutive wildcard characters",
		}
	}

	sanitized := searchTerm
	sanitized = strings.Map(func(r rune) rune {
		if strings.ContainsRune("!@#$^&*()+=[]{}|<>?,", r) {
			return ' '
		}
		return r
	}, sanitized)

	sanitized = regexp.MustCompile(`\s+`).ReplaceAllString(sanitized, " ")
	sanitized = strings.TrimSpace(sanitized)

	return sanitized, nil
}

func VerifySearchCategory[T any](entity T, omitFields []string, searchCates []string) ([]string, error) {
	entityType := reflect.TypeOf(entity)
	if entityType.Kind() == reflect.Ptr {
		entityType = entityType.Elem()
	}

	if entityType.Kind() != reflect.Struct {
		return []string{}, errors.New("entity must be struct")
	}

	fields, err := getAllFieldsWithSnakeCase(entityType, omitFields)
	if err != nil {
		return []string{}, nil
	}

	validCates := util.SubsetArrayString(fields, searchCates)

	return validCates, nil
}
