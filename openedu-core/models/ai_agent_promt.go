package models

import (
	"openedu-core/pkg/setting"

	"gorm.io/gorm"
)

type AIAgentType string

const (
	AIAgentTypeChat          AIAgentType = "ai_chat"
	AIAgentTypeSearch        AIAgentType = "ai_search"
	AIAgentTypeSlide         AIAgentType = "ai_slide"
	AIAgentTypeImageGenerate AIAgentType = "ai_image_generate"
	AIAgentTypeImageAnalysis AIAgentType = "ai_image_analysis"
	AIAgentTypeVideo         AIAgentType = "ai_chat"
	AIAgentTypeCode          AIAgentType = "ai_code"
)

type AIPrompt struct {
	Model
	UserID      string      `json:"user_id" gorm:"type:varchar(20)"`
	Text        string      `json:"text"`
	Enable      bool        `json:"enable"`
	AIAgentType AIAgentType `json:"ai_agent_type"`
	CategoryID  string      `json:"category_id"`
	Order       int         `json:"order" gorm:"default:0"`
	Locale      string      `json:"locale"`
}

// TableName overrides the table name used by User to `profiles`
func (AIPrompt) TableName() string {
	return setting.DatabaseSetting.TablePrefix + "ai_prompts"
}

type AIPromptQuery struct {
	ID          *string      `json:"id" form:"id"`
	UserID      *string      `json:"user_id" form:"user_id"`
	AIAgentType *AIAgentType `json:"ai_agent_type" form:"ai_agent_type"`
	CategoryID  *string      `json:"category_id" form:"category_id"`
	IDIn        []string     `json:"id_in" form:"id_in"`
	Enable      *bool        `json:"enable" form:"enable"`
	Deleted     *bool        `json:"deleted" form:"deleted"`
	Locale      *string      `json:"locale" form:"locale"`
}

func (query *AIPromptQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.AIAgentType != nil {
		qb = qb.Where("ai_agent_type = ?", *query.AIAgentType)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.Deleted == nil || !*query.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.CategoryID != nil {
		qb = qb.Where("category_id = ?", *query.CategoryID)
	}

	if query.Locale != nil {
		qb = qb.Where("locale = ?", *query.Locale)
	}

	return qb
}

func (r *AIPromptRepository) Create(p *AIPrompt, trans *gorm.DB) error {
	return create(AIPromptTbl, p, trans)
}

func (r *AIPromptRepository) CreateMany(entities []*AIPrompt, trans *gorm.DB) error {
	if err := createMany(AIPromptTbl, entities, trans); err != nil {
		return err
	}
	return nil
}

func (r *AIPromptRepository) FindByID(id string, options *FindOneOptions) (*AIPrompt, error) {
	return findByID[AIPrompt](AIPromptTbl, id, options)
}

func (r *AIPromptRepository) Update(p *AIPrompt, trans *gorm.DB) error {
	return update(AIPromptTbl, p, trans)
}

func (r *AIPromptRepository) FindOne(query *AIPromptQuery, options *FindOneOptions) (*AIPrompt, error) {
	return findOne[AIPrompt](AIPromptTbl, query, options)
}

func (r *AIPromptRepository) FindMany(query *AIPromptQuery, options *FindManyOptions) ([]*AIPrompt, error) {
	return findMany[AIPrompt](AIPromptTbl, query, options)
}

func (r *AIPromptRepository) FindPage(query *AIPromptQuery, options *FindPageOptions) ([]*AIPrompt, *Pagination, error) {
	return findPage[AIPrompt](AIPromptTbl, query, options)
}

func (r *AIPromptRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[AIPrompt](AIPromptTbl, id, trans)
}

func (r *AIPromptRepository) DeleteMany(query *AIPromptQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[AIPrompt](AIPromptTbl, query, trans)
}
