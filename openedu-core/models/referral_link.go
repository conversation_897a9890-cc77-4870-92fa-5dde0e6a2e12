package models

import (
	"fmt"
	"gorm.io/gorm"
)

type ReferralLink struct {
	Model
	UserID       string  `json:"user_id" gorm:"type:varchar(20);not null"`
	OrgID        string  `json:"org_id" gorm:"type:varchar(20);not null"`
	CampaignID   string  `json:"campaign_id" gorm:"type:varchar(20);not null"`
	CommissionID string  `json:"commission_id" gorm:"type:varchar(20);not null"`
	RefCode      string  `json:"ref_code" gorm:"type:varchar(20);not null"`
	RefLevel1    string  `json:"ref_level1" gorm:"type:varchar(20)"`
	RefLevel2    string  `json:"ref_level2" gorm:"type:varchar(20)"`
	ShareRate    float32 `json:"share_rate" gorm:"type:float8;default:0"`
	Enable       bool    `json:"enable" gorm:"default:true"`
	IsExtend     bool    `json:"is_extend" gorm:"default:false"`

	Campaign   *AffiliateCampaign `json:"campaign"`
	Commission *Commission        `json:"commission"`
}

type ReferralLinkQuery struct {
	ID             *string   `json:"id" form:"id"`
	IDIn           []*string `json:"id_in" form:"id_in"`
	UserID         *string   `json:"user_id" form:"user_id"`
	CampaignID     *string   `json:"campaign_id" form:"campaign_id"`
	OrgID          *string   `json:"org_id" form:"org_id"`
	CommissionID   *string   `json:"commission_id" form:"commission_id"`
	RefCode        *string   `json:"ref_code" form:"ref_code"`
	RefLevel1      *string   `json:"ref_level1" form:"ref_level1"`
	RefLevel2      *string   `json:"ref_level2" form:"ref_level2"`
	IncludeDeleted *bool     `json:"include_deleted" form:"include_deleted"`
	Enable         *bool     `json:"enable"`
	IsExtend       *bool     `json:"is_extend"`
}

func (query *ReferralLinkQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.CommissionID != nil {
		qb = qb.Where("commission_id = ?", *query.CommissionID)
	}

	if query.RefCode != nil {
		qb = qb.Where("ref_code = ?", *query.RefCode)
	}

	if query.CampaignID != nil {
		qb = qb.Where("campaign_id = ?", *query.CampaignID)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.IsExtend != nil {
		qb = qb.Where("is_extend = ?", *query.IsExtend)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *ReferralLinkRepository) Create(e *ReferralLink, trans *gorm.DB) error {
	return create(ReferralLinkTbl, e, trans)
}

func (r *ReferralLinkRepository) CreateMany(ts []*ReferralLink, trans *gorm.DB) error {
	return createMany(ReferralLinkTbl, ts, trans)
}

func (r *ReferralLinkRepository) Update(f *ReferralLink, trans *gorm.DB) error {
	return update(ReferralLinkTbl, f, trans)
}

func (r *ReferralLinkRepository) FindOne(query *ReferralLinkQuery, options *FindOneOptions) (*ReferralLink, error) {
	return findOne[ReferralLink](ReferralLinkTbl, query, options)
}

func (r *ReferralLinkRepository) FindPage(query *ReferralLinkQuery, options *FindPageOptions) ([]*ReferralLink, *Pagination, error) {
	return findPage[ReferralLink](ReferralLinkTbl, query, options)
}

func (r *ReferralLinkRepository) FindMany(query *ReferralLinkQuery, options *FindManyOptions) ([]*ReferralLink, error) {
	return findMany[ReferralLink](ReferralLinkTbl, query, options)
}

func (r *ReferralLinkRepository) Count(query *ReferralLinkQuery) (int64, error) {
	return count[ReferralLink](ReferralLinkTbl, query)
}

func (r *ReferralLinkRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ReferralLink](ReferralLinkTbl, id, trans)
}

func (r *ReferralLinkRepository) MaxShareRateByCampaign(campaignID string) (float32, error) {
	type Result struct {
		Max float32 `json:"max"`
	}

	var result Result
	tbl := GetTblName(ReferralLinkTbl)
	query := fmt.Sprintf(
		"SELECT MAX(share_rate) FROM %s WHERE campaign_id = '%s' and \"enable\" = true and delete_at = 0",
		tbl, campaignID,
	)
	err := DB.Debug().Raw(query).Scan(&result).Error
	if err != nil {
		return 0, err
	}
	return result.Max, nil
}
