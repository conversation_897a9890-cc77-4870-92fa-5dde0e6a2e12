package models

import (
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type SectionStatus string

const (
	SectionStatusDraft   SectionStatus = "draft"
	SectionStatusPublish SectionStatus = "publish"
	SectionStatusHide    SectionStatus = "hide"
)

type Section struct {
	UID      string        `json:"uid"`
	OrgID    string        `json:"org_id"`
	CourseID string        `json:"course_id"`
	UserID   string        `json:"user_id"`
	Title    string        `json:"title"`
	Note     string        `json:"note"`
	Order    int           `json:"order"`
	Free     bool          `json:"free" gorm:"default:false"`
	Status   SectionStatus `json:"status" gorm:"default:draft"`
	ParentID string        `json:"parent_id"`

	SectionIDV2 string `json:"section_id_v2"`
	LessonIDV2  string `json:"lesson_id_v2"`

	LessonCount  int `json:"lesson_count"`
	ActiveLesson int `json:"active_lesson"`

	User                   *User            `json:"user" gorm:"-"`
	Course                 *Course          `json:"course" gorm:"-"`
	Section                *Section         `json:"section" gorm:"-"`
	Lessons                []*Section       `json:"lessons" gorm:"-"`
	Contents               []*LessonContent `json:"contents" gorm:"-"`
	IsCertificateCondition bool             `json:"is_certificate_condition" gorm:"-"`
	Model
	//Count
	CountTextLesson  int `json:"count_text_lesson"`
	CountVideoLesson int `json:"count_video_lesson"`
	CountPdfLesson   int `json:"count_pdf_lesson"`
	CountPPLesson    int `json:"count_pp_lesson"`
	CountDocLesson   int `json:"count_doc_lesson"`
	CountQuizLesson  int `json:"count_quiz_lesson"`
	CountEmbedLesson int `json:"count_embed_lesson"`

	CountActiveTextLesson  int `json:"count_active_text_lesson"`
	CountActiveVideoLesson int `json:"count_active_video_lesson"`
	CountActivePdfLesson   int `json:"count_active_pdf_lesson"`
	CountActivePPLesson    int `json:"count_active_pp_lesson"`
	CountActiveDocLesson   int `json:"count_active_doc_lesson"`
	CountActiveQuizLesson  int `json:"count_active_quiz_lesson"`
	CountActiveEmbedLesson int `json:"count_active_embed_lesson"`
}

type CourseStats struct {
	SectionCount  int `json:"section_count"`
	LessonCount   int `json:"lesson_count"`
	ActiveSection int `json:"active_section"`
	ActiveLesson  int `json:"active_lesson"`
	VideoCount    int `json:"video_count"`
	QuizCount     int `json:"quiz_count"`
}

type SimpleSection struct {
	Model
	UID          string                 `json:"uid"`
	OrgID        string                 `json:"org_id"`
	CourseID     string                 `json:"course_id"`
	UserID       string                 `json:"user_id"`
	Title        string                 `json:"title"`
	Note         string                 `json:"note"`
	Status       SectionStatus          `json:"status"`
	Order        int                    `json:"order"`
	Free         bool                   `json:"free"`
	ParentID     string                 `json:"parent_id"`
	LessonCount  int                    `json:"lesson_count"`
	ActiveLesson int                    `json:"active_lesson"`
	User         *SimpleUser            `json:"user,omitempty"`
	Course       *SimpleCourse          `json:"course,omitempty"`
	Section      *SimpleSection         `json:"section,omitempty"`
	Lessons      []*SimpleSection       `json:"lessons"`
	Contents     []*SimpleLessonContent `json:"contents"`
}

func (s *Section) IsLesson() bool {
	return s.ParentID != ""
}

func (s *Section) Sanitize() *Section {
	sanitizedSection := &Section{
		OrgID:        s.OrgID,
		UID:          s.UID,
		CourseID:     s.CourseID,
		UserID:       s.UserID,
		Title:        s.Title,
		Note:         s.Note,
		Order:        s.Order,
		Free:         s.Free,
		Status:       s.Status,
		ParentID:     s.ParentID,
		LessonCount:  s.LessonCount,
		ActiveLesson: s.ActiveLesson,
		User:         s.User,
		Course:       s.Course,
		Section:      s.Section,
		Lessons:      s.Lessons,
		Contents:     s.Contents,
		Model:        s.Model,
	}

	for idx := 0; idx < len(sanitizedSection.Contents); idx++ {
		sanitizedSection.Contents[idx] = sanitizedSection.Contents[idx].Sanitize()
	}

	// Recursively sanitize contents of nested sections
	for idx := 0; idx < len(sanitizedSection.Lessons); idx++ {
		sanitizedSection.Lessons[idx] = sanitizedSection.Lessons[idx].Sanitize()
	}

	return sanitizedSection
}

func (s *Section) ToSimple() *SimpleSection {
	simpleSection := &SimpleSection{
		Model:        s.Model,
		OrgID:        s.OrgID,
		UID:          s.UID,
		CourseID:     s.CourseID,
		UserID:       s.UserID,
		Title:        s.Title,
		Note:         s.Note,
		Status:       s.Status,
		Order:        s.Order,
		Free:         s.Free,
		ParentID:     s.ParentID,
		LessonCount:  s.LessonCount,
		ActiveLesson: s.ActiveLesson,
		Lessons: lo.Map(s.Lessons, func(lesson *Section, index int) *SimpleSection {
			return lesson.ToSimple()
		}),
		Contents: lo.Map(s.Contents, func(content *LessonContent, index int) *SimpleLessonContent {
			return content.ToSimple()
		}),
	}
	if s.User != nil {
		simpleSection.User = s.User.ToSimpleUser()
	}
	if s.Course != nil {
		simpleSection.Course = s.Course.Sanitize()
	}
	return simpleSection
}

func (s *Section) IsSection() bool {
	return s.ParentID == ""
}

type SectionQuery struct {
	OrgID           *string        `json:"org_id" form:"org_id"`
	ID              *string        `json:"id" form:"id"`
	UID             *string        `json:"uid" form:"uid"`
	UIDIn           []string       `json:"uid_in" form:"uid_in"`
	IDIn            []*string      `json:"id_in" form:"id_in"`
	CourseID        *string        `json:"course_id" form:"course_id"`
	ParentID        *string        `json:"parent_id" form:"parent_id"`
	ParentIDNotNull *bool          `json:"parent_id_not_null" form:"parent_id_not_null"`
	ParentIDNull    *bool          `json:"parent_id_null" form:"parent_id_null"`
	Title           *string        `json:"title" form:"title"`
	Status          *SectionStatus `json:"status" form:"status"`
	Free            *bool          `json:"free" form:"free"`
	IncludeDeleted  *bool
}

func (query *SectionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.UID != nil {
		qb = qb.Where("uid = ?", *query.UID)
	}

	if len(query.UIDIn) > 0 {
		qb = qb.Where("uid IN (?)", query.UIDIn)
	}

	if query.CourseID != nil {
		qb = qb.Where("course_id = ?", *query.CourseID)
	}

	if query.ParentID != nil {
		qb = qb.Where("parent_id = ?", *query.ParentID)
	}

	if query.ParentIDNotNull != nil && *query.ParentIDNotNull {
		qb = qb.Where("parent_id != '' ")
	}

	if query.ParentIDNull != nil && *query.ParentIDNull {
		qb = qb.Where("parent_id = '' ")
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.Title != nil {
		qb = qb.Where("title = ?", *query.Title)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.Free != nil {
		qb = qb.Where("free = ?", *query.Free)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a section to database, transaction is optional
func (r *SectionRepository) Create(entity *Section, trans *gorm.DB) error {
	return create(SectionTbl, entity, trans)
}

func (r *SectionRepository) CreateMany(entities []*Section, trans *gorm.DB) error {
	return createMany(SectionTbl, entities, trans)
}

func (r *SectionRepository) Update(entity *Section, trans *gorm.DB) error {
	return update(SectionTbl, entity, trans)
}

func (r *SectionRepository) FindOne(query *SectionQuery, options *FindOneOptions) (*Section, error) {
	return findOne[Section](SectionTbl, query, options)
}

func (r *SectionRepository) FindMany(query *SectionQuery, options *FindManyOptions) ([]*Section, error) {
	return findMany[Section](SectionTbl, query, options)
}

func (r *SectionRepository) FindPage(query *SectionQuery, options *FindPageOptions) ([]*Section, *Pagination, error) {
	return findPage[Section](SectionTbl, query, options)
}

// Delete perform soft deletion to a org by ID, transaction is optional
func (r *SectionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Section](SectionTbl, id, trans)
}

// Count returns number of Sections by query conditions, transaction is optional
func (r *SectionRepository) Count(query *SectionQuery) (int64, error) {
	return count[Section](SectionTbl, query)
}

func (r *SectionRepository) FindDistinctMany(query *SectionQuery, options *FindManyOptions) (entities []*Section, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	subQuery := DB.Table(GetTblName(SectionTbl)).Debug().
		Select("DISTINCT ON (uid) *")

	qb := ApplyQueryOptions(DB, query, options)
	result := qb.Table("(?) as sub", subQuery).Debug().
		Select("*").
		Find(&entities)
	if fErr := result.Error; fErr != nil {
		err = fErr
		return
	}
	return
}

func (r *SectionRepository) CountCourseStats(courseID string) (*CourseStats, error) {
	sectionTbl := GetTblName(SectionTbl)
	lessonContentTbl := GetTblName(LessonContentTbl)

	query := fmt.Sprintf(
		`
		WITH sections AS (
			SELECT id, status
			FROM %[1]s
			WHERE course_id = @courseID AND parent_id = ''
		), lessons AS (
			SELECT id, status, parent_id
			FROM %[1]s
			WHERE course_id = @courseID AND parent_id <> ''
		), active_lessons AS (
			SELECT l.*
			FROM lessons l
				LEFT JOIN sections s ON l.parent_id = s.id
			WHERE l.status = @publishStatus AND s.status = @publishStatus
		), lesson_contents AS (
			SELECT lc.id, lc.type
			FROM active_lessons l
					 LEFT JOIN %[2]s lc ON lc.lesson_id = l.id
		), quizzes AS (
			SELECT *
			FROM lesson_contents lc
				INNER JOIN public.openedu_quiz_relations qr ON lc.id = qr.related_entity_id 
					AND qr.related_entity_type = @lessonContentModel
		)
		SELECT
			(SELECT COUNT(*) FROM sections) AS section_count,
			(SELECT COUNT(*) FROM lessons) AS lesson_count,
			(SELECT COUNT(*) FROM sections WHERE status = @publishStatus) AS active_section,
			(SELECT COUNT(*) FROM active_lessons) AS active_lesson,
			(SELECT COUNT(*) FROM lesson_contents WHERE type = @videoType) AS video_count,
    		(SELECT COUNT(*) FROM quizzes) AS quiz_count
	`,
		sectionTbl,
		lessonContentTbl,
	)

	args := map[string]interface{}{
		"courseID":           courseID,
		"publishStatus":      SectionStatusPublish,
		"videoType":          LessonTypeVideo,
		"lessonContentModel": QuizRelationEntityLessonContent,
	}

	var stats CourseStats
	if err := DB.Debug().Raw(query, args).Scan(&stats).Error; err != nil {
		return nil, err
	}

	return &stats, nil
}
