package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/util"
	"sort"
	"strings"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type LaunchpadStatus string

const (
	LaunchpadSTTDraft     LaunchpadStatus = "draft"
	LaunchpadSTTReviewing LaunchpadStatus = "reviewing"
	LaunchpadSTTRejected  LaunchpadStatus = "rejected"
	LaunchpadSTTApproved  LaunchpadStatus = "approved"
	LaunchpadSTTPublish   LaunchpadStatus = "publish"
	LaunchpadSTTCancelled LaunchpadStatus = "cancelled"
	LaunchpadSTTSuccess   LaunchpadStatus = "success"
	LaunchpadSTTVoting    LaunchpadStatus = "voting"
	LaunchpadSTTFailed    LaunchpadStatus = "failed"
	LaunchpadSTTFunding   LaunchpadStatus = "funding"
	LaunchpadSTTWaiting   LaunchpadStatus = "waiting"
	LaunchpadSTTRefunded  LaunchpadStatus = "refunded"
)

type ClpLaunchpad struct {
	Model
	OrgID               string          `json:"org_id"`
	OrgSchema           string          `json:"org_schema"`
	Name                string          `json:"name"`
	Description         string          `json:"description"`
	Slug                string          `json:"slug"`
	LearnerOutcome      string          `json:"learner_outcome"`
	PreviewVideoID      string          `json:"preview_video_id"`
	ThumbnailID         string          `json:"thumbnail_id"`
	Status              LaunchpadStatus `json:"status" gorm:"default:'draft'"`
	Props               LaunchpadProps  `json:"props" gorm:"type:jsonb"`
	Enable              bool            `json:"enable" gorm:"default:true"`
	VotingStartDate     int             `json:"voting_start_date"`
	VotingEndDate       int             `json:"voting_end_date"`
	EstimateFundingDays int             `json:"estimate_funding_days"`
	FundingStartDate    int64           `json:"funding_start_date"`
	FundingEndDate      int64           `json:"funding_end_date"`
	UserID              string          `json:"user_id" gorm:"not null"`
	FundingGoal         FundingProps    `json:"funding_goal" gorm:"type:jsonb"`

	Courses          []*Course             `json:"courses" gorm:"-"`
	VotingMilestones []*ClpVotingMilestone `json:"voting_milestones" gorm:"-"`
	User             *User                 `json:"user" gorm:"-"`
	Owner            *SimpleProfile        `json:"owner" gorm:"-"`
	Thumbnail        *File                 `json:"thumbnail" gorm:"-"`
	PreviewVideo     *File                 `json:"preview_video" gorm:"-"`
	Categories       []*Category           `json:"categories" gorm:"-"`
	Levels           []*Category           `json:"levels" gorm:"-"`
	Bookmarked       bool                  `json:"bookmarked" gorm:"-"`
	OwnerProfile     *LaunchpadPartner     `json:"owner_profile" gorm:"-"`
	Outline          []*Section            `json:"outline" gorm:"-"`
	TotalAmount      *decimal.Decimal      `json:"total_amount" gorm:"-"`
	TotalBackers     *int                  `json:"total_backers" gorm:"-"`
	Investment       *ClpInvestment        `json:"investment" gorm:"-"`
	TotalRefunded    decimal.Decimal       `json:"total_refunded" gorm:"default:0"`
}

type SimpleLaunchpad struct {
	Model
	Name                string                `json:"name"`
	OrgID               string                `json:"org_id"`
	Description         string                `json:"description"`
	Slug                string                `json:"slug"`
	LearnerOutcome      string                `json:"learner_outcome"`
	Status              LaunchpadStatus       `json:"status"`
	Props               LaunchpadProps        `json:"props"`
	Enable              bool                  `json:"enable"`
	VotingStartDate     int                   `json:"voting_start_date"`
	VotingEndDate       int                   `json:"voting_end_date"`
	EstimateFundingDays int                   `json:"estimate_funding_days"`
	FundingStartDate    int64                 `json:"funding_start_date"`
	FundingEndDate      int64                 `json:"funding_end_date"`
	UserID              string                `json:"user_id"`
	Courses             []*SimpleCourse       `json:"courses"`
	VotingMilestones    []*ClpVotingMilestone `json:"voting_milestones"`
	User                *User                 `json:"user,omitempty"`
	Owner               *SimpleProfile        `json:"owner"`
	Thumbnail           *File                 `json:"thumbnail"`
	PreviewVideo        *File                 `json:"preview_video"`
	Categories          []*Category           `json:"categories"`
	Levels              []*Category           `json:"levels"`
	FundingGoal         FundingProps          `json:"funding_goal"`
	Bookmarked          bool                  `json:"bookmarked"`
	OwnerProfile        *LaunchpadPartner     `json:"owner_profile"`
	Outline             []*Section            `json:"outlines"`
	TotalAmount         *decimal.Decimal      `json:"total_amount"`
	TotalBackers        *int                  `json:"total_backers"`
	Investment          *ClpInvestment        `json:"investment"`
	TotalRefunded       decimal.Decimal       `json:"total_refunded"`
}

type FundingProps struct {
	TargetFunding    decimal.Decimal `json:"target_funding"`
	Currency         Currency        `json:"currency"`
	MinPledge        decimal.Decimal `json:"min_pledge"`
	ProfitPercentage float64         `json:"profit_percentage"`
}

type LaunchpadVotingPowerEntry struct {
	UserID      string            `json:"user_id"`
	Address     string            `json:"address"`
	Amount      decimal.Decimal   `json:"amount"`
	TotalAmount decimal.Decimal   `json:"total_amount"`
	VotingPower float64           `json:"voting_power"`
	Network     BlockchainNetwork `json:"network"`
}

type LaunchpadProps struct {
	PoolID            string `json:"pool_id"`
	WalletID          string `json:"wallet_id"`
	RejectOrgReason   string `json:"reject_org_reason"`
	RejectRootReason  string `json:"reject_root_reason"`
	PubDate           int64  `json:"pub_date"`
	PubRootDate       int64  `json:"pub_root_date"`
	PubRootRejectDate int64  `json:"pub_root_reject_date"`
	PubRejectDate     int64  `json:"pub_reject_date"`
}

func (j LaunchpadProps) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *LaunchpadProps) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

func (c *ClpLaunchpad) IsStatusFunding() bool {
	return c.Status == LaunchpadSTTFunding
}

func (c *ClpLaunchpad) IsStatusDraft() bool {
	return c.Status == LaunchpadSTTDraft
}

func (c *ClpLaunchpad) IsStatusVoting() bool {
	return c.Status == LaunchpadSTTVoting
}

func (c *ClpLaunchpad) IsStatusSuccess() bool {
	return c.Status == LaunchpadSTTSuccess
}

func (c *ClpLaunchpad) IsStatusFailed() bool {
	return c.Status == LaunchpadSTTFailed
}

func (c *ClpLaunchpad) IsStatusApproved() bool {
	return c.Status == LaunchpadSTTApproved
}

func (c *ClpLaunchpad) IsStatusWaiting() bool {
	return c.Status == LaunchpadSTTWaiting
}

func (c *ClpLaunchpad) HasFundingTimePeriod() bool {
	return c.FundingStartDate != 0 && c.FundingEndDate != 0
}

func (c *ClpLaunchpad) HasPool() bool {
	return c.Props.PoolID != ""
}

func (c *ClpLaunchpad) GetPoolID() string {
	return c.Props.PoolID
}

func (c *ClpLaunchpad) GetWalletID() string {
	return c.Props.WalletID
}

func (c *ClpLaunchpad) GetCurrency() Currency {
	return c.FundingGoal.Currency
}

func (c *ClpLaunchpad) GetMaxVotingPhases() int {
	rules := GetConfig[[]*CourseLaunchpadVotingPhaseRuleConfig](CourseLaunchpadVotingPhaseRule)
	// Sort by max phases ASC
	sort.Slice(rules, func(i, j int) bool {
		return rules[i].MaxPhases < rules[j].MaxPhases
	})

	var maxPhase int
	for _, rule := range rules {
		if c.FundingGoal.TargetFunding.GreaterThanOrEqual(rule.FundingGoalGte) &&
			(c.FundingGoal.TargetFunding.LessThan(rule.FundingGoalLt) || rule.FundingGoalLt.Equal(decimal.NewFromInt(-1))) {

			maxPhase = rule.MaxPhases
		}
	}
	return maxPhase
}

func (c *ClpLaunchpad) Sanitize() *SimpleLaunchpad {
	simpleLaunchpad := &SimpleLaunchpad{
		Model:               c.Model,
		Name:                c.Name,
		OrgID:               c.OrgID,
		Description:         c.Description,
		Slug:                c.Slug,
		LearnerOutcome:      c.LearnerOutcome,
		Status:              c.Status,
		Enable:              c.Enable,
		VotingStartDate:     c.VotingStartDate,
		VotingEndDate:       c.VotingEndDate,
		EstimateFundingDays: c.EstimateFundingDays,
		FundingStartDate:    c.FundingStartDate,
		FundingEndDate:      c.FundingEndDate,
		UserID:              c.UserID,
		Owner:               c.Owner,
		Thumbnail:           c.Thumbnail,
		PreviewVideo:        c.PreviewVideo,
		Categories:          c.Categories,
		Levels:              c.Levels,
		FundingGoal:         c.FundingGoal,
		VotingMilestones:    c.VotingMilestones,
		Bookmarked:          c.Bookmarked,
		Props:               c.Props,
		OwnerProfile:        c.OwnerProfile,
		Outline:             c.Outline,
		TotalRefunded:       c.TotalRefunded,
	}

	if len(c.Courses) > 0 {
		simpleLaunchpad.Courses = lo.Map(c.Courses, func(course *Course, _ int) *SimpleCourse {
			return course.Sanitize()
		})
	}

	if c.TotalAmount != nil {
		simpleLaunchpad.TotalAmount = c.TotalAmount
	}

	if c.TotalBackers != nil {
		simpleLaunchpad.TotalBackers = c.TotalBackers
	}

	if c.Investment != nil {
		simpleLaunchpad.Investment = c.Investment
	}

	if c.User != nil {
		simpleLaunchpad.User = c.User
	}

	return simpleLaunchpad
}

func (j FundingProps) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *FundingProps) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

type LaunchpadQuery struct {
	ID             *string           `json:"id" form:"id"`
	IDNe           *string           `json:"id_ne" form:"id_ne"`
	IDIn           []string          `json:"id_in" form:"id_in"`
	Name           *string           `json:"name" form:"name"`
	UserID         *string           `json:"user_id" form:"user_id"`
	CourseCuid     *string           `json:"course_cuid" form:"course_cuid"`
	OrgID          *string           `json:"org_id" form:"org_id"`
	Status         *LaunchpadStatus  `json:"status" form:"status"`
	StatusIn       []LaunchpadStatus `json:"status_in" form:"status_in"`
	StatusNotIn    []LaunchpadStatus `json:"status_not_in" form:"status_not_in"`
	IncludeDeleted *bool

	SearchTerm       *string `json:"search_term" form:"search_term"`
	SearchCategories *string `json:"search_categories" form:"search_categories"`

	CategoryIDIn []string `json:"category_id_in" form:"category_id_in"`
}

func (query *LaunchpadQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.IDNe != nil {
		qb = qb.Where("id <> ?", *query.IDNe)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", query.StatusIn)
	}

	if query.StatusNotIn != nil {
		qb = qb.Where("status NOT IN (?)", query.StatusNotIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	searchStr := ""
	searchParams := []any{}
	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		for idx, category := range categories {
			if idx == 0 {
				searchStr += fmt.Sprintf("%s ~ ?", category)
			} else {
				searchStr += fmt.Sprintf(" OR %s ~* ?", category)
			}
			searchParams = append(searchParams, *query.SearchTerm)
		}
		qb = qb.Where(searchStr, searchParams...)

	}

	return qb
}

type LaunchpadPreload struct {
	Categories       bool
	Levels           bool
	Owner            bool
	Courses          bool
	VotingMilestones bool
	User             bool
}

func getExPreloadLaunchpad(preloads []string) (*LaunchpadPreload, []string) {
	shouldPreloadCategories := false
	shouldPreloadLevels := false
	shouldPreloadOwner := false
	shouldPreloadCourses := false
	shouldPreloadVotingMilestones := false
	shouldPreloadUser := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, CoursesField) {
		shouldPreloadCourses = true
		newPreloads = util.RemoveElement(newPreloads, CoursesField)
	}

	if lo.Contains(newPreloads, CategoriesField) {
		shouldPreloadCategories = true
		newPreloads = util.RemoveElement(newPreloads, CategoriesField)
	}

	if lo.Contains(newPreloads, LevelsField) {
		shouldPreloadLevels = true
		newPreloads = util.RemoveElement(newPreloads, LevelsField)
	}

	if lo.Contains(newPreloads, OwnerField) {
		shouldPreloadOwner = true
		newPreloads = util.RemoveElement(newPreloads, OwnerField)
	}

	if lo.Contains(newPreloads, VotingMilestonesField) {
		shouldPreloadVotingMilestones = true
		newPreloads = util.RemoveElement(newPreloads, VotingMilestonesField)
	}

	if lo.Contains(newPreloads, UserField) {
		shouldPreloadUser = true
		newPreloads = util.RemoveElement(newPreloads, UserField)
	}

	return &LaunchpadPreload{
		Categories:       shouldPreloadCategories,
		Levels:           shouldPreloadLevels,
		Owner:            shouldPreloadOwner,
		Courses:          shouldPreloadCourses,
		VotingMilestones: shouldPreloadVotingMilestones,
		User:             shouldPreloadUser,
	}, newPreloads
}

// preloadLaunchpadCourses preloads course latest version for launchpad
func (r *ClpLaunchpadRepository) preloadLaunchpadCourses(launchpad *ClpLaunchpad) error {
	// Find all course_launchpad relationships
	courseLaunchpads, err := Repository.ClpCourseLaunchpad(r.ctx).FindMany(
		&ClpCourseLaunchpadQuery{ClpLaunchpadID: &launchpad.ID}, nil)
	if err != nil {
		return err
	}

	// Extract course_cuids from relationships
	courseCuids := lo.Map(courseLaunchpads, func(cl *ClpCourseLaunchpad, _ int) string {
		return cl.CourseCuid
	})

	// Find all latest courses
	if len(courseCuids) > 0 {
		courses, err := Repository.Course(r.ctx).FindMany(&CourseQuery{
			CuidIn: courseCuids,
			Latest: util.NewBool(true),
		}, nil)
		if err != nil {
			return err
		}

		launchpad.Courses = append(launchpad.Courses, courses...)
	}

	return nil
}

func (r *ClpLaunchpadRepository) preloadLaunchpadsCourses(launchpads []*ClpLaunchpad) error {
	// If no launchpads, return early
	if len(launchpads) == 0 {
		return nil
	}

	// Extract all launchpad IDs
	launchpadIDs := lo.Map(launchpads, func(l *ClpLaunchpad, _ int) string {
		return l.ID
	})

	// Find all course_launchpad relationships for these launchpads
	courseLaunchpads, err := Repository.ClpCourseLaunchpad(r.ctx).FindMany(
		&ClpCourseLaunchpadQuery{
			ClpLaunchpadIDIn: launchpadIDs,
		}, nil)
	if err != nil {
		return err
	}

	// Group relationships by launchpad ID
	courseLaunchpadsByLaunchpadID := lo.GroupBy(courseLaunchpads, func(cl *ClpCourseLaunchpad) string {
		return cl.ClpLaunchpadID
	})

	// Extract unique course CUIDs
	courseCuidSet := make(map[string]struct{})
	for _, cl := range courseLaunchpads {
		courseCuidSet[cl.CourseCuid] = struct{}{}
	}

	// Convert set to slice
	courseCuids := lo.Keys(courseCuidSet)

	// Find all latest courses if we have any course CUIDs
	if len(courseCuids) > 0 {
		courses, err := Repository.Course(r.ctx).FindMany(&CourseQuery{
			CuidIn: courseCuids,
			Latest: util.NewBool(true),
		}, nil)
		if err != nil {
			return err
		}

		// Create map of course by CUID for easy lookup
		coursesByCuid := lo.KeyBy(courses, func(c *Course) string {
			return c.Cuid
		})

		// Assign courses to each launchpad
		for _, launchpad := range launchpads {
			// Get course relationships for this launchpad
			if courseLaunchpads, ok := courseLaunchpadsByLaunchpadID[launchpad.ID]; ok {
				// Map each relationship to its course
				for _, cl := range courseLaunchpads {
					if course, exists := coursesByCuid[cl.CourseCuid]; exists {
						launchpad.Courses = append(launchpad.Courses, course)
					}
				}
			}
		}
	}

	return nil
}

func preloadLaunchpadCategories(launchpads []*ClpLaunchpad) error {
	ids := lo.Map(launchpads, func(launchpad *ClpLaunchpad, _ int) string {
		return launchpad.ID
	})
	if categoriesByLaunchpadIDs, mErr := Repository.CategoryRelation.GetCategoriesByEntities(ClpLaunchpadModelName, ids, CategoriesField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(launchpads, func(launchpad *ClpLaunchpad, _ int) {
			launchpad.Categories = categoriesByLaunchpadIDs[launchpad.ID]
		})
	}
	return nil
}

func preloadLaunchpadLevels(launchpads []*ClpLaunchpad) error {
	ids := lo.Map(launchpads, func(launchpad *ClpLaunchpad, _ int) string {
		return launchpad.ID
	})
	if categoriesByLaunchpadIDs, mErr := Repository.CategoryRelation.GetCategoriesByEntities(ClpLaunchpadModelName, ids, LevelsField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(launchpads, func(launchpad *ClpLaunchpad, _ int) {
			launchpad.Levels = categoriesByLaunchpadIDs[launchpad.ID]
		})
	}
	return nil
}

func preloadLaunchpadOwner(launchpads []*ClpLaunchpad) error {
	userIDs := lo.Map(launchpads, func(launchpad *ClpLaunchpad, _ int) string {
		return launchpad.UserID
	})
	if owners, pErr := Repository.User.FindMany(
		&UserQuery{IDIn: &userIDs},
		&FindManyOptions{}); pErr != nil {
		if !errors.Is(pErr, gorm.ErrRecordNotFound) {
			return pErr
		}
	} else {
		ownerMap := make(map[string]*User)
		for _, owner := range owners {
			ownerMap[owner.ID] = owner
		}

		for _, launchpad := range launchpads {
			if owner, exists := ownerMap[launchpad.UserID]; exists {
				launchpad.Owner = owner.ToSimpleProfile()
			}
		}
	}
	return nil
}

func preloadLaunchpadUser(launchpad *ClpLaunchpad) error {
	user, err := Repository.User.FindByID(launchpad.UserID)
	if err != nil {
		return err
	}

	launchpad.User = user
	return nil
}

func preloadVotingMilestone(launchpad *ClpLaunchpad) error {
	if votingMilestones, pErr := Repository.ClpVotingMilestone.FindMany(
		&ClpVotingMilestoneQuery{ClpLaunchpadID: &launchpad.ID}, &FindManyOptions{Sort: []string{OrderASC}}); pErr != nil {
		return pErr
	} else {
		launchpad.VotingMilestones = votingMilestones
	}

	return nil
}

func preloadVotingMilestones(launchpads []*ClpLaunchpad) error {
	if len(launchpads) == 0 {
		return nil
	}

	// Collect all launchpad IDs
	launchpadIDs := make([]string, len(launchpads))
	launchpadMap := make(map[string]*ClpLaunchpad)

	for i, launchpad := range launchpads {
		launchpadIDs[i] = launchpad.ID
		launchpadMap[launchpad.ID] = launchpad
	}

	votingMilestones, err := Repository.ClpVotingMilestone.FindMany(
		&ClpVotingMilestoneQuery{
			ClpLaunchpadIDIn: launchpadIDs,
		},
		&FindManyOptions{Sort: []string{OrderASC}},
	)
	if err != nil {
		return err
	}

	// Group milestones by launchpad ID
	for _, milestone := range votingMilestones {
		if launchpad, exists := launchpadMap[milestone.ClpLaunchpadID]; exists {
			launchpad.VotingMilestones = append(launchpad.VotingMilestones, milestone)
		}
	}

	return nil
}

func (c *ClpLaunchpad) GetUserID() string {
	return c.UserID
}

func (c *ClpLaunchpad) GetID() string {
	return c.ID
}

func applyLaunchpadSort(sorts []string) []string {
	if sorts == nil {
		return nil
	}

	newSort := make([]string, 0, len(sorts))
	for _, sortClause := range sorts {
		switch strings.ToLower(sortClause) {
		case TargetFundingASC:
			newSort = append(newSort, "(funding_goal->>'target_funding')::int ASC")
		case TargetFundingDESC:
			newSort = append(newSort, "(funding_goal->>'target_funding')::int DESC")
		default:
			newSort = append(newSort, sortClause)
		}
	}

	return newSort
}

// Create inserts an org to database, transaction is optional
func (r *ClpLaunchpadRepository) Create(c *ClpLaunchpad, trans *gorm.DB) error {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	// Create launchpad
	if err := create(ClpLaunchpadTbl, c, tx); err != nil {
		if trans == nil {
			tx.Rollback()
		}
		return err
	}

	// Add categories
	if cErr := Repository.CategoryRelation.AddCategories(ClpLaunchpadModelName, c.ID, CategoriesField, c.Categories, tx); cErr != nil {
		if trans == nil {
			tx.Rollback()
		}
		return cErr
	}

	// Add levels
	if cErr := Repository.CategoryRelation.AddCategories(ClpLaunchpadModelName, c.ID, LevelsField, c.Levels, tx); cErr != nil {
		if trans == nil {
			tx.Rollback()
		}
		return cErr
	}

	if trans == nil {
		return tx.Commit().Error
	}

	return nil
}

// Update updates a launchpad by ID in database, transaction is optional
func (r *ClpLaunchpadRepository) Update(c *ClpLaunchpad, trans *gorm.DB) error {
	if err := update(ClpLaunchpadTbl, c, trans); err != nil {
		return err
	}

	if cErr := Repository.CategoryRelation.AddCategories(ClpLaunchpadModelName, c.ID, CategoriesField, c.Categories, nil); cErr != nil {
		return cErr
	}

	if cErr := Repository.CategoryRelation.AddCategories(ClpLaunchpadModelName, c.ID, LevelsField, c.Levels, nil); cErr != nil {
		return cErr
	}

	return nil
}

// FindByID finds a v by ID with given find options, transaction is optional
func (r *ClpLaunchpadRepository) FindByID(id string, options *FindOneOptions) (*ClpLaunchpad, error) {
	return findByID[ClpLaunchpad](ClpLaunchpadTbl, id, options)
}

// FindOne finds one launchpad with given find queries and options, transaction is optional
func (r *ClpLaunchpadRepository) FindOne(query *LaunchpadQuery, options *FindOneOptions) (*ClpLaunchpad, error) {
	if options == nil {
		options = &FindOneOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := getExPreloadLaunchpad(preloads)
	options.Preloads = newPreloads
	launchpad, err := findOne[ClpLaunchpad](ClpLaunchpadTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Categories {
		if categories, mErr := Repository.CategoryRelation.GetCategories(ClpLaunchpadModelName, launchpad.ID, CategoriesField); mErr != nil {
			return launchpad, mErr
		} else {
			launchpad.Categories = categories
		}
	}

	if exPreload.Levels {
		if levels, mErr := Repository.CategoryRelation.GetCategories(ClpLaunchpadModelName, launchpad.ID, LevelsField); mErr != nil {
			return launchpad, mErr
		} else {
			launchpad.Levels = levels
		}
	}

	if exPreload.Courses {
		if mErr := r.preloadLaunchpadCourses(launchpad); mErr != nil {
			return launchpad, mErr
		}
	}

	if exPreload.VotingMilestones {
		if mErr := preloadVotingMilestone(launchpad); mErr != nil {
			return launchpad, mErr
		}
	}

	if exPreload.Owner {
		if owner, preErr := Repository.User.FindByID(launchpad.UserID); preErr != nil {
			return nil, fmt.Errorf("preload form relations error: %v", preErr)
		} else {
			launchpad.Owner = owner.ToSimpleProfile()
		}
	}

	if launchpad.ThumbnailID != "" {
		if thumb, tErr := Repository.File.FindByID(launchpad.ThumbnailID, nil); tErr != nil {
			if !errors.Is(tErr, gorm.ErrRecordNotFound) {
				return nil, errors.New("find thumbnail failed: " + tErr.Error())
			}
		} else {
			launchpad.Thumbnail = thumb
		}
	}

	if launchpad.PreviewVideoID != "" {
		if video, tErr := Repository.File.FindByID(launchpad.PreviewVideoID, nil); tErr != nil {
			if !errors.Is(tErr, gorm.ErrRecordNotFound) {
				return nil, errors.New("find preview video failed: " + tErr.Error())
			}
		} else {
			launchpad.PreviewVideo = video
		}
	}

	if exPreload.User {
		if mErr := preloadLaunchpadUser(launchpad); mErr != nil {
			return launchpad, mErr
		}
	}

	return launchpad, err
}

// FindMany finds launchpads by query conditions with give find options
func (r *ClpLaunchpadRepository) FindMany(query *LaunchpadQuery, options *FindManyOptions) ([]*ClpLaunchpad, error) {
	if options == nil {
		options = &FindManyOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	// Apply sort
	options.Sort = applyLaunchpadSort(options.Sort)
	exPreload, newPreloads := getExPreloadLaunchpad(preloads)
	options.Preloads = newPreloads
	launchpads, err := findMany[ClpLaunchpad](ClpLaunchpadTbl, query, options)
	if err != nil {
		return nil, err
	}

	thumbIds := lo.Map(launchpads, func(item *ClpLaunchpad, _ int) string {
		return item.ThumbnailID
	})
	if len(thumbIds) > 0 {
		if thumbs, tErr := Repository.File.FindMany(&FileQuery{IDIn: thumbIds}, nil); tErr != nil {
			return nil, tErr
		} else {
			lo.ForEach(launchpads, func(launchpad *ClpLaunchpad, _ int) {
				t, ok := lo.Find(thumbs, func(item *File) bool {
					return item.ID == launchpad.ThumbnailID
				})
				if ok {
					launchpad.Thumbnail = t
				}
			})
		}
	}

	videoIds := lo.Map(launchpads, func(item *ClpLaunchpad, _ int) string {
		return item.PreviewVideoID
	})
	if len(videoIds) > 0 {
		if videos, tErr := Repository.File.FindMany(&FileQuery{IDIn: videoIds}, nil); tErr != nil {
			return nil, tErr
		} else {
			lo.ForEach(launchpads, func(launchpad *ClpLaunchpad, _ int) {
				t, ok := lo.Find(videos, func(item *File) bool {
					return item.ID == launchpad.PreviewVideoID
				})
				if ok {
					launchpad.PreviewVideo = t
				}
			})
		}
	}

	if exPreload.Categories {
		if catErr := preloadLaunchpadCategories(launchpads); catErr != nil {
			return launchpads, catErr
		}
	}

	if exPreload.Levels {
		if catErr := preloadLaunchpadLevels(launchpads); catErr != nil {
			return launchpads, catErr
		}
	}

	if exPreload.Owner {
		if preErr := preloadLaunchpadOwner(launchpads); preErr != nil {
			return launchpads, fmt.Errorf("preload owner error: %v", preErr)
		}
	}

	if exPreload.Courses {
		if catErr := r.preloadLaunchpadsCourses(launchpads); catErr != nil {
			return launchpads, catErr
		}
	}

	if exPreload.VotingMilestones {
		if mErr := preloadVotingMilestones(launchpads); mErr != nil {
			return launchpads, mErr
		}
	}

	return launchpads, err
}

// FindPage returns launchpads and pagination by query conditions and find options, transaction is optional
func (r *ClpLaunchpadRepository) FindPage(query *LaunchpadQuery, options *FindPageOptions) ([]*ClpLaunchpad, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	// Apply sort
	options.Sort = applyLaunchpadSort(options.Sort)
	exPreload, newPreloads := getExPreloadLaunchpad(preloads)
	options.Preloads = newPreloads
	launchpads, pagination, err := findPage[ClpLaunchpad](ClpLaunchpadTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	thumbIds := lo.Map(launchpads, func(item *ClpLaunchpad, _ int) string {
		return item.ThumbnailID
	})

	if len(thumbIds) > 0 {
		if thumbs, tErr := Repository.File.FindMany(&FileQuery{IDIn: thumbIds}, nil); tErr != nil {
			return nil, nil, tErr
		} else {
			lo.ForEach(launchpads, func(launchpad *ClpLaunchpad, _ int) {
				t, ok := lo.Find(thumbs, func(item *File) bool {
					return item.ID == launchpad.ThumbnailID
				})
				if ok {
					launchpad.Thumbnail = t
				}
			})
		}
	}

	videoIds := lo.Map(launchpads, func(item *ClpLaunchpad, _ int) string {
		return item.PreviewVideoID
	})
	if len(videoIds) > 0 {
		if videos, tErr := Repository.File.FindMany(&FileQuery{IDIn: videoIds}, nil); tErr != nil {
			return nil, nil, tErr
		} else {
			lo.ForEach(launchpads, func(launchpad *ClpLaunchpad, _ int) {
				t, ok := lo.Find(videos, func(item *File) bool {
					return item.ID == launchpad.PreviewVideoID
				})
				if ok {
					launchpad.PreviewVideo = t
				}
			})
		}
	}

	if exPreload.Categories {
		if catErr := preloadLaunchpadCategories(launchpads); catErr != nil {
			return launchpads, pagination, catErr
		}
	}

	if exPreload.Levels {
		if catErr := preloadLaunchpadLevels(launchpads); catErr != nil {
			return launchpads, pagination, catErr
		}
	}

	if exPreload.Owner {
		if preErr := preloadLaunchpadOwner(launchpads); preErr != nil {
			return launchpads, pagination, fmt.Errorf("preload owner error: %v", preErr)
		}
	}

	if exPreload.Courses {
		if catErr := r.preloadLaunchpadsCourses(launchpads); catErr != nil {
			return launchpads, pagination, catErr
		}
	}

	if exPreload.VotingMilestones {
		if mErr := preloadVotingMilestones(launchpads); mErr != nil {
			return launchpads, pagination, mErr
		}
	}

	return launchpads, pagination, err
}

// Delete perform soft deletion to a org by ID, transaction is optional
func (r *ClpLaunchpadRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ClpLaunchpad](ClpLaunchpadTbl, id, trans)
}

// Count returns number of courses by query conditions, transaction is optional
func (r *ClpLaunchpadRepository) Count(query *LaunchpadQuery) (int64, error) {
	return count[ClpLaunchpad](ClpLaunchpadTbl, query)
}

func (r *ClpLaunchpadRepository) ValidateFields(fields []string) error {
	validFields := map[string]bool{
		CategoriesField:       true,
		LevelsField:           true,
		UserField:             true,
		CoursesField:          true,
		VotingMilestonesField: true,
		OwnerField:            true,
		InvestmentField:       true,
		OwnerProfileField:     true,
		OutlineField:          true,
	}

	for _, field := range fields {
		if !validFields[field] {
			return fmt.Errorf("invalid field: %s", field)
		}
	}

	return nil
}
