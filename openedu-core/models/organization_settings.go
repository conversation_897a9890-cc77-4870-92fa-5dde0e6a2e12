package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/util"
)

type OrgSetting struct {
	SenderEmail    string              `json:"sender_email"`
	LoginProviders []*OrgLoginProvider `json:"login_providers"`
	Locales        []string            `json:"locales"`
}

func (s OrgSetting) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	return string(val), err
}

func (s *OrgSetting) Scan(src interface{}) error {
	source, ok := src.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &s)
	if err != nil {
		return err
	}

	return nil
}

func (s *OrgSetting) ToJSONB() (*JSONB, error) {
	var o JSONB
	b, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(b, &o); err != nil {
		return nil, err
	}
	return &o, nil
}

func (s *OrgSetting) IsValid() error {

	seen := map[util.SNSProvider]struct{}{}
	for _, item := range s.LoginProviders {
		seen[item.Name] = struct{}{}
	}

	requiredProviders := []util.SNSProvider{
		util.Kakao,
		util.Naver,
		util.Facebook,
		util.Google,
		util.Twitter,
	}

	validProviders := map[util.SNSProvider]struct{}{}
	for _, provider := range requiredProviders {
		if _, ok := seen[provider]; !ok {
			return fmt.Errorf("invalid login settings: missing provider %s", provider)
		}
		validProviders[provider] = struct{}{}
	}

	countProvider := map[util.SNSProvider]int{}
	for _, item := range s.LoginProviders {
		if _, ok := countProvider[item.Name]; !ok {
			countProvider[item.Name] = 1
		} else {
			countProvider[item.Name]++
		}

		if _, ok := validProviders[item.Name]; !ok {
			return fmt.Errorf("invalid login settings: invalid provider %s", item.Name)
		}
	}

	for provider, count := range countProvider {
		if count > 1 {
			return fmt.Errorf("invalid login settings: provider %s appear %d times", provider, count)
		}
	}

	return nil
}

func MakeDefaultOrgSettings() *OrgSetting {
	return &OrgSetting{
		//LoginProviders: MakeDefaultLoginProviders(),
		SenderEmail: "",
	}
}
