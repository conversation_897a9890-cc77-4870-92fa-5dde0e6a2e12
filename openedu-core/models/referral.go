package models

import (
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Referral struct {
	Model
	OrgID        string `json:"org_id" gorm:"type:varchar(20);not null"`
	CourseCuid   string `json:"course_cuid" gorm:"type:varchar(20);not null"`
	CourseID     string `json:"course_id" gorm:"type:varchar(20)"`
	CampaignID   string `json:"campaign_id" gorm:"type:varchar(20);not null"`
	CommissionID string `json:"commission_id" gorm:"type:varchar(20);not null"`

	OrderID     string          `json:"order_id" gorm:"type:varchar(20);not null"`
	OrderAmount decimal.Decimal `json:"order_amount" gorm:"type:numeric(19,4);not null;default:0"`
	Currency    Currency        `json:"currency" gorm:"default:VND"`

	Ref1UserID        string          `json:"ref1_user_id" gorm:"type:varchar(20)"`
	Ref1LinkID        string          `json:"ref1_link_id"`
	Ref1Rate          float32         `json:"ref1_rate" gorm:"type:float8;default:0"`
	Ref1Amount        decimal.Decimal `json:"ref1_amount" gorm:"type:numeric(19,4);not null;default:0"`
	ShareRate         float32         `json:"share_rate" gorm:"type:float8;default:0"`
	ShareAmount       decimal.Decimal `json:"share_amount" gorm:"type:numeric(19,4);not null;default:0"`
	BonusCommissionID string          `json:"bonus_commission_id" gorm:"type:varchar(20);default:null"`
	BonusRate         float32         `json:"bonus_rate" gorm:"type:float8;default:0"`
	BonusAmount       decimal.Decimal `json:"bonus_amount" gorm:"type:numeric(19,4);not null;default:0"`

	Ref2UserID string          `json:"ref2_user_id" gorm:"type:varchar(20);default:null"`
	Ref2LinkID string          `json:"ref2_link_id" gorm:"type:varchar(20);default:null"`
	Ref2Rate   float32         `json:"ref2_rate" gorm:"type:float8;default:0"`
	Ref2Amount decimal.Decimal `json:"ref2_amount" gorm:"type:numeric(19,4);not null;default:0"`

	Ref3UserID string          `json:"ref3_user_id" gorm:"type:varchar(20);default:null"`
	Ref3LinkID string          `json:"ref3_link_id" gorm:"type:varchar(20);default:null"`
	Ref3Rate   float32         `json:"ref3_rate" gorm:"type:float8;default:0"`
	Ref3Amount decimal.Decimal `json:"ref3_amount" gorm:"type:numeric(19,4);not null;default:0"`

	PubCourse *PublishCourse     `json:"pub_course" gorm:"-"`
	Campaign  *AffiliateCampaign `json:"campaign"`
	Order     *Order             `json:"order"`
	Ref1User  *User              `json:"ref1_user"`
	Ref2User  *User              `json:"ref2_user"`
	Ref3User  *User              `json:"ref3_user"`
	Ref1Link  *ReferralLink      `json:"ref1_link"`
	Ref2Link  *ReferralLink      `json:"ref2_link"`
	Ref3Link  *ReferralLink      `json:"ref3_link"`
}

type ReferralQuery struct {
	ID              *string   `json:"id" form:"id"`
	IDIn            []*string `json:"id_in" form:"id_in"`
	Ref1UserID      *string   `json:"ref1_user_id" form:"ref1_user_id"`
	Ref2UserID      *string   `json:"ref2_user_id" form:"ref2_user_id"`
	Ref3UserID      *string   `json:"ref3_user_id" form:"ref3_user_id"`
	CourseCuid      *string   `json:"course_cuid" form:"course_cuid"`
	CampaignID      *string   `json:"campaign_id" form:"campaign_id"`
	CommissionID    *string   `json:"commission_id" form:"commission_id"`
	OrgID           *string   `json:"org_id" form:"org_id"`
	IncludeDeleted  *bool     `form:"include_deleted"`
	Relative2UserID *string   `json:"relative_2_user" form:"relative_2_user"`
	UserID          *string   `json:"user_id" form:"user_id"`
	FromDate        *int64    `json:"from_date" form:"from_date"`
	ToDate          *int64    `json:"to_date" form:"to_date"`
	CampaignOwnerID *string   `json:"campaign_owner_id" form:"campaign_owner_id"`
}

func (query *ReferralQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	refTbl := GetTblName(ReferralTbl)
	if query.ID != nil {
		qb = qb.Where(refTbl+".id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where(refTbl+".id IN (?)", query.IDIn)
	}

	if query.Ref1UserID != nil {
		qb = qb.Where(refTbl+".ref1_user_id = ?", *query.Ref1UserID)
	}

	if query.Ref2UserID != nil {
		qb = qb.Where(refTbl+".ref2_user_id = ?", *query.Ref2UserID)
	}

	if query.Ref3UserID != nil {
		qb = qb.Where(refTbl+".ref3_user_id = ?", *query.Ref3UserID)
	}

	if query.OrgID != nil {
		qb = qb.Where(refTbl+".org_id = ?", *query.OrgID)
	}

	if query.CourseCuid != nil {
		qb = qb.Where(refTbl+".course_cuid = ?", *query.CourseCuid)
	}

	if query.CampaignID != nil {
		qb = qb.Where(refTbl+".campaign_id = ?", *query.CampaignID)
	}

	if query.CommissionID != nil {
		qb = qb.Where(refTbl+".commission_id = ?", *query.CommissionID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where(refTbl + ".delete_at = 0")
	}

	if query.Relative2UserID != nil {
		qb = qb.Where(fmt.Sprintf("(%[1]s.ref1_user_id = '%[2]s' OR %[1]s.ref2_user_id = '%[2]s' OR %[1]s.ref3_user_id = '%[2]s')", refTbl, *query.Relative2UserID))
	}

	if query.FromDate != nil {
		qb = qb.Where(refTbl+".create_at >= ?", query.FromDate)
	}

	if query.ToDate != nil {
		qb = qb.Where(refTbl+".create_at <= ?", query.ToDate)
	}

	if query.CampaignOwnerID != nil {
		qb = qb.Joins(fmt.Sprintf("INNER JOIN %[1]s ON %[1]s.id = %[2]s.campaign_id", GetTblName(AffiliateCampaignTbl), GetTblName(ReferralTbl))).
			Where(fmt.Sprintf("%[1]s.user_id = ?", GetTblName(AffiliateCampaignTbl)), *query.CampaignOwnerID)
	}

	return qb
}

func (r *ReferralRepository) Create(e *Referral, trans *gorm.DB) error {
	return create(ReferralTbl, e, trans)
}

func (r *ReferralRepository) CreateMany(ts []*Referral, trans *gorm.DB) error {
	return createMany(ReferralTbl, ts, trans)
}

func (r *ReferralRepository) Update(f *Referral, trans *gorm.DB) error {
	return update(ReferralTbl, f, trans)
}

func (r *ReferralRepository) FindOne(query *ReferralQuery, options *FindOneOptions) (*Referral, error) {
	return findOne[Referral](ReferralTbl, query, options)
}

func (r *ReferralRepository) FindPage(query *ReferralQuery, options *FindPageOptions) ([]*Referral, *Pagination, error) {
	return findPage[Referral](ReferralTbl, query, options)
}

func (r *ReferralRepository) FindMany(query *ReferralQuery, options *FindManyOptions) ([]*Referral, error) {
	return findMany[Referral](ReferralTbl, query, options)
}

func (r *ReferralRepository) Count(query *ReferralQuery) (int64, error) {
	return count[Referral](ReferralTbl, query)
}

func (r *ReferralRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Referral](ReferralTbl, id, trans)
}
