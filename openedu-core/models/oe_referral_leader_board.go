package models

import "gorm.io/gorm"

type OEReferralLeaderBoard struct {
	Model
	OrgID       string     `json:"org_id" gorm:"type:varchar(20)"`
	UserID      string     `json:"user_id" gorm:"type:varchar(20)"`
	ParentID    string     `json:"parent_id" gorm:"type:varchar(20)"`
	ParentName  string     `json:"parent_name"`
	LocalLevel  LocalLevel `json:"local_level"`
	DisplayName string     `json:"display_name"`
	Email       string     `json:"email"`
	RefCode     string     `json:"ref_code"`
	RefCodeID   string     `json:"ref_code_id"`

	RegisterCount        int64   `json:"register_count" gorm:"default:0"`
	RefCount             int64   `json:"ref_count" gorm:"default:0"`
	CertCount            int64   `json:"cert_count" gorm:"default:0"`
	CompletedCourseCount int64   `json:"completed_course_count" gorm:"default:0"`
	PercentCertOnRef     float64 `json:"percent_cert_on_ref" gorm:"default:0"`
	PercentCertOnReg     float64 `json:"percent_cert_on_reg" gorm:"default:0"`
	CourseCuid           string  `json:"course_cuid"` // each record each for each course
	CampaignKey          string  `json:"campaign_key"`
}

type OEReferralLeaderBoardQuery struct {
	ID              *string     `json:"id" form:"id"`
	OrgID           *string     `json:"org_id" form:"org_id"`
	UserID          *string     `json:"user_id" form:"user_id"`
	ParentID        *string     `json:"parent_id" form:"parent_id"`
	LocalLevel      *LocalLevel `json:"local_level" form:"local_level"`
	Email           *string     `json:"email" form:"email"`
	PointCampaignID *string     `json:"point_campaign_id" form:"point_campaign_id"`
	CourseCuid      *string     `json:"course_cuid" form:"course_cuid"`
	CampaignKey     *string     `json:"campaign_key" form:"campaign_key"`
	Deleted         *bool       `json:"deleted" form:"deleted"`
	DisplayName     *string     `json:"display_name" form:"display_name"`
	DisplayNameNe   *string     `json:"display_name_ne" form:"display_name_ne"`
}

func (q *OEReferralLeaderBoardQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if q.ID != nil {
		qb = qb.Where("id = ?", *q.ID)
	}

	if q.OrgID != nil {
		qb = qb.Where("org_id = ?", *q.OrgID)
	}

	if q.UserID != nil {
		qb = qb.Where("user_id = ?", *q.UserID)
	}

	if q.ParentID != nil {
		qb = qb.Where("parent_id = ?", *q.ParentID)
	}

	if q.LocalLevel != nil {
		qb = qb.Where("local_level = ?", *q.LocalLevel)
	}

	if q.Email != nil {
		qb = qb.Where("email = ?", *q.Email)
	}

	if q.PointCampaignID != nil {
		qb = qb.Where("point_campaign_id = ?", *q.PointCampaignID)
	}

	if q.CourseCuid != nil {
		qb = qb.Where("course_cuid = ?", *q.CourseCuid)
	}

	if q.CampaignKey != nil {
		qb = qb.Where("campaign_key = ?", *q.CampaignKey)
	}

	if q.DisplayName != nil {
		qb = qb.Where("display_name = ?", *q.DisplayName)
	}

	if q.DisplayNameNe != nil {
		qb = qb.Where("display_name <> ?", *q.DisplayNameNe)
	}

	if q.Deleted == nil || !*q.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *OEReferralLeaderBoardRepository) Create(entity *OEReferralLeaderBoard, trans *gorm.DB) error {
	return create(OEReferralLeaderBoardTbl, entity, trans)
}

func (r *OEReferralLeaderBoardRepository) CreateMany(entities []*OEReferralLeaderBoard, trans *gorm.DB) error {
	return createMany(OEReferralLeaderBoardTbl, entities, trans)
}

func (r *OEReferralLeaderBoardRepository) Update(entity *OEReferralLeaderBoard, trans *gorm.DB) error {
	return update(OEReferralLeaderBoardTbl, entity, trans)
}

func (r *OEReferralLeaderBoardRepository) FindByID(id string, options *FindOneOptions) (*OEReferralLeaderBoard, error) {
	return findByID[OEReferralLeaderBoard](OEReferralLeaderBoardTbl, id, options)
}

func (r *OEReferralLeaderBoardRepository) FindOne(query *OEReferralLeaderBoardQuery, options *FindOneOptions) (*OEReferralLeaderBoard, error) {
	return findOne[OEReferralLeaderBoard](OEReferralLeaderBoardTbl, query, options)
}

func (r *OEReferralLeaderBoardRepository) FindMany(query *OEReferralLeaderBoardQuery, options *FindManyOptions) ([]*OEReferralLeaderBoard, error) {
	return findMany[OEReferralLeaderBoard](OEReferralLeaderBoardTbl, query, options)
}

func (r *OEReferralLeaderBoardRepository) FindPage(query *OEReferralLeaderBoardQuery, options *FindPageOptions) ([]*OEReferralLeaderBoard, *Pagination, error) {
	return findPage[OEReferralLeaderBoard](OEReferralLeaderBoardTbl, query, options)
}

func (r *OEReferralLeaderBoardRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OEReferralLeaderBoard](OEReferralLeaderBoardTbl, id, trans)
}

func (r *OEReferralLeaderBoardRepository) DeleteMany(query *OEReferralLeaderBoardQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[OEReferralLeaderBoard](OEReferralLeaderBoardTbl, query, trans)
}

// Count returns number of UserActions by query conditions, transaction is optional
func (r *OEReferralLeaderBoardRepository) Count(query *OEReferralLeaderBoardQuery) (int64, error) {
	return count[OEReferralLeaderBoard](OEReferralLeaderBoardTbl, query)
}
