package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type CourseProgressStatus string

const (
	CourseProgressStatusNotStarted  CourseProgressStatus = "not_started"
	CourseProgressStatusInpProgress CourseProgressStatus = "in_progress"
	CourseProgressStatusCompleted   CourseProgressStatus = "completed"
)

type ProgressEvent string

const (
	CourseProgress        ProgressEvent = "course_progress"
	SectionProgress       ProgressEvent = "section_progress"
	LessonProgress        ProgressEvent = "lesson_progress"
	LessonContentProgress ProgressEvent = "lesson_content_progress"
	LatestLessonProgress  ProgressEvent = "latest_lesson_progress"
)

type LearningStatus struct {
	Model
	OrgID             string           `json:"org_id,omitempty" gorm:"not null;type:varchar(20)"`
	UserID            string           `json:"user_id,omitempty" gorm:"not null;type:varchar(20)"`
	CourseCuid        string           `json:"course_cuid,omitempty" gorm:"not null;type:varchar(20)"`
	StartAt           int64            `json:"start_at,omitempty" gorm:"type:int8;not null;default:0"`
	CompletedAt       int64            `json:"completed_at,omitempty" gorm:"type:int8;not null;default:0"`
	Sections          SectionRecordMap `json:"sections,omitempty" gorm:"type:jsonb"`
	CurrentLessonUID  string           `json:"current_lesson_uid,omitempty" gorm:"type:varchar(20)"`
	CurrentSectionUID string           `json:"current_section_uid,omitempty" gorm:"type:varchar(20)"`
	LatestLessonUID   string           `json:"latest_lesson_uid,omitempty" gorm:"type:varchar(20)"`
	LatestSectionUID  string           `json:"latest_section_uid,omitempty" gorm:"type:varchar(20)"`

	User   *User
	Org    *Organization
	Course *Course `json:"course,omitempty" gorm:"-"`
}

type SectionRecord struct {
	SectionUID string          `json:"section_uid"`
	StartAt    int64           `json:"start_at"`
	CompleteAt int64           `json:"completed_at"`
	Lessons    LessonRecordMap `json:"lessons"`
}

type SectionRecordMap map[string]*SectionRecord

func (p SectionRecordMap) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (p *SectionRecordMap) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, p)
}

type LessonRecordMap map[string]*LessonRecord

type LessonRecord struct {
	LessonUID  string           `json:"lesson_uid"`
	StartAt    int64            `json:"start_at"`
	CompleteAt int64            `json:"completed_at"`
	Contents   ContentRecordMap `json:"contents"`
}

func (p LessonRecordMap) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (p *LessonRecordMap) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, p)
}

type ContentRecordMap map[string]*ContentRecord

type ContentRecord struct {
	ContentUID     string                  `json:"content_uid"`
	ContentType    LessonContentType       `json:"content_type"`
	StartAt        int64                   `json:"start_at"`
	PauseAt        int64                   `json:"pause_at"`
	TextPercent    float64                 `json:"text_percent,omitempty"`
	PdfCurrentPage int                     `json:"pdf_current_page,omitempty"`
	Quizzes        []*QuizLearningProgress `json:"quizzes,omitempty"`
	CompleteAt     int64                   `json:"completed_at"`
}

func (p ContentRecordMap) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (p *ContentRecordMap) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, p)
}

type QuizLearningProgresses []*QuizLearningProgress

func (p QuizLearningProgresses) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (p *QuizLearningProgresses) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, p)
}

type LearningProgressOverview struct {
	CourseCuid       string                              `json:"course_cuid"`
	TotalLesson      int                                 `json:"total_lesson"`
	CompletedLesson  int                                 `json:"completed_lesson"`
	TotalSection     int                                 `json:"total_section"`
	CompletedSection int                                 `json:"completed_section"`
	CompleteAt       int64                               `json:"complete_at"`
	SectionByUID     map[string]*SectionLearningProgress `json:"section_by_uid,omitempty"` // map[SectionUID]*SectionLearningProgress
}

type SimpleLearningProgressOverview struct {
	TotalLesson     int64    `json:"total_lessons"`
	CompletedLesson int64    `json:"completed_lessons"`
	CurrentSection  *Section `json:"current_section"`
	CurrentLesson   *Section `json:"current_lesson"`
}

type CompletedLessonCountByUser struct {
	CourseCUID       string `json:"course_cuid" gorm:"column:course_cuid"`
	UserID           string `json:"user_id"`
	CompletedLessons int64  `json:"completed_lessons"`
	TotalLessons     int64  `json:"total_lessons"`
}

type SectionLearningProgress struct {
	SectionUID      string                             `json:"section_uid"`
	TotalLesson     int                                `json:"total_lesson"`
	CompletedLesson int                                `json:"completed_lesson"`
	CompleteAt      int64                              `json:"complete_at"`
	LessonByUID     map[string]*LessonLearningProgress `json:"lesson_by_uid,omitempty"`
}

type LessonLearningProgress struct {
	LessonUID              string                                    `json:"lesson_uid"`
	TotalLessonContent     int                                       `json:"total_lesson_content"`
	CompletedLessonContent int                                       `json:"completed_lesson_content"`
	CompleteAt             int64                                     `json:"complete_at"`
	LessonContentByUID     map[string]*LessonContentLearningProgress `json:"lesson_content_by_uid,omitempty"`
	CompletedPercent       float64                                   `json:"completed_percent"`
}

type LessonContentLearningProgress struct {
	LessonContentUID string                  `json:"lesson_content_uid"`
	CompleteAt       int64                   `json:"complete_at"`
	PauseAt          int64                   `json:"pause_at"`
	StartAt          int64                   `json:"start_at"`
	ContentType      LessonContentType       `json:"content_type"`
	Duration         int64                   `json:"duration"`
	TextPercent      float64                 `json:"text_percent"`
	VideoPercent     float64                 `json:"video_percent"`
	PdfCurrentPage   int                     `json:"pdf_current_page"`
	Quizzes          *QuizLearningProgresses `json:"quizzes,omitempty"`
}

type QuizLearningProgress struct {
	QuizID  string               `json:"quiz_id" gorm:"type:varchar(20);not null"`
	QuizUID string               `json:"quiz_uid"`
	Status  QuizSubmissionStatus `json:"status"`
	Passed  bool                 `json:"passed"`
}

type LearningProgressByCourse struct {
	SectionUID                  string                  `json:"section_uid"`
	LessonUID                   string                  `json:"lesson_uid"`
	LessonContentUID            string                  `json:"lesson_content_uid"`
	LearningProgressContentType LessonContentType       `json:"learning_progress_content_type"`
	CompleteAt                  int64                   `json:"complete_at"`
	PauseAt                     int64                   `json:"pause_at"`
	StartAt                     int64                   `json:"start_at"`
	Duration                    int64                   `json:"duration"`
	LessonContentType           LessonContentType       `json:"lesson_content_type"`
	Quizzes                     *QuizLearningProgresses `json:"quizzes" gorm:"type:jsonb"`
	LearningProgressID          string                  `json:"learning_progress_id"`
}

type LearningStatusQuery struct {
	OrgID                   *string  `json:"org_id,omitempty" form:"org_id"`
	UserID                  *string  `json:"user_id,omitempty" form:"user_id"`
	UserIDIn                []string `json:"user_id_in,omitempty" form:"user_id_in"`
	CourseCuidIn            []string `json:"course_cuid_in,omitempty" form:"course_cuid_in"`
	CourseCuid              *string  `json:"course_cuid,omitempty" form:"course_cuid"`
	ExcludeDeletedCourses   *bool    `json:"exclude_deleted_courses" form:"exclude_deleted_courses"`
	IsCompleted             *bool    `json:"is_completed" form:"is_completed"`
	ExcludeUnPublishCourses *bool    `json:"exclude_unpublish_courses" form:"exclude_unpublish_courses"`
	IncludeDeleted          *bool    `json:"is_deleted,omitempty" form:"is_deleted"`
	UpdateAtLte             *int64   `form:"update_at_lte" json:"update_at_lte"`
}

func (query *LearningStatusQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	learningStatusTbl := GetTblName(LearningStatusTbl)

	if query.OrgID != nil {
		qb = qb.Where(learningStatusTbl+".org_id = ?", *query.OrgID)
	}

	if query.UserID != nil {
		qb = qb.Where(learningStatusTbl+".user_id = ?", *query.UserID)
	}

	if len(query.UserIDIn) > 0 {
		qb = qb.Where(learningStatusTbl+".user_id IN (?)", query.UserIDIn)
	}

	if len(query.CourseCuidIn) > 0 {
		qb = qb.Where(learningStatusTbl+".course_cuid IN (?)", query.CourseCuidIn)
	}

	if query.CourseCuid != nil {
		qb = qb.Where(learningStatusTbl+".course_cuid = ?", *query.CourseCuid)
	}

	if query.UpdateAtLte != nil {
		qb = qb.Where(learningStatusTbl+".update_at <= ?", *query.UpdateAtLte)
	}

	if query.IncludeDeleted == nil || *query.IncludeDeleted {
		qb = qb.Where(learningStatusTbl + ".delete_at = 0")
	}

	shouldJoinSubQuery := false
	subQuery := db.Table(GetTblName(PublishCourseTbl)).
		Select("course_cuid, mark_as_completed").
		Group("course_cuid, mark_as_completed")

	if query.ExcludeUnPublishCourses != nil && *query.ExcludeUnPublishCourses {
		shouldJoinSubQuery = true
		subQuery = subQuery.Where("pub_date <> 0")
	}

	if query.ExcludeDeletedCourses != nil && *query.ExcludeDeletedCourses {
		shouldJoinSubQuery = true
		subQuery = subQuery.Where("delete_at = 0")
	}

	if query.IsCompleted != nil {
		shouldJoinSubQuery = true
		if *query.IsCompleted {
			qb = qb.Where(fmt.Sprintf("sub.mark_as_completed = true and %s.completed_at > 0", learningStatusTbl))
		} else {
			qb = qb.Where(fmt.Sprintf("(sub.mark_as_completed = FALSE OR (sub.mark_as_completed = TRUE and %s.completed_at = 0))", learningStatusTbl))
		}
	}

	if shouldJoinSubQuery {
		qb = qb.Joins(fmt.Sprintf("INNER JOIN (?) AS sub ON %[1]s.course_cuid = sub.course_cuid", learningStatusTbl), subQuery)
	}

	return qb
}

// Create inserts a LearningStatus to database, transaction is optional
func (r *LearningStatusRepository) Create(t *LearningStatus, trans *gorm.DB) error {
	return create(LearningStatusTbl, t, trans)
}

func (r *LearningStatusRepository) CreateMany(ts []*LearningStatus, trans *gorm.DB) error {
	return createMany(LearningStatusTbl, ts, trans)
}

// Update updates a LearningStatus by ID in database, transaction is optional
func (r *LearningStatusRepository) Update(t *LearningStatus, trans *gorm.DB) error {
	return update(LearningStatusTbl, t, trans)
}

// FindByID finds a LearningStatus by ID with given find options, transaction is optional
func (r *LearningStatusRepository) FindByID(id string, options *FindOneOptions) (*LearningStatus, error) {
	return findByID[LearningStatus](LearningStatusTbl, id, options)
}

// FindOne finds one LearningStatus with given find queries and options, transaction is optional
func (r *LearningStatusRepository) FindOne(query *LearningStatusQuery, options *FindOneOptions) (*LearningStatus, error) {
	return findOne[LearningStatus](LearningStatusTbl, query, options)
}

// FindMany finds LearningStatuses by query conditions with give find options
func (r *LearningStatusRepository) FindMany(query *LearningStatusQuery, options *FindManyOptions) ([]*LearningStatus, error) {
	return findMany[LearningStatus](LearningStatusTbl, query, options)
}

// FindPage returns LearningStatuses and pagination by query conditions and find options, transaction is optional
func (r *LearningStatusRepository) FindPage(query *LearningStatusQuery, options *FindPageOptions) ([]*LearningStatus, *Pagination, error) {
	return findPage[LearningStatus](LearningStatusTbl, query, options)
}

// Delete performs soft deletion to a LearningStatuses by ID, transaction is optional
func (r *LearningStatusRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[LearningStatus](LearningStatusTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *LearningStatusRepository) DeleteMany(query *LearningStatusQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[LearningStatus](LearningStatusTbl, query, trans)
}

// Count returns number of LearningStatuses by query conditions, transaction is optional
func (r *LearningStatusRepository) Count(query *LearningStatusQuery) (int64, error) {
	return count[LearningStatus](LearningStatusTbl, query)
}

type LatestLearningStatus struct {
	UserID            string `json:"user_id"`
	CurrentSectionUID string `json:"current_section_uid"`
	CurrentLessonUID  string `json:"current_lesson_uid"`
}

func (r *LearningStatusRepository) FindCurrentSectionsAndLessonsByUsers(courseID, courseCuid string, userIDs []string) (map[string][]*Section, error) {
	if len(userIDs) == 0 {
		return make(map[string][]*Section), nil
	}

	var results []LatestLearningStatus

	query := fmt.Sprintf(`
        SELECT ls.user_id, ls.current_section_uid, ls.current_lesson_uid
       	FROM %[1]s ls
        WHERE ls.course_cuid = ? AND ls.user_id IN (?)`, GetTblName(LearningStatusTbl))

	err := DB.Debug().Raw(query, courseCuid, userIDs).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch latest learing status: %w", err)
	}

	var listSectionIDs []string
	for _, lp := range results {
		listSectionIDs = append(listSectionIDs, lp.CurrentLessonUID, lp.CurrentSectionUID)
	}

	if len(listSectionIDs) == 0 {
		return make(map[string][]*Section), nil
	}

	sections, findErr := Repository.Section.FindMany(&SectionQuery{UIDIn: listSectionIDs, CourseID: util.NewString(courseID)}, &FindManyOptions{})
	if findErr != nil {
		return nil, findErr
	}
	mapSectionBySectionID := make(map[string]*Section)
	for _, section := range sections {
		mapSectionBySectionID[section.UID] = section
	}

	mapListSectionByUserID := make(map[string][]*Section)
	for _, res := range results {
		section, ok := mapSectionBySectionID[res.CurrentSectionUID]
		if ok {
			mapListSectionByUserID[res.UserID] = append(mapListSectionByUserID[res.UserID], section)
		}
		lesson, ok := mapSectionBySectionID[res.CurrentLessonUID]
		if ok {
			mapListSectionByUserID[res.UserID] = append(mapListSectionByUserID[res.UserID], lesson)
		}
	}

	return mapListSectionByUserID, nil
}

func (r *LearningStatusRepository) CountCompletedLessonsByUsers(courseIDs []string, userIDs []string) ([]*CompletedLessonCountByUser, error) {
	var completeLessonByUser []*CompletedLessonCountByUser
	if len(userIDs) == 0 {
		return completeLessonByUser, nil
	}

	sectionTbl := GetTblName(SectionTbl)
	courseTbl := GetTblName(CourseTbl)
	learningStatusTbl := GetTblName(LearningStatusTbl)
	query := fmt.Sprintf(`
		WITH active_sections AS (
			SELECT id, uid
			FROM %[1]s section
			WHERE course_id IN @courseIDs AND parent_id = '' AND status = @publishStatus
		), active_lessons AS (
			SELECT id, uid
			FROM %[1]s section
			WHERE parent_id IN (SELECT id FROM active_sections)
			  AND status = @publishStatus
		), courses AS (
			SELECT c.id, c.cuid, c.active_lesson
			FROM %[2]s c
			WHERE c.id IN @courseIDs
		)
		SELECT ls.course_cuid, ls.user_id, (
			   SELECT COUNT(*)
			   FROM jsonb_each(ls.sections) AS section,
					jsonb_each(section.value -> 'lessons') AS lesson
			   WHERE (lesson.value ->> 'completed_at')::bigint > 0
					AND (lesson.value ->> 'lesson_uid') IN (SELECT uid FROM active_lessons)
		   ) AS completed_lessons, courses.active_lesson AS total_lessons
		FROM courses
		LEFT JOIN %[3]s ls ON courses.cuid = ls.course_cuid
		WHERE ls.user_id IN @userIDs`,
		sectionTbl,
		courseTbl,
		learningStatusTbl,
	)

	result := DB.Raw(query, map[string]interface{}{
		"courseIDs":     courseIDs,
		"userIDs":       userIDs,
		"publishStatus": SectionStatusPublish,
	}).Debug().Scan(&completeLessonByUser)
	if err := result.Error; err != nil {
		return nil, err
	}

	return completeLessonByUser, nil
}

func (r *LearningStatusRepository) FindCurrentSectionsAndLessonsByUser(courseIDs []string, user *User) ([]*Section, error) {
	var currentSections []*Section

	sectionTbl := GetTblName(SectionTbl)
	learningStatusTbl := GetTblName(LearningStatusTbl)

	query := fmt.Sprintf(
		`
        SELECT *
		FROM %[1]s s
				 INNER JOIN %[2]s ls ON ls.latest_lesson_uid = s.uid OR ls.latest_section_uid = s.uid
		WHERE ls.user_id = ? AND s.course_id IN (?)
    `,
		sectionTbl,
		learningStatusTbl,
	)

	result := DB.Raw(query, user.ID, courseIDs).Scan(&currentSections)
	if err := result.Error; err != nil {
		return nil, err
	}

	seenCourseIDs := make(map[string]struct{})
	for _, section := range currentSections {
		seenCourseIDs[section.CourseID] = struct{}{}
	}

	var missingCourseIDs []string
	for _, courseID := range courseIDs {
		if _, found := seenCourseIDs[courseID]; !found {
			missingCourseIDs = append(missingCourseIDs, courseID)
		}
	}

	if len(missingCourseIDs) > 0 {
		var firstCurrentSections []*Section
		query = fmt.Sprintf(
			`
			SELECT s.*
			FROM %[1]s s
			WHERE s.course_id IN (?)
			  AND s.parent_id = ''
			  AND s."order" = (
				SELECT MIN("order")
				FROM %[1]s
				WHERE course_id = s.course_id AND parent_id = ''
				GROUP BY course_id
			)
		`, sectionTbl)

		result = DB.Raw(query, missingCourseIDs).Scan(&firstCurrentSections)
		if err := result.Error; err != nil {
			return nil, err
		}

		var firstCurrentLessons []*Section
		sectionIDs := lo.Map(firstCurrentSections, func(section *Section, _ int) string {
			return section.ID
		})
		query = fmt.Sprintf(
			`
			SELECT s.*
			FROM %[1]s s
			WHERE s.course_id IN (?)
			  AND s.parent_id IN (?)
			  AND s."order" = (
				SELECT MIN("order")
				FROM %[1]s
				WHERE course_id = s.course_id AND parent_id IN (?)
				GROUP BY course_id
			)
		`, sectionTbl)

		result = DB.Raw(query, missingCourseIDs, sectionIDs, sectionIDs).Scan(&firstCurrentLessons)
		if err := result.Error; err != nil {
			return nil, err
		}

		currentSections = append(currentSections, firstCurrentSections...)
		currentSections = append(currentSections, firstCurrentLessons...)
	}

	return currentSections, nil
}

func (r *LearningStatusRepository) FindLatestLearningStatusManyUser(query *LearningStatusQuery, options *FindManyOptions) (entities []*LearningStatus, err error) {
	entitiesChan := make(chan []*LearningStatus)
	errorChan := make(chan error)

	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		subQuery := DB.Table(GetTblName(BlogTbl)).Debug().
			Select("DISTINCT ON (user_id) *").
			Order("user_id, create_at DESC")

		qb := ApplyQueryOptions(DB, query, options)
		result := qb.Table("(?) as sub", subQuery).Debug().
			Select("*").
			Find(&entities)

		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	return
}
