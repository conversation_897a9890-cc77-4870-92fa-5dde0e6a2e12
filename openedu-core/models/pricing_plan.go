package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type PlanTier string
type PlanCycle string
type AiFeeType string
type ResourceType string

const (
	FreePlanTier       PlanTier = "free"
	StarterPlanTier    PlanTier = "starter"
	BusinessPlanTier   PlanTier = "business"
	EnterprisePlanTier PlanTier = "enterprise"
	CustomPlanTier     PlanTier = "custom"
	InternalPlanTier   PlanTier = "internal"

	PlanCycleDaily   PlanCycle = "daily"
	PlanCycleWeekly  PlanCycle = "weekly"
	PlanCycleMonthly PlanCycle = "monthly"
	PlanCycleAnnual  PlanCycle = "annual"
	PlanCycleOverall PlanCycle = "overall"

	AiFeeTypeToken   AiFeeType = "token"
	AiFeeTypeRequest AiFeeType = "request"
	AiFeeTypeBalance AiFeeType = "balance"

	ResourceTypeAi ResourceType = "ai"
)

type PricingPlan struct {
	Model
	Tier         PlanTier   `json:"tier"`
	Name         string     `json:"name" validate:"required" gorm:"unique"`
	Description  string     `json:"description"`
	MonthlyPrice PlanPrice  `json:"monthly_price" gorm:"type:jsonb"`
	AnnualPrice  PlanPrice  `json:"annual_price" gorm:"type:jsonb"`
	Enable       bool       `json:"enable" gorm:"default:false"`
	Order        int        `json:"order" gorm:"default:0"`
	Period       int        `json:"period" gorm:"default:0"`
	PeriodUnit   TimePeriod `json:"period_unit" gorm:"type:varchar(50)"`
	Cycle        PlanCycle  `json:"cycle"`
	EmailEvent   string     `json:"email_event"`
	EnableEmail  bool       `json:"enable_email"`

	AiLimitation AiLimitation `json:"ai_limitation" gorm:"type:jsonb"`

	UserID string `json:"user_id"`
	User   *User  `json:"user"`
}

type SimplePricingPlan struct {
	ID           string     `json:"id"`
	Tier         PlanTier   `json:"tier"`
	Name         string     `json:"name"`
	Description  string     `json:"description"`
	MonthlyPrice PlanPrice  `json:"monthly_price" gorm:"type:jsonb"`
	AnnualPrice  PlanPrice  `json:"annual_price" gorm:"type:jsonb"`
	Enable       bool       `json:"enable" gorm:"default:false"`
	Order        int        `json:"order" gorm:"default:0"`
	Period       int        `json:"period"`
	PeriodUnit   TimePeriod `json:"period_unit"`
	Cycle        PlanCycle  `json:"cycle"`
	EmailEvent   string     `json:"email_event"`
	EnableEmail  bool       `json:"enable_email"`
}

func (plan *PricingPlan) Santinize() *SimplePricingPlan {
	return &SimplePricingPlan{
		ID:           plan.ID,
		Tier:         plan.Tier,
		Name:         plan.Name,
		Description:  plan.Description,
		MonthlyPrice: plan.MonthlyPrice,
		AnnualPrice:  plan.AnnualPrice,
		Enable:       plan.Enable,
		Order:        plan.Order,
		Period:       plan.Period,
		Cycle:        plan.Cycle,
		EmailEvent:   plan.EmailEvent,
	}
}

type PlanPrice struct {
	Fiat       decimal.Decimal `json:"month_fiat"`
	FiatUnit   Currency        `json:"month_fiat_unit"`
	Crypto     decimal.Decimal `json:"crypto"`
	CryptoUnit Currency        `json:"crypto_unit"`
}

func (j PlanPrice) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *PlanPrice) Scan(value interface{}) error {
	source, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &j)
	if err != nil {
		return err
	}
	return nil
}

// TODO: Free plan here
type AiLimitation struct {
	Cycle     PlanCycle       `json:"cycle"`
	Type      AiFeeType       `json:"type"`
	Analytics bool            `json:"analytics"` // show usage report
	Models    []*AILimitModel `json:"models"`
}

type AILimitModel struct {
	AiModelID     string          `json:"ai_model_id"`
	ModelName     string          `json:"model_name"`
	RequestAmount int64           `json:"request_amount"`
	BalanceAmount decimal.Decimal `json:"balance_amount"`
}

func (j AiLimitation) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *AiLimitation) Scan(value interface{}) error {
	source, ok := value.([]byte)
	if !ok {
		return errors.New("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, &j)
	if err != nil {
		return err
	}
	return nil
}

func (plan *PricingPlan) IsFreePlan() bool {
	return plan.Tier == FreePlanTier
}

type PricingPlanQuery struct {
	ID      *string   `json:"id" form:"id"`
	IDNot   *string   `json:"id_not" form:"id_not"`
	Name    *string   `json:"name" form:"name"`
	Enable  *bool     `json:"enable" form:"enable"`
	Deleted *bool     `json:"deleted" form:"deleted"`
	Tier    *PlanTier `json:"tier"`
}

func (query *PricingPlanQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.Deleted == nil || !*query.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.Tier != nil {
		qb = qb.Where("tier = ?", *query.Tier)
	}

	if query.IDNot != nil {
		qb = qb.Where("id <> ?", *query.IDNot)
	}

	return qb
}

// Create inserts a PricingPlan to database, transaction is optional
func (r *PricingPlanRepository) Create(q *PricingPlan, trans *gorm.DB) error {
	return create(PricingPlanTbl, q, trans)
}

func (r *PricingPlanRepository) CreateMany(qs []*PricingPlan, trans *gorm.DB) error {
	return createMany(PricingPlanTbl, qs, trans)
}

// Update updates a PricingPlan by ID in database, transaction is optional
func (r *PricingPlanRepository) Update(q *PricingPlan, trans *gorm.DB) error {
	return update(PricingPlanTbl, q, trans)
}

// FindByID finds a PricingPlan by ID with given find options, transaction is optional
func (r *PricingPlanRepository) FindByID(id string, options *FindOneOptions) (*PricingPlan, error) {
	return findByID[PricingPlan](PricingPlanTbl, id, options)
}

// FindOne finds one PricingPlan with given find queries and options, transaction is optional
func (r *PricingPlanRepository) FindOne(query *PricingPlanQuery, options *FindOneOptions) (*PricingPlan, error) {
	return findOne[PricingPlan](PricingPlanTbl, query, options)
}

// FindMany finds PricingPlans by query conditions with give find options
func (r *PricingPlanRepository) FindMany(query *PricingPlanQuery, options *FindManyOptions) ([]*PricingPlan, error) {
	return findMany[PricingPlan](PricingPlanTbl, query, options)
}

// FindPage returns PricingPlans and pagination by query conditions and find options, transaction is optional
func (r *PricingPlanRepository) FindPage(query *PricingPlanQuery, options *FindPageOptions) ([]*PricingPlan, *Pagination, error) {
	return findPage[PricingPlan](PricingPlanTbl, query, options)
}

// Delete perform soft deletion to a FormRelation by ID, transaction is optional
func (r *PricingPlanRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[PricingPlan](PricingPlanTbl, id, trans)
}
