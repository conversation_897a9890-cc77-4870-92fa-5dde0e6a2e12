package models

import "gorm.io/gorm"

type Reminder struct {
	Model
	UserID     string `json:"user_id" gorm:"type:varchar(20)"`
	EntityType string `json:"entity_type"`
	EntityID   string `json:"entity_id" gorm:"type:varchar(20)"`
	RemindeAt  int    `json:"remind_at"`
	DaysBefore int    `json:"days_before"`
	Expired    bool   `json:"expired" gorm:"default:false"`
}

type ReminderQuery struct {
    ID           *string `json:"id" form:"id"`
    IDIn         []*string `json:"id_in" form:"id_in"`
    UserID       *string `json:"user_id" form:"user_id"`
    EntityType   *string `json:"entity_type" form:"entity_type"`
    EntityID     *string `json:"entity_id" form:"entity_id"`
    RemindeAt    *int    `json:"remind_at" form:"remind_at"`
    DaysBefore   *int    `json:"days_before" form:"days_before"`
    Expired      *bool   `json:"expired" form:"expired"`
    IncludeDeleted *bool `json:"include_deleted"`
}

func (query *ReminderQuery) Apply(db *gorm.DB) *gorm.DB {
    qb := db

    if query.ID != nil {
        qb = qb.Where("id = ?", *query.ID)
    }

    if len(query.IDIn) > 0 {
        qb = qb.Where("id IN (?)", query.IDIn)
    }

    if query.UserID != nil {
        qb = qb.Where("user_id = ?", *query.UserID)
    }

    if query.EntityType != nil {
        qb = qb.Where("entity_type = ?", *query.EntityType)
    }

    if query.EntityID != nil {
        qb = qb.Where("entity_id = ?", *query.EntityID)
    }

    if query.RemindeAt != nil {
        qb = qb.Where("remind_at = ?", *query.RemindeAt)
    }

    if query.DaysBefore != nil {
        qb = qb.Where("days_before = ?", *query.DaysBefore)
    }

    if query.Expired != nil {
        qb = qb.Where("expired = ?", *query.Expired)
    }

    if query.IncludeDeleted == nil || !*query.IncludeDeleted {
        qb = qb.Where("delete_at = 0")
    }

    return qb
}

func (r *ReminderRepository) Create(e *Reminder, trans *gorm.DB) error {
    return create(ReminderTbl, e, trans)
}

func (r *ReminderRepository) CreateMany(rs []*Reminder, trans *gorm.DB) error {
    return createMany(ReminderTbl, rs, trans)
}

func (r *ReminderRepository) Update(f *Reminder, trans *gorm.DB) error {
    return update(ReminderTbl, f, trans)
}

func (r *ReminderRepository) FindOne(query *ReminderQuery, options *FindOneOptions) (*Reminder, error) {
    return findOne[Reminder](ReminderTbl, query, options)
}

func (r *ReminderRepository) FindPage(query *ReminderQuery, options *FindPageOptions) ([]*Reminder, *Pagination, error) {
    return findPage[Reminder](ReminderTbl, query, options)
}

func (r *ReminderRepository) FindMany(query *ReminderQuery, options *FindManyOptions) ([]*Reminder, error) {
    return findMany[Reminder](ReminderTbl, query, options)
}

func (r *ReminderRepository) Count(query *ReminderQuery) (int64, error) {
    return count[Reminder](ReminderTbl, query)
}

func (r *ReminderRepository) Delete(id string, trans *gorm.DB) error {
    return deleteByID[Reminder](ReminderTbl, id, trans)
}
