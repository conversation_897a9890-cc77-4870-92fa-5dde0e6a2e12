package models

import (
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"openedu-core/pkg/util"
)

type LocalLevel int

const (
	LocalLevelOther    LocalLevel = -1
	LocalLevelCountry  LocalLevel = 0
	LocalLevelProvince LocalLevel = 1
	LocalLevelUnit     LocalLevel = 2

	DefaultBatchSize = 500
)

type OEReferralReport struct {
	Model
	OrgID                      string     `json:"org_id" gorm:"type:varchar(20);not null"`
	CampaignKey                string     `json:"campaign_key"`
	LocalLevel                 LocalLevel `gorm:"column:local_level"` //
	Province                   string     `json:"province"`
	LocalUnit                  string     `json:"local_unit"`
	UserID                     string     `json:"user_id"`
	UserProps                  JSONB      `json:"user_props" gorm:"type:jsonb"` // FORM info
	CourseCuid                 string     `json:"course_cuid"`
	RefBy                      string     `json:"ref_by" gorm:"default:''"`
	FillFormDate               int64      `json:"fill_form_date" gorm:"default:0"`
	RegisterDate               int64      `json:"register_date" gorm:"default:0"`
	EnrollDate                 int64      `json:"enroll_date" gorm:"default:0"`
	CompleteDate               int64      `json:"complete_date" gorm:"default:0"`
	ClaimCertDate              int64      `json:"claim_cert_date" gorm:"default:0"`
	CanClaimCert               bool       `json:"can_claim_cert"`
	NumberOfCompletedSection   int        `json:"number_of_completed_section" gorm:"default:0"`
	FirstCheckCanClaimCertDate int64      `json:"first_check_can_claim_cert_date"`
	LastCheckCanClaimCertDate  int64      `json:"last_check_can_claim_cert_date"`
}

type OEReferralReportQuery struct {
	ID                                  *string     `json:"id" form:"id"`
	OrgID                               *string     `json:"org_id" form:"org_id"`
	CampaignKey                         *string     `json:"campaign_key" form:"campaign_key"`
	LocalLevel                          *LocalLevel `json:"local_level" form:"local_level"`
	Province                            *string     `json:"province" form:"province"`
	LocalUnit                           *string     `json:"local_unit" form:"local_unit"`
	UserID                              *string     `json:"user_id" form:"user_id"`
	UserIDIn                            []string    `json:"user_id_in" form:"user_id_in"`
	CourseCUID                          *string     `json:"course_cuid" form:"course_cuid"`
	RefBy                               *string     `json:"ref_by" form:"ref_by"`
	IsRef                               *bool       `json:"is_ref" form:"is_ref"`
	Deleted                             *bool       `json:"deleted" form:"deleted"`
	RegLT                               *int64      `json:"reg_lt" form:"reg_lt"`
	EnrollLT                            *int64      `json:"enroll_lt" form:"enroll_lt"`
	EnrollNe                            *int64      `json:"enroll_gt" form:"enroll_gt"`
	CompleteLT                          *int64      `json:"complete_lt" form:"complete_lt"`
	ClaimCertLT                         *int64      `json:"claim_cert_lt" form:"claim_cert_lt"`
	CanClaimCert                        *bool       `json:"can_claim_cert" form:"can_claim_cert"`
	LastCheckCanClaimCertDateLte        *int64      `json:"last_check_can_claim_cert_date_lte" form:"last_check_can_claim_cert_date_lte"`
	FirstCheckCanClaimCertDateGteOrZero *int64      `json:"first_check_can_claim_cert_date_gte_or_zero" form:"first_check_can_claim_cert_date_gte_zero"`
}

type OERefProvinceStats struct {
	Province        string `json:"province"`
	LearnerCount    int64  `json:"learner_count"`
	CompletionCount int64  `json:"completion_count"`
	CertCount       int64  `json:"cert_count"`
	EnrollCount     int64  `json:"enroll_count"`
}

type OERefSectionStats struct {
	SectionUID     string `json:"section_uid"`
	CompletedCount int64  `json:"completed_count"`
}

type OERefSectionByProvinceStats struct {
	Province       string `json:"province"`
	SectionUID     string `json:"section_uid"`
	CompletedCount int64  `json:"completed_count"`
}

type OERefGeneralStats struct {
	TotalRegisteredUsers int64   `json:"total_registered_users"` // Total users who registered an account
	TotalEnrolledUsers   int64   `json:"total_enrolled_users"`   // Total users enrolled in courses based on filters
	TotalCompletedUsers  int64   `json:"total_completed_users"`  // Total users who completed courses and received certificates
	CompletionRate       float64 `json:"completion_rate"`        // Completion rate percentage
}

func (q OEReferralReportQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if q.ID != nil {
		qb = qb.Where("org_id = ?", *q.ID)
	}

	if q.OrgID != nil {
		qb = qb.Where("org_id=?", *q.OrgID)
	}

	if q.CampaignKey != nil {
		qb = qb.Where("campaign_key=?", *q.CampaignKey)
	}

	if q.LocalLevel != nil {
		qb = qb.Where("local_level=?", *q.LocalLevel)
	}

	if q.Province != nil {
		qb = qb.Where("province=?", *q.Province)
	}

	if q.LocalUnit != nil {
		qb = qb.Where("local_unit=?", *q.LocalUnit)
	}

	if q.UserID != nil {
		qb = qb.Where("user_id=?", *q.UserID)
	}

	if len(q.UserIDIn) > 0 {
		qb = qb.Where("user_id IN (?)", q.UserIDIn)
	}

	if q.IsRef != nil && *q.IsRef {
		qb = qb.Where("ref_by <> ''")
	}

	if q.CourseCUID != nil {
		qb = qb.Where("course_cuid=?", *q.CourseCUID)
	}

	if q.Deleted == nil || !*q.Deleted {
		qb = qb.Where("delete_at = 0")
	}

	if q.RegLT != nil {
		qb = qb.Where("register_date > ?", *q.RegLT)
	}

	if q.EnrollLT != nil {
		qb = qb.Where("enroll_date > ?", *q.EnrollLT)
	}

	if q.EnrollNe != nil {
		qb = qb.Where("enroll_date <> ?", *q.EnrollNe)
	}

	if q.CompleteLT != nil {
		qb = qb.Where("complete_date > ?", *q.CompleteLT)
	}

	if q.ClaimCertLT != nil {
		qb = qb.Where("claim_cert_date > ?", *q.ClaimCertLT)
	}

	if q.CanClaimCert != nil {
		qb = qb.Where("can_claim_cert = ?", *q.CanClaimCert)
	}

	if q.LastCheckCanClaimCertDateLte != nil {
		qb = qb.Where("last_check_can_claim_cert_date <= ?", *q.LastCheckCanClaimCertDateLte)
	}

	if q.FirstCheckCanClaimCertDateGteOrZero != nil {
		qb = qb.Where("first_check_can_claim_cert_date >= ? OR first_check_can_claim_cert_date = 0", *q.FirstCheckCanClaimCertDateGteOrZero)
	}

	return qb
}

func (r *OEReferralReportRepository) Create(entity *OEReferralReport, trans *gorm.DB) error {
	return create(OEReferralReportTbl, entity, trans)
}

func (r *OEReferralReportRepository) CreateMany(entities []*OEReferralReport, trans *gorm.DB) error {
	return createMany(OEReferralReportTbl, entities, trans)
}

func (r *OEReferralReportRepository) Update(entity *OEReferralReport, trans *gorm.DB) error {
	return update(OEReferralReportTbl, entity, trans)
}

func (r *OEReferralReportRepository) FindByID(id string, options *FindOneOptions) (*OEReferralReport, error) {
	return findByID[OEReferralReport](OEReferralReportTbl, id, options)
}

func (r *OEReferralReportRepository) FindOne(query *OEReferralReportQuery, options *FindOneOptions) (*OEReferralReport, error) {
	return findOne[OEReferralReport](OEReferralReportTbl, query, options)
}

func (r *OEReferralReportRepository) FindMany(query *OEReferralReportQuery, options *FindManyOptions) ([]*OEReferralReport, error) {
	return findMany[OEReferralReport](OEReferralReportTbl, query, options)
}

func (r *OEReferralReportRepository) FindPage(query *OEReferralReportQuery, options *FindPageOptions) ([]*OEReferralReport, *Pagination, error) {
	return findPage[OEReferralReport](OEReferralReportTbl, query, options)
}

func (r *OEReferralReportRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[OEReferralReport](OEReferralReportTbl, id, trans)
}

func (r *OEReferralReportRepository) DeleteMany(query *OEReferralReportQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[OEReferralReport](OEReferralReportTbl, query, trans)
}

// Count returns number of UserActions by query conditions, transaction is optional
func (r *OEReferralReportRepository) Count(query *OEReferralReportQuery) (int64, error) {
	return count[OEReferralReport](OEReferralReportTbl, query)
}

func (r *OEReferralReportRepository) GenReportsFromRegisterEvent(
	campaignKey string,
	orgID string,
	from int,
	to int,
	courseCuids []string,
	batchSize int,
) error {
	referralReportTbl := GetTblName(OEReferralReportTbl)
	userRoleOrgTbl := GetTblName(UserRoleOrgTbl)
	publishCourseTbl := GetTblName(PublishCourseTbl)

	var totalCount int64
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM %s r
			LEFT JOIN (
				SELECT course_cuid, org_id
				FROM %s
				WHERE delete_at = 0 AND enable = true AND org_id = @orgID
				GROUP BY course_cuid, org_id
			) pc ON r.org_id = pc.org_id
		WHERE r.create_at BETWEEN @startTime AND @endTime 
			AND r.org_id = @orgID 
			AND r.role_id = @roleID %s
	`, userRoleOrgTbl, publishCourseTbl, lo.If(len(courseCuids) == 0, "").Else("AND pc.course_cuid IN @courseCuids"))
	if err := DB.Debug().Raw(countSQL, map[string]interface{}{
		"startTime":   from,
		"endTime":     to,
		"orgID":       orgID,
		"roleID":      LearnerRoleType,
		"courseCuids": lo.If(courseCuids == nil, make([]string, 0)).Else(courseCuids),
	}).Count(&totalCount).Error; err != nil {
		return err
	}

	if batchSize <= 0 {
		batchSize = DefaultBatchSize
	}
	for offset := 0; offset < int(totalCount); offset += batchSize {
		rawSQL := fmt.Sprintf(`
		INSERT INTO %s (
			id, create_at, update_at, delete_at, org_id, campaign_key, local_level,
			province, local_unit, user_id, user_props, course_cuid, fill_form_date,
			register_date, enroll_date, complete_date, claim_cert_date,
			number_of_completed_section
		)
		SELECT generate_id(), @createAt, @updateAt, @deleteAt, r.org_id, @campaignKey, @localLevel,
			@province, @localUnit, r.user_id, @userProps, pc.course_cuid, @fillFormDate, r.create_at,
			@enrollDate, @completeDate, @claimCertDate, @numOfCompletedSection
		FROM %s r
			LEFT JOIN (
				SELECT course_cuid, org_id
				FROM %s
				WHERE delete_at = 0 AND enable = true AND org_id = @orgID
				GROUP BY course_cuid, org_id
			) pc ON r.org_id = pc.org_id
		WHERE r.create_at BETWEEN @startTime AND @endTime AND r.org_id = @orgID AND r.role_id = @roleID %s
		ORDER BY r.user_id ASC, pc.course_cuid ASC
		LIMIT @limit OFFSET @offset
		ON CONFLICT (user_id, org_id, campaign_key, course_cuid) DO UPDATE 
		SET register_date = EXCLUDED.register_date
	`, referralReportTbl, userRoleOrgTbl, publishCourseTbl, lo.If(len(courseCuids) == 0, "").Else("AND pc.course_cuid IN @courseCuids"))

		now := util.GetCurrentTime()
		result := DB.Debug().Exec(rawSQL, map[string]interface{}{
			"campaignKey":           campaignKey,
			"localLevel":            LocalLevelProvince,
			"startTime":             from,
			"endTime":               to,
			"province":              "",
			"localUnit":             "",
			"userProps":             JSONB{},
			"orgID":                 orgID,
			"roleID":                LearnerRoleType,
			"fillFormDate":          0,
			"claimCertDate":         0,
			"enrollDate":            0,
			"completeDate":          0,
			"createAt":              now,
			"updateAt":              now,
			"deleteAt":              0,
			"numOfCompletedSection": 0,
			"courseCuids":           lo.If(courseCuids == nil, make([]string, 0)).Else(courseCuids),
			"offset":                offset,
			"limit":                 batchSize,
		})
		if err := result.Error; err != nil {
			return err
		}
	}
	return nil
}

func (r *OEReferralReportRepository) GenReportsFromLearningStatus(
	campaignKey string,
	orgID string,
	from int,
	to int,
	courseCuids []string,
	batchSize int,
) error {
	referralReportTbl := GetTblName(OEReferralReportTbl)
	learningStatusTbl := GetTblName(LearningStatusTbl)

	var totalCount int64
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM %s ls
		WHERE ls.update_at BETWEEN @startTime AND @endTime AND ls.org_id = @orgID %s
	`, learningStatusTbl, lo.If(len(courseCuids) == 0, "").Else("AND ls.course_cuid IN @courseCuids"))
	if err := DB.Debug().Raw(countSQL, map[string]interface{}{
		"startTime":   from,
		"endTime":     to,
		"orgID":       orgID,
		"courseCuids": courseCuids,
	}).Count(&totalCount).Error; err != nil {
		return err
	}

	if batchSize <= 0 {
		batchSize = DefaultBatchSize
	}
	for offset := 0; offset < int(totalCount); offset += batchSize {
		rawSQL := fmt.Sprintf(`
		INSERT INTO %[1]s (
			id, create_at, update_at, delete_at, org_id, campaign_key, local_level,
			province, local_unit, user_id, user_props, course_cuid, fill_form_date,
			register_date, enroll_date, complete_date, claim_cert_date,
			number_of_completed_section
		)
		SELECT generate_id(), @createAt, @updateAt, @deleteAt, ls.org_id, @campaignKey, @localLevel,
			   @province, @localUnit, ls.user_id, @userProps, ls.course_cuid, @fillFormDate, @registerDate,
               @enrollDate, ls.completed_at, @claimCertDate, (
				   SELECT COUNT(*)
				   FROM jsonb_each(ls.sections) AS section
				   WHERE (section.value ->> 'completed_at')::bigint > 0
			   )
		FROM %[2]s ls
		WHERE ls.update_at BETWEEN @startTime AND @endTime AND ls.org_id = @orgID %[3]s
		ORDER BY ls.update_at ASC, ls.user_id ASC
		LIMIT @limit OFFSET @offset
		ON CONFLICT (user_id, org_id, campaign_key, course_cuid) DO UPDATE 
		SET
				complete_date = EXCLUDED.complete_date,
				number_of_completed_section = EXCLUDED.number_of_completed_section
	`, referralReportTbl, learningStatusTbl, lo.If(len(courseCuids) == 0, "").Else("AND ls.course_cuid IN @courseCuids"))

		now := util.GetCurrentTime()
		result := DB.Debug().Exec(rawSQL, map[string]interface{}{
			"campaignKey":   campaignKey,
			"localLevel":    LocalLevelProvince,
			"startTime":     from,
			"endTime":       to,
			"orgID":         orgID,
			"province":      "",
			"localUnit":     "",
			"userProps":     JSONB{},
			"fillFormDate":  0,
			"registerDate":  0,
			"enrollDate":    0,
			"claimCertDate": 0,
			"createAt":      now,
			"updateAt":      now,
			"deleteAt":      0,
			"courseCuids":   lo.If(courseCuids == nil, make([]string, 0)).Else(courseCuids),
			"offset":        offset,
			"limit":         batchSize,
		})
		if err := result.Error; err != nil {
			return err
		}
	}
	return nil
}

func (r *OEReferralReportRepository) GenReportsFromEnrollEvent(
	campaignKey string,
	orgID string,
	from int,
	to int,
	courseCuids []string,
	batchSize int,
) error {
	referralReportTbl := GetTblName(OEReferralReportTbl)
	enrollmentTbl := GetTblName(CourseEnrollmentTbl)
	userRoleOrgTbl := GetTblName(UserRoleOrgTbl)

	var totalCount int64
	countSQL := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM %s ce
		WHERE ce.create_at BETWEEN @startTime AND @endTime AND ce.org_id = @orgID AND ce.delete_at = 0 %s
	`, enrollmentTbl, lo.If(len(courseCuids) == 0, "").Else("AND ce.course_cuid IN @courseCuids"))
	if err := DB.Debug().Raw(countSQL, map[string]interface{}{
		"startTime":   from,
		"endTime":     to,
		"orgID":       orgID,
		"courseCuids": lo.If(courseCuids == nil, make([]string, 0)).Else(courseCuids),
	}).Count(&totalCount).Error; err != nil {
		return err
	}

	if batchSize <= 0 {
		batchSize = DefaultBatchSize
	}
	for offset := 0; offset < int(totalCount); offset += batchSize {
		rawSQL := fmt.Sprintf(`
		INSERT INTO %[1]s (
			id, create_at, update_at, delete_at, org_id, campaign_key, local_level,
			province, local_unit, user_id, user_props, course_cuid, fill_form_date,
			register_date, enroll_date, complete_date, claim_cert_date,
			number_of_completed_section
		)
		SELECT generate_id(), @createAt, @updateAt, @deleteAt, ce.org_id, @campaignKey, @localLevel,
			@province, @localUnit, ce.user_id, @userProps, ce.course_cuid, @fillFormDate, uro.create_at, ce.create_at,
			@completeDate, @claimCertDate, @numOfCompletedSection
		FROM %[2]s ce
			LEFT JOIN %[3]s uro ON uro.user_id = ce.user_id
		WHERE ce.create_at BETWEEN @startTime AND @endTime AND ce.org_id = @orgID AND ce.delete_at = 0 %[4]s
			 AND uro.role_id = @roleID AND uro.org_id = @orgID
		ORDER BY ce.user_id ASC, ce.course_cuid ASC
		LIMIT @limit OFFSET @offset
		ON CONFLICT (user_id, org_id, campaign_key, course_cuid) DO UPDATE 
		SET enroll_date = EXCLUDED.enroll_date, register_date = EXCLUDED.register_date
	`, referralReportTbl, enrollmentTbl, userRoleOrgTbl, lo.If(len(courseCuids) == 0, "").Else("AND ce.course_cuid IN @courseCuids"))

		now := util.GetCurrentTime()
		result := DB.Debug().Exec(rawSQL, map[string]interface{}{
			"campaignKey":           campaignKey,
			"localLevel":            LocalLevelProvince,
			"startTime":             from,
			"endTime":               to,
			"orgID":                 orgID,
			"province":              "",
			"localUnit":             "",
			"userProps":             JSONB{},
			"fillFormDate":          0,
			"claimCertDate":         0,
			"completeDate":          0,
			"createAt":              now,
			"updateAt":              now,
			"deleteAt":              0,
			"courseCuids":           lo.If(courseCuids == nil, make([]string, 0)).Else(courseCuids),
			"numOfCompletedSection": 0,
			"offset":                offset,
			"limit":                 batchSize,
			"roleID":                LearnerRoleType,
		})
		if err := result.Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *OEReferralReportRepository) GenReportsFromCertificates(
	campaignKey string,
	orgID string,
	from int,
	to int,
	courseCuids []string,
	batchSize int,
) error {
	referralReportTbl := GetTblName(OEReferralReportTbl)
	certificateTbl := GetTblName(CertificateTbl)
	userRoleOrgTbl := GetTblName(UserRoleOrgTbl)

	var totalCount int64
	countSQL := fmt.Sprintf(`
		WITH recent_certificates AS (
			SELECT user_id, course_cuid, MAX(create_at) AS receive_at
			FROM %s c
			WHERE c.create_at BETWEEN @startTime AND @endTime AND c.org_id = @orgID %s
			GROUP BY user_id, course_cuid
		)
		SELECT COUNT(*) FROM recent_certificates
	`, certificateTbl, lo.If(len(courseCuids) == 0, "").Else("AND c.course_cuid IN @courseCuids"))
	if err := DB.Debug().Raw(countSQL, map[string]interface{}{
		"startTime":   from,
		"endTime":     to,
		"orgID":       orgID,
		"courseCuids": lo.If(courseCuids == nil, make([]string, 0)).Else(courseCuids),
	}).Count(&totalCount).Error; err != nil {
		return err
	}

	if batchSize <= 0 {
		batchSize = DefaultBatchSize
	}
	for offset := 0; offset < int(totalCount); offset += batchSize {
		rawSQL := fmt.Sprintf(`
		WITH recent_certificates AS (
			SELECT c.user_id, c.course_cuid, c.org_id, MAX(create_at) AS create_at
			FROM %[1]s c
			WHERE c.create_at BETWEEN @startTime AND @endTime AND c.org_id = @orgID %[4]s
			GROUP BY c.user_id, c.course_cuid, c.org_id
		)
		INSERT INTO %[2]s (
			id, create_at, update_at, delete_at, org_id, campaign_key, local_level,
			province, local_unit, user_id, user_props, course_cuid, fill_form_date,
			register_date, enroll_date, complete_date, claim_cert_date,
			number_of_completed_section, can_claim_cert
		)
		SELECT generate_id(), @createAt, @updateAt, @deleteAt, c.org_id, @campaignKey, @localLevel,
			   @province, @localUnit, c.user_id, @userProps, c.course_cuid, @fillFormDate, uro.create_at,
               @enrollDate, @completeDate, c.create_at, @numOfCompletedSection, c.create_at > 0
		FROM recent_certificates c
			LEFT JOIN %[3]s uro ON uro.user_id = c.user_id
		WHERE uro.role_id = @roleID AND uro.org_id = @orgID
		ORDER BY c.user_id ASC, c.course_cuid ASC
		LIMIT @limit OFFSET @offset
		ON CONFLICT (user_id, org_id, campaign_key, course_cuid) DO UPDATE 
		SET claim_cert_date = EXCLUDED.claim_cert_date, register_date = EXCLUDED.register_date
	`, certificateTbl, referralReportTbl, userRoleOrgTbl, lo.If(len(courseCuids) == 0, "").Else("AND c.course_cuid IN @courseCuids"))

		now := util.GetCurrentTime()
		result := DB.Debug().Exec(rawSQL, map[string]interface{}{
			"campaignKey":           campaignKey,
			"localLevel":            LocalLevelProvince,
			"startTime":             from,
			"endTime":               to,
			"orgID":                 orgID,
			"province":              "",
			"localUnit":             "",
			"userProps":             JSONB{},
			"fillFormDate":          0,
			"enrollDate":            0,
			"claimCertDate":         0,
			"completeDate":          0,
			"createAt":              now,
			"updateAt":              now,
			"deleteAt":              0,
			"numOfCompletedSection": 0,
			"courseCuids":           lo.If(courseCuids == nil, make([]string, 0)).Else(courseCuids),
			"offset":                offset,
			"limit":                 batchSize,
			"roleID":                LearnerRoleType,
		})
		if err := result.Error; err != nil {
			return err
		}
	}
	return nil
}

func (r *OEReferralReportRepository) FindGeneralStats(
	fromDate int64,
	toDate int64,
	campaignKey string,
	courseCUIDs []string,
) (*OERefGeneralStats, error) {
	referralReportTbl := GetTblName(OEReferralReportTbl)
	query := fmt.Sprintf(
		`
		WITH campaign_reports AS (
			SELECT user_id, course_cuid, register_date, enroll_date, complete_date, claim_cert_date
			FROM %[1]s
			WHERE campaign_key = @campaignKey %[2]s AND (
					register_date BETWEEN @fromDate AND @toDate
					OR enroll_date BETWEEN @fromDate AND @toDate 
					OR (complete_date BETWEEN @fromDate AND @toDate AND claim_cert_date BETWEEN @fromDate AND @toDate)
				)
				
		), registed_users AS (
			SELECT user_id
			FROM campaign_reports
			WHERE register_date BETWEEN @fromDate AND @toDate
			GROUP BY user_id
		), enrolled_users AS (
			SELECT user_id, course_cuid
			FROM campaign_reports
			WHERE enroll_date BETWEEN @fromDate AND @toDate 
			GROUP BY user_id, course_cuid
		), completed_users AS (
			SELECT user_id, course_cuid
			FROM campaign_reports
			WHERE complete_date BETWEEN @fromDate AND @toDate AND claim_cert_date BETWEEN @fromDate AND @toDate 
			GROUP BY user_id, course_cuid
		)
		SELECT
			(SELECT COUNT(*) FROM registed_users) AS total_registered_users,
			(SELECT COUNT(*) FROM enrolled_users) AS total_enrolled_users,
			(SELECT COUNT(*) FROM completed_users) AS total_completed_users;
	`,
		referralReportTbl,
		lo.If(len(courseCUIDs) == 0, "").Else("AND course_cuid IN @courseCuids"),
	)

	var generalStats OERefGeneralStats
	if err := DB.Debug().Raw(query, map[string]interface{}{
		"campaignKey": campaignKey,
		"fromDate":    fromDate,
		"toDate":      toDate,
		"courseCuids": courseCUIDs,
	}).Scan(&generalStats).Error; err != nil {
		return nil, err
	}

	return &generalStats, nil
}

func (r *OEReferralReportRepository) FindSectionStatsByCourse(
	fromDate int64,
	toDate int64,
	campaignKey string,
	courseOutline *Course,
) ([]*OERefSectionStats, error) {

	learningStatusTbl := GetTblName(LearningStatusTbl)

	// Get section completion data
	// AND enroll_date BETWEEN @fromDate AND @toDate
	query := fmt.Sprintf(`
		WITH section_counts AS (
			SELECT
				user_id,
				course_cuid,
				section_key AS section_uid,
				(sections->section_key->>'completed_at')::bigint AS completed_at
			FROM
				%[1]s ls,
				jsonb_object_keys(sections) AS section_key
			WHERE course_cuid = @courseCUID
			ORDER BY
				user_id, section_uid DESC
		)
		SELECT section_uid, COUNT(user_id) AS completed_count
		FROM section_counts 
		WHERE completed_at BETWEEN @fromDate AND @toDate GROUP BY section_uid;
	`, learningStatusTbl)

	args := map[string]interface{}{
		"fromDate":    fromDate,
		"toDate":      toDate,
		"campaignKey": campaignKey,
		"courseCUID":  courseOutline.Cuid,
	}

	var listSectionStats []*OERefSectionStats
	if err := DB.Debug().Raw(query, args).Scan(&listSectionStats).Error; err != nil {
		return nil, err
	}

	return listSectionStats, nil
}

func (r *OEReferralReportRepository) FindSectionStatsByProvinces(
	fromDate, toDate int64,
	campaignKey string,
	provinces []string,
	courseCUIDs []string,
) ([]*OERefSectionByProvinceStats, error) {

	learningStatusTbl := GetTblName(LearningStatusTbl)
	oeReferralReportTbl := GetTblName(OEReferralReportTbl)

	query := fmt.Sprintf(
		`
		WITH section_counts AS (
			SELECT
				user_id,
				course_cuid,
				section_key AS section_uid,
				(sections->section_key->>'completed_at')::bigint AS completed_at
			FROM
				%[1]s ls,
				jsonb_object_keys(sections) AS section_key
			WHERE course_cuid IN @courseCUIDs
		)
		SELECT section_counts.section_uid, rp.province, COUNT(*) AS completed_count
		FROM section_counts
			LEFT JOIN %[2]s rp ON rp.user_id = section_counts.user_id
				AND rp.course_cuid = section_counts.course_cuid
		WHERE section_counts.completed_at BETWEEN @fromDate AND @toDate %[3]s
		GROUP BY section_counts.section_uid, rp.province
	`,
		learningStatusTbl,
		oeReferralReportTbl,
		lo.If(len(provinces) == 0, "").Else("AND rp.province IN @provinces"),
	)

	args := map[string]interface{}{
		"fromDate":    fromDate,
		"toDate":      toDate,
		"campaignKey": campaignKey,
		"courseCUIDs": courseCUIDs,
		"provinces": lo.Map(provinces, func(province string, _ int) string {
			return lo.If(province == util.ProvinceOther, "").Else(province)
		}),
	}

	var listSectionStats []*OERefSectionByProvinceStats
	if err := DB.Debug().Raw(query, args).Scan(&listSectionStats).Error; err != nil {
		return nil, err
	}

	for _, sectionStat := range listSectionStats {
		if sectionStat.Province == "" {
			sectionStat.Province = util.ProvinceOther
		}
	}

	return listSectionStats, nil
}

func (r *OEReferralReportRepository) FindProvinceStats(
	fromDate int64,
	toDate int64,
	campaignKey string,
	provinces []string,
	courseCUIDs []string,
) ([]*OERefProvinceStats, error) {

	referralReportTbl := GetTblName(OEReferralReportTbl)
	query := fmt.Sprintf(
		`
       SELECT
          CASE 
             WHEN province <> '' THEN province
             ELSE @unknownProvince
          END AS province,
          COUNT(*) AS learner_count,
          COUNT(CASE WHEN enroll_date > 0 THEN 1 ELSE NULL END) AS enroll_count,
          COUNT(CASE WHEN complete_date > 0 THEN 1 ELSE NULL END) AS completion_count,
          COUNT(CASE WHEN claim_cert_date > 0 THEN 1 ELSE NULL END) AS cert_count
       FROM %[1]s
       WHERE campaign_key = @campaignKey
          AND enroll_date BETWEEN @fromDate AND @toDate %[2]s %[3]s
       GROUP BY province
       ORDER BY learner_count DESC
    `,
		referralReportTbl,
		lo.If(len(courseCUIDs) == 0, "").Else("AND course_cuid IN @courseCUIDs"),
		lo.If(len(provinces) == 0, "").Else("AND province IN @provinces"),
	)
	var listProvinceStats []*OERefProvinceStats
	if err := DB.Debug().Raw(query, map[string]interface{}{
		"campaignKey": campaignKey,
		"fromDate":    fromDate,
		"toDate":      toDate,
		"courseCUIDs": courseCUIDs,
		"provinces": lo.Map(provinces, func(province string, _ int) string {
			return lo.If(province == util.ProvinceOther, "").Else(province)
		}),
		"unknownProvince": util.ProvinceOther,
	}).Scan(&listProvinceStats).Error; err != nil {
		return nil, err
	}
	return listProvinceStats, nil
}
