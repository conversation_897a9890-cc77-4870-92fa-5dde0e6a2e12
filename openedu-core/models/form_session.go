package models

import (
	"fmt"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type FormSessionStatus string

const (
	FormSessionsStatusReviewing FormSessionStatus = "reviewing"
	FormSessionsStatusApproved  FormSessionStatus = "approved"
	FormSessionsStatusRejected  FormSessionStatus = "rejected"
	FormSessionsStatusOutDated  FormSessionStatus = "outdated"
)

type FormSession struct {
	Model
	UserID         *string           `json:"user_id,omitempty" gorm:"type:varchar(20)"`
	User           *User             `json:"user,omitempty"`
	FormUID        string            `json:"form_uid,omitempty" gorm:"type:varchar(20)"`
	FormID         string            `json:"form_id,omitempty" gorm:"type:varchar(20);not null"`
	FormRelationID *string           `json:"form_relation_id,omitempty" gorm:"type:varchar(20)"`
	Status         FormSessionStatus `json:"status,omitempty" gorm:"type:varchar(100)"`
	Answers        []*FormAnswer     `json:"answers,omitempty" gorm:"foreignKey:SessionID"`
	Note           string            `json:"note,omitempty" gorm:"type:text"`
}

type SimpleFormSession struct {
	Model
	UserID         *string             `json:"user_id"`
	User           *SimpleUser         `json:"user"`
	FormID         string              `json:"form_id"`
	FormUID        string              `json:"form_uid"`
	FormRelationID *string             `json:"form_relation_id" gorm:"type:varchar(20)"`
	Status         FormSessionStatus   `json:"status"`
	Answers        []*SimpleFormAnswer `json:"answers"`
	Note           string              `json:"note"`
}

func (s *FormSession) ToSimple() *SimpleFormSession {
	var simpleUser *SimpleUser
	if s.User != nil {
		simpleUser = s.User.ToSimpleUser()
	}
	return &SimpleFormSession{
		Model:          s.Model,
		UserID:         s.UserID,
		User:           simpleUser,
		FormID:         s.FormID,
		FormUID:        s.FormUID,
		FormRelationID: s.FormRelationID,
		Status:         s.Status,
		Answers: lo.Map(s.Answers, func(answer *FormAnswer, _ int) *SimpleFormAnswer {
			return answer.Sanitize()
		}),
		Note: s.Note,
	}
}

func (s *FormSession) ExtractUserInfo() *UserInfo {
	var userInfo UserInfo
	for _, answer := range s.Answers {
		switch {
		case answer.IsKeyFullName():
			userInfo.DisplayName = answer.Text

		case answer.IsKeyEmail():
			userInfo.Email = answer.Text

		case answer.IsKeyPhone():
			userInfo.Phone = answer.Text
		}
	}
	return &userInfo
}

type FormSessionQuery struct {
	ID               *string  `json:"id,omitempty" form:"id"`
	IDIn             []string `json:"id_in,omitempty" form:"id_in"`
	Status           *string  `json:"status,omitempty" form:"status"`
	StatusIn         []string `json:"status_in,omitempty" form:"status_in"`
	FormID           *string  `json:"form_id,omitempty" form:"form_id"`
	FormIDIn         []string `json:"form_id_in,omitempty" form:"form_id_in"`
	FormUID          *string  `json:"form_uid,omitempty" form:"form_uid"`
	FormUIDIn        []string `json:"form_uid_in,omitempty" form:"form_uid_in"`
	UserID           *string  `json:"user_id,omitempty" form:"user_id"`
	IncludeDeleted   *bool    `json:"include_deleted,omitempty" form:"include_deleted"`
	SearchTerm       *string  `json:"search_term" form:"search_term"`
	SearchCategories *string  `json:"search_categories" form:"search_categories"`
	CreateAtGte      *int     `json:"create_at_gt"`
	CreateAtLte      *int     `json:"create_at_lte"`
}

func (query *FormSessionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN (?)", *&query.StatusIn)
	}

	if query.FormID != nil {
		qb = qb.Where("form_id = ?", *query.FormID)
	}

	if query.FormUID != nil {
		qb = qb.Where("form_uid = ?", *query.FormUID)
	}

	if query.FormUID != nil {
		qb = qb.Where("form_uid = ?", *query.FormUID)
	}

	if len(query.FormUIDIn) > 0 {
		qb = qb.Where("form_uid IN (?)", *&query.FormUIDIn)
	}

	if len(query.FormIDIn) > 0 {
		qb = qb.Where("form_id IN (?)", *&query.FormIDIn)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.SearchCategories, ",")
		subQuery := db.Table(GetTblName(FormAnswerTbl)).
			Select("session_id").
			Where("key IN (?) AND text ~* ?", categories, *query.SearchTerm).
			Group("session_id")

		if query.FormID != nil {
			subQuery = subQuery.Where("form_id = ?", *query.FormID)
		}

		qb = qb.Joins(fmt.Sprintf("INNER JOIN (?) AS sub ON %[1]s.id = sub.session_id", GetTblName(FormSessionTbl)), subQuery)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.CreateAtGte != nil {
		qb = qb.Where("create_at >= ?", *query.CreateAtGte)
	}

	if query.CreateAtLte != nil {
		qb = qb.Where("create_at <= ?", *query.CreateAtLte)
	}

	return qb
}

func (r *FormSessionRepository) Create(f *FormSession, trans *gorm.DB) error {
	return create(FormSessionTbl, f, trans)
}

func (r *FormSessionRepository) CreateMany(o []*FormSession, trans *gorm.DB) error {
	return createMany(FormSessionTbl, o, trans)
}

func (r *FormSessionRepository) Update(o *FormSession, trans *gorm.DB) error {
	return update(FormSessionTbl, o, trans)
}

func (r *FormSessionRepository) FindByID(id string, options *FindOneOptions) (*FormSession, error) {
	if options == nil {
		options = &FindOneOptions{}
	}
	shouldPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersFilesField) {
		shouldPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersFilesField)
	}
	formSession, err := findByID[FormSession](FormSessionTbl, id, options)
	if err != nil {
		return nil, err
	}
	if shouldPreloadFiles {
		answerIDs := lo.Map(formSession.Answers, func(answer *FormAnswer, _ int) string {
			return answer.ID
		})
		if filesByAnswerIDs, mErr := Repository.FileRelation.GetFilesByEntities(FormAnswerModelName, answerIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			lo.ForEach(formSession.Answers, func(answer *FormAnswer, _ int) {
				answer.Files = filesByAnswerIDs[answer.ID]
			})
		}
	}
	return formSession, err
}

func (r *FormSessionRepository) FindOne(query *FormSessionQuery, options *FindOneOptions) (*FormSession, error) {
	if options == nil {
		options = &FindOneOptions{}
	}
	shouldPreloadFiles := false
	if options.Preloads != nil && lo.Contains(options.Preloads, util.AnswersFilesField) {
		shouldPreloadFiles = true
		options.Preloads = util.RemoveElement(options.Preloads, util.AnswersFilesField)
	}
	formSession, err := findOne[FormSession](FormSessionTbl, query, options)
	if err != nil {
		return nil, err
	}
	if shouldPreloadFiles {
		answerIDs := lo.Map(formSession.Answers, func(answer *FormAnswer, _ int) string {
			return answer.ID
		})
		if filesByAnswerIDs, mErr := Repository.FileRelation.GetFilesByEntities(FormAnswerModelName, answerIDs, util.FilesField); mErr != nil {
			return nil, mErr
		} else {
			lo.ForEach(formSession.Answers, func(answer *FormAnswer, _ int) {
				answer.Files = filesByAnswerIDs[answer.ID]
			})
		}
	}
	return formSession, err
}

func (r *FormSessionRepository) Count(query *FormSessionQuery) (int64, error) {
	return count[FormSession](FormSessionTbl, query)
}

func (r *FormSessionRepository) FindMany(query *FormSessionQuery, options *FindManyOptions) ([]*FormSession, error) {
	return findMany[FormSession](FormSessionTbl, query, options)
}

func (r *FormSessionRepository) FindPage(query *FormSessionQuery, options *FindPageOptions) ([]*FormSession, *Pagination, error) {
	return findPage[FormSession](FormSessionTbl, query, options)
}

func (r *FormSessionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[FormSession](FormSessionTbl, id, trans)
}

func (r *FormSessionRepository) DeleteMany(query *FormSessionQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[FormSession](FormSessionTbl, query, trans)
}

func (r *FormSessionRepository) UpdateOutDatedStatus(query *FormSessionQuery, trans *gorm.DB) (count int64, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	var entity FormSession
	now := time.Now().UnixMilli()
	result := qb.Table(GetTblName(FormSessionTbl)).Debug().
		Model(&entity).
		Updates(map[string]interface{}{
			"status":    "outdated",
			"update_at": now,
		})

	err = result.Error
	count = result.RowsAffected
	return

}
