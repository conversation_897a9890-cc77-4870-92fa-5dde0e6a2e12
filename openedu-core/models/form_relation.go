package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"gorm.io/gorm"
)

type FormTriggerEvent string

type FormConfirmationButtonType string

type FormRelationType string

const (
	FormRelationTypeForm         FormRelationType = "form"
	FormRelationTypeNotification FormRelationType = "notification"
)

type FormRelation struct {
	Model
	Enabled              bool                     `json:"enabled"`
	FormID               string                   `json:"form_id" gorm:"type:varchar(20);not null"`
	FormUID              string                   `json:"form_uid" gorm:"type:varchar(20)"`
	Form                 *Form                    `json:"form" gorm:"foreignKey:FormID"`
	RelatedEntityID      string                   `json:"related_entity_id" gorm:"type:varchar(20);not null"`
	RelatedEntityUID     string                   `json:"related_entity_uid" gorm:"type:varchar(20)"`
	RelatedEntityType    ModelName                `json:"related_entity_type"`
	OrgID                string                   `json:"org_id" gorm:"type:varchar(20)"`
	OrgSchema            string                   `json:"org_schema"`
	StartWhen            FormTriggerCondition     `json:"start_when" gorm:"type:jsonb"`
	EndWhen              *FormTriggerCondition    `json:"end_when" gorm:"type:jsonb"`
	ConfirmationSettings FormConfirmationSettings `json:"confirmation_settings" gorm:"type:jsonb"`
	Type                 FormRelationType         `json:"type" gorm:"default:'form'"`
	Name                 string                   `json:"name"`
	AddDate              int                      `json:"add_date" gorm:"type:int8;not null;default:0"`
	HasCancelBtn         bool                     `json:"has_cancel_btn"`

	Submitted *bool `json:"submitted,omitempty" gorm:"-"`
}

type FormConfirmationButton struct {
	Type    FormConfirmationButtonType `json:"type"`
	Text    string                     `json:"text"`
	Variant string                     `json:"variant"`
}

type FormConfirmationSettings struct {
	Enabled               bool                      `json:"enabled"`
	Title                 string                    `json:"title"`
	Description           string                    `json:"description"`
	Buttons               []*FormConfirmationButton `json:"buttons"`
	AutoCloseEnabled      bool                      `json:"auto_close_enabled"`
	AutoCloseAfterSeconds int                       `json:"auto_close_after_seconds"`
	DisplayOnDetail       bool                      `json:"display_on_detail"`
}

func (s FormConfirmationSettings) Value() (driver.Value, error) {
	val, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (s *FormConfirmationSettings) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, s)
}

type FormTriggerCondition struct {
	Type                    EventType `json:"type,omitempty"`
	Time                    int64     `json:"time,omitempty"`
	Days                    int64     `json:"days,omitempty"`
	MinCorrectAnswerEnabled bool      `json:"min_correct_answer_enabled,omitempty"`
	MinCorrectAnswers       int64     `json:"min_correct_answers,omitempty"`
	MaxSubmissions          int64     `json:"max_submissions,omitempty"`
	EntityID                string    `json:"entity_id,omitempty"`
	EntityType              string    `json:"entity_type,omitempty"`
}

func (c FormTriggerCondition) Value() (driver.Value, error) {
	val, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (c *FormTriggerCondition) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, c)
}

//type SubmissionNotifySettings struct {
//	NotifyCoursePartnersEnabled    bool   `json:"notify_course_partners_enabled"`
//	NotifyCourseEmailTemplateID    string `json:"notify_course_email_template_id"`
//	NotifySubmitterEnabled         bool   `json:"notify_submitter_enabled"`
//	NotifySubmitterEmailTemplateID string `json:"notify_submitter_email_template_id"`
//}
//
//func (s SubmissionNotifySettings) Value() (driver.Value, error) {
//	val, err := json.Marshal(s)
//	if err != nil {
//		return nil, err
//	}
//	return string(val), nil
//}
//
//func (s *SubmissionNotifySettings) Scan(input interface{}) error {
//	b, ok := input.([]byte)
//	if !ok {
//		return errors.New("type assertion to []byte failed")
//	}
//	return json.Unmarshal(b, s)
//}

type FormRelationQuery struct {
	FormID             *string    `form:"form_id" json:"form_id"`
	RelatedEntityType  *ModelName `form:"related_entity_type" json:"related_entity_type"`
	RelatedEntityID    *string    `form:"related_entity_id" json:"related_entity_id"`
	RelatedEntityIDIn  []string   `form:"related_entity_ids" json:"related_entity_ids"`
	RelatedEntityUIDIn []string   `form:"related_entity_uids" json:"related_entity_uids"`
	IncludeDeleted     *bool
}

func (query *FormRelationQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.FormID != nil {
		qb = qb.Where("form_id = ?", *query.FormID)
	}

	if query.RelatedEntityType != nil {
		qb = qb.Where("related_entity_type = ?", *query.RelatedEntityType)
	}

	if query.RelatedEntityID != nil {
		qb = qb.Where("related_entity_id = ?", *query.RelatedEntityID)
	}

	if len(query.RelatedEntityIDIn) > 0 {
		qb = qb.Where("related_entity_id IN (?)", query.RelatedEntityIDIn)
	}

	if len(query.RelatedEntityUIDIn) > 0 {
		qb = qb.Where("related_entity_uid IN (?)", query.RelatedEntityUIDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

// Create inserts a Form Relation to database, transaction is optional
func (r *FormRelationRepository) Create(q *FormRelation, trans *gorm.DB) error {
	return create(FormRelationTbl, q, trans)
}

func (r *FormRelationRepository) CreateMany(qs []*FormRelation, trans *gorm.DB) error {
	return createMany(FormRelationTbl, qs, trans)
}

// Update updates a Form Relation by ID in database, transaction is optional
func (r *FormRelationRepository) Update(q *FormRelation, trans *gorm.DB) error {
	return update(FormRelationTbl, q, trans)
}

// FindByID finds a Form Relation by ID with given find options, transaction is optional
func (r *FormRelationRepository) FindByID(id string, options *FindOneOptions) (*FormRelation, error) {
	return findByID[FormRelation](FormRelationTbl, id, options)
}

// FindOne finds one Form Relation with given find queries and options, transaction is optional
func (r *FormRelationRepository) FindOne(query *FormRelationQuery, options *FindOneOptions) (*FormRelation, error) {
	return findOne[FormRelation](FormRelationTbl, query, options)
}

// FindMany finds Form Relations by query conditions with give find options
func (r *FormRelationRepository) FindMany(query *FormRelationQuery, options *FindManyOptions) ([]*FormRelation, error) {
	return findMany[FormRelation](FormRelationTbl, query, options)
}

// FindPage returns Form Relations and pagination by query conditions and find options, transaction is optional
func (r *FormRelationRepository) FindPage(query *FormRelationQuery, options *FindPageOptions) ([]*FormRelation, *Pagination, error) {
	return findPage[FormRelation](FormRelationTbl, query, options)
}

// Delete perform soft deletion to a FormRelation by ID, transaction is optional
func (r *FormRelationRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[FormRelation](FormRelationTbl, id, trans)
}
