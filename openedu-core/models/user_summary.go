package models

import (
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserSummary struct {
	UserID       string `gorm:"primaryKey;type:varchar(20);unique" json:"user_id"`
	Following    int64  `json:"following" gorm:"type:int4;default:0"`
	Followers    int64  `json:"followers" gorm:"type:int4;default:0"`
	TotalBlogs   int64  `json:"total_blogs" gorm:"type:int4;default:0"`
	TotalCourses int64  `json:"total_courses" gorm:"type:int4;default:0"`
}

type SimpleUserSummary struct {
	UserID       string `json:"user_id"`
	Following    int64  `json:"following"`
	Followers    int64  `json:"followers"`
	TotalBlogs   int64  `json:"total_blogs"`
	TotalCourses int64  `json:"total_courses"`
}

type UserSummaryQuery struct {
	UserID   *string  `json:"user_id" form:"user_id"`
	UserIDIn []string `form:"user_id_in"`
}

func (query *UserSummaryQuery) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", query.UserID)
	}

	if query.UserIDIn != nil {
		qb = qb.Where("user_id IN (?)", query.UserIDIn)
	}

	return qb
}

// Create inserts a userSummary to database, transaction is optional
func (r *UserSummaryRepository) Create(us *UserSummary, trans *gorm.DB) error {
	return create(UserSummaryTbl, us, trans)
}

func (r *UserSummaryRepository) CreateMany(uss []*UserSummary, trans *gorm.DB) error {
	return createMany(UserSummaryTbl, uss, trans)
}

// Update updates a userSummary by ID in database, transaction is optional
func (r *UserSummaryRepository) Update(us *UserSummary, trans *gorm.DB) error {
	return update(UserSummaryTbl, us, trans)
}

func (r *UserSummaryRepository) Upsert(us *UserSummary, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}

	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"following", "followers", "total_blogs", "total_courses"}),
	}).Create(&us).Error
	return
}

// FindByID finds a userSummary by ID with given find options, transaction is optional
func (r *UserSummaryRepository) FindByID(id string, options *FindOneOptions) (*UserSummary, error) {
	return findByID[UserSummary](UserSummaryTbl, id, options)
}

// FindOne finds one userSummary with given find queries and options, transaction is optional
func (r *UserSummaryRepository) FindOne(query *UserSummaryQuery, options *FindOneOptions) (*UserSummary, error) {
	return findOne[UserSummary](UserSummaryTbl, query, options)
}

// FindMany finds userTokens by query conditions with give find options
func (r *UserSummaryRepository) FindMany(query *UserSummaryQuery, options *FindManyOptions) ([]*UserSummary, error) {
	return findMany[UserSummary](UserSummaryTbl, query, options)
}

// FindPage returns userTokens and pagination by query conditions and find options, transaction is optional
func (r *UserSummaryRepository) FindPage(query *UserSummaryQuery, options *FindPageOptions) ([]*UserSummary, *Pagination, error) {
	return findPage[UserSummary](UserSummaryTbl, query, options)
}

// Delete perform soft deletion to a userTokens by ID, transaction is optional
func (r *UserSummaryRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[UserSummary](UserSummaryTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *UserSummaryRepository) DeleteMany(query *UserSummaryQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[UserSummary](UserSummaryTbl, query, trans)
}

// Count returns number of userTokens by query conditions, transaction is optional
func (r *UserSummaryRepository) Count(query *UserSummaryQuery) (int64, error) {
	return count[UserSummary](UserSummaryTbl, query)
}

func (r *UserSummaryRepository) Increase(query *UserSummaryQuery, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = qb.Table(GetTblName(UserSummaryTbl)).Debug().UpdateColumn(field, gorm.Expr("? + ?", gorm.Expr(field), amount)).Error
	return
}

func (r *UserSummaryRepository) Decrease(query *UserSummaryQuery, field string, amount int, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	err = qb.Table(GetTblName(UserSummaryTbl)).Debug().UpdateColumn(field, gorm.Expr("? - ?", gorm.Expr(field), amount)).Error
	return
}
