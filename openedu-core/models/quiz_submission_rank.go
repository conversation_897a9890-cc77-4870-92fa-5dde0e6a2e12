package models

type QuizSubmissionRank struct {
	Rank                          int     `json:"rank"`
	SubmissionID                  string  `json:"submission_id"`
	UserID                        string  `json:"user_id"`
	User                          *User   `json:"user"`
	ArchivedPoints                int     `json:"archived_points"`
	HighestPointsOnSingleQuestion int     `json:"highest_points_on_single_question"`
	HighestStreak                 int     `json:"highest_streak"`
	TimeToCompleteInSeconds       float64 `json:"time_to_complete_in_milli_seconds"`
}

type SimpleQuizSubmissionRank struct {
	Rank                          int         `json:"rank"`
	SubmissionID                  string      `json:"submission_id"`
	UserID                        string      `json:"user_id"`
	User                          *SimpleUser `json:"user"`
	ArchivedPoints                int         `json:"archived_points"`
	HighestPointsOnSingleQuestion int         `json:"highest_points_on_single_question"`
	HighestStreak                 int         `json:"highest_streak"`
	TimeToCompleteInSeconds       float64     `json:"time_to_complete_in_milli_seconds"`
}

func (r *QuizSubmissionRank) ToSimple() *SimpleQuizSubmissionRank {
	simpleRank := &SimpleQuizSubmissionRank{
		Rank:                          r.Rank,
		SubmissionID:                  r.SubmissionID,
		UserID:                        r.UserID,
		ArchivedPoints:                r.ArchivedPoints,
		HighestPointsOnSingleQuestion: r.HighestPointsOnSingleQuestion,
		HighestStreak:                 r.HighestStreak,
		TimeToCompleteInSeconds:       r.TimeToCompleteInSeconds,
	}
	if r.User != nil {
		simpleRank.User = r.User.ToSimpleUser()
	}
	return simpleRank
}
