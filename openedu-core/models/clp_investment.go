package models

import (
	"errors"
	"fmt"
	"openedu-core/pkg/util"
	"sync"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type StatusInvestment string

const (
	PledgedStatus    StatusInvestment = "pledged"
	VotingStatus     StatusInvestment = "voting"
	GotRevenueStatus StatusInvestment = "got_revenue"
	GotRefunded      StatusInvestment = "got_refunded"
)

type ClpInvestment struct {
	Model
	UserID         string           `json:"user_id"`
	User           *SimpleUser      `json:"user" gorm:"-"`
	ClpLaunchpadID string           `json:"clp_launchpad_id"`
	ClpLaunchpad   *ClpLaunchpad    `json:"clp_launchpad" gorm:"-"`
	Amount         decimal.Decimal  `json:"amount" gorm:"type:numeric(19,4);not null;default:0"`
	RevenueAmount  decimal.Decimal  `json:"revenue_amount" gorm:"type:numeric(19,4);not null;default:0"`
	RefundedAmount decimal.Decimal  `json:"refunded_amount" gorm:"type:numeric(19,4);not null;default:0"`
	Currency       Currency         `json:"currency"`
	Status         StatusInvestment `json:"status"`
}

type SimpleClpInvestment struct {
	Model
	UserID         string           `json:"user_id"`
	Amount         decimal.Decimal  `json:"amount"`
	RevenueAmount  decimal.Decimal  `json:"revenue_amount"`
	RefundedAmount decimal.Decimal  `json:"refunded_amount"`
	Currency       Currency         `json:"currency"`
	Status         StatusInvestment `json:"status"`
}

type ClpInvestmentQuery struct {
	ID             *string           `json:"id" form:"id"`
	IDIn           []string          `json:"id_in" form:"id_in"`
	IDNotIn        []string          `json:"id_not_id" form:"id_not_id"`
	ClpLaunchpadID *string           `json:"clp_launchpad_id" form:"clp_launchpad_id"`
	Status         *StatusInvestment `json:"status" form:"status"`
	UserID         *string           `json:"user_id" form:"user_id"`
	IncludeDeleted *bool             `json:"include_deleted" form:"include_deleted"`
}

type LaunchpadStats struct {
	ClpLaunchpadID string          `json:"clp_launchpad_id"`
	TotalBackers   int             `json:"total_backers"`
	TotalAmount    decimal.Decimal `json:"total_amount"`
}

type InvestmentStats struct {
	TotalBackers int             `json:"total_backers"`
	TotalAmount  decimal.Decimal `json:"total_amount"`
}

type InvestmentPreload struct {
	User      bool
	Launchpad bool
}

func (c *ClpInvestment) Sanitize() *SimpleClpInvestment {
	return &SimpleClpInvestment{
		Model:          c.Model,
		UserID:         c.UserID,
		Amount:         c.Amount,
		RevenueAmount:  c.RefundedAmount,
		RefundedAmount: c.RefundedAmount,
		Currency:       c.Currency,
		Status:         c.Status,
	}
}

func getExPreloadInvestment(preloads []string) (*InvestmentPreload, []string) {
	shouldPreloadUser := false
	shouldPreloadLaunchpad := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, UserField) {
		shouldPreloadUser = true
		newPreloads = util.RemoveElement(newPreloads, UserField)
	}

	if lo.Contains(newPreloads, ClpLaunchpadField) {
		shouldPreloadLaunchpad = true
		newPreloads = util.RemoveElement(newPreloads, ClpLaunchpadField)
	}

	return &InvestmentPreload{
		User:      shouldPreloadUser,
		Launchpad: shouldPreloadLaunchpad,
	}, newPreloads
}

// Preload user for investment
func preloadInvestmentUser(investments []*ClpInvestment) error {
	userIDs := lo.Map(investments, func(investment *ClpInvestment, _ int) string {
		return investment.UserID
	})

	if users, pErr := Repository.User.FindMany(
		&UserQuery{IDIn: &userIDs},
		&FindManyOptions{}); pErr != nil {
		if !errors.Is(pErr, gorm.ErrRecordNotFound) {
			return pErr
		}
	} else {
		userMap := make(map[string]*User)
		for _, user := range users {
			userMap[user.ID] = user
		}

		for _, investment := range investments {
			if user, exists := userMap[investment.UserID]; exists {
				investment.User = user.ToSimpleUser()
			}
		}
	}

	return nil
}

// Preload launchpad for investment
func (r *ClpInvestmentRepository) preloadLaunchpad(investments []*ClpInvestment) error {
	launchpadIDs := lo.Map(investments, func(investment *ClpInvestment, _ int) string {
		return investment.ClpLaunchpadID
	})

	if launchpads, pErr := Repository.ClpLaunchpad(r.ctx).FindMany(
		&LaunchpadQuery{IDIn: launchpadIDs},
		&FindManyOptions{
			Preloads: []string{OwnerField},
		}); pErr != nil {
		if !errors.Is(pErr, gorm.ErrRecordNotFound) {
			return pErr
		}
	} else {
		launchpadMap := make(map[string]*ClpLaunchpad)
		for _, launchpad := range launchpads {
			launchpadMap[launchpad.ID] = launchpad
		}

		for _, investment := range investments {
			if launchpad, exists := launchpadMap[investment.ClpLaunchpadID]; exists {
				investment.ClpLaunchpad = launchpad
			}
		}
	}

	return nil
}

func (query *ClpInvestmentQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if len(query.IDNotIn) > 0 {
		qb = qb.Where("id NOT IN (?)", query.IDNotIn)
	}

	if query.ClpLaunchpadID != nil {
		qb = qb.Where("clp_launchpad_id = ?", *query.ClpLaunchpadID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *ClpInvestmentRepository) Create(e *ClpInvestment, trans *gorm.DB) error {
	return create(ClpInvestmentTbl, e, trans)
}

func (r *ClpInvestmentRepository) CreateMany(ts []*ClpInvestment, trans *gorm.DB) error {
	return createMany(ClpInvestmentTbl, ts, trans)
}

func (r *ClpInvestmentRepository) Update(f *ClpInvestment, trans *gorm.DB) error {
	return update(ClpInvestmentTbl, f, trans)
}

func (r *ClpInvestmentRepository) FindOne(query *ClpInvestmentQuery, options *FindOneOptions) (*ClpInvestment, error) {
	return findOne[ClpInvestment](ClpInvestmentTbl, query, options)
}

func (r *ClpInvestmentRepository) FindPage(query *ClpInvestmentQuery, options *FindPageOptions) ([]*ClpInvestment, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}

	exPreload, newPreloads := getExPreloadInvestment(preloads)
	options.Preloads = newPreloads
	investments, pagination, err := findPage[ClpInvestment](ClpInvestmentTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if exPreload.User {
		if preErr := preloadInvestmentUser(investments); preErr != nil {
			return investments, pagination, fmt.Errorf("preload users error: %v", preErr)
		}
	}

	if exPreload.Launchpad {
		if preErr := r.preloadLaunchpad(investments); preErr != nil {
			return investments, pagination, fmt.Errorf("preload launchpad error: %v", preErr)
		}
	}

	return investments, pagination, err
}

func (r *ClpInvestmentRepository) FindMany(query *ClpInvestmentQuery, options *FindManyOptions) ([]*ClpInvestment, error) {
	return findMany[ClpInvestment](ClpInvestmentTbl, query, options)
}

func (r *ClpInvestmentRepository) Count(query *ClpInvestmentQuery) (int64, error) {
	return count[ClpInvestment](ClpInvestmentTbl, query)
}

func (r *ClpInvestmentRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[ClpInvestment](ClpInvestmentTbl, id, trans)
}

func (r *ClpInvestmentRepository) FindPageWithStats(query *ClpInvestmentQuery, options *FindPageOptions) ([]*ClpInvestment, *Pagination, *InvestmentStats, error) {
	investmentsChan := make(chan []*ClpInvestment, 1)
	paginationChan := make(chan *Pagination, 1)
	statsChan := make(chan *InvestmentStats, 1)
	errorChan := make(chan error, 2)
	var wg sync.WaitGroup
	wg.Add(2)

	// Get investments with pagination
	go func() {
		defer wg.Done()
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic in FindPage: %v", r)
			}
		}()
		investments, pagination, err := r.FindPage(query, options)
		if err != nil {
			errorChan <- fmt.Errorf("FindPage failed: %w", err)
			return
		}

		investmentsChan <- investments
		paginationChan <- pagination
	}()

	// Get statistics
	go func() {
		defer wg.Done()
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic in GetInvestmentStats: %v", r)
			}
		}()

		stats, err := r.GetInvestmentStats(query)
		if err != nil {
			errorChan <- fmt.Errorf("GetInvestmentStats failed: %w", err)
			return
		}

		statsChan <- stats
	}()

	// close errorChan when goroutine done
	go func() {
		wg.Wait()
		close(errorChan)
	}()

	// result
	var (
		investments []*ClpInvestment
		pagination  *Pagination
		stats       *InvestmentStats
	)

	// check error
	if err := <-errorChan; err != nil {
		return nil, nil, nil, err
	}

	investments = <-investmentsChan
	pagination = <-paginationChan
	stats = <-statsChan

	return investments, pagination, stats, nil
}

func (r *ClpInvestmentRepository) GetInvestmentStats(query *ClpInvestmentQuery) (*InvestmentStats, error) {
	var stats InvestmentStats

	statsQuery := DB.Table(GetTblName(ClpInvestmentTbl)).Debug().
		Select("COUNT(DISTINCT user_id) as total_backers, COALESCE(SUM(amount), 0) as total_amount").
		Where("delete_at = 0")

	statsQuery = query.Apply(statsQuery)
	if err := statsQuery.Scan(&stats).Error; err != nil {
		return nil, fmt.Errorf("failed to get investment stats: %w", err)
	}

	return &stats, nil
}

func (r *ClpInvestmentRepository) IncreaseAmount(investment *ClpInvestment, amount decimal.Decimal, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	// Lock the amount until investment updated
	var invest ClpInvestment
	if err = tx.Table(GetTblName(ClpInvestmentTbl)).Debug().
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ? AND delete_at = 0", investment.ID).
		First(&invest).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update amount
	if err = tx.Table(GetTblName(ClpInvestmentTbl)).Debug().
		Where("id = ?", investment.ID).
		Update("amount", gorm.Expr("amount + ?", amount)).Error; err != nil {
		tx.Rollback()
		return err
	}

	return
}

func (r *ClpInvestmentRepository) GetInvestmentStatsByLaunchpadIDs(launchpadIDs []string) ([]*LaunchpadStats, error) {
	if len(launchpadIDs) == 0 {
		return nil, nil
	}

	var stats []*LaunchpadStats
	err := DB.Table(GetTblName(ClpInvestmentTbl)).Debug().
		Select(`
            clp_launchpad_id,
            COUNT(DISTINCT user_id) as total_backers,
            COALESCE(SUM(amount), 0) as total_amount
        `).
		Where("clp_launchpad_id IN ? AND delete_at = 0", launchpadIDs).
		Group("clp_launchpad_id").
		Scan(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get investment stats: %w", err)
	}

	return stats, nil
}

func (r *ClpInvestmentRepository) UpdateManyInvestmentStatus(status StatusInvestment, launchpadID string, trans *gorm.DB) (err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	defer func() {
		if rcv := recover(); rcv != nil {
			err = fmt.Errorf("panic: %v", rcv)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	result := DB.Table(GetTblName(ClpInvestmentTbl)).
		Where("clp_launchpad_id = ? AND delete_at = 0", launchpadID).
		Update("status", status)

	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update investment status: %w", result.Error)
	}

	// Check if any records were affected
	if result.RowsAffected == 0 {
		tx.Rollback()
		return fmt.Errorf("no matching investments found for launchpad ID: %s", launchpadID)
	}

	return nil
}
