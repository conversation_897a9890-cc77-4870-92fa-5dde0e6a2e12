package models

import "gorm.io/gorm"

// region: Page config
// region: Page config entity
type PageConfigType string

const (
	PageConfigTypeEntity PageConfigType = "entity"
	PageConfigTypeAction PageConfigType = "action"
)

type PageConfig struct {
	ID          string         `gorm:"primaryKey;type:varchar(120);unique" json:"id"`
	Name        string         `json:"name" gorm:"not null;type:varchar(50)"`
	Type        PageConfigType `json:"type" gorm:"not null"`
	Actions     StringArray    `json:"actions" gorm:"type: jsonb"`
	Description *string        `json:"description"`
}

type PageConfigQuery struct {
	ID             *string  `form:"id" json:"id,omitempty"`
	IDIn           []string `form:"id_in" json:"id_in,omitempty"`
	Name           *string  `form:"name" json:"name"`
	Type           *string  `form:"type" json:"type"`
	IncludeDeleted *bool    `form:"include_deleted" json:"include_deleted,omitempty"`
}

// end region: Page config entity

// region: impl Page config
func (query *PageConfigQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Type != nil {
		qb = qb.Where("type = ?", *query.Type)
	}

	return qb
}
func (r *PageConfigRepository) Create(p *PageConfig, trans *gorm.DB) error {
	return create(PageConfigTbl, p, trans)
}

func (r *PageConfigRepository) CreateMany(entities []*PageConfig, trans *gorm.DB) error {
	if err := createMany(PageConfigTbl, entities, trans); err != nil {
		return err
	}
	return nil
}

func (r *PageConfigRepository) FindByID(id string, options *FindOneOptions) (*PageConfig, error) {
	return findByID[PageConfig](PageConfigTbl, id, options)
}

func (r *PageConfigRepository) Update(p *PageConfig, trans *gorm.DB) error {
	return update(PageConfigTbl, p, trans)
}

func (r *PageConfigRepository) FindOne(query *PageConfigQuery, options *FindOneOptions) (*PageConfig, error) {
	return findOne[PageConfig](PageConfigTbl, query, options)
}

func (r *PageConfigRepository) FindMany(query *PageConfigQuery, options *FindManyOptions) ([]*PageConfig, error) {
	return findMany[PageConfig](PageConfigTbl, query, options)
}

func (r *PageConfigRepository) FindPage(query *PageConfigQuery, options *FindPageOptions) ([]*PageConfig, *Pagination, error) {
	return findPage[PageConfig](PageConfigTbl, query, options)
}

func (r *PageConfigRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[PageConfig](PageConfigTbl, id, trans)
}

func (r *PageConfigRepository) DeleteMany(query *PageConfigQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[PageConfig](PageConfigTbl, query, trans)
}

func (r *PageConfigRepository) UpdateMany(entities []*PageConfig, trans *gorm.DB) error {
	var tx *gorm.DB
	// Begin trans
	if trans != nil {
		tx = trans

	} else {
		tx = GetDb(PageConfigTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	for _, c := range entities {
		if err := tx.Model(&PageConfig{}).Where("id = ?", c.ID).Updates(map[string]interface{}{
			"type":        c.Type,
			"actions":     c.Actions,
			"description": c.Description,
		}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}
