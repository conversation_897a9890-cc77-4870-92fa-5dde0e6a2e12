package models

import (
	"errors"
	"fmt"
	"openedu-core/pkg/log"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"
	"strings"
	"time"

	"github.com/samber/lo"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type User struct {
	Model
	Username           string             `json:"username" validate:"required,omitempty" gorm:"unique"`
	Email              string             `json:"email" validate:"required,omitempty" gorm:"unique"`
	NormalizedEmail    string             `json:"normalized_email,omitempty"`
	Phone              string             `json:"phone,omitempty"`
	Password           string             `json:"password,omitempty" gorm:"omitempty"`
	Active             bool               `json:"active" gorm:"default:0"`
	Blocked            bool               `json:"blocked" gorm:"default:0"`
	Roles              []*UserRoleOrg     `json:"roles,omitempty" gorm:"-"`
	Props              UserSettingsOption `json:"props,omitempty" gorm:"type:jsonb"`
	CoverPhoto         string             `json:"cover_photo,omitempty"`
	Skills             StringArray        `json:"skills,omitempty" gorm:"type:jsonb"`
	Avatar             string             `json:"avatar,omitempty"`
	DisplayName        string             `json:"display_name,omitempty"`
	Headline           string             `json:"headline,omitempty"`
	About              string             `json:"about,omitempty"`
	Position           string             `json:"position,omitempty"`
	RequireSetPassword bool               `json:"require_set_password,omitempty" gorm:"default:false"`
	Summary            *UserSummary       `json:"summary" gorm:"foreignKey:UserID;references:ID"`
	CreatedByID        *string            `json:"created_by_id"`

	CreatedBy *User `json:"created_by"`
}

func (u *User) GetID() string {
	return u.ID
}

type SimpleUser struct {
	ID                 string             `json:"id"`
	Username           string             `json:"username"`
	Email              string             `json:"email"`
	Active             bool               `json:"active"`
	Blocked            bool               `json:"blocked"`
	DisplayName        string             `json:"display_name"`
	Avatar             string             `json:"avatar"`
	Roles              []*SimpleUserRole  `json:"roles"`
	CoverPhoto         string             `json:"cover_photo"`
	Skills             []string           `json:"skills"`
	Headline           string             `json:"headline"`
	Phone              string             `json:"phone"`
	About              string             `json:"about"`
	Position           string             `json:"position"`
	Props              UserSettingsOption `json:"props"`
	RequireSetPassword bool               `json:"require_set_password" gorm:"default:false"`
}

type BasicUserProfile struct {
	ID          string `json:"id"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	DisplayName string `json:"display_name"`
	Active      bool   `json:"active"`
	Phone       string `json:"phone"`
}

type SimpleProfile struct {
	ID                     string            `json:"id"`
	Username               string            `json:"username"`
	DisplayName            string            `json:"display_name"`
	Email                  string            `json:"email"`
	Active                 bool              `json:"active"`
	Blocked                bool              `json:"blocked"`
	Roles                  []*SimpleUserRole `json:"roles"`
	Avatar                 string            `json:"avatar"`
	Headline               string            `json:"headline"`
	NewUserSurveyCompleted bool              `json:"new_user_survey_completed"`
}

type UserInfo struct {
	ID          string `json:"id,omitempty"`
	DisplayName string `json:"display_name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
}

type UserQuery struct {
	ID                     *string     `json:"id,omitempty" form:"id"`
	IDIn                   *[]string   `form:"id_in"`
	IDNotIn                []string    `json:"id_not_in" form:"id_not_in"`
	EmailIn                []string    `form:"email_in"`
	IncludeDeleted         *bool       `form:"include_deleted"`
	IncludeDeletedRoles    *bool       `form:"include_deleted_roles"`
	Email                  *string     `form:"email"`
	EmailNot               *string     `form:"email_not"`
	NormalizedEmail        *string     `form:"normalized_email"`
	NormalizedEmailEmpty   *bool       `form:"normalized_email_empty"`
	CoursePartnerRoleID    *CourseRole `form:"course_partner_role_id"`
	Username               *string     `form:"username"`
	Action                 *bool       `form:"action"`
	Active                 *bool       `form:"active"`
	Blocked                *bool       `form:"blocked"`
	OrgID                  *string     `form:"org_id"`
	CreatorIds             []string    `form:"creator_ids" json:"creator_ids,omitempty"`
	RoleID                 *string     `form:"role_id"`
	NotRoleID              *string     `form:"not_role_id"`
	RoleIDIn               []string    `json:"role_id_in" form:"role_id_in"`
	IsFull                 *bool       `json:"is_full" form:"is_full"`
	LearningStatusMigrated *bool       `json:"learning_status_migrated" form:"learning_status_migrated"`
	CreatedByID            *string     `form:"created_by_id"`
	BaseSearchQuery
}

func (query *UserQuery) Apply(tx *gorm.DB) *gorm.DB {
	qb := tx
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.IDIn != nil {
		qb = qb.Where("id IN (?)", *query.IDIn)
	}

	if len(query.EmailIn) > 0 {
		qb = qb.Where("email IN (?)", query.EmailIn)
	}

	if query.Email != nil {
		qb = qb.Where("email = ?", query.Email)
	}

	if query.EmailNot != nil {
		qb = qb.Where("email <> ?", query.EmailNot)
	}

	if query.NormalizedEmail != nil {
		qb = qb.Where("normalized_email = ?", query.NormalizedEmail)
	}

	if query.NormalizedEmailEmpty != nil && *query.NormalizedEmailEmpty {
		qb = qb.Where("(normalized_email = '' OR normalized_email IS NULL)")
	}

	if query.Username != nil {
		qb = qb.Where("username = ?", *query.Username)
	}

	if query.Action != nil {
		qb = qb.Where("action = ?", *query.Action)
	}

	if query.Blocked != nil {
		qb = qb.Where("blocked = ?", *query.Blocked)
	}

	if len(query.IDNotIn) > 0 {
		qb = qb.Where("id NOT IN (?)", query.IDNotIn)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, User{}, nil)

	}

	if query.LearningStatusMigrated != nil {
		qb = qb.Where("coalesce((props -> 'learning_status_migrated')::bool, false) = ?", *query.LearningStatusMigrated)
	}

	if query.CreatedByID != nil {
		qb = qb.Where("created_by_id = ?", *query.CreatedByID)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (query *UserQuery) ApplyJoinRole(tx *gorm.DB) *gorm.DB {
	qb := tx
	if query.ID != nil {
		qb = qb.Where("u.id = ?", *query.ID)
	}

	if query.IDIn != nil {
		qb = qb.Where("u.id IN (?)", *query.IDIn)
	}

	if len(query.EmailIn) > 0 {
		qb = qb.Where("u.email IN (?)", query.EmailIn)
	}

	if query.Email != nil {
		qb = qb.Where("u.email = ?", query.Email)
	}

	if query.Username != nil {
		qb = qb.Where("u.username = ?", *query.Username)
	}

	if query.Action != nil {
		qb = qb.Where("u.action = ?", *query.Action)
	}

	if query.Blocked != nil {
		qb = qb.Where("u.blocked = ?", *query.Blocked)
	}

	if query.OrgID != nil {
		qb = qb.Where("uro.org_id = ?", *query.OrgID)
	}

	if query.RoleID != nil {
		qb = qb.Where("uro.role_id = ?", *query.RoleID)
	}

	if query.NotRoleID != nil {
		qb = qb.Where("uro.role_id <> ?", *query.NotRoleID)
	}

	if len(query.RoleIDIn) > 0 {
		qb = qb.Where("uro.role_id IN (?)", query.RoleIDIn)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, User{}, util.NewString("u"))

	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("u.delete_at = 0")
	}

	if query.IncludeDeletedRoles == nil || !*query.IncludeDeletedRoles {
		qb = qb.Where("uro.delete_at = 0")
	}

	return qb
}

func (u *User) ToSimpleUser() *SimpleUser {
	return &SimpleUser{
		ID:          u.ID,
		DisplayName: u.DisplayName,
		Username:    u.Username,
		Email:       u.Email,
		Phone:       u.Phone,
		Active:      u.Active,
		Blocked:     u.Blocked,
		Avatar:      u.Avatar,
		Headline:    u.Headline,
	}
}

func (u *User) ToSimpleProfile() *SimpleProfile {
	simpleRoles := make([]*SimpleUserRole, 0)
	if u.Roles != nil {
		simpleRoles = lo.Map(u.Roles, func(item *UserRoleOrg, _ int) *SimpleUserRole {
			return item.ToSimple()
		})
	}
	return &SimpleProfile{
		ID:                     u.ID,
		Username:               u.Username,
		DisplayName:            u.DisplayName,
		Email:                  u.Email,
		Active:                 u.Active,
		Blocked:                u.Blocked,
		Roles:                  simpleRoles,
		Avatar:                 u.Avatar,
		NewUserSurveyCompleted: u.Props.NewUserSurveyCompleted,
	}
}

func (u *User) ToSimpleUserProfile() *SimpleUser {
	simpleUserProfile := &SimpleUser{
		ID:         u.ID,
		Username:   u.Username,
		Email:      u.Email,
		Active:     u.Active,
		Blocked:    u.Blocked,
		Avatar:     u.Avatar,
		Headline:   u.Headline,
		Skills:     u.Skills,
		CoverPhoto: u.CoverPhoto,
		About:      u.About,
		Position:   u.Position,
		Props:      u.Props,
		Roles: lo.Map(u.Roles, func(item *UserRoleOrg, _ int) *SimpleUserRole {
			return item.ToSimple()
		}),
	}
	simpleUserProfile.Props.Notification = ""
	return simpleUserProfile
}

// Sanitize returns new user that is removed sensitive data such as passwords, etc.
func (u *User) Sanitize() *User {
	return &User{
		Model:       u.Model,
		Username:    u.Username,
		DisplayName: u.DisplayName,
		Email:       u.Email,
		Phone:       u.Phone,
		Active:      u.Active,
		//Roles:        u.Roles,
		Password:           "",
		Props:              u.Props,
		RequireSetPassword: u.RequireSetPassword,
	}
}

// HashPassword substitutes User.Password with its bcrypt hash
func (u *User) HashPassword() error {
	hash, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.Password = string(hash)
	return nil
}

// ComparePassword compares User.Password hash with raw password
func (u *User) ComparePassword(password string) error {
	return bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
}

func (u *User) IsValid() (err error) {
	if err := util.Validator.Struct(u); err != nil {
		return err
	}

	return nil
}

// BeforeSave is GORM hooks, run before saving user
func (u *User) BeforeSave(tx *gorm.DB) (err error) {
	if u.Password != "" {
		if err := u.HashPassword(); err != nil {
			return nil
		}
	}

	if err := u.IsValid(); err != nil {
		return err
	}

	return
}

func (u *User) IsSysAdmin() bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsSysAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	urs = lo.Filter(urs, func(item *UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0
	})

	return IsSysAdminRoles(urs)
}

func (u *User) IsApprovalOwner(entity EntityApproval) bool {
	return u.ID == entity.GetUserID()
}

func (u *User) IsOpeneduAdmin(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsOrgAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	urs = lo.Filter(urs, func(item *UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0
	})

	org, oErr := Repository.Organization.FindByID(orgID, nil)
	if oErr != nil {
		log.Error("Repository.Organization.FindByID: Find  ", oErr)
		return false
	}

	isAdmin := lo.ContainsBy(urs, func(item *UserRoleOrg) bool {
		return item.RoleID == OrgAdminRoleType && item.OrgID == orgID && org.IsRoot()
	})

	return isAdmin
}

func (u *User) IsOrgAdmin(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsOrgAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	urs = lo.Filter(urs, func(item *UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0
	})

	return ExactlyOrgAdminRoles(urs, orgID)
}

func (u *User) IsOrgMod(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsOrgAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	urs = lo.Filter(urs, func(item *UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0
	})

	return IsOrgMod(urs, orgID)
}

func (u *User) IsOrgEditor(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsOrgAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	return IsOrgEditor(urs, orgID)
}

func (u *User) IsOrgWriter(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsOrgAdmin: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	return IsOrgWriter(urs, orgID)
}

func (u *User) IsCreator(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsCreator: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	urs = lo.Filter(urs, func(item *UserRoleOrg, _ int) bool {
		return item.DeleteAt == 0
	})

	return ExactlyOrgCreatorRoles(urs, orgID)
}

func (u *User) CanManagementBlog(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsCreator: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return IsOrgWriter(urs, orgID) || IsOrgEditor(urs, orgID) || ExactlyOrgAdminRoles(urs, orgID) || IsSysAdminRoles(urs)

}

func (u *User) MyDisplayName() string {
	if u.DisplayName != "" {
		return u.DisplayName
	}
	return u.Username
}

func (u *User) MyLocale() string {
	locale := u.Props.Locale
	if locale == "" {
		locale = EnLocale
	}
	return locale
}

func (u *User) MyRoles() []*UserRoleOrg {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:IsCreator: Find UserRoleOrg by user_id failed ", err)
		return nil
	}

	return urs
}

func (u *User) CanUpdateUser(domain string) bool {
	return u.IsSysAdmin() && setting.IsSysAdminSite(domain)
}

func (u *User) CanFindCreator(orgID string) bool {
	return u.IsOrgAdmin(orgID) || u.IsSysAdmin()
}

func (u *User) CanInviteCreator(orgID string) bool {
	return u.IsOrgAdmin(orgID) || u.IsSysAdmin()
}

func (u *User) CanInviteAgency(orgID string) bool {
	return u.IsOrgAdmin(orgID) || u.IsSysAdmin()
}

func (u *User) CanApproveCreatorForm(orgID string, orgFormID string) bool {
	return (u.IsOrgAdmin(orgID) && orgFormID == orgID) || u.IsSysAdmin()
}

func (u *User) CanApproveWriterForm(orgID string, orgFormID string) bool {
	return (u.IsOrgAdmin(orgID) && orgFormID == orgID) || (u.IsOrgWriter(orgID) && orgFormID == orgID) || u.IsSysAdmin()
}

func (u *User) CanApproveRegisterOrgForm() bool {
	return u.IsSysAdmin()
}

func (u *User) IsOrgOwner(org *Organization) bool {
	return org.UserID == u.ID
}

func (u *User) CanActCoupon(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:CanActCoupon: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return IsSysAdminRoles(urs) || ExactlyOrgAdminRoles(urs, orgID) || IsPartnerRoles(urs, orgID)
}

func (u *User) CanActBlog(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:CanActBlog: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return (IsSysAdminRoles(urs) || ExactlyOrgAdminRoles(urs, orgID)) || IsOrgWriter(urs, orgID) || IsOrgEditor(urs, orgID)
}

func (u *User) CanActEmailTemplate(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:CanActCoupon: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	return IsSysAdminRoles(urs) || ExactlyOrgAdminRoles(urs, orgID)
}

func (u *User) CanCreateForm(form *Form) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:CanCreateForm: Find UserRoleOrg by user_id failed ", err)
		return false
	}

	allow := false
	switch form.Event {
	case FormEventRegisterOrg:
		for _, r := range urs {
			if lo.Contains(CanCreateFormRegisterOrgRoles, r.RoleID) {
				allow = true
				break
			}
		}
	case FormEventRegisterCreator:
		for _, r := range urs {
			if lo.Contains(CanCreateFormRegisterInstructorRoles, r.RoleID) {
				allow = true
				break
			}
		}
	default:
		for _, r := range urs {
			if lo.Contains(CanCreateFormRegisterCourseRoles, r.RoleID) {
				allow = true
				break
			}
		}
	}
	return allow
}

func (u *User) CanActSysConfig(orgID string) bool {
	urs, err := Repository.UserRoleOrg.FindByUserId(u.ID)
	if err != nil {
		log.Error("Model:User:CanActCoupon: Find UserRoleOrg by user_id failed ", err)
		return false
	}
	allow := false

	if IsSysAdminRoles(urs) || ExactlyOrgAdminRoles(urs, orgID) {
		allow = true
	}

	return allow
}

func (u *User) CanUpdateBookmark(bookmark *Bookmark) bool {
	return u.ID == bookmark.UserID
}

func (u *User) CanDeleteBookmark(bookmark *Bookmark) bool {
	return u.ID == bookmark.UserID
}

// Create insert user data to database
func (r *UserRepository) Create(user *User) error {
	return create(UserTbl, user, nil)
}

func (r *UserRepository) CreateMany(users []*User, trans *gorm.DB) error {
	return createMany(UserTbl, users, trans)
}

// FindPage returns users and pagination by query conditions and find options, transaction is optional
func (r *UserRepository) FindPage(query *UserQuery, options *FindPageOptions) ([]*User, *Pagination, error) {
	log.Debugf("FindPage %+v", query)
	log.Debugf("FindPage %+v", options)
	return findPage[User](UserTbl, query, options)
}

// FindMany finds actions by query conditions with give find options
func (r *UserRepository) FindMany(query *UserQuery, options *FindManyOptions) ([]*User, error) {
	return findMany[User](UserTbl, query, options)
}

// FindByID finds a user by ID
func (r *UserRepository) FindByID(id string) (*User, error) {
	var userCache User
	if cErr := Cache.User.GetByUserID(id, &userCache); cErr == nil && userCache.ID != "" {
		return &userCache, nil
	}
	user, err := r.findByField("id", id, nil)
	if err != nil {
		return nil, err
	}

	if cErr := Cache.User.SetUser(user.ID, user); cErr != nil {
		log.Errorf("UserRepository::FindByID Set user to cache failed: %v", cErr)
	}

	return user, err
}

// FindByIDWithOpts finds a user by ID, with optional preloading of related data.
func (r *UserRepository) FindByIDWithOpts(id string, opts *FindOneOptions) (*User, error) {
	return r.findByField("id", id, opts)
}

// FindByEmailWithOpts finds a user by email, with optional preloading of related data.
func (r *UserRepository) FindByEmailWithOpts(email string, opts *FindOneOptions) (*User, error) {
	return r.findByField("email", email, opts)
}

// FindByUsernameWithOpts finds a user by username, with optional preloading of related data.
func (r *UserRepository) FindByUsernameWithOpts(username string, opts *FindOneOptions) (*User, error) {
	return r.findByField("username", username, opts)
}

func (r *UserRepository) makeCacheKeyFindByField(fieldName string, value string, opts *FindOneOptions) string {
	key := fieldName + "_" + value
	if len(opts.Joins) > 0 {
		key += "_joins_" + strings.Join(opts.Joins, "_")
	}
	if len(opts.Preloads) > 0 {
		key += "_preloads_" + strings.Join(opts.Preloads, "_")
	}
	if len(opts.Sort) > 0 {
		key += "_sorts"
		for _, sort := range opts.Sort {
			key += "_" + strings.ReplaceAll(sort, " ", "-")
		}
	}
	if len(opts.CustomPreloads) > 0 {
		key += "_custom_preloads"
		for k, v := range opts.CustomPreloads {
			key += "_" + fmt.Sprintf("%s-%v", k, util.NameOfFunction(v))
		}
	}
	return key
}

// findUserByField finds a user by a specific field and value, with optional preloading of related data.
func (r *UserRepository) findByField(fieldName string, value string, opts *FindOneOptions) (*User, error) {
	var user *User
	qb := GetDb(UserTbl)
	if opts == nil {
		opts = &FindOneOptions{}
	}

	for _, preload := range opts.Preloads {
		qb = qb.Preload(preload)
	}

	var userCache User
	cacheKey := r.makeCacheKeyFindByField(fieldName, value, opts)
	if cErr := Cache.User.GetByKey(cacheKey, &userCache); cErr == nil && userCache.ID != "" {
		return &userCache, nil
	}

	err := qb.Debug().Where(fieldName+" = ? and delete_at = 0", value).First(&user).Error
	if err != nil {
		return nil, err
	}

	if cErr := Cache.User.SetByKey(cacheKey, user); cErr != nil {
		log.Errorf("UserRepository::findByField Set user to cache failed: %v", cErr)
	}

	return user, nil
}

func (r *UserRepository) Update(user *User) (*User, error) {
	// Relation many to many must use select uppercase to update.
	err := GetDb(UserTbl).Debug().Model(&user).
		// Select("username", "email", "profile_image", "password", "active", "blocked", "props", "update_at").
		Select("username", "email", "normalized_email", "learning_status_migrated", "avatar", "active", "blocked", "props", "update_at", "display_name", "phone", "headline", "about", "position", "skills", "cover_photo", "referrer_id").
		Updates(user).Error
	if err != nil {
		return nil, err
	}

	if cErr := Cache.User.Flush(); cErr != nil {
		log.Errorf("UserRepository.Update::Clear cache users error: %v", cErr)
	}
	return user, nil
}

func (r *UserRepository) UpdateByAdmin(user *User) (*User, error) {
	// Relation many to many must use select uppercase to update.
	err := GetDb(UserTbl).Debug().Model(&user).
		Select("username", "email", "avatar", "active", "blocked", "props", "update_at", "display_name", "phone", "headline", "about", "position", "password").
		Updates(user).Error
	if err != nil {
		return nil, err
	}

	if cErr := Cache.User.Flush(); cErr != nil {
		log.Errorf("UserRepository.UpdateByAdmin::Clear cache users error: %v", cErr)
	}
	return user, nil
}

// UpdatePassword updates the password of the user
func (r *UserRepository) UpdatePassword(user *User) (*User, error) {
	err := GetDb(UserTbl).Debug().Model(&user).
		Select("password", "update_at").
		Updates(user).Error
	if err != nil {
		return nil, err
	}

	if cErr := Cache.User.Flush(); cErr != nil {
		log.Errorf("UserRepository.UpdatePassword::Clear cache users error: %v", cErr)
	}
	return user, nil
}

// Delete performs soft deletion to a user by ID
func (r *UserRepository) Delete(id string, trans *gorm.DB) error {
	err := deleteByID[User](UserTbl, id, trans)
	if err != nil {
		return err
	}

	if cErr := Cache.User.Flush(); cErr != nil {
		log.Errorf("UserRepository.Delete::Clear cache users error: %v", cErr)
	}
	return nil
}

// WithdrawUser withdraw user
func (r *UserRepository) WithdrawUser(id string, email string, transaction *gorm.DB) error {
	var tx *gorm.DB
	if transaction != nil {
		tx = transaction

	} else {
		tx = GetDb(UserTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	now := time.Now().UnixMilli()
	email = fmt.Sprintf("deleted_%d_%s", now, email)
	tableName, err := GetTableName(&User{}, tx)
	if err != nil {
		return err
	}

	result := tx.Table(tableName).Where("id = ? AND delete_at = 0", id).Updates(map[string]interface{}{
		"email":     email,
		"update_at": now,
		"delete_at": now,
		"active":    false,
	})
	if err := result.Error; err != nil {
		return err
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	if cErr := Cache.User.Flush(); cErr != nil {
		log.Errorf("UserRepository.WithdrawUser::Clear cache users error: %v", cErr)
	}

	// Do not commit when use external transaction
	if transaction != nil {
		return nil
	}

	return tx.Commit().Error
}

// Count returns number of users by query conditions, transaction is optional
func (r *UserRepository) Count(query *UserQuery) (int64, error) {
	return count[User](UserTbl, query)
}

func (r *UserRepository) CanRegister(username, email string) (bool, error) {
	var c int64
	if err := GetDb(UserTbl).Model(&User{}).Where("username = ? OR email = ?", username, email).Count(&c).Error; err != nil {
		return false, err
	}
	return c == 0, nil
}

func (r *UserRepository) UpdateRoles(role *UserRoleOrg) error {
	a := &UserRoleOrgQuery{
		UserID:         util.NewString(role.UserID),
		RoleID:         util.NewString(role.RoleID),
		OrgID:          util.NewString(role.OrgID),
		IncludeDeleted: util.NewBool(true),
	}
	if o, err2 := Repository.UserRoleOrg.FindOne(a, nil); err2 != nil {
		if errors.Is(err2, gorm.ErrRecordNotFound) {
			return Repository.UserRoleOrg.Create(&UserRoleOrg{
				UserID: role.UserID,
				RoleID: role.RoleID,
				OrgID:  role.OrgID,
			})
		} else {
			return err2
		}
	} else {
		o.DeleteAt = 0
		if err3 := Repository.UserRoleOrg.Update(o, nil); err3 != nil {
			return err3
		}
	}
	return nil
}

func (r *UserRepository) UpsertMany(users []*User, trans *gorm.DB) error {
	var tx *gorm.DB
	// Begin transaction
	if trans != nil {
		tx = trans
	} else {
		tx = GetDb(UserTbl).Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	if err := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "email"}},
		DoUpdates: clause.AssignmentColumns([]string{"username", "email", "avatar", "active", "blocked", "props", "update_at", "display_name", "phone", "headline", "about", "position"}),
	}).Create(&users).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	return tx.Commit().Error
}

func (r *UserRepository) FindManyMapEmail(emails []string) (map[string]*User, error) {
	var entities []*User
	result := make(map[string]*User, len(emails))

	for _, email := range emails {
		result[email] = nil
	}

	// Query the database
	err := DB.Table(GetTblName(UserTbl)).Where("email IN (?)", emails).Find(&entities).Error

	if err != nil {
		return nil, err
	}

	// Update the map with the fetched entities
	for _, entity := range entities {
		result[entity.Email] = entity
	}

	return result, nil
}

func (r *UserRepository) FindOne(query *UserQuery, options *FindOneOptions) (*User, error) {
	return findOne[User](UserTbl, query, options)
}

func (r *UserRepository) FindUserFromRoles(query *UserQuery, options *FindPageOptions) ([]*User, *Pagination, error) {
	entitiesChan := make(chan []*User)
	countChan := make(chan int64)
	errorChan := make(chan error)
	var err error
	defer func() {
		if re := recover(); re != nil {
			err = fmt.Errorf("panic: %v", re)
		}
	}()

	go func() {
		defer func() {
			if re := recover(); re != nil {
				errorChan <- fmt.Errorf("panic: %v", re)
			}
		}()

		var entities []*User
		qb := query.ApplyJoinRole(DB)
		lo.ForEach(options.Sort, func(clause string, _ int) {
			qb = qb.Order("u." + clause)
		})
		qb = qb.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)

		result := qb.Table(GetTblName(UserTbl) + " as u").Debug().
			Select("u.*").
			Joins("join " + GetTblName(UserRoleOrgTbl) + " as uro on uro.user_id = u.id").
			Find(&entities)
		if fErr := result.Error; fErr != nil {
			errorChan <- fErr
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if re := recover(); re != nil {
				errorChan <- fmt.Errorf("panic: %v", re)
			}
		}()

		var c int64
		var entity User
		qb := query.ApplyJoinRole(DB)
		result := qb.Table(GetTblName(UserTbl) + " as u").Debug().
			Joins("join " + GetTblName(UserRoleOrgTbl) + " as uro on uro.user_id = u.id").
			Model(&entity).Count(&c)
		if err := result.Error; err != nil {
			errorChan <- err
			return
		}
		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*User
	var entityCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entityCount = <-countChan:
		case err = <-errorChan:
			return nil, nil, err
		}
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entityCount)), nil
}

type OwnerHolder interface {
	GetOwnerID() string
	SetOwner(user *User)
}

func PreloadOwner[T OwnerHolder](items []T) error {
	var users []*User
	var userIDs []string
	for _, item := range items {
		var userCache User
		if cErr := Cache.User.GetByUserID(item.GetOwnerID(), &userCache); cErr == nil && userCache.ID != "" {
			users = append(users, &userCache)
		} else {
			userIDs = append(userIDs, item.GetOwnerID())
		}
	}

	if len(userIDs) > 0 {
		us, err := Repository.User.FindMany(
			&UserQuery{IDIn: &userIDs},
			&FindManyOptions{})
		if err != nil {
			return err
		}
		users = append(users, us...)
	}

	usersByIDs := make(map[string]*User)
	for _, user := range users {
		usersByIDs[user.ID] = user
		if cErr := Cache.User.SetUser(user.ID, user); cErr != nil {
			log.Errorf("UserRepository::PreloadOwner Set user to cache failed: %v", cErr)
		}
	}

	for _, item := range items {
		if user, exists := usersByIDs[item.GetOwnerID()]; exists {
			item.SetOwner(user)
		}
	}
	return nil
}

func (r *UserRepository) FindManySimpleUser(query *UserQuery, options *FindManyOptions) ([]*BasicUserProfile, error) {
	return findMany[BasicUserProfile](UserTbl, query, options)
}
