package models

import (
	"gorm.io/gorm"
)

type DetailAIError struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type AIHistory struct {
	Model
	RequestIDs   StringArray    `json:"request_ids" gorm:"column:request_ids;type:jsonb"` // target generate id
	RequestType  ModelName      `json:"request_type" gorm:"column:request_type;type:varchar(50)"`
	UserID       string         `json:"user_id" gorm:"column:user_id;type:varchar(20)"`
	EntityID     string         `json:"entity_id" gorm:"column:entity_id;type:varchar(20)"` // parent generate id
	EntityType   ModelName      `json:"entity_type" gorm:"column:entity_type;type:varchar(50)"`
	GenerateID   string         `json:"generate_id" gorm:"column:generate_id;type:varchar(20)"` // ai info id
	GenerateType AIOfferType    `json:"generate_type" gorm:"column:generate_type;type:varchar(50)"`
	Status       AIStatus       `json:"status" gorm:"column:status;type:varchar(20);default:pending"`
	Cost         float64        `json:"cost" gorm:"column:cost;type:decimal(20,2)"`
	Request      JSONB          `json:"request" gorm:"column:request;type:jsonb"`
	Response     JSONB          `json:"response" gorm:"column:response;type:jsonb"`
	StartDate    int64          `json:"start_date" gorm:"column:start_date;type:int8;default:0"`       // start when generate with ai started
	EndDate      int64          `json:"end_date" gorm:"column:end_date;type:int8;default:0"`           // end when generate with ai completed
	RunDuration  int16          `json:"run_duration" gorm:"column:run_duration;type:int2;default:0"`   // end_date - start_date
	WaitDuration int16          `json:"wait_duration" gorm:"column:wait_duration;type:int2;default:0"` // end_date - create_date
	Error        *DetailAIError `json:"error" gorm:"column:error;type:jsonb"`
	OrgID        string         `json:"org_id" gorm:"column:org_id;type:varchar(20)"`
	OrgSchema    string         `json:"org_schema" gorm:"column:org_schema;type:varchar(50)"`
	Step         AIGenerateStep `json:"step" gorm:"column:step;type:varchar(50)"`
	AIOfferID    string         `json:"ai_offer_id" gorm:"column:ai_offer_id;type:varchar(50)"` // offer id from ai
}

type AIHistoryQuery struct {
	RequestType        *ModelName      `json:"request_type" form:"request_type"`
	UserID             *string         `json:"user_id" form:"user_id"`
	EntityID           *string         `json:"entity_id" form:"entity_id"`
	EntityType         *ModelName      `json:"entity_type" form:"entity_type"`
	GenerateID         *string         `json:"generate_id" form:"generate_id"`
	GenerateType       *AIOfferType    `json:"generate_type" form:"generate_type"`
	Status             *AIStatus       `json:"status" form:"status"`
	OrgID              *string         `json:"org_id" form:"org_id"`
	OrgSchema          *string         `json:"org_schema" form:"org_schema"`
	Step               *AIGenerateStep `json:"step" form:"step"`
	IDIn               []string        `json:"id_in,omitempty" form:"id_in"`
	StatusIn           []*AIStatus     `json:"status_in,omitempty" form:"status_in"`
	RequestIDsEmpty    bool            `json:"request_ids_empty" form:"request_ids_empty"`
	ID                 *string         `json:"id,omitempty" form:"id"`
	IncludeDeleted     *bool
	RequestIDsNotEmpty bool              `json:"request_ids_not_empty" form:"request_ids_not_empty"`
	AIOfferID          *string           `json:"ai_offer_id" form:"column:ai_offer_id"`
	StepIn             []*AIGenerateStep `json:"step_in" form:"step_in"`
}

func (query *AIHistoryQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.UserID != nil {
		qb = qb.Where("user_id = ?", *query.UserID)
	}

	if query.RequestIDsEmpty {
		qb = qb.Where("jsonb_array_length(request_ids) = 0")
	}

	if query.RequestIDsNotEmpty {
		qb = qb.Where("jsonb_array_length(request_ids) > 0")
	}

	if query.RequestType != nil {
		qb = qb.Where("request_type = ?", *query.RequestType)
	}

	if query.EntityID != nil {
		qb = qb.Where("entity_id = ?", *query.EntityID)
	}

	if query.EntityType != nil {
		qb = qb.Where("entity_type = ?", *query.EntityType)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.GenerateID != nil {
		qb = qb.Where("generate_id = ?", *query.GenerateID)
	}

	if query.GenerateType != nil {
		qb = qb.Where("generate_type = ?", *query.GenerateType)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN ?", query.IDIn)
	}

	if len(query.StatusIn) > 0 {
		qb = qb.Where("status IN ?", query.StatusIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.AIOfferID != nil {
		qb = qb.Where("ai_offer_id = ?", *query.AIOfferID)
	}

	if len(query.StepIn) > 0 {
		qb = qb.Where("step IN ?", query.StepIn)
	}

	return qb
}

// Create inserts a aiHistory to database, transaction is optional
func (r *AIHistoryRepository) Create(ah *AIHistory, trans *gorm.DB) error {
	return create(AIHistoryTbl, ah, trans)
}

func (r *AIHistoryRepository) CreateMany(ahs []*AIHistory, trans *gorm.DB) error {
	return createMany(AIHistoryTbl, ahs, trans)
}

// Update updates a aiHistory by ID in database, transaction is optional
func (r *AIHistoryRepository) Update(ah *AIHistory, trans *gorm.DB) error {
	return update(AIHistoryTbl, ah, trans)
}

// FindByID finds a aiHistory by ID with given find options, transaction is optional
func (r *AIHistoryRepository) FindByID(id string, options *FindOneOptions) (*AIHistory, error) {
	return findByID[AIHistory](AIHistoryTbl, id, options)
}

// FindOne finds one aiHistory with given find queries and options, transaction is optional
func (r *AIHistoryRepository) FindOne(query *AIHistoryQuery, options *FindOneOptions) (*AIHistory, error) {
	return findOne[AIHistory](AIHistoryTbl, query, options)
}

// FindMany finds userTokens by query conditions with give find options
func (r *AIHistoryRepository) FindMany(query *AIHistoryQuery, options *FindManyOptions) ([]*AIHistory, error) {
	return findMany[AIHistory](AIHistoryTbl, query, options)
}

// FindPage returns userTokens and pagination by query conditions and find options, transaction is optional
func (r *AIHistoryRepository) FindPage(query *AIHistoryQuery, options *FindPageOptions) ([]*AIHistory, *Pagination, error) {
	return findPage[AIHistory](AIHistoryTbl, query, options)
}

// Delete perform soft deletion to a userTokens by ID, transaction is optional
func (r *AIHistoryRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[AIHistory](AIHistoryTbl, id, trans)
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *AIHistoryRepository) DeleteMany(query *AIHistoryQuery, trans *gorm.DB) (int64, error) {
	return deleteMany[AIHistory](AIHistoryTbl, query, trans)
}

func (r *AIHistoryRepository) GetLanguageFromKey(key string) string {
	languageMap := GetConfig[map[string]string](AILanguages)

	if language, exists := languageMap[key]; exists {
		return language
	}
	return languageMap["en"]
}

func (r *AIHistoryRepository) GetKeyFromLanguage(language string) string {
	languageMap := GetConfig[map[string]string](AILanguages)

	for key, lang := range languageMap {
		if lang == language {
			return key
		}
	}

	return "en"
}
