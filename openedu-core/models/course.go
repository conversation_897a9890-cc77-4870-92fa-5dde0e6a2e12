package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"openedu-core/pkg/dbscanner"
	"openedu-core/pkg/log"
	"openedu-core/pkg/util"
	"strings"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CourseStatus string

type CourseGroup string

const (
	CourseSTTDraft            CourseStatus = "draft"
	CourseSTTReviewing        CourseStatus = "reviewing"
	CourseSTTReject           CourseStatus = "reject"
	CourseSTTPublic           CourseStatus = "publish"
	CourseSTTPublicRoot       CourseStatus = "publish_root"
	CourseSTTUnCancelled      CourseStatus = "cancelled"
	CourseSTTPublicInvestment CourseStatus = "public_investment"

	CourseGroupInProgress CourseGroup = "in_progress"
	CourseGroupNotStarted CourseGroup = "not_started"
	CourseGroupCompleted  CourseGroup = "completed"
	CourseGroupWishlist   CourseGroup = "wishlist"
)

type CourseIface interface {
	GetCourseCUID() string
	SetIsPaid(v bool)
	SetIsEnrolled(v bool)
	SetIsWishlist(v bool)
	SetBookmark(v *Bookmark)
}

type Course struct {
	OrgID             string       `json:"org_id" gorm:"not null;type:varchar(20)"`
	Cuid              string       `json:"cuid" gorm:"not null;type:varchar(20)"` // course unique ID
	Version           int          `json:"version" gorm:"type:int8;default:0"`
	Latest            bool         `json:"latest" gorm:"default:true"`
	Name              string       `json:"name" gorm:"not null"`
	Slug              string       `json:"slug"`
	Description       string       `json:"description" gorm:"type:text"`
	ShortDesc         string       `json:"short_desc" gorm:"type:text"`
	ThumbnailID       string       `json:"thumbnail_id"`
	LearnMethod       string       `json:"learn_method"`
	UserID            string       `json:"user_id" gorm:"not null"`
	Status            CourseStatus `json:"status" gorm:"default:'draft'"`
	Props             CourseProps  `json:"props" gorm:"type:jsonb"`
	PubDate           int          `json:"pub_date" gorm:"type:int8;default:0"`
	PubRejectDate     int          `json:"pub_reject_date" gorm:"type:int8;default:0"`
	PubRootDate       int          `json:"pub_root_date" gorm:"type:int8;default:0"`
	PubRootRejectDate int          `json:"pub_root_reject_date" gorm:"type:int8;default:0"`
	Enable            bool         `json:"enable" gorm:"default:true"`
	StartDate         int          `json:"start_date" gorm:"type:int8;default:0"`
	EndDate           int          `json:"end_date" gorm:"type:int8;default:0"`
	MarkAsCompleted   bool         `json:"mark_as_completed" gorm:"default:false"`
	CourseIDV2        string       `json:"course_id_v2" gorm:"type:varchar(20)"`

	IsAIGenerated    bool            `json:"is_ai_generated" gorm:"default:false"`
	AIGenerateStatus AIStatus        `json:"ai_generate_status" gorm:"default:manual"`
	AICourseID       string          `json:"ai_course_id,omitempty" gorm:"type:varchar(20)"`
	AICourse         *SimpleAICourse `json:"ai_course" gorm:"-"`

	Model

	SectionCount  int   `json:"section_count" gorm:"type:int8;default:0"`
	LessonCount   int   `json:"lesson_count" gorm:"type:int8;default:0"`
	ActiveLesson  int   `json:"active_lesson" gorm:"type:int8;default:0"`
	ActiveSection int   `json:"active_section" gorm:"type:int8;default:0"`
	VideoCount    int   `json:"video_count" gorm:"type:int8;default:0"`
	QuizCount     int   `json:"quiz_count" gorm:"type:int8;default:0"`
	LearnerCount  int64 `json:"learner_count" gorm:"-"`

	Accesses JSONArray `json:"accesses" gorm:"->:true"` // join form partner

	PriceSettings            *CoursePrice                    `json:"price_settings" gorm:"-"`
	Thumbnail                *File                           `json:"thumbnail" gorm:"-"`
	Medias                   []*File                         `json:"medias" gorm:"-"`
	Docs                     []*File                         `json:"docs" gorm:"-"`
	Outline                  []*Section                      `json:"outline" gorm:"-"`
	Partners                 []*SimplePartner                `json:"partners" gorm:"-"`
	Categories               []*Category                     `json:"categories" gorm:"-"`
	Levels                   []*Category                     `json:"levels" gorm:"-"`
	Reviewing                *SimplePublishCourseStatus      `json:"reviewing" gorm:"-"`
	RootRequest              *Approval                       `json:"root_request" gorm:"-"`
	OrgRequest               *Approval                       `json:"org_request" gorm:"-"`
	Published                []*SimplePublishCourseStatus    `json:"published" gorm:"-"`
	FormRelations            []*FormRelation                 `json:"form_relations" gorm:"-"`
	LearningProgressOverview *SimpleLearningProgressOverview `json:"learning_progress_overview" gorm:"-"`
	Bookmark                 *Bookmark                       `json:"bookmark" gorm:"-"`
	Owner                    *SimpleProfile                  `json:"owner" gorm:"-"`

	Rating float64             `json:"rating" gorm:"-"`
	Org    *SimpleOrganization `json:"org" gorm:"-"`

	IsReceivedCert bool          `json:"is_received_cert" gorm:"-"`
	IsPaid         bool          `json:"is_paid" gorm:"-"`
	IsEnrolled     bool          `json:"is_enrolled" gorm:"-"`
	IsWishlist     bool          `json:"is_wishlist" gorm:"-"`
	HasCertificate bool          `json:"has_certificate" gorm:"default:false"`
	HtmlTemplate   *HtmlTemplate `json:"html_template" gorm:"-"`
	Launchpad      *ClpLaunchpad `json:"launchpad" gorm:"-"`
}

func (c *Course) GetCourseCUID() string {
	return c.Cuid
}

func (c *Course) SetIsPaid(v bool) {
	c.IsPaid = v
}

func (c *Course) SetIsEnrolled(v bool) {
	c.IsEnrolled = v
}

func (c *Course) SetIsWishlist(v bool) {
	c.IsWishlist = v
}

func (c *Course) SetBookmark(v *Bookmark) {
	c.Bookmark = v
}

type SimpleCourse struct {
	Model
	OrgID           string `json:"org_id"`
	Cuid            string `json:"cuid"`
	Version         int    `json:"version"`
	Latest          bool   `json:"latest"`
	Name            string `json:"name"`
	Slug            string `json:"slug"`
	Description     string `json:"description"`
	ShortDesc       string `json:"short_desc"`
	LearnMethod     string `json:"learn_method"`
	UserID          string `json:"user_id"`
	Enable          bool   `json:"enable"`
	StartDate       int    `json:"start_date"`
	EndDate         int    `json:"end_date"`
	MarkAsCompleted bool   `json:"mark_as_completed"`

	PubDate           int         `json:"pub_date"`
	PubRootDate       int         `json:"pub_root_date"`
	PubRootRejectDate int         `json:"pub_root_reject_date"`
	PubRejectDate     int         `json:"pub_reject_date"`
	Props             CourseProps `json:"props"`

	PriceSettings *CoursePrice `json:"price_settings" gorm:"foreignKey:CourseID;references:ID"`

	Status                   CourseStatus                    `json:"status"`
	Medias                   []*File                         `json:"medias"`
	Docs                     []*File                         `json:"docs"`
	Thumbnail                *File                           `json:"thumbnail"`
	Partners                 []*SimplePartner                `json:"partners"`
	Categories               []*Category                     `json:"categories"`
	Levels                   []*Category                     `json:"levels"`
	LearningProgressOverview *SimpleLearningProgressOverview `json:"learning_progress_overview,omitempty"`

	Published   []*SimplePublishCourseStatus `json:"published"`
	Org         *SimpleOrganization          `json:"org"`
	RootRequest *Approval                    `json:"root_request"`
	OrgRequest  *Approval                    `json:"org_request"`

	Rating        float64   `json:"rating"`
	LearnerCount  int64     `json:"learner_count"`
	ActiveSection int       `json:"active_section" `
	SectionCount  int       `json:"section_count"`
	VideoCount    int       `json:"video_count"`
	QuizCount     int       `json:"quiz_count"`
	LessonCount   int       `json:"lesson_count"`
	ActiveLesson  int       `json:"active_lesson"`
	Accesses      JSONArray `json:"accesses"`

	IsPaid     bool      `json:"is_paid"`
	IsEnrolled bool      `json:"is_enrolled"`
	IsWishlist bool      `json:"is_wishlist"`
	Bookmark   *Bookmark `json:"bookmark"`

	HasCertificate bool `json:"has_certificate"`

	Owner *SimpleProfile `json:"owner" gorm:"-"`

	IsAIGenerated    bool            `json:"is_ai_generated"`
	AIGenerateStatus AIStatus        `json:"ai_generate_status"`
	AICourseID       string          `json:"ai_course_id"`
	AICourse         *SimpleAICourse `json:"ai_course"`

	Launchpad *ClpLaunchpad `json:"launchpad"`
}

type CourseListItem struct {
	Cuid     string `json:"cuid" dbscan:"cuid"`
	ID       string `json:"id" dbscan:"id"`
	CreateAt int    `json:"create_at" dbscan:"create_at"`
	UpdateAt int    `json:"update_at" dbscan:"update_at"`
	DeleteAt int    `json:"delete_at" dbscan:"delete_at"`

	UserID      string `json:"user_id" dbscan:"user_id"`
	OrgID       string `json:"org_id" dbscan:"org_id"`
	Name        string `json:"name" dbscan:"name"`
	Slug        string `json:"slug" dbscan:"slug"`
	Description string `json:"description" dbscan:"description"`
	ShortDesc   string `json:"short_desc" dbscan:"short_desc"`

	Thumbnail     *SimpleFile    `json:"thumbnail" dbscan:"thumbnail"`
	Owner         *SimpleProfile `json:"owner"`
	Org           *OrgProfile    `json:"org"`
	Categories    []*Category    `json:"categories"`
	Levels        []*Category    `json:"levels"`
	PriceSettings *CoursePrice   `json:"price_settings" dbscan:"price_settings"`

	SectionCount  int   `json:"section_count" dbscan:"section_count"`
	LessonCount   int   `json:"lesson_count" dbscan:"lesson_count"`
	ActiveLesson  int   `json:"active_lesson" dbscan:"active_lesson"`
	ActiveSection int   `json:"active_section" dbscan:"active_section"`
	VideoCount    int   `json:"video_count" dbscan:"video_count"`
	QuizCount     int   `json:"quiz_count" dbscan:"quiz_count"`
	LearnerCount  int64 `json:"learner_count" dbscan:"learner_count"`

	Rating          float64 `json:"rating"`
	MarkAsCompleted bool    `json:"mark_as_completed" dbscan:"mark_as_completed"`
	HasCertificate  bool    `json:"has_certificate" dbscan:"has_certificate"`

	IsPaid                   bool                            `json:"is_paid"`
	IsEnrolled               bool                            `json:"is_enrolled"`
	IsWishlist               bool                            `json:"is_wishlist"`
	Bookmark                 *Bookmark                       `json:"bookmark"`
	LearningProgressOverview *SimpleLearningProgressOverview `json:"learning_progress_overview"`
}

func (i *CourseListItem) GetOrgID() string {
	return i.OrgID
}

func (i *CourseListItem) SetOrg(org *Organization) {
	if org != nil {
		i.Org = org.ToOrgProfile()
	} else {
		i.Org = nil
	}
}

func (i *CourseListItem) GetCourseID() string {
	return i.ID
}

func (i *CourseListItem) GetCourseCUID() string {
	return i.GetCUID()
}

func (i *CourseListItem) GetCUID() string {
	return i.Cuid
}

func (i *CourseListItem) SetLevels(levels []*Category) {
	i.Levels = levels
}

func (i *CourseListItem) SetCategories(categories []*Category) {
	i.Categories = categories
}

func (i *CourseListItem) AppendLevels(levels []*Category) {
	i.Levels = append(i.Levels, levels...)
}

func (i *CourseListItem) AppendCategories(categories []*Category) {
	i.Categories = append(i.Categories, categories...)
}

func (i *CourseListItem) SetLearnerCount(learnCount int64) {
	i.LearnerCount = learnCount
}

func (i *CourseListItem) GetOwnerID() string {
	return i.UserID
}

func (i *CourseListItem) SetOwner(user *User) {
	if user != nil {
		i.Owner = user.ToSimpleProfile()
	}
	i.Owner = nil
}

func (i *CourseListItem) SetIsPaid(v bool) {
	i.IsPaid = v
}

func (i *CourseListItem) SetIsEnrolled(v bool) {
	i.IsEnrolled = v
}

func (i *CourseListItem) SetIsWishlist(v bool) {
	i.IsWishlist = v
}

func (i *CourseListItem) SetBookmark(v *Bookmark) {
	i.Bookmark = v
}

type SimplePublishCourseStatus struct {
	OrgID             string       `json:"org_id"`
	Name              string       `json:"name"`
	ID                string       `json:"id"`
	Version           int          `json:"version"`
	Latest            bool         `json:"latest"`
	Status            CourseStatus `json:"status"`
	StartDate         int          `json:"start_date"`
	EndDate           int          `json:"end_date"`
	MarkAsCompleted   bool         `json:"mark_as_completed"`
	PubDate           int          `json:"pub_date"`
	PubRootDate       int          `json:"pub_root_date"`
	PubRootRejectDate int          `json:"pub_root_reject_date"`
	PubRejectDate     int          `json:"pub_reject_date"`
	IsRoot            bool         `json:"is_root"`
}

type CourseProps struct {
	SupportChannel       JSONB                       `json:"support_channel,omitempty"`
	PreviousVersion      int                         `json:"previous_version,omitempty"`
	PreviousID           string                      `json:"previous_id,omitempty"`
	RejectOrgReason      string                      `json:"reject_org_reason,omitempty"`
	RejectRootReason     string                      `json:"reject_root_reason,omitempty"`
	ApprovalUid          string                      `json:"approval_uid,omitempty"`
	RequestID            string                      `json:"request_id,omitempty"`
	RequestVersion       int                         `json:"request_version,omitempty"`
	PreApprovalUid       string                      `json:"pre_approval_uid,omitempty"`
	CertificateCondition *CertificateCondition       `json:"certificate_condition,omitempty"`
	MintCertNFTSettings  *MintCertificateNFTSettings `json:"mint_cert_nft_settings,omitempty"`
	Achievements         JSONArray                   `json:"achievements,omitempty"`
	PrivateChannels      StringArray                 `json:"private_channels,omitempty"`
	DefaultLanguage      string                      `json:"default_language,omitempty"`
	TelegramChannel      string                      `json:"telegram_channel,omitempty"`

	PreviewLessons                    []PreviewLessonContent `json:"preview_lessons,omitempty"`
	IsAffiliate                       bool                   `json:"is_affiliate,omitempty"`
	CourseEnrollmentEmailTemplateCode string                 `json:"course_enrollment_email_template_code,omitempty"`
	IsCacheContents                   bool                   `json:"is_cache_content"`
}

type MintCertificateNFTSettings struct {
	Enabled     bool              `json:"enabled"`
	GasFeePayer Participant       `json:"gas_fee_payer"`
	Network     BlockchainNetwork `json:"network"`
}

func (s MintCertificateNFTSettings) Value() (driver.Value, error) {
	valueString, err := json.Marshal(s)
	return string(valueString), err
}

func (s *MintCertificateNFTSettings) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &s); err != nil {
		return err
	}
	return nil
}

type CertificateCondition struct {
	CompletedAllQuizzes           bool     `json:"completed_all_quiz"`
	CompletedCourse               bool     `json:"completed_course"`
	CourseCompletionPercentage    int      `json:"course_completion_percentage"`
	CompletedFinalQuiz            bool     `json:"completed_final_quiz"`
	FinalQuizCompletionPercentage int      `json:"final_quiz_completion_percentage"`
	CompletedRequiredLesson       bool     `json:"completed_required_lesson"`
	RequiredLessonUIDs            []string `json:"required_lesson_uids"`
}

func (c *CertificateCondition) IsCompletedCourseEnabled() bool {
	return c.CompletedCourse
}

func (c *CertificateCondition) IsCompletedAllQuizzesEnabled() bool {
	return c.CompletedAllQuizzes
}

func (c *CertificateCondition) IsCompletedFinalQuizEnabled() bool {
	return c.CompletedFinalQuiz
}

func (c *CertificateCondition) IsCompletedRequiredLessonEnabled() bool {
	return c.CompletedRequiredLesson
}

func MakeDefaultCertificateCondition() *CertificateCondition {
	return &CertificateCondition{
		CompletedAllQuizzes:           true,
		CompletedCourse:               true,
		CourseCompletionPercentage:    80,
		CompletedFinalQuiz:            true,
		FinalQuizCompletionPercentage: 80,
		CompletedRequiredLesson:       false,
		RequiredLessonUIDs:            []string{},
	}
}

func MakeDefaultMintCertNFTSettings() *MintCertificateNFTSettings {
	return &MintCertificateNFTSettings{
		Enabled:     false,
		GasFeePayer: "learner",
		Network:     BlockchainNetworkNEAR,
	}
}

type PreviewLessonContent struct {
	Title       string            `json:"title"`
	Content     string            `json:"content"`
	FileId      string            `json:"file_id"`
	Order       int               `json:"order"`
	ContentType LessonContentType `json:"content_type"`
}

func (c *Course) GetUserID() string {
	return c.UserID
}

func (c *Course) GetID() string {
	return c.ID
}

func (j CourseProps) Value() (driver.Value, error) {
	valueString, err := json.Marshal(j)
	return string(valueString), err
}

func (j *CourseProps) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j); err != nil {
		return err
	}
	return nil
}

func (c CertificateCondition) Value() (driver.Value, error) {
	valueString, err := json.Marshal(c)
	return string(valueString), err
}

func (c *CertificateCondition) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &c); err != nil {
		return err
	}
	return nil
}

func (c *Course) Sanitize() *SimpleCourse {
	return &SimpleCourse{
		Model:                    c.Model,
		OrgID:                    c.OrgID,
		Cuid:                     c.Cuid,
		Version:                  c.Version,
		Name:                     c.Name,
		Slug:                     c.Slug,
		Description:              c.Description,
		ShortDesc:                c.ShortDesc,
		LearnMethod:              c.LearnMethod,
		UserID:                   c.UserID,
		Enable:                   c.Enable,
		PriceSettings:            c.PriceSettings,
		SectionCount:             c.SectionCount,
		LessonCount:              c.LessonCount,
		ActiveLesson:             c.ActiveLesson,
		Status:                   c.Status,
		Medias:                   c.Medias,
		Docs:                     c.Docs,
		Thumbnail:                c.Thumbnail,
		Partners:                 c.Partners,
		Categories:               c.Categories,
		Levels:                   c.Levels,
		Props:                    c.Props,
		Published:                c.Published,
		RootRequest:              c.RootRequest,
		OrgRequest:               c.OrgRequest,
		LearningProgressOverview: c.LearningProgressOverview,
		Latest:                   c.Latest,
		Rating:                   c.Rating,
		LearnerCount:             c.LearnerCount,
		Org:                      c.Org,
		ActiveSection:            c.ActiveSection,
		VideoCount:               c.VideoCount,
		QuizCount:                c.QuizCount,

		StartDate:         c.StartDate,
		EndDate:           c.EndDate,
		MarkAsCompleted:   c.MarkAsCompleted,
		Accesses:          c.Accesses,
		PubDate:           c.PubDate,
		PubRootDate:       c.PubRootDate,
		PubRejectDate:     c.PubRejectDate,
		PubRootRejectDate: c.PubRootRejectDate,

		IsPaid:     c.IsPaid,
		IsEnrolled: c.IsEnrolled,
		IsWishlist: c.IsWishlist,
		Bookmark:   c.Bookmark,

		HasCertificate: c.HasCertificate,
		Owner:          c.Owner,
		Launchpad:      c.Launchpad,

		IsAIGenerated:    c.IsAIGenerated,
		AIGenerateStatus: c.AIGenerateStatus,
		AICourseID:       c.AICourseID,
		AICourse:         c.AICourse,
	}
}

func (c *Course) ToSimplePublishCourseStatus() *SimplePublishCourseStatus {
	return &SimplePublishCourseStatus{
		ID:                c.ID,
		OrgID:             c.OrgID,
		Name:              c.Name,
		Version:           c.Version,
		Latest:            c.Latest,
		Status:            c.Status,
		StartDate:         c.StartDate,
		EndDate:           c.EndDate,
		MarkAsCompleted:   c.MarkAsCompleted,
		PubDate:           c.PubDate,
		PubRootDate:       c.PubRootDate,
		PubRootRejectDate: c.PubRootRejectDate,
		PubRejectDate:     c.PubRejectDate,
		IsRoot:            false,
	}
}

func (c *Course) CanUpdateCourse(user *User) bool {
	return user.ID == c.UserID
}

func (c *Course) IsMintNFTEnabled() bool {
	return c.Props.MintCertNFTSettings.Enabled
}

func (c *Course) CertMintNFTNetwork() BlockchainNetwork {
	network := BlockchainNetworkNEAR
	if c.Props.MintCertNFTSettings != nil && c.Props.MintCertNFTSettings.Network != "" {
		network = c.Props.MintCertNFTSettings.Network
	}
	return network
}

func (c *Course) GetCertCondition() *CertificateCondition {
	return c.Props.CertificateCondition
}

type CourseQuery struct {
	OrgID           *string  `json:"org_id" form:"org_id"`
	ID              *string  `json:"id" form:"id"`
	IDNe            *string  `json:"id_ne" form:"id_ne"`
	Cuid            *string  `json:"cuid" form:"cuid"`
	CuidNe          *string  `json:"cuid_ne" form:"cuid_ne"`
	CuidIn          []string `json:"cuid_in" form:"cuid_in"`
	IDIn            []string `json:"id_in" form:"id_in"`
	Name            *string  `json:"name" form:"name"`
	Slug            *string  `json:"slug" form:"slug"`
	UserId          *string  `json:"user_id" form:"user_id"`
	Status          *string  `json:"status" form:"status"`
	IncludeDeleted  *bool
	Latest          *bool   `json:"latest" form:"latest"`
	Version         *int    `json:"version" form:"version"`
	Partner         *string `json:"partner"`
	PubDate         *int    `json:"pub_date"`
	PubRootDate     *int    `json:"pub_root_date"`
	IsPay           *bool   `json:"is_pay" form:"is_pay"`
	Enable          *bool   `json:"enable"`
	GteStartDate    *int    `json:"gte_start_date"`
	LteEndDate      *int    `json:"lte_end_date"`
	SectionCountGte *int    `json:"section_count_gte" form:"section_count_gte"`
	HasLaunchpad    *bool   `json:"has_launchpad" form:"has_launchpad"`

	AICourseID       *string   `json:"ai_course_id" form:"ai_course_id"`
	AICourseIDIn     []string  `json:"ai_course_id_in" form:"ai_course_id_in"`
	AIGenerateStatus *AIStatus `json:"ai_generate_status" form:"ai_generate_status"`

	BaseSearchQuery
}

func (query *CourseQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.IDNe != nil {
		qb = qb.Where("id <> ?", *query.IDNe)
	}

	if query.Cuid != nil {
		qb = qb.Where("cuid = ?", *query.Cuid)
	}

	if query.CuidNe != nil {
		qb = qb.Where("cuid <> ?", *query.CuidNe)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	if len(query.CuidIn) > 0 {
		qb = qb.Where("cuid IN (?)", query.CuidIn)
	}

	if query.Name != nil {
		qb = qb.Where("name = ?", *query.Name)
	}

	if query.Slug != nil {
		qb = qb.Where("slug = ?", *query.Slug)
	}

	if query.IsPay != nil {
		subQuery := db.Table(GetTblName(CoursePriceTbl)).
			Select("course_id, is_pay")

		qb = qb.Joins("LEFT JOIN (?) AS p ON id = p.course_id", subQuery).
			Where("p.is_pay = ?", *query.IsPay)
	}

	if query.UserId != nil {
		qb = qb.Where("user_id = ?", *query.UserId)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	if query.Latest != nil {
		qb = qb.Where("latest = ?", *query.Latest)
	}

	if query.Enable != nil {
		qb = qb.Where("enable = ?", *query.Enable)
	}

	if query.GteStartDate != nil {
		qb = qb.Where("start_date <= ? ", *query.GteStartDate)
	}

	if query.LteEndDate != nil {
		qb = qb.Where("end_date >= ? ", *query.LteEndDate)
	}

	if query.Version != nil {
		qb = qb.Where("version = ?", *query.Version)
	}

	if query.PubDate != nil {
		qb = qb.Where("pub_date = ?", *query.PubDate)
	}

	if query.PubRootDate != nil {
		qb = qb.Where("pub_root_date = ?", *query.PubRootDate)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, Course{}, nil)
	}

	if query.AICourseID != nil {
		qb = qb.Where("ai_course_id = ?", *query.AICourseID)
	}

	if len(query.AICourseIDIn) > 0 {
		qb = qb.Where("ai_course_id IN (?)", query.AICourseIDIn)
	}

	if query.AIGenerateStatus != nil {
		qb = qb.Where("ai_generate_status = ?", *query.AIGenerateStatus)
	}

	if query.SectionCountGte != nil {
		qb = qb.Where("section_count >= ?", *query.SectionCountGte)
	}

	if query.HasLaunchpad != nil {
		subQuery := db.Table(GetTblName(ClpCourseLaunchpadTbl)).
			Select("clp_launchpad_id AS launchpad_id, course_cuid")

		if *query.HasLaunchpad {
			qb = qb.Joins("INNER JOIN (?) AS l ON cuid = l.course_cuid", subQuery)
		} else {
			qb = qb.Joins("LEFT JOIN (?) AS l ON cuid = l.course_cuid", subQuery).
				Where("l.course_cuid IS NULL")
		}
	}

	return qb
}

func (query *CourseQuery) ApplyJoin(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("c.id = ?", *query.ID)
	}

	if query.OrgID != nil {
		qb = qb.Where("c.org_id = ?", *query.OrgID)
	}

	if query.Cuid != nil {
		qb = qb.Where("c.cuid = ?", *query.Cuid)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("c.id IN (?)", query.IDIn)
	}

	if query.Name != nil {
		qb = qb.Where("c.name = ?", *query.Name)
	}

	if query.Slug != nil {
		qb = qb.Where("c.slug = ?", *query.Slug)
	}

	if query.IsPay != nil {
		subQuery := db.Table(GetTblName(CoursePriceTbl)).
			Select("course_id, is_pay")

		qb = qb.Joins("LEFT JOIN (?) AS prices ON id = prices.course_id", subQuery).
			Where("prices.is_pay = ?", *query.IsPay)
	}

	if query.UserId != nil {
		qb = qb.Where("c.user_id = ?", *query.UserId)
	}

	if query.Status != nil {
		qb = qb.Where("c.status = ?", *query.Status)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("c.delete_at = 0")
	}

	if query.Latest != nil {
		qb = qb.Where("c.latest = ?", *query.Latest)
	}

	if query.Enable != nil {
		qb = qb.Where("c.enable = ?", *query.Enable)
	}

	if query.Version != nil {
		qb = qb.Where("c.version = ?", *query.Version)
	}

	if query.Partner != nil {
		qb = qb.Where("p.partner_id = ?", *query.Partner)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		Search.ApplySearch(qb, query, Course{}, util.NewString("c"))
	}

	if query.SectionCountGte != nil {
		qb = qb.Where("section_count >= ?", *query.SectionCountGte)
	}

	if query.HasLaunchpad != nil {
		subQuery := db.Table(GetTblName(ClpCourseLaunchpadTbl)).
			Select("clp_launchpad_id AS launchpad_id, course_cuid")

		if *query.HasLaunchpad {
			qb = qb.Joins("INNER JOIN (?) AS l ON c.cuid = l.course_cuid", subQuery)
		} else {
			qb = qb.Joins("LEFT JOIN (?) AS l ON c.cuid = l.course_cuid", subQuery).
				Where("l.course_cuid IS NULL")
		}
	}
	return qb
}

func (query *CourseQuery) ToWhereClauseWithAlias(alias string) (string, []interface{}, error) {
	var exprs []string
	var vals []interface{}

	if query.ID != nil {
		exprs = append(exprs, fmt.Sprintf("%s.id = ?", alias))
		vals = append(vals, query.ID)
	}

	if query.OrgID != nil {
		exprs = append(exprs, fmt.Sprintf("%s.org_id IN (?)", alias))
		vals = append(vals, query.OrgID)
	}

	if query.IDNe != nil {
		exprs = append(exprs, fmt.Sprintf("%s.id <> ?", alias))
		vals = append(vals, query.IDNe)
	}

	if query.Cuid != nil {
		exprs = append(exprs, fmt.Sprintf("%s.cuid = ?", alias))
		vals = append(vals, query.Cuid)
	}

	if query.CuidNe != nil {
		exprs = append(exprs, fmt.Sprintf("%s.cuid <> ?", alias))
		vals = append(vals, query.CuidNe)
	}

	if len(query.IDIn) > 0 {
		exprs = append(exprs, fmt.Sprintf("%s.id IN (?)", alias))
		vals = append(vals, query.IDIn)
	}

	if len(query.CuidIn) > 0 {
		exprs = append(exprs, fmt.Sprintf("%s.cuid IN (?)", alias))
		vals = append(vals, query.CuidIn)
	}

	if query.Name != nil {
		exprs = append(exprs, fmt.Sprintf("%s.name = ?", alias))
		vals = append(vals, query.Name)
	}

	if query.Slug != nil {
		exprs = append(exprs, fmt.Sprintf("%s.slug = ?", alias))
		vals = append(vals, query.Slug)
	}

	if query.UserId != nil {
		exprs = append(exprs, fmt.Sprintf("%s.user_id = ?", alias))
		vals = append(vals, query.UserId)
	}

	if query.Status != nil {
		exprs = append(exprs, fmt.Sprintf("%s.status = ?", alias))
		vals = append(vals, query.Status)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		exprs = append(exprs, fmt.Sprintf("%s.delete_at = 0", alias))
	}

	if query.Latest != nil {
		exprs = append(exprs, fmt.Sprintf("%s.latest = ?", alias))
		vals = append(vals, query.Latest)
	}

	if query.Enable != nil {
		exprs = append(exprs, fmt.Sprintf("%s.enable = ?", alias))
		vals = append(vals, query.Enable)
	}

	if query.GteStartDate != nil {
		exprs = append(exprs, fmt.Sprintf("%s.start_date <= ?", alias))
		vals = append(vals, query.GteStartDate)
	}

	if query.LteEndDate != nil {
		exprs = append(exprs, fmt.Sprintf("%s.end_date >= ?", alias))
		vals = append(vals, query.LteEndDate)
	}

	if query.Version != nil {
		exprs = append(exprs, fmt.Sprintf("%s.version = ?", alias))
		vals = append(vals, query.Version)
	}

	if query.PubDate != nil {
		exprs = append(exprs, fmt.Sprintf("%s.pub_date = ?", alias))
		vals = append(vals, query.PubDate)
	}

	if query.PubRootDate != nil {
		exprs = append(exprs, fmt.Sprintf("%s.pub_root_date = ?", alias))
		vals = append(vals, query.PubRootDate)
	}

	if query.SearchTerm != nil && query.SearchCategories != nil {
		categories := strings.Split(*query.GetSearchCategories(), ",")
		validCategories, err := VerifySearchCategory(Course{}, []string{}, categories)
		if err != nil {
			return "", nil, err
		}

		_, err = VerifySearchTerm(*query.GetSearchTerm())
		if err != nil {
			return "", nil, err
		}

		searchTerm := strings.TrimSpace(*query.GetSearchTerm())
		if searchTerm == "" {
			return "", nil, err
		}

		baseTerm := extractBaseTerm(searchTerm)

		for _, category := range validCategories {
			columnName := fmt.Sprintf("%s.%s", alias, category)

			exprs = append(exprs,
				fmt.Sprintf("LOWER(%s) LIKE LOWER(?)", columnName))
			vals = append(vals,
				fmt.Sprintf("%%%s%%", searchTerm))

			if baseTerm != searchTerm {
				exprs = append(exprs,
					fmt.Sprintf("LOWER(%s) LIKE LOWER(?)", columnName))
				vals = append(vals,
					fmt.Sprintf("%%%s%%", baseTerm))
			}
		}
	}

	if query.AICourseID != nil {
		exprs = append(exprs, fmt.Sprintf("%s.ai_course_id = ?", alias))
		vals = append(vals, query.AICourseID)
	}

	if len(query.AICourseIDIn) > 0 {
		exprs = append(exprs, fmt.Sprintf("%s.ai_course_id IN (?)", alias))
		vals = append(vals, query.AICourseIDIn)
	}

	if query.AIGenerateStatus != nil {
		exprs = append(exprs, fmt.Sprintf("%s.ai_generate_status = ?", alias))
		vals = append(vals, query.AIGenerateStatus)
	}

	return strings.Join(exprs, " AND "), vals, nil
}

type CoursePreload struct {
	Medias               bool
	Docs                 bool
	Partners             bool
	Categories           bool
	Levels               bool
	Reviewing            bool
	Published            bool
	FormRelations        bool
	User                 bool
	Owner                bool
	Org                  bool
	TotalUserEnrollments bool
	AICourse             bool
	CertificateTemplate  bool
	ClpLaunchpad         bool
}

func getExPreload(preloads []string) (*CoursePreload, []string) {
	shouldPreloadMedias := false
	shouldPreloadDocs := false
	shouldPreloadPartner := false
	shouldPreloadCategories := false
	shouldPreloadLevels := false
	shouldPreloadReviewing := false
	shouldPreloadPublished := false
	shouldPreloadUser := false
	shouldPreloadFormRelations := false
	shouldPreloadOwner := false
	shouldPreloadTotalUserEnrollments := false
	shouldPreloadAICourse := false
	shouldPreloadCertificateTemplate := false
	shouldPreloadOrg := false
	shouldPreloadClpLaunchpad := false

	newPreloads := make([]string, len(preloads))
	copy(newPreloads, preloads)

	if lo.Contains(newPreloads, MediasField) {
		shouldPreloadMedias = true
		newPreloads = util.RemoveElement(newPreloads, MediasField)
	}

	if lo.Contains(newPreloads, DocsField) {
		shouldPreloadDocs = true
		newPreloads = util.RemoveElement(newPreloads, DocsField)
	}

	if lo.Contains(newPreloads, PartnersField) {
		shouldPreloadPartner = true
		newPreloads = util.RemoveElement(newPreloads, PartnersField)
	}

	if lo.Contains(newPreloads, CategoriesField) {
		shouldPreloadCategories = true
		newPreloads = util.RemoveElement(newPreloads, CategoriesField)
	}

	if lo.Contains(newPreloads, LevelsField) {
		shouldPreloadLevels = true
		newPreloads = util.RemoveElement(newPreloads, LevelsField)
	}

	if lo.Contains(newPreloads, ReviewingField) {
		shouldPreloadReviewing = true
		newPreloads = util.RemoveElement(newPreloads, ReviewingField)
	}

	if lo.Contains(newPreloads, PublishedField) {
		shouldPreloadPublished = true
		newPreloads = util.RemoveElement(newPreloads, PublishedField)
	}

	if lo.Contains(newPreloads, FormRelationsField) {
		shouldPreloadFormRelations = true
		newPreloads = util.RemoveElement(newPreloads, FormRelationsField)
	}

	if lo.Contains(newPreloads, UserField) {
		shouldPreloadUser = true
		newPreloads = util.RemoveElement(newPreloads, UserField)
	}

	if lo.Contains(newPreloads, OwnerField) {
		shouldPreloadOwner = true
		newPreloads = util.RemoveElement(newPreloads, OwnerField)
	}

	if lo.Contains(newPreloads, TotalUserEnrollmentsField) {
		shouldPreloadTotalUserEnrollments = true
		newPreloads = util.RemoveElement(newPreloads, TotalUserEnrollmentsField)
	}

	if lo.Contains(newPreloads, CertificateTemplateField) {
		shouldPreloadCertificateTemplate = true
		newPreloads = util.RemoveElement(newPreloads, CertificateTemplateField)
	}

	if lo.Contains(newPreloads, AICourseField) {
		shouldPreloadAICourse = true
		newPreloads = util.RemoveElement(newPreloads, AICourseField)
	}

	if lo.Contains(newPreloads, OrgField) {
		shouldPreloadOrg = true
		newPreloads = util.RemoveElement(newPreloads, OrgField)
	}

	if lo.Contains(newPreloads, ClpLaunchpadField) {
		shouldPreloadClpLaunchpad = true
		newPreloads = util.RemoveElement(newPreloads, ClpLaunchpadField)
	}

	return &CoursePreload{
		Medias:               shouldPreloadMedias,
		Docs:                 shouldPreloadDocs,
		Partners:             shouldPreloadPartner,
		Categories:           shouldPreloadCategories,
		Levels:               shouldPreloadLevels,
		Reviewing:            shouldPreloadReviewing,
		Published:            shouldPreloadPublished,
		FormRelations:        shouldPreloadFormRelations,
		User:                 shouldPreloadUser,
		Owner:                shouldPreloadOwner,
		Org:                  shouldPreloadOrg,
		TotalUserEnrollments: shouldPreloadTotalUserEnrollments,
		AICourse:             shouldPreloadAICourse,
		CertificateTemplate:  shouldPreloadCertificateTemplate,
		ClpLaunchpad:         shouldPreloadClpLaunchpad,
	}, newPreloads
}

func preloadFormRelations(courses []*Course) error {
	if len(courses) == 0 {
		return nil
	}
	courseIDs := lo.Map(courses, func(course *Course, _ int) string {
		return course.ID
	})
	courseIDs = lo.Uniq(courseIDs)
	formRelations, err := Repository.FormRelation.FindMany(&FormRelationQuery{
		RelatedEntityType: util.NewT(CourseModelName),
		RelatedEntityIDIn: courseIDs,
	}, nil)
	if err != nil {
		return err
	}

	formRelationsByCourseIDs := map[string][]*FormRelation{}
	for _, formRelation := range formRelations {
		if _, found := formRelationsByCourseIDs[formRelation.RelatedEntityID]; found {
			formRelationsByCourseIDs[formRelation.RelatedEntityID] = append(formRelationsByCourseIDs[formRelation.RelatedEntityID], formRelation)
		} else {
			formRelationsByCourseIDs[formRelation.RelatedEntityID] = []*FormRelation{formRelation}
		}
	}

	for _, course := range courses {
		if relations, found := formRelationsByCourseIDs[course.ID]; found {
			course.FormRelations = relations
		}
	}

	return nil
}

func (r *CourseRepository) preloadLearnerCount(courses []*Course) error {
	courseCUIDs := lo.Map(courses, func(course *Course, index int) string {
		return course.Cuid
	})
	query := &CourseEnrollmentQuery{
		CourseCuidIn: lo.Uniq(courseCUIDs),
		Blocked:      util.NewBool(false),
	}

	learnerCountsByCourseCUIDs, err := Repository.CourseEnrollment(r.ctx).CountByCourseCUIDs(query)
	if err != nil {
		return err
	}

	for _, course := range courses {
		if learnerCount, found := learnerCountsByCourseCUIDs[course.Cuid]; found {
			course.LearnerCount = learnerCount
		} else {
			course.LearnerCount = 0
		}
	}
	return nil
}

func preloadReviewingCourse(courses []*Course) error {
	preIds := lo.Map(courses, func(item *Course, _ int) string {
		return item.Props.ApprovalUid
	})
	preIds = lo.Uniq(preIds)
	if len(preIds) > 0 {
		approvals, approvalErr := Repository.Approval.FindMany(&ApprovalQuery{
			RequestUidIn: preIds,
			StatusIn:     []ApprovalStatus{ApprovalStatusNew, ApprovalStatusPending},
		}, nil)
		if approvalErr != nil {
			return errors.New("Find approvals for " + util.Struct2Json(preIds) + " Err: " + approvalErr.Error())
		}
		if len(approvals) > 0 {
			approvalsGroup := lo.Reduce(approvals, func(agg map[string]*Approval, approval *Approval, _ int) map[string]*Approval {
				return map[string]*Approval{
					approval.RequestUid: approval,
				}
			}, map[string]*Approval{})

			lo.ForEach(courses, func(course *Course, _ int) {
				if course.Props.ApprovalUid != "" && approvalsGroup[course.Props.ApprovalUid] != nil {
					course.OrgRequest = approvalsGroup[course.Props.ApprovalUid]
				}
			})
		}
	}

	return nil
}

func (r *CourseRepository) preloadPublishedCourse(courses []*Course) error {
	if len(courses) == 0 {
		return nil
	}

	// get all course publishing
	publishCourse, pbErr := Repository.PublishCourse(r.ctx).FindMany(&PublishCourseQuery{
		CourseCuidIn: lo.Uniq(lo.Map(courses, func(item *Course, _ int) string {
			return item.Cuid
		})),
		IsPub: util.NewBool(true),
	}, nil)
	if pbErr != nil {
		return pbErr
	}

	if len(publishCourse) > 0 {
		var pubCourses []*Course
		ids := lo.Map(publishCourse, func(item *PublishCourse, _ int) string {
			return item.CourseID
		})
		ids = lo.Uniq(ids)
		if css, preErr := Repository.Course(r.ctx).FindMany(&CourseQuery{IDIn: ids}, nil); preErr != nil {
			return errors.New("find published courses failed: " + preErr.Error())
		} else {
			pubCourses = css
		}
		for _, course := range courses {
			pubs := lo.Filter(publishCourse, func(item *PublishCourse, _ int) bool {
				return item.CourseCuid == course.Cuid
			})
			spcs := []*SimplePublishCourseStatus{}
			if len(pubs) > 0 {
				for _, p := range pubs {
					c, ok := lo.Find(pubCourses, func(item *Course) bool {
						return item.ID == p.CourseID
					})
					if ok {
						scs := c.ToSimplePublishCourseStatus()
						scs.IsRoot = p.IsRoot
						spcs = append(spcs, scs)
					}
				}

			}
			course.Published = spcs
		}
	}
	return nil
}

func preloadCourseCategories(courses []*Course) error {
	cuids := lo.Map(courses, func(course *Course, _ int) string {
		return course.Cuid
	})
	if categoriesByCoursesIDs, mErr := Repository.CategoryRelation.GetCategoriesByEntities(CourseModelName, cuids, CategoriesField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(courses, func(course *Course, _ int) {
			course.Categories = categoriesByCoursesIDs[course.Cuid]
		})
	}
	return nil
}

func preloadCourseLevels(courses []*Course) error {
	cuids := lo.Map(courses, func(course *Course, _ int) string {
		return course.Cuid
	})
	if categoriesByCoursesIDs, mErr := Repository.CategoryRelation.GetCategoriesByEntities(CourseModelName, cuids, LevelsField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(courses, func(course *Course, _ int) {
			course.Levels = categoriesByCoursesIDs[course.Cuid]
		})
	}
	return nil
}

func preloadCoursePartners(courses []*Course) error {
	cuids := lo.Map(courses, func(course *Course, _ int) string {
		return course.Cuid
	})
	if partners, pErr := Repository.CoursePartner.FindMany(
		&CoursePartnerQuery{CourseIDIn: cuids},
		&FindManyOptions{Preloads: []string{PartnerField}}); pErr != nil {
		if !errors.Is(pErr, gorm.ErrRecordNotFound) {
			return pErr
		}
	} else {
		for _, course := range courses {
			course.Partners = []*SimplePartner{}
			for _, partner := range partners {
				if partner.CourseID == course.Cuid {
					course.Partners = append(course.Partners, partner.ToSimplePartner())
				}
			}
		}
	}
	return nil
}

func preloadCourseOwner(courses []*Course) error {
	userIDs := lo.Map(courses, func(course *Course, _ int) string {
		return course.UserID
	})
	if owners, pErr := Repository.User.FindMany(
		&UserQuery{IDIn: &userIDs},
		&FindManyOptions{}); pErr != nil {
		if !errors.Is(pErr, gorm.ErrRecordNotFound) {
			return pErr
		}
	} else {
		ownerMap := make(map[string]*User)
		for _, owner := range owners {
			ownerMap[owner.ID] = owner
		}

		for _, course := range courses {
			if owner, exists := ownerMap[course.UserID]; exists {
				course.Owner = owner.ToSimpleProfile()
			}
		}
	}
	return nil
}

func preloadMedias(courses []*Course) error {
	courseIDs := lo.Map(courses, func(course *Course, _ int) string {
		return course.ID
	})
	if mediasByCoursesIDs, mErr := Repository.FileRelation.GetFilesByEntities(CourseModelName, courseIDs, MediasField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(courses, func(course *Course, _ int) {
			course.Medias = mediasByCoursesIDs[course.ID]
		})
	}
	return nil
}

func preloadDocs(courses []*Course) error {
	courseIDs := lo.Map(courses, func(course *Course, _ int) string {
		return course.ID
	})
	if docsByCourseIDs, mErr := Repository.FileRelation.GetFilesByEntities(CourseModelName, courseIDs, DocsField); mErr != nil {
		return mErr
	} else {
		lo.ForEach(courses, func(course *Course, _ int) {
			course.Docs = docsByCourseIDs[course.ID]
		})
	}
	return nil
}

func preloadAICourse(courses []*Course) error {
	courseCuids := lo.Map(courses, func(course *Course, _ int) string {
		return course.Cuid
	})
	if aiCourses, mErr := Repository.AICourse.FindMany(&AICourseQuery{
		CourseCuidIn: courseCuids,
	}, &FindManyOptions{}); mErr != nil {
		return mErr
	} else {
		aiCourseMap := make(map[string]*AICourse)
		lo.ForEach(aiCourses, func(aiCourse *AICourse, _ int) {
			aiCourseMap[aiCourse.CourseCuid] = aiCourse
		})

		for _, course := range courses {
			if aiCourse, exists := aiCourseMap[course.Cuid]; exists {
				course.AICourse = aiCourse.ToSimple()
			}
		}
	}
	return nil
}

func preloadPriceSettings(courses []*Course) error {
	if len(courses) == 0 {
		return nil
	}

	courseIDs := lo.Map(courses, func(course *Course, _ int) string {
		return course.ID
	})
	var prices []*CoursePrice
	var err error
	prices, err = Repository.CoursePrice.FindMany(&CoursePriceQuery{
		CourseIDIn: courseIDs,
	}, nil)

	if err != nil {
		return err
	}

	pricesByCourseIDs := map[string]*CoursePrice{}
	lo.ForEach(prices, func(price *CoursePrice, _ int) {
		pricesByCourseIDs[price.CourseID] = price
	})

	lo.ForEach(courses, func(course *Course, _ int) {
		course.PriceSettings = pricesByCourseIDs[course.ID]
	})
	return nil
}

func (r *CourseRepository) preloadClpLaunchpad(courses []*Course) error {
	if len(courses) == 0 {
		return nil
	}

	courseCUIDs := lo.Map(courses, func(course *Course, _ int) string {
		return course.Cuid
	})
	if clpCourseLaunchpads, mErr := Repository.ClpCourseLaunchpad(r.ctx).FindMany(&ClpCourseLaunchpadQuery{
		CourseCuidIn: courseCUIDs,
	}, &FindManyOptions{Preloads: []string{ClpLaunchpadField}}); mErr != nil {
		return mErr
	} else {
		clpLaunchpadMap := make(map[string]*ClpLaunchpad)
		lo.ForEach(clpCourseLaunchpads, func(courseLaunchpad *ClpCourseLaunchpad, _ int) {
			clpLaunchpadMap[courseLaunchpad.CourseCuid] = courseLaunchpad.ClpLaunchpad
		})

		for _, course := range courses {
			if clpLaunchpad, exists := clpLaunchpadMap[course.Cuid]; exists {
				course.Launchpad = clpLaunchpad
			}
		}
	}

	return nil
}

// Create inserts an org to database, transaction is optional
func (r *CourseRepository) Create(c *Course, trans *gorm.DB) error {
	omitFields := []string{PriceSettingsField}
	if err := createWithOmitFields(CourseTbl, c, omitFields, trans); err != nil {
		return err
	}
	if c.PriceSettings != nil {
		c.PriceSettings.CourseID = c.ID
		if pErr := Repository.CoursePrice.Create(c.PriceSettings, trans); pErr != nil {
			return pErr
		}
	} else {
		// TODO get default fiat and crypto from models.SystemConfigs
		c.PriceSettings = &CoursePrice{
			CourseID: c.ID,

			IsPay: false,

			FiatCurrency:      DefaultFiatCurrency,
			FiatPrice:         decimal.Zero,
			FiatDiscountPrice: decimal.Zero,

			CryptoPaymentEnabled: false,
			CryptoCurrency:       CryptoCurrencyUSDT,
			CryptoPrice:          decimal.Zero,
			CryptoDiscountPrice:  decimal.Zero,
		}
		if pErr := Repository.CoursePrice.Create(c.PriceSettings, trans); pErr != nil {
			return pErr
		}
	}
	if fErr := Repository.FileRelation.AddFiles(CourseModelName, c.ID, MediasField, c.Medias); fErr != nil {
		return fErr
	}
	if fErr := Repository.FileRelation.AddFiles(CourseModelName, c.ID, DocsField, c.Docs); fErr != nil {
		return fErr
	}
	if cErr := Repository.CategoryRelation.AddCategories(CourseModelName, c.Cuid, CategoriesField, c.Categories, nil); cErr != nil {
		return cErr
	}
	if cErr := Repository.CategoryRelation.AddCategories(CourseModelName, c.Cuid, LevelsField, c.Levels, nil); cErr != nil {
		return cErr
	}
	if cErr := Cache.Course.Flush(); cErr != nil {
		log.Errorf("CourseRepository.Create::Clear cache courses error: %v", cErr)
	}

	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("CourseRepository.Create::Clear cache lesson contents error: %v", cErr)
	}
	return nil
}

func (r *CourseRepository) CreateMany(entities []*Course, trans *gorm.DB) error {
	return createMany(CourseTbl, entities, trans)
}

// Update updates a course by ID in database, transaction is optional
func (r *CourseRepository) Update(c *Course, trans *gorm.DB) error {
	omitFields := []string{PriceSettingsField}
	if err := updateWithOmitFields(CourseTbl, c, omitFields, trans); err != nil {
		return err
	}

	if c.PriceSettings != nil {
		c.PriceSettings.CourseID = c.ID
		if pErr := Repository.CoursePrice.Upsert(c.PriceSettings, trans); pErr != nil {
			return pErr
		}
	}

	if fErr := Repository.FileRelation.AddFiles(CourseModelName, c.ID, MediasField, c.Medias); fErr != nil {
		return fErr
	}

	if fErr := Repository.FileRelation.AddFiles(CourseModelName, c.ID, DocsField, c.Docs); fErr != nil {
		return fErr
	}

	if cErr := Repository.CategoryRelation.AddCategories(CourseModelName, c.Cuid, CategoriesField, c.Categories, nil); cErr != nil {
		return cErr
	}

	if cErr := Repository.CategoryRelation.AddCategories(CourseModelName, c.Cuid, LevelsField, c.Levels, nil); cErr != nil {
		return cErr
	}

	if cErr := Cache.Course.Flush(); cErr != nil {
		log.Errorf("CourseRepository.Update::Clear cache courses error: %v", cErr)
	}

	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("CourseRepository.Update::Clear cache lesson contents error: %v", cErr)
	}
	return nil
}

// UpdateBasic updates a course by ID in database, transaction is optional
func (r *CourseRepository) UpdateBasic(c *Course, trans *gorm.DB) error {
	if err := update(CourseTbl, c, trans); err != nil {
		return err
	}

	if cErr := Cache.Course.Flush(); cErr != nil {
		log.Errorf("CourseRepository.UpdateBasic::Clear cache courses error: %v", cErr)
	}
	return nil
}

// FindByID finds a v by ID with given find options, transaction is optional
func (r *CourseRepository) FindByID(id string, options *FindOneOptions) (*Course, error) {
	if options == nil {
		options = &FindOneOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := getExPreload(preloads)
	options.Preloads = newPreloads
	course, err := findByID[Course](CourseTbl, id, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Medias {
		if files, mErr := Repository.FileRelation.GetFiles(CourseModelName, course.ID, MediasField); mErr != nil {
			return course, mErr
		} else {
			course.Medias = files
		}
	}

	if exPreload.Docs {
		if files, mErr := Repository.FileRelation.GetFiles(CourseModelName, course.ID, DocsField); mErr != nil {
			return course, mErr
		} else {
			course.Docs = files
		}
	}

	if exPreload.Partners {
		if partners, _, pErr := Repository.CoursePartner.FindPage(
			&CoursePartnerQuery{CourseID: util.NewString(course.Cuid)},
			&FindPageOptions{Preloads: []string{PartnerField}, Page: util.PageMin, PerPage: util.PerPageMax}); pErr != nil {
			if !errors.Is(pErr, gorm.ErrRecordNotFound) {
				return course, pErr
			}
		} else {
			course.Partners = lo.Map(partners, func(item *CoursePartner, _ int) *SimplePartner {
				return item.ToSimplePartner()
			})
		}
	}

	if exPreload.Categories {
		if categories, mErr := Repository.CategoryRelation.GetCategories(CourseModelName, course.Cuid, CategoriesField); mErr != nil {
			return course, mErr
		} else {
			course.Categories = categories
		}
	}

	if exPreload.Levels {
		if levels, mErr := Repository.CategoryRelation.GetCategories(CourseModelName, course.Cuid, LevelsField); mErr != nil {
			return course, mErr
		} else {
			course.Levels = levels
		}
	}

	if exPreload.Reviewing {
		if course.Props.ApprovalUid != "" {
			approvals, approvalErr := Repository.Approval.FindMany(&ApprovalQuery{
				RequestUid: util.NewString(course.Props.ApprovalUid),
			}, nil)
			if approvalErr != nil {
				return course, errors.New("Repository.Approval.FindByID: " + course.Props.ApprovalUid + " Err: " + approvalErr.Error())
			}
			root, _ := lo.Find(approvals, func(item *Approval) bool {
				return item.Type == ApproveTypePublishRoot
			})
			base, _ := lo.Find(approvals, func(item *Approval) bool {
				return item.Type == ApproveTypePublishOrg
			})
			course.RootRequest = root
			course.OrgRequest = base
		}
	}

	if exPreload.Published {
		// get all course publishing
		publishCourses, pbErr := Repository.PublishCourse(r.ctx).FindMany(&PublishCourseQuery{
			CourseCuid: util.NewString(course.Cuid),
			IsPub:      util.NewBool(true),
		}, nil)
		if pbErr != nil {
			return course, pbErr
		}
		course.Published = []*SimplePublishCourseStatus{}
		if len(publishCourses) > 0 {
			for _, p := range publishCourses {
				preQuery := CourseQuery{
					ID: util.NewString(p.CourseID),
				}
				if published, preErr := r.FindOne(&preQuery, nil); preErr != nil {
					return course, errors.New("find previous course failed: " + util.Struct2Json(preQuery) + " Err: " + preErr.Error())
				} else {
					pp := published.ToSimplePublishCourseStatus()
					pp.IsRoot = p.IsRoot
					course.Published = append(course.Published, pp)
				}
			}
		}
	}

	if course.ThumbnailID != "" {
		if thumb, tErr := Repository.File.FindByID(course.ThumbnailID, nil); tErr != nil {
			if !errors.Is(tErr, gorm.ErrRecordNotFound) {
				return nil, errors.New("find thumbnail failed: " + tErr.Error())
			}
		} else {
			course.Thumbnail = thumb
		}
	}

	if exPreload.FormRelations {
		if preErr := preloadFormRelations([]*Course{course}); preErr != nil {
			return nil, fmt.Errorf("preload form relations error: %v", preErr)
		}
	}

	if exPreload.Owner {
		if owner, preErr := Repository.User.FindByID(course.UserID); preErr != nil {
			return nil, fmt.Errorf("preload form relations error: %v", preErr)
		} else {
			course.Owner = owner.ToSimpleProfile()
		}
	}

	if err = r.preloadLearnerCount([]*Course{course}); err != nil {
		return nil, err
	}

	if err = preloadPriceSettings([]*Course{course}); err != nil {
		return nil, err
	}

	if exPreload.AICourse {
		if course.AICourseID != "" {
			aiCourse, preErr := Repository.AICourse.FindByID(course.AICourseID, &FindOneOptions{})
			if preErr != nil {
				return nil, fmt.Errorf("preload ai course error: %v", preErr)
			}
			if len(aiCourse.GeneratedThumbnailIDs) > 0 {
				files, preErr := Repository.File.FindMany(&FileQuery{
					IDIn: aiCourse.GeneratedThumbnailIDs,
				}, &FindManyOptions{})
				if preErr != nil {
					return nil, fmt.Errorf("preload ai course error: %v", preErr)
				} else {
					aiCourse.ThumbnailGenerated = files
				}
			}
			if aiCourse.MaterialID != "" {
				file, preErr := Repository.File.FindByID(aiCourse.MaterialID, &FindOneOptions{})
				if preErr != nil {
					return nil, fmt.Errorf("preload ai course error: %v", preErr)
				} else {
					aiCourse.Material = file
				}
			}
			course.AICourse = aiCourse.ToSimple()
		}
	}

	if exPreload.ClpLaunchpad {
		if preErr := r.preloadClpLaunchpad([]*Course{course}); preErr != nil {
			return nil, fmt.Errorf("preload launchpad error: %v", preErr)
		}
	}

	return course, err
}

// FindOne finds one course with given find queries and options, transaction is optional
func (r *CourseRepository) FindOne(query *CourseQuery, options *FindOneOptions) (*Course, error) {
	if options == nil {
		options = &FindOneOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := getExPreload(preloads)
	options.Preloads = newPreloads

	course, err := findOne[Course](CourseTbl, query, options)
	if err != nil {
		return nil, err
	}

	if exPreload.Medias {
		if files, mErr := Repository.FileRelation.GetFiles(CourseModelName, course.ID, MediasField); mErr != nil {
			return course, mErr
		} else {
			course.Medias = files
		}
	}

	if exPreload.Docs {
		if files, mErr := Repository.FileRelation.GetFiles(CourseModelName, course.ID, DocsField); mErr != nil {
			return course, mErr
		} else {
			course.Docs = files
		}
	}

	if exPreload.Partners {
		if partners, _, pErr := Repository.CoursePartner.FindPage(
			&CoursePartnerQuery{CourseID: util.NewString(course.Cuid)},
			&FindPageOptions{Preloads: []string{PartnerField}, Page: util.PageMin, PerPage: util.PerPageMax}); pErr != nil {
			if !errors.Is(pErr, gorm.ErrRecordNotFound) {
				return course, pErr
			}
		} else {
			course.Partners = lo.Map(partners, func(item *CoursePartner, _ int) *SimplePartner {
				return item.ToSimplePartner()
			})
		}
	}

	if exPreload.Categories {
		if categories, mErr := Repository.CategoryRelation.GetCategories(CourseModelName, course.Cuid, CategoriesField); mErr != nil {
			return course, mErr
		} else {
			course.Categories = categories
		}
	}

	if exPreload.Levels {
		if levels, mErr := Repository.CategoryRelation.GetCategories(CourseModelName, course.Cuid, LevelsField); mErr != nil {
			return course, mErr
		} else {
			course.Levels = levels
		}
	}

	if exPreload.Reviewing {
		if course.Props.ApprovalUid != "" {
			approvals, approvalErr := Repository.Approval.FindMany(&ApprovalQuery{
				RequestUid: util.NewString(course.Props.ApprovalUid),
			}, nil)
			if approvalErr != nil {
				return course, errors.New("Repository.Approval.FindByID: " + course.Props.ApprovalUid + " Err: " + approvalErr.Error())
			}
			root, _ := lo.Find(approvals, func(item *Approval) bool {
				return item.Type == ApproveTypePublishRoot
			})
			base, _ := lo.Find(approvals, func(item *Approval) bool {
				return item.Type == ApproveTypePublishOrg
			})
			course.RootRequest = root
			course.OrgRequest = base
		}
	}

	if exPreload.Published {
		// get all course publishing
		publishs, pbErr := Repository.PublishCourse(r.ctx).FindMany(&PublishCourseQuery{
			CourseCuid: util.NewString(course.Cuid),
			IsPub:      util.NewBool(true),
		}, nil)
		if pbErr != nil {
			return course, pbErr
		}
		course.Published = []*SimplePublishCourseStatus{}
		if len(publishs) > 0 {
			for _, p := range publishs {
				preQuery := CourseQuery{
					ID: util.NewString(p.CourseID),
				}
				if published, preErr := r.FindOne(&preQuery, nil); preErr != nil {
					return course, errors.New("Find Publish course: " + p.OrgSchema + "---" + util.Struct2Json(preQuery) + " Err: " + preErr.Error())
				} else {
					pp := published.ToSimplePublishCourseStatus()
					pp.IsRoot = p.IsRoot
					course.Published = append(course.Published, pp)
				}
			}
		}
	}

	if course.ThumbnailID != "" {
		if thumb, tErr := Repository.File.FindByID(course.ThumbnailID, nil); tErr != nil {
			if !errors.Is(tErr, gorm.ErrRecordNotFound) {
				return nil, errors.New("find thumbnail failed: " + tErr.Error())
			}
		} else {
			course.Thumbnail = thumb
		}
	}

	if exPreload.FormRelations {
		if preErr := preloadFormRelations([]*Course{course}); preErr != nil {
			return nil, fmt.Errorf("preload form relations error: %v", preErr)
		}
	}

	if exPreload.Owner {
		if owner, preErr := Repository.User.FindByID(course.UserID); preErr != nil {
			return nil, fmt.Errorf("preload form relations error: %v", preErr)
		} else {
			course.Owner = owner.ToSimpleProfile()
		}
	}

	if err = r.preloadLearnerCount([]*Course{course}); err != nil {
		return nil, err
	}

	if err = preloadPriceSettings([]*Course{course}); err != nil {
		return nil, err
	}

	if exPreload.AICourse {
		if course.AICourseID != "" {
			aiCourse, preErr := Repository.AICourse.FindByID(course.AICourseID, &FindOneOptions{})
			if preErr != nil {
				return nil, fmt.Errorf("preload ai course error: %v", preErr)
			}
			if len(aiCourse.GeneratedThumbnailIDs) > 0 {
				files, preErr := Repository.File.FindMany(&FileQuery{
					IDIn: aiCourse.GeneratedThumbnailIDs,
				}, &FindManyOptions{})
				if preErr != nil {
					return nil, fmt.Errorf("preload ai course error: %v", preErr)
				} else {
					aiCourse.ThumbnailGenerated = files
				}
			}
			if aiCourse.MaterialID != "" {
				file, preErr := Repository.File.FindByID(aiCourse.MaterialID, &FindOneOptions{})
				if preErr != nil {
					return nil, fmt.Errorf("preload ai course error: %v", preErr)
				} else {
					aiCourse.Material = file
				}
			}
			course.AICourse = aiCourse.ToSimple()
		}
	}

	if exPreload.CertificateTemplate {
		templateQuery := HtmlTemplateQuery{
			Enable:         util.NewBool(true),
			IncludeDeleted: util.NewBool(false),
			CourseCuid:     &course.Cuid,
			Type:           util.NewString(string(CertificateLayer)),
		}
		template, err := Repository.HtmlTemplate.FindOne(&templateQuery, &FindOneOptions{})
		if err != nil && !IsRecordNotFound(err) {
			return nil, fmt.Errorf("preload certificate template failed: %v", err)
		}
		course.HtmlTemplate = template
	}

	if exPreload.ClpLaunchpad {
		if preErr := r.preloadClpLaunchpad([]*Course{course}); preErr != nil {
			return nil, fmt.Errorf("preload launchpad error: %v", preErr)
		}
	}

	return course, err
}

// FindMany finds courses by query conditions with give find options
func (r *CourseRepository) FindMany(query *CourseQuery, options *FindManyOptions) ([]*Course, error) {
	if options == nil {
		options = &FindManyOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := getExPreload(preloads)
	options.Preloads = newPreloads

	courses, err := findMany[Course](CourseTbl, query, options)
	if err != nil {
		return nil, err
	}

	if len(courses) == 0 {
		return courses, nil
	}

	var thumbIDs []string
	for _, course := range courses {
		if course.ThumbnailID != "" {
			thumbIDs = append(thumbIDs, course.ThumbnailID)
		}
	}
	if len(thumbIDs) > 0 {
		if thumbs, tErr := Repository.File.FindMany(&FileQuery{IDIn: thumbIDs}, nil); tErr != nil {
			return nil, tErr
		} else {
			lo.ForEach(courses, func(course *Course, _ int) {
				t, ok := lo.Find(thumbs, func(item *File) bool {
					return item.ID == course.ThumbnailID
				})
				if ok {
					course.Thumbnail = t
				}
			})
		}
	}

	if exPreload.Medias {
		if medErr := preloadMedias(courses); medErr != nil {
			return courses, medErr
		}
	}

	if exPreload.Docs {
		if medErr := preloadDocs(courses); medErr != nil {
			return courses, medErr
		}
	}

	if exPreload.Categories {
		if catErr := preloadCourseCategories(courses); catErr != nil {
			return courses, catErr
		}
	}

	if exPreload.Levels {
		if catErr := preloadCourseLevels(courses); catErr != nil {
			return courses, catErr
		}
	}

	if exPreload.Partners {
		if partnerErr := preloadCoursePartners(courses); partnerErr != nil {
			return courses, partnerErr
		}
	}

	if exPreload.Owner {
		if ownerErr := preloadCourseOwner(courses); ownerErr != nil {
			return courses, ownerErr
		}
	}

	if err = r.preloadLearnerCount(courses); err != nil {
		return nil, err
	}

	if exPreload.AICourse {
		if preErr := preloadAICourse(courses); preErr != nil {
			return courses, preErr
		}
	}

	if err = preloadPriceSettings(courses); err != nil {
		return nil, err
	}

	if exPreload.ClpLaunchpad {
		if preErr := r.preloadClpLaunchpad(courses); preErr != nil {
			return nil, fmt.Errorf("preload launchpad error: %v", preErr)
		}
	}

	return courses, nil
}

func (r *CourseRepository) FindManyListItems(query *CourseQuery, options *FindManyOptions) ([]*CourseListItem, error) {
	if options == nil {
		options = new(FindManyOptions)
	}

	exPreload, newPreloads := getExPreload(options.Preloads)
	options.Preloads = newPreloads

	selectCols := []string{
		"c.cuid",
		"c.id",
		"c.create_at",
		"c.update_at",
		"c.delete_at",
		"c.org_id",
		"c.user_id",
		"c.name",
		"c.slug",
		"c.description",
		"c.short_desc",
		"c.section_count",
		"c.lesson_count",
		"c.active_lesson",
		"c.active_section",
		"c.video_count",
		"c.quiz_count",
		"c.mark_as_completed",
		"c.has_certificate",
		"p.course_id AS price_settings__course_id",
		"p.is_pay AS price_settings__is_pay",
		"p.fiat_currency AS price_settings__fiat_currency",
		"p.fiat_price AS price_settings__fiat_price",
		"p.fiat_discount_price AS price_settings__fiat_discount_price",
		"p.crypto_payment_enabled AS price_settings__crypto_payment_enabled",
		"p.crypto_currency AS price_settings__crypto_currency",
		"p.crypto_price AS price_settings__crypto_price",
		"p.crypto_discount_price AS price_settings__crypto_discount_price",
		"f.user_id AS thumbnail__user_id",
		"f.id AS thumbnail__id",
		"f.name AS thumbnail__name",
		"f.mime AS thumbnail__mime",
		"f.ext AS thumbnail__ext",
		"f.url AS thumbnail__url",
		"f.thumbnail_url AS thumbnail__thumbnail_url",
		"f.duration AS thumbnail__duration",
	}

	var stmtBuilder strings.Builder
	var vals []interface{}
	stmtBuilder.WriteString("FROM " + GetTblName(CourseTbl) + " AS c ")                                 // FROM courses AS c
	stmtBuilder.WriteString("LEFT JOIN " + GetTblName(CoursePriceTbl) + " AS p ON c.id = p.course_id ") // LEFT JOIN course_prices AS p ON c.id = p.course_id
	//stmtBuilder.WriteString("LEFT JOIN " + GetTblName(FileRelationTbl) + " AS fr ON fr.related_id = c.id AND fr.related_type = ? AND fr.field = ? ")
	//vals = append(vals, CourseModelName)
	//vals = append(vals, ThumbnailField)
	//stmtBuilder.WriteString("LEFT JOIN " + GetTblName(FileTbl) + " AS f ON fr.file_id = f.id ")
	stmtBuilder.WriteString("LEFT JOIN " + GetTblName(FileTbl) + " AS f ON c.thumbnail_id = f.id ")

	if query != nil {
		whereClause, whereVals, err := query.ToWhereClauseWithAlias("c")
		if err != nil {
			return nil, err
		}

		if whereClause != "" {
			stmtBuilder.WriteString("WHERE " + whereClause + " ")
			vals = append(vals, whereVals...)
		}
	}

	if len(options.Sort) > 0 {
		var sortsWithAlias []string
		for _, sortExpr := range options.Sort {
			sortsWithAlias = append(sortsWithAlias, "c."+sortExpr)
		}
		stmtBuilder.WriteString("ORDER BY " + strings.Join(sortsWithAlias, ",") + " ")
	}

	if options.Limit != nil {
		stmtBuilder.WriteString("LIMIT ? ")
		vals = append(vals, *options.Limit)
	}

	if options.Offset != nil {
		stmtBuilder.WriteString("OFFSET ? ")
		vals = append(vals, *options.Offset)
	}

	if len(options.CustomPreloads) > 0 {
		return nil, fmt.Errorf("custom preloads is unsupported")
	}

	if len(options.Preloads) > 0 {
		return nil, fmt.Errorf("unsupported preloads: %s", strings.Join(options.Preloads, ","))
	}

	rawSQL := "SELECT " + strings.Join(selectCols, ", ") + " " + stmtBuilder.String()
	rows, err := DB.Debug().Raw(rawSQL, vals...).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var courseItems []*CourseListItem
	if err = dbscanner.ScanRows(rows, &courseItems); err != nil {
		return nil, err
	}

	if err = r.preloadExFieldsForCourseItems(courseItems, exPreload); err != nil {
		return nil, err
	}

	return courseItems, nil
}

func (r *CourseRepository) findPageCourseByPartner(query *CourseQuery, options *FindPageOptions) ([]*Course, *Pagination, error) {
	entitiesChan := make(chan []*Course)
	countChan := make(chan int64)
	errorChan := make(chan error)
	defer func() {
		close(entitiesChan)
		close(countChan)
		close(errorChan)
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()

		var entities []*Course

		qb := query.ApplyJoin(DB)
		lo.ForEach(options.Sort, func(clause string, _ int) {
			qb = qb.Order("c." + clause)
		})
		qb = qb.Limit(options.PerPage).Offset((options.Page - 1) * options.PerPage)
		qb = qb.Table(GetTblName(CourseTbl) + " as c").Debug()
		if query.Partner != nil {
			qb = qb.Select("DISTINCT c.*, p.roles as accesses")
		} else {
			qb = qb.Select("DISTINCT c.*")
		}
		qb = qb.Joins("join " + GetTblName(CoursePartnerTbl) + " as p on p.course_id = c.cuid")
		result := qb.Find(&entities)

		if err := result.Error; err != nil {
			errorChan <- err
			return
		}
		entitiesChan <- entities
	}()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errorChan <- fmt.Errorf("panic: %v", r)
			}
		}()
		var c int64
		var entity Course
		qb := query.ApplyJoin(DB)
		result := qb.Table(GetTblName(CourseTbl) + " as c").Debug().
			Joins("join " + GetTblName(CoursePartnerTbl) + " as p on p.course_id = c.cuid").
			Model(&entity).Distinct("c.id").Count(&c)
		if err := result.Error; err != nil {
			errorChan <- err
			return
		}

		countChan <- c
	}()

	// Wait for all goroutines to finish
	var entities []*Course
	var entitiesCount int64
	for i := 0; i < 2; i++ {
		select {
		case entities = <-entitiesChan:
		case entitiesCount = <-countChan:
		case err := <-errorChan:
			return nil, nil, err
		}
	}

	if len(entities) == 0 {
		return entities, NewPagination(options.Page, options.PerPage, int(entitiesCount)), nil
	}

	if err := r.preloadLearnerCount(entities); err != nil {
		return nil, nil, err
	}

	if err := preloadPriceSettings(entities); err != nil {
		return nil, nil, err
	}

	return entities, NewPagination(options.Page, options.PerPage, int(entitiesCount)), nil
}

// FindPageByPartner returns courses and pagination by query conditions and find options, transaction is optional
func (r *CourseRepository) FindPageByPartner(query *CourseQuery, options *FindPageOptions) ([]*Course, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := getExPreload(preloads)
	options.Preloads = newPreloads
	courses, pagination, err := r.findPageCourseByPartner(query, options)
	if err != nil {
		return nil, nil, err
	}

	if len(courses) == 0 {
		return courses, pagination, nil
	}

	var thumbIDs []string
	for _, course := range courses {
		if course.ThumbnailID != "" {
			thumbIDs = append(thumbIDs, course.ThumbnailID)
		}
	}
	if len(thumbIDs) > 0 {
		if thumbs, tErr := Repository.File.FindMany(&FileQuery{IDIn: thumbIDs}, nil); tErr != nil {
			return nil, nil, tErr
		} else {
			lo.ForEach(courses, func(course *Course, _ int) {
				t, ok := lo.Find(thumbs, func(item *File) bool {
					return item.ID == course.ThumbnailID
				})
				if ok {
					course.Thumbnail = t
				}
			})
		}
	}

	if exPreload.Medias {
		if medErr := preloadMedias(courses); medErr != nil {
			return courses, pagination, medErr
		}
	}

	if exPreload.Docs {
		if medErr := preloadDocs(courses); medErr != nil {
			return courses, pagination, medErr
		}
	}

	if exPreload.Categories {
		if catErr := preloadCourseCategories(courses); catErr != nil {
			return courses, pagination, catErr
		}
	}

	if exPreload.Levels {
		if catErr := preloadCourseLevels(courses); catErr != nil {
			return courses, pagination, catErr
		}
	}

	if exPreload.Reviewing {
		if preErr := preloadReviewingCourse(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if exPreload.Published {
		if pubErr := r.preloadPublishedCourse(courses); pubErr != nil {
			return courses, pagination, pubErr
		}
	}

	if exPreload.FormRelations {
		if preErr := preloadFormRelations(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if exPreload.AICourse {
		if preErr := preloadAICourse(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if exPreload.ClpLaunchpad {
		if preErr := r.preloadClpLaunchpad(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if exPreload.Owner {
		if preErr := preloadCourseOwner(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if err = r.preloadLearnerCount(courses); err != nil {
		return nil, nil, err
	}

	if err = preloadPriceSettings(courses); err != nil {
		return nil, nil, err
	}

	return courses, pagination, err
}

// FindPage returns courses and pagination by query conditions and find options, transaction is optional
func (r *CourseRepository) FindPage(query *CourseQuery, options *FindPageOptions) ([]*Course, *Pagination, error) {
	if options == nil {
		options = &FindPageOptions{}
	}
	var preloads []string
	if options.Preloads != nil {
		preloads = options.Preloads
	}
	exPreload, newPreloads := getExPreload(preloads)
	options.Preloads = newPreloads
	courses, pagination, err := findPage[Course](CourseTbl, query, options)
	if err != nil {
		return nil, nil, err
	}

	if len(courses) == 0 {
		return courses, pagination, nil
	}

	var thumbIDs []string
	for _, course := range courses {
		if course.ThumbnailID != "" {
			thumbIDs = append(thumbIDs, course.ThumbnailID)
		}
	}
	if len(thumbIDs) > 0 {
		if thumbs, tErr := Repository.File.FindMany(&FileQuery{IDIn: thumbIDs}, nil); tErr != nil {
			return nil, nil, tErr
		} else {
			lo.ForEach(courses, func(course *Course, _ int) {
				t, ok := lo.Find(thumbs, func(item *File) bool {
					return item.ID == course.ThumbnailID
				})
				if ok {
					course.Thumbnail = t
				}
			})
		}
	}
	if exPreload.Medias {
		if medErr := preloadMedias(courses); medErr != nil {
			return courses, pagination, medErr
		}
	}

	if exPreload.Docs {
		if medErr := preloadDocs(courses); medErr != nil {
			return courses, pagination, medErr
		}
	}

	if exPreload.Categories {
		if catErr := preloadCourseCategories(courses); catErr != nil {
			return courses, pagination, catErr
		}
	}

	if exPreload.Levels {
		if catErr := preloadCourseLevels(courses); catErr != nil {
			return courses, pagination, catErr
		}
	}

	if exPreload.Reviewing {
		if preErr := preloadReviewingCourse(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if exPreload.Published {
		if pubErr := r.preloadPublishedCourse(courses); pubErr != nil {
			return courses, pagination, pubErr
		}
	}

	if exPreload.FormRelations {
		if preErr := preloadFormRelations(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if exPreload.AICourse {
		if preErr := preloadAICourse(courses); preErr != nil {
			return courses, pagination, preErr
		}
	}

	if err = preloadPriceSettings(courses); err != nil {
		return nil, nil, err
	}

	if exPreload.ClpLaunchpad {
		if preErr := r.preloadClpLaunchpad(courses); preErr != nil {
			return nil, nil, fmt.Errorf("preload launchpad error: %v", preErr)
		}
	}

	return courses, pagination, err
}

// Delete perform soft deletion to a org by ID, transaction is optional
func (r *CourseRepository) Delete(id string, trans *gorm.DB) error {
	if err := deleteByID[Course](CourseTbl, id, trans); err != nil {
		return err
	}

	if cErr := Cache.Course.Flush(); cErr != nil {
		log.Errorf("CourseRepository.Delete::Clear cache courses error: %v", cErr)
	}

	if cErr := Cache.LessonContent.Flush(); cErr != nil {
		log.Errorf("CourseRepository.Delete::Clear cache lesson contents error: %v", cErr)
	}
	return nil
}

// DeleteMany performs soft deletion to actions by query conditions
func (r *CourseRepository) DeleteMany(query *CourseQuery, trans *gorm.DB) (int64, error) {
	deletedCount, err := deleteMany[Course](CourseTbl, query, trans)
	if err != nil {
		return 0, err
	}

	if cErr := Cache.Course.Flush(); cErr != nil {
		log.Errorf("CourseRepository.Delete::Clear cache courses error: %v", cErr)
	}
	return deletedCount, nil
}

// Count returns number of courses by query conditions, transaction is optional
func (r *CourseRepository) Count(query *CourseQuery) (int64, error) {
	return count[Course](CourseTbl, query)
}

func (r *CourseRepository) UpdateLatestVersions(cuid string) error {
	query := fmt.Sprintf(`UPDATE %[1]s
        SET latest = false
        WHERE %[1]s.cuid = '%[2]s'
        AND %[1]s.version < (
            SELECT MAX(%[1]s.version)
            FROM %[1]s
            WHERE %[1]s.cuid = '%[2]s'
        )`, GetTblName(CourseTbl), cuid)

	result := DB.Exec(query)

	if result.Error != nil {
		return result.Error
	}

	if cErr := Cache.Course.Flush(); cErr != nil {
		log.Errorf("CourseRepository.UpdateLatestVersions::Clear cache courses error: %v", cErr)
	}

	return nil
}

func (r *CourseRepository) preloadExFieldsForCourseItems(courseItems []*CourseListItem, exPreload *CoursePreload) error {
	if len(courseItems) == 0 {
		return nil
	}

	errChan := make(chan error, 4)

	go func() {
		errChan <- PreloadLeanerCount(r.ctx, courseItems)
	}()

	go func() {
		errChan <- PreloadCategoriesAndLevel(courseItems, CourseModelName, exPreload.Categories, exPreload.Levels)
	}()

	if exPreload.Org {
		go func() {
			errChan <- PreloadOrgs(courseItems)
		}()
	} else {
		errChan <- nil
	}

	if exPreload.Owner {
		go func() {
			errChan <- PreloadOwner(courseItems)
		}()
	} else {
		errChan <- nil
	}

	for i := 0; i < 4; i++ {
		if err := <-errChan; err != nil {
			return err
		}
	}
	return nil
}

type UserCourseStats struct {
	CourseCuid string    `json:"course_cuid" dbscan:"course_cuid"`
	IsPaid     bool      `json:"is_paid" dbscan:"is_paid"`
	IsEnrolled bool      `json:"is_enrolled" dbscan:"is_enrolled"`
	Bookmark   *Bookmark `json:"bookmark" dbscan:"bookmark"`
}

func (r *CourseRepository) FindUserStats(user *User, courseCUIDs []string) ([]*UserCourseStats, error) {
	if len(courseCUIDs) == 0 || user == nil {
		return []*UserCourseStats{}, nil
	}

	formattedCUIDs := strings.Join(lo.Map(courseCUIDs, func(courseCUID string, _ int) string {
		return "'" + courseCUID + "'"
	}), ",")

	rawSQL := `
		WITH course_ids AS (
			SELECT unnest(ARRAY[` + formattedCUIDs + `]) AS course_cuid
		), i AS (
			SELECT id, entity_cuid AS course_cuid
			FROM public.openedu_order_items
			WHERE user_id = @user_id
			  AND entity_cuid IN (SELECT course_cuid FROM course_ids)
			  AND entity_type = @entity_type
			  AND status = @status
		), e AS (
			SELECT id, course_cuid
			FROM public.openedu_course_enrollments
			WHERE user_id = @user_id
			  AND course_cuid IN (SELECT course_cuid FROM course_ids)
			  AND blocked = false
		), b AS (
			SELECT *
			FROM public.openedu_bookmarks
			WHERE user_id = @user_id
			  AND entity_id IN (SELECT course_cuid FROM course_ids)
			  AND entity_type = @entity_type
			  AND delete_at = 0
		)
		SELECT
			c.course_cuid,
			(i.id IS NOT NULL) AS is_paid,
			(e.id IS NOT NULL) AS is_enrolled,
			b.id AS bookmark__model__id,
			b.create_at AS bookmark__model__create_at,
			b.update_at AS bookmark__model__update_at,
			b.delete_at AS bookmark__model__delete_at,
			b.name AS bookmark__name,
			b.entity_id AS bookmark__entity_id,
			b.entity_type AS bookmark__entity_type,
			b.user_id AS bookmark__user_id,
			b.parent_id AS bookmark__parent_id,
			b.link AS bookmark__link
		FROM course_ids c
				 LEFT JOIN i ON c.course_cuid = i.course_cuid
				 LEFT JOIN e ON c.course_cuid = e.course_cuid
				 LEFT JOIN b ON c.course_cuid = b.entity_id;`
	rows, err := DB.Debug().Raw(rawSQL,
		sql.Named("user_id", user.ID),
		sql.Named("entity_type", CourseModelName),
		sql.Named("status", OrderStatusSuccess),
	).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userStats []*UserCourseStats
	if err = dbscanner.ScanRows(rows, &userStats); err != nil {
		return nil, err
	}

	return userStats, nil
}

func (c *Course) CanUsePaymaster() bool {
	allowedCourses := GetConfig[[]string](PaymasterAllowedCoursesConfig)

	for _, cuid := range allowedCourses {
		if cuid == c.Cuid {
			return true
		}
	}

	return false
}

func (r *CourseRepository) FindCoursePropsByCourseID(courseID string) (CourseProps, error) {
	var courseProps interface{}
	if err := Cache.CourseProps.Get(courseID, courseProps); err != nil {
		type CourseWithProps struct {
			Props CourseProps `json:"props" gorm:"type:jsonb"`
		}

		c, err := findByID[CourseWithProps](CourseTbl, courseID, &FindOneOptions{})
		if err != nil {
			return CourseProps{}, err
		}
		if err := Cache.CourseProps.Set(courseID, c.Props); err != nil {
			log.Infof("CacheLessonContent.ErrorWithCacheCourseProps: %#v", err)
			return c.Props, err
		}
		return c.Props, nil
	} else {
		log.Infof("CacheLessonContent.courseProps hit cache")
		return courseProps.(CourseProps), nil
	}
}
