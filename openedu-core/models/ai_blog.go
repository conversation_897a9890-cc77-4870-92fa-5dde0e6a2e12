package models

import "gorm.io/gorm"

type BlogAIErrorCode string

const (
	BlogAIErrorCodeNoContext     BlogAIErrorCode = "no_context"
	BlogAIErrorCodeUnknown       BlogAIErrorCode = "unknown"
	BlogAIErrorExceedTokenLength BlogAIErrorCode = "exceed_token_length"
	BlogAIErrorInternalAI        BlogAIErrorCode = "internal_ai"
)

type AIBlogRewrite struct {
	Model
	Link       string   `json:"link"`
	OldContent *string  `json:"old_content"`
	BlogID     string   `json:"blog_id"`
	Status     AIStatus `json:"status"`
	AIPayload  JSONB    `json:"ai_payload" gorm:"column:ai_payload; type:jsonb"`
	// AIPPAYLOAD
	Title          string         `json:"title"`
	NewContent     string         `json:"content"`
	Cost           float64        `json:"cost"`
	MetaData       string         `json:"metadata"`
	Thumbnail      string         `json:"thumbnail"`
	OrgID          string         `json:"org_id"`
	AuthorID       string         `json:"author_id"`
	Schema         string         `json:"schema"`
	BlogType       BlogType       `json:"blog_type"`
	Error          *DetailAIError `json:"error" gorm:"type:jsonb"`
	OfferID        string         `json:"offer_id"`
	CurrentStep    AIGenerateStep `json:"current_step" gorm:"column:current_step;type:varchar(50)"`
	BlogCuid       string         `json:"blog_cuid" gorm:"column:blog_cuid;type:varchar(20)"`
	Language       string         `json:"language" gorm:"column:language;type:varchar(20);default:'English'"`
	RewriteOfferID string         `json:"rewrite_offer_id"`
	Tone           AITone         `json:"tone" gorm:"column:tone;type:varchar(20);default:'normal'"`
}

type SimpleAIBlogRewrite struct {
	Model
	Link     string         `json:"link"`
	BlogID   string         `json:"blog_id"`
	Status   AIStatus       `json:"status"`
	Title    string         `json:"title"`
	Cost     float64        `json:"cost"`
	MetaData string         `json:"metadata"`
	Error    *DetailAIError `json:"error" gorm:"type:jsonb"`
	Language string         `json:"language"`
}

func (b *AIBlogRewrite) ToSimple() *SimpleAIBlogRewrite {
	language := Repository.AIHistory.GetKeyFromLanguage(b.Language)
	return &SimpleAIBlogRewrite{
		Model:    b.Model,
		Link:     b.Link,
		BlogID:   b.BlogID,
		Status:   b.Status,
		Title:    b.Title,
		Cost:     b.Cost,
		MetaData: b.MetaData,
		Error:    b.Error,
		Language: language,
	}
}

type AIBlogRewriteQuery struct {
	Link        *string   `json:"link,omitempty" form:"link"`
	LinkNotNull *bool     `json:"not_nut_link,omitempty" form:"not_nut_link"`
	Title       *string   `json:"title,omitempty" form:"title"`
	Content     *string   `json:"content,omitempty" form:"content"`
	Cost        *float64  `json:"cost,omitempty" form:"cost"`
	MetaData    *string   `json:"metadata,omitempty" form:"meta_data"`
	Thumbnail   *string   `json:"thumbnail,omitempty" form:"thumbnail"`
	BlogID      *string   `json:"blog_id,omitempty" form:"blog_id"`
	BlogIDIn    []string  `json:"blog_id_in,omitempty" form:"blog_id_in"`
	Status      *AIStatus `json:"status,omitempty" form:"status"`
	OrgID       *string   `json:"org_id,omitempty" form:"org_id"`
	BlogCuid    *string   `json:"blog_cuid,omitempty" form:"blog_cuid"`
}

func (query *AIBlogRewriteQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.BlogID != nil {
		qb = qb.Where("blog_id = ?", *query.BlogID)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	if query.OrgID != nil {
		qb = qb.Where("org_id = ?", *query.OrgID)
	}

	if query.LinkNotNull != nil && *query.LinkNotNull {
		qb = qb.Where("link != '' ")

	}

	if len(query.BlogIDIn) > 0 {
		qb = qb.Where("blog_id IN ?", query.BlogIDIn)
	}

	if query.BlogCuid != nil {
		qb = qb.Where("blog_cuid = ?", *query.BlogCuid)
	}

	return qb
}

func (r *AIBlogRewriteRepository) Create(entity *AIBlogRewrite, trans *gorm.DB) error {
	return create(AIBlogRewriteTbl, entity, trans)
}

func (r *AIBlogRewriteRepository) CreateMany(entity []*AIBlogRewrite, trans *gorm.DB) error {
	return createMany(AIBlogRewriteTbl, entity, trans)
}

func (r *AIBlogRewriteRepository) FindMany(query *AIBlogRewriteQuery, options *FindManyOptions) ([]*AIBlogRewrite, error) {
	return findMany[AIBlogRewrite](AIBlogRewriteTbl, query, options)
}

func (r *AIBlogRewriteRepository) FindPage(query *AIBlogRewriteQuery, options *FindPageOptions) ([]*AIBlogRewrite, *Pagination, error) {
	return findPage[AIBlogRewrite](AIBlogRewriteTbl, query, options)
}

func (r *AIBlogRewriteRepository) FindOne(query *AIBlogRewriteQuery, options *FindOneOptions) (*AIBlogRewrite, error) {
	return findOne[AIBlogRewrite](AIBlogRewriteTbl, query, options)
}

func (r *AIBlogRewriteRepository) Update(entity *AIBlogRewrite, trans *gorm.DB) error {
	return update(AIBlogRewriteTbl, entity, trans)
}
