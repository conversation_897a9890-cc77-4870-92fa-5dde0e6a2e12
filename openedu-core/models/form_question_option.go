package models

import (
	"fmt"
	"gorm.io/gorm"
	"time"
)

type FormQuestionOption struct {
	ID         string `json:"id" gorm:"primaryKey;type:varchar(20);unique"`
	UID        string `json:"uid" gorm:"type:varchar(20)"`
	QuestionID string `json:"question_id" gorm:"type:varchar(20)"`
	Text       string `json:"text"`
	Order      int    `json:"order" gorm:"type:int8"`
	DeleteAt   int    `json:"delete_at" gorm:"type:int8"`
}

type SimpleFormQuestionOption struct {
	ID         string `json:"id"`
	UID        string `json:"uid"`
	QuestionID string `json:"question_id"`
	Text       string `json:"text"`
	Order      int    `json:"order"`
}

func (o *FormQuestionOption) Sanitize() *SimpleFormQuestionOption {
	return &SimpleFormQuestionOption{
		ID:         o.ID,
		UID:        o.UID,
		QuestionID: o.QuestionID,
		Text:       o.Text,
		Order:      o.Order,
	}
}

type FormQuestionOptionQuery struct {
	ID             *string  `json:"id,omitempty" form:"id"`
	IDIn           []string `json:"id_in,omitempty" form:"id_in"`
	IncludeDeleted *bool    `json:"include_deleted,omitempty" form:"include_deleted"`
}

func (query *FormQuestionOptionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db
	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", *&query.IDIn)
	}

	if query.IncludeDeleted == nil || !*query.IncludeDeleted {
		qb = qb.Where("delete_at = 0")
	}

	return qb
}

func (r *FormQuestionOptionRepository) Create(f *FormQuestionOption, trans *gorm.DB) error {
	return create(FormQuestionOptionTbl, f, trans)
}

func (r *FormQuestionOptionRepository) CreateMany(o []*FormQuestionOption, trans *gorm.DB) error {
	return createMany(FormQuestionOptionTbl, o, trans)
}

func (r *FormQuestionOptionRepository) Update(o *FormQuestionOption, trans *gorm.DB) error {
	return update(FormQuestionOptionTbl, o, trans)
}

func (r *FormQuestionOptionRepository) FindByID(id string, options *FindOneOptions) (*FormQuestionOption, error) {
	return findByID[FormQuestionOption](FormQuestionOptionTbl, id, options)
}

func (r *FormQuestionOptionRepository) FindOne(query *FormQuestionOptionQuery, options *FindOneOptions) (*FormQuestionOption, error) {
	return findOne[FormQuestionOption](FormQuestionOptionTbl, query, options)
}

func (r *FormQuestionOptionRepository) FindMany(query *FormQuestionOptionQuery, options *FindManyOptions) ([]*FormQuestionOption, error) {
	return findMany[FormQuestionOption](FormQuestionOptionTbl, query, options)
}

func (r *FormQuestionOptionRepository) FindPage(query *FormQuestionOptionQuery, options *FindPageOptions) ([]*FormQuestionOption, *Pagination, error) {
	return findPage[FormQuestionOption](FormQuestionOptionTbl, query, options)
}

func (r *FormQuestionOptionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[FormQuestionOption](FormQuestionOptionTbl, id, trans)
}

func (r *FormQuestionOptionRepository) DeleteMany(query *FormQuestionOptionQuery, trans *gorm.DB) (count int64, err error) {
	var tx *gorm.DB
	if trans != nil {
		tx = trans
	} else {
		tx = DB.Begin()
	}
	qb := ApplyQueryOptions(tx, query)
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}

		// Commit or rollback if not use external transaction
		if trans != nil {
			return
		}

		if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	now := time.Now().UnixMilli()
	result := qb.Table(GetTblName(FormQuestionOptionTbl)).Debug().
		Model(&FormQuestionOption{}).
		Updates(map[string]interface{}{
			"delete_at": now,
		})

	err = result.Error
	count = result.RowsAffected
	return
}
