## Openedu Core Backend
The API server for Openedu project.

### Development

#### Prerequisites 
- Go version: 1.21+

#### Installation
To facilitate a smooth development experience, follow these steps:

1. Install Hot Reload: https://github.com/cosmtrek/air
```shell
go install github.com/cosmtrek/air@latest
```
- MacOS ft ZSH, fix if receive error: `zsh: command not found air`
  - `nano ~/.zshrc`
  - add `alias air='$(go env GOPATH)/bin/air'`
  - restart zsh

2. Install dependencies:
```shell
go mod tidy
```

3. Install docker
Chose 1 of these ways:
- 1. Install Docker Desktop: visit this website: https://docs.docker.com/desktop/
- 2. Install Docker Engine and Docker compose: 
  - Docker Engine: https://docs.docker.com/engine/install/ubuntu/
  - Docker compose: https://docs.docker.com/compose/install/linux/

4. Init database
```shell 
docker-compose up -d
```

5. Start development
```shell
air
```

or 
```shell
go run main.go
```
