name: CI/CD Phuong

on:
  workflow_dispatch:

jobs:
  build-and-run:
    runs-on: ubuntu-latest
    environment: develop
    steps:
      - name: Check Out Repository
        uses: actions/checkout@v4

      - name: Deploy to server
        env:
          PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          USER_NAME: ${{ secrets.SSH_USERNAME }}
          HOST_NAME: ${{ secrets.SSH_HOST }}
        run: |
          mkdir -p ~/.ssh
          echo "$PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
          chmod 700 ~/.ssh/id_rsa
          rsync -avzr --delete -e 'ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null' ./ $USER_NAME@$HOST_NAME:/home/<USER>/openedu/phuong/openedu_core
          ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $USER_NAME@$HOST_NAME "cd /home/<USER>/openedu/phuong/openedu_core && sudo systemctl restart openedu_core_phuong.service"