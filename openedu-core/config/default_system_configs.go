package config

import (
	"openedu-core/models"
	"openedu-core/pkg/email_normalizer/rules"
	"openedu-core/pkg/setting"
	"openedu-core/pkg/util"

	"time"

	"github.com/shopspring/decimal"
)

type InitConfigParams struct {
	Key  string
	Val  interface{}
	Type string
}

func decimalFromString(val string) decimal.Decimal {
	value, _ := decimal.NewFromString(val)
	return value
}

func GetDefaultSystemConfigsParams() []InitConfigParams {
	return []InitConfigParams{
		// Allow domain
		{Key: models.AllowOriginConfig, Val: setting.AppSetting.AllowOrigin, Type: models.JsonArr},

		// Locales config
		{Key: models.LocaleConfig, Val: models.StringArray{"en", "vi"}, Type: models.JsonArr},

		// Learn method
		{Key: models.LearnMethodConfig, Val: models.StringArray{"on-demand", "hybrid", "online"}, Type: models.JsonArr},

		// Lesson content type
		{Key: models.CourseLessonContentType, Val: models.StringArray{"video", "pdf"}, Type: models.JsonArr},

		// Currency
		{Key: models.CurrencyConfig, Val: []*models.CurrencyInfo{
			// Fiat
			{
				Name:   "Vietnamese Dong",
				Symbol: models.FiatCurrencyVND,
				Type:   models.AssetTypeFiat,
			},
			{
				Name:   "United State Dollar",
				Symbol: models.FiatCurrencyUSD,
				Type:   models.AssetTypeFiat,
			},
			// Crypto
			{
				Name:   "Tether",
				Symbol: models.CryptoCurrencyUSDT,
				Type:   models.AssetTypeCrypto,
			},
			{
				Name:   "USD Coin",
				Symbol: models.CryptoCurrencyUSDC,
				Type:   models.AssetTypeCrypto,
			},
		}, Type: models.JsonArr},

		// Landing page
		{Key: models.OrgLandingPageConfig, Val: models.JSONB{}, Type: models.JsonB},

		// Email template variable name

		// Email verify path
		{Key: models.VerifyEmailPathConfig, Val: "email-verify", Type: models.String},

		// Reset password path
		{Key: models.ResetPasswordPathConfig, Val: "set-password", Type: models.String},

		// Set password path
		{Key: models.SetPasswordPathConfig, Val: "set-password", Type: models.String},

		// Invite creator path
		{Key: models.InviteCreatorPathConfig, Val: "invite-creator", Type: models.String},

		// Invite user path
		{Key: models.InviteUserPathConfig, Val: "invite-user", Type: models.String},

		// Policy URL
		{Key: models.PolicyUrlConfig, Val: "https://openedu101.com/policy", Type: models.String},

		// Wallet fiat min remain amount
		{Key: models.WalletFiatMinRemainAmount, Val: "0", Type: models.String},

		// Wallet crypto min remain amount
		{Key: models.WalletCryptoMinRemainAmount, Val: "0", Type: models.String},

		// Verify OTP
		{Key: models.VerifyOTP, Val: true, Type: models.String},

		// Block disposable emails enabled
		{Key: models.BlockDisposableEmailsEnabled, Val: true, Type: models.String},

		// Disposable email domains
		{Key: models.DisposableEmailDomains, Val: util.GetDisposableEmailsAsMap(), Type: models.JsonB},

		// AI Languages
		{Key: models.AILanguages, Val: util.Languages, Type: models.JsonB},

		// Whitelist Email AI Usage
		{Key: models.AIResourceUsageEmailConfig, Val: models.InitResourceUsage(), Type: models.JsonB},

		// Course launchpad min sections
		{Key: models.CourseLaunchpadMinSections, Val: 4, Type: models.Int},

		// Course launchpad voting phase rule
		{
			Key: models.CourseLaunchpadVotingPhaseRule,
			Val: []*models.CourseLaunchpadVotingPhaseRuleConfig{
				{
					FundingGoalGte: decimal.Zero,
					FundingGoalLt:  decimal.NewFromInt(10000),
					MaxPhases:      1,
				},
				{
					FundingGoalGte: decimal.NewFromInt(10000),
					FundingGoalLt:  decimal.NewFromInt(20000),
					MaxPhases:      2,
				},
				{
					FundingGoalGte: decimal.NewFromInt(20000),
					FundingGoalLt:  decimal.NewFromInt(40000),
					MaxPhases:      3,
				},
				{
					FundingGoalGte: decimal.NewFromInt(40000),
					FundingGoalLt:  decimal.NewFromInt(-1),
					MaxPhases:      4,
				},
			},
			Type: models.JsonArr,
		},

		// Course launchpad min approval percentage
		{Key: models.CourseLaunchpadMinApprovalPercentage, Val: 75, Type: models.Int},

		// Course launchpad min reject percentage
		{Key: models.CourseLaunchpadMinRejectPercentage, Val: 30, Type: models.Int},

		// Course launchpad min approval percentage
		{Key: models.CourseLaunchPadMinPledgeOptions, Val: []int64{
			5,
			10,
			15,
			20,
			35,
			40,
			45,
			50,
		}, Type: models.JsonArr},

		// Course launchpad voting duration
		{Key: models.CourseLaunchpadVotingDuration, Val: 7 * 24 * time.Hour, Type: models.TimeDuration},

		{Key: models.FundingWaitingPeriodDays, Val: 3 * 24 * time.Hour, Type: models.TimeDuration},

		{Key: models.CourseLaunchpadTestIDs, Val: []string{}, Type: models.JsonArr},

		{Key: models.WaitingSettingFundingTimeDays, Val: 15 * 24 * time.Hour, Type: models.TimeDuration},

		// EarnedPoint system ref user
		// {Key: string(models.OEPointSystemConfig), Val: &models.OEPointRefUserConfig{
		// 	EarnedPoint:            decimal.NewFromInt(100),
		// 	Enable:           true,
		// 	Currency:         models.FiatCurrencyUSD,
		// 	Rate:             decimalFromString(".001"),
		// 	ExpirationPeriod: models.TimePeriodDay,
		// 	ExpirationVal:    90,
		// }, Type: models.JsonB},

		// Field allow in request
		{Key: models.FieldAllowInRequest, Val: []string{util.NextPathField}, Type: models.JsonB},

		// Invite user path
		{Key: models.ConfirmInvitationPathConfig, Val: "auth-confirm", Type: models.String},

		{Key: models.ClpWaitingProcessFundingDays, Val: 3 * 24 * time.Hour, Type: models.TimeDuration},

		{Key: models.ClpMaximumPledgePercentage, Val: 20, Type: models.Int},

		{Key: models.NormalizeEmailEnabled, Val: true, Type: models.String},

		{Key: models.NormalizeEmailRules, Val: []rules.NormalizeEmailRule{
			// Gmail
			{
				PrimaryDomain:          "gmail.com",
				AliasDomains:           []string{"googlemail.com"},
				RemoveDots:             true,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Outlook/Microsoft
			{
				PrimaryDomain:          "outlook.com",
				AliasDomains:           []string{"hotmail.com", "live.com", "msn.com", "outlook.jp"},
				RemoveDots:             false,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Yahoo
			{
				PrimaryDomain:          "yahoo.com",
				AliasDomains:           []string{"yahoo.co.jp", "yahoo.co.uk", "ymail.com", "rocketmail.com"},
				RemoveDots:             true,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Apple iCloud
			{
				PrimaryDomain:          "icloud.com",
				AliasDomains:           []string{"me.com", "mac.com"},
				RemoveDots:             false,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// ProtonMail
			{
				PrimaryDomain:          "proton.me",
				AliasDomains:           []string{"protonmail.com", "pm.me"},
				RemoveDots:             false,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Zoho
			{
				PrimaryDomain:          "zoho.com",
				AliasDomains:           []string{"zohomail.com"},
				RemoveDots:             false,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// AOL
			{
				PrimaryDomain:          "aol.com",
				AliasDomains:           []string{"aim.com"},
				RemoveDots:             false,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// GMX
			{
				PrimaryDomain:          "gmx.com",
				AliasDomains:           []string{"gmx.net", "gmx.de", "gmx.at", "gmx.ch"},
				RemoveDots:             false,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Mail.ru
			{
				PrimaryDomain:          "mail.ru",
				AliasDomains:           []string{"bk.ru", "inbox.ru", "list.ru", "internet.ru"},
				RemoveDots:             false,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Yandex
			{
				PrimaryDomain:          "yandex.ru",
				AliasDomains:           []string{"yandex.com", "ya.ru"},
				RemoveDots:             false,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Annotate
			{
				PrimaryDomain:          "tutanota.com",
				AliasDomains:           []string{"tutanota.de", "tutamail.com", "tuta.io"},
				RemoveDots:             false,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// FastMail
			{
				PrimaryDomain:          "fastmail.com",
				AliasDomains:           []string{"fastmail.fm"},
				RemoveDots:             false,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Mailchimp Mandrill
			{
				PrimaryDomain:          "mandrillapp.com",
				AliasDomains:           []string{},
				RemoveDots:             false,
				RemovePlusSuffix:       true,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// QQ Mail
			{
				PrimaryDomain:          "qq.com",
				AliasDomains:           []string{},
				RemoveDots:             false,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Web.de
			{
				PrimaryDomain:          "web.de",
				AliasDomains:           []string{},
				RemoveDots:             false,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},

			// Hey.com
			{
				PrimaryDomain:          "hey.com",
				AliasDomains:           []string{},
				RemoveDots:             false,
				RemovePlusSuffix:       false,
				ConvertToLowercase:     true,
				ConvertToPrimaryDomain: true,
			},
		}, Type: models.JsonArr},

		{Key: models.AIModelsConfig, Val: models.GetDefaultAIModels(), Type: models.JsonB},

		{Key: models.AISubscribePlanForOrgEnable, Val: false, Type: models.String},

		{Key: models.AISubscribePlanForOrgConfig, Val: models.InitSubscibePlanForOrgConfig(), Type: models.JsonB},

		{Key: models.PlatformSponsorNFTGasCourses, Val: models.StringArray{}, Type: models.JsonArr},

		{Key: models.CheckCanClaimCertEveryHoursConfig, Val: 4, Type: models.Int}, // 4 hours

		{Key: models.StopCheckCanClaimCertAfterHoursConfig, Val: 168, Type: models.Int}, // 7 days

		{Key: models.AllowedDisplayModalsConfig, Val: models.StringArray{"AI_PRO_MODAL"}, Type: models.JsonArr},
	}
}
