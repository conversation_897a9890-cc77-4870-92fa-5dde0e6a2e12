package dto

import "openedu-core/models"

type PageConfigRequest struct {
	ID          string                `json:"id"`
	Name        string                `json:"name"`
	Type        models.PageConfigType `json:"type"`
	Actions     []string              `json:"actions,omitempty"`
	Description *string               `json:"description"`
}

type BulkCreatePageConfigRequest struct {
	Configs []PageConfigRequest `json:"configs"`
}

type ListPageConfigResponse struct {
	Results    []*models.PageConfig `json:"results"`
	Pagination *models.Pagination   `json:"pagination"`
}

type BulkUpdatePageConfigRequest struct {
	PageConfigs []PageConfigRequest `json:"page_configs"`
}
