package dto

import (
	"openedu-core/models"
	communicationdto "openedu-core/pkg/communication/dto"
)

type PricingPlanRequest struct {
	ID           *string          `json:"id"`
	Tier         models.PlanTier  `json:"tier"`
	Name         string           `json:"name"`
	Description  string           `json:"description"`
	MonthlyPrice models.PlanPrice `json:"monthly_price"`
	AnnualPrice  models.PlanPrice `json:"annual_price"`
	Enable       bool             `json:"enable"`
	Order        int              `json:"order"`
	Cycle        models.PlanCycle `json:"cycle"`
	EmailEvent   string           `json:"email_event"`
	EnableEmail  bool             `json:"enable_email"`

	AiLimitation models.AiLimitation `json:"ai_limitation"`
	Period       int                 `json:"period"`
	PeriodUnit   models.TimePeriod   `json:"period_unit"`

	User *models.User `json:"user"`
}

type ListPricingPlanResponse struct {
	Results    []*models.PricingPlan `json:"results"`
	Pagination *models.Pagination    `json:"pagination"`
}

type UserSubscribePlanRequest struct {
	Event       *communicationdto.EventType `json:"event"`
	EnableEmail bool                        `json:"enable_email"`
}
