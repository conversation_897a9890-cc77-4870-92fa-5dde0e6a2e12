package dto

import "openedu-core/models"

type CreateSectionRequest struct {
	ID       *string `json:"id"`
	UID      *string `json:"uid"`
	OrgID    string  `json:"org_id"`
	CourseID string  `json:"course_id" validated:"required"`
	ParentID string  `json:"parent_id"`
	Title    string  `json:"title" validated:"required"`
	Note     string  `json:"note" validated:"required"`
	Order    int     `json:"order"`
	Free     bool    `json:"free"`

	Status   *models.SectionStatus   `json:"status"`
	Contents []*LessonContentRequest `json:"contents"`

	User   *models.User   `json:"user"`
	Course *models.Course `json:"course"`
}

type UpdateSectionRequest struct {
	CreateSectionRequest
	Lessons []*CreateSectionRequest `json:"lessons"`
}

type BulkUpdateSectionRequest struct {
	CourseID string                  `json:"course_id"`
	Sections []*UpdateSectionRequest `json:"sections"`
}

type ListSectionResponse struct {
	Results    []*models.Section  `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}
