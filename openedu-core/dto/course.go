package dto

import (
	"openedu-core/models"

	"github.com/shopspring/decimal"
)

type CreateCourseRequest struct {
	ID              *string                 `json:"id"`
	Cuid            *string                 `json:"cuid"`
	Name            string                  `json:"name" validate:"required"`
	Slug            *string                 `json:"slug"`
	Description     string                  `json:"description"`
	ShortDesc       string                  `json:"short_desc"`
	MediaIDs        []*DefaultEntityRequest `json:"medias"`
	DocsIDs         []*DefaultEntityRequest `json:"docs"`
	ThumbnailID     string                  `json:"thumbnail_id"`
	PriceSettings   *models.CoursePrice     `json:"price_settings"`
	Enable          bool                    `json:"enable"`
	StartDate       int                     `json:"start_date"`
	EndDate         int                     `json:"end_date"`
	MarkAsCompleted bool                    `json:"mark_as_completed"`

	Partners         []*PartnerRequest       `json:"partners"`
	LearnMethod      string                  `json:"learn_method"`
	Status           *models.CourseStatus    `json:"status"`
	Categories       []*DefaultEntityRequest `json:"categories"`
	Levels           []*DefaultEntityRequest `json:"levels"`
	Props            *models.CourseProps     `json:"props"`
	HasCertificate   *bool                   `json:"has_certificate"`
	IsAIGenerated    *bool                   `json:"is_ai_generated"`
	AIGenerateStatus *models.AIStatus        // not show on dto
	AICourseID       *string                 `json:"ai_course_id"`
}

type UpdateCourseRequest struct {
	Name            string                  `json:"name" validate:"required"`
	Slug            *string                 `json:"slug"`
	Description     string                  `json:"description"`
	ShortDesc       string                  `json:"short_desc"`
	MediaIDs        []*DefaultEntityRequest `json:"medias"`
	DocsIDs         []*DefaultEntityRequest `json:"docs"`
	ThumbnailID     string                  `json:"thumbnail_id"`
	PriceSettings   *models.CoursePrice     `json:"price_settings"`
	Enable          bool                    `json:"enable"`
	StartDate       int                     `json:"start_date"`
	EndDate         int                     `json:"end_date"`
	MarkAsCompleted bool                    `json:"mark_as_completed"`

	Partners []*PartnerRequest `json:"partners"`
	//Status      models.CourseStatus     `json:"status"`
	LearnMethod    string                  `json:"learn_method"`
	Categories     []*DefaultEntityRequest `json:"categories"`
	Levels         []*DefaultEntityRequest `json:"levels"`
	Props          *models.CourseProps     `json:"props"`
	HasCertificate *bool                   `json:"has_certificate"`
}

type UpdateCoursePartnerRequest struct {
	Partners []*PartnerRequest `json:"partners"`
}

type DeleteCoursePartnerRequest struct {
	UserIds []string `json:"user_ids" form:"user_ids"`
}

type ListCourseResponse struct {
	Results    []*models.SimpleCourse `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type ListCourseItemsResponse struct {
	Results    []*models.CourseListItem `json:"results"`
	Pagination *models.Pagination       `json:"pagination"`
}

type ListCoursePartnerResponse struct {
	Results    []*models.SimplePartner `json:"results"`
	Pagination *models.Pagination      `json:"pagination"`
}

type ListPublishCourseResponse struct {
	Results    []*models.PublishCourse `json:"results"`
	Pagination *models.Pagination      `json:"pagination"`
}

type PartnerRequest struct {
	ID     string             `json:"id"`
	Roles  models.StringArray `json:"roles"`
	Enable bool               `json:"enable"`
}

type PublishCourseRequest struct {
	Status models.CourseStatus `json:"status"`
	Note   string              `json:"note"`

	Course    *models.Course
	Org       *models.Organization
	Requester *models.User
}

type UpdateCourseEnrollmentRequest struct {
	Blocked       bool   `json:"blocked"`
	BlockedReason string `json:"blocked_reason"`
}

type ListCourseEnrollmentResponse struct {
	Results    []*models.SimpleCourseEnrollment `json:"results"`
	Pagination *models.Pagination               `json:"pagination"`
}

type ChangeCourseStageRequest struct {
	User       *models.User
	Enable     *bool  `json:"enable"`
	EnableRoot *bool  `json:"enable_root"`
	Cuid       string `json:"cuid"  validate:"required"`
}

type CertificateConditionRequest struct {
	CompletedAllQuizzes           *bool `json:"completed_all_quiz"`
	CompletedCourse               *bool `json:"completed_course"`
	CourseCompletionPercentage    *int  `json:"course_completion_percentage"`
	CompletedFinalQuiz            *bool `json:"completed_final_quiz"`
	FinalQuizCompletionPercentage *int  `json:"final_quiz_completion_percentage"`
}

type GetMyCoursesRequest struct {
	User                    *models.User         `json:"-" form:"-"`
	Org                     *models.Organization `json:"-" form:"-"`
	Group                   models.CourseGroup   `json:"group" form:"group"`
	ExcludeUnPublishCourses *bool                `json:"exclude_unpublish_courses" form:"exclude_unpublish_courses"`
}

type CountMyCoursesRequest struct {
	User                    *models.User         `json:"-" form:"-"`
	Org                     *models.Organization `json:"-" form:"-"`
	Group                   models.CourseGroup   `json:"group" form:"group"`
	ExcludeUnPublishCourses *bool                `json:"exclude_unpublish_courses" form:"exclude_unpublish_courses"`
}

type CountMyCoursesResponse struct {
	InProgress int64 `json:"in_progress"`
	Completed  int64 `json:"completed"`
	NotStarted int64 `json:"not_started"`
	Wishlist   int64 `json:"wishlist"`
}

type DepositSponsorGasFeeRequest struct {
	Course *models.Course       `json:"-"`
	Wallet *models.Wallet       `json:"-"`
	Org    *models.Organization `json:"-"`

	Amount decimal.Decimal `json:"amount"`
}

type WithdrawSponsorGasFeeRequest struct {
	Course *models.Course       `json:"-"`
	Wallet *models.Wallet       `json:"-"`
	Org    *models.Organization `json:"-"`

	Amount decimal.Decimal `json:"amount"`
}

type FeePerMintNFTResponse struct {
	Currency       models.Currency          `json:"currency"`
	Network        models.BlockchainNetwork `json:"network"`
	SponsorBalance decimal.Decimal          `json:"sponsor_balance"`
	EstimatedFee   decimal.Decimal          `json:"estimated_fee"`
}

type CourseRevenueReportResp struct {
	Results    []*models.CourseRevenueOrderDetail `json:"results"`
	Pagination *models.Pagination                 `json:"pagination"`
}

type CronJobCreateCourseRevenueReq struct {
	Periods []*models.TimePeriod `json:"periods"`
}

type CourseRevenueGraphReq struct {
	CuidIn    []string `json:"cuid_in" validate:"required"`
	StartDate *int64   `json:"start_date"`
	EndDate   *int64   `json:"end_date"`
}

type CourseRevenueGraphResp struct {
	DataPoints []*models.CourseRevenuePoint `json:"data_points"`
	Pagination *models.Pagination           `json:"pagination"`
}
