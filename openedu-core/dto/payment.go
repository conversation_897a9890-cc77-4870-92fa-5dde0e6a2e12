package dto

import (
	"github.com/shopspring/decimal"
	"openedu-core/models"
)

type CreatePaymentRequest struct {
	PaymentInfo     models.PaymentInfo   `json:"payment_info"`
	OrderID         string               `json:"order_id"`
	CourseID        []string             `json:"course_id"`
	PaymentMethodID string               `json:"payment_method_id"`
	Amount          decimal.Decimal      `json:"amount"`
	Payload         SepayTransaction     `json:"payload"`
	Status          models.PaymentStatus `json:"status"`
	OrgID           string               `json:"org_id"`
	PayFromOrgID    string               `json:"pay_from_org_id"`
	Currency        models.Currency      `json:"currency"`
}

type UpdatePaymentRequest struct {
	Status *models.PaymentStatus `json:"status"`
}

type SepayWebhookRequest struct {
	ID              int             `json:"id"`
	Gateway         string          `json:"gateway"`
	TransactionDate string          `json:"transactionDate"`
	AccountNumber   string          `json:"accountNumber"`
	Code            string          `json:"code"`
	Content         string          `json:"content"`
	TransferType    string          `json:"transferType"`
	TransferAmount  decimal.Decimal `json:"transferAmount"`
	Accumulated     decimal.Decimal `json:"accumulated"`
	SubAccount      string          `json:"subAccount"`
	ReferenceCode   string          `json:"referenceCode"`
	Description     string          `json:"description"`
}

type SepayTransaction struct {
	ID                 int             `json:"id"`
	Gateway            string          `json:"gateway"`
	TransactionDate    int64           `json:"transaction_date"`
	AccountNumber      string          `json:"account_number"`
	SubAccount         string          `json:"sub_account"`
	AmountIn           decimal.Decimal `json:"amount_in"`
	AmountOut          decimal.Decimal `json:"amount_out"`
	Accumulated        decimal.Decimal `json:"accumulated"`
	Code               string          `json:"code"`
	TransactionContent string          `json:"transaction_content"`
	ReferenceNumber    string          `json:"reference_number"`
	Body               string          `json:"body"`
}

type ListPaymentResponse struct {
	Results    []*models.SimplePayment `json:"results"`
	Pagination *models.Pagination      `json:"pagination"`
}
