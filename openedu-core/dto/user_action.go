package dto

import "openedu-core/models"

type ReportUserRequest struct {
	Reason string `json:"reason"`
}

type ReportUserParams struct {
	User         *models.User `json:"user"`
	TargetUserID string       `json:"target_user_id"`
	Reason       string       `json:"reason" validate:"required"`
}

type ActionParams struct {
	User         *models.User `json:"user"`
	TargetUserID string       `json:"target_user_id"`
}

type ListUserActionResponse struct {
	Results    []*models.SimpleUserAction `json:"results"`
	Pagination *models.Pagination         `json:"pagination"`
}

type FindPageUserActionRequest struct {
	SearchTerm       *string                   `json:"search_term" form:"search_term"`
	SearchCategories *string                   `json:"search_categories" form:"search_categories"`
	Type             models.ActionRelationType `json:"type" form:"type" validate:"required"`
}

type BlockListUsersRequest struct {
	UserIDs []string `json:"user_ids"`
}
