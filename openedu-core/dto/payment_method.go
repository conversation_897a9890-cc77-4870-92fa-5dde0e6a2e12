package dto

import (
	"openedu-core/models"
)

type PaymentMethodRequest struct {
	OrganizeID    string                   `json:"organize_id"`
	CourseID      string                   `json:"course_id"`
	Service       models.PaymentService    `json:"service"`
	Account       string                   `json:"account"`
	AccountNumber string                   `json:"account_number"`
	AccountName   string                   `json:"account_name"`
	Network       models.BlockchainNetwork `json:"network"`
	Enable        bool                     `json:"enable"`
}

type ListPaymentMethodResponse struct {
	Results    []*models.SimplePaymentMethod `json:"results"`
	Pagination *models.Pagination            `json:"pagination"`
}
