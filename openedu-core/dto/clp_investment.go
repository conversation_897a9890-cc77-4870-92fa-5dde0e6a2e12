package dto

import (
	"openedu-core/models"

	"github.com/shopspring/decimal"
)

type CreateInvestmentRequest struct {
	LaunchpadID string          `json:"launchpad_id" validate:"required"`
	WalletID    string          `json:"wallet_id" validate:"required"`
	Amount      decimal.Decimal `json:"amount" validate:"required"`

	Launchpad *models.ClpLaunchpad `json:"-"`
	User      *models.User         `json:"-"`
	Org       *models.Organization `json:"-"`
	Wallet    *models.Wallet       `json:"-"`
}

type ListClpInvestmentResponse struct {
	TotalAmount  decimal.Decimal         `json:"total_amount"`
	TotalBackers int                     `json:"total_backers"`
	FundingGoal  models.FundingProps     `json:"funding_goal"`
	Results      []*models.ClpInvestment `json:"results"`
	Pagination   *models.Pagination      `json:"pagination"`
}

type VotingLaunchpadRequest struct {
	Status      models.StatusVotingLaunchpad `json:"status" validate:"required"`
	Launchpad   *models.ClpLaunchpad         `json:"-"`
	Milestone   *models.ClpVotingMilestone   `json:"-"`
	Investment  *models.ClpInvestment        `json:"-"`
	VotingPhase *models.ClpVotingPhase       `json:"-"`
	User        *models.User                 `json:"-"`
}