package dto

import "openedu-core/models"

type BookmarkRequest struct {
	UserID     string           `json:"user_id"`
	Name       string           `json:"name"`
	EntityID   string           `json:"entity_id"`
	EntityType models.ModelName `json:"entity_type"`
	ParentID   *string          `json:"parent_id"`
	Link       string           `json:"link"`
}

type ListBookmarkResponse struct {
	Results    []*models.Bookmark `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}
