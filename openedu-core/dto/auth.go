package dto

import (
	"openedu-core/models"
	"openedu-core/pkg/util"
)

type AuthRequest struct {
	Provider     util.SNSProvider `json:"provider" validate:"required"`
	Code         string           `json:"code" validate:"required"`
	CodeVerifier string           `json:"code_verifier"`
	RefCode      string           `json:"ref_code"`
}

type AuthResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	OTPVerified  bool   `json:"otp_verified"`
}

type AuthParams struct {
	User        *models.User
	Org         *models.Organization
	UserAgent   *string
	OriginUrl   string
	OTPVerified bool
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

type RefreshTokenParams struct {
	Org          *models.Organization
	OriginUrl    string
	RefreshToken string
}

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type RequestTokenTwitter struct {
	CallbackURL string `json:"oauth_callback" form:"oauth_callback" validate:"required"`
}

type RegisterRequest struct {
	DisplayName string `json:"display_name" validate:"required,max=50"`
	Email       string `json:"email" validate:"required,email"`
	Password    string `json:"password" validate:"required,min=8"`
	RefCode     string `json:"ref_code"`
}

type RegisterParams struct {
	DisplayName     string
	Email           string
	Password        string
	Org             *models.Organization
	OriginUrl       string
	AllowFieldsData map[string]interface{}
	RefCode         string
}

type RegisterResponse struct {
	AccessToken     string `json:"access_token"`
	RefreshToken    string `json:"refresh_token"`
	NormalizedEmail string `json:"normalized_email"`
	ConflictedEmail string `json:"conflicted_email"`
}

type ExistingNormalizedEmailResponse struct {
	Message         string `json:"message"`
	NormalizedEmail string `json:"normalized_email"`
	ConflictedEmail string `json:"conflicted_email"`
}

type ResendMailRequest struct {
	Event models.EventType `json:"event" validate:"required,max=50"`
	Email string           `json:"email" validate:"required,email"`
}

type ResendMailPrams struct {
	Event           models.EventType
	Email           string
	AllowFieldsData map[string]interface{}
}

type VerifyUserRequest struct {
	Token string `json:"token" validate:"required,max=50"`
}

type VerifyUserResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type VerifyUserParams struct {
	Token     string
	Org       *models.Organization
	OriginUrl string
}

type ForgotPasswordRequest struct {
	Email string `json:"email" validate:"required,email"`
}

type ConfirmResetPasswordRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Token    string `json:"token" validate:"required,max=50"`
	Password string `json:"password" validate:"required,min=8"`
}

type ConfirmResetParams struct {
	Email     string
	Token     string
	Password  string
	Org       *models.Organization
	OriginUrl string
}

type ConfirmResetResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type ExternalRegisterRequest struct {
	Email        string `json:"email" validate:"required,email"`
	Organization string `json:"organization" validate:"required"`
	SourceUrl    string `json:"source_url" validate:"required"`
}

type ExternalRegisterParams struct {
	Email           string
	AllowFieldsData map[string]interface{}
}

type SetPasswordRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Token    string `json:"token" validate:"required,max=50"`
	Password string `json:"password" validate:"required,min=8"`
}

type SetPasswordParams struct {
	Email     string
	Token     string
	Password  string
	Org       *models.Organization
	OriginUrl string
}

type SetPasswordResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type VerifyOTPrequest struct {
	Email string `json:"email" validate:"required,email"`
	OTP   string `json:"otp" validate:"required"`
}

type ResendOTPRequest struct {
	Email string `json:"email" validate:"required,email"`
}
