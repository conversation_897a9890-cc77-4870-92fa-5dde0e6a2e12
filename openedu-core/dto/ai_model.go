package dto

import "openedu-core/models"

type GetAvailableAIModel struct {
	ID           string                `json:"id"`
	Name         string                `json:"name"`
	Description  string                `json:"description"`
	Configs      models.AIModelConfigs `json:"configs"`
	DisplayName  string                `json:"display_name"`
	ThumbnailURL string                `json:"thumbnail_url"`

	OrgID     string `json:"org_id"`
	OrgSchema string `json:"org_schema"`

	IsAvailable bool `json:"is_available"`
}

type FindAIModelRequest struct {
	UserID           string `json:"user_id" form:"user_id"`
	OrgID            string `json:"org_id" form:"org_id"`
	ExtendedThinking bool   `json:"extended_thinking" form:"exte"`
}

type AIModelRequest struct {
	Name         string                `json:"name" validate:"required"`
	Description  string                `json:"description"`
	Enabled      bool                  `json:"enabled" validate:"required"`
	Configs      models.AIModelConfigs `json:"configs" validate:"required"`
	DisplayName  string                `json:"display_name"`
	ThumbnailURL string                `json:"thumbnail_url"`
	Order        int16                 `json:"order" validate:"required"`
}
