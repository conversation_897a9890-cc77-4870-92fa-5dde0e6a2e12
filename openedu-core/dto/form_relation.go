package dto

import "openedu-core/models"

type FormRelationRequest struct {
	Enabled              bool                            `json:"enabled"`
	FormID               *string                         `json:"form_id"`
	RelatedEntityID      string                          `json:"related_entity_id"`
	RelatedEntityUID     string                          `json:"related_entity_uid"`
	RelatedEntityType    models.ModelName                `json:"related_entity_type"`
	OrgID                string                          `json:"org_id"`
	OrgSchema            string                          `json:"org_schema"`
	StartWhen            models.FormTriggerCondition     `json:"start_when"`
	EndWhen              *models.FormTriggerCondition    `json:"end_when"`
	ConfirmationSettings models.FormConfirmationSettings `json:"confirmation_settings" gorm:"type:jsonb"`
	Type                 *models.FormRelationType        `json:"type"`
	Name                 string                          `json:"name"`
	HasCancelBtn         bool                            `json:"has_cancel_btn"`
}

type ListFormRelationResponse struct {
	Results    []*models.FormRelation `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}
