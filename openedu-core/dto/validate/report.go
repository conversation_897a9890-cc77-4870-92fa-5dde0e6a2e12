package dto

import (
	"openedu-core/models"

	"github.com/go-playground/validator/v10"
)

func validateReportType(fl validator.FieldLevel) bool {
	report, ok := fl.Parent().Interface().(models.ReportCourseEnrollmentRequest)
	if !ok {
		return false
	}
	switch report.ReportType {
	case models.ReportCourseEnrollment, models.ReportCourseOrderedEnrolled:
		return report.CourseCuid != nil
	}
	return true
}
