package dto

import (
	"github.com/shopspring/decimal"
	"openedu-core/dto"
	"openedu-core/models"

	"github.com/go-playground/validator/v10"
)

func validateCouponStruct(fl validator.FieldLevel) bool {
	coupon, ok := fl.Parent().Interface().(dto.CreateCouponRequest)
	if !ok {
		return false
	}
	switch coupon.Type {
	case models.CouponPercentage:
		return !coupon.FiatDiscountPercentage.Equal(decimal.Zero)
		//case models.CouponFlat:
		//	return !coupon.DiscountAmount.Equal(decimal.Zero)
	}
	return false
}

func validateMaximumCouponStruct(fl validator.FieldLevel) bool {
	//coupon, ok := fl.Parent().Interface().(dto.CreateCouponRequest)
	//if !ok {
	//	return false
	//}
	//if coupon.Type == models.CouponFlat {
	//	return coupon.FiatAllowMaximumDiscount.Equal(decimal.Zero)
	//}
	return true
}
