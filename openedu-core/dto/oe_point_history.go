package dto

import (
	"github.com/shopspring/decimal"
	"openedu-core/models"
)

type UserPointResponse struct {
	Point       models.OEPointProps     `json:"point"`
	RefCode     *models.OEReferralCode  `json:"ref_code"`
	NewPoints   NewPointHistoryResponse `json:"new_points"`
	PointWallet []*models.Wallet        `json:"point_wallets"`
	TotalReward int64                   `json:"total_reward"`
}

type NewPointHistoryResponse struct {
	Referral      NPointHistoryResponse    `json:"referral"`
	Milestone     PointHistoryMilestoneRes `json:"milestone"`
	Featured      NPointHistoryFeatureRes  `json:"featured"`
	Timebase      NPointHistoryResponse    `json:"timebase"`
	WeeklyStreak  NPointHistoryStreakRes   `json:"weekly_streak"`
	MonthlyStreak NPointHistoryStreakRes   `json:"monthly_streak"`
	Referee       NPointHistoryResponse    `json:"referee"`
}

type NPointHistoryResponse struct {
	Amount decimal.Decimal `json:"amount"`
	Count  int             `json:"count"`
}

type NPointHistoryStreakRes struct {
	NPointHistoryResponse
	TotalCount int `json:"total_count"`
}

type NPointHistoryFeatureRes struct {
	NPointHistoryResponse
	FiatCount   int `json:"fiat_count"`
	CryptoCount int `json:"crypto_count"`
	CourseCount int `json:"course_count"`
}

type PointHistoryMilestoneRes struct {
	NPointHistoryResponse
	Milestones []NPointHistoryRes `json:"milestones"`
}

type NPointHistoryRes struct {
	ID               string             `json:"id"`
	CreateAt         int                `json:"create_at"`
	Amount           decimal.Decimal    `json:"amount"`
	UserID           string             `json:"user_id"`
	CampaignID       string             `json:"campaign_id"`
	Source           models.PointSource `json:"source"`
	ClaimDate        int                `json:"claim_date"`
	MilestoneReached int64              `json:"milestone_reached"`
}

type UserClaimPointRequest struct {
	User       *models.User                `json:"user"`
	Point      decimal.Decimal             `json:"point"`
	Source     models.PointSource          `json:"source"`
	Props      *models.OEPointHistoryProps `json:"props"`
	CampaignID string                      `json:"campaign_id"`
}

type UserClaimNewPointRequest struct {
	Sources []models.PointSource `json:"sources"`
}

type ListOEPointHistoryResponse struct {
	Results    []*models.OEPointHistory `json:"results"`
	Pagination *models.Pagination       `json:"pagination"`
}
