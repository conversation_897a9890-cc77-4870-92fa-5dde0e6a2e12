package dto

import (
	"openedu-core/models"
	"openedu-core/pkg/openedu_scheduler"

	"github.com/shopspring/decimal"
)

type CreateClpLaunchpadRequest struct {
	Name           string `json:"name" validate:"required"`
	Description    string `json:"description"`
	LearnerOutcome string `json:"learner_outcome"`
	ThumbnailID    string `json:"thumbnail_id"`
	PreviewVideoID string `json:"preview_video_id"`

	CourseCuid *string `json:"course_cuid"`

	VotingStartDate     int `json:"voting_start_date"`
	VotingEndDate       int `json:"voting_end_date"`
	EstimateFundingDays int `json:"estimate_funding_days"`

	Status      *models.LaunchpadStatus `json:"status"`
	Categories  []*DefaultEntityRequest `json:"categories"`
	Levels      []*DefaultEntityRequest `json:"levels"`
	FundingGoal *models.FundingProps    `json:"funding_goal"`
}

type ListClpLaunchpadResponse struct {
	Results    []*models.ClpLaunchpad `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type UpdateClpLaunchpadRequest struct {
	Name                *string                   `json:"name"`
	Slug                *string                   `json:"slug"`
	Description         *string                   `json:"description"`
	ThumbnailID         *string                   `json:"thumbnail_id"`
	LearnerOutcome      *string                   `json:"learner_outcome"`
	PreviewVideoID      *string                   `json:"preview_video_id"`
	Enable              *bool                     `json:"enable"`
	VotingStartDate     *int                      `json:"voting_start_date"`
	VotingEndDate       *int                      `json:"voting_end_date"`
	Categories          []*DefaultEntityRequest   `json:"categories"`
	Levels              []*DefaultEntityRequest   `json:"levels"`
	TargetFunding       *decimal.Decimal          `json:"target_funding"`
	MinPledge           *decimal.Decimal          `json:"min_pledge"`
	Currency            *models.Currency          `json:"currency"`
	ProfitPercentage    *float64                  `json:"profit_percentage"`
	VotingMilestones    []*VotingMilestoneRequest `json:"clp_voting_milestones"`
	CourseCuid          *string                   `json:"course_cuid"`
	EstimateFundingDays *int                      `json:"estimate_funding_days"`
	//FundingStartDate    *int64                    `json:"funding_start_date"`
	//FundingEndDate      *int64                    `json:"funding_end_date"`
}

type VotingMilestoneRequest struct {
	ID                    string `json:"id"`
	Order                 int    `json:"order"`
	EstimatedOpenVoteDate int64  `json:"estimated_open_vote_date"`
	TargetSection         int    `json:"target_section"`
}

type ListLaunchpadPartnerResponse struct {
	OwnerProfile *models.LaunchpadPartner   `json:"owner_profile"`
	Partners     []*models.LaunchpadPartner `json:"partners"`
}

func (user *UserProfileResponse) ToLaunchpadPartner() *models.LaunchpadPartner {
	return &models.LaunchpadPartner{
		ID:           user.ID,
		Username:     user.Username,
		Email:        user.Email,
		Active:       user.Active,
		Blocked:      user.Blocked,
		DisplayName:  user.DisplayName,
		Avatar:       user.Avatar,
		CoverPhoto:   user.CoverPhoto,
		Skills:       user.Skills,
		Headline:     user.Headline,
		About:        user.About,
		Phone:        user.Phone,
		Position:     user.Position,
		Props:        user.Props,
		Roles:        user.Roles,
		Following:    user.Following,
		Followers:    user.Followers,
		TotalBlogs:   user.TotalBlogs,
		TotalCourses: user.TotalCourses,
	}
}

type PublishLaunchpadRequest struct {
	Status models.LaunchpadStatus `json:"status" validate:"required"`
	Note   string                 `json:"note"`

	Launchpad *models.ClpLaunchpad
	Org       *models.Organization
	Requester *models.User
}

type OpenVotingMilestoneRequest struct {
	User            *models.User
	Launchpad       *models.ClpLaunchpad
	VotingMilestone *models.ClpVotingMilestone
}

type InitLaunchpadPoolRequest struct {
	WalletID string `json:"wallet_id"  validate:"required"`

	Wallet    *models.Wallet       `json:"-"`
	User      *models.User         `json:"-"`
	Org       *models.Organization `json:"-"`
	Launchpad *models.ClpLaunchpad `json:"-"`
}

type ApproveLaunchpadPoolRequest struct {
	IsApproved bool                 `json:"is_approved"`
	Launchpad  *models.ClpLaunchpad `json:"launchpad"`
}

type UpdateLpPoolFundingTimeRequest struct {
	PoolID           string `json:"pool_id"  validate:"required"`
	FundingStartDate int64  `json:"funding_start_date"  validate:"required"`
	FundingEndDate   int64  `json:"funding_end_date"  validate:"required"`
}

type CancelLaunchpadRequest struct {
	Launchpad *models.ClpLaunchpad `json:"launchpad"`
}

type SetFundingTimeLaunchpadRequest struct {
	Launchpad        *models.ClpLaunchpad `json:"-"`
	FundingStartDate int64                `json:"funding_start_date" validate:"required"`
}

type ClaimLaunchpadRefundRequest struct {
	User      *models.User
	Launchpad *models.ClpLaunchpad
}

type MyLaunchpadsResponse struct {
	Results    []*MyLaunchpadResponse `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type MyLaunchpadResponse struct {
	Launchpad  *models.ClpLaunchpad        `json:"launchpad"`
	Investment *models.SimpleClpInvestment `json:"investment"`
}

type DecideContinueVotingRequest struct {
	IsContinued bool `json:"is_continued"`
}

type SchedulerClpLaunchpadRequest struct {
	JobKey        openedu_scheduler.ScheduledJobKey `json:"job_key"`
	LaunchpadID   string                            `json:"launchpad_id"`
	VotingPhaseID string                            `json:"voting_phase_id"`
	MilestoneID   string                            `json:"milestone_id"`
}
