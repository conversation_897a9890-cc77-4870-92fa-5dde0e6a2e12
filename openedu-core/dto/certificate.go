package dto

import (
	"openedu-core/models"

	"github.com/shopspring/decimal"
)

type CertificateRequest struct {
	CourseCuid  string       `json:"course_cuid" validate:"required"`
	File        *models.File `json:"file" validate:"required"`
	Image       *models.File `json:"image"`
	CompletedAt int          `json:"completed_at" validate:"required"`
}

type ListCertificateResponse struct {
	Results    []*CertificateResponse `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}

type CheckingCertificateConditionRequest struct {
	UserID string         `json:"user_id"`
	Course *models.Course `json:"course" validate:"required"`
	OrgID  string         `json:"org_id"`
}

type CertificateResponse struct {
	ID             string                   `json:"id"`
	UserID         string                   `json:"user_id"`
	CourseName     string                   `json:"course_name"`
	CompletedAt    int                      `json:"completed_at"`
	Files          []*models.File           `json:"files"`
	MintNFTEnabled bool                     `json:"mint_nft_enabled"`
	NftTokenID     string                   `json:"nft_token_id"`
	NftTxHash      string                   `json:"nft_tx_hash"`
	NftNetwork     models.BlockchainNetwork `json:"nft_network"`
	Props          models.CertificateProps  `json:"props"`
}

type CertificateDetailResponse struct {
	ID             string                   `json:"id"`
	User           *UserCertificateProfile  `json:"user"`
	Course         *models.SimpleCourse     `json:"course"`
	Files          []*models.File           `json:"files"`
	Image          *models.File             `json:"image"`
	MintNFTEnabled bool                     `json:"mint_nft_enabled"`
	NftTokenID     string                   `json:"nft_token_id"`
	NftTxHash      string                   `json:"nft_tx_hash"`
	NftNetwork     models.BlockchainNetwork `json:"nft_network"`
	Props          models.CertificateProps  `json:"props"`
}

type UserCertificateProfile struct {
	ID          string `json:"id"`
	Username    string `json:"username"`
	DisplayName string `json:"display_name"`
	Avatar      string `json:"avatar"`
	TotalPoints int    `json:"total_points"`
	CompletedAt int    `json:"completed_at"`
}

type CertificateNFTFeesResponse struct {
	MintNFTEnabled       bool                     `json:"mint_nft_enabled"`
	GasFeePayerInSetting models.Participant       `json:"gas_fee_payer_in_setting"`
	ActualGasFeePayer    models.Participant       `json:"actual_gas_fee_payer"`
	EstimatedFee         decimal.Decimal          `json:"estimated_fee"`
	SponsorBalance       decimal.Decimal          `json:"sponsor_balance"`
	Network              models.BlockchainNetwork `json:"network"`
}

type MintCertificateNFTRequest struct {
	Certificate *models.Certificate 	 `json:"-"`
	User        *models.User        	 `json:"-"`
	Network     models.BlockchainNetwork `json:"network"`
	GasFeePayer models.Participant  	 `json:"gas_fee_payer"`
}

type ClaimCertificateRequest struct {
	Org                *models.Organization
	User               *models.User
	Cert               *models.Certificate
	Course             *models.Course
	CheckCertCondition bool
}

type IssueCertificateRequest struct {
	UserID      string       `json:"user_id" validate:"required"`
	CourseCuid  string       `json:"course_cuid" validate:"required"`
	File        *models.File `json:"file" validate:"required"`
	Image       *models.File `json:"image"`
	CompletedAt int          `json:"completed_at" validate:"required"`
}

type CheckCertificateConditionRequest struct {
	CourseCuid string `json:"course_cuid" validate:"required"`
}

type CompletedCourseConditionResponse struct {
	Enable              bool    `json:"enable"`
	Passed              bool    `json:"passed"`
	RequiredPercentage  float64 `json:"required_percentage"`
	CompletedPercentage float64 `json:"completed_percentage"`
}

type CompletedAllQuizzesConditionResponse struct {
	Enable bool `json:"enable"`
	Passed bool `json:"passed"`
}

type CompletedFinalQuizConditionResponse struct {
	Enable             bool    `json:"enable"`
	Passed             bool    `json:"passed"`
	RequiredPercentage float64 `json:"required_percentage"`
}

type RequiredSection struct {
	ID    string `json:"id"`
	UID   string `json:"uid"`
	Title string `json:"title"`
}

type RequiredLesson struct {
	ID          string           `json:"id"`
	UID         string           `json:"uid"`
	Title       string           `json:"title"`
	Section     *RequiredSection `json:"section"`
	CompletedAt int64            `json:"completed_at"`
}

type CompletedRequiredLessonsConditionResponse struct {
	Enable  bool              `json:"enable"`
	Passed  bool              `json:"passed"`
	Lessons []*RequiredLesson `json:"lessons"`
}

type CheckCertificateConditionResponse struct {
	IsReceived               bool                                       `json:"is_received"`
	CanReceive               bool                                       `json:"can_receive"`
	CompletedCourse          *CompletedCourseConditionResponse          `json:"completed_course"`
	CompletedAllQuizzes      *CompletedAllQuizzesConditionResponse      `json:"completed_all_quizzes"`
	CompletedFinalQuiz       *CompletedFinalQuizConditionResponse       `json:"completed_final_quiz"`
	CompletedRequiredLessons *CompletedRequiredLessonsConditionResponse `json:"completed_required_lessons"`
}

type ClaimCertificateRequest struct {
	Org                *models.Organization
	User               *models.User
	Cert               *models.Certificate
	Course             *models.Course
	CheckCertCondition bool
}

type IssueCertificateRequest struct {
	UserID      string       `json:"user_id" validate:"required"`
	CourseCuid  string       `json:"course_cuid" validate:"required"`
	File        *models.File `json:"file" validate:"required"`
	Image       *models.File `json:"image"`
	CompletedAt int          `json:"completed_at" validate:"required"`
}

type CheckCertificateConditionRequest struct {
	CourseCuid string `json:"course_cuid" validate:"required"`
}

type CompletedCourseConditionResponse struct {
	Enable              bool    `json:"enable"`
	Passed              bool    `json:"passed"`
	RequiredPercentage  float64 `json:"required_percentage"`
	CompletedPercentage float64 `json:"completed_percentage"`
}

type CompletedAllQuizzesConditionResponse struct {
	Enable bool `json:"enable"`
	Passed bool `json:"passed"`
}

type CompletedFinalQuizConditionResponse struct {
	Enable             bool    `json:"enable"`
	Passed             bool    `json:"passed"`
	RequiredPercentage float64 `json:"required_percentage"`
}

type RequiredSection struct {
	ID    string `json:"id"`
	UID   string `json:"uid"`
	Title string `json:"title"`
}

type RequiredLesson struct {
	ID          string           `json:"id"`
	UID         string           `json:"uid"`
	Title       string           `json:"title"`
	Section     *RequiredSection `json:"section"`
	CompletedAt int64            `json:"completed_at"`
}

type CompletedRequiredLessonsConditionResponse struct {
	Enable  bool              `json:"enable"`
	Passed  bool              `json:"passed"`
	Lessons []*RequiredLesson `json:"lessons"`
}

type CheckCertificateConditionResponse struct {
	IsReceived               bool                                       `json:"is_received"`
	CanReceive               bool                                       `json:"can_receive"`
	CompletedCourse          *CompletedCourseConditionResponse          `json:"completed_course"`
	CompletedAllQuizzes      *CompletedAllQuizzesConditionResponse      `json:"completed_all_quizzes"`
	CompletedFinalQuiz       *CompletedFinalQuizConditionResponse       `json:"completed_final_quiz"`
	CompletedRequiredLessons *CompletedRequiredLessonsConditionResponse `json:"completed_required_lessons"`
}
