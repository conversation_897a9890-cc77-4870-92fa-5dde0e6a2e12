package dto

import "openedu-core/models"

type FeaturedContent struct {
	EntityID string `json:"entity_id"`
	Order    int    `json:"order"`
}

type BulkUpdateFeaturedContent struct {
	OrgID      string                     `json:"org_id"`
	Type       models.FeaturedContentType `json:"type"`
	EntityType models.ModelName           `json:"entity_type"`
	Entities   []FeaturedContent          `json:"entities"`
}

type ListFeaturedContentResponse struct {
	Results    []*models.FeaturedContent `json:"results"`
	Pagination *models.Pagination        `json:"pagination"`
}

type FindByTypeAndEntityRequest struct {
	OrgID      string                     `json:"org_id" form:"org_id"`
	Type       models.FeaturedContentType `json:"type" form:"type"`
	EntityType models.ModelName           `json:"entity_type" form:"entity_type"`
}
