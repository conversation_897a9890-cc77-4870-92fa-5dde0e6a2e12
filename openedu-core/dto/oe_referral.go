package dto

import "openedu-core/models"

type OEReferralRequest struct {
	Referrer  *models.User              `json:"referrer"`
	Referee   *models.User              `json:"referee"`
	Trigger   models.OEReferralTrigger  `json:"trigger"`
	Campaigns []*models.OEPointCampaign `json:"campaigns"`
	Code      *models.OEReferralCode    `json:"code"`
}

type OEReferralInviteRefereeRequest struct {
	ReferralType models.ReferralType `json:"referral_type"`
	Emails       []string            `json:"emails"`
}

type ListOEReferralResponse struct {
	Results    []*models.OEReferral `json:"results"`
	Pagination *models.Pagination   `json:"pagination"`
}
