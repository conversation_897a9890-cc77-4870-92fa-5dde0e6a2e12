package dto

import "openedu-core/models"

type CreateQuizSubmissionRequest struct {
	CourseID string `json:"course_id" validate:"required"`
	QuizID   string `json:"quiz_id" validate:"required"`
}

type QuizQuestionSubmissionRequest struct {
	QuestionID       string                       `json:"question_id"`
	AnsweredItemSets [][]*QuizQuestionItemRequest `json:"answered_item_sets"`
}

type GetQuestionSubmissionResponse struct {
	HasNextQuestion      bool                 `json:"has_next_question"`
	StartAt              int64                `json:"start_at"`
	CurrentQuestionIndex int                  `json:"current_question_index"`
	Question             *models.QuizQuestion `json:"question"`
}

type GetQuizSubmissionRanksRequest struct {
	Top int `json:"top" form:"top"`
}

type ListQuizSubmissionsResponse struct {
	Results    []*models.SimpleQuizSubmission `json:"results"`
	Pagination *models.Pagination             `json:"pagination"`
}

type UpdateQuizSubmissionStatusRequest struct {
	Status string `json:"status" validate:"required"`
}

type QuizSubmissionSummary struct {
	QuizID       string `json:"quiz_id"`
	TotalSubmits int    `json:"total_submits"`
}
