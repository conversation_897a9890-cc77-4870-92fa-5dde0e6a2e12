package dto

import (
	"openedu-core/models"

	"github.com/shopspring/decimal"
)

type CreateCouponRequest struct {
	Name        string              `json:"name,omitempty" validate:"required"`
	Description string              `json:"description,omitempty"`
	OrgID       string              `json:"org_id,omitempty" validate:"orgExists"`
	CouponCode  string              `json:"coupon_code,omitempty" validate:"required"`
	Type        models.CouponType   `json:"type,omitempty" validate:"required,discountCoupon"`
	Method      models.CouponMethod `json:"method,omitempty" validate:"required"`
	StartDate   int64               `json:"start_date,omitempty"`
	EndDate     int64               `json:"end_date,omitempty"`

	FiatDiscountEnabled      bool            `json:"fiat_discount_enabled"`
	FiatDiscountPercentage   decimal.Decimal `json:"fiat_discount_percentage,omitempty"`
	FiatMinAmountToUse       decimal.Decimal `json:"fiat_min_amount_to_use,omitempty"`
	FiatAllowMaximumDiscount decimal.Decimal `json:"fiat_allow_maximum_discount,omitempty" validate:"flatCoupon"`
	//DiscountAmount       decimal.Decimal `json:"discount_amount,omitempty"`

	CryptoDiscountEnabled      bool            `json:"crypto_discount_enabled"`
	CryptoDiscountPercentage   decimal.Decimal `json:"crypto_discount_percentage"`
	CryptoMinAmountToUse       decimal.Decimal `json:"crypto_min_amount_to_use"`
	CryptoAllowMaximumDiscount decimal.Decimal `json:"crypto_allow_maximum_discount"`
	//CryptoDiscountAmount       decimal.Decimal `json:"crypto_discount_amount"`

	MaximumTotalUsage int16 `json:"maximum_total_usage,omitempty"`

	AllowCourses []string `json:"allow_courses,omitempty" validate:"courseListExists"`
	AllowTeams   []string `json:"allow_teams,omitempty"`
	IsActive     bool     `json:"is_active,omitempty"`
}

type UpdateCouponRequest struct {
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	CouponCode  string `json:"coupon_code,omitempty"`
	Type        string `json:"type,omitempty"`
	OrgID       string `json:"org_id,omitempty"`
	Method      string `json:"method,omitempty"`
	StartDate   int64  `json:"start_date,omitempty"`
	EndDate     int64  `json:"end_date,omitempty"`

	FiatDiscountEnabled      bool            `json:"fiat_discount_enabled"`
	FiatDiscountPercentage   decimal.Decimal `json:"fiat_discount_percentage,omitempty"`
	FiatMinAmountToUse       decimal.Decimal `json:"fiat_min_amount_to_use,omitempty"`
	FiatAllowMaximumDiscount decimal.Decimal `json:"fiat_allow_maximum_discount,omitempty" validate:"flatCoupon"`
	//DiscountAmount       decimal.Decimal `json:"discount_amount,omitempty"`

	CryptoDiscountEnabled      bool            `json:"crypto_discount_enabled"`
	CryptoDiscountPercentage   decimal.Decimal `json:"crypto_discount_percentage"`
	CryptoMinAmountToUse       decimal.Decimal `json:"crypto_min_amount_to_use"`
	CryptoAllowMaximumDiscount decimal.Decimal `json:"crypto_allow_maximum_discount"`
	//CryptoDiscountAmount       decimal.Decimal `json:"crypto_discount_amount"`

	MaximumTotalUsage int16 `json:"maximum_total_usage,omitempty"`

	AllowCourses []string `json:"allow_courses,omitempty"`
	AllowTeams   []string `json:"allow_teams,omitempty"`
	IsActive     bool     `json:"is_active,omitempty"`
}

type UseCouponRequest struct {
	CouponCode string `json:"coupon_code,omitempty" validate:"required"`
	CourseID   string `json:"course_id,omitempty" validate:"required"`
}

type ListCouponResponse struct {
	Results    []*models.SimpleCoupon `json:"results"`
	Pagination *models.Pagination     `json:"pagination"`
}
