package dto

import "openedu-core/models"

type AffiliateCampaignReferrerRequest struct {
	CampaignID string            `json:"campaign_id"`
	Referrers  []ReferrerRequest `json:"referrers"`

	Org      *models.Organization
	Campaign *models.AffiliateCampaign
}

type InviteReferrerResponse struct {
	Email  string               `json:"email"`
	Type   models.ReferrerType  `json:"type"`
	Status models.DefaultStatus `json:"status"`
}

type ReferrerRequest struct {
	ID     *string             `json:"id"`
	UserID *string             `json:"user_id"`
	Email  string              `json:"email"`
	Type   models.ReferrerType `json:"type"`
	Enable bool                `json:"enable"`
}

type RemoveReferrerRequest struct {
	CampaignID string   `json:"campaign_id" validate:"required" form:"campaign_id"`
	IDs        []string `json:"ids" form:"ids" form:"ids"`
}

type ListReferrerResponse struct {
	Results    []*models.Referrer `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}
