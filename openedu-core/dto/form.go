package dto

import "openedu-core/models"

type FormQuestionOptionParams struct {
	ID   string `json:"id,omitempty" default:""`
	Text string `json:"text" default:"Rice"`
}

type FormSubQuestionParams struct {
	ID          string `json:"id,omitempty" default:""`
	Title       string `json:"title" default:"Your sub question?"`
	Description string `json:"description" default:"Sub question description here"`
}

type FormQuestionSettingParams struct {
	IsDefault          bool                   `json:"is_default" default:"false"`
	Required           bool                   `json:"required" default:"false"`
	OtherOptionEnabled bool                   `json:"other_option_enabled" default:"false"`
	OtherOptionLabel   string                 `json:"other_option_label"`
	Key                models.FormQuestionKey `json:"key"`
	BaseDomain         string                 `json:"base_domain" default:"openedu101.com"`
	Props              models.JSONB           `json:"props"`
}

type FormQuestionParams struct {
	ID           string                      `json:"id,omitempty" default:""`
	Title        string                      `json:"title" validate:"required" default:"What's your favorite food?"`
	Description  string                      `json:"description" default:"Please enter your favorite food?"`
	QuestionType string                      `json:"question_type" validate:"required" default:"checkbox" enums:"text,long_text,number,email,phone,dropdown,checkbox,multiple_choice,file,checkbox_grid,multiple_choice_grid,sub_domain"`
	SubQuestions []*FormSubQuestionParams    `json:"sub_questions"`
	Options      []*FormQuestionOptionParams `json:"options"`
	Settings     FormQuestionSettingParams   `json:"settings"`
	Order        *int                        `json:"-"`
}

type CreateFormRequest struct {
	Title        string                `json:"title" default:"Form Title"`
	Description  string                `json:"description" default:"Form Description"`
	Event        string                `json:"event" validate:"required" default:"register_course" enums:"register_organization,register_instructor,register_course"`
	Type         string                `json:"type" validate:"required" default:"page" enums:"slide,page"`
	StartDate    int                   `json:"start_date" default:"0"`
	EndDate      int                   `json:"end_date" default:"1719939600000"`
	AuthRequired bool                  `json:"auth_required" default:"false"`
	IsTemplate   bool                  `json:"is_template" default:"false"`
	Questions    []*FormQuestionParams `json:"questions" validate:"required"`
}

type FormAnswerParams struct {
	QuestionID    string   `json:"question_id"`
	SubQuestionID *string  `json:"sub_question_id"`
	AnswerText    string   `json:"answer_text"`
	AnswerFiles   []string `json:"answer_files"`
	Options       []string `json:"options"`
}

type SubmitFormRequest struct {
	FormRelationID *string             `json:"form_relation_id"`
	Answers        []*FormAnswerParams `json:"answers"`
}

type UpdateFormRequest struct {
	Title        string                `json:"title" default:"Form Title"`
	Description  string                `json:"description" default:"What's your favorite food?"`
	Event        string                `json:"event" validate:"required" default:"register_course" enums:"register_organization,register_instructor,register_course"`
	Type         string                `json:"type" validate:"required" default:"page" enums:"slide,page"`
	Status       *string               `json:"status" default:"status" enums:"draft,published_all"`
	StartDate    int                   `json:"start_date"`
	EndDate      int                   `json:"end_date"`
	AuthRequired bool                  `json:"auth_required"`
	IsTemplate   *bool                 `json:"is_template" default:"false"`
	Questions    []*FormQuestionParams `json:"questions" validate:"required"`
}

type UpdateFormStatusRequest struct {
	Status string `json:"status"`
}

type ListFormSessionResponse struct {
	Results    []*models.SimpleFormSession `json:"results"`
	Pagination *models.Pagination          `json:"pagination"`
}

type RejectFormSession struct {
	Reason string `json:"reason"`
}

type RejectFormSessionCreator struct {
	FormSessionID *string `json:"form_session_id" validate:"required"`
	Reason        string  `json:"reason" `
}

type ListFormResponse struct {
	Results    []*models.Form     `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}

type CheckFormSubmittedResponse struct {
	Submitted bool `json:"submitted"`
}
