package dto

import "openedu-core/models"

type UserTrackingStatsCountResp struct {
	Email       string `json:"email"`
	Username    string `json:"username"`
	DisplayName string `json:"display_name"`
	Phone       string `json:"phone"`
	RefCount    int64  `json:"ref_count"`
	Network     string `json:"network"`
	Address     string `json:"address"`
}
type ListUserTrackingStatsCountResp struct {
	Results    []*UserTrackingStatsCountResp `json:"results"`
	Pagination *models.Pagination            `json:"pagination"`
}
