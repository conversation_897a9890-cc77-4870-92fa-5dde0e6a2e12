package dto

import (
	"openedu-core/models"
)

type QuizRequest struct {
	OrgID             string                       `json:"org_id"`
	ID                string                       `json:"id"`
	UID               *string                      `json:"-"`
	Version           *int                         `json:"-"`
	RelatedEntityType models.QuizRelationEntity    `json:"related_entity_type"`
	RelatedEntityID   string                       `json:"related_entity_id"`
	RelationType      models.QuizRelationType      `json:"relation_type"`
	TriggerConditions QuizTriggerConditionsRequest `json:"trigger_conditions"`
	Title             string                       `json:"title" validate:"max=255"`
	Description       string                       `json:"description"`
	Files             []*models.SimpleFile         `json:"files"`
	Settings          QuizSettingsRequest          `json:"settings"`
	Questions         []*QuizQuestionRequest       `json:"questions"`
}

type QuizQuestionRequest struct {
	OrgID           string                       `json:"org_id"`
	ID              string                       `json:"id"`
	CreateAt        int                          `json:"create_at"`
	UpdateAt        int                          `json:"update_at"`
	DeleteAt        int                          `json:"delete_at"`
	Title           string                       `json:"title"`
	Description     string                       `json:"description"`
	Text            string                       `json:"text"`
	Files           []*models.SimpleFile         `json:"files" gorm:"-"`
	Type            models.QuizQuestionType      `json:"type"`
	Explanation     string                       `json:"explanation"`
	Points          int                          `json:"points"`
	Items           []*QuizQuestionItemRequest   `json:"items"`
	CorrectItemSets [][]*QuizQuestionItemRequest `json:"correct_item_sets,omitempty"`
	Settings        QuizQuestionSettingsRequest  `json:"settings"`
}

type QuizSettingsRequest struct {
	ShowCorrectAnswersEnabled      bool                       `json:"show_correct_answers_enabled"`
	ShuffleQuestionsEnabled        bool                       `json:"shuffle_questions_enabled"`
	ShuffleChoicesEnabled          bool                       `json:"shuffle_choices_enabled"`
	TimeLimitEnabled               bool                       `json:"time_limit_enabled"`
	TimeLimitType                  models.QuizTimeLimitType   `json:"time_limit_type"` // overall, per_question
	TimeLimit                      models.Duration            `json:"time_limit"`
	SubmissionLimitEnabled         bool                       `json:"submission_limit_enabled"`
	SubmissionLimit                int                        `json:"submission_limit"`
	PassCriteria                   models.QuizPassCriteria    `json:"pass_criteria"`
	MinPercentageToPass            float64                    `json:"min_percentage_to_pass"`
	MinCorrectAnswersToPass        int                        `json:"min_correct_answers_to_pass"`
	TimeBonusPointsEnabled         bool                       `json:"time_bonus_points_enabled"`
	TimeBonusPointsPerSecond       int                        `json:"time_bonus_points_per_second"`
	StreakBonusEnabled             bool                       `json:"streak_bonus_enabled"`
	StreakBonusType                models.QuizStreakBonusType `json:"streak_bonus_type"` // percentage, points
	StreakBonusPercentageIncrement float64                    `json:"streak_bonus_percentage_increment"`
	StreakBonusMaxPercentage       float64                    `json:"streak_bonus_max_percentage"`
	PenaltyPointsEnabled           bool                       `json:"penalty_points_enabled"`
	PenaltyPointsPerWrongAnswer    int                        `json:"penalty_points_per_wrong_answer"`
}

type QuizQuestionSettingsRequest struct {
	TimeLimit models.Duration `json:"time_limit"`
}

type QuizTriggerConditionsRequest struct {
	IsTriggeredByTimestamp     bool    `json:"is_triggered_by_timestamp"`
	Timestamp                  string  `json:"timestamp"`
	IsTriggerByReachPageNumber bool    `json:"is_trigger_by_reach_page_number"`
	PageNumber                 int     `json:"page_number"`
	ShowAtPercentage           float64 `json:"show_at_percentage"`
}

type QuizRelationRequest struct {
	RelatedEntityType models.QuizRelationEntity    `json:"related_entity_type" validate:"required"`
	RelatedEntityID   string                       `json:"related_entity_id" validate:"required"`
	RelationType      models.QuizRelationType      `json:"relation_type" validate:"required"`
	TriggerConditions QuizTriggerConditionsRequest `json:"trigger_conditions"`
}

type QuizQuestionItemRequest struct {
	ID     string             `json:"id"`
	Text   string             `json:"text,omitempty"`
	FileID string             `json:"file_id,omitempty"`
	File   *models.SimpleFile `json:"file,omitempty"`
	Side   int                `json:"side,omitempty"`
}
