package dto

import (
	"openedu-core/models"
	"openedu-core/pkg/e"
)

type ValidateReferralCodeRequest struct {
	CourseCuid   string `json:"course_cuid" validate:"required"`
	ReferralCode string `json:"referral_code"`
}

type CreateOrderRequest struct {
	CourseID        string          `json:"course_id" validate:"required"`
	CourseCuid      string          `json:"course_cuid" validate:"required"`
	PaymentMethodID string          `json:"payment_method_id,omitempty"`
	CouponCode      *string         `json:"coupon_code,omitempty"`
	Currency        models.Currency `json:"currency,omitempty"`
	ReferralCode    *string         `json:"referral_code"`
	Source          string          `json:"source"`

	Org *models.Organization
}

type VerifyPayment struct {
	PaymentMethodID string `json:"payment_method_id" validate:"required"`
}

type UpdateOrderRequest struct {
	PaymentMethodID *string             `json:"payment_method_id"`
	Status          *models.OrderStatus `json:"status"`
	Currency        *models.Currency    `json:"currency"`
}

type ListOrdersResponse struct {
	Results    []*models.SimpleOrder `json:"results"`
	Pagination *models.Pagination    `json:"pagination"`
}

type OrderPaymentResponse struct {
	Order         models.Order          `json:"order"`
	PaymentUrl    *string               `json:"payment_url"`
	PaymentMethod *models.PaymentMethod `json:"payment_method"`
	ReferralError *e.AppError           `json:"referral_error"`
}

type UseCouponParams struct {
	ID   string `uri:"id" binding:"required,uuid"`
	Name string `uri:"name" binding:"required"`
}

type PayOrderRequest struct {
	WalletID string `json:"wallet_id"`
}

type CheckOrderStatusResponse struct {
	Status models.OrderStatus `json:"status"`
}
