package dto

import "openedu-core/models"

type CreateLearningProgressParams struct {
	UserID           string
	CourseSlug       string
	SectionUID       string
	LessonUID        string
	LessonContentUID string
	CompleteAt       int64
	StartAt          int64
	PauseAt          int64
	TextPercent      float64
	PdfCurrentPage   int
	Quizzes          []*models.QuizLearningProgress
	OrgID            string
	Org              *models.Organization
	User             *models.User
}

type CreateLearningProgressRequest struct {
	CourseCuid       string                         `json:"course_cuid"`
	CourseSlug       string                         `json:"course_slug" validated:"required"`
	SectionUID       string                         `json:"section_uid" validated:"required"`
	LessonUID        string                         `json:"lesson_uid" validated:"required"`
	LessonContentUID string                         `json:"lesson_content_uid" `
	CompleteAt       int64                          `json:"complete_at,omitempty"`
	StartAt          int64                          `json:"start_at,omitempty"`
	PauseAt          int64                          `json:"pause_at,omitempty"`
	TextPercent      float64                        `json:"text_percent"`
	PdfCurrentPage   int                            `json:"pdf_current_page"`
	Quizzes          []*models.QuizLearningProgress `json:"quizzes"`
	Event            models.ProgressEvent           `json:"event,omitempty" validate:"validateLPEventLesson"`
}

type CreateEventLearningProgressParams struct {
	UserID     string
	SectionUID string
	LessonUID  string
	OrgID      string
	Course     *models.Course
}

type CheckUserCompleteCourse struct {
	CourseCuid string                       `json:"course_cuid" validate:"required" form:"course_cuid"`
	UserIDs    []string                     `json:"user_ids" validate:"required" form:"user_ids"`
	Status     *models.CourseProgressStatus `json:"status" form:"status"`
}

type CheckUserCompleteCourseResp struct {
	UserID string                      `json:"user_id"`
	Status models.CourseProgressStatus `json:"is_complete"`
}

type UpdateCurrentLessonRequest struct {
	UserID            string
	CourseSlug        string
	CurrentSectionUID string
	CurrentLessonUID  string
	OrgID             string
}

type MigrateLearningStatusStartAtRequest struct {
	CourseCUIDs []string `json:"course_cuids"`
}
