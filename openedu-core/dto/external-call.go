package dto

import (
	"openedu-core/models"
)

type ExBlogResp struct {
	models.Model
	AuthorID      string          `json:"author_id,omitempty"`
	Cuid          string          `json:"cuid,omitempty" `
	Title         string          `json:"title,omitempty"`
	BlogType      models.BlogType `json:"blog_type,omitempty"`
	Slug          string          `json:"slug,omitempty"`
	PubDate       int             `json:"pub_date" `
	PubRejectDate int             `json:"pub_reject_date" `
	View          int             `json:"view" `
}

type ListExBlogsResponse struct {
	Results    []*ExBlogResp      `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}

type ExUserResp struct {
	ID            string            `json:"id"`
	Username      string            `json:"username"`
	Email         string            `json:"email"`
	Active        bool              `json:"active"`
	Blocked       bool              `json:"blocked"`
	Avatar        string            `json:"avatar"`
	DisplayName   string            `json:"display_name"`
	Phone         string            `json:"phone"`
	TotalBlogView int               `json:"total_blog_view"`
	FollowStatus  models.ActionType `json:"follow_status" `
	Followers     int64             `json:"followers" `
}

type ListExUsersResponse struct {
	Results    []*ExUserResp      `json:"results"`
	Pagination *models.Pagination `json:"pagination"`
}

type ListTrackingResponse struct {
	Results    []*TrackingResponse `json:"results"`
	Pagination *models.Pagination  `json:"pagination"`
}

type TrackingResponse struct {
	ID           string                 `json:"id"`
	ActorID      string                 `json:"actor_id"`
	Verb         models.VerbType        `json:"verb"`
	Object       models.ModelName       `json:"object"`
	ObjectID     string                 `json:"object_id"`
	Context      models.TrackingContext `json:"context"`
	ContextValue any                    `json:"context_value"`
	OrgID        string                 `json:"org_id"`
	OrgSchema    string                 `json:"org_schema"`
	CreateAt     int64                  `json:"create_at" `
	UpdateAt     int64                  `json:"update_at" `
	DeleteAt     int64                  `json:"delete_at" `
}

type UpdateRefTrackingRequest struct {
	ActorID      string `json:"actor_id" validate:"required"`
	ContextValue string `json:"context_value"`
	OrgID        string `json:"org_id" validate:"required"`
}

type TrackingRequest struct {
	ActorID      string                 `json:"actor_id" validate:"required"`
	Verb         models.VerbType        `json:"verb" validate:"required"`
	Object       models.ModelName       `json:"object" validate:"required"`
	ObjectID     string                 `json:"object_id" validate:"required"`
	Context      models.TrackingContext `json:"context" validate:"required"`
	ContextValue any                    `json:"context_value"`
	OrgID        string                 `json:"org_id" validate:"required"`
	OrgSchema    string                 `json:"org_schema" validate:"required"`
	IsValid      bool                   `json:"is_valid"`
}

type ExCountRefUserResp struct {
	UserID   string `json:"user_id"`
	RefCount int64  `json:"count"`
}

type ListRefCountResponse struct {
	Results    []*ExCountRefUserResp `json:"results"`
	Pagination *models.Pagination    `json:"pagination"`
}
