package dto

import (
	"github.com/shopspring/decimal"
	"openedu-core/models"
)

type ListTransactionResponse struct {
	Results    []*models.Transaction `json:"results"`
	Pagination *models.Pagination    `json:"pagination"`
}

type CreateWithdrawRequest struct {
	User          *models.User
	Wallet        *models.Wallet
	Org           *models.Organization
	Amount        decimal.Decimal `json:"amount" validate:"required"`
	BankAccountID string          `json:"bank_account_id" validate:"required"`
	Note          string          `json:"note"`
}

type WalletWithdrawalRequest struct {
	User       *models.User
	Wallet     *models.Wallet
	Org        *models.Organization
	Type       models.WalletWithdrawalType `json:"type"`
	Currency   models.Currency             `json:"currency" validate:"required"`
	Amount     decimal.Decimal             `json:"amount" validate:"required"`
	ToAddress  string                      `json:"to_address" validate:"required"`
	Network    models.BlockchainNetwork    `json:"network" validate:"required,blockchain_network"`
	ContractID string                      `json:"contract_id"`
	IsMainnet  bool                        `json:"is_mainnet"`
	Note       string                      `json:"note"`
}

type CreateTransactionRequest struct {
	OrgID          string
	Wallet         *models.Wallet
	ToAddress      string
	Network        models.BlockchainNetwork
	ContractID     string
	Currency       models.Currency
	Amount         decimal.Decimal
	EntityType     models.ModelName
	EntityID       string
	Note           string
	TxHash         string
	IsMainnet      bool
	Files          []*models.File
	BlockchainTxID string
}

type WalletSyncRequest struct {
	ID           string          `json:"id"`
	UserID       string          `json:"user_id"`
	Address      string          `json:"address"`
	PublicKey    string          `json:"public_key"`
	Type         string          `json:"type"`
	Status       string          `json:"status"`
	Balance      decimal.Decimal `json:"balance"`
	CoreWalletID string          `json:"core_wallet_id"`
}

type SponsorWalletSyncRequest struct {
	ID          string `json:"id"`
	SponsorID   string `json:"sponsor_id"`
	SponsorName string `json:"sponsor_name"`
	WalletID    string `json:"wallet_id"`
	Network     string `json:"network"`
	Status      string `json:"status"`
	CoreTxID    string `json:"core_tx_id"`
}

type ClaimEarningsRequest struct {
	Org    *models.Organization
	User   *models.User
	Wallet *models.Wallet
}

type RetrieveWalletDetailsRequest struct {
	Type models.RetrieveWalletDetailsType `json:"type"`
	Data interface{}                      `json:"data"`
}

type CreateSponsorWalletRequest struct {
	User   *models.User         `json:"-"`
	Wallet *models.Wallet       `json:"-"`
	Org    *models.Organization `json:"-"`

	Amount      decimal.Decimal          `json:"amount" validate:"required"`
	Description string                   `json:"description"`
	Network     models.BlockchainNetwork `json:"network" validate:"required,blockchain_network"`
}

type CreateSponsorWalletResponse struct {
	SponsorWalletID string `json:"sponsor_wallet_id"`
	TransactionID   string `json:"transaction_id"`
	Status          string `json:"status"`
	Message         string `json:"message"`
}

type GetSponsorWalletResponse struct {
	SponsorWalletID string          `json:"sponsor_wallet_id"`
	WalletAddress   string          `json:"wallet_address"`
	Network         string          `json:"network"`
	Balance         decimal.Decimal `json:"balance"`
	Token           string          `json:"token"`
	Status          string          `json:"status"`
	CreatedAt       string          `json:"created_at,omitempty"`
	LastUpdated     string          `json:"last_updated,omitempty"`
}

type RetrieveWalletEarningsRequest struct {
	UserID   string                   `json:"user_id"`
	Earnings []*WalletEarningsRequest `json:"earnings"`
}

type WalletEarningsRequest struct {
	BlockchainWalletID string                   `json:"blockchain_wallet_id"`
	CoreWalletID       string                   `json:"core_wallet_id"`
	Address            string                   `json:"address"`
	Network            models.BlockchainNetwork `json:"network" validate:"blockchain_network,omitempty"`
	Currency           models.Currency          `json:"currency"`
	TokenID            string                   `json:"token_id"`
	Amount             decimal.Decimal          `json:"amount"`
}

type GetAccountInfoRequest struct {
	Network   models.BlockchainNetwork `json:"network"`
	Address   string                   `json:"address"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type GetAccountInfoResponse struct {
	Network     models.BlockchainNetwork `json:"network"`
	Address     string                   `json:"address"`
	AccountInfo interface{}              `json:"account_info"`
}

type InitWalletRequest struct {
	UserID            string                   `json:"user_id"`
	Type              models.AssetType         `json:"type"`
	Currency          models.Currency          `json:"currency"`
	Network           models.BlockchainNetwork `json:"network"`
	IsDefault         bool                     `json:"is_default"`
	IsEarningOffChain bool                     `json:"is_earning_off_chain"`
}
