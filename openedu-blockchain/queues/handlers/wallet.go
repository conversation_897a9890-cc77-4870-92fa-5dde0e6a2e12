package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/openedu_core"
	"openedu-blockchain/pkg/queue/consumer"
	"openedu-blockchain/services"
	"time"
)

const (
	CreateWalletWorkerName        = "Queue.CreateWallet"
	CreateSponsorWalletWorkerName = "Queue.CreateSponsorWallet"
	WalletQueryHandlerWorkerNames = "Queue.HandleWalletRPCQuery"
)

func CreateWallet(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", CreateWalletWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			startTime := time.Now()
			if !ok {
				// Channel closed, stop the worker.
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.WalletRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
				continue
			}

			wallet, appErr := services.Wallet.Create(&req)
			if appErr != nil {
				handleErrorRPC(startTime, workerName, msg, appErr)
				continue
			}

			handleSuccessRPC(startTime, workerName, msg, &openedu_core.SyncWalletRequest{
				ID:           wallet.ID,
				UserID:       wallet.UserID,
				Address:      wallet.Address,
				PublicKey:    wallet.PublicKey,
				Type:         wallet.Network.String(),
				Status:       wallet.Status.String(),
				CoreWalletID: wallet.CoreWalletID,
			})
		}
	}
}

func CreateSponsorWallet(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", CreateSponsorWalletWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			startTime := time.Now()
			if !ok {
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.InitSponsorWalletRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
				continue
			}

			transaction, appErr := services.SponsorWallet.InitSponsorWallet(&req)
			if appErr != nil {
				handleErrorRPC(startTime, workerName, msg, appErr)
				continue
			}

			// Sync back to core service
			handleSuccessRPC(startTime, workerName, msg, &openedu_core.SyncSponsorWalletRequest{
				ID:          transaction.ID,
				SponsorID:   req.SponsorID,
				SponsorName: req.SponsorName,
				WalletID:    req.WalletID,
				Network:     req.Network.String(),
				Status:      string(transaction.Status),
				CoreTxID:    req.CoreTxID,
			})
		}
	}
}

func HandleWalletRPCQuery(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", WalletQueryHandlerWorkerNames, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			startTime := time.Now()
			if !ok {
				// Channel closed, stop the worker.
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.RpcQueryRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleError(startTime, workerName, msg, err)
				continue
			}

			switch req.Type {
			case models.QueryTypeWalletEarnings:
				var getEarningsReq dto.GetWalletEarningsRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &getEarningsReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				walletEarnings, appErr := services.Wallet.GetEarningsByUser(&getEarningsReq)
				if appErr != nil {
					handleErrorRPC(startTime, workerName, msg, appErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, walletEarnings)

			case models.QueryTypeWalletAccountInfo:
				var getAccReq dto.GetAccountInfoRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &getAccReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				accInfoResp, appErr := services.Wallet.GetAccountInfo(&getAccReq)
				if appErr != nil {
					handleErrorRPC(startTime, workerName, msg, appErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, accInfoResp)

			case models.QueryTypeWalletGasSponsorBalance:
				var getSponsorBalanceReq dto.GetWalletGasSponsorBalanceRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &getSponsorBalanceReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				sponsorBalance, appErr := services.Wallet.GetGasSponsorBalance(&getSponsorBalanceReq)
				if appErr != nil {
					handleErrorRPC(startTime, workerName, msg, appErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, sponsorBalance)

			case models.QueryTypeSponsorWalletExists:
				var sponsorWalletQuery models.SponsorWalletQuery
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &sponsorWalletQuery); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				_, appErr := services.SponsorWallet.GetSponsorWalletByQuery(&sponsorWalletQuery)
				if appErr != nil {
					if appErr.StatusCode == 404 {
						handleSuccessRPC(startTime, workerName, msg, false)
						continue
					}
					handleErrorRPC(startTime, workerName, msg, appErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, true)

			case models.QueryTypeGetSponsorWallets:
				var getSponsorWalletsReq struct {
					Query   models.SponsorWalletQuery `json:"query"`
					Options models.FindPageOptions    `json:"options"`
				}
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &getSponsorWalletsReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				sponsorWallets, pagination, appErr := services.SponsorWallet.GetSponsorWallets(&getSponsorWalletsReq.Query, &getSponsorWalletsReq.Options)
				if appErr != nil {
					handleErrorRPC(startTime, workerName, msg, appErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, map[string]interface{}{
					"results":    sponsorWallets,
					"pagination": pagination,
				})

			default:
				appErr := e.NewError400(e.WalletInvalidRequest, "Invalid get details type: "+string(req.Type))
				handleErrorRPC(startTime, workerName, msg, appErr)
			}
		}
	}
}
