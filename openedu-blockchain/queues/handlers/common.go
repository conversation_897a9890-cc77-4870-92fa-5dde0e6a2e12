package handlers

import (
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/openedu_core"
	"openedu-blockchain/pkg/queue/consumer"
	"time"
)

func handleError(startTime time.Time, workerName string, msg consumer.Message, err error) {
	if rErr := msg.Reject(); rErr != nil {
		log.Errorf("%s failed to reject message: %v", workerName, rErr)
	}
	log.Errorf("%s failed to process message: %v", workerName, err)

	log.Infof("%s handle message ID %s failed in %v", workerName, msg.GetID(), time.Since(startTime))
}

func handleSuccess(startTime time.Time, workerName string, msg consumer.Message) {
	if err := msg.Ack(); err != nil {
		log.Errorf("%s failed to acknowledge message ID %s: %v", workerName, msg.GetID(), err)
	}
	log.Infof("%s handle message ID %s success in %v", workerName, msg.GetID(), time.Since(startTime))
}

func handleErrorRPC(startTime time.Time, workerName string, msg consumer.Message, appErr *e.AppError) {
	if rErr := msg.Reject(); rErr != nil {
		log.Errorf("%s failed to reject message: %v", workerName, rErr)
	}
	log.Errorf("%s failed to process message: %v", workerName, appErr)

	if rErr := openedu_core.Wallet.ReplyRPC(msg.GetReplyTo(), msg.GetCorrelationID(), app.Response{
		Data: map[string]interface{}{
			"message": appErr.Msg,
		},
		Msg:  e.GetMsg(appErr.ErrCode),
		Code: appErr.ErrCode,
	}); rErr != nil {
		log.Errorf("%s failed to reply RPC result for message ID %s: %v", workerName, msg.GetID(), rErr)
	}

	log.Infof("%s handle message ID %s failed in %v", workerName, msg.GetID(), time.Since(startTime))
}

func handleSuccessRPC(startTime time.Time, workerName string, msg consumer.Message, data any) {
	if err := msg.Ack(); err != nil {
		log.Errorf("%s failed to acknowledge message ID %s: %v", workerName, msg.GetID(), err)
	}

	if err := openedu_core.Wallet.ReplyRPC(msg.GetReplyTo(), msg.GetCorrelationID(), app.Response{
		Data: data,
		Msg:  e.GetMsg(e.Success),
		Code: e.Success,
	}); err != nil {
		log.Errorf("%s failed to reply RPC result for message ID %s: %v", workerName, msg.GetID(), err)
	}

	log.Infof("%s handle message ID %s success in %v", workerName, msg.GetID(), time.Since(startTime))
}
