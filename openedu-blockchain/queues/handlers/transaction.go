package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/queue/consumer"
	"openedu-blockchain/services"
	"time"
)

const (
	CreateTransactionWorkerName = "Queue.CreateTransaction"
)

func CreateTransaction(ctx context.Context, workerID int, msgChan <-chan consumer.Message) {
	workerName := fmt.Sprintf("%s worker ID %d", CreateTransactionWorkerName, workerID)
	defer log.Infof("%s stopped", workerName)
	for {
		select {
		case <-ctx.Done():
			return
		case msg, ok := <-msgChan:
			startTime := time.Now()
			if !ok {
				// Channel closed, stop the worker.
				log.Infof("%s stopping due to closed message channel", workerName)
				return
			}

			log.Infof("%s received message ID %s", workerName, msg.GetID())
			log.Debugf("%s message ID %s data: %s", workerName, msg.GetID(), string(msg.GetBody()))

			var req dto.CreateTransactionRequest
			if err := json.Unmarshal(msg.GetBody(), &req); err != nil {
				handleError(startTime, workerName, msg, err)
				continue
			}

			var appErr *e.AppError
			switch req.Type {
			case models.TxnTypeTransfer:
				var transferReq dto.SingleTransferRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &transferReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				txn, aErr := services.Transaction.Transfer(&transferReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, txn.ToSimple())
				continue

			case models.TxnTypeBatchTransfer:
				var batchTransferReq dto.BatchTransferRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &batchTransferReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				txn, aErr := services.Transaction.BatchTransfer(&batchTransferReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, txn.ToSimple())
				continue

			case models.TxnTypeDepositSponsorGas:
				var depositReq dto.DepositSponsorGasRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &depositReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.DepositNftSponsorGasFee(&depositReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnTypeWithdrawSponsorGas:
				var withdrawReq dto.WithdrawSponsorGasRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &withdrawReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.WithdrawNftSponsorGasFee(&withdrawReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnTypeMintNFT:
				var mintReq dto.MintNFTRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &mintReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.MintNFT(&mintReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnTypePayment:
				var paymentReq dto.PaymentRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleError(startTime, workerName, msg, err)
					continue
				}

				if err = json.Unmarshal(b, &paymentReq); err != nil {
					handleError(startTime, workerName, msg, err)
					continue
				}
				transaction, aErr := services.Transaction.Payment(&paymentReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnTypeClaimEarning:
				var claimReq dto.ClaimEarningRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleError(startTime, workerName, msg, err)
					continue
				}

				if err = json.Unmarshal(b, &claimReq); err != nil {
					handleError(startTime, workerName, msg, err)
					continue
				}

				transaction, aErr := services.Transaction.ClaimEarning(&claimReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnInitLaunchpadPool:
				var initPoolReq dto.InitLaunchpadPoolRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &initPoolReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.InitLaunchpadPool(&initPoolReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnApproveLaunchpadPool:
				var approveReq dto.ApproveLaunchpadPoolRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &approveReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.ApproveLaunchpadPool(&approveReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnUpdateLpPoolFundingTime:
				var updateReq dto.UpdateLpPoolFundingTimeRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &updateReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.UpdateLpPoolFundingTime(&updateReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnPledgeLaunchpad:
				var pledgeReq dto.PledgeLaunchpadRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &pledgeReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.PledgeLaunchpad(&pledgeReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnCancelLaunchpad:
				var cancelReq dto.CancelLpPoolRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &cancelReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.CancelLaunchpadPool(&cancelReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnCheckLpFundingResult:
				var checkReq dto.CheckLpFundingResultRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &checkReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.CheckLpFundingResult(&checkReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, dto.CheckLpFundingResultResponse{
					PoolID: transaction.Props.PoolID,
					Status: transaction.Props.PoolStatus,
				})
				continue

			case models.TxnContinueLpPartialFund:
				var continueReq dto.ContinueLpPartialFundRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &continueReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.ContinueLpPartialFunds(&continueReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnSetLpPoolFundingTime:
				var setFundingTimeReq dto.SetLpFundingTimeRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &setFundingTimeReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.SetLaunchpadFundingTime(&setFundingTimeReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnWithdrawLaunchpadFundToCreator:
				var withdrawReq dto.WithdrawLaunchpadFundToCreatorRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &withdrawReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.WithdrawLaunchpadFundToCreator(&withdrawReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnTypeClaimLaunchpadRefund:
				var claimReq dto.ClaimLaunchpadRefundRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &claimReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.ClaimLaunchpadRefund(&claimReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			case models.TxnUpdateLaunchpadPoolStatus:
				var updateReq dto.UpdateLaunchpadPoolStatusRequest
				b, err := json.Marshal(req.Data)
				if err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				if err = json.Unmarshal(b, &updateReq); err != nil {
					handleErrorRPC(startTime, workerName, msg, e.NewError400(e.InvalidParams, "Bind JSON failed "+err.Error()))
					continue
				}

				transaction, aErr := services.Transaction.UpdateLaunchpadPoolStatus(&updateReq)
				if aErr != nil {
					handleErrorRPC(startTime, workerName, msg, aErr)
					continue
				}

				handleSuccessRPC(startTime, workerName, msg, transaction.ToSimple())
				continue

			default:
				appErr = e.NewError400(e.TransactionInvalidRequest, string("Invalid action type: "+req.Type))
			}

			if appErr != nil {
				handleError(startTime, workerName, msg, appErr)
				continue
			}

			handleSuccess(startTime, workerName, msg)
		}
	}
}
