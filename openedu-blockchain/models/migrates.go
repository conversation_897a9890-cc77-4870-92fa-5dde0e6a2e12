package models

import (
	"fmt"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/setting"
)

var sharedTables = []any{
	&Wallet{},
	&UserSetting{},
	&Transaction{},
	&SponsorWallet{},
}

func MigrateDatabase() {
	if err := DB.AutoMigrate(sharedTables...); err != nil {
		log.Fatalf("models.MigrateDatabase failed to run migration: %v", err)
	}

	// Create DB indexes
	if err := CreateIndexes(); err != nil {
		log.Fatalf("Failed to create or update indexes: %v", err)
	}
}

func CreateIndexes() error {
	prefix := setting.DatabaseSetting.TablePrefix
	indexRawSQLs := []string{
		// Index name convention: idx_{table_name}_{column_name(s)}
		// Example: idx_user_role_orgs_user_id_role_id -> Index for table `user_role_orgs` on columns (`user_id`, `role_id`)
		
		// Index for `address` column in `sponsor_wallets` table
		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_%[1]s_address
			ON %[2]s(address);`, prefix+string(SponsorWalletTbl), GetTblName(SponsorWalletTbl)),
		
		// Composite index for `sponsor_id` and `network` columns in `sponsor_wallets` table
		fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_%[1]s_sponsor_network
			ON %[2]s(sponsor_id, network);`, prefix+string(SponsorWalletTbl), GetTblName(SponsorWalletTbl)),
	}

	uniqIndexRawSQLs := []string{
		// Unique index name convention: {constraint_type}_{table_name}_{column_name(s)}
		// Example: uniq_idx_hashtags_name_org_id -> Unique index for table `hashtags` on columns (`name`, `org_id`)

		// Unique index for `core_wallet_id` column in `wallets` table
		fmt.Sprintf(`CREATE UNIQUE INDEX IF NOT EXISTS uniq_idx_%[1]s_core_wallet_id
			ON %[2]s(core_wallet_id);`, prefix+string(WalletTbl), GetTblName(WalletTbl)),
	}

	for _, rawSQL := range indexRawSQLs {
		result := DB.Exec(rawSQL)
		if err := result.Error; err != nil {
			log.Errorf("Create index result for `%s` error: %v", rawSQL, result)
			return err
		}
	}

	for _, rawSQL := range uniqIndexRawSQLs {
		result := DB.Exec(rawSQL)
		if err := result.Error; err != nil {
			log.Errorf("Create unique index result for `%s` error: %v", rawSQL, result)
			return err
		}
	}

	return nil
}
