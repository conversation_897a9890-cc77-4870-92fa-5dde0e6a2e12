package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Transaction struct {
	Model
	WalletID    string            `json:"wallet_id"`
	FromAddress string            `json:"from_address"`
	FromNetwork BlockchainNetwork `json:"from_network"`
	ToAddress   string            `json:"to_address"`
	ToNetwork   BlockchainNetwork `json:"to_network"`
	Status      TransactionStatus `json:"status"`
	ErrorCode   int               `json:"error_code"`
	Type        TransactionType   `json:"type"`
	TxHash      string            `json:"tx_hash"`
	MethodName  string            `json:"method_name"`
	InputData   JSONB             `json:"input_data" gorm:"type:jsonb"`
	Deposit     decimal.Decimal   `json:"value"`
	Token       BlockchainToken   `json:"token"`
	ContractID  string            `json:"contract_id"`
	GasLimit    uint64            `json:"gas_limit"`
	GasBurnt    uint64            `json:"gas_burnt"`
	Nonce       uint64            `json:"nonce"`
	BlockHash   string            `json:"block_hash"`
	IsMainnet   bool              `json:"is_mainnet"`
	Props       TransactionProps  `json:"props" gorm:"type:jsonb"`
	Response    JSONB             `json:"response" gorm:"type:jsonb"`
}

type TransactionProps struct {
	CoreTxIDs []string `json:"core_tx_ids"`

	Recipients any `json:"recipients,omitempty"`

	NftTokenID   string          `json:"nft_token_id,omitempty"`
	StorageCost  decimal.Decimal `json:"storage_cost,omitempty"`
	GasCost      decimal.Decimal `json:"gas_cost,omitempty"`
	TotalGasCost decimal.Decimal `json:"total_gas_cost,omitempty"`

	PoolID     string `json:"pool_id,omitempty"`
	PoolStatus string `json:"pool_status,omitempty"`
}

func (p TransactionProps) Value() (driver.Value, error) {
	val, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	return string(val), nil
}

func (p *TransactionProps) Scan(input interface{}) error {
	b, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, p)
}

type SimpleTransaction struct {
	Model
	WalletID    string            `json:"wallet_id"`
	FromAddress string            `json:"from_address"`
	FromNetwork BlockchainNetwork `json:"from_network"`
	ToAddress   string            `json:"to_address"`
	ToNetwork   BlockchainNetwork `json:"to_network"`
	Status      TransactionStatus `json:"status"`
	ErrorCode   int               `json:"error_code"`
	Type        TransactionType   `json:"type"`
	TxHash      string            `json:"tx_hash"`
	MethodName  string            `json:"method_name"`
	InputData   JSONB             `json:"input_data" gorm:"type:jsonb"`
	Deposit     decimal.Decimal   `json:"value"`
	Token       BlockchainToken   `json:"token"`
	ContractID  string            `json:"contract_id"`
	GasLimit    uint64            `json:"gas_limit"`
	GasBurnt    uint64            `json:"gas_burnt"`
	Nonce       uint64            `json:"nonce"`
	BlockHash   string            `json:"block_hash"`
	IsMainnet   bool              `json:"is_mainnet"`
	Props       TransactionProps  `json:"props"`
}

func (t *Transaction) ToSimple() *SimpleTransaction {
	return &SimpleTransaction{
		Model:       t.Model,
		WalletID:    t.WalletID,
		FromAddress: t.FromAddress,
		FromNetwork: t.FromNetwork,
		ToAddress:   t.ToAddress,
		ToNetwork:   t.ToNetwork,
		Status:      t.Status,
		ErrorCode:   t.ErrorCode,
		Type:        t.Type,
		TxHash:      t.TxHash,
		MethodName:  t.MethodName,
		InputData:   t.InputData,
		Deposit:     t.Deposit,
		Token:       t.Token,
		ContractID:  t.ContractID,
		GasLimit:    t.GasLimit,
		GasBurnt:    t.GasBurnt,
		Nonce:       t.Nonce,
		BlockHash:   t.BlockHash,
		IsMainnet:   t.IsMainnet,
		Props:       t.Props,
	}
}

type TransactionQuery struct {
	ID   *string   `json:"id" form:"id"`
	IDIn []*string `json:"id_in" form:"id_in"`
}

func (query *TransactionQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if len(query.IDIn) > 0 {
		qb = qb.Where("id IN (?)", query.IDIn)
	}

	return qb
}

func (r *TransactionRepository) Create(wallet *Transaction, trans *gorm.DB) error {
	return create(TransactionTbl, wallet, trans)
}

func (r *TransactionRepository) CreateMany(wallets []*Transaction, trans *gorm.DB) error {
	return createMany(TransactionTbl, wallets, trans)
}

func (r *TransactionRepository) Update(wallet *Transaction, trans *gorm.DB) error {
	return update(TransactionTbl, wallet, trans)
}

func (r *TransactionRepository) FindByID(id string, options *FindOneOptions) (*Transaction, error) {
	return findByID[Transaction](TransactionTbl, id, options)
}

func (r *TransactionRepository) FindOne(query *TransactionQuery, options *FindOneOptions) (*Transaction, error) {
	return findOne[Transaction](TransactionTbl, query, options)
}

func (r *TransactionRepository) FindPage(query *TransactionQuery, options *FindPageOptions) ([]*Transaction, *Pagination, error) {
	return findPage[Transaction](TransactionTbl, query, options)
}

func (r *TransactionRepository) FindMany(query *TransactionQuery, options *FindManyOptions) ([]*Transaction, error) {
	return findMany[Transaction](TransactionTbl, query, options)
}

func (r *TransactionRepository) Count(query *TransactionQuery) (int64, error) {
	return count[Transaction](TransactionTbl, query)
}

func (r *TransactionRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[Transaction](TransactionTbl, id, trans)
}
