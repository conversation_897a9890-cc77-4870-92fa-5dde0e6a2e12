package models

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

const (
	SponsorWalletTbl            TableName = "sponsor_wallets"
	SponsorWalletStatusActive             = "active"
	SponsorWalletStatusInactive           = "inactive"
)

type SponsorWallet struct {
	Wallet                     
	WalletID    string          `json:"wallet_id"`  
	SponsorID   string          `json:"sponsor_id"`
	SponsorName string          `json:"sponsor_name"`
	Description string          `json:"description"`
	Balance     decimal.Decimal `json:"balance"`
	Status      string          `json:"status"` 
}

func (sw *SponsorWallet) IsNetworkBASE() bool {
	return sw.Wallet.Network == NetworkBASE
}

type SanitizedSponsorWallet struct {
	Model
	WalletID    string            `json:"wallet_id"`
	SponsorID   string            `json:"sponsor_id"`
	SponsorName string            `json:"sponsor_name"`
	Description string            `json:"description"`
	Balance     decimal.Decimal   `json:"balance"`
	Network     BlockchainNetwork `json:"network"`
	Status      string            `json:"status"`
	Address     string            `json:"address"`
	UserID       string       `json:"user_id"`
	PublicKey    string       `json:"public_key"`
	WalletStatus WalletStatus `json:"wallet_status"`
	CoreWalletID string       `json:"core_wallet_id"`
}

func (sw *SponsorWallet) ToSanitized() *SanitizedSponsorWallet {
	return &SanitizedSponsorWallet{
		Model:       sw.Model,
		WalletID:    sw.WalletID,
		SponsorID:   sw.SponsorID,
		SponsorName: sw.SponsorName,
		Description: sw.Description,
		Balance:     sw.Balance,
		Network:     sw.Wallet.Network, 
		Status:      sw.Status,         
		Address:     sw.Wallet.Address,
		UserID:       sw.Wallet.UserID,
		PublicKey:    sw.Wallet.PublicKey,
		WalletStatus: sw.Wallet.Status, 
		CoreWalletID: sw.Wallet.CoreWalletID,
	}
}

// SponsorWalletQuery represents query parameters for sponsor wallets
type SponsorWalletQuery struct {
	ID        *string            `json:"id" form:"id"`
	WalletID  *string            `json:"wallet_id" form:"wallet_id"`
	SponsorID *string            `json:"sponsor_id" form:"sponsor_id"`
	Network   *BlockchainNetwork `json:"network" form:"network"`
	Status    *string            `json:"status" form:"status"`
}

// Apply applies the query to the db
func (query *SponsorWalletQuery) Apply(db *gorm.DB) *gorm.DB {
	qb := db

	if query.ID != nil {
		qb = qb.Where("id = ?", *query.ID)
	}

	if query.WalletID != nil {
		qb = qb.Where("wallet_id = ?", *query.WalletID)
	}

	if query.SponsorID != nil {
		qb = qb.Where("sponsor_id = ?", *query.SponsorID)
	}

	if query.Network != nil {
		qb = qb.Where("network = ?", *query.Network)
	}

	if query.Status != nil {
		qb = qb.Where("status = ?", *query.Status)
	}

	return qb
}

type SponsorWalletRepository struct{}

// Create creates a new sponsor wallet
func (r *SponsorWalletRepository) Create(sponsorWallet *SponsorWallet, trans *gorm.DB) error {
	return create(SponsorWalletTbl, sponsorWallet, trans)
}

// Update updates an existing sponsor wallet
func (r *SponsorWalletRepository) Update(sponsorWallet *SponsorWallet, trans *gorm.DB) error {
	return update(SponsorWalletTbl, sponsorWallet, trans)
}

// FindByID finds a sponsor wallet by ID
func (r *SponsorWalletRepository) FindByID(id string, options *FindOneOptions) (*SponsorWallet, error) {
	return findByID[SponsorWallet](SponsorWalletTbl, id, options)
}

// FindOne finds a single sponsor wallet based on query
func (r *SponsorWalletRepository) FindOne(query *SponsorWalletQuery, options *FindOneOptions) (*SponsorWallet, error) {
	return findOne[SponsorWallet](SponsorWalletTbl, query, options)
}

// FindPage finds a page of sponsor wallets
func (r *SponsorWalletRepository) FindPage(query *SponsorWalletQuery, options *FindPageOptions) ([]*SponsorWallet, *Pagination, error) {
	return findPage[SponsorWallet](SponsorWalletTbl, query, options)
}

// FindMany finds multiple sponsor wallets
func (r *SponsorWalletRepository) FindMany(query *SponsorWalletQuery, options *FindManyOptions) ([]*SponsorWallet, error) {
	return findMany[SponsorWallet](SponsorWalletTbl, query, options)
}

// Count counts sponsor wallets based on query
func (r *SponsorWalletRepository) Count(query *SponsorWalletQuery) (int64, error) {
	return count[SponsorWallet](SponsorWalletTbl, query)
}

// Delete deletes a sponsor wallet by ID
func (r *SponsorWalletRepository) Delete(id string, trans *gorm.DB) error {
	return deleteByID[SponsorWallet](SponsorWalletTbl, id, trans)
}
