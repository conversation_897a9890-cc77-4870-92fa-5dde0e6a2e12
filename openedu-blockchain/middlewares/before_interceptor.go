package middleware

import (
	"github.com/gin-gonic/gin"
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
)

type headerData struct {
	ApiKey string
}

func extractHeaderData(c *gin.Context) *headerData {
	return &headerData{
		ApiKey: c.Request.Header.Get(util.HeaderAPIKey),
	}
}

// BeforeInterceptor is a middleware function that verify API authentication.
func BeforeInterceptor() gin.HandlerFunc {
	return func(c *gin.Context) {
		appG := app.Gin{C: c}
		headers := extractHeaderData(c)
		if headers.ApiKey == setting.AppSetting.APIKey {
			c.Next()
			return
		}

		maskedApiKey := util.MaskString(headers.ApiKey, len(headers.ApiKey)/3)
		log.Debugf("Invalid API key: %s", masked<PERSON><PERSON><PERSON><PERSON>)
		appG.Response401(e.<PERSON>th<PERSON><PERSON><PERSON><PERSON><PERSON>, "Invalid API key")
		c.Abort()
		return
	}
}
