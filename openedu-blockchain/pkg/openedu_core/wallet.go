package openedu_core

import (
	"encoding/json"
	"fmt"
	"openedu-blockchain/pkg/queue/producer"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
)

const (
	WalletSyncQueueName            = "wallet_sync_queue"
	WalletRetrieveDetailsQueueName = "wallet_retrieve_get_details_queue"
	SponsorWalletSyncQueueName     = "sponsor_wallet_sync_queue"
)

type WalletService struct {
	producer producer.Producer
}

func NewWalletService() (*WalletService, error) {
	p, err := producer.NewProducer(producer.RabbitMQ)
	if err != nil {
		return nil, fmt.Errorf("failed to create producer: %v", err)
	}

	return &WalletService{
		producer: p,
	}, nil
}

func (s *WalletService) ReplyRPC(queueName string, corrID string, data any) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	if err = s.producer.Publish(
		queueName,
		&producer.Message{
			ID:            util.GenerateId(),
			Body:          bytes,
			ContentType:   "application/json",
			CorrelationID: &corrID,
			ReplyTo:       queueName,
		},
	); err != nil {
		return fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}
	return nil
}

func (s *WalletService) Sync(req *SyncWalletRequest) error {
	bytes, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	if err = s.producer.Publish(
		setting.RabbitMQSetting.Prefix+WalletSyncQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	); err != nil {
		return fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}
	return nil
}

func (s *WalletService) RetrieveWalletDetails(req *RetrieveWalletDetailsRequest) error {
	bytes, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	if err = s.producer.Publish(
		setting.RabbitMQSetting.Prefix+WalletRetrieveDetailsQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	); err != nil {
		return fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}
	return nil
}

func (s *WalletService) SyncSponsorWallet(req *SyncSponsorWalletRequest) error {
	bytes, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("%w: failed to marshal request: %s", ErrMakeRequest, err)
	}

	if err = s.producer.Publish(
		setting.RabbitMQSetting.Prefix+SponsorWalletSyncQueueName,
		&producer.Message{
			ID:          util.GenerateId(),
			Body:        bytes,
			ContentType: "application/json",
		},
	); err != nil {
		return fmt.Errorf("%w: failed to send amqp publishing request: %s", ErrMakeRequest, err)
	}
	return nil
}

type SyncSponsorWalletRequest struct {
	ID          string      `json:"id"`
	SponsorID   string      `json:"sponsor_id"`
	SponsorName string      `json:"sponsor_name"`
	WalletID    string      `json:"wallet_id"`
	Network     string      `json:"network"`
	Status      string      `json:"status"`
	CoreTxID    string      `json:"core_tx_id"`
	Message     string      `json:"message,omitempty"`
	Response    interface{} `json:"response,omitempty"`
}
