package openedu_core

import (
	"log"
)

type WalletServiceIface interface {
	Sync(req *SyncWalletRequest) error
	RetrieveWalletDetails(req *RetrieveWalletDetailsRequest) error
	ReplyRPC(queueName string, corrID string, data any) error
}

var Wallet WalletServiceIface

func Setup() {
	walletService, err := NewWalletService()
	if err != nil {
		log.Fatalf("failed to initialize wallet service: %v", err)
	}

	Wallet = walletService
}
