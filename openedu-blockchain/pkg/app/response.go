package app

import (
	"encoding/json"
	"fmt"
	goteamsnotify "github.com/atc0005/go-teams-notify/v2"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/setting"
	"os"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/atc0005/go-teams-notify/v2/adaptivecard"
)

type Gin struct {
	C *gin.Context
}

type Response struct {
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
	Code int         `json:"code"`
}

type ResponseT[T any] struct {
	Data T      `json:"data"`
	Msg  string `json:"msg"`
	Code int    `json:"code"`
}

type PanicError struct {
	Message string `json:"message"`
	Stack   string `json:"stack,omitempty"`
}

type ErrorData struct {
	Message string `json:"message"`
}

// Response setting gin.JSON
func (g *Gin) Response(httpCode, errCode int, data interface{}) {
	if data == nil {
		data = struct{}{}
	}

	if reflect.TypeOf(data).Name() == "string" {
		data = map[string]interface{}{
			"message": data,
		}
	}

	if httpCode >= 400 {
		log.GinRespLogger().Error(data)
	}

	resp := Response{
		Code: errCode,
		Msg:  e.GetMsg(errCode),
		Data: data,
	}
	if httpCode >= 500 && setting.AppSetting.EnableAPIFailureAlerts && setting.AppSetting.MsTeamWebHookURL != "" {
		go func(res Response) {
			msg, err := g.buildMsTeamMsg(&res)
			if err != nil {
				log.Errorf("Build message for notify to Microsoft Team error: %v", err)
			} else {
				mstClient := goteamsnotify.NewTeamsClient()
				if err := mstClient.Send(setting.AppSetting.MsTeamWebHookURL, msg); err != nil {
					log.Errorf("Notify API failure to Microsoft Team error: %v", err)
				}
			}
		}(resp)
	}

	g.C.JSON(httpCode, resp)
	return
}

func (g *Gin) ResponseAppError(e *e.AppError) {
	data := map[string]interface{}{
		"message": e.Msg,
	}
	if e.Root != nil {
		data["root"] = e.Root.Error()
	}
	g.Response(e.StatusCode, e.ErrCode, data)
	return
}

func (g *Gin) Response200(data interface{}) {
	g.Response(200, e.Success, data)
	return
}

func (g *Gin) Response201(data interface{}) {
	g.Response(201, e.Created, data)
	return
}

func (g *Gin) Response204(data interface{}) {
	g.Response(204, e.NoContent, data)
	return
}

func (g *Gin) Response400(errCode int, data interface{}) {
	g.Response(400, errCode, data)
	return
}

func (g *Gin) Response401(errCode int, data interface{}) {
	g.Response(401, errCode, data)
	return
}

func (g *Gin) Response403(errCode int, data interface{}) {
	g.Response(403, errCode, data)
	return
}

func (g *Gin) Response404(errCode int, data interface{}) {
	g.Response(404, errCode, data)
	return
}

func (g *Gin) Response500(errCode int, data interface{}) {
	g.Response(500, errCode, data)
	return
}

func (g *Gin) buildMsTeamMsg(resp *Response) (*adaptivecard.Message, error) {
	stage := os.Getenv("STAGE")
	if stage == "" {
		stage = "local"
	}

	path := g.C.Request.URL.Path
	raw := g.C.Request.URL.RawQuery
	if raw != "" {
		path = path + "?" + raw
	}

	msgTitle := fmt.Sprintf("[%s] API Failure Alerts", strings.ToUpper(stage))
	msgText := "Here is detail error: \n " +
		fmt.Sprintf("* Service Name: **%s** \n ", setting.AppSetting.Name) +
		fmt.Sprintf("* Stage: **%s** \n ", stage) +
		"* User ID: **N/A** \n " +
		"* Organization ID: **N/A** \n " +
		fmt.Sprintf("* API Path: **%s %s** \n ", g.C.Request.Method, path) +
		fmt.Sprintf("* Status Code: **%d** \n ", g.C.Writer.Status())

	card, err := adaptivecard.NewTextBlockCard(msgText, msgTitle, true)
	if err != nil {
		return nil, err
	}

	card.SetFullWidth()
	card.MSTeams.Width = adaptivecard.MSTeamsWidthFull
	card.MSTeams.AllowExpand = true

	if len(setting.AppSetting.MsTeamMentionedEmails) > 0 {
		var userMentions []adaptivecard.Mention
		for _, email := range setting.AppSetting.MsTeamMentionedEmails {
			userMention, err := adaptivecard.NewMention(email, email)
			if err != nil {
				return nil, err
			}
			userMentions = append(userMentions, userMention)
		}

		if err := card.AddMention(true, userMentions...); err != nil {
			return nil, err
		}
	}

	b, err := json.MarshalIndent(&resp, "", "    ")
	if err != nil {
		return nil, err
	}

	codeSnippet := string(b)
	codeSnippet = strings.TrimSpace(codeSnippet)
	formattedCodeSnippet := "```json\n" + codeSnippet + "\n```"
	codeBlock := adaptivecard.NewTextBlock(formattedCodeSnippet, true)
	//codeBlock := adaptivecard.NewCodeBlock(codeSnippet, "JSON", 1)
	if err := card.AddElement(false, codeBlock); err != nil {
		return nil, err
	}

	return adaptivecard.NewMessageFromCard(card)
}
