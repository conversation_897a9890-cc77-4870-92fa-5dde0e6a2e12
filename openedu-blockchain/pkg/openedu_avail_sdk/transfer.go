package openedu_avail_sdk

import (
	"fmt"
	"github.com/availproject/avail-go-sdk/src/sdk"
	"github.com/centrifuge/go-substrate-rpc-client/v4/registry"
	"github.com/centrifuge/go-substrate-rpc-client/v4/registry/retriever"
	"github.com/centrifuge/go-substrate-rpc-client/v4/registry/state"
	"github.com/centrifuge/go-substrate-rpc-client/v4/types"
	"github.com/vedhavyas/go-subkey/v2"
	"math/big"
)

const (
	TransferKeepAliveMethodName = "Balances.transfer_keep_alive"
	UtilityBatchMethodName      = "Utility.batch"
	PaymentQueryInfoMethodName  = "payment_queryInfo"

	BalanceTransferEvent        = "Balances.Transfer"
	SystemExtrinsicFailedEvent  = "System.ExtrinsicFailed"
	SystemExtrinsicSuccessEvent = "System.ExtrinsicSuccess"
	TransactionFeePaidEvent     = "TransactionPayment.TransactionFeePaid"
)

type TransactionStatus string

const (
	Success TransactionStatus = "success"
	Failed  TransactionStatus = "failed"
)

type TransactionDetails struct {
	BlockHash string
	TxHash    string
	Nonce     uint64
	FeePaid   types.U128
	Status    TransactionStatus
}

type BatchTransferRecipient struct {
	Address string
	Amount  types.UCompact
}

func TransferKeepAlive(
	api *sdk.SubstrateAPI,
	seed string,
	waitForInclusion sdk.WaitFor,
	toAddress string,
	amount types.UCompact,
) (*TransactionDetails, error) {

	keyringPair, err := sdk.KeyringFromSeed(seed)
	if err != nil {
		return nil, fmt.Errorf("cannot create KeyPair: %v", err)
	}

	_, pubKeyBytes, _ := subkey.SS58Decode(toAddress)
	hexString := subkey.EncodeHex(pubKeyBytes)
	addr, err := sdk.NewMultiAddressFromHexAccountID(hexString)
	if err != nil {
		return nil, err
	}

	blockHashChan := make(chan types.Hash)
	txHashChan := make(chan types.Hash)
	nonceChan := make(chan uint64)

	go func() {
		err = NewExtrinsicWatch(
			api,
			TransferKeepAliveMethodName,
			keyringPair,
			blockHashChan,
			txHashChan,
			nonceChan,
			0,
			waitForInclusion,
			addr,
			amount,
		)
		if err != nil {
			close(blockHashChan)
			close(txHashChan)
			close(nonceChan)
			return
		}
	}()
	txHash := <-txHashChan
	nonce := <-nonceChan
	blockHash := <-blockHashChan

	if err != nil {
		return nil, err
	}

	eventRetriever, err := retriever.NewDefaultEventRetriever(state.NewEventProvider(api.RPC.State), api.RPC.State)
	if err != nil {
		return nil, err
	}

	extrinsicIdx, err := getExtrinsicIndex(api, blockHash, txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve extrinsic index: %w", err)
	}

	events, err := eventRetriever.GetEvents(blockHash)
	if err != nil {
		return nil, err
	}

	txDetails := TransactionDetails{
		BlockHash: blockHash.Hex(),
		TxHash:    txHash.Hex(),
		Nonce:     nonce,
	}

	for _, event := range events {
		if event.Phase.IsApplyExtrinsic && event.Phase.AsApplyExtrinsic == extrinsicIdx {
			switch event.Name {
			case BalanceTransferEvent:
				txDetails.Status = Success

			case SystemExtrinsicFailedEvent:
				txDetails.Status = Failed

			case TransactionFeePaidEvent:
				feePaid, fErr := registry.GetDecodedFieldAsType[types.U128](
					event.Fields,
					func(fieldIndex int, field *registry.DecodedField) bool {
						return fieldIndex == 1
					},
				)
				if fErr != nil {
					return nil, fmt.Errorf("decode transaction fee paid failed: %w", fErr)
				}
				txDetails.FeePaid = feePaid
			}
		}
	}

	return &txDetails, nil
}

func EstimateBatchTransferKeepAlive(
	api *sdk.SubstrateAPI,
	seed string,
	recipients []*BatchTransferRecipient,
) (*big.Int, error) {

	keyringPair, err := sdk.KeyringFromSeed(seed)
	if err != nil {
		return nil, fmt.Errorf("cannot create KeyPair: %v", err)
	}

	meta, err := api.RPC.State.GetMetadataLatest()
	if err != nil {
		return nil, fmt.Errorf("failed to get metadata: %w", err)
	}

	var calls []types.Call
	for _, recipient := range recipients {
		_, pubKeyBytes, dErr := subkey.SS58Decode(recipient.Address)
		if dErr != nil {
			return nil, fmt.Errorf("failed to decode recipient address: %w", dErr)
		}
		hexString := subkey.EncodeHex(pubKeyBytes)
		addr, aErr := sdk.NewMultiAddressFromHexAccountID(hexString)
		if aErr != nil {
			return nil, fmt.Errorf("failed to create multi address: %w", aErr)
		}

		call, aErr := types.NewCall(meta, "Balances.transfer_keep_alive", addr, recipient.Amount)
		if aErr != nil {
			return nil, fmt.Errorf("failed to create transfer call for recipient %s: %w", recipient.Address, aErr)
		}
		calls = append(calls, call)
	}

	ext, err := sdk.CreateExtrinsic(api, UtilityBatchMethodName, keyringPair, 0, calls)
	if err != nil {
		return nil, fmt.Errorf("failed to create extrinsic: %w", err)
	}

	encodedExt, err := sdk.EncodeToHex(ext)
	if err != nil {
		return nil, fmt.Errorf("failed to encode extrinsic: %w", err)
	}

	var paymentInfo map[string]interface{}
	err = api.Client.Call(&paymentInfo, PaymentQueryInfoMethodName, encodedExt, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment info: %w", err)
	}

	//weight := paymentInfo["weight"].(map[string]interface{})
	//weightJSON, err := json.Marshal(map[string]interface{}{
	//	"refTime":   weight["ref_time"],
	//	"proofSize": weight["proof_size"],
	//})
	//if err != nil {
	//	return nil, fmt.Errorf("failed to get marshal weight: %w", err)
	//}

	partialFee, ok := new(big.Int).SetString(paymentInfo["partialFee"].(string), 10)
	if !ok {
		return nil, fmt.Errorf("failed to parse partial fee: %w", err)
	}
	return partialFee, nil
}

func BatchTransferKeepAlive(
	api *sdk.SubstrateAPI,
	seed string,
	waitForInclusion sdk.WaitFor,
	recipients []*BatchTransferRecipient,
) (*TransactionDetails, error) {

	keyringPair, err := sdk.KeyringFromSeed(seed)
	if err != nil {
		return nil, fmt.Errorf("cannot create KeyPair: %v", err)
	}

	meta, err := api.RPC.State.GetMetadataLatest()
	if err != nil {
		return nil, fmt.Errorf("failed to get metadata: %w", err)
	}

	var calls []types.Call
	for _, recipient := range recipients {
		_, pubKeyBytes, dErr := subkey.SS58Decode(recipient.Address)
		if dErr != nil {
			return nil, fmt.Errorf("failed to decode recipient address: %w", dErr)
		}
		hexString := subkey.EncodeHex(pubKeyBytes)
		addr, aErr := sdk.NewMultiAddressFromHexAccountID(hexString)
		if aErr != nil {
			return nil, fmt.Errorf("failed to create multi address: %w", aErr)
		}

		call, aErr := types.NewCall(meta, "Balances.transfer_keep_alive", addr, recipient.Amount)
		if aErr != nil {
			return nil, fmt.Errorf("failed to create transfer call for recipient %s: %w", recipient.Address, aErr)
		}
		calls = append(calls, call)
	}

	blockHashChan := make(chan types.Hash)
	txHashChan := make(chan types.Hash)
	nonceChan := make(chan uint64)

	go func() {
		err = NewExtrinsicWatch(
			api,
			UtilityBatchMethodName,
			keyringPair,
			blockHashChan,
			txHashChan,
			nonceChan,
			0,
			waitForInclusion,
			calls,
		)
		if err != nil {
			close(blockHashChan)
			close(txHashChan)
			close(nonceChan)
			return
		}
	}()
	txHash := <-txHashChan
	nonce := <-nonceChan
	blockHash := <-blockHashChan

	if err != nil {
		return nil, err
	}

	eventRetriever, err := retriever.NewDefaultEventRetriever(state.NewEventProvider(api.RPC.State), api.RPC.State)
	if err != nil {
		return nil, err
	}

	extrinsicIdx, err := getExtrinsicIndex(api, blockHash, txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve extrinsic index: %w", err)
	}

	events, err := eventRetriever.GetEvents(blockHash)
	if err != nil {
		return nil, err
	}

	txDetails := TransactionDetails{
		BlockHash: blockHash.Hex(),
		TxHash:    txHash.Hex(),
		Nonce:     nonce,
	}

	for _, event := range events {
		if event.Phase.IsApplyExtrinsic && event.Phase.AsApplyExtrinsic == extrinsicIdx {
			switch event.Name {
			case BalanceTransferEvent:
				txDetails.Status = Success

			case SystemExtrinsicFailedEvent:
				txDetails.Status = Failed

			case SystemExtrinsicSuccessEvent:
				txDetails.Status = Success

			case TransactionFeePaidEvent:
				feePaid, fErr := registry.GetDecodedFieldAsType[types.U128](
					event.Fields,
					func(fieldIndex int, field *registry.DecodedField) bool {
						return fieldIndex == 1
					},
				)
				if fErr != nil {
					return nil, fmt.Errorf("decode transaction fee paid failed: %w", fErr)
				}
				txDetails.FeePaid = feePaid
			}
		}
	}

	return &txDetails, nil
}
