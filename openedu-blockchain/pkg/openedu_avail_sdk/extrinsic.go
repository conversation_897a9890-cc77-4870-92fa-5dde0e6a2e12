package openedu_avail_sdk

import (
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/availproject/avail-go-sdk/src/extrinsic"
	"github.com/availproject/avail-go-sdk/src/rpc"
	"github.com/availproject/avail-go-sdk/src/sdk"
	"github.com/centrifuge/go-substrate-rpc-client/v4/signature"
	"github.com/centrifuge/go-substrate-rpc-client/v4/types"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"golang.org/x/crypto/blake2b"
)

func NewExtrinsicWatch(
	api *sdk.SubstrateAPI,
	extCall string,
	keyring signature.KeyringPair,
	finalChan chan types.Hash,
	txHashChan chan types.Hash,
	nonce<PERSON>han chan uint64,
	appID int,
	waitForInclusion sdk.WaitFor,
	arg ...interface{},
) error {
	meta, err := api.RPC.State.GetMetadataLatest()
	if err != nil {
		return err
	}

	call, err := types.NewCall(meta, extCall, arg...)
	if err != nil {
		return err
	}

	ext := extrinsic.NewExtrinsic(call)
	genesisHash, err := api.RPC.Chain.GetBlockHash(0)
	if err != nil {
		return err
	}

	rv, err := api.RPC.State.GetRuntimeVersionLatest()
	if err != nil {
		return err
	}

	key, err := types.CreateStorageKey(meta, "System", "Account", keyring.PublicKey)
	if err != nil {
		return err
	}

	var accountInfo types.AccountInfo
	ok, err := api.RPC.State.GetStorageLatest(key, &accountInfo)
	if err != nil || !ok {
		return err
	}

	nonce := uint32(accountInfo.Nonce)
	options := extrinsic.SignatureOptions{
		BlockHash:          genesisHash,
		Era:                extrinsic.ExtrinsicEra{IsMortalEra: false},
		GenesisHash:        genesisHash,
		Nonce:              types.NewUCompactFromUInt(uint64(nonce)),
		SpecVersion:        rv.SpecVersion,
		Tip:                types.NewUCompactFromUInt(100),
		AppID:              types.NewUCompactFromUInt(uint64(appID)),
		TransactionVersion: rv.TransactionVersion,
	}
	err = ext.Sign(keyring, options)
	if err != nil {
		return err
	}

	enc, _ := sdk.EncodeToHex(ext)

	cleanedHexString := strings.TrimPrefix(enc, "0x")
	bytes, dErr := hex.DecodeString(cleanedHexString)
	if dErr != nil {
		return fmt.Errorf("decode hex string to bytes failed: %w", dErr)
	}
	hash := blake2b.Sum256(bytes)
	extZ := hexutil.Encode(hash[:])
	hash, err = sdk.NewHashFromHexString(extZ)
	if err != nil {
		return fmt.Errorf("new hash from hex string failed: %w", dErr)
	}
	txHashChan <- hash
	nonceChan <- uint64(nonce)

	sub, err := rpc.SubmitAndWatchExtrinsic(ext, api.Client)
	if err != nil {
		return fmt.Errorf("submit and watch extrinsic failed: %w", err)
	}

	defer sub.Unsubscribe()
	timeout := time.After(200 * time.Second)
	for {
		select {
		case status := <-sub.Chan():
			switch waitForInclusion {
			case sdk.BlockInclusion:
				if status.IsInBlock {
					finalChan <- status.AsInBlock
					return err
				}
			case sdk.BlockFinalization:
				if status.IsFinalized {
					finalChan <- status.AsFinalized
					return err
				}
			}
		case <-timeout:
			return err
		}
	}
}

func getExtrinsicIndex(api *sdk.SubstrateAPI, blockHash types.Hash, txHash types.Hash) (uint32, error) {
	block, err := api.RPC.Chain.GetBlock(blockHash)
	if err != nil {
		return 0, fmt.Errorf("fetch block data failed: %w", err)
	}

	for i, e := range block.Block.Extrinsics {

		enc, eErr := sdk.EncodeToHex(e)
		if eErr != nil {
			return 0, fmt.Errorf("encode extrinsics to hex failed: %w", eErr)
		}

		cleanedHexString := strings.TrimPrefix(enc, "0x")
		bytes, dErr := hex.DecodeString(cleanedHexString)
		if dErr != nil {
			return 0, fmt.Errorf("decode hex string to bytes failed: %w", dErr)
		}

		hash := blake2b.Sum256(bytes)
		extZ := hexutil.Encode(hash[:])
		hash, err = sdk.NewHashFromHexString(extZ)
		if err != nil {
			return 0, fmt.Errorf("new hash from hex string failed: %w", dErr)
		}

		if types.Hash(hash).Hex() == txHash.Hex() {
			return uint32(i), nil
		}
	}

	return 0, fmt.Errorf("transaction hash not found in block")
}
