package cache

//import (
//	"context"
//	"errors"
//	"github.com/allegro/bigcache/v3"
//	"github.com/eko/gocache/lib/v4/cache"
//	bigcache_store "github.com/eko/gocache/store/bigcache/v4"
//	"time"
//)
//
//type BigCache struct {
//	ctx   context.Context
//	cache *cache.Cache[[]byte]
//}
//
//func NewBigCache() *BigCache {
//	bigcacheClient, _ := bigcache.NewBigCache(bigcache.DefaultConfig(5 * time.Second))
//	bigcacheStore := bigcache_store.NewBigcache(bigcacheClient)
//	cache5m := cache.New[[]byte](bigcacheStore)
//	return &BigCache{
//		ctx:   context.Background(),
//		cache: cache5m,
//	}
//}
//
//func (c *BigCache) Set(key, value interface{}) error {
//	// Assert the key is of string type
//	keyString, ok := key.(string)
//	if !ok {
//		return errors.New("a cache key must be a string")
//	}
//
//	// Serialize the value into bytes
//	valueBytes, err := serialize(value)
//	if err != nil {
//		return err
//	}
//
//	return c.cache.Set(c.ctx, keyString, valueBytes)
//}
//
//func (c *BigCache) Get(key interface{}) (interface{}, error) {
//	// Assert the key is of string type
//	keyString, ok := key.(string)
//	if !ok {
//		return nil, errors.New("a cache key must be a string")
//	}
//
//	// Get the value in the byte format it is stored in
//	valueBytes, err := c.cache.Get(c.ctx, keyString)
//	if err != nil {
//		return nil, err
//	}
//
//	// Deserialize the bytes of the value
//	value, err := deserialize(valueBytes)
//	if err != nil {
//		return nil, err
//	}
//
//	return value, nil
//}
