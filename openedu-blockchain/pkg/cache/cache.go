package cache

import (
	"bytes"
	"encoding/gob"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/setting"
	"time"
)

type Provider string

const (
	MemoryCache Provider = "memory"
	RedisCache  Provider = "redis"

	NoExpiration      time.Duration = -1
	DefaultExpiration               = 5 * time.Minute
)

var Client ClientIface

type ClientIface interface {
	// Set Add an item to the cache, replacing any existing item. If the duration is 0
	// (DefaultExpiration), the cache's default expiration time is used. If it is -1
	// (NoExpiration), the item never expires.
	Set(key string, value interface{}, ttl time.Duration) error
	Get(key string, value interface{}) error
	Delete(key string) error
	DeleteByPrefix(prefix string) error
	Flush() error
}

func getCache() (ClientIface, error) {
	provider := Provider(setting.AppSetting.CacheService)
	var cache ClientIface
	switch provider {
	case RedisCache:
		cache = newRedis()
		break
	case MemoryCache:
		cache = newGoCache()
		break
	default:
		cache = newGoCache()
		break
	}
	return cache, nil
}

func SetupCache() {
	cache, err := getCache()
	if err != nil {
		log.Fatal("Setup cache service failed", err)
		return
	}

	Client = cache
}

func serialize(value interface{}) ([]byte, error) {
	buf := bytes.Buffer{}
	enc := gob.NewEncoder(&buf)
	gob.Register(value)

	err := enc.Encode(&value)
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func deserialize(valueBytes []byte) (interface{}, error) {
	var value interface{}
	buf := bytes.NewBuffer(valueBytes)
	dec := gob.NewDecoder(buf)

	err := dec.Decode(&value)
	if err != nil {
		return nil, err
	}

	return value, nil
}
