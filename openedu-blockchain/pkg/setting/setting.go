package setting

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/go-ini/ini"
)

const (
	RunModeRelease = "release"
	RunModeDebug   = "debug"
)

type App struct {
	Name string

	RuntimeRootPath string

	APIKey string

	DefaultPerPage int

	LogSavePath         string
	LogSaveName         string
	LogSaveTimeFormat   string
	LogFileExt          string
	LogFileMaxSizeInMB  int
	LogFileMaxAgeInDays int
	LogFileMaxBackups   int
	LogCompressEnabled  bool

	KeyManagerProvider string
	KeyManagerKeyID    string

	CacheService string

	EnableAPIFailureAlerts bool
	MsTeamWebHookURL       string
	MsTeamMentionedEmails  []string
}

type Server struct {
	RunMode      string
	Host         string
	HttpPort     int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	BuildName    string
}

type Database struct {
	Type        string
	User        string
	Password    string
	Host        string
	Name        string
	Port        string
	TablePrefix string
	IdSize      int
	SSLMode     string
}

type RabbitMQ struct {
	URL    string
	Prefix string
}

type Redis struct {
	Host          string
	Password      string
	MaxIdle       int
	MaxActive     int
	IdleTimeout   time.Duration
	PrefixChannel string
}

type Aws struct {
	AccessKey string
	SecretKey string
	Region    string
}

type OpenEduCore struct {
	BaseURL string
	ApiKey  string
}

type Near struct {
	// Mainnet RPC
	MainnetURLs []string

	// Testnet RPC
	TestnetURLs []string

	// FT Mainnet
	MainnetUSDTContractID string
	MainnetUSDTDecimals   int

	MainnetUSDCContractID string
	MainnetUSDCDecimals   int

	MainnetOpenEduContractID string
	MainnetOpenEduDecimals   int

	// FT Testnet
	TestnetUSDTContractID string
	TestnetUSDTDecimals   int

	TestnetUSDCContractID string
	TestnetUSDCDecimals   int

	TestnetOpenEduContractID string
	TestnetOpenEduDecimals   int

	// Payment Mainnet
	MainnetPaymentContractID        string
	MainnetPaymentClaimMethod       string
	MainnetPaymentGetUserInfoMethod string

	// Payment Testnet
	TestnetPaymentContractID        string
	TestnetPaymentClaimMethod       string
	TestnetPaymentGetUserInfoMethod string

	// NFT Mainnet
	MainnetNftContractID                  string
	MainnetNftPrivateKey                  string
	MainnetNftMintMethodName              string
	MainnetNftMintWithSponsorMethodName   string
	MainnetNftMintWithSignatureMethodName string
	MainnetNftDepositSponsorMethodName    string
	MainnetNftGetSponsorBalanceMethodName string
	MainnetNftWithdrawSponsorMethodName   string
	MainnetNftSignaturePrivateKey         string

	// NFT Testnet
	TestnetNftContractID                  string
	TestnetNftPrivateKey                  string
	TestnetNftMintMethodName              string
	TestnetNftMintWithSponsorMethodName   string
	TestnetNftMintWithSignatureMethodName string
	TestnetNftDepositSponsorMethodName    string
	TestnetNftGetSponsorBalanceMethodName string
	TestnetNftWithdrawSponsorMethodName   string
	TestnetNftSignaturePrivateKey         string

	// Launchpad Mainnet
	MainnetLaunchpadContractID                      string
	MainnetLaunchpadPrivateKey                      string
	MainnetLaunchpadInitPoolMethodName              string
	MainnetLaunchpadApprovePoolMethodName           string
	MainnetLaunchpadPledgeMethodName                string
	MainnetLaunchpadUpdatePoolFundingTimeMethodName string
	MainnetLaunchpadCancelPoolMethodName            string
	MainnetLaunchpadGetUserRecordsByPoolMethodName  string
	MainnetLaunchpadGetPoolDetailsMethodName        string
	MainnetLaunchpadCheckFundingResultMethodName    string
	MainnetLaunchpadWithdrawToCreatorMethodName     string
	MainnetLaunchpadCreatorAcceptVotingMethodName   string
	MainnetLaunchpadSetFundingTimeMethodName        string
	MainnetLaunchpadClaimRefundMethodName           string
	MainnetLaunchpadUpdatePoolStatusMethodName      string

	// Launchpad Testnet
	TestnetLaunchpadContractID                      string
	TestnetLaunchpadPrivateKey                      string
	TestnetLaunchpadInitPoolMethodName              string
	TestnetLaunchpadApprovePoolMethodName           string
	TestnetLaunchpadPledgeMethodName                string
	TestnetLaunchpadUpdatePoolFundingTimeMethodName string
	TestnetLaunchpadCancelPoolMethodName            string
	TestnetLaunchpadGetUserRecordsByPoolMethodName  string
	TestnetLaunchpadGetPoolDetailsMethodName        string
	TestnetLaunchpadCheckFundingResultMethodName    string
	TestnetLaunchpadWithdrawToCreatorMethodName     string
	TestnetLaunchpadCreatorAcceptVotingMethodName   string
	TestnetLaunchpadSetFundingTimeMethodName        string
	TestnetLaunchpadClaimRefundMethodName           string
	TestnetLaunchpadUpdatePoolStatusMethodName      string
}

type Avail struct {
	MainnetURLs []string

	TestnetURLs []string
}

type Evm struct {
	// RPC Mainnet
	MainnetURLs    []string
	MainnetChainID string

	// RPC Testnet
	TestnetURLs    []string
	TestnetChainID string

	// FTs Mainnet
	MainnetUSDCContractAddress string
	MainnetUSDCDecimals        int

	// FTs Testnet
	TestnetUSDCContractAddress      string
	TestnetUSDCDecimals             int

	// Payment Mainnet
	MainnetPaymentContractAddress   string
	MainnetVaultContractAddress     string
	MainnetPaymentOwnerPrivateKey   string

	// Payment Testnet
	TestnetPaymentContractAddress   string
	TestnetVaultContractAddress     string
	TestnetPaymentOwnerPrivateKey   string

	// Launchpad Mainnet
	MainnetLaunchpadContractAddress string
	MainnetLaunchpadOwnerPrivateKey string

	// Launchpad Testnet
	TestnetLaunchpadContractAddress string
	TestnetLaunchpadOwnerPrivateKey string

	// NFT Certificate Mainnet
	MainnetNFTCertificateAddress    string
	MainnetNFTOwnerPrivateKey       string

	// NFT Certificate Testnet
	TestnetNFTCertificateAddress string
	TestnetNFTOwnerPrivateKey    string
}

type Paymaster struct {
	PaymasterURL    string
	PaymasterAPIKey string
}

type Pinata struct {
	ApiKey    string
	ApiSecret string
	JWT       string
	BaseURL   string
	Gateway   string
}

var AppSetting = &App{}
var ServerSetting = &Server{}
var DatabaseSetting = &Database{}
var RedisSetting = &Redis{}
var RabbitMQSetting = &RabbitMQ{}
var AwsSetting = &Aws{}
var OpenEduCoreSetting = &OpenEduCore{}
var NearSetting = &Near{}
var AvailSetting = &Avail{}
var EvmSetting = &Evm{}
var PaymasterSetting = &Paymaster{}
var PinataSetting = &Pinata{}

var cfg *ini.File

func getInitFilename() string {
	appEnvFile := os.Getenv("APP_ENV_FILE")
	if appEnvFile != "" {
		return appEnvFile
	}
	stage := os.Getenv("STAGE")
	test := os.Getenv("TEST")
	if test == "true" {
		return "../config/app.ini"
	}
	if stage == "" {
		return "config/app.ini"
	}

	return "config/app-" + stage + ".ini"
}

func IsProduction() bool {
	stage := os.Getenv("STAGE")
	return stage == "prod"
}

func hideString(str string, numVisibleChars int) string {
	secretLen := len(str)
	if numVisibleChars >= secretLen {
		return str
	}
	return str[:numVisibleChars] + strings.Repeat("*", secretLen-numVisibleChars)
}

// Setup initialize the configuration instance
func Setup() {
	var err error
	fileName := getInitFilename()
	fmt.Println("getInitFilename()", fileName)
	cfg, err = ini.Load(fileName)
	if err != nil {
		log.Fatalf("setting.Setup, failed to parse '%s': %v", fileName, err)
	}

	mapTo("app", AppSetting)
	mapTo("server", ServerSetting)
	mapTo("database", DatabaseSetting)
	mapTo("redis", RedisSetting)
	mapTo("rabbitmq", RabbitMQSetting)
	mapTo("aws", AwsSetting)
	mapTo("openedu-core", OpenEduCoreSetting)
	mapTo("near", NearSetting)
	mapTo("avail", AvailSetting)
	mapTo("evm", EvmSetting)
	mapTo("paymaster", PaymasterSetting)
	mapTo("pinata", PinataSetting)

	ServerSetting.ReadTimeout = ServerSetting.ReadTimeout * time.Second
	ServerSetting.WriteTimeout = ServerSetting.WriteTimeout * time.Second

	RedisSetting.IdleTimeout = RedisSetting.IdleTimeout * time.Second

	envVars := []string{
		"MAINNET_NFT_CONTRACT_PRIVATE_KEY",
		"MAINNET_NFT_SIGNATURE_PRIVATE_KEY",
		"MAINNET_LAUNCHPAD_PRIVATE_KEY",
		"MAINNET_EVM_OWNER_PRIVATE_KEY",
		"TESTNET_NFT_CONTRACT_PRIVATE_KEY",
		"TESTNET_NFT_SIGNATURE_PRIVATE_KEY",
		"TESTNET_LAUNCHPAD_PRIVATE_KEY",
		"TESTNET_EVM_OWNER_PRIVATE_KEY",
	}
	for _, envVar := range envVars {
		log.Println(envVar, hideString(processEscapeSequences(os.Getenv(envVar)), len(processEscapeSequences(os.Getenv(envVar)))/3))
	}

	NearSetting.MainnetNftPrivateKey = processEscapeSequences(os.Getenv("MAINNET_NFT_CONTRACT_PRIVATE_KEY"))
	NearSetting.MainnetNftSignaturePrivateKey = processEscapeSequences(os.Getenv("MAINNET_NFT_SIGNATURE_PRIVATE_KEY"))
	NearSetting.MainnetLaunchpadPrivateKey = processEscapeSequences(os.Getenv("MAINNET_LAUNCHPAD_PRIVATE_KEY"))
	EvmSetting.MainnetPaymentOwnerPrivateKey = processEscapeSequences(os.Getenv("MAINNET_EVM_OWNER_PRIVATE_KEY"))
	EvmSetting.MainnetLaunchpadOwnerPrivateKey = processEscapeSequences(os.Getenv("MAINNET_EVM_OWNER_PRIVATE_KEY"))
	EvmSetting.MainnetNFTOwnerPrivateKey = processEscapeSequences(os.Getenv("MAINNET_EVM_NFT_OWNER_PRIVATE_KEY"))


	NearSetting.TestnetNftPrivateKey = processEscapeSequences(os.Getenv("TESTNET_NFT_CONTRACT_PRIVATE_KEY"))
	NearSetting.TestnetNftSignaturePrivateKey = processEscapeSequences(os.Getenv("TESTNET_NFT_SIGNATURE_PRIVATE_KEY"))
	NearSetting.TestnetLaunchpadPrivateKey = processEscapeSequences(os.Getenv("TESTNET_LAUNCHPAD_PRIVATE_KEY"))
	EvmSetting.TestnetPaymentOwnerPrivateKey = processEscapeSequences(os.Getenv("TESTNET_EVM_OWNER_PRIVATE_KEY"))
	EvmSetting.TestnetLaunchpadOwnerPrivateKey = processEscapeSequences(os.Getenv("TESTNET_EVM_OWNER_PRIVATE_KEY"))
	EvmSetting.TestnetNFTOwnerPrivateKey = processEscapeSequences(os.Getenv("TESTNET_EVM_NFT_OWNER_PRIVATE_KEY"))
}

func processEscapeSequences(input string) string {
	// Process backslashes first
	input = strings.ReplaceAll(input, "\\\\", "\x00") // Temporary placeholder for backslash

	// Process other escape sequences
	input = strings.ReplaceAll(input, "\\n", "\n")
	input = strings.ReplaceAll(input, "\\t", "\t")
	input = strings.ReplaceAll(input, "\\r", "\r")
	input = strings.ReplaceAll(input, "\\\"", "\"")

	// Restore backslashes
	input = strings.ReplaceAll(input, "\x00", "\\")

	return input
}

// mapTo map section
func mapTo(section string, v interface{}) {
	err := cfg.Section(section).MapTo(v)
	if err != nil {
		log.Fatalf("Cfg.MapTo %s err: %v", section, err)
	}
}
