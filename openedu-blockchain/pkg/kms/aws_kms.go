package kms

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/kms"
	"github.com/aws/aws-sdk-go-v2/service/kms/types"
)

type AwsKeyManager struct {
	keyID  string // KMS Key ID or ARN
	client *kms.Client
}

type AwsKeyManagerConfig struct {
	KeyID     string
	AwsConfig aws.Config
}

func NewAwsKeyManager(cfg AwsKeyManagerConfig) *AwsKeyManager {
	client := kms.NewFromConfig(cfg.AwsConfig)
	return &AwsKeyManager{
		keyID:  cfg.KeyID,
		client: client,
	}
}

func (m *AwsKeyManager) Encrypt(data string) (string, error) {
	input := &kms.EncryptInput{
		KeyId:               aws.String(m.keyID),
		Plaintext:           []byte(data),
		EncryptionAlgorithm: types.EncryptionAlgorithmSpecRsaesOaepSha256,
	}

	result, err := m.client.Encrypt(context.Background(), input)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt data: %w", err)
	}

	encryptedData := base64.StdEncoding.EncodeToString(result.CiphertextBlob)
	return encryptedData, nil
}

func (m *AwsKeyManager) Decrypt(encryptedData string) (string, error) {
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	input := &kms.DecryptInput{
		KeyId:               aws.String(m.keyID),
		CiphertextBlob:      encryptedBytes,
		EncryptionAlgorithm: types.EncryptionAlgorithmSpecRsaesOaepSha256,
	}

	result, err := m.client.Decrypt(context.Background(), input)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt data: %w", err)
	}

	return string(result.Plaintext), nil
}
