package kms

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"log"
)

type Provider string

const (
	AwsKMS Provider = "aws_kms"
)

type KeyManagerIface interface {
	Encrypt(data string) (string, error)
	Decrypt(encryptedData string) (string, error)
}
type KeyManagerConfig struct {
	Provider     Provider
	KeyID        string
	AwsAccessKey string
	AwsSecretKey string
	AwsRegion    string
}

var defaultKeyManager KeyManagerIface

func Encrypt(data string) (string, error) {
	return defaultKeyManager.Encrypt(data)
}

func Decrypt(encryptedData string) (string, error) {
	return defaultKeyManager.Decrypt(encryptedData)
}

func Setup(cfg KeyManagerConfig) {
	switch cfg.Provider {
	case AwsKMS:
		awsCfg, err := config.LoadDefaultConfig(
			context.Background(),
			config.WithRegion(cfg.AwsRegion),
			config.WithCredentialsProvider(
				credentials.NewStaticCredentialsProvider(cfg.AwsAccessKey, cfg.AwsSecretKey, ""),
			),
		)
		if err != nil {
			log.Fatalf("load aws config error: %v", err)
		}

		defaultKeyManager = NewAwsKeyManager(AwsKeyManagerConfig{
			KeyID:     cfg.KeyID,
			AwsConfig: awsCfg,
		})

	default:
		log.Fatalf("unknown key manager provider: %s", cfg.Provider)
	}
}
