package producer

import (
	"context"
	"fmt"
	"github.com/streadway/amqp"
	"openedu-blockchain/pkg/setting"
	"time"
)

const (
	defaultQueueDurable    = true
	defaultQueueAutoDelete = false
	defaultQueueExclusive  = false
	defaultQueueNoWait     = false
)

type RabbitMQProducer struct {
	amqpConn *amqp.Connection
	amqpChan *amqp.Channel
}

func newRabbitMQProducer() (*RabbitMQProducer, error) {
	conn, err := amqp.Dial(setting.RabbitMQSetting.URL)
	if err != nil {
		return nil, fmt.Errorf("connect rabbitmq error: %w", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("create rabbitmq channel error: %w", err)
	}

	return &RabbitMQProducer{
		amqpConn: conn,
		amqpChan: ch,
	}, nil
}

func (p *RabbitMQProducer) Publish(queueName string, msg *Message) error {
	//queue, err := p.declareQueue(queueName)
	//if err != nil {
	//	return fmt.Errorf("declare rabbitmq queue %s error: %w", queueName, err)
	//}

	publishing := amqp.Publishing{
		ContentType:  msg.ContentType,
		DeliveryMode: amqp.Persistent,
		MessageId:    msg.ID,
		Timestamp:    time.Now(),
		Body:         msg.Body,
	}
	if msg.CorrelationID != nil {
		publishing.CorrelationId = *msg.CorrelationID
	}

	return p.amqpChan.Publish(
		"",
		queueName,
		false,
		false,
		publishing,
	)
}

func (p *RabbitMQProducer) PublishRPC(ctx context.Context, queueName string, msg *Message) (*Message, error) {
	replyQueue, err := p.amqpChan.QueueDeclare(
		"",
		false,
		true,
		true,
		false,
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("declare reply queue error: %w", err)
	}

	msgs, err := p.amqpChan.Consume(
		replyQueue.Name,
		"",
		true,
		true,
		false,
		false,
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("create consumer error: %w", err)
	}

	msg.ReplyTo = replyQueue.Name
	correlationID := fmt.Sprintf("%d", time.Now().UnixNano())
	msg.CorrelationID = &correlationID

	if err = p.Publish(queueName, msg); err != nil {
		return nil, err
	}

	select {
	case delivery := <-msgs:
		return &Message{
			Body:          delivery.Body,
			ContentType:   delivery.ContentType,
			CorrelationID: &delivery.CorrelationId,
		}, nil
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

func (p *RabbitMQProducer) declareQueue(queueName string) (amqp.Queue, error) {
	return p.amqpChan.QueueDeclare(
		queueName,
		defaultQueueDurable,
		defaultQueueAutoDelete,
		defaultQueueExclusive,
		defaultQueueNoWait,
		nil,
	)
}

func (p *RabbitMQProducer) Close() error {
	if err := p.amqpChan.Close(); err != nil {
		return err
	}
	return p.amqpConn.Close()
}
