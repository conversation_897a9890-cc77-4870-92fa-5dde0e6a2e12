package consumer

import (
	"context"
	"fmt"
	"github.com/streadway/amqp"
	"openedu-blockchain/pkg/setting"
)

const (
	defaultQueueDurable    = true
	defaultQueueAutoDelete = false
	defaultQueueExclusive  = false
	defaultQueueNoWait     = false

	defaultConsumeAutoAck   = false
	defaultConsumeExclusive = false
	defaultConsumeNoLocal   = false
	defaultConsumeNoWait    = false

	defaultRequeue     = false
	defaultAckMultiple = false

	defaultPrefetchCount = 1
	defaultPrefetchSize  = 0
	defaultGlobal        = false
)

type RabbitMQConsumer struct {
	amqpConn *amqp.Connection
	amqpChan *amqp.Channel
}

type RabbitMQMessage struct {
	delivery *amqp.Delivery
}

func (m *RabbitMQMessage) Reject() error {
	return m.delivery.Reject(defaultRequeue)
}

func (m *RabbitMQMessage) Ack() error {
	return m.delivery.Ack(defaultAckMultiple)
}

func (m *RabbitMQMessage) GetID() string {
	return fmt.Sprintf("%d", m.delivery.DeliveryTag)
}

func (m *RabbitMQMessage) GetBody() []byte {
	return m.delivery.Body
}

func (m *RabbitMQMessage) GetCorrelationID() string {
	return m.delivery.CorrelationId
}

func (m *RabbitMQMessage) GetReplyTo() string {
	return m.delivery.ReplyTo
}

func newRabbitMQConsumer() (*RabbitMQConsumer, error) {
	conn, err := amqp.Dial(setting.RabbitMQSetting.URL)
	if err != nil {
		return nil, fmt.Errorf("connect rabbitmq error: %w", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("create rabbitmq channel error: %w", err)
	}

	err = ch.Qos(defaultPrefetchCount, defaultPrefetchSize, defaultGlobal)
	if err != nil {
		conn.Close()
		ch.Close()
		return nil, fmt.Errorf("set QoS error: %w", err)
	}

	return &RabbitMQConsumer{
		amqpConn: conn,
		amqpChan: ch,
	}, nil
}

func (c *RabbitMQConsumer) FetchMessage(ctx context.Context, queueName string) (<-chan Message, error) {
	queue, err := c.declareQueue(queueName)
	if err != nil {
		return nil, err
	}

	deliveries, err := c.amqpChan.Consume(
		queue.Name,
		"",
		defaultConsumeAutoAck,
		defaultConsumeExclusive,
		defaultConsumeNoLocal,
		defaultConsumeNoWait,
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("create consumer error: %w", err)
	}

	msgChan := make(chan Message)
	go func() {
		defer close(msgChan)

		for {
			select {
			case <-ctx.Done():
				return
			case delivery, ok := <-deliveries:
				if !ok {
					return
				}

				msg := &RabbitMQMessage{
					delivery: &delivery,
				}

				select {
				case msgChan <- msg:
				case <-ctx.Done():
					return
				}
			}
		}
	}()

	return msgChan, nil
}

func (c *RabbitMQConsumer) declareQueue(name string) (amqp.Queue, error) {
	return c.amqpChan.QueueDeclare(
		name,
		defaultQueueDurable,
		defaultQueueAutoDelete,
		defaultQueueExclusive,
		defaultQueueNoWait,
		nil,
	)
}

func (c *RabbitMQConsumer) Close() error {
	if err := c.amqpChan.Close(); err != nil {
		return err
	}
	return c.amqpConn.Close()
}
