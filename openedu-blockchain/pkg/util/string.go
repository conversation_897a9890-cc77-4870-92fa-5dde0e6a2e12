package util

import (
	gonanoid "github.com/matoous/go-nanoid/v2"
	"openedu-blockchain/pkg/setting"
	"strings"
)

func GenerateId() string {
	id, err := gonanoid.Generate(AlphanumericCharset, setting.DatabaseSetting.IdSize)
	if err != nil {
		return GenerateId()
	}

	return id
}

func MaskString(str string, numVisibleChars int) string {
	secretLen := len(str)
	if numVisibleChars >= secretLen {
		return str
	}
	return str[:numVisibleChars] + strings.Repeat("*", secretLen-numVisibleChars)
}

func RemoveHexPrefix(hexStr string) string {
	if len(hexStr) >= 2 && hexStr[0:2] == "0x" {
		return hexStr[2:]
	}
	return hexStr
}
