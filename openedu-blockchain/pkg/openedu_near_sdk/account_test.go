package openedu_near_sdk

import (
	"testing"
)

func TestIsValidAddress(t *testing.T) {
	tests := []struct {
		address string
		valid   bool
	}{
		{"valid-near-account.near", true},
		{"invalid..account", false},
		{"invalid_account!", false},
		{"abcdefghabcdefghabcdefghabcdefghabcdefghabcdefghabcdefghabcdefgh", false},
		{"8514b4ed734b2cfe4ab2e80b3b15b4e3ee5e315a952858354096c13a3c92e53d", true},
		{"short", true},
		{"5GWpQNMqZUzZ9x8rFLLnz8v2dEByeTmG25YuQY7VdufaiSoj", false},
		{"1invalidstart", true}, // NEAR allows digits at start
		{"endswithdot.", false},
		{"double..dot", false},
		{"validnear.test", true},
		{"UPPERCASEINVALID", false},
	}

	for _, test := range tests {
		if got := IsValidAddress(test.address); got != test.valid {
			t.Errorf("IsValidAddress(%q) = %v, want %v", test.address, got, test.valid)
		}
	}
}
