package openedu_near_sdk

import (
	"crypto/ed25519"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/cenkalti/backoff/v4"
	"github.com/shopspring/decimal"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/util"
)

// SendNEARs sends yoctoNEAR amount to account ID
func SendNEARs(account *near.Account, recipientAccountId string, amount decimal.Decimal) (*TransactionDetails, error) {
	respMap, err := account.SendMoney(recipientAccountId, *amount.BigInt())
	if err != nil {
		return nil, fmt.Errorf("function call send money failed: %w", err)
	}

	txDetails, err := util.MapToStruct[TransactionDetails](respMap)
	if err != nil {
		return nil, fmt.Errorf("parse send money response failed: %w", err)
	}

	return &txDetails, nil
}

func SendNEARsWithRetry(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	recipientAccountId string,
	amount decimal.Decimal,
) (*TransactionDetails, error) {

	var txDetails *TransactionDetails
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			txDetails, err = SendNEARs(account, recipientAccountId, amount)
			if err == nil {
				return nil
			} else if IsNotEnoughBalance(err) {
				return err
			}
			log.Errorf("Failed to send NEARs on node %s: %v, trying next node...", nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to send NEARs after retries: %w", err)
	}
	return txDetails, nil
}