package openedu_near_sdk

import (
	"crypto/ed25519"
	"encoding/json"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/cenkalti/backoff/v4"
	"github.com/shopspring/decimal"
	"math/big"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/util"
	"strings"
)

type FtMetadata struct {
	Spec          string      `json:"spec"`
	Name          string      `json:"name"`
	Symbol        string      `json:"symbol"`
	Icon          string      `json:"icon"`
	Reference     interface{} `json:"reference"`
	ReferenceHash interface{} `json:"reference_hash"`
	Decimals      uint8       `json:"decimals"`
}

type StorageBalance struct {
	Total     decimal.Decimal `json:"total"`
	Available decimal.Decimal `json:"available"`
}

type StorageBounds struct {
	Min decimal.Decimal `json:"min"`
	Max decimal.Decimal `json:"max"`
}

func GetFtBalanceOf(account *near.Account, accountID string, tokenID string) (decimal.Decimal, error) {
	argsBuf := []byte(fmt.Sprintf(`{"account_id": "%s"}`, accountID))
	resp, err := account.ViewFunction(tokenID, FtBalanceOfMethodName, argsBuf, nil)
	if err != nil {
		return decimal.Decimal{}, fmt.Errorf("function view method %s error: %w", FtBalanceOfMethodName, err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return decimal.Decimal{}, fmt.Errorf("parse ft_balance_of response error: type assertion interface{} to map[string]interface{} failed: resp is of type %T", resp)
	}

	result, found := respMap["result"]
	if !found {
		return decimal.Decimal{}, fmt.Errorf("parse ft_balance_of response failed: result not found")
	}

	resultArray, ok := result.([]interface{})
	if !ok {
		return decimal.Decimal{}, fmt.Errorf("parse ft_balance_of response failed: type assertion interface{} to []interface[} failed: result is of type %T", result)
	}

	bytes := make([]byte, len(resultArray))
	for i, v := range resultArray {
		// Convert json.Number to byte
		jsonNum, ok := v.(json.Number)
		if !ok {
			return decimal.Decimal{}, fmt.Errorf("parse ft_balance_of response failed: type asertion interface{} to json.Number failed: v is of type %T", v)
		}

		byteValue, err := jsonNum.Int64()
		if err != nil {
			return decimal.Decimal{}, fmt.Errorf("parse ft_balance_of response failed: convert json.Number to int64 failed: %w", err)
		}

		bytes[i] = byte(byteValue)
	}

	balanceStr := strings.Trim(string(bytes), `"`)
	balance, err := decimal.NewFromString(balanceStr)
	if err != nil {
		return decimal.Decimal{}, e.NewError500(e.TransactionTransferTokensFailed, "parse ft_balance_of response failed: parse balance error: "+err.Error())
	}

	return balance, nil
}

func GetFtBalanceOfWithRetry(nodeURLs []string, accountID string, privateKey ed25519.PrivateKey, tokenID string) (decimal.Decimal, error) {
	var balanceInDecimals decimal.Decimal
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			balanceInDecimals, err = GetFtBalanceOf(account, accountID, tokenID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get ft balance of %s on node %s: %v, trying next node...", tokenID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get ft balance of %s after retries: %w", tokenID, err)
	}
	return balanceInDecimals, nil
}

func GetFtMetadata(account *near.Account, tokenID string) (*FtMetadata, error) {
	resp, err := account.ViewFunction(tokenID, "ft_metadata", nil, nil)
	if err != nil {
		return nil, fmt.Errorf("call ft_metadata method failed: %w", err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("parse ft_metadata response failed: type assertion interface{} to map[string]interface[} failed: resp is of type %T", resp)
	}

	result, found := respMap["result"]
	if !found {
		return nil, fmt.Errorf("parse ft_metadata response failed: result not found")
	}

	arr, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("parse ft_metadata response failed: type assertion interface{} to []interface[} failed: result is of type %T", result)
	}

	bytes := make([]byte, len(arr))
	for i, v := range arr {
		jsonNum, ok := v.(json.Number)
		if !ok {
			return nil, fmt.Errorf("parse ft_metadata response failed: type asertion interface{} to json.Number failed: v is of type %T", v)
		}

		byteValue, err := jsonNum.Int64()
		if err != nil {
			return nil, fmt.Errorf("parse ft_metadata response failed: convert json.Number to int64 failed: %w", err)
		}

		bytes[i] = byte(byteValue)
	}

	var ftMetadata FtMetadata
	if fErr := json.Unmarshal(bytes, &ftMetadata); fErr != nil {
		return nil, fmt.Errorf("parse ft_metadata response failed: %w", fErr)
	}

	return &ftMetadata, nil
}

func GetFtMetadataWithRetry(nodeURLs []string, accountID string, privateKey ed25519.PrivateKey, tokenID string) (*FtMetadata, error) {
	var ftMetadata *FtMetadata
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			ftMetadata, err = GetFtMetadata(account, tokenID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get ft metadata of %s on node %s: %v, trying next node...", tokenID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get ft metadata of %s after retries: %w", tokenID, err)
	}
	return ftMetadata, nil
}

func GetStorageBalanceOf(account *near.Account, accountID string, tokenID string) (*StorageBalance, error) {
	argsBuf := []byte(fmt.Sprintf(`{"account_id": "%s"}`, accountID))
	resp, err := account.ViewFunction(tokenID, StorageBalanceOfMethodName, argsBuf, nil)
	if err != nil {
		return nil, fmt.Errorf("function view method %s error: %w", StorageBalanceOfMethodName, err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("parse storage_balance_of response error: type assertion interface{} to map[string]interface{} failed: resp is of type %T", resp)
	}

	result, found := respMap["result"]
	if !found {
		return nil, fmt.Errorf("parse storage_balance_of response failed: result not found")
	}

	resultArray, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("parse storage_balance_of response failed: type assertion interface{} to []interface[} failed: result is of type %T", result)
	}

	bytes := make([]byte, len(resultArray))
	for i, v := range resultArray {
		// Convert json.Number to byte
		jsonNum, ok := v.(json.Number)
		if !ok {
			return nil, fmt.Errorf("parse storage_balance_of response failed: type asertion interface{} to json.Number failed: v is of type %T", v)
		}

		byteValue, err := jsonNum.Int64()
		if err != nil {
			return nil, fmt.Errorf("parse storage_balance_of response failed: convert json.Number to int64 failed: %w", err)
		}

		bytes[i] = byte(byteValue)
	}

	var storageBalance StorageBalance
	if fErr := json.Unmarshal(bytes, &storageBalance); fErr != nil {
		return nil, fmt.Errorf("parse storage_balance_of response failed: %w", fErr)
	}

	return &storageBalance, nil
}

func GetStorageBalanceOfWithRetry(nodeURLs []string, accountID string, privateKey ed25519.PrivateKey, tokenID string) (*StorageBalance, error) {
	var storageBalance *StorageBalance
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			storageBalance, err = GetStorageBalanceOf(account, accountID, tokenID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get storage balance of %s on node %s: %v, trying next node...", tokenID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get storage balance of %s after retries: %w", tokenID, err)
	}
	return storageBalance, nil
}

func GetStorageBalanceBounds(account *near.Account, tokenID string) (*StorageBounds, error) {
	resp, err := account.ViewFunction(tokenID, StorageBalanceBoundsMethodName, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("function view method %s error: %w", StorageBalanceBoundsMethodName, err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("parse storage_balance_bounds response error: type assertion interface{} to map[string]interface{} failed: resp is of type %T", resp)
	}

	result, found := respMap["result"]
	if !found {
		return nil, fmt.Errorf("parse storage_balance_bounds response failed: result not found")
	}

	resultArray, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("parse storage_balance_bounds response failed: type assertion interface{} to []interface[} failed: result is of type %T", result)
	}

	bytes := make([]byte, len(resultArray))
	for i, v := range resultArray {
		// Convert json.Number to byte
		jsonNum, ok := v.(json.Number)
		if !ok {
			return nil, fmt.Errorf("parse storage_balance_bounds response failed: type asertion interface{} to json.Number failed: v is of type %T", v)
		}

		byteValue, err := jsonNum.Int64()
		if err != nil {
			return nil, fmt.Errorf("parse storage_balance_bounds response failed: convert json.Number to int64 failed: %w", err)
		}

		bytes[i] = byte(byteValue)
	}

	var storageBounds StorageBounds
	if fErr := json.Unmarshal(bytes, &storageBounds); fErr != nil {
		return nil, fmt.Errorf("parse storage_balance_bounds response failed: %w", fErr)
	}

	return &storageBounds, nil
}

func GetStorageBalanceBoundsWithRetry(nodeURLs []string, accountID string, privateKey ed25519.PrivateKey, tokenID string) (*StorageBounds, error) {
	var storageBounds *StorageBounds
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			storageBounds, err = GetStorageBalanceBounds(account, tokenID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get storage bounds of %s on node %s: %v, trying next node...", tokenID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get storage bounds of %s after retries: %w", tokenID, err)
	}
	return storageBounds, nil
}

func StorageDeposit(account *near.Account, tokenID string, recipientAccountId string, amount decimal.Decimal, gas uint64) (*TransactionDetails, error) {
	argsBuf := []byte(fmt.Sprintf(`{"account_id": "%s", "registration_only": true}`, recipientAccountId))
	respMap, err := account.FunctionCall(tokenID, StorageDepositMethodName, argsBuf, gas, *amount.BigInt())
	if err != nil {
		return nil, fmt.Errorf("function call method %s failed: %w", StorageDepositMethodName, err)
	}

	txDetails, err := util.MapToStruct[TransactionDetails](respMap)
	if err != nil {
		return nil, fmt.Errorf("parse method %s response failed: %w", StorageDepositMethodName, err)
	}

	return &txDetails, nil
}

func StorageDepositWithRetry(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	tokenID string,
	recipientAccountId string,
	amount decimal.Decimal,
	gas uint64,
) (*TransactionDetails, error) {

	var txDetails *TransactionDetails
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			txDetails, err = StorageDeposit(account, tokenID, recipientAccountId, amount, gas)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get storage deposit of %s on node %s: %v, trying next node...", tokenID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get storage deposit of %s after retries: %w", tokenID, err)
	}
	return txDetails, nil
}

func SendFTs(account *near.Account, tokenID string, recipientAccountId string, amount decimal.Decimal, gas uint64) (*TransactionDetails, error) {
	argsBuf := []byte(fmt.Sprintf(
		`{"receiver_id": "%s", "amount": "%s"}`,
		recipientAccountId,
		amount.String(),
	))
	respMap, err := account.FunctionCall(tokenID, FtTransferMethodName, argsBuf, gas, *(big.NewInt(1)))
	if err != nil {
		return nil, fmt.Errorf("function call %s failed: %w", FtTransferMethodName, err)
	}

	txDetails, err := util.MapToStruct[TransactionDetails](respMap)
	if err != nil {
		return nil, fmt.Errorf("parse %s response failed: %w", FtTransferMethodName, err)
	}
	return &txDetails, nil
}

func SendFTsWithRetry(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	tokenID string,
	recipientAccountId string,
	amount decimal.Decimal,
	gas uint64,
) (*TransactionDetails, error) {

	var txDetails *TransactionDetails
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			txDetails, err = SendFTs(account, tokenID, recipientAccountId, amount, gas)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to send FTs on node %s: %v, trying next node...", nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to send FTs after retries: %w", err)
	}
	return txDetails, nil
}
