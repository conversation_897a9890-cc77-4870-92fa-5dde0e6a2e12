package openedu_near_sdk

import (
	"crypto/ed25519"
	"encoding/json"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/cenkalti/backoff/v4"
	"github.com/shopspring/decimal"
	"openedu-blockchain/pkg/log"
	"strings"
)

func GetGasSponsorBalance(account *near.Account, contractID, methodName, courseID, sponsorID string) (decimal.Decimal, error) {
	args := fmt.Sprintf(`{"course_id": "%s", "sponsor_id": "%s"}`, courseID, sponsorID)
	resp, err := account.ViewFunction(
		contractID,
		methodName,
		[]byte(args),
		nil,
	)
	if err != nil {
		return decimal.Zero, fmt.Errorf("call %s method on contract %s failed: %w", methodName, contractID, err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return decimal.Zero, fmt.Errorf("parse %s response failed: type assertion interface{} to map[string]interface{} failed: resp is of type %T", methodName, resp)
	}

	result, found := respMap["result"]
	if !found {
		return decimal.Decimal{}, fmt.Errorf("parse %s response failed: result not found", methodName)
	}

	resultArray, ok := result.([]interface{})
	if !ok {
		return decimal.Decimal{}, fmt.Errorf("parse %s response failed: type assertion interface{} to []interface[} failed: result is of type %T", methodName, result)
	}

	bytes := make([]byte, len(resultArray))
	for i, v := range resultArray {
		// Convert json.Number to byte
		jsonNum, ok := v.(json.Number)
		if !ok {
			return decimal.Decimal{}, fmt.Errorf("parse %s response failed: type asertion interface{} to json.Number failed: v is of type %T", methodName, v)
		}

		byteValue, err := jsonNum.Int64()
		if err != nil {
			return decimal.Decimal{}, fmt.Errorf("parse %s response failed: convert json.Number to int64 failed: %w", methodName, err)
		}

		bytes[i] = byte(byteValue)
	}

	balanceStr := strings.Trim(string(bytes), `"`)
	if balanceStr == "null" {
		return decimal.Zero, nil
	}

	balance, err := decimal.NewFromString(balanceStr)
	if err != nil {
		return decimal.Decimal{}, fmt.Errorf("parse %s response failed: parse balance failed: %v", methodName, err)
	}
	return balance, nil
}

func GetGasSponsorBalanceWithRetry(nodeURLs []string, privateKey ed25519.PrivateKey, contractID, methodName, courseID, sponsorID string) (decimal.Decimal, error) {
	var balance decimal.Decimal
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, sponsorID, privateKey)
			balance, err = GetGasSponsorBalance(account, contractID, methodName, courseID, sponsorID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get sponsor balance for course %s of sponsor ID %s on node %s: %v, trying next node...",
				courseID, sponsorID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get sponsor balance for course %s, sponsor %s after retries: %w",
			courseID, sponsorID, err)
	}
	return balance, nil
}
