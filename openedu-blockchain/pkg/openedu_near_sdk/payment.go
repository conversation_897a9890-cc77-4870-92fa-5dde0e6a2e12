package openedu_near_sdk

import (
	"crypto/ed25519"
	"encoding/json"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/cenkalti/backoff/v4"
	"github.com/shopspring/decimal"
	"math/big"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/util"
)

type UserDeposit struct {
	TokenID string          `json:"token_id"`
	Amount  decimal.Decimal `json:"amount"`
}

type UserEarning struct {
	UserID   string         `json:"user_id"`
	Deposits []*UserDeposit `json:"deposits"`
}

func GetUserEarning(account *near.Account, contractID, methodName, accountID string) (*UserEarning, error) {
	resp, err := account.ViewFunction(
		contractID,
		methodName,
		[]byte(fmt.Sprintf(`{"user_id": "%s"}`, accountID)),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("call %s method on contract %s failed: %w", methodName, contractID, err)
	}

	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("parse %s response failed: type assertion interface{} to map[string]interface[} failed: resp is of type %T", methodName, resp)
	}

	result, found := respMap["result"]
	if !found {
		return nil, fmt.Errorf("parse %s response failed: result not found", methodName)
	}

	arr, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("parse %s response failed: type assertion interface{} to []interface[} failed: result is of type %T", methodName, result)
	}

	bytes := make([]byte, len(arr))
	for i, v := range arr {
		jsonNum, ok := v.(json.Number)
		if !ok {
			return nil, fmt.Errorf("parse %s response failed: type asertion interface{} to json.Number failed: v is of type %T", methodName, v)
		}

		byteValue, bErr := jsonNum.Int64()
		if bErr != nil {
			return nil, fmt.Errorf("parse %s response failed: convert json.Number to int64 failed: %v", methodName, bErr)
		}

		bytes[i] = byte(byteValue)
	}

	var userEarning UserEarning
	if fErr := json.Unmarshal(bytes, &userEarning); fErr != nil {
		return nil, fmt.Errorf("parse %s response failed: %v", methodName, fErr)
	}

	return &userEarning, nil
}

func GetUserEarningWithRetry(nodeURLs []string, privateKey ed25519.PrivateKey, contractID, methodName string, accountID string) (*UserEarning, error) {
	var userEarning *UserEarning
	var err error

	expBackoff := NewDefaultExpBackoffForViewFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			userEarning, err = GetUserEarning(account, contractID, methodName, accountID)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to get earning of user %s on node %s: %v, trying next node...", accountID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to get earning of user %s after retries: %w", accountID, err)
	}
	return userEarning, nil
}

func ClaimEarning(account *near.Account, contractID, method, tokenID string, gas uint64, deposit big.Int) (*TransactionDetails, error) {
	bytes := []byte(fmt.Sprintf(`{"token_id": "%s"}`, tokenID))
	respMap, err := account.FunctionCall(contractID, method, bytes, gas, deposit)
	if err != nil {
		return nil, fmt.Errorf("function call method %s on contract ID %s failed: %w", method, contractID, err)
	}

	txDetails, err := util.MapToStruct[TransactionDetails](respMap)
	if err != nil {
		return nil, fmt.Errorf("parse transaction details response failed: %w", err)
	}

	return &txDetails, nil
}

func ClaimEarningWithRetry(nodeURLs []string, accountID string, privateKey ed25519.PrivateKey, contractID, method, tokenID string, gas uint64, deposit big.Int) (*TransactionDetails, error) {
	var txDetails *TransactionDetails
	var err error

	expBackoff := NewDefaultExpBackoffForCallFunc()
	err = backoff.Retry(func() error {
		for _, nodeURL := range nodeURLs {
			conn := near.NewConnection(nodeURL)
			account := near.LoadAccountWithPrivateKey(conn, accountID, privateKey)
			txDetails, err = ClaimEarning(account, contractID, method, tokenID, gas, deposit)
			if err == nil {
				return nil
			}
			log.Errorf("Failed to claim earning method %s on contract ID %s on node %s: %v, trying next node...", method, contractID, nodeURL, err)
		}
		return err
	}, expBackoff)

	if err != nil {
		return nil, fmt.Errorf("failed to claim earning after retries: %w", err)
	}
	return txDetails, nil
}
