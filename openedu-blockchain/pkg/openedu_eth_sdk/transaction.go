package openedu_eth_sdk

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"math/big"
	"strings"
)

// GetTransactionFailureMsg attempts to retrieve detailed error information for a failed transaction
func GetTransactionFailureMsg(ctx context.Context, client *ethclient.Client, txHash common.Hash, receipt *types.Receipt) (string, error) {
	// First, try to get the transaction
	tx, isPending, err := client.TransactionByHash(ctx, txHash)
	if err != nil {
		return "", fmt.Errorf("failed to get transaction: %w", err)
	}

	if isPending {
		return "transaction is still pending", nil
	}

	// Try to get more details using debug_traceTransaction if available
	var result json.RawMessage
	err = client.Client().CallContext(ctx, &result, DebugTraceTxMethodName, txHash.Hex(), map[string]interface{}{
		"tracer": "callTracer",
	})

	if err == nil && len(result) > 0 {
		var traceResult map[string]interface{}
		if uErr := json.Unmarshal(result, &traceResult); uErr == nil {
			if revertReason, ok := extractRevertReason(traceResult); ok {
				return fmt.Sprintf("reverted: %s", revertReason), nil
			}
		}
	}

	// Try to get more details using eth_call (replaying the transaction)
	var from common.Address
	signer := types.LatestSignerForChainID(tx.ChainId())
	sender, err := types.Sender(signer, tx)
	if err != nil {
		return "", err
	}
	from = sender

	// Create CallMsg from the transaction data
	callMsg := ethereum.CallMsg{
		From:     from,
		To:       tx.To(),
		Gas:      tx.Gas(),
		GasPrice: tx.GasPrice(),
		Value:    tx.Value(),
		Data:     tx.Data(),
	}

	// Set the block number to previous block for the call
	blockNumber := new(big.Int).Sub(receipt.BlockNumber, big.NewInt(1))

	// Call with the transaction parameters to get a potential revert reason
	if _, err = client.CallContract(ctx, callMsg, blockNumber); err != nil {
		return parseRevertReason(err), nil
	}

	// If we couldn't get more specific details, report the logs
	if len(receipt.Logs) > 0 {
		var logDetails []string
		for i, log := range receipt.Logs {
			logDetails = append(logDetails, fmt.Sprintf("log[%d]: address=%s topics=%v",
				i, log.Address.Hex(), formatTopics(log.Topics)))
		}
		return fmt.Sprintf("failed with logs: %s", strings.Join(logDetails, "; ")), nil
	}

	return "", nil
}

// Helper function to format topic arrays
func formatTopics(topics []common.Hash) string {
	var result []string
	for _, topic := range topics {
		result = append(result, topic.Hex())
	}
	return "[" + strings.Join(result, ", ") + "]"
}

func extractRevertReason(traceResult map[string]interface{}) (string, bool) {
	return getRevertReason(traceResult)
}

// getRevertReason searches for an error or revert reason in a trace result
func getRevertReason(traceResult map[string]interface{}) (string, bool) {
	// Check for a direct error message
	if errorMsg, found := traceResult["error"].(string); found && errorMsg != "" {
		return errorMsg, true
	}

	// Check for a direct revert reason
	if revertReason, found := traceResult["revertReason"].(string); found && revertReason != "" {
		return revertReason, true
	}

	// Check for nested calls and search recursively
	nestedCalls, hasNestedCalls := traceResult["calls"].([]interface{})
	if !hasNestedCalls {
		return "", false
	}

	for _, call := range nestedCalls {
		if callMap, ok := call.(map[string]interface{}); ok {
			if reason, found := getRevertReason(callMap); found {
				return reason, true
			}
		}
	}
	return "", false
}

func parseRevertReason(err error) string {
	// Check for standard revert pattern
	errMsg := err.Error()
	revertPrefix := "execution reverted:"
	if idx := strings.Index(errMsg, revertPrefix); idx >= 0 {
		return strings.TrimSpace(errMsg[idx+len(revertPrefix):])
	}

	// Check for hex-encoded revert reason
	hexPrefix := "0x"
	if idx := strings.Index(errMsg, hexPrefix); idx >= 0 {
		hexData := errMsg[idx:]
		if nextSpace := strings.Index(hexData, " "); nextSpace > 0 {
			hexData = hexData[:nextSpace]
		}

		// Try to decode the hex data
		if data, err := hex.DecodeString(hexData[2:]); err == nil {
			// ABI encoding for errors typically has method signature in first 4 bytes
			// followed by string length and string data
			if len(data) > 4 {
				// Skip first 4 bytes (function selector)
				data = data[4:]

				// Try to decode as a string
				if len(data) >= 32 {
					strLen := new(big.Int).SetBytes(data[:32]).Uint64()
					if uint64(len(data)) >= 32+strLen && strLen < 1000 { // reasonable length check
						return string(data[32 : 32+strLen])
					}
				}
			}
		}
	}

	return errMsg
}
