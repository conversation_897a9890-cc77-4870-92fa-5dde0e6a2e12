package openedu_eth_sdk

import (
	"time"
)

const (
	// ETHDerivationPath
	// BIP-44 standard derivation path for Ethereum
	// m/44'/60'/0'/0/0 where:
	// 44' - BIP-44 purpose
	// 60' - Ethereum coin type
	// 0'  - Account 0
	// 0   - External chain (not change addresses)
	// 0   - First address index
	ETHDerivationPath = "m/44'/60'/0'/0/0"

	ETHDecimalExp = 18

	DefaultTransferGasLimit      = 300000
	DefaultTokenTransferGasLimit = 100000
	DefaultGasLimit              = 300000

	TxConfirmationTimeout = 200 * time.Second
	TxPollingInterval     = 2 * time.Second

	SendTxMethodName       = "eth_sendTransaction"
	DebugTraceTxMethodName = "debug_traceTransaction"

	ETHAddressHex = "******************************************"
)
