package openedu_eth_sdk

import (
	"fmt"
	"github.com/ethereum/go-ethereum/common"
	"math/big"
)

type AccountInfo struct {
	Nonce    uint64   `json:"nonce"`
	Balance  *big.Int `json:"balance"`
	GasPrice *big.Int `json:"gasPrice"`
	Code     []byte   `json:"code"` // For tokens and smart contracts
}

func ParseAddressFromHex(addrHexStr string) (common.Address, error) {
	// Check if address is valid
	if !common.IsHexAddress(addrHexStr) {
		return common.Address{}, fmt.Errorf("invalid address: %s is not hex address", addrHexStr)
	}

	address := common.HexToAddress(addrHexStr)
	return address, nil
}
