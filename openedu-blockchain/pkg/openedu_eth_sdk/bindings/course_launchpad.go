// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package bindings

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

// ICourseLaunchpadLaunchpad is an auto generated low-level Go binding around an user-defined struct.
type ICourseLaunchpadLaunchpad struct {
	Owner            common.Address
	Token            common.Address
	Goal             *big.Int
	TotalPledged     *big.Int
	Raised           *big.Int
	AvailableClaim   *big.Int
	StartFundingTime *big.Int
	EndFundingTime   *big.Int
	StakeAmount      *big.Int
	MinPledgeAmount  *big.Int
	Status           uint8
}

// CourseLaunchpadMetaData contains all meta data concerning the CourseLaunchpad contract.
var CourseLaunchpadMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"provided\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"required\",\"type\":\"uint256\"}],\"name\":\"InvalidAmount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"provided\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"required\",\"type\":\"uint256\"}],\"name\":\"InvalidFundingDuration\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"enumICourseLaunchpad.LaunchpadStatus\",\"name\":\"current\",\"type\":\"uint8\"},{\"internalType\":\"enumICourseLaunchpad.LaunchpadStatus\",\"name\":\"required\",\"type\":\"uint8\"}],\"name\":\"InvalidStatus\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"InvalidToken\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"LaunchpadAlreadyExists\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"reason\",\"type\":\"string\"}],\"name\":\"TransactionFailed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"reason\",\"type\":\"string\"}],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"actor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"actionType\",\"type\":\"string\"}],\"name\":\"FundingAction\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"goal\",\"type\":\"uint256\"}],\"name\":\"LaunchpadCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"enumICourseLaunchpad.LaunchpadStatus\",\"name\":\"oldStatus\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"enumICourseLaunchpad.LaunchpadStatus\",\"name\":\"newStatus\",\"type\":\"uint8\"}],\"name\":\"LaunchpadStatusUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferStarted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"availableClaim\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"successful\",\"type\":\"bool\"}],\"name\":\"VotingResult\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_value\",\"type\":\"string\"}],\"name\":\"_removeRefundingLaunchpad\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"acceptFunding\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"addAcceptedToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"approveLaunchpad\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"cancelLaunchpad\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"claimFunding\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"availableClaim\",\"type\":\"uint256\"}],\"name\":\"completeVotingPhase\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"enumICourseLaunchpad.LaunchpadStatus\",\"name\":\"status\",\"type\":\"uint8\"}],\"name\":\"emergencyChangeStatus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"endFundingResult\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"isSuccessful\",\"type\":\"bool\"}],\"name\":\"endLaunchpad\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAllRefundingLaunchpads\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"backer\",\"type\":\"address\"}],\"name\":\"getBackerBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"getLaunchpad\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"goal\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"totalPledged\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"raised\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"availableClaim\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"startFundingTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endFundingTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"stakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minPledgeAmount\",\"type\":\"uint256\"},{\"internalType\":\"enumICourseLaunchpad.LaunchpadStatus\",\"name\":\"status\",\"type\":\"uint8\"}],\"internalType\":\"structICourseLaunchpad.Launchpad\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"launchpadOwner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"goal\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minPledgeAmount\",\"type\":\"uint256\"}],\"name\":\"initLaunchpad\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingOwner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"pledgeERC20\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"fee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"pledgeERC20withPermit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"pledgeNative\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"refundContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"receiversRoot\",\"type\":\"bytes32\"}],\"name\":\"refundLaunchpad\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"}],\"name\":\"rejectLaunchpad\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"removeAcceptedToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxFundingBps\",\"type\":\"uint256\"}],\"name\":\"setMaxFundingBps\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxFundingDuration\",\"type\":\"uint256\"}],\"name\":\"setMaxFundingDuration\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxPledgeBps\",\"type\":\"uint256\"}],\"name\":\"setMaxPledgeBps\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requiredStakeAmount\",\"type\":\"uint256\"}],\"name\":\"setRequiredStakeAmount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requiredVotingBps\",\"type\":\"uint256\"}],\"name\":\"setRequiredVotingBps\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"launchpadId\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"startFundingTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endFundingTime\",\"type\":\"uint256\"}],\"name\":\"startFunding\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}]",
}

// CourseLaunchpadABI is the input ABI used to generate the binding from.
// Deprecated: Use CourseLaunchpadMetaData.ABI instead.
var CourseLaunchpadABI = CourseLaunchpadMetaData.ABI

// CourseLaunchpad is an auto generated Go binding around an Ethereum contract.
type CourseLaunchpad struct {
	CourseLaunchpadCaller     // Read-only binding to the contract
	CourseLaunchpadTransactor // Write-only binding to the contract
	CourseLaunchpadFilterer   // Log filterer for contract events
}

// CourseLaunchpadCaller is an auto generated read-only Go binding around an Ethereum contract.
type CourseLaunchpadCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// CourseLaunchpadTransactor is an auto generated write-only Go binding around an Ethereum contract.
type CourseLaunchpadTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// CourseLaunchpadFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type CourseLaunchpadFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// CourseLaunchpadSession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type CourseLaunchpadSession struct {
	Contract     *CourseLaunchpad  // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// CourseLaunchpadCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type CourseLaunchpadCallerSession struct {
	Contract *CourseLaunchpadCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts          // Call options to use throughout this session
}

// CourseLaunchpadTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type CourseLaunchpadTransactorSession struct {
	Contract     *CourseLaunchpadTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts          // Transaction auth options to use throughout this session
}

// CourseLaunchpadRaw is an auto generated low-level Go binding around an Ethereum contract.
type CourseLaunchpadRaw struct {
	Contract *CourseLaunchpad // Generic contract binding to access the raw methods on
}

// CourseLaunchpadCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type CourseLaunchpadCallerRaw struct {
	Contract *CourseLaunchpadCaller // Generic read-only contract binding to access the raw methods on
}

// CourseLaunchpadTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type CourseLaunchpadTransactorRaw struct {
	Contract *CourseLaunchpadTransactor // Generic write-only contract binding to access the raw methods on
}

// NewCourseLaunchpad creates a new instance of CourseLaunchpad, bound to a specific deployed contract.
func NewCourseLaunchpad(address common.Address, backend bind.ContractBackend) (*CourseLaunchpad, error) {
	contract, err := bindCourseLaunchpad(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpad{CourseLaunchpadCaller: CourseLaunchpadCaller{contract: contract}, CourseLaunchpadTransactor: CourseLaunchpadTransactor{contract: contract}, CourseLaunchpadFilterer: CourseLaunchpadFilterer{contract: contract}}, nil
}

// NewCourseLaunchpadCaller creates a new read-only instance of CourseLaunchpad, bound to a specific deployed contract.
func NewCourseLaunchpadCaller(address common.Address, caller bind.ContractCaller) (*CourseLaunchpadCaller, error) {
	contract, err := bindCourseLaunchpad(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadCaller{contract: contract}, nil
}

// NewCourseLaunchpadTransactor creates a new write-only instance of CourseLaunchpad, bound to a specific deployed contract.
func NewCourseLaunchpadTransactor(address common.Address, transactor bind.ContractTransactor) (*CourseLaunchpadTransactor, error) {
	contract, err := bindCourseLaunchpad(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadTransactor{contract: contract}, nil
}

// NewCourseLaunchpadFilterer creates a new log filterer instance of CourseLaunchpad, bound to a specific deployed contract.
func NewCourseLaunchpadFilterer(address common.Address, filterer bind.ContractFilterer) (*CourseLaunchpadFilterer, error) {
	contract, err := bindCourseLaunchpad(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadFilterer{contract: contract}, nil
}

// bindCourseLaunchpad binds a generic wrapper to an already deployed contract.
func bindCourseLaunchpad(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := CourseLaunchpadMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_CourseLaunchpad *CourseLaunchpadRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CourseLaunchpad.Contract.CourseLaunchpadCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_CourseLaunchpad *CourseLaunchpadRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.CourseLaunchpadTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_CourseLaunchpad *CourseLaunchpadRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.CourseLaunchpadTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_CourseLaunchpad *CourseLaunchpadCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CourseLaunchpad.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_CourseLaunchpad *CourseLaunchpadTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_CourseLaunchpad *CourseLaunchpadTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.contract.Transact(opts, method, params...)
}

// GetAllRefundingLaunchpads is a free data retrieval call binding the contract method 0x2a7bc030.
//
// Solidity: function getAllRefundingLaunchpads() view returns(string[])
func (_CourseLaunchpad *CourseLaunchpadCaller) GetAllRefundingLaunchpads(opts *bind.CallOpts) ([]string, error) {
	var out []interface{}
	err := _CourseLaunchpad.contract.Call(opts, &out, "getAllRefundingLaunchpads")

	if err != nil {
		return *new([]string), err
	}

	out0 := *abi.ConvertType(out[0], new([]string)).(*[]string)

	return out0, err

}

// GetAllRefundingLaunchpads is a free data retrieval call binding the contract method 0x2a7bc030.
//
// Solidity: function getAllRefundingLaunchpads() view returns(string[])
func (_CourseLaunchpad *CourseLaunchpadSession) GetAllRefundingLaunchpads() ([]string, error) {
	return _CourseLaunchpad.Contract.GetAllRefundingLaunchpads(&_CourseLaunchpad.CallOpts)
}

// GetAllRefundingLaunchpads is a free data retrieval call binding the contract method 0x2a7bc030.
//
// Solidity: function getAllRefundingLaunchpads() view returns(string[])
func (_CourseLaunchpad *CourseLaunchpadCallerSession) GetAllRefundingLaunchpads() ([]string, error) {
	return _CourseLaunchpad.Contract.GetAllRefundingLaunchpads(&_CourseLaunchpad.CallOpts)
}

// GetBackerBalance is a free data retrieval call binding the contract method 0x07285d26.
//
// Solidity: function getBackerBalance(string launchpadId, address backer) view returns(uint256)
func (_CourseLaunchpad *CourseLaunchpadCaller) GetBackerBalance(opts *bind.CallOpts, launchpadId string, backer common.Address) (*big.Int, error) {
	var out []interface{}
	err := _CourseLaunchpad.contract.Call(opts, &out, "getBackerBalance", launchpadId, backer)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetBackerBalance is a free data retrieval call binding the contract method 0x07285d26.
//
// Solidity: function getBackerBalance(string launchpadId, address backer) view returns(uint256)
func (_CourseLaunchpad *CourseLaunchpadSession) GetBackerBalance(launchpadId string, backer common.Address) (*big.Int, error) {
	return _CourseLaunchpad.Contract.GetBackerBalance(&_CourseLaunchpad.CallOpts, launchpadId, backer)
}

// GetBackerBalance is a free data retrieval call binding the contract method 0x07285d26.
//
// Solidity: function getBackerBalance(string launchpadId, address backer) view returns(uint256)
func (_CourseLaunchpad *CourseLaunchpadCallerSession) GetBackerBalance(launchpadId string, backer common.Address) (*big.Int, error) {
	return _CourseLaunchpad.Contract.GetBackerBalance(&_CourseLaunchpad.CallOpts, launchpadId, backer)
}

// GetLaunchpad is a free data retrieval call binding the contract method 0xe085a23e.
//
// Solidity: function getLaunchpad(string launchpadId) view returns((address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint8))
func (_CourseLaunchpad *CourseLaunchpadCaller) GetLaunchpad(opts *bind.CallOpts, launchpadId string) (ICourseLaunchpadLaunchpad, error) {
	var out []interface{}
	err := _CourseLaunchpad.contract.Call(opts, &out, "getLaunchpad", launchpadId)

	if err != nil {
		return *new(ICourseLaunchpadLaunchpad), err
	}

	out0 := *abi.ConvertType(out[0], new(ICourseLaunchpadLaunchpad)).(*ICourseLaunchpadLaunchpad)

	return out0, err

}

// GetLaunchpad is a free data retrieval call binding the contract method 0xe085a23e.
//
// Solidity: function getLaunchpad(string launchpadId) view returns((address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint8))
func (_CourseLaunchpad *CourseLaunchpadSession) GetLaunchpad(launchpadId string) (ICourseLaunchpadLaunchpad, error) {
	return _CourseLaunchpad.Contract.GetLaunchpad(&_CourseLaunchpad.CallOpts, launchpadId)
}

// GetLaunchpad is a free data retrieval call binding the contract method 0xe085a23e.
//
// Solidity: function getLaunchpad(string launchpadId) view returns((address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint8))
func (_CourseLaunchpad *CourseLaunchpadCallerSession) GetLaunchpad(launchpadId string) (ICourseLaunchpadLaunchpad, error) {
	return _CourseLaunchpad.Contract.GetLaunchpad(&_CourseLaunchpad.CallOpts, launchpadId)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_CourseLaunchpad *CourseLaunchpadCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CourseLaunchpad.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_CourseLaunchpad *CourseLaunchpadSession) Owner() (common.Address, error) {
	return _CourseLaunchpad.Contract.Owner(&_CourseLaunchpad.CallOpts)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_CourseLaunchpad *CourseLaunchpadCallerSession) Owner() (common.Address, error) {
	return _CourseLaunchpad.Contract.Owner(&_CourseLaunchpad.CallOpts)
}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_CourseLaunchpad *CourseLaunchpadCaller) PendingOwner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CourseLaunchpad.contract.Call(opts, &out, "pendingOwner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_CourseLaunchpad *CourseLaunchpadSession) PendingOwner() (common.Address, error) {
	return _CourseLaunchpad.Contract.PendingOwner(&_CourseLaunchpad.CallOpts)
}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_CourseLaunchpad *CourseLaunchpadCallerSession) PendingOwner() (common.Address, error) {
	return _CourseLaunchpad.Contract.PendingOwner(&_CourseLaunchpad.CallOpts)
}

// RemoveRefundingLaunchpad is a paid mutator transaction binding the contract method 0x78318a7a.
//
// Solidity: function _removeRefundingLaunchpad(string _value) returns(bool)
func (_CourseLaunchpad *CourseLaunchpadTransactor) RemoveRefundingLaunchpad(opts *bind.TransactOpts, _value string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "_removeRefundingLaunchpad", _value)
}

// RemoveRefundingLaunchpad is a paid mutator transaction binding the contract method 0x78318a7a.
//
// Solidity: function _removeRefundingLaunchpad(string _value) returns(bool)
func (_CourseLaunchpad *CourseLaunchpadSession) RemoveRefundingLaunchpad(_value string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RemoveRefundingLaunchpad(&_CourseLaunchpad.TransactOpts, _value)
}

// RemoveRefundingLaunchpad is a paid mutator transaction binding the contract method 0x78318a7a.
//
// Solidity: function _removeRefundingLaunchpad(string _value) returns(bool)
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) RemoveRefundingLaunchpad(_value string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RemoveRefundingLaunchpad(&_CourseLaunchpad.TransactOpts, _value)
}

// AcceptFunding is a paid mutator transaction binding the contract method 0x9bcae654.
//
// Solidity: function acceptFunding(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) AcceptFunding(opts *bind.TransactOpts, launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "acceptFunding", launchpadId)
}

// AcceptFunding is a paid mutator transaction binding the contract method 0x9bcae654.
//
// Solidity: function acceptFunding(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) AcceptFunding(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.AcceptFunding(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// AcceptFunding is a paid mutator transaction binding the contract method 0x9bcae654.
//
// Solidity: function acceptFunding(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) AcceptFunding(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.AcceptFunding(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "acceptOwnership")
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_CourseLaunchpad *CourseLaunchpadSession) AcceptOwnership() (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.AcceptOwnership(&_CourseLaunchpad.TransactOpts)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.AcceptOwnership(&_CourseLaunchpad.TransactOpts)
}

// AddAcceptedToken is a paid mutator transaction binding the contract method 0x3eee83f1.
//
// Solidity: function addAcceptedToken(address token) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) AddAcceptedToken(opts *bind.TransactOpts, token common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "addAcceptedToken", token)
}

// AddAcceptedToken is a paid mutator transaction binding the contract method 0x3eee83f1.
//
// Solidity: function addAcceptedToken(address token) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) AddAcceptedToken(token common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.AddAcceptedToken(&_CourseLaunchpad.TransactOpts, token)
}

// AddAcceptedToken is a paid mutator transaction binding the contract method 0x3eee83f1.
//
// Solidity: function addAcceptedToken(address token) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) AddAcceptedToken(token common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.AddAcceptedToken(&_CourseLaunchpad.TransactOpts, token)
}

// ApproveLaunchpad is a paid mutator transaction binding the contract method 0x806bced5.
//
// Solidity: function approveLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) ApproveLaunchpad(opts *bind.TransactOpts, launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "approveLaunchpad", launchpadId)
}

// ApproveLaunchpad is a paid mutator transaction binding the contract method 0x806bced5.
//
// Solidity: function approveLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) ApproveLaunchpad(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.ApproveLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// ApproveLaunchpad is a paid mutator transaction binding the contract method 0x806bced5.
//
// Solidity: function approveLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) ApproveLaunchpad(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.ApproveLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// CancelLaunchpad is a paid mutator transaction binding the contract method 0x3b7860c0.
//
// Solidity: function cancelLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) CancelLaunchpad(opts *bind.TransactOpts, launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "cancelLaunchpad", launchpadId)
}

// CancelLaunchpad is a paid mutator transaction binding the contract method 0x3b7860c0.
//
// Solidity: function cancelLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) CancelLaunchpad(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.CancelLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// CancelLaunchpad is a paid mutator transaction binding the contract method 0x3b7860c0.
//
// Solidity: function cancelLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) CancelLaunchpad(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.CancelLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// ClaimFunding is a paid mutator transaction binding the contract method 0x33f0d67c.
//
// Solidity: function claimFunding(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) ClaimFunding(opts *bind.TransactOpts, launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "claimFunding", launchpadId)
}

// ClaimFunding is a paid mutator transaction binding the contract method 0x33f0d67c.
//
// Solidity: function claimFunding(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) ClaimFunding(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.ClaimFunding(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// ClaimFunding is a paid mutator transaction binding the contract method 0x33f0d67c.
//
// Solidity: function claimFunding(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) ClaimFunding(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.ClaimFunding(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// CompleteVotingPhase is a paid mutator transaction binding the contract method 0x9f0f5b87.
//
// Solidity: function completeVotingPhase(string launchpadId, uint256 availableClaim) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) CompleteVotingPhase(opts *bind.TransactOpts, launchpadId string, availableClaim *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "completeVotingPhase", launchpadId, availableClaim)
}

// CompleteVotingPhase is a paid mutator transaction binding the contract method 0x9f0f5b87.
//
// Solidity: function completeVotingPhase(string launchpadId, uint256 availableClaim) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) CompleteVotingPhase(launchpadId string, availableClaim *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.CompleteVotingPhase(&_CourseLaunchpad.TransactOpts, launchpadId, availableClaim)
}

// CompleteVotingPhase is a paid mutator transaction binding the contract method 0x9f0f5b87.
//
// Solidity: function completeVotingPhase(string launchpadId, uint256 availableClaim) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) CompleteVotingPhase(launchpadId string, availableClaim *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.CompleteVotingPhase(&_CourseLaunchpad.TransactOpts, launchpadId, availableClaim)
}

// EmergencyChangeStatus is a paid mutator transaction binding the contract method 0x2f1fc276.
//
// Solidity: function emergencyChangeStatus(string launchpadId, uint8 status) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) EmergencyChangeStatus(opts *bind.TransactOpts, launchpadId string, status uint8) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "emergencyChangeStatus", launchpadId, status)
}

// EmergencyChangeStatus is a paid mutator transaction binding the contract method 0x2f1fc276.
//
// Solidity: function emergencyChangeStatus(string launchpadId, uint8 status) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) EmergencyChangeStatus(launchpadId string, status uint8) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.EmergencyChangeStatus(&_CourseLaunchpad.TransactOpts, launchpadId, status)
}

// EmergencyChangeStatus is a paid mutator transaction binding the contract method 0x2f1fc276.
//
// Solidity: function emergencyChangeStatus(string launchpadId, uint8 status) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) EmergencyChangeStatus(launchpadId string, status uint8) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.EmergencyChangeStatus(&_CourseLaunchpad.TransactOpts, launchpadId, status)
}

// EndFundingResult is a paid mutator transaction binding the contract method 0x8f1737bb.
//
// Solidity: function endFundingResult(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) EndFundingResult(opts *bind.TransactOpts, launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "endFundingResult", launchpadId)
}

// EndFundingResult is a paid mutator transaction binding the contract method 0x8f1737bb.
//
// Solidity: function endFundingResult(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) EndFundingResult(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.EndFundingResult(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// EndFundingResult is a paid mutator transaction binding the contract method 0x8f1737bb.
//
// Solidity: function endFundingResult(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) EndFundingResult(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.EndFundingResult(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// EndLaunchpad is a paid mutator transaction binding the contract method 0xdd259440.
//
// Solidity: function endLaunchpad(string launchpadId, bool isSuccessful) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) EndLaunchpad(opts *bind.TransactOpts, launchpadId string, isSuccessful bool) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "endLaunchpad", launchpadId, isSuccessful)
}

// EndLaunchpad is a paid mutator transaction binding the contract method 0xdd259440.
//
// Solidity: function endLaunchpad(string launchpadId, bool isSuccessful) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) EndLaunchpad(launchpadId string, isSuccessful bool) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.EndLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId, isSuccessful)
}

// EndLaunchpad is a paid mutator transaction binding the contract method 0xdd259440.
//
// Solidity: function endLaunchpad(string launchpadId, bool isSuccessful) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) EndLaunchpad(launchpadId string, isSuccessful bool) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.EndLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId, isSuccessful)
}

// InitLaunchpad is a paid mutator transaction binding the contract method 0xcf03a563.
//
// Solidity: function initLaunchpad(string launchpadId, address launchpadOwner, address token, uint256 goal, uint256 minPledgeAmount) payable returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) InitLaunchpad(opts *bind.TransactOpts, launchpadId string, launchpadOwner common.Address, token common.Address, goal *big.Int, minPledgeAmount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "initLaunchpad", launchpadId, launchpadOwner, token, goal, minPledgeAmount)
}

// InitLaunchpad is a paid mutator transaction binding the contract method 0xcf03a563.
//
// Solidity: function initLaunchpad(string launchpadId, address launchpadOwner, address token, uint256 goal, uint256 minPledgeAmount) payable returns()
func (_CourseLaunchpad *CourseLaunchpadSession) InitLaunchpad(launchpadId string, launchpadOwner common.Address, token common.Address, goal *big.Int, minPledgeAmount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.InitLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId, launchpadOwner, token, goal, minPledgeAmount)
}

// InitLaunchpad is a paid mutator transaction binding the contract method 0xcf03a563.
//
// Solidity: function initLaunchpad(string launchpadId, address launchpadOwner, address token, uint256 goal, uint256 minPledgeAmount) payable returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) InitLaunchpad(launchpadId string, launchpadOwner common.Address, token common.Address, goal *big.Int, minPledgeAmount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.InitLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId, launchpadOwner, token, goal, minPledgeAmount)
}

// PledgeERC20 is a paid mutator transaction binding the contract method 0x6f5222fc.
//
// Solidity: function pledgeERC20(string launchpadId, uint256 amount) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) PledgeERC20(opts *bind.TransactOpts, launchpadId string, amount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "pledgeERC20", launchpadId, amount)
}

// PledgeERC20 is a paid mutator transaction binding the contract method 0x6f5222fc.
//
// Solidity: function pledgeERC20(string launchpadId, uint256 amount) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) PledgeERC20(launchpadId string, amount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.PledgeERC20(&_CourseLaunchpad.TransactOpts, launchpadId, amount)
}

// PledgeERC20 is a paid mutator transaction binding the contract method 0x6f5222fc.
//
// Solidity: function pledgeERC20(string launchpadId, uint256 amount) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) PledgeERC20(launchpadId string, amount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.PledgeERC20(&_CourseLaunchpad.TransactOpts, launchpadId, amount)
}

// PledgeERC20withPermit is a paid mutator transaction binding the contract method 0x1896d236.
//
// Solidity: function pledgeERC20withPermit(string launchpadId, uint256 amount, uint256 fee, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) PledgeERC20withPermit(opts *bind.TransactOpts, launchpadId string, amount *big.Int, fee *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "pledgeERC20withPermit", launchpadId, amount, fee, deadline, v, r, s)
}

// PledgeERC20withPermit is a paid mutator transaction binding the contract method 0x1896d236.
//
// Solidity: function pledgeERC20withPermit(string launchpadId, uint256 amount, uint256 fee, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) PledgeERC20withPermit(launchpadId string, amount *big.Int, fee *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.PledgeERC20withPermit(&_CourseLaunchpad.TransactOpts, launchpadId, amount, fee, deadline, v, r, s)
}

// PledgeERC20withPermit is a paid mutator transaction binding the contract method 0x1896d236.
//
// Solidity: function pledgeERC20withPermit(string launchpadId, uint256 amount, uint256 fee, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) PledgeERC20withPermit(launchpadId string, amount *big.Int, fee *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.PledgeERC20withPermit(&_CourseLaunchpad.TransactOpts, launchpadId, amount, fee, deadline, v, r, s)
}

// PledgeNative is a paid mutator transaction binding the contract method 0x3ed682e0.
//
// Solidity: function pledgeNative(string launchpadId) payable returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) PledgeNative(opts *bind.TransactOpts, launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "pledgeNative", launchpadId)
}

// PledgeNative is a paid mutator transaction binding the contract method 0x3ed682e0.
//
// Solidity: function pledgeNative(string launchpadId) payable returns()
func (_CourseLaunchpad *CourseLaunchpadSession) PledgeNative(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.PledgeNative(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// PledgeNative is a paid mutator transaction binding the contract method 0x3ed682e0.
//
// Solidity: function pledgeNative(string launchpadId) payable returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) PledgeNative(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.PledgeNative(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// RefundLaunchpad is a paid mutator transaction binding the contract method 0xfe5060b5.
//
// Solidity: function refundLaunchpad(string launchpadId, address refundContract, bytes32 receiversRoot) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) RefundLaunchpad(opts *bind.TransactOpts, launchpadId string, refundContract common.Address, receiversRoot [32]byte) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "refundLaunchpad", launchpadId, refundContract, receiversRoot)
}

// RefundLaunchpad is a paid mutator transaction binding the contract method 0xfe5060b5.
//
// Solidity: function refundLaunchpad(string launchpadId, address refundContract, bytes32 receiversRoot) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) RefundLaunchpad(launchpadId string, refundContract common.Address, receiversRoot [32]byte) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RefundLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId, refundContract, receiversRoot)
}

// RefundLaunchpad is a paid mutator transaction binding the contract method 0xfe5060b5.
//
// Solidity: function refundLaunchpad(string launchpadId, address refundContract, bytes32 receiversRoot) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) RefundLaunchpad(launchpadId string, refundContract common.Address, receiversRoot [32]byte) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RefundLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId, refundContract, receiversRoot)
}

// RejectLaunchpad is a paid mutator transaction binding the contract method 0xac228013.
//
// Solidity: function rejectLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) RejectLaunchpad(opts *bind.TransactOpts, launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "rejectLaunchpad", launchpadId)
}

// RejectLaunchpad is a paid mutator transaction binding the contract method 0xac228013.
//
// Solidity: function rejectLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) RejectLaunchpad(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RejectLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// RejectLaunchpad is a paid mutator transaction binding the contract method 0xac228013.
//
// Solidity: function rejectLaunchpad(string launchpadId) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) RejectLaunchpad(launchpadId string) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RejectLaunchpad(&_CourseLaunchpad.TransactOpts, launchpadId)
}

// RemoveAcceptedToken is a paid mutator transaction binding the contract method 0xd8134a23.
//
// Solidity: function removeAcceptedToken(address token) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) RemoveAcceptedToken(opts *bind.TransactOpts, token common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "removeAcceptedToken", token)
}

// RemoveAcceptedToken is a paid mutator transaction binding the contract method 0xd8134a23.
//
// Solidity: function removeAcceptedToken(address token) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) RemoveAcceptedToken(token common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RemoveAcceptedToken(&_CourseLaunchpad.TransactOpts, token)
}

// RemoveAcceptedToken is a paid mutator transaction binding the contract method 0xd8134a23.
//
// Solidity: function removeAcceptedToken(address token) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) RemoveAcceptedToken(token common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RemoveAcceptedToken(&_CourseLaunchpad.TransactOpts, token)
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) RenounceOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "renounceOwnership")
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_CourseLaunchpad *CourseLaunchpadSession) RenounceOwnership() (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RenounceOwnership(&_CourseLaunchpad.TransactOpts)
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) RenounceOwnership() (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.RenounceOwnership(&_CourseLaunchpad.TransactOpts)
}

// SetMaxFundingBps is a paid mutator transaction binding the contract method 0x18dc30f1.
//
// Solidity: function setMaxFundingBps(uint256 maxFundingBps) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) SetMaxFundingBps(opts *bind.TransactOpts, maxFundingBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "setMaxFundingBps", maxFundingBps)
}

// SetMaxFundingBps is a paid mutator transaction binding the contract method 0x18dc30f1.
//
// Solidity: function setMaxFundingBps(uint256 maxFundingBps) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) SetMaxFundingBps(maxFundingBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetMaxFundingBps(&_CourseLaunchpad.TransactOpts, maxFundingBps)
}

// SetMaxFundingBps is a paid mutator transaction binding the contract method 0x18dc30f1.
//
// Solidity: function setMaxFundingBps(uint256 maxFundingBps) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) SetMaxFundingBps(maxFundingBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetMaxFundingBps(&_CourseLaunchpad.TransactOpts, maxFundingBps)
}

// SetMaxFundingDuration is a paid mutator transaction binding the contract method 0xd1c6aa5e.
//
// Solidity: function setMaxFundingDuration(uint256 maxFundingDuration) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) SetMaxFundingDuration(opts *bind.TransactOpts, maxFundingDuration *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "setMaxFundingDuration", maxFundingDuration)
}

// SetMaxFundingDuration is a paid mutator transaction binding the contract method 0xd1c6aa5e.
//
// Solidity: function setMaxFundingDuration(uint256 maxFundingDuration) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) SetMaxFundingDuration(maxFundingDuration *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetMaxFundingDuration(&_CourseLaunchpad.TransactOpts, maxFundingDuration)
}

// SetMaxFundingDuration is a paid mutator transaction binding the contract method 0xd1c6aa5e.
//
// Solidity: function setMaxFundingDuration(uint256 maxFundingDuration) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) SetMaxFundingDuration(maxFundingDuration *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetMaxFundingDuration(&_CourseLaunchpad.TransactOpts, maxFundingDuration)
}

// SetMaxPledgeBps is a paid mutator transaction binding the contract method 0xd19d0049.
//
// Solidity: function setMaxPledgeBps(uint256 maxPledgeBps) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) SetMaxPledgeBps(opts *bind.TransactOpts, maxPledgeBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "setMaxPledgeBps", maxPledgeBps)
}

// SetMaxPledgeBps is a paid mutator transaction binding the contract method 0xd19d0049.
//
// Solidity: function setMaxPledgeBps(uint256 maxPledgeBps) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) SetMaxPledgeBps(maxPledgeBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetMaxPledgeBps(&_CourseLaunchpad.TransactOpts, maxPledgeBps)
}

// SetMaxPledgeBps is a paid mutator transaction binding the contract method 0xd19d0049.
//
// Solidity: function setMaxPledgeBps(uint256 maxPledgeBps) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) SetMaxPledgeBps(maxPledgeBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetMaxPledgeBps(&_CourseLaunchpad.TransactOpts, maxPledgeBps)
}

// SetRequiredStakeAmount is a paid mutator transaction binding the contract method 0x1866f8bd.
//
// Solidity: function setRequiredStakeAmount(uint256 requiredStakeAmount) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) SetRequiredStakeAmount(opts *bind.TransactOpts, requiredStakeAmount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "setRequiredStakeAmount", requiredStakeAmount)
}

// SetRequiredStakeAmount is a paid mutator transaction binding the contract method 0x1866f8bd.
//
// Solidity: function setRequiredStakeAmount(uint256 requiredStakeAmount) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) SetRequiredStakeAmount(requiredStakeAmount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetRequiredStakeAmount(&_CourseLaunchpad.TransactOpts, requiredStakeAmount)
}

// SetRequiredStakeAmount is a paid mutator transaction binding the contract method 0x1866f8bd.
//
// Solidity: function setRequiredStakeAmount(uint256 requiredStakeAmount) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) SetRequiredStakeAmount(requiredStakeAmount *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetRequiredStakeAmount(&_CourseLaunchpad.TransactOpts, requiredStakeAmount)
}

// SetRequiredVotingBps is a paid mutator transaction binding the contract method 0x50542e1a.
//
// Solidity: function setRequiredVotingBps(uint256 requiredVotingBps) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) SetRequiredVotingBps(opts *bind.TransactOpts, requiredVotingBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "setRequiredVotingBps", requiredVotingBps)
}

// SetRequiredVotingBps is a paid mutator transaction binding the contract method 0x50542e1a.
//
// Solidity: function setRequiredVotingBps(uint256 requiredVotingBps) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) SetRequiredVotingBps(requiredVotingBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetRequiredVotingBps(&_CourseLaunchpad.TransactOpts, requiredVotingBps)
}

// SetRequiredVotingBps is a paid mutator transaction binding the contract method 0x50542e1a.
//
// Solidity: function setRequiredVotingBps(uint256 requiredVotingBps) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) SetRequiredVotingBps(requiredVotingBps *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.SetRequiredVotingBps(&_CourseLaunchpad.TransactOpts, requiredVotingBps)
}

// StartFunding is a paid mutator transaction binding the contract method 0x6656b65d.
//
// Solidity: function startFunding(string launchpadId, uint256 startFundingTime, uint256 endFundingTime) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) StartFunding(opts *bind.TransactOpts, launchpadId string, startFundingTime *big.Int, endFundingTime *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "startFunding", launchpadId, startFundingTime, endFundingTime)
}

// StartFunding is a paid mutator transaction binding the contract method 0x6656b65d.
//
// Solidity: function startFunding(string launchpadId, uint256 startFundingTime, uint256 endFundingTime) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) StartFunding(launchpadId string, startFundingTime *big.Int, endFundingTime *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.StartFunding(&_CourseLaunchpad.TransactOpts, launchpadId, startFundingTime, endFundingTime)
}

// StartFunding is a paid mutator transaction binding the contract method 0x6656b65d.
//
// Solidity: function startFunding(string launchpadId, uint256 startFundingTime, uint256 endFundingTime) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) StartFunding(launchpadId string, startFundingTime *big.Int, endFundingTime *big.Int) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.StartFunding(&_CourseLaunchpad.TransactOpts, launchpadId, startFundingTime, endFundingTime)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactor) TransferOwnership(opts *bind.TransactOpts, newOwner common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.contract.Transact(opts, "transferOwnership", newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_CourseLaunchpad *CourseLaunchpadSession) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.TransferOwnership(&_CourseLaunchpad.TransactOpts, newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_CourseLaunchpad *CourseLaunchpadTransactorSession) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _CourseLaunchpad.Contract.TransferOwnership(&_CourseLaunchpad.TransactOpts, newOwner)
}

// CourseLaunchpadFundingActionIterator is returned from FilterFundingAction and is used to iterate over the raw logs and unpacked data for FundingAction events raised by the CourseLaunchpad contract.
type CourseLaunchpadFundingActionIterator struct {
	Event *CourseLaunchpadFundingAction // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CourseLaunchpadFundingActionIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CourseLaunchpadFundingAction)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CourseLaunchpadFundingAction)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CourseLaunchpadFundingActionIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CourseLaunchpadFundingActionIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CourseLaunchpadFundingAction represents a FundingAction event raised by the CourseLaunchpad contract.
type CourseLaunchpadFundingAction struct {
	LaunchpadId common.Hash
	Actor       common.Address
	Amount      *big.Int
	ActionType  string
	Raw         types.Log // Blockchain specific contextual infos
}

// FilterFundingAction is a free log retrieval operation binding the contract event 0xc210b7fc8c2b7ab4d645189659a7325accf5e62b52fa2ad36c32a796dbffe8b5.
//
// Solidity: event FundingAction(string indexed launchpadId, address indexed actor, uint256 amount, string actionType)
func (_CourseLaunchpad *CourseLaunchpadFilterer) FilterFundingAction(opts *bind.FilterOpts, launchpadId []string, actor []common.Address) (*CourseLaunchpadFundingActionIterator, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}
	var actorRule []interface{}
	for _, actorItem := range actor {
		actorRule = append(actorRule, actorItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.FilterLogs(opts, "FundingAction", launchpadIdRule, actorRule)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadFundingActionIterator{contract: _CourseLaunchpad.contract, event: "FundingAction", logs: logs, sub: sub}, nil
}

// WatchFundingAction is a free log subscription operation binding the contract event 0xc210b7fc8c2b7ab4d645189659a7325accf5e62b52fa2ad36c32a796dbffe8b5.
//
// Solidity: event FundingAction(string indexed launchpadId, address indexed actor, uint256 amount, string actionType)
func (_CourseLaunchpad *CourseLaunchpadFilterer) WatchFundingAction(opts *bind.WatchOpts, sink chan<- *CourseLaunchpadFundingAction, launchpadId []string, actor []common.Address) (event.Subscription, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}
	var actorRule []interface{}
	for _, actorItem := range actor {
		actorRule = append(actorRule, actorItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.WatchLogs(opts, "FundingAction", launchpadIdRule, actorRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CourseLaunchpadFundingAction)
				if err := _CourseLaunchpad.contract.UnpackLog(event, "FundingAction", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseFundingAction is a log parse operation binding the contract event 0xc210b7fc8c2b7ab4d645189659a7325accf5e62b52fa2ad36c32a796dbffe8b5.
//
// Solidity: event FundingAction(string indexed launchpadId, address indexed actor, uint256 amount, string actionType)
func (_CourseLaunchpad *CourseLaunchpadFilterer) ParseFundingAction(log types.Log) (*CourseLaunchpadFundingAction, error) {
	event := new(CourseLaunchpadFundingAction)
	if err := _CourseLaunchpad.contract.UnpackLog(event, "FundingAction", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CourseLaunchpadLaunchpadCreatedIterator is returned from FilterLaunchpadCreated and is used to iterate over the raw logs and unpacked data for LaunchpadCreated events raised by the CourseLaunchpad contract.
type CourseLaunchpadLaunchpadCreatedIterator struct {
	Event *CourseLaunchpadLaunchpadCreated // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CourseLaunchpadLaunchpadCreatedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CourseLaunchpadLaunchpadCreated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CourseLaunchpadLaunchpadCreated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CourseLaunchpadLaunchpadCreatedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CourseLaunchpadLaunchpadCreatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CourseLaunchpadLaunchpadCreated represents a LaunchpadCreated event raised by the CourseLaunchpad contract.
type CourseLaunchpadLaunchpadCreated struct {
	LaunchpadId common.Hash
	Owner       common.Address
	Token       common.Address
	Goal        *big.Int
	Raw         types.Log // Blockchain specific contextual infos
}

// FilterLaunchpadCreated is a free log retrieval operation binding the contract event 0xcc41d9439be02510c53fc9b99d8af5160d6938f967f7bb9dcb1cea72fc1bbcdb.
//
// Solidity: event LaunchpadCreated(string indexed launchpadId, address indexed owner, address indexed token, uint256 goal)
func (_CourseLaunchpad *CourseLaunchpadFilterer) FilterLaunchpadCreated(opts *bind.FilterOpts, launchpadId []string, owner []common.Address, token []common.Address) (*CourseLaunchpadLaunchpadCreatedIterator, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}
	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var tokenRule []interface{}
	for _, tokenItem := range token {
		tokenRule = append(tokenRule, tokenItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.FilterLogs(opts, "LaunchpadCreated", launchpadIdRule, ownerRule, tokenRule)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadLaunchpadCreatedIterator{contract: _CourseLaunchpad.contract, event: "LaunchpadCreated", logs: logs, sub: sub}, nil
}

// WatchLaunchpadCreated is a free log subscription operation binding the contract event 0xcc41d9439be02510c53fc9b99d8af5160d6938f967f7bb9dcb1cea72fc1bbcdb.
//
// Solidity: event LaunchpadCreated(string indexed launchpadId, address indexed owner, address indexed token, uint256 goal)
func (_CourseLaunchpad *CourseLaunchpadFilterer) WatchLaunchpadCreated(opts *bind.WatchOpts, sink chan<- *CourseLaunchpadLaunchpadCreated, launchpadId []string, owner []common.Address, token []common.Address) (event.Subscription, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}
	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var tokenRule []interface{}
	for _, tokenItem := range token {
		tokenRule = append(tokenRule, tokenItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.WatchLogs(opts, "LaunchpadCreated", launchpadIdRule, ownerRule, tokenRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CourseLaunchpadLaunchpadCreated)
				if err := _CourseLaunchpad.contract.UnpackLog(event, "LaunchpadCreated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseLaunchpadCreated is a log parse operation binding the contract event 0xcc41d9439be02510c53fc9b99d8af5160d6938f967f7bb9dcb1cea72fc1bbcdb.
//
// Solidity: event LaunchpadCreated(string indexed launchpadId, address indexed owner, address indexed token, uint256 goal)
func (_CourseLaunchpad *CourseLaunchpadFilterer) ParseLaunchpadCreated(log types.Log) (*CourseLaunchpadLaunchpadCreated, error) {
	event := new(CourseLaunchpadLaunchpadCreated)
	if err := _CourseLaunchpad.contract.UnpackLog(event, "LaunchpadCreated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CourseLaunchpadLaunchpadStatusUpdatedIterator is returned from FilterLaunchpadStatusUpdated and is used to iterate over the raw logs and unpacked data for LaunchpadStatusUpdated events raised by the CourseLaunchpad contract.
type CourseLaunchpadLaunchpadStatusUpdatedIterator struct {
	Event *CourseLaunchpadLaunchpadStatusUpdated // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CourseLaunchpadLaunchpadStatusUpdatedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CourseLaunchpadLaunchpadStatusUpdated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CourseLaunchpadLaunchpadStatusUpdated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CourseLaunchpadLaunchpadStatusUpdatedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CourseLaunchpadLaunchpadStatusUpdatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CourseLaunchpadLaunchpadStatusUpdated represents a LaunchpadStatusUpdated event raised by the CourseLaunchpad contract.
type CourseLaunchpadLaunchpadStatusUpdated struct {
	LaunchpadId common.Hash
	OldStatus   uint8
	NewStatus   uint8
	Raw         types.Log // Blockchain specific contextual infos
}

// FilterLaunchpadStatusUpdated is a free log retrieval operation binding the contract event 0x941ba2cdc0fa1805e9eeaf4942c0eb062a6916dc8d45e04e37deabaffdbfb8f0.
//
// Solidity: event LaunchpadStatusUpdated(string indexed launchpadId, uint8 oldStatus, uint8 newStatus)
func (_CourseLaunchpad *CourseLaunchpadFilterer) FilterLaunchpadStatusUpdated(opts *bind.FilterOpts, launchpadId []string) (*CourseLaunchpadLaunchpadStatusUpdatedIterator, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.FilterLogs(opts, "LaunchpadStatusUpdated", launchpadIdRule)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadLaunchpadStatusUpdatedIterator{contract: _CourseLaunchpad.contract, event: "LaunchpadStatusUpdated", logs: logs, sub: sub}, nil
}

// WatchLaunchpadStatusUpdated is a free log subscription operation binding the contract event 0x941ba2cdc0fa1805e9eeaf4942c0eb062a6916dc8d45e04e37deabaffdbfb8f0.
//
// Solidity: event LaunchpadStatusUpdated(string indexed launchpadId, uint8 oldStatus, uint8 newStatus)
func (_CourseLaunchpad *CourseLaunchpadFilterer) WatchLaunchpadStatusUpdated(opts *bind.WatchOpts, sink chan<- *CourseLaunchpadLaunchpadStatusUpdated, launchpadId []string) (event.Subscription, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.WatchLogs(opts, "LaunchpadStatusUpdated", launchpadIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CourseLaunchpadLaunchpadStatusUpdated)
				if err := _CourseLaunchpad.contract.UnpackLog(event, "LaunchpadStatusUpdated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseLaunchpadStatusUpdated is a log parse operation binding the contract event 0x941ba2cdc0fa1805e9eeaf4942c0eb062a6916dc8d45e04e37deabaffdbfb8f0.
//
// Solidity: event LaunchpadStatusUpdated(string indexed launchpadId, uint8 oldStatus, uint8 newStatus)
func (_CourseLaunchpad *CourseLaunchpadFilterer) ParseLaunchpadStatusUpdated(log types.Log) (*CourseLaunchpadLaunchpadStatusUpdated, error) {
	event := new(CourseLaunchpadLaunchpadStatusUpdated)
	if err := _CourseLaunchpad.contract.UnpackLog(event, "LaunchpadStatusUpdated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CourseLaunchpadOwnershipTransferStartedIterator is returned from FilterOwnershipTransferStarted and is used to iterate over the raw logs and unpacked data for OwnershipTransferStarted events raised by the CourseLaunchpad contract.
type CourseLaunchpadOwnershipTransferStartedIterator struct {
	Event *CourseLaunchpadOwnershipTransferStarted // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CourseLaunchpadOwnershipTransferStartedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CourseLaunchpadOwnershipTransferStarted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CourseLaunchpadOwnershipTransferStarted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CourseLaunchpadOwnershipTransferStartedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CourseLaunchpadOwnershipTransferStartedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CourseLaunchpadOwnershipTransferStarted represents a OwnershipTransferStarted event raised by the CourseLaunchpad contract.
type CourseLaunchpadOwnershipTransferStarted struct {
	PreviousOwner common.Address
	NewOwner      common.Address
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferStarted is a free log retrieval operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_CourseLaunchpad *CourseLaunchpadFilterer) FilterOwnershipTransferStarted(opts *bind.FilterOpts, previousOwner []common.Address, newOwner []common.Address) (*CourseLaunchpadOwnershipTransferStartedIterator, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.FilterLogs(opts, "OwnershipTransferStarted", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadOwnershipTransferStartedIterator{contract: _CourseLaunchpad.contract, event: "OwnershipTransferStarted", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferStarted is a free log subscription operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_CourseLaunchpad *CourseLaunchpadFilterer) WatchOwnershipTransferStarted(opts *bind.WatchOpts, sink chan<- *CourseLaunchpadOwnershipTransferStarted, previousOwner []common.Address, newOwner []common.Address) (event.Subscription, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.WatchLogs(opts, "OwnershipTransferStarted", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CourseLaunchpadOwnershipTransferStarted)
				if err := _CourseLaunchpad.contract.UnpackLog(event, "OwnershipTransferStarted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferStarted is a log parse operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_CourseLaunchpad *CourseLaunchpadFilterer) ParseOwnershipTransferStarted(log types.Log) (*CourseLaunchpadOwnershipTransferStarted, error) {
	event := new(CourseLaunchpadOwnershipTransferStarted)
	if err := _CourseLaunchpad.contract.UnpackLog(event, "OwnershipTransferStarted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CourseLaunchpadOwnershipTransferredIterator is returned from FilterOwnershipTransferred and is used to iterate over the raw logs and unpacked data for OwnershipTransferred events raised by the CourseLaunchpad contract.
type CourseLaunchpadOwnershipTransferredIterator struct {
	Event *CourseLaunchpadOwnershipTransferred // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CourseLaunchpadOwnershipTransferredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CourseLaunchpadOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CourseLaunchpadOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CourseLaunchpadOwnershipTransferredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CourseLaunchpadOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CourseLaunchpadOwnershipTransferred represents a OwnershipTransferred event raised by the CourseLaunchpad contract.
type CourseLaunchpadOwnershipTransferred struct {
	PreviousOwner common.Address
	NewOwner      common.Address
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferred is a free log retrieval operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_CourseLaunchpad *CourseLaunchpadFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, previousOwner []common.Address, newOwner []common.Address) (*CourseLaunchpadOwnershipTransferredIterator, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.FilterLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadOwnershipTransferredIterator{contract: _CourseLaunchpad.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferred is a free log subscription operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_CourseLaunchpad *CourseLaunchpadFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CourseLaunchpadOwnershipTransferred, previousOwner []common.Address, newOwner []common.Address) (event.Subscription, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.WatchLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CourseLaunchpadOwnershipTransferred)
				if err := _CourseLaunchpad.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferred is a log parse operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_CourseLaunchpad *CourseLaunchpadFilterer) ParseOwnershipTransferred(log types.Log) (*CourseLaunchpadOwnershipTransferred, error) {
	event := new(CourseLaunchpadOwnershipTransferred)
	if err := _CourseLaunchpad.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// CourseLaunchpadVotingResultIterator is returned from FilterVotingResult and is used to iterate over the raw logs and unpacked data for VotingResult events raised by the CourseLaunchpad contract.
type CourseLaunchpadVotingResultIterator struct {
	Event *CourseLaunchpadVotingResult // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *CourseLaunchpadVotingResultIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CourseLaunchpadVotingResult)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(CourseLaunchpadVotingResult)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *CourseLaunchpadVotingResultIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *CourseLaunchpadVotingResultIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// CourseLaunchpadVotingResult represents a VotingResult event raised by the CourseLaunchpad contract.
type CourseLaunchpadVotingResult struct {
	LaunchpadId    common.Hash
	AvailableClaim *big.Int
	Successful     bool
	Raw            types.Log // Blockchain specific contextual infos
}

// FilterVotingResult is a free log retrieval operation binding the contract event 0x59b79e718c8a6a244dc2aad91256364f73ccb464c75ff0c7a408c057d0f15c11.
//
// Solidity: event VotingResult(string indexed launchpadId, uint256 availableClaim, bool successful)
func (_CourseLaunchpad *CourseLaunchpadFilterer) FilterVotingResult(opts *bind.FilterOpts, launchpadId []string) (*CourseLaunchpadVotingResultIterator, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.FilterLogs(opts, "VotingResult", launchpadIdRule)
	if err != nil {
		return nil, err
	}
	return &CourseLaunchpadVotingResultIterator{contract: _CourseLaunchpad.contract, event: "VotingResult", logs: logs, sub: sub}, nil
}

// WatchVotingResult is a free log subscription operation binding the contract event 0x59b79e718c8a6a244dc2aad91256364f73ccb464c75ff0c7a408c057d0f15c11.
//
// Solidity: event VotingResult(string indexed launchpadId, uint256 availableClaim, bool successful)
func (_CourseLaunchpad *CourseLaunchpadFilterer) WatchVotingResult(opts *bind.WatchOpts, sink chan<- *CourseLaunchpadVotingResult, launchpadId []string) (event.Subscription, error) {

	var launchpadIdRule []interface{}
	for _, launchpadIdItem := range launchpadId {
		launchpadIdRule = append(launchpadIdRule, launchpadIdItem)
	}

	logs, sub, err := _CourseLaunchpad.contract.WatchLogs(opts, "VotingResult", launchpadIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(CourseLaunchpadVotingResult)
				if err := _CourseLaunchpad.contract.UnpackLog(event, "VotingResult", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseVotingResult is a log parse operation binding the contract event 0x59b79e718c8a6a244dc2aad91256364f73ccb464c75ff0c7a408c057d0f15c11.
//
// Solidity: event VotingResult(string indexed launchpadId, uint256 availableClaim, bool successful)
func (_CourseLaunchpad *CourseLaunchpadFilterer) ParseVotingResult(log types.Log) (*CourseLaunchpadVotingResult, error) {
	event := new(CourseLaunchpadVotingResult)
	if err := _CourseLaunchpad.contract.UnpackLog(event, "VotingResult", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}
