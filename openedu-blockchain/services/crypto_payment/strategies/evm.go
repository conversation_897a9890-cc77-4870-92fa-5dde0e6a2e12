package payment

import (
	"context"
	"fmt"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common/math"
	"math/big"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/openedu_eth_sdk"
	"openedu-blockchain/pkg/openedu_eth_sdk/bindings"
	"openedu-blockchain/pkg/openedu_eth_sdk/eip712"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/services/crypto_payment"
	"openedu-blockchain/services/crypto_transfer"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"

	"github.com/samber/lo"
)

type EvmPaymentService struct {
}

// GasEstimation represents the estimated gas for a transaction
type GasEstimation struct {
	EstimatedGas uint64          `json:"estimated_gas"`
	GasCostWei   string          `json:"gas_cost_wei"`
	GasCostEth   decimal.Decimal `json:"gas_cost_eth"`
}

// Payment implements the payment strategy for EVM compatible blockchains
func (ps *EvmPaymentService) Payment(account crypto_payment.Account, req *crypto_payment.PaymentRequest) (*crypto_payment.Response, error) {

	ethClient, err := ps.getClient(req.IsMainnet)
	if err != nil {
		return nil, err
	}
	defer ethClient.Close()

	// Get user private key (to sign the permit)
	signerPrivateKeyStr, err := ps.getPrivateKey(account)
	if err != nil {
		return nil, err
	}

	// Convert hex private key to ECDSA private key for signer (user)
	signerPrivateKey, err := crypto.HexToECDSA(signerPrivateKeyStr)
	if err != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("parse signer private key failed: %w", err))
	}

	// Get payment contract private key
	ownerPrivateKeyStr := ps.getPaymentOwnerPrivateKey(req.IsMainnet)
	ownerPrivateKey, err := crypto.HexToECDSA(ownerPrivateKeyStr)
	if err != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("parse owner private key failed: %w", err))
	}

	// Get token address based on token type and network
	tokenAddress, tokenInstance, err := ps.getTokenInstance(ethClient, req.Token, req.IsMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to get token address and instance: %w", err)
	}
	tokenAddressHex := common.HexToAddress(tokenAddress)

	// Connect to course payment contract
	coursePaymentAddressStr := ps.getPaymentContractAddress(req.IsMainnet)
	coursePaymentAddress, err := openedu_eth_sdk.ParseAddressFromHex(coursePaymentAddressStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment contract address: %w", err)
	}

	coursePaymentInstance, err := bindings.NewCoursePayment(coursePaymentAddress, ethClient)
	if err != nil {
		return nil, fmt.Errorf("failed to instantiate course payment contract: %w", err)
	}

	// Get token decimals
	callOpts := &bind.CallOpts{
		From: common.HexToAddress(account.GetAddress()),
	}
	decimals, err := tokenInstance.Decimals(callOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to get token decimals: %w", err)
	}

	// Parse amount and fee
	parsedAmount := ps.convertDecimalToBigInt(req.Amount, int(decimals))
	parsedFee := ps.convertDecimalToBigInt(req.Fee, int(decimals))

	// Check if the account has sufficient balance
	userAddress := common.HexToAddress(account.GetAddress())
	balance, err := tokenInstance.BalanceOf(callOpts, userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Convert the amount to decimal for comparison
	totalAmount := new(big.Int).Add(parsedAmount, parsedFee)
	if balance.Cmp(totalAmount) < 0 {
		err = fmt.Errorf("required %s, available %s",
			decimal.NewFromBigInt(totalAmount, -int32(decimals)).String(),
			decimal.NewFromBigInt(balance, -int32(decimals)).String())

		return nil, e.Wrap(crypto_payment.ErrInsufficientBalance, err)
	}

	// Calculate deadline (24 hours from now)
	deadlineHours := int64(24)
	deadline := big.NewInt(time.Now().UnixMilli() + (deadlineHours * 60 * 60 * 1000))

	// Get nonce for the signer
	nonce, err := tokenInstance.Nonces(callOpts, userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}

	// Create permit domain
	tokenName, err := tokenInstance.Name(callOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to get token name: %w", err)
	}

	// Get chain ID
	chainID := ps.getChainID(req.IsMainnet)

	// Create TypedData according to EIP-712 for permit
	var EIP712DomainType = []eip712.Type{
		{Name: "name", Type: "string"},
		{Name: "version", Type: "string"},
		{Name: "chainId", Type: "uint256"},
		{Name: "verifyingContract", Type: "address"},
	}

	typedData := &eip712.TypedData{
		Types: eip712.Types{
			"EIP712Domain": EIP712DomainType,
			"Permit": []eip712.Type{
				{Name: "owner", Type: "address"},
				{Name: "spender", Type: "address"},
				{Name: "value", Type: "uint256"},
				{Name: "nonce", Type: "uint256"},
				{Name: "deadline", Type: "uint256"},
			},
		},
		PrimaryType: "Permit",
		Domain: eip712.TypedDataDomain{
			Name:              tokenName,
			Version:           "1",
			ChainId:           math.NewHexOrDecimal256(chainID.Int64()),
			VerifyingContract: tokenAddressHex.Hex(),
		},
		Message: eip712.TypedDataMessage{
			"owner":    userAddress.Hex(),
			"spender":  coursePaymentAddress.Hex(),
			"value":    new(big.Int).Add(parsedAmount, parsedFee).String(),
			"nonce":    nonce.String(),
			"deadline": deadline.String(),
		},
	}

	// Encode data for signing
	byteData, err := eip712.EncodeForSigning(typedData)
	if err != nil {
		return nil, fmt.Errorf("failed to encode data for signing: %w", err)
	}

	// Sign data hash with SIGNER's private key (user)
	dataHash := crypto.Keccak256(byteData)
	signature, err := crypto.Sign(dataHash, signerPrivateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign data: %w", err)
	}

	// Convert signature to v, r, s format
	v := signature[64] + 27 // Ethereum expects v to be 27 or 28
	var r, s [32]byte
	copy(r[:], signature[:32])
	copy(s[:], signature[32:64])

	txOpts, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, &chainID)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction options: %w", err)
	}

	paymentOwnerAddress := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)
	txOpts.From = paymentOwnerAddress
	txOpts.GasLimit = 500000

	// Execute transaction
	tx, err := coursePaymentInstance.PayWithPermit(
		txOpts,
		tokenAddressHex,
		userAddress,    // Signer address
		req.CourseCUID, // Using CourseCUID as courseId
		parsedAmount,
		parsedFee,
		deadline,
		v,
		r,
		s,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute payment: %w", err)
	}

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Prepare response
	status := models.TxStatusSuccess
	if receipt.Status == 0 {
		status = models.TxStatusFailed
	}

	gasUsed := receipt.GasUsed
	gasPrice := tx.GasPrice()
	gasCostWei := new(big.Int).Mul(gasPrice, big.NewInt(int64(gasUsed)))
	gasCostEth := decimal.NewFromBigInt(gasCostWei, -18) // Convert wei to ETH

	// Prepare input data for response
	inputData := models.JSONB{
		"tokenAddress":  tokenAddressHex.Hex(),
		"signerAddress": account.GetAddress(),
		"courseId":      req.CourseCUID,
		"amount":        req.Amount.String(),
		"fee":           req.Fee.String(),
		"deadline":      deadline.String(),
	}

	return &crypto_payment.Response{
		ContractID:   coursePaymentAddress.Hex(),
		MethodName:   "payWithPermit",
		InputData:    inputData,
		Deposit:      req.Amount,
		Token:        req.Token,
		BlockHash:    receipt.BlockHash.Hex(),
		TxHash:       tx.Hash().Hex(),
		GasLimit:     tx.Gas(),
		GasBurnt:     receipt.GasUsed,
		GasCost:      gasCostEth,
		TotalGasCost: gasCostEth,
		Nonce:        tx.Nonce(),
		TxDetails:    receipt,
		Status:       status,
		ErrorMsg:     "",
	}, nil
}

// EstimateGasForPayment estimates the gas needed for payment
func (ps *EvmPaymentService) EstimateGasForPayment(account crypto_payment.Account, req *crypto_payment.PaymentRequest) (*GasEstimation, error) {
	ctx := context.Background()

	ethClient, err := ps.getClient(req.IsMainnet)
	if err != nil {
		return nil, err
	}
	defer ethClient.Close()

	// Get private key
	privateKeyStr, err := ps.getPrivateKey(account)
	if err != nil {
		return nil, err
	}

	// Convert hex private key to ECDSA private key
	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("parse private key failed: %w", err))
	}

	// Get token address based on token type and network
	tokenAddress, tokenInstance, err := ps.getTokenInstance(ethClient, req.Token, req.IsMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to get token address: %w", err)
	}
	tokenAddressHex := common.HexToAddress(tokenAddress)

	// Get token decimals
	callOpts := &bind.CallOpts{
		From: common.HexToAddress(account.GetAddress()),
	}
	decimals, err := tokenInstance.Decimals(callOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to get token decimals: %w", err)
	}

	// Parse amount and fee
	parsedAmount := ps.convertDecimalToBigInt(req.Amount, int(decimals))

	// Calculate fee
	feePercentage := decimal.NewFromFloat(0.01) // 1% fee, adjust as needed
	fee := req.Amount.Mul(feePercentage)
	parsedFee := ps.convertDecimalToBigInt(fee, int(decimals))

	// Calculate deadline (24 hours from now)
	deadlineHours := int64(24)
	deadline := big.NewInt(time.Now().Unix() + (deadlineHours * 60 * 60))

	// Get nonce for the owner
	nonce, err := tokenInstance.Nonces(callOpts, common.HexToAddress(account.GetAddress()))
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}

	// Get token name for domain
	tokenName, err := tokenInstance.Name(callOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to get token name: %w", err)
	}

	coursePaymentAddress, err := openedu_eth_sdk.ParseAddressFromHex(ps.getPaymentContractAddress(req.IsMainnet))
	if err != nil {
		return nil, fmt.Errorf("failed to get payment contract address: %w", err)
	}

	// Get chain ID
	chainID := ps.getChainID(req.IsMainnet)

	// Create TypedData according to EIP-712 for permit
	var EIP712DomainType = []eip712.Type{
		{Name: "name", Type: "string"},
		{Name: "version", Type: "string"},
		{Name: "chainId", Type: "uint256"},
		{Name: "verifyingContract", Type: "address"},
	}

	typedData := &eip712.TypedData{
		Types: eip712.Types{
			"EIP712Domain": EIP712DomainType,
			"Permit": []eip712.Type{
				{Name: "owner", Type: "address"},
				{Name: "spender", Type: "address"},
				{Name: "value", Type: "uint256"},
				{Name: "nonce", Type: "uint256"},
				{Name: "deadline", Type: "uint256"},
			},
		},
		PrimaryType: "Permit",
		Domain: eip712.TypedDataDomain{
			Name:              tokenName,
			Version:           "1",
			ChainId:           math.NewHexOrDecimal256(chainID.Int64()),
			VerifyingContract: tokenAddressHex.Hex(),
		},
		Message: eip712.TypedDataMessage{
			"owner":    common.HexToAddress(account.GetAddress()).Hex(),
			"spender":  coursePaymentAddress.Hex(),
			"value":    parsedAmount.String(), // Sử dụng String() thay vì math.NewHexOrDecimal256
			"nonce":    nonce.String(),
			"deadline": deadline.String(),
		},
	}

	// Encode data for signing
	data, err := eip712.EncodeForSigning(typedData)
	if err != nil {
		return nil, fmt.Errorf("failed to encode data for signing: %w", err)
	}

	// Sign data
	signature, err := crypto.Sign(data, privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign data: %w", err)
	}

	// Convert signature to v, r, s format
	v := signature[64] + 27 // Ethereum expects v to be 27 or 28
	var r, s [32]byte
	copy(r[:], signature[:32])
	copy(s[:], signature[32:64])

	// Get CoursePayment ABI and pack the parameters
	parsed, err := bindings.CoursePaymentMetaData.GetAbi()
	if err != nil {
		return nil, fmt.Errorf("failed to get CoursePayment ABI: %w", err)
	}

	// Pack function input parameters (updated to match new contract ABI)
	data, err = parsed.Pack("payWithPermit",
		tokenAddressHex,
		common.HexToAddress(account.GetAddress()),
		req.CourseCUID,
		parsedAmount,
		parsedFee,
		deadline,
		v,
		r,
		s,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to pack data for gas estimation: %w", err)
	}

	// Estimate gas using ethereum.CallMsg
	gasEstimate, err := ethClient.EstimateGas(ctx, ethereum.CallMsg{
		From: common.HexToAddress(account.GetAddress()),
		To:   &coursePaymentAddress,
		Data: data,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to estimate gas: %w", err)
	}

	// Get current gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Calculate gas cost
	gasCostWei := new(big.Int).Mul(gasPrice, big.NewInt(int64(gasEstimate)))
	gasCostEth := decimal.NewFromBigInt(gasCostWei, -18)

	return &GasEstimation{
		EstimatedGas: gasEstimate,
		GasCostWei:   gasCostWei.String(),
		GasCostEth:   gasCostEth,
	}, nil
}

// ClaimEarning allows users to claim their earnings from the Vault contract
// The contract owner executes the transaction on behalf of the user
func (ps *EvmPaymentService) ClaimEarning(account crypto_payment.Account, req *crypto_payment.ClaimEarningRequest) (*crypto_payment.Response, error) {
	// Get connected to the blockchain network
	ethClient, err := ps.getClient(req.IsMainnet)
	if err != nil {
		return nil, err
	}
	defer ethClient.Close()

	// Get payment owner's private key (the contract owner who will execute the transaction)
	ownerPrivateKeyStr := ps.getPaymentOwnerPrivateKey(req.IsMainnet)
	ownerPrivateKey, err := crypto.HexToECDSA(ownerPrivateKeyStr)
	if err != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("parse owner private key failed: %w", err))
	}

	// Get token address based on token type and network
	tokenAddress, tokenInstance, err := ps.getTokenInstance(ethClient, req.Token, req.IsMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to get token address and instance: %w", err)
	}
	tokenAddressHex := common.HexToAddress(tokenAddress)

	// Connect to Vault contract
	vaultAddressStr := ps.getVaultContractAddress(req.IsMainnet)
	vaultAddress := common.HexToAddress(vaultAddressStr)
	vaultInstance, err := bindings.NewVault(vaultAddress, ethClient)
	if err != nil {
		return nil, fmt.Errorf("failed to instantiate vault contract: %w", err)
	}

	// Get token decimals
	callOpts := &bind.CallOpts{
		From: common.HexToAddress(account.GetAddress()),
	}
	decimals, err := tokenInstance.Decimals(callOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to get token decimals: %w", err)
	}

	// Convert amount and fee to big.Int with appropriate decimals
	parsedAmount := ps.convertDecimalToBigInt(req.Amount, int(decimals))
	parsedFee := ps.convertDecimalToBigInt(req.Fee, int(decimals))

	// Get chain ID for the network
	chainID := ps.getChainID(req.IsMainnet)

	// Create transaction options for the owner
	txOpts, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, &chainID)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction options: %w", err)
	}

	ownerAddress := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)
	txOpts.From = ownerAddress
	txOpts.GasLimit = 300000 // Estimated value, can be adjusted

	// Get user's address (the recipient of the withdrawn funds)
	userAddress := common.HexToAddress(account.GetAddress())

	// Execute withdraw from Vault with owner as the caller and user as the recipient
	tx, err := vaultInstance.Withdraw(
		txOpts,
		tokenAddressHex,
		parsedAmount,
		parsedFee,
		userAddress, // User is the recipient of the withdrawn funds
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute claim earning: %w", err)
	}

	// Wait for transaction to be mined
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Prepare response with transaction status
	status := models.TxStatusSuccess
	if receipt.Status == 0 {
		status = models.TxStatusFailed
	}

	// Calculate gas costs
	gasUsed := receipt.GasUsed
	gasPrice := tx.GasPrice()
	gasCostWei := new(big.Int).Mul(gasPrice, big.NewInt(int64(gasUsed)))
	gasCostEth := decimal.NewFromBigInt(gasCostWei, -18) // Convert wei to ETH

	// Prepare input data for response
	inputData := models.JSONB{
		"tokenAddress": tokenAddressHex.Hex(),
		"userAddress":  account.GetAddress(),
		"amount":       req.Amount.String(),
		"fee":          req.Fee.String(),
	}

	return &crypto_payment.Response{
		ContractID:   vaultAddress.Hex(),
		MethodName:   "withdraw",
		InputData:    inputData,
		Deposit:      req.Amount,
		Token:        req.Token,
		BlockHash:    receipt.BlockHash.Hex(),
		TxHash:       tx.Hash().Hex(),
		GasLimit:     tx.Gas(),
		GasBurnt:     receipt.GasUsed,
		GasCost:      gasCostEth,
		TotalGasCost: gasCostEth,
		Nonce:        tx.Nonce(),
		TxDetails:    receipt,
		Status:       status,
		ErrorMsg:     "",
	}, nil
}

func (ps *EvmPaymentService) getTokenInstance(ethClient *ethclient.Client, token models.BlockchainToken, isMainnet bool) (string, openedu_eth_sdk.ERC20TokenContract, error) {
	var tokenAddressStr string
	var tokenInstance openedu_eth_sdk.ERC20TokenContract
	var err error

	switch token {
	case models.TokenUSDC:
		tokenAddressStr = lo.If(isMainnet, setting.EvmSetting.MainnetUSDCContractAddress).Else(setting.EvmSetting.TestnetUSDCContractAddress)
		tokenAddressHex := common.HexToAddress(tokenAddressStr)
		tokenInstance, err = bindings.NewUSDC(tokenAddressHex, ethClient)
		if err != nil {
			return "", nil, fmt.Errorf("instantiate token contract failed: %w", err)
		}

	default:
		return "", nil, crypto_payment.ErrUnsupportedToken
	}

	return tokenAddressStr, tokenInstance, nil
}

// convertDecimalToBigInt converts a decimal.Decimal to *big.Int accounting for token decimals
func (ps *EvmPaymentService) convertDecimalToBigInt(d decimal.Decimal, decimals int) *big.Int {
	// Scale the decimal by the token's decimals
	scaledDecimal := d.Mul(decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(decimals))))
	// Convert to big.Int
	bigint, _ := new(big.Int).SetString(scaledDecimal.String(), 10)
	return bigint
}

// getClient creates a new Ethereum client
func (ps *EvmPaymentService) getClient(isMainnet bool) (*ethclient.Client, error) {
	nodeURLs := ps.getNodeURLs(isMainnet)
	client, err := ethclient.Dial(nodeURLs[0])
	if err == nil {
		return client, nil
	}
	return nil, e.Wrap(crypto_payment.ErrConnectRPCFailed, fmt.Errorf("connect to node %s failed: %w", nodeURLs[0], err))
}

// getNodeURLs returns the appropriate node URLs based on network
func (ps *EvmPaymentService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetURLs).
		Else(setting.EvmSetting.TestnetURLs)
}

func (ps *EvmPaymentService) getChainID(isMainnet bool) big.Int {
	str := lo.If(isMainnet, setting.EvmSetting.MainnetChainID).
		Else(setting.EvmSetting.TestnetChainID)
	bigInt := new(big.Int)
	bigInt, _ = bigInt.SetString(str, 10)
	return *bigInt
}

func (ps *EvmPaymentService) getPaymentContractAddress(isMainnet bool) string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetPaymentContractAddress).
		Else(setting.EvmSetting.TestnetPaymentContractAddress)
}

func (ps *EvmPaymentService) getPaymentOwnerPrivateKey(isMainnet bool) string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetPaymentOwnerPrivateKey).
		Else(setting.EvmSetting.TestnetPaymentOwnerPrivateKey)
}

// getPrivateKey gets the private key from an account
func (ps *EvmPaymentService) getPrivateKey(account crypto_payment.Account) (string, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return "", e.Wrap(crypto_payment.ErrGetPrivateKeyFailed, err)
	}

	// Remove '0x' prefix if exists
	if len(privateKeyStr) > 2 && privateKeyStr[:2] == "0x" {
		privateKeyStr = privateKeyStr[2:]
	}

	return privateKeyStr, nil
}

// getVaultContractAddress returns the appropriate Vault contract address based on network
func (ps *EvmPaymentService) getVaultContractAddress(isMainnet bool) string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetVaultContractAddress).
		Else(setting.EvmSetting.TestnetVaultContractAddress)
}