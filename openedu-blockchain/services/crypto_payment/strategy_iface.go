package crypto_payment

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
)

type PaymentStrategy interface {
	Payment(account Account, req *PaymentRequest) (*Response, error)
	ClaimEarning(account Account, req *ClaimEarningRequest) (*Response, error)
}

type Account interface {
	GetAddress() string
	GetNetwork() models.BlockchainNetwork
	GetPublicKey() string
	GetPrivateKey() (string, error)
}

type PaymentRequest struct {
	CourseCUID          string                    `json:"course_cuid"`
	Token               models.BlockchainToken    `json:"token"`
	Amount              decimal.Decimal           `json:"amount"`
	Fee                 decimal.Decimal           `json:"fee"`
	ProfitDistributions []*dto.ProfitDistribution `json:"profit_distributions"`
	IsMainnet           bool                      `json:"is_mainnet"`
}

type ClaimEarningRequest struct {
	Amount    decimal.Decimal        `json:"amount"`
	Fee       decimal.Decimal        `json:"fee"`
	Token     models.BlockchainToken `json:"token"`
	IsMainnet bool                   `json:"is_mainnet"`
}

type Response struct {
	ContractID   string                   `json:"contract_id"`
	MethodName   string                   `json:"method_name"`
	InputData    models.JSONB             `json:"input_data"`
	Deposit      decimal.Decimal          `json:"deposit"`
	Token        models.BlockchainToken   `json:"token"`
	BlockHash    string                   `json:"block_hash"`
	TxHash       string                   `json:"tx_hash"`
	GasLimit     uint64                   `json:"gas_limit"`
	GasBurnt     uint64                   `json:"gas_burnt"`
	StorageCost  decimal.Decimal          `json:"storage_cost"`
	GasCost      decimal.Decimal          `json:"gas_cost"`
	TotalGasCost decimal.Decimal          `json:"total_gas_cost"`
	Nonce        uint64                   `json:"nonce"`
	TxDetails    interface{}              `json:"tx_details"`
	Status       models.TransactionStatus `json:"status"`
	ErrorMsg     string                   `json:"error_msg"`
}
