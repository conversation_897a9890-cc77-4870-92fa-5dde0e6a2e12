package services

import (
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/kms"
	"openedu-blockchain/pkg/util"
)

type UserSettingService struct{}

func (s *UserSettingService) Create(setting *models.UserSetting) (*models.UserSetting, *e.AppError) {
	if !setting.HasSecret() {
		secret, err := util.GenerateSecret(util.DefaultSecretLength)
		if err != nil {
			return nil, e.NewError500(e.UserSettingCreateFailed, "Generated secret error: "+err.Error())
		}

		encryptedSecret, err := kms.Encrypt(secret)
		if err != nil {
			return nil, e.NewError500(e.UserSettingCreateFailed, "Secret decryption error: "+err.Error())
		}

		setting.EncryptedSecret = encryptedSecret
	}

	if !setting.HasSeedPhrase() {
		seedPhrase, err := util.GenerateMnemonic()
		if err != nil {
			return nil, e.New<PERSON>rror500(e.UserSettingCreateFailed, "Generated seed phrase error: "+err.Error())
		}

		encryptedSeedPhrase, err := kms.Encrypt(seedPhrase)
		if err != nil {
			return nil, e.NewError500(e.UserSettingCreateFailed, "Seed phrase decryption error: "+err.Error())
		}

		setting.EncryptedSeedPhrase = encryptedSeedPhrase
	}

	if err := models.Repository.UserSetting.Create(setting, nil); err != nil {
		return nil, e.NewError500(e.UserSettingCreateFailed, "Create user setting error: "+err.Error())
	}

	return setting, nil
}
