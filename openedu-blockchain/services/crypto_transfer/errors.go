package crypto_transfer

import (
	"errors"
)

var (
	ErrConnectRPCFailed                    = errors.New("connect rpc failed")
	ErrGetPrivateKeyFailed                 = errors.New("get private key failed")
	ErrParsePrivateKeyFailed               = errors.New("parse private key failed")
	ErrInvalidAddress                      = errors.New("invalid address")
	ErrAccountNotExists                    = errors.New("account does not exist")
	ErrGetAccountStateFailed               = errors.New("failed to get account state")
	ErrInsufficientGasFee                  = errors.New("insufficient gas fee")
	ErrInsufficientBalance                 = errors.New("insufficient balance")
	ErrInvalidAmount                       = errors.New("invalid amount")
	ErrInvalidPrivateKey                   = errors.New("invalid private key")
	ErrUnsupportedToken                    = errors.New("unsupported token")
	ErrGetTokenDecimalsFailed              = errors.New("failed to get token decimals")
	ErrGetTokenContractIDFailed            = errors.New("failed to get token contract id")
	ErrGetFtBalanceFailed                  = errors.New("get ft balance failed")
	ErrGetStorageBalanceFailed             = errors.New("get storage balance failed")
	ErrGetStorageBoundsFailed              = errors.New("get storage bounds failed")
	ErrInsufficientBalanceToStorageDeposit = errors.New("insufficient balance to storage deposit")
	ErrStorageDepositFailed                = errors.New("storage deposit failed")
	ErrTransferFailed                      = errors.New("transfer failed")
	ErrNotImplemented                      = errors.New("function not implemented yet")
)
