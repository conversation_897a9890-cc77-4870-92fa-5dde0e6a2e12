package strategies

import (
	"context"
	"crypto/ecdsa"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"math/big"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/openedu_eth_sdk"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"

	"openedu-blockchain/models"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/services/crypto_transfer"
)

// TransactionParams holds parameters for creating a transaction
type TransactionParams struct {
	From       common.Address
	To         common.Address
	Amount     *big.Int
	Nonce      uint64
	GasPrice   *big.Int
	GasLimit   uint64
	Data       []byte
	PrivateKey *ecdsa.PrivateKey
	ChainID    *big.Int
}

// Eth2EthTransferService implements Ethereum transfer functionality
type Eth2EthTransferService struct{}

// Transfer handles both native and token transfers
func (s *Eth2EthTransferService) Transfer(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {
	if req.Token == models.TokenETH {
		return s.transferNativeToken(account, req)
	}
	return s.transferFungibleToken(account, req)
}

// BatchTransfer handles batch transfers
func (s *Eth2EthTransferService) BatchTransfer(
	account crypto_transfer.Account,
	req *crypto_transfer.BatchTransferRequest,
) ([]*crypto_transfer.TransferResult, error) {
	if req.Token == models.TokenETH {
		return s.batchTransferNativeToken(account, req)
	}
	return s.batchTransferFungibleToken(account, req)
}

// GetAccountInfo retrieves account information from the blockchain
func (s *Eth2EthTransferService) GetAccountInfo(
	req *crypto_transfer.GetAccountInfoRequest,
) (*crypto_transfer.GetAccountInfoResponse, error) {

	client, err := s.getClient(req.IsMainnet)
	if err != nil {
		return nil, err
	}
	defer client.Close()

	address, err := openedu_eth_sdk.ParseAddressFromHex(req.Address)
	if err != nil {
		return nil, e.Wrap(crypto_transfer.ErrInvalidAddress, err)
	}

	var (
		balance                                *big.Int
		nonce                                  uint64
		gasPrice                               *big.Int
		code                                   []byte
		balErr, nonceErr, gasPriceErr, codeErr error
		wg                                     = make(chan struct{}, 4) // Use buffered channel as semaphore
		ctx                                    = context.Background()
	)

	go func() {
		balance, balErr = client.BalanceAt(ctx, address, nil)
		wg <- struct{}{}
	}()

	go func() {
		nonce, nonceErr = client.NonceAt(ctx, address, nil)
		wg <- struct{}{}
	}()

	go func() {
		gasPrice, gasPriceErr = client.SuggestGasPrice(ctx)
		wg <- struct{}{}
	}()

	go func() {
		code, codeErr = client.CodeAt(ctx, address, nil)
		wg <- struct{}{}
	}()

	for i := 0; i < 4; i++ {
		<-wg
	}

	if balErr != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get balance failed: %w", balErr))
	}
	if nonceErr != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get nonce failed: %w", nonceErr))
	}
	if gasPriceErr != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get gas price failed: %w", gasPriceErr))
	}
	if codeErr != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get code failed: %w", codeErr))
	}

	accountInfo := &openedu_eth_sdk.AccountInfo{
		Nonce:    nonce,
		Balance:  balance,
		GasPrice: gasPrice,
		Code:     code,
	}

	return &crypto_transfer.GetAccountInfoResponse{
		Network:     req.Network,
		Address:     req.Address,
		AccountInfo: accountInfo,
	}, nil
}

// transferNativeToken handles ETH transfers
func (s *Eth2EthTransferService) transferNativeToken(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {

	result := &crypto_transfer.TransferResult{
		MethodName: openedu_eth_sdk.SendTxMethodName,
		InputData:  models.JSONB{},
	}

	// Validate amount
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		err := fmt.Errorf("amount must be greater than 0: amount(%s)", req.Amount.String())
		return result, e.Wrap(crypto_transfer.ErrInvalidAmount, err)
	}

	// Get sender account info
	senderInfo, client, privateKey, err := s.prepareTransferAccount(account, req.IsMainnet)
	if err != nil {
		return result, err
	}
	defer client.Close()

	// Convert ETH to Wei
	transferAmount := parseEthAmount2Wei(req.Amount)

	// Check balance
	if senderInfo.Balance.Cmp(transferAmount) < 0 {
		err = fmt.Errorf(
			"balance does not enough: balance(%s), amount(%s)",
			senderInfo.Balance.String(),
			transferAmount.String(),
		)
		return result, e.Wrap(crypto_transfer.ErrInsufficientBalance, err)
	}

	// Create transaction parameters
	toAddress := common.HexToAddress(req.ToAddress)

	// Estimate gas limit
	gasLimit, err := s.estimateGasLimitNative(client, senderInfo.From, toAddress, transferAmount)
	if err != nil {
		// Use default if estimation fails
		gasLimit = openedu_eth_sdk.DefaultTokenTransferGasLimit
	}

	txParams := &TransactionParams{
		From:       senderInfo.From,
		To:         toAddress,
		Amount:     transferAmount,
		Nonce:      senderInfo.Nonce,
		GasPrice:   senderInfo.GasPrice,
		GasLimit:   gasLimit,
		Data:       nil,
		PrivateKey: privateKey,
		ChainID:    senderInfo.ChainID,
	}
	return s.executeTransaction(client, txParams, result)
}

// transferFungibleToken handles ERC20 token transfers
func (s *Eth2EthTransferService) transferFungibleToken(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {

	result := &crypto_transfer.TransferResult{
		MethodName: openedu_eth_sdk.SendTxMethodName,
		InputData:  models.JSONB{},
	}

	// Validate amount
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return result, e.Wrap(crypto_transfer.ErrInvalidAmount, fmt.Errorf("amount must be greater than 0: amount(%s)", req.Amount.String()))
	}

	// Get sender account info
	senderInfo, client, privateKey, err := s.prepareTransferAccount(account, req.IsMainnet)
	if err != nil {
		return result, err
	}
	defer client.Close()

	// Get token contract address
	tokenAddress, err := s.getTokenContractAddress(req.Token, req.IsMainnet)
	if err != nil {
		return result, err
	}
	tokenContractAddress := common.HexToAddress(tokenAddress)

	// Get token decimals
	tokenDecimals, err := s.getTokenDecimals(client, tokenContractAddress)
	if err != nil {
		return result, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get token decimals failed: %w", err))
	}

	// Convert amount to token units
	transferAmount := s.parseTokenAmount(req.Amount, tokenDecimals)

	// Check token balance
	tokenBalance, err := s.getTokenBalance(client, tokenContractAddress, senderInfo.From)
	if err != nil {
		return result, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get token balance failed: %w", err))
	}

	if tokenBalance.Cmp(transferAmount) < 0 {
		err = fmt.Errorf("token balance not enough: balance(%s), amount(%s)", transferAmount.String(), tokenBalance.String())
		return result, e.Wrap(crypto_transfer.ErrInsufficientBalance, err)
	}

	// Create ERC20 transfer data
	toAddress := common.HexToAddress(req.ToAddress)
	txData := s.createERC20TransferData(toAddress, transferAmount)

	// Estimate gas limit
	gasLimit, err := s.estimateGasLimitToken(client, senderInfo.From, tokenContractAddress, txData)
	if err != nil {
		// Use default if estimation fails
		gasLimit = openedu_eth_sdk.DefaultTokenTransferGasLimit
	}

	// Check if there's enough ETH for gas
	gasCost := new(big.Int).Mul(senderInfo.GasPrice, big.NewInt(int64(gasLimit)))
	if senderInfo.Balance.Cmp(gasCost) < 0 {
		err = fmt.Errorf("balance not enough ETH for gas: balance(%s), required(%s)", senderInfo.Balance.String(), gasCost.String())
		return result, e.Wrap(crypto_transfer.ErrInsufficientBalance, err)
	}

	// Create transaction parameters
	txParams := &TransactionParams{
		From:       senderInfo.From,
		To:         tokenContractAddress,
		Amount:     big.NewInt(0), // 0 ETH for token transfers
		Nonce:      senderInfo.Nonce,
		GasPrice:   senderInfo.GasPrice,
		GasLimit:   gasLimit,
		Data:       txData,
		PrivateKey: privateKey,
		ChainID:    senderInfo.ChainID,
	}

	// Execute the transaction and return result
	return s.executeTransaction(client, txParams, result)
}

// batchTransferNativeToken handles batch ETH transfers
func (s *Eth2EthTransferService) batchTransferNativeToken(
	account crypto_transfer.Account,
	req *crypto_transfer.BatchTransferRequest,
) ([]*crypto_transfer.TransferResult, error) {
	var results []*crypto_transfer.TransferResult

	// Convert batch request to multiple single requests
	for _, recipient := range req.Recipients {
		transferReq := &crypto_transfer.TransferRequest{
			Token:     req.Token,
			ToAddress: recipient.ToAddress,
			Amount:    recipient.Amount,
			IsMainnet: req.IsMainnet,
		}

		result, err := s.transferNativeToken(account, transferReq)
		if err != nil {
			return results, err
		}

		results = append(results, result)
	}

	return results, nil
}

// batchTransferFungibleToken is not implemented yet
func (s *Eth2EthTransferService) batchTransferFungibleToken(
	_ crypto_transfer.Account,
	_ *crypto_transfer.BatchTransferRequest,
) ([]*crypto_transfer.TransferResult, error) {
	return nil, e.Wrap(crypto_transfer.ErrNotImplemented, fmt.Errorf("Eth2EthTransferService.batchTransferFungibleToken is not implemented"))
}

// SenderInfo contains necessary info about a sender account
type SenderInfo struct {
	From     common.Address
	Nonce    uint64
	Balance  *big.Int
	GasPrice *big.Int
	ChainID  *big.Int
}

// prepareTransferAccount prepares the sender account for a transfer
func (s *Eth2EthTransferService) prepareTransferAccount(
	account crypto_transfer.Account,
	isMainnet bool,
) (*SenderInfo, *ethclient.Client, *ecdsa.PrivateKey, error) {

	// Get client
	client, err := s.getClient(isMainnet)
	if err != nil {
		return nil, nil, nil, err
	}

	privateKeyStr, err := s.getPrivateKey(account)
	if err != nil {
		return nil, nil, nil, err
	}

	// Convert hex private key to ECDSA private key
	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return nil, nil, nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("parse private key failed: %w", err))
	}

	// Derive sender address
	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return nil, nil, nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get public key failed"))
	}
	fromAddress := crypto.PubkeyToAddress(*publicKeyECDSA)

	// Get account state
	ctx := context.Background()
	balance, err := client.BalanceAt(ctx, fromAddress, nil)
	if err != nil {
		client.Close()
		return nil, nil, nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get balance failed: %w", err))
	}

	nonce, err := client.PendingNonceAt(ctx, fromAddress)
	if err != nil {
		client.Close()
		return nil, nil, nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get nonce failed: %w", err))
	}

	gasPrice, err := client.SuggestGasPrice(ctx)
	if err != nil {
		client.Close()
		return nil, nil, nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get gas price failed: %w", err))
	}

	// Get chain ID
	chainID, err := client.NetworkID(ctx)
	if err != nil {
		client.Close()
		return nil, nil, nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("get chain ID failed: %w", err))
	}

	senderInfo := &SenderInfo{
		From:     fromAddress,
		Nonce:    nonce,
		Balance:  balance,
		GasPrice: gasPrice,
		ChainID:  chainID,
	}

	return senderInfo, client, privateKey, nil
}

// getClient creates a new Ethereum client
func (s *Eth2EthTransferService) getClient(isMainnet bool) (*ethclient.Client, error) {
	nodeURLs := s.getNodeURLs(isMainnet)
	client, err := ethclient.Dial(nodeURLs[0])
	if err == nil {
		return client, nil
	}
	return nil, e.Wrap(crypto_transfer.ErrConnectRPCFailed, fmt.Errorf("connect to node %s failed: %w", nodeURLs[0], err))
}

// estimateGasLimitNative estimates gas for ETH transfers
func (s *Eth2EthTransferService) estimateGasLimitNative(
	client *ethclient.Client,
	from common.Address,
	to common.Address,
	value *big.Int,
) (uint64, error) {
	ctx := context.Background()
	gasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:  from,
		To:    &to,
		Value: value,
	})
	return gasLimit, err
}

// estimateGasLimitToken estimates gas for token transfers
func (s *Eth2EthTransferService) estimateGasLimitToken(
	client *ethclient.Client,
	from common.Address,
	tokenContract common.Address,
	data []byte,
) (uint64, error) {
	ctx := context.Background()
	gasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:  from,
		To:    &tokenContract,
		Value: big.NewInt(0), // 0 ETH since we're just calling a contract
		Data:  data,
	})
	return gasLimit, err
}

// createERC20TransferData creates the data field for an ERC20 transfer
func (s *Eth2EthTransferService) createERC20TransferData(
	to common.Address,
	amount *big.Int,
) []byte {
	// Function signature: transfer(address,uint256)
	transferFnSignature := []byte("transfer(address,uint256)")
	transferFnHash := crypto.Keccak256(transferFnSignature)[:4]

	// Pad address to 32 bytes
	paddedAddress := common.LeftPadBytes(to.Bytes(), 32)

	// Pad amount to 32 bytes
	paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)

	// Concatenate function selector and parameters
	var data []byte
	data = append(data, transferFnHash...)
	data = append(data, paddedAddress...)
	data = append(data, paddedAmount...)

	return data
}

// executeTransaction executes a transaction and waits for confirmation
func (s *Eth2EthTransferService) executeTransaction(
	client *ethclient.Client,
	params *TransactionParams,
	result *crypto_transfer.TransferResult,
) (*crypto_transfer.TransferResult, error) {
	// Create transaction
	txData := &types.LegacyTx{
		Nonce:    params.Nonce,
		To:       &params.To,
		Value:    params.Amount,
		Gas:      params.GasLimit,
		GasPrice: params.GasPrice,
		Data:     params.Data,
	}

	tx := types.NewTx(txData)

	// Sign transaction
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(params.ChainID), params.PrivateKey)
	if err != nil {
		return result, e.Wrap(crypto_transfer.ErrTransferFailed, fmt.Errorf("sign transaction failed: %w", err))
	}

	// Send transaction
	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return result, e.Wrap(crypto_transfer.ErrTransferFailed, fmt.Errorf("send transaction failed: %w", err))
	}

	// Calculate gas cost
	gasCost := new(big.Int).Mul(params.GasPrice, big.NewInt(int64(params.GasLimit)))

	// Set initial status
	result.Status = models.TxStatusPending
	result.TxHash = signedTx.Hash().Hex()
	result.GasBurnt = gasCost.Uint64()
	result.Nonce = params.Nonce
	result.TxDetails = signedTx

	// Wait for transaction confirmation
	return s.waitForTransactionReceipt(client, signedTx, result)
}

// waitForTransactionReceipt waits for a transaction to be mined
func (s *Eth2EthTransferService) waitForTransactionReceipt(
	client *ethclient.Client,
	signedTx *types.Transaction,
	result *crypto_transfer.TransferResult,
) (*crypto_transfer.TransferResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), openedu_eth_sdk.TxConfirmationTimeout)
	defer cancel()

	// Create channels for receipt and errors
	receiptChan := make(chan *types.Receipt)
	errChan := make(chan error)

	// Poll for receipt in a goroutine
	go func() {
		ticker := time.NewTicker(openedu_eth_sdk.TxPollingInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				receipt, err := client.TransactionReceipt(ctx, signedTx.Hash())
				if err != nil {
					if errors.Is(err, ethereum.NotFound) {
						// Transaction still pending, continue polling
						continue
					}
					errChan <- fmt.Errorf("failed to get transaction receipt: %w", err)
					return
				}
				receiptChan <- receipt
				return
			case <-ctx.Done():
				errChan <- ctx.Err()
				return
			}
		}
	}()

	// Wait for either a receipt or an error
	select {
	case receipt := <-receiptChan:
		// Update status based on receipt
		if receipt.Status == types.ReceiptStatusSuccessful {
			result.Status = models.TxStatusSuccess
			result.GasBurnt = receipt.GasUsed
			result.BlockHash = fmt.Sprintf("%d", receipt.BlockNumber.Uint64())
		} else {
			result.Status = models.TxStatusFailed
			result.GasBurnt = receipt.GasUsed

			// Extract detailed error information
			errorDetails, err := openedu_eth_sdk.GetTransactionFailureMsg(ctx, client, signedTx.Hash(), receipt)
			if err != nil || errorDetails == "" {
				result.ErrorMsg = fmt.Sprintf("transaction execution failed: status=%d, blockHash=%s, blockNumber=%s",
					receipt.Status, receipt.BlockHash.Hex(), receipt.BlockNumber.String())
			} else {
				result.ErrorMsg = fmt.Sprintf("transaction execution failed: %s", errorDetails)
			}
		}
	case cErr := <-errChan:
		if errors.Is(cErr, context.DeadlineExceeded) {
			// Timeout occurred, transaction is still pending
			result.Status = models.TxStatusPending
			result.ErrorMsg = "confirmation timeout exceeded"
		} else {
			result.Status = models.TxStatusFailed
			result.ErrorMsg = cErr.Error()
		}
	}

	return result, nil
}

// getNodeURLs returns the appropriate node URLs based on network
func (s *Eth2EthTransferService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetURLs).
		Else(setting.EvmSetting.TestnetURLs)
}

// getPrivateKey gets the private key from an account
func (s *Eth2EthTransferService) getPrivateKey(account crypto_transfer.Account) (string, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return "", e.Wrap(crypto_transfer.ErrGetPrivateKeyFailed, err)
	}

	// Remove '0x' prefix if exists
	if len(privateKeyStr) > 2 && privateKeyStr[:2] == "0x" {
		privateKeyStr = privateKeyStr[2:]
	}

	return privateKeyStr, nil
}

// parseEthAmount2Wei converts ETH amount to Wei (10^18)
func parseEthAmount2Wei(amount decimal.Decimal) *big.Int {
	return amount.Mul(decimal.New(1, openedu_eth_sdk.ETHDecimalExp)).BigInt()
}

// getTokenContractAddress returns the contract address for a given token symbol
func (s *Eth2EthTransferService) getTokenContractAddress(token models.BlockchainToken, isMainnet bool) (string, error) {
	var tokenAddress string

	switch token {
	case models.TokenUSDC:
		tokenAddress = lo.If(isMainnet, setting.EvmSetting.MainnetUSDCContractAddress).Else(setting.EvmSetting.TestnetUSDCContractAddress)
	}

	if tokenAddress == "" {
		return "", crypto_transfer.ErrUnsupportedToken
	}

	return tokenAddress, nil
}

// getTokenDecimals gets the number of decimals for a token
func (s *Eth2EthTransferService) getTokenDecimals(client *ethclient.Client, tokenAddress common.Address) (uint8, error) {
	// ERC20 decimals() function signature
	data := []byte("decimals()")
	hash := crypto.Keccak256(data)[:4]

	// Call the decimals() method on the token contract
	result, err := client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &tokenAddress,
		Data: hash,
	}, nil) // nil for latest block

	if err != nil {
		return 0, err
	}

	if len(result) == 0 {
		// Some older tokens might not have the decimals function
		return 18, nil // Default to 18 as most tokens follow ETH
	}

	// Convert the result to uint8
	decimals := uint8(new(big.Int).SetBytes(result).Uint64())
	return decimals, nil
}

// getTokenBalance gets the token balance for an address
func (s *Eth2EthTransferService) getTokenBalance(
	client *ethclient.Client,
	tokenAddress common.Address,
	accountAddress common.Address,
) (*big.Int, error) {
	// ERC20 balanceOf(address) function signature
	data := []byte("balanceOf(address)")
	hash := crypto.Keccak256(data)[:4]

	// Pad the address parameter to 32 bytes
	paddedAddress := common.LeftPadBytes(accountAddress.Bytes(), 32)

	// Concatenate function selector and address parameter
	var callData []byte
	callData = append(callData, hash...)
	callData = append(callData, paddedAddress...)

	// Call the balanceOf method on the token contract
	result, err := client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &tokenAddress,
		Data: callData,
	}, nil) // nil for latest block

	if err != nil {
		return nil, err
	}

	// Convert the result to big.Int
	return new(big.Int).SetBytes(result), nil
}

// parseTokenAmount converts decimal amount to token units with correct decimals
func (s *Eth2EthTransferService) parseTokenAmount(amount decimal.Decimal, decimals uint8) *big.Int {
	// Calculate 10^decimals
	decimalFactor := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(decimals)))

	// Multiply amount by decimal factor
	tokenAmount := amount.Mul(decimalFactor)
	return tokenAmount.BigInt()
}