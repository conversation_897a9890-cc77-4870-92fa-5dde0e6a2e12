package strategies

import (
	"crypto/ed25519"
	"errors"
	"fmt"
	"github.com/aurora-is-near/near-api-go"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/openedu_near_sdk"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/crypto_transfer"
	"strings"
	"sync"
	"time"
)

type Near2NearTransferService struct{}

func (s *Near2NearTransferService) GetAccountInfo(
	_ *crypto_transfer.GetAccountInfoRequest,
) (*crypto_transfer.GetAccountInfoResponse, error) {
	return nil, fmt.Errorf("implement me: Near2NearTransferService.GetAccountInfo")
}

func (s *Near2NearTransferService) Transfer(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {
	if req.Token != models.TokenNEAR {
		return s.transferFungibleToken(account, req)
	}
	return s.transferNativeToken(account, req)
}

func (s *Near2NearTransferService) BatchTransfer(
	account crypto_transfer.Account,
	req *crypto_transfer.BatchTransferRequest,
) ([]*crypto_transfer.TransferResult, error) {
	if req.Token == models.TokenNEAR {
		if err := s.validateBatchTransferNativeToken(account, req); err != nil {
			return nil, err
		}
	} else {
		if err := s.validateBatchTransferFungibleToken(account, req); err != nil {
			return nil, err
		}
	}

	var results []*crypto_transfer.TransferResult
	for _, recipient := range req.Recipients {
		transferReq := &crypto_transfer.TransferRequest{
			ToAddress: recipient.ToAddress,
			ToNetwork: req.ToNetwork,
			Token:     req.Token,
			Amount:    recipient.Amount,
			IsMainnet: req.IsMainnet,
		}
		if result, err := s.Transfer(account, transferReq); err != nil {
			return nil, err
		} else {
			results = append(results, result)
		}
	}
	return results, nil
}

func (s *Near2NearTransferService) transferNativeToken(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {

	result := &crypto_transfer.TransferResult{
		MethodName: openedu_near_sdk.TransferMethodName,
		InputData:  models.JSONB{},
		GasLimit:   openedu_near_sdk.DefaultGasUint64,
	}

	// Check transfer amount greater than zero
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)",
			crypto_transfer.ErrInvalidAmount, req.Amount.String())
	}

	if !openedu_near_sdk.IsValidAddress(req.ToAddress) {
		return result, fmt.Errorf("%w: invalid to address %s", crypto_transfer.ErrInvalidAddress, req.ToAddress)
	}

	// Get account state to check gas fee and balance
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	accountState, err := s.getAccountState(nodeURLs, account)
	if err != nil {
		if s.isAccountNotExistError(account, err) {
			return result, fmt.Errorf("%w: the account does not exist: %w",
				crypto_transfer.ErrInsufficientBalance, err)
		}
		return result, err
	}

	// Check sender account is whether enough balance to send
	transferAmountInYoctoNEARs := parseNearAmount2Yocto(req.Amount)
	balanceInYoctoNEARs := accountState.Amount
	if transferAmountInYoctoNEARs.GreaterThan(balanceInYoctoNEARs) {
		return result, fmt.Errorf("%w: balance does not enough: balance(%s), amount(%s)",
			crypto_transfer.ErrInsufficientBalance, balanceInYoctoNEARs.String(), transferAmountInYoctoNEARs.String())
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Send NEARs
	txDetails, err := openedu_near_sdk.SendNEARsWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		req.ToAddress,
		transferAmountInYoctoNEARs,
	)
	if err != nil {
		if openedu_near_sdk.IsNotEnoughBalance(err) {
			b, cost, pErr := openedu_near_sdk.ParseBalanceAndCostFromMsg(err.Error())
			if pErr != nil {
				return result, fmt.Errorf("%w: insufficient gas fee", crypto_transfer.ErrInsufficientGasFee)
			}

			return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
				crypto_transfer.ErrInsufficientGasFee, b.String(), cost.Sub(transferAmountInYoctoNEARs).String())
		}
		return nil, err
	}

	// Check transaction status
	time.Sleep(openedu_near_sdk.CheckTxnStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: check transaction status failed: %w", crypto_transfer.ErrTransferFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.TxHash = txDetails.TransactionOutcome.Id
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *Near2NearTransferService) transferFungibleToken(
	account crypto_transfer.Account,
	req *crypto_transfer.TransferRequest,
) (*crypto_transfer.TransferResult, error) {

	result := &crypto_transfer.TransferResult{
		MethodName: openedu_near_sdk.FtTransferMethodName,
		InputData: models.JSONB{
			"receiver_id": req.ToAddress,
		},
		GasLimit: openedu_near_sdk.DefaultGasUint64,
	}

	// Check transfer amount greater than zero
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return result, fmt.Errorf("%w: amount must be greater than 0: amount(%s)",
			crypto_transfer.ErrInvalidAmount, req.Amount.String())
	}

	if !openedu_near_sdk.IsValidAddress(req.ToAddress) {
		return result, fmt.Errorf("%w: invalid to address %s", crypto_transfer.ErrInvalidAddress, req.ToAddress)
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Get token ID and node URLs
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	tokenID := req.TokenID
	if tokenID == "" {
		if tokenID, err = s.token2TokenID(req.Token, req.IsMainnet); err != nil {
			return result, fmt.Errorf("%w: %w", crypto_transfer.ErrGetTokenContractIDFailed, err)
		}
	}

	var wg sync.WaitGroup

	var numDecimals uint8
	var dErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		numDecimals, dErr = s.tokenID2Decimals(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	var accountState *openedu_near_sdk.AccountState
	var aErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		accountState, aErr = s.getAccountState(nodeURLs, account)
	}()

	var ftBalance decimal.Decimal
	var fErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		ftBalance, fErr = openedu_near_sdk.GetFtBalanceOfWithRetry(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	var storageBalance *openedu_near_sdk.StorageBalance
	var sErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		storageBalance, sErr = openedu_near_sdk.GetStorageBalanceOfWithRetry(nodeURLs, req.ToAddress, privateKey, tokenID)
	}()

	wg.Wait()

	if dErr != nil {
		return result, fmt.Errorf("%w: %w", crypto_transfer.ErrGetTokenDecimalsFailed, dErr)
	}

	if fErr != nil {
		return result, fmt.Errorf("%w: %w", crypto_transfer.ErrGetFtBalanceFailed, fErr)
	}

	if sErr != nil {
		return result, fmt.Errorf("%w: %w",
			crypto_transfer.ErrGetStorageBalanceFailed, sErr)
	}

	if aErr != nil {
		if errors.Is(aErr, crypto_transfer.ErrAccountNotExists) {
			return result, fmt.Errorf("%w: the account does not exist: %w",
				crypto_transfer.ErrInsufficientBalance, aErr)
		}

		return result, fmt.Errorf("%w: %w", crypto_transfer.ErrGetAccountStateFailed, aErr)
	}

	// Check sender account is already enough gas fee
	if accountState.Amount.LessThan(openedu_near_sdk.DefaultGasFeeInYocto) {
		return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
			crypto_transfer.ErrInsufficientGasFee, accountState.Amount.String(), openedu_near_sdk.DefaultGasFeeInYocto)
	}

	// Check sender account is already enough FT balance
	transferAmount := parse2TokenDecimalAmount(req.Amount, numDecimals)
	result.InputData["amount"] = transferAmount.String()
	if ftBalance.LessThan(transferAmount) {
		return result, fmt.Errorf(
			"%w: balance does not enough: balance(%s), amount(%s)",
			crypto_transfer.ErrInsufficientBalance, ftBalance.String(), transferAmount.String(),
		)
	}

	// Check whether user deposit storage or not
	if storageBalance.Total.Equal(decimal.Zero) {
		storageBounds, err := openedu_near_sdk.GetStorageBalanceBoundsWithRetry(
			nodeURLs,
			account.GetAddress(),
			privateKey,
			tokenID,
		)
		if err != nil {
			return result, fmt.Errorf("%w: %w", crypto_transfer.ErrGetStorageBoundsFailed, err)
		}

		if accountState.Amount.LessThan(storageBounds.Min.Add(openedu_near_sdk.DefaultGasFeeInYocto)) {
			return result, fmt.Errorf(
				"%w: balance does not enough: balance(%s) min(%s) gas(%s)",
				crypto_transfer.ErrInsufficientBalanceToStorageDeposit,
				accountState.Amount.String(),
				storageBounds.Min.String(),
				openedu_near_sdk.DefaultGasFeeInYocto,
			)
		}

		// Storage deposit
		depositTxDetails, err := openedu_near_sdk.StorageDepositWithRetry(
			nodeURLs,
			account.GetAddress(),
			privateKey,
			tokenID,
			req.ToAddress,
			storageBounds.Min,
			openedu_near_sdk.DefaultGasUint64,
		)
		if err != nil {
			return result, fmt.Errorf("%w: %w", crypto_transfer.ErrStorageDepositFailed, err)
		}

		time.Sleep(crypto_transfer.DelayBeforeCheckStatus)

		depositTxDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
			nodeURLs,
			depositTxDetails.TransactionOutcome.Id,
			account.GetAddress(),
			near.TxExecutionStatus_Default,
		)
		if err != nil {
			return result, fmt.Errorf("%w: check transaction status failed: %w", crypto_transfer.ErrStorageDepositFailed, err)
		}

		if depositTxDetails.IsFailed() {
			return result, fmt.Errorf("%w: %v", crypto_transfer.ErrStorageDepositFailed,
				depositTxDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError)
		}
	}

	// Send FTs
	txDetails, err := openedu_near_sdk.SendFTsWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		tokenID,
		req.ToAddress,
		transferAmount,
		openedu_near_sdk.DefaultGasUint64,
	)
	if err != nil {
		return nil, err
	}

	// Check transaction status
	time.Sleep(openedu_near_sdk.CheckTxnStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: check transaction status failed: %w", crypto_transfer.ErrTransferFailed, err)
	}

	result.ContractID = tokenID
	result.Status = models.TxStatusSuccess
	result.TxHash = txDetails.TransactionOutcome.Id
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *Near2NearTransferService) validateBatchTransferNativeToken(
	account crypto_transfer.Account,
	req *crypto_transfer.BatchTransferRequest,
) error {
	transferAmount := util.ParseNearAmount2Yocto(lo.Reduce(req.Recipients, func(agg decimal.Decimal, item *crypto_transfer.TransferRecipient, _ int) decimal.Decimal {
		return agg.Add(item.Amount)
	}, decimal.Zero))

	// Get account state to check gas fee and balance
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	accountState, err := s.getAccountState(nodeURLs, account)
	if err != nil {
		if errors.Is(err, crypto_transfer.ErrAccountNotExists) {
			return fmt.Errorf("%w: the account does not exist: %w", crypto_transfer.ErrInsufficientBalance, err)
		}
		return err
	}

	// Check sender account is whether enough balance to send
	balance := accountState.Amount
	if transferAmount.GreaterThan(balance) {
		return fmt.Errorf("%w: balance does not enough: balance(%s), amount(%s)", crypto_transfer.ErrInsufficientBalance, balance.String(), transferAmount.String())
	}
	return nil
}

func (s *Near2NearTransferService) validateBatchTransferFungibleToken(
	account crypto_transfer.Account,
	req *crypto_transfer.BatchTransferRequest,
) error {

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return err
	}

	// Get token ID and node URLs
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	tokenID := req.TokenID
	if tokenID == "" {
		if tokenID, err = s.token2TokenID(req.Token, req.IsMainnet); err != nil {
			return fmt.Errorf("%w: %w", crypto_transfer.ErrGetTokenContractIDFailed, err)
		}
	}

	var wg sync.WaitGroup

	var numDecimals uint8
	var dErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		numDecimals, dErr = s.tokenID2Decimals(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	var accountState *openedu_near_sdk.AccountState
	var aErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		accountState, aErr = s.getAccountState(nodeURLs, account)
	}()

	var ftBalance decimal.Decimal
	var fErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		ftBalance, fErr = openedu_near_sdk.GetFtBalanceOfWithRetry(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	//var storageBalance *openedu_near_sdk.StorageBalance
	//var sErr error
	//wg.Add(1)
	//go func() {
	//	defer wg.Done()
	//	storageBalance, sErr = openedu_near_sdk.GetStorageBalanceOfWithRetry(nodeURLs, account.GetAccountID(), privateKey, req.TokenID)
	//}()

	wg.Wait()

	if dErr != nil {
		return fmt.Errorf("%w: %w", crypto_transfer.ErrGetTokenDecimalsFailed, dErr)
	}

	if fErr != nil {
		return fmt.Errorf("%w: %w", crypto_transfer.ErrGetFtBalanceFailed, fErr)
	}

	//if sErr != nil {
	//	return fmt.Errorf("%w: %w", ErrGetStorageBalanceFailed, sErr)
	//}

	if aErr != nil {
		if errors.Is(aErr, crypto_transfer.ErrAccountNotExists) {
			return fmt.Errorf("%w: the account does not exist: %w", crypto_transfer.ErrInsufficientBalance, aErr)
		}
		return fmt.Errorf("%w: %w", crypto_transfer.ErrGetAccountStateFailed, aErr)
	}

	// Check sender account is already enough gas fee
	if accountState.Amount.LessThan(openedu_near_sdk.DefaultGasFeeInYocto.Mul(decimal.NewFromInt(int64(len(req.Recipients))))) {
		return fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
			crypto_transfer.ErrInsufficientGasFee, accountState.Amount.String(), openedu_near_sdk.DefaultGasFeeInYocto)
	}

	// Check sender account is already enough FT balance
	transferAmount := util.ParseReadableAmount2RawValue(lo.Reduce(req.Recipients, func(agg decimal.Decimal, item *crypto_transfer.TransferRecipient, _ int) decimal.Decimal {
		return agg.Add(item.Amount)
	}, decimal.Zero), numDecimals)
	if ftBalance.LessThan(transferAmount) {
		return fmt.Errorf(
			"%w: balance does not enough: balance(%s), amount(%s)",
			crypto_transfer.ErrInsufficientBalance,
			ftBalance.String(),
			transferAmount.String(),
		)
	}

	return nil
}

func (s *Near2NearTransferService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.NearSetting.MainnetURLs).
		Else(setting.NearSetting.TestnetURLs)
}

func (s *Near2NearTransferService) getPrivateKey(account crypto_transfer.Account) (ed25519.PrivateKey, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return nil, fmt.Errorf("%w: %w", crypto_transfer.ErrGetPrivateKeyFailed, err)
	}

	privateKey, err := util.Ed25519PrivateKeyFromString(util.Ed25519Prefix + privateKeyStr)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", crypto_transfer.ErrParsePrivateKeyFailed, err)
	}

	return privateKey, nil
}

func (s *Near2NearTransferService) isAccountNotExistError(account crypto_transfer.Account, err error) bool {
	return strings.Contains(err.Error(), fmt.Sprintf("account %s does not exist while viewing", account.GetAddress()))
}

// TODO refactor as util in pkg/openedu_near_sdk
func (s *Near2NearTransferService) tokenID2Decimals(
	nodeURLs []string,
	accountID string,
	privateKey ed25519.PrivateKey,
	tokenID string,
) (uint8, error) {
	switch tokenID {
	case setting.NearSetting.MainnetUSDTContractID:
		return uint8(setting.NearSetting.MainnetUSDTDecimals), nil

	case setting.NearSetting.MainnetUSDCContractID:
		return uint8(setting.NearSetting.MainnetUSDCDecimals), nil

	case setting.NearSetting.MainnetOpenEduContractID:
		return uint8(setting.NearSetting.MainnetOpenEduDecimals), nil

	case setting.NearSetting.TestnetUSDTContractID:
		return uint8(setting.NearSetting.TestnetUSDTDecimals), nil

	case setting.NearSetting.TestnetUSDCContractID:
		return uint8(setting.NearSetting.TestnetUSDCDecimals), nil

	case setting.NearSetting.TestnetOpenEduContractID:
		return uint8(setting.NearSetting.TestnetOpenEduDecimals), nil
	}

	// TODO implement cache
	ftMetadata, err := openedu_near_sdk.GetFtMetadataWithRetry(nodeURLs, accountID, privateKey, tokenID)
	if err != nil {
		return 0, err
	}

	return ftMetadata.Decimals, nil
}

func (s *Near2NearTransferService) getAccountState(
	nodeURLs []string,
	account crypto_transfer.Account,
) (*openedu_near_sdk.AccountState, error) {
	state, err := openedu_near_sdk.GetAccountStateWithRetry(nodeURLs, account.GetAddress())
	if err == nil {
		return state, nil
	}
	if s.isAccountNotExistError(account, err) {
		return nil, fmt.Errorf("%w: %w", crypto_transfer.ErrAccountNotExists, err)
	}
	return nil, fmt.Errorf("%w: %w", crypto_transfer.ErrGetAccountStateFailed, err)
}

func (s *Near2NearTransferService) token2TokenID(token models.BlockchainToken, isMainnet bool) (string, error) {
	var tokenID string
	switch token {
	case models.TokenUSDT:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetUSDTContractID).Else(setting.NearSetting.TestnetUSDTContractID)

	case models.TokenUSDC:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetUSDCContractID).Else(setting.NearSetting.TestnetUSDCContractID)

	case models.TokenOPENEDU:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetOpenEduContractID).Else(setting.NearSetting.TestnetOpenEduContractID)

	}
	if tokenID == "" {
		return "", fmt.Errorf("unsupported token")
	}
	return tokenID, nil
}

// parseNearAmount2Yocto parses NEAR amount to yoctoNEAR amount
func parseNearAmount2Yocto(amount decimal.Decimal) decimal.Decimal {
	multiplier := decimal.New(1, int32(openedu_near_sdk.NEARNominationExp))
	return amount.Mul(multiplier)
}

// parse2TokenDecimalAmount parses token amount to decimal amount
func parse2TokenDecimalAmount(amount decimal.Decimal, numDecimals uint8) decimal.Decimal {
	multiplier := decimal.New(1, int32(numDecimals))
	return amount.Mul(multiplier)
}
