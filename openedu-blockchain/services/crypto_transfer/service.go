package crypto_transfer

import "openedu-blockchain/models"

type TransferService struct {
	resolver *StrategyResolver
}

func NewTransferService() *TransferService {
	return &TransferService{
		resolver: NewStrategyResolver(),
	}
}

func (s *TransferService) RegisterStrategy(transferType models.TransferType, strategy TransferStrategy) error {
	return s.resolver.RegisterStrategy(transferType, strategy)
}

func (s *TransferService) Transfer(account Account, req *TransferRequest) (*TransferResult, error) {
	strategy, err := s.resolver.PickStrategySingleTransfer(account, req)
	if err != nil {
		return nil, err
	}

	return strategy.Transfer(account, req)
}

func (s *TransferService) BatchTransfer(account Account, req *BatchTransferRequest) ([]*TransferResult, error) {
	strategy, err := s.resolver.PickStrategyBatchTransfer(account, req)
	if err != nil {
		return nil, err
	}

	return strategy.BatchTransfer(account, req)
}

func (s *TransferService) GetAccountInfo(req *GetAccountInfoRequest) (*GetAccountInfoResponse, error) {
	strategy, err := s.resolver.PickStrategyByNetwork(req.Network)
	if err != nil {
		return nil, err
	}

	return strategy.GetAccountInfo(req)
}
