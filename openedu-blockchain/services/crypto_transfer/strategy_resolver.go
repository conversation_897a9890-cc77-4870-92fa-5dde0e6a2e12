package crypto_transfer

import (
	"fmt"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/log"
	"sync"
)

// StrategyResolver manages strategy registration and resolution
type StrategyResolver struct {
	strategies map[models.TransferType]TransferStrategy
	mu         sync.RWMutex
}

func NewStrategyResolver() *StrategyResolver {
	return &StrategyResolver{
		strategies: make(map[models.TransferType]TransferStrategy),
	}
}

// RegisterStrategy registers a transfer strategy for a specific transfer type
func (r *StrategyResolver) RegisterStrategy(transferType models.TransferType, strategy TransferStrategy) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if transferType == "" {
		return fmt.Errorf("transfer type cannot empty")
	}

	if strategy == nil {
		return fmt.Errorf("strategy must not be nil")
	}

	if _, exists := r.strategies[transferType]; exists {
		log.Warnf("Overwriting existing strategy: %v", transferType)
	}

	r.strategies[transferType] = strategy
	log.Infof("Registered transfer strategy: %v", transferType)
	return nil
}

// PickStrategySingleTransfer selects the appropriate strategy based on the transfer request
func (r *StrategyResolver) PickStrategySingleTransfer(account Account, req *TransferRequest) (TransferStrategy, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	transferType, err := r.detectTransferTypeForSingleTransfer(account, req)
	if err != nil {
		return nil, fmt.Errorf("failed to detect transfer type: %w", err)
	}

	strategy, exists := r.strategies[transferType]
	if !exists {
		return nil, fmt.Errorf("failed to find transfer strategy for transfer type: %v", transferType)
	}
	return strategy, nil
}

// detectTransferTypeForSingleTransfer determines the transfer type based on addresses
func (r *StrategyResolver) detectTransferTypeForSingleTransfer(account Account, req *TransferRequest) (models.TransferType, error) {
	fromNEAR := account.GetNetwork() == models.NetworkNEAR
	toNEAR := req.ToNetwork == models.NetworkNEAR
	fromAVAIL := account.GetNetwork() == models.NetworkAVAIL
	toAVAIL := req.ToNetwork == models.NetworkAVAIL
	fromETH := account.GetNetwork() == models.NetworkBASE
	toETH := req.ToNetwork == models.NetworkBASE

	switch {
	case fromNEAR && toNEAR:
		return models.NEAR2NEAR, nil
	case fromAVAIL && toAVAIL:
		return models.AVAIL2AVAIL, nil
	case fromETH && toETH:
		return models.ETH2ETH, nil
	default:
		return "", fmt.Errorf("failed to detect transfer type: from_network=%v to_network=%v", account.GetNetwork(), req.ToNetwork)
	}
}

// PickStrategyBatchTransfer selects the appropriate strategy based on the transfer request
func (r *StrategyResolver) PickStrategyBatchTransfer(account Account, req *BatchTransferRequest) (TransferStrategy, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	transferType, err := r.detectTransferTypeForBatchTransfer(account, req)
	if err != nil {
		return nil, fmt.Errorf("failed to detect transfer type: %w", err)
	}

	strategy, exists := r.strategies[transferType]
	if !exists {
		return nil, fmt.Errorf("failed to find transfer strategy for transfer type: %v", transferType)
	}
	return strategy, nil
}

// detectTransferTypeForBatchTransfer determines the transfer type based on addresses
func (r *StrategyResolver) detectTransferTypeForBatchTransfer(account Account, req *BatchTransferRequest) (models.TransferType, error) {
	fromNEAR := account.GetNetwork() == models.NetworkNEAR
	toNEAR := req.ToNetwork == models.NetworkNEAR
	fromAVAIL := account.GetNetwork() == models.NetworkAVAIL
	toAVAIL := req.ToNetwork == models.NetworkAVAIL

	switch {
	case fromNEAR && toNEAR:
		return models.NEAR2NEAR, nil
	case fromAVAIL && toAVAIL:
		return models.AVAIL2AVAIL, nil
	default:
		return "", fmt.Errorf("failed to detect transfer type: from_network=%v to_network=%v", account.GetNetwork(), req.ToNetwork)
	}
}

// PickStrategyByNetwork picks the appropriate minting strategy based on the account's network
func (r *StrategyResolver) PickStrategyByNetwork(network models.BlockchainNetwork) (TransferStrategy, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var transferType models.TransferType
	switch network {
	case models.NetworkNEAR:
		transferType = models.NEAR2NEAR

	case models.NetworkAVAIL:
		transferType = models.AVAIL2AVAIL
	}

	strategy, exists := r.strategies[transferType]
	if !exists {
		return nil, fmt.Errorf("failed to find transfer strategy for transfer type: %v", transferType)
	}
	return strategy, nil
}
