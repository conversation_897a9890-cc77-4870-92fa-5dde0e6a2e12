package nft

import (
	"fmt"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/log"
	"sync"
)

type StrategyResolver struct {
	strategies map[models.BlockchainNetwork]MintNftStrategy
	mu         sync.RWMutex
}

func NewStrategyResolver() *StrategyResolver {
	return &StrategyResolver{
		strategies: make(map[models.BlockchainNetwork]MintNftStrategy),
	}
}

// RegisterStrategy registers a transfer strategy for a specific transfer type
func (r *StrategyResolver) RegisterStrategy(network models.BlockchainNetwork, strategy MintNftStrategy) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if network == "" {
		return fmt.Errorf("network cannot empty")
	}

	if strategy == nil {
		return fmt.Errorf("strategy cannot be nil")
	}

	if _, exists := r.strategies[network]; exists {
		log.Warnf("Overwriting existing strategy: %v", network)
	}

	r.strategies[network] = strategy
	log.Infof("Registered new mint NFT strategy for network: %v", network)
	return nil
}

// PickStrategy picks the appropriate minting strategy based on the account's network
func (r *StrategyResolver) PickStrategy(account Account) (MintNftStrategy, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	strategy, exists := r.strategies[account.GetNetwork()]
	if !exists {
		return nil, fmt.Errorf("failed to find minting strategy for network: %v", account.GetNetwork())
	}
	return strategy, nil
}

// PickStrategyByNetwork picks the appropriate minting strategy based on the specified network
func (r *StrategyResolver) PickStrategyByNetwork(network models.BlockchainNetwork) (MintNftStrategy, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if network == "" {
		return nil, fmt.Errorf("network cannot be empty")
	}

	strategy, exists := r.strategies[network]
	if !exists {
		return nil, fmt.Errorf("failed to find minting strategy for network: %v", network)
	}
	return strategy, nil
}
