package nft

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/models"
)

type MintNftStrategy interface {
	DepositSponsorGas(account Account, req *DepositSponsorGasRequest) (*DepositSponsorGasResponse, error)
	WithdrawSponsorGas(account Account, req *WithdrawSponsorGasRequest) (*WithdrawSponsorGasResponse, error)
	MintNFT(account Account, req *MintNftRequest) (*MintNftResponse, error)
}

// EIP712Strategy i for EIP-712
type EIP712Strategy interface {
	CreatePermit(req *CreatePermitRequest) (*CreatePermitResponse, error)
	MintNFTWithPermit(account Account, req *MintNftWithPermitRequest) (*MintNftResponse, error)
}

type Account interface {
	GetAddress() string
	GetNetwork() models.BlockchainNetwork
	GetPublicKey() string
	GetPrivateKey() (string, error)
}

type DepositSponsorGasRequest struct {
	CourseCuid string          `json:"course_cuid"`
	Amount     decimal.Decimal `json:"amount"`
	IsMainnet  bool            `json:"is_mainnet"`
}

type WithdrawSponsorGasRequest struct {
	CourseCuid string          `json:"course_cuid"`
	Amount     decimal.Decimal `json:"amount"`
	IsMainnet  bool            `json:"is_mainnet"`
}

type MintNftRequest struct {
	CourseCuid         string                   `json:"course_cuid"`
	CourseOwnerAddress string                   `json:"course_owner_address"`
	GasFeePayer        models.Participant       `json:"gas_fee_payer"`
	TokenID            string                   `json:"token_id"`
	TokenMetadata      TokenMetadata            `json:"token_metadata"`
	Network            models.BlockchainNetwork `json:"network,omitempty"`
	IsMainnet          bool                     `json:"is_mainnet"`
}

type TokenMetadata struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	MediaURL    string `json:"media_url"`
}

type MintNftResponse struct {
	ContractID   string                   `json:"contract_id"`
	MethodName   string                   `json:"method_name"`
	InputData    models.JSONB             `json:"input_data"`
	Deposit      decimal.Decimal          `json:"deposit"`
	Token        models.BlockchainToken   `json:"token"`
	BlockHash    string                   `json:"block_hash"`
	TxHash       string                   `json:"tx_hash"`
	GasLimit     uint64                   `json:"gas_limit"`
	GasBurnt     uint64                   `json:"gas_burnt"`
	StorageCost  decimal.Decimal          `json:"storage_cost"`
	GasCost      decimal.Decimal          `json:"gas_cost"`
	TotalGasCost decimal.Decimal          `json:"total_gas_cost"`
	Nonce        uint64                   `json:"nonce"`
	TxDetails    interface{}              `json:"tx_details"`
	Status       models.TransactionStatus `json:"status"`
	ErrorMsg     string                   `json:"error_msg"`
}

type DepositSponsorGasResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type WithdrawSponsorGasResponse struct {
	ContractID string                   `json:"contract_id"`
	MethodName string                   `json:"method_name"`
	InputData  models.JSONB             `json:"input_data"`
	BlockHash  string                   `json:"block_hash"`
	TxHash     string                   `json:"tx_hash"`
	GasLimit   uint64                   `json:"gas_limit"`
	GasBurnt   uint64                   `json:"gas_burnt"`
	Nonce      uint64                   `json:"nonce"`
	TxDetails  interface{}              `json:"tx_details"`
	Status     models.TransactionStatus `json:"status"`
	ErrorMsg   string                   `json:"error_msg"`
}

type CreatePermitRequest struct {
	ReceiverAddress    string                   `json:"receiver_address"`
	CourseCuid         string                   `json:"course_cuid"`
	CourseOwnerAddress string                   `json:"course_owner_address,omitempty"`
	GasFeePayer        models.Participant       `json:"gas_fee_payer,omitempty"`
	TokenMetadata      TokenMetadata            `json:"token_metadata"`
	Network            models.BlockchainNetwork `json:"network"`
	IsMainnet          bool                     `json:"is_mainnet"`
}

type CreatePermitResponse struct {
	SignatureV        uint8  `json:"signature_v"`
	SignatureR        string `json:"signature_r"`
	SignatureS        string `json:"signature_s"`
	SignatureNonce    int64  `json:"signature_nonce"`
	SignatureDeadline int64  `json:"signature_deadline"`
	TokenURI          string `json:"token_uri,omitempty"`
}

type MintNftWithPermitRequest struct {
	CourseCuid         string                   `json:"course_cuid"`
	CourseOwnerAddress string                   `json:"course_owner_address"`
	GasFeePayer        models.Participant       `json:"gas_fee_payer,omitempty"`
	TokenID            string                   `json:"token_id"`
	TokenMetadata      TokenMetadata            `json:"token_metadata"`
	Network            models.BlockchainNetwork `json:"network"`
	IsMainnet          bool                     `json:"is_mainnet"`
	SignatureV         uint8                    `json:"signature_v"`
	SignatureR         string                   `json:"signature_r"`
	SignatureS         string                   `json:"signature_s"`
	SignatureNonce     int64                    `json:"signature_nonce"`
	SignatureDeadline  int64                    `json:"signature_deadline"`
}
