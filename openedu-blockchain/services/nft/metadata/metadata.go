package metadata

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/setting"
	"sync"
	"time"
)

const (
	// Pinata API constants
	PinataPinFileEndpoint     = "/pinning/pinFileToIPFS"
	PinataAPIKeyHeader        = "pinata_api_key"
	PinataSecretAPIKeyHeader  = "pinata_secret_api_key"
	PinataAuthorizationHeader = "Authorization"
	PinataBearerPrefix        = "Bearer "
	ContentTypeJSON           = "application/json"

	// Timeout constants
	DefaultRequestTimeout = 30 * time.Second

	// Retry constants
	MaxRetries        = 3
	RetryInitialDelay = 1 * time.Second
	RetryMaxDelay     = 5 * time.Second

	// Cache constants
	CacheExpirationTime = 24 * time.Hour
	MaxCacheSize        = 1000
)

type CacheEntry struct {
	Hash      string
	Timestamp time.Time
}

type NFTMetadata struct {
	Name           string                 `json:"name"`
	Description    string                 `json:"description"`
	Image          string                 `json:"image"`
	ExternalURL    string                 `json:"external_url,omitempty"`
	Properties     map[string]interface{} `json:"properties,omitempty"`
	Attributes     []NFTAttribute         `json:"attributes,omitempty"`
	AdditionalData map[string]interface{} `json:"additional_data,omitempty"`
}

type NFTAttribute struct {
	TraitType   string      `json:"trait_type"`
	Value       interface{} `json:"value"`
	DisplayType string      `json:"display_type,omitempty"`
}

type PinataResponse struct {
	IpfsHash    string `json:"IpfsHash"`
	PinSize     int    `json:"PinSize"`
	Timestamp   string `json:"Timestamp"`
	IsDuplicate bool   `json:"isDuplicate"`
}

type Service interface {
	UploadMetadata(metadata *NFTMetadata) (string, error)
	CreateBasicMetadata(title, description, mediaURL string) *NFTMetadata
	CreateMetadataFromCore(title, description, mediaURL, courseCuid, courseOwnerAddress, receiverWalletID, network string) *NFTMetadata
	GetTokenURI(ipfsHash string) string
}

type PinataService struct {
	apiKey    string
	apiSecret string
	jwt       string
	gateway   string
	baseURL   string
	cache     map[string]CacheEntry
	cacheMux  sync.RWMutex
	cacheKeys []string
}

func NewPinataService() Service {
	log.Infof("Initializing PinataService with credentials from settings")

	if setting.PinataSetting.BaseURL == "" {
		log.Fatalf("Pinata BaseURL is not configured")
	}
	if setting.PinataSetting.Gateway == "" {
		log.Fatalf("Pinata Gateway is not configured")
	}
	if setting.PinataSetting.ApiKey == "" && setting.PinataSetting.JWT == "" {
		log.Fatalf("Either Pinata API Key or JWT must be configured")
	}

	service := &PinataService{
		apiKey:    setting.PinataSetting.ApiKey,
		apiSecret: setting.PinataSetting.ApiSecret,
		jwt:       setting.PinataSetting.JWT,
		gateway:   setting.PinataSetting.Gateway,
		baseURL:   setting.PinataSetting.BaseURL,
		cache:     make(map[string]CacheEntry),
		cacheKeys: make([]string, 0, MaxCacheSize),
	}

	return service
}

func generateMetadataHash(metadata *NFTMetadata) (string, error) {
	jsonData, err := json.Marshal(metadata)
	if err != nil {
		return "", err
	}

	hash := sha256.Sum256(jsonData)
	return hex.EncodeToString(hash[:]), nil
}

func (s *PinataService) cleanupExpiredCache() {
	s.cacheMux.Lock()
	defer s.cacheMux.Unlock()

	now := time.Now()
	newKeys := make([]string, 0, len(s.cacheKeys))

	for _, key := range s.cacheKeys {
		entry, exists := s.cache[key]
		if !exists || now.Sub(entry.Timestamp) > CacheExpirationTime {
			delete(s.cache, key)
		} else {
			newKeys = append(newKeys, key)
		}
	}

	s.cacheKeys = newKeys
}

func (s *PinataService) addToCache(metadataHash, ipfsHash string) {
	s.cacheMux.RLock()
	if _, exists := s.cache[metadataHash]; exists {
		s.cacheMux.RUnlock()
		return
	}
	s.cacheMux.RUnlock()

	s.cacheMux.Lock()
	defer s.cacheMux.Unlock()

	if _, exists := s.cache[metadataHash]; exists {
		return
	}

	if len(s.cache) >= MaxCacheSize {
		oldestKey := s.cacheKeys[0]
		delete(s.cache, oldestKey)
		s.cacheKeys = s.cacheKeys[1:]
		log.Infof("Cache size limit reached, evicting oldest entry: %s", oldestKey)
	}

	s.cache[metadataHash] = CacheEntry{
		Hash:      ipfsHash,
		Timestamp: time.Now(),
	}
	s.cacheKeys = append(s.cacheKeys, metadataHash)
	log.Infof("Cached metadata upload result, current cache size: %d", len(s.cache))
}

func (s *PinataService) UploadMetadata(metadata *NFTMetadata) (string, error) {
	metadataHash, err := generateMetadataHash(metadata)
	if err != nil {
		log.Warnf("Failed to generate metadata hash: %v", err)
	} else {
		s.cacheMux.RLock()
		if cacheEntry, found := s.cache[metadataHash]; found {
			if time.Since(cacheEntry.Timestamp) <= CacheExpirationTime {
				log.Infof("Cache hit for metadata: %s", metadata.Name)
				s.cacheMux.RUnlock()
				return cacheEntry.Hash, nil
			}
		}
		s.cacheMux.RUnlock()
	}

	go s.cleanupExpiredCache()

	var (
		pinataResp PinataResponse
		attempts   = 0
		delay      = RetryInitialDelay
	)
	for attempts < MaxRetries {
		if attempts > 0 {
			log.Infof("Retrying Pinata upload (attempt %d of %d) after %v delay", attempts+1, MaxRetries, delay)
			time.Sleep(delay)
			delay = time.Duration(float64(delay) * 1.5)
			if delay > RetryMaxDelay {
				delay = RetryMaxDelay
			}
		}
		attempts++
		ctx, cancel := context.WithTimeout(context.Background(), DefaultRequestTimeout)

		pinataResp, err = s.doUploadMetadata(ctx, metadata)
		cancel()

		if err == nil {
			if metadataHash != "" {
				s.addToCache(metadataHash, pinataResp.IpfsHash)
			}
			return pinataResp.IpfsHash, nil
		}

		if !isRetryableError(err) {
			log.Warnf("Non-retryable error encountered: %v", err)
			return "", err
		}

		log.Warnf("Retryable error encountered: %v", err)
	}

	return "", fmt.Errorf("failed to upload metadata after %d attempts: %w", MaxRetries, err)
}

func (s *PinataService) doUploadMetadata(ctx context.Context, metadata *NFTMetadata) (PinataResponse, error) {
	var pinataResp PinataResponse

	jsonData, err := json.Marshal(metadata)
	if err != nil {
		return pinataResp, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	part, err := writer.CreateFormFile("file", "metadata.json")
	if err != nil {
		return pinataResp, fmt.Errorf("failed to create form file: %w", err)
	}
	_, err = part.Write(jsonData)
	if err != nil {
		return pinataResp, fmt.Errorf("failed to write JSON data: %w", err)
	}

	metadataField, err := writer.CreateFormField("pinataMetadata")
	if err != nil {
		return pinataResp, fmt.Errorf("failed to create metadata field: %w", err)
	}
	pinataMetadata := map[string]interface{}{
		"name": metadata.Name,
	}
	pinataMetadataJSON, err := json.Marshal(pinataMetadata)
	if err != nil {
		return pinataResp, fmt.Errorf("failed to marshal pinata metadata: %w", err)
	}
	_, err = metadataField.Write(pinataMetadataJSON)
	if err != nil {
		return pinataResp, fmt.Errorf("failed to write pinata metadata: %w", err)
	}

	err = writer.Close()
	if err != nil {
		return pinataResp, fmt.Errorf("failed to close writer: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.baseURL+PinataPinFileEndpoint, body)
	if err != nil {
		return pinataResp, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	if s.jwt != "" {
		req.Header.Add(PinataAuthorizationHeader, PinataBearerPrefix+s.jwt)
	} else {
		req.Header.Add(PinataAPIKeyHeader, s.apiKey)
		req.Header.Add(PinataSecretAPIKeyHeader, s.apiSecret)
	}
	req.Header.Add("Accept", ContentTypeJSON)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return pinataResp, fmt.Errorf("request timed out after %s: %w", DefaultRequestTimeout, err)
		}
		return pinataResp, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return pinataResp, fmt.Errorf("upload failed with status code: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	err = json.NewDecoder(resp.Body).Decode(&pinataResp)
	if err != nil {
		return pinataResp, fmt.Errorf("failed to decode response: %w", err)
	}

	return pinataResp, nil
}

func isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	if err.Error() == "context deadline exceeded" || err.Error() == "net/http: request canceled" {
		return true
	}

	if err.Error() == "connection refused" || err.Error() == "no such host" ||
		err.Error() == "i/o timeout" || err.Error() == "network is unreachable" {
		return true
	}

	errStr := err.Error()
	for _, code := range []string{"500", "502", "503", "504"} {
		if errStr == fmt.Sprintf("upload failed with status code: %s", code) {
			return true
		}
	}

	return false
}

func (s *PinataService) CreateBasicMetadata(title, description, mediaURL string) *NFTMetadata {
	return &NFTMetadata{
		Name:        title,
		Description: description,
		Image:       mediaURL,
	}
}

func (s *PinataService) CreateMetadataFromCore(title, description, mediaURL, courseCuid, courseOwnerAddress, receiverWalletID, network string) *NFTMetadata {
	attributes := []NFTAttribute{
		{TraitType: "Course ID", Value: courseCuid},
		{TraitType: "Course Owner Address", Value: courseOwnerAddress},
		{TraitType: "Receiver Wallet ID", Value: receiverWalletID},
		{TraitType: "Network", Value: network},
	}

	properties := map[string]interface{}{
		"course_id":            courseCuid,
		"course_owner_address": courseOwnerAddress,
		"receiver_wallet_id":   receiverWalletID,
		"network":              network,
	}

	return &NFTMetadata{
		Name:        title,
		Description: description,
		Image:       mediaURL,
		Attributes:  attributes,
		Properties:  properties,
	}
}

func (s *PinataService) GetTokenURI(ipfsHash string) string {
	return fmt.Sprintf("%s/%s", s.gateway, ipfsHash)
}
