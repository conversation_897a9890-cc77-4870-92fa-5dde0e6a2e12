package services

import (
	"fmt"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services/launchpad"
	"strings"
)

type LaunchpadService struct {
	launchpadService *launchpad.ProcessLaunchpadService
}

type LaunchpadServiceDeps struct {
	LaunchpadService *launchpad.ProcessLaunchpadService
}

func NewLaunchpadService(deps *LaunchpadServiceDeps) *LaunchpadService {
	return &LaunchpadService{
		launchpadService: deps.LaunchpadService,
	}
}

func (s *LaunchpadService) GetVotingPowersByPool(req *dto.GetVotingPowersRequest) (*dto.GetVotingPowersResponse, *e.AppError) {
	resp, err := s.launchpadService.GetVotingPowers(&launchpad.GetVotingPowersRequest{
		PoolID:    req.PoolID,
		Network:   req.Network,
		IsMainnet: req.IsMainnet,
	})
	if err != nil {
		return nil, e.NewError500(e.TransactionGetVotingPowersFailed,
			"Get voting powers by pool ID "+req.PoolID+" on network "+strings.ToUpper(string(req.Network))+" failed: "+err.Error())
	}

	var addresses []string
	result := dto.GetVotingPowersResponse{}
	for _, votingPower := range resp.VotingPowers {
		addresses = append(addresses, votingPower.Address)
		result.VotingPowers = append(result.VotingPowers, &dto.VotingPowerEntry{
			UserID:      "",
			Address:     votingPower.Address,
			Amount:      votingPower.Amount,
			VotingPower: votingPower.VotingPower,
		})
	}

	if len(addresses) > 0 {
		wallets, err := models.Repository.Wallet.FindMany(&models.WalletQuery{
			AddressIn: addresses,
			Network:   &req.Network,
		}, &models.FindManyOptions{})
		if err != nil {
			return nil, e.NewError500(e.TransactionGetVotingPowersFailed,
				fmt.Sprintf("Find wallets with address in (%v) on network %s failed: %v", addresses, req.Network, err))
		}

		userIDsByAddresses := make(map[string]string)
		for _, wallet := range wallets {
			userIDsByAddresses[wallet.Address] = wallet.UserID
		}

		for _, votingPower := range result.VotingPowers {
			votingPower.UserID = userIDsByAddresses[votingPower.Address]
		}
	}
	return &result, nil
}
