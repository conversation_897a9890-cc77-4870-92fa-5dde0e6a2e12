package strategies

import (
	"crypto/ed25519"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/openedu_near_sdk"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/crypto_transfer"
	"openedu-blockchain/services/launchpad"
	"openedu-blockchain/services/nft"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/aurora-is-near/near-api-go"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

const (
	NearDefaultGas           = 300_000_000_000_000 // 300 Tgas
	NearCheckStatusDelay     = 200 * time.Millisecond
	NearInitPoolDepositNEARs = 1 // 1 NEAR
	NearInitPoolLogRegexp    = `Pool (\d+) created by .+ using token ([^\s]+)\. Staking amount: (\d+) yoctoNEAR`
	NearClaimRefundLogRegexp = `User ([^\s]+)\ withdrew (\d+) Token from pool (\d+)`
	NearClaimRefundLogPrefix = "User "
)

var (
	NearDefaultGasFeeInYocto = util.ParseReadableAmount2RawValue(decimal.NewFromFloat(0.2), util.NearNominationExp) // 0.2NEAR
)

type NearLaunchpadService struct{}

type NearLaunchpadPoolResult struct {
	PoolID            int     `json:"pool_id"`
	CampaignID        string  `json:"campaign_id"`
	CreatorID         string  `json:"creator_id"`
	StakingAmount     float64 `json:"staking_amount"`
	Status            string  `json:"status"`
	TokenID           string  `json:"token_id"`
	TotalBalance      int     `json:"total_balance"`
	TargetFunding     int     `json:"target_funding"`
	TimeStartPledge   int64   `json:"time_start_pledge"`
	TimeEndPledge     int64   `json:"time_end_pledge"`
	MinMultiplePledge int     `json:"min_multiple_pledge"`
}

func (s *NearLaunchpadService) InitPool(
	account launchpad.Account,
	req *launchpad.InitPoolRequest,
) (*launchpad.InitPoolResponse, error) {

	stakingAmountInNEARs := decimal.NewFromInt(NearInitPoolDepositNEARs)
	stakingAmountInYoctoNEARs := util.ParseReadableAmount2RawValue(stakingAmountInNEARs, util.NearNominationExp)
	contractID, method, args, privateKey, err := s.getParamsForInitPool(account, req)
	result := &launchpad.InitPoolResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Deposit:    stakingAmountInNEARs,
		Token:      models.TokenNEAR,
	}
	if err != nil {
		return result, err
	}

	// Check balance is enough to sponsor
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	accountState, err := s.getAccountState(nodeURLs, account)
	if err != nil {
		if errors.Is(err, launchpad.ErrAccountNotExists) {
			return nil, fmt.Errorf("%w: the account does not exist: %w", launchpad.ErrInsufficientBalance, err)
		}
		return result, err
	}

	balanceInYoctoNEARs := accountState.Amount
	if stakingAmountInYoctoNEARs.GreaterThan(accountState.Amount) {
		return result, fmt.Errorf("%w: balance does not enough: balance(%s), amount(%s)",
			launchpad.ErrInsufficientBalance, balanceInYoctoNEARs.String(), stakingAmountInYoctoNEARs.String())
	}

	// Init pool
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*stakingAmountInYoctoNEARs.BigInt(),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}

	if txDetails.Status.SuccessValue != nil {
		successValue, pErr := s.parseLaunchpadPoolResult(*txDetails.Status.SuccessValue)
		if pErr != nil {
			return result, fmt.Errorf("%w: parse init pool result failed: %w", launchpad.ErrInitPoolFailed, pErr)
		}

		result.PoolID = fmt.Sprintf("%d", successValue.PoolID)
	}

	return result, nil
}

func (s *NearLaunchpadService) ApprovePool(
	req *launchpad.ApprovePoolRequest,
) (*launchpad.ApprovePoolStatusResponse, error) {

	contractID, method, args := s.getParamsForApprovePool(req)
	result := &launchpad.ApprovePoolStatusResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Token:      models.TokenNEAR,
	}

	// Get private key
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	// Approve pool
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearLaunchpadService) Pledge(
	account launchpad.Account,
	req *launchpad.PledgeLaunchpadRequest,
) (*launchpad.PledgeLaunchpadResponse, error) {

	contractID, method := s.getContractAndMethodForPledgeLaunchpad(req)
	result := &launchpad.PledgeLaunchpadResponse{
		ContractID: contractID,
		MethodName: method,
		GasLimit:   NearDefaultGas,
		Deposit:    req.Amount,
		Token:      req.Token,
	}

	tokenID, err := s.token2TokenID(req.Token, req.IsMainnet)
	if err != nil {
		return nil, err
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	var wg sync.WaitGroup

	var numDecimals uint8
	var dErr error
	wg.Add(1)
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	go func() {
		defer wg.Done()
		numDecimals, dErr = s.tokenID2Decimals(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	var accountState *openedu_near_sdk.AccountState
	var aErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		accountState, aErr = s.getAccountState(nodeURLs, account)
	}()

	var ftBalance decimal.Decimal
	var fErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		ftBalance, fErr = openedu_near_sdk.GetFtBalanceOfWithRetry(nodeURLs, account.GetAddress(), privateKey, tokenID)
	}()

	var storageBalance *openedu_near_sdk.StorageBalance
	var sErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		storageBalance, sErr = openedu_near_sdk.GetStorageBalanceOfWithRetry(nodeURLs, contractID, privateKey, tokenID)
	}()

	wg.Wait()

	if dErr != nil {
		return result, fmt.Errorf("%w: %w", launchpad.ErrGetTokenDecimalsFailed, dErr)
	}

	if fErr != nil {
		return result, fmt.Errorf("%w: %w", launchpad.ErrGetFtBalanceFailed, fErr)
	}

	if sErr != nil {
		return result, fmt.Errorf("%w: %w",
			launchpad.ErrGetStorageBalanceFailed, sErr)
	}

	if aErr != nil {
		if errors.Is(aErr, launchpad.ErrAccountNotExists) {
			return result, fmt.Errorf("%w: the account does not exist: %w",
				launchpad.ErrInsufficientBalance, aErr)
		}

		return result, fmt.Errorf("%w: %w", launchpad.ErrGetAccountStateFailed, aErr)
	}

	rawPledgeAmount := util.ParseReadableAmount2RawValue(req.Amount, numDecimals)
	args := s.getArgsForPledgeLaunchpad(req, contractID, rawPledgeAmount)
	result.InputData = args

	// Check sender account is already enough gas fee
	if accountState.Amount.LessThan(NearDefaultGasFeeInYocto) {
		return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
			launchpad.ErrInsufficientGasFee, accountState.Amount.String(), NearDefaultGasFeeInYocto)
	}

	// Check sender account is already enough FT balance
	if ftBalance.LessThan(rawPledgeAmount) {
		return result, fmt.Errorf(
			"%w: balance does not enough: balance(%s), amount(%s)",
			launchpad.ErrInsufficientBalance, ftBalance.String(), rawPledgeAmount.String(),
		)
	}

	// Check whether user deposit storage or not
	if storageBalance.Total.Equal(decimal.Zero) {
		storageBounds, err := openedu_near_sdk.GetStorageBalanceBoundsWithRetry(
			nodeURLs,
			account.GetAddress(),
			privateKey,
			tokenID,
		)
		if err != nil {
			return result, fmt.Errorf("%w: %w", launchpad.ErrGetStorageBoundsFailed, err)
		}

		if accountState.Amount.LessThan(storageBounds.Min.Add(NearDefaultGasFeeInYocto)) {
			return result, fmt.Errorf(
				"%w: balance does not enough: balance(%s) min(%s) gas(%s)",
				launchpad.ErrInsufficientBalanceToStorageDeposit,
				accountState.Amount.String(),
				storageBounds.Min.String(),
				NearDefaultGasFeeInYocto,
			)
		}

		// Storage deposit
		depositTxDetails, err := openedu_near_sdk.StorageDepositWithRetry(
			nodeURLs,
			account.GetAddress(),
			privateKey,
			tokenID,
			contractID,
			storageBounds.Min,
			NearDefaultGas,
		)
		if err != nil {
			return result, fmt.Errorf("%w: %w", launchpad.ErrStorageDepositFailed, err)
		}

		time.Sleep(NearCheckStatusDelay)

		depositTxDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
			nodeURLs,
			depositTxDetails.TransactionOutcome.Id,
			account.GetAddress(),
			near.TxExecutionStatus_Default,
		)
		if err != nil {
			return result, fmt.Errorf("%w: check transaction status failed: %w", crypto_transfer.ErrStorageDepositFailed, err)
		}

		if depositTxDetails.IsFailed() {
			return result, fmt.Errorf("%w: %v", crypto_transfer.ErrStorageDepositFailed,
				depositTxDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError)
		}
	}

	// Pledge to pool
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		tokenID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(1),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}

	var errorPattern = regexp.MustCompile(
		"Token ID from message does not match any token ID in the list|" +
			"Invalid pool ID in message|" +
			"Pool does not exist|" +
			"Pool is not in funding status|" +
			"Not within pledge period|" +
			"Invalid token for this pool")

OuterLoop:
	for _, receipt := range txDetails.ReceiptsOutcome {
		for _, str := range receipt.Outcome.Logs {
			if errorPattern.MatchString(str) {
				log.Debugf("NearLaunchpadService::Pledge failed receipt log: %s", str)
				result.Status = models.TxStatusFailed
				result.ErrorMsg = str
				break OuterLoop
			}
		}
	}

	return result, nil
}

func (s *NearLaunchpadService) UpdatePoolFundingTime(
	req *launchpad.UpdatePoolFundingTimeRequest,
) (*launchpad.UpdatePoolFundingTimeResponse, error) {

	contractID, method, args := s.getParamsForUpdatePoolFundingTime(req)
	result := &launchpad.UpdatePoolFundingTimeResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Token:      models.TokenNEAR,
	}

	// Get private key
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	// Update pool funding time
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearLaunchpadService) CancelPool(
	account launchpad.Account,
	req *launchpad.CancelPoolRequest,
) (*launchpad.CancelPoolResponse, error) {
	contractID, method, args := s.getParamsForCancelPool(req)
	result := &launchpad.CancelPoolResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Token:      models.TokenNEAR,
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Check gas fee is enough
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	accountState, err := s.getAccountState(nodeURLs, account)
	if err != nil {
		if errors.Is(err, launchpad.ErrAccountNotExists) {
			return nil, fmt.Errorf("%w: the account does not exist: %w", launchpad.ErrInsufficientBalance, err)
		}
		return result, err
	}

	if accountState.Amount.LessThan(NearDefaultGasFeeInYocto) {
		return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
			launchpad.ErrInsufficientGasFee, accountState.Amount.String(), NearDefaultGasFeeInYocto)
	}

	// Cancel pool
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearLaunchpadService) GetVotingPowers(
	req *launchpad.GetVotingPowersRequest,
) (*launchpad.GetVotingPowersResponse, error) {

	// Get private key
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	// Get pool detail
	poolDetails, err := s.GetPoolDetails(&launchpad.GetPoolDetailsRequest{
		PoolID:    req.PoolID,
		Network:   req.Network,
		IsMainnet: req.IsMainnet,
	})
	if err != nil {
		return nil, err
	}

	// Get voting powers
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	contractID, method := s.getParamsForGetVotingPowers(req)
	votingPowers, err := openedu_near_sdk.GetVotingPowersByPoolWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		req.PoolID,
	)
	if err != nil {
		return nil, err
	}

	// Get num decimals of token
	numDecimals, err := s.tokenID2Decimals(nodeURLs, contractID, privateKey, poolDetails.TokenId)
	if err != nil {
		return nil, err
	}

	totalAmount := util.ParseRawValue2ReadableAmount(decimal.NewFromInt(poolDetails.TotalBalance), numDecimals)
	return &launchpad.GetVotingPowersResponse{
		VotingPowers: lo.Map(votingPowers, func(item *openedu_near_sdk.UserVotingPower, _ int) *launchpad.VotingPowerEntry {
			return &launchpad.VotingPowerEntry{
				Address:     item.UserId,
				Amount:      util.ParseRawValue2ReadableAmount(decimal.NewFromInt(item.Record.Amount), numDecimals),
				TotalAmount: totalAmount,
				VotingPower: item.Record.VotingPower,
			}
		}),
	}, nil
}

func (s *NearLaunchpadService) GetPoolDetails(
	req *launchpad.GetPoolDetailsRequest,
) (*launchpad.GetPoolDetailsResponse, error) {

	// Get private key
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	// Get pool detail
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	contractID, method := s.getParamsForGetPoolDetails(req)
	poolDetails, err := openedu_near_sdk.GetPoolDetailsWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		req.PoolID,
	)
	if err != nil {
		return nil, err
	}

	if poolDetails == nil {
		return nil, fmt.Errorf("%w: parse pool detail failed", launchpad.ErrDecodeResultFailed)
	}

	return &launchpad.GetPoolDetailsResponse{
		CampaignId:        poolDetails.CampaignId,
		CreatorId:         poolDetails.CreatorId,
		MinMultiplePledge: poolDetails.MinMultiplePledge,
		PoolId:            poolDetails.PoolId,
		StakingAmount:     poolDetails.StakingAmount,
		Status:            poolDetails.Status,
		TargetFunding:     poolDetails.TargetFunding,
		TimeEndPledge:     poolDetails.TimeEndPledge / 1000_000,
		TimeStartPledge:   poolDetails.TimeStartPledge / 1000_000,
		TokenId:           poolDetails.TokenId,
		TotalBalance:      poolDetails.TotalBalance,
	}, nil
}

func (s *NearLaunchpadService) CheckFundingResult(
	req *launchpad.CheckFundingRequest,
) (*launchpad.CheckFundingResponse, error) {
	contractID, method, args := s.getParamsForCheckFundingResult(req)
	result := &launchpad.CheckFundingResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Token:      models.TokenNEAR,
	}

	// Get private key
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	//// Check gas fee is enough
	//accountState, err := s.getAccountState(nodeURLs, account)
	//if err != nil {
	//	if errors.Is(err, launchpad.ErrAccountNotExists) {
	//		return nil, fmt.Errorf("%w: the account does not exist: %w", launchpad.ErrInsufficientBalance, err)
	//	}
	//	return result, err
	//}
	//
	//if accountState.Amount.LessThan(NearDefaultGasFeeInYocto) {
	//	return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
	//		launchpad.ErrInsufficientGasFee, accountState.Amount.String(), NearDefaultGasFeeInYocto)
	//}

	// Check funding result pool
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}

	if txDetails.Status.SuccessValue != nil {
		successValue, pErr := s.parseLaunchpadPoolResult(*txDetails.Status.SuccessValue)
		if pErr != nil {
			return result, fmt.Errorf("%w: parse init pool result failed: %w", launchpad.ErrInitPoolFailed, pErr)
		}

		result.PoolID = fmt.Sprintf("%d", successValue.PoolID)
		result.PoolStatus = successValue.Status
	}
	return result, nil
}

func (s *NearLaunchpadService) ContinueWithPartialFund(
	req *launchpad.ContinueLpPartialFundRequest,
) (*launchpad.ContinueLpPartialFundResponse, error) {
	contractID, method, args := s.getParamsForContinuePartialFund(req)
	result := &launchpad.ContinueLpPartialFundResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Token:      models.TokenNEAR,
	}

	// Get private key
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	// Creator accept continue voting
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}

	if txDetails.Status.SuccessValue != nil {
		successValue, pErr := s.parseLaunchpadPoolResult(*txDetails.Status.SuccessValue)
		if pErr != nil {
			return result, fmt.Errorf("%w: parse init pool result failed: %w", launchpad.ErrInitPoolFailed, pErr)
		}

		result.PoolID = fmt.Sprintf("%d", successValue.PoolID)
		result.PoolStatus = successValue.Status
	}
	return result, nil
}

func (s *NearLaunchpadService) SetPoolFundingTime(
	account launchpad.Account,
	req *launchpad.SetPoolFundingTimeRequest,
) (*launchpad.SetPoolFundingTimeResponse, error) {

	contractID, method, args := s.getParamsForSetPoolFundingTime(req)
	result := &launchpad.SetPoolFundingTimeResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Token:      models.TokenNEAR,
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Set pool funding time
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearLaunchpadService) WithdrawToCreator(
	req *launchpad.WithdrawToCreatorRequest,
) (*launchpad.WithdrawToCreatorResponse, error) {

	contractID, method := s.getContractAndMethodForWithdrawToCreator(req)
	result := &launchpad.WithdrawToCreatorResponse{
		ContractID: contractID,
		MethodName: method,
		GasLimit:   NearDefaultGas,
		Deposit:    req.Amount,
		Token:      req.Token,
	}

	tokenID, err := s.token2TokenID(req.Token, req.IsMainnet)
	if err != nil {
		return nil, err
	}
	// Get private key
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	nodeURLs := s.getNodeURLs(req.IsMainnet)
	numDecimals, dErr := s.tokenID2Decimals(nodeURLs, contractID, privateKey, tokenID)
	if dErr != nil {
		return nil, dErr
	}

	rawPledgeAmount := util.ParseReadableAmount2RawValue(req.Amount, numDecimals)
	args := s.getArgsForWithdrawToCreator(req, rawPledgeAmount)
	result.InputData = args

	// Withdraw to creator
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearLaunchpadService) ClaimRefund(
	account launchpad.Account,
	req *launchpad.ClaimRefundRequest,
) (*launchpad.ClaimRefundResponse, error) {

	contractID, method, args := s.getParamsForClaimRefund(req)
	result := &launchpad.ClaimRefundResponse{
		ContractID: contractID,
		MethodName: method,
		GasLimit:   NearDefaultGas,
		InputData:  args,
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Pledge to pool
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		account.GetAddress(),
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		account.GetAddress(),
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}

	for _, receipt := range txDetails.ReceiptsOutcome {
		for _, str := range receipt.Outcome.Logs {
			if strings.HasPrefix(str, NearClaimRefundLogPrefix) {
				log.Debugf("NearLaunchpadService::ClaimRefund find receipt log: %s", str)
				if _, _, refundAmount, pErr := s.parseClaimRefundLog(str); pErr != nil {
					log.Errorf("NearLaunchpadService::ClaimRefund parse init pool log failed: %v", pErr)
				} else {
					tokenID, err := s.token2TokenID(req.Token, req.IsMainnet)
					if err != nil {
						return result, err
					}

					numDecimals, dErr := s.tokenID2Decimals(nodeURLs, account.GetAddress(), privateKey, tokenID)
					if dErr != nil {
						return result, dErr
					}
					result.Deposit = util.ParseRawValue2ReadableAmount(refundAmount, numDecimals)
				}
			}
		}
	}

	return result, nil
}

func (s *NearLaunchpadService) UpdatePoolStatus(
	req *launchpad.UpdatePoolStatusRequest,
) (*launchpad.UpdatePoolStatusStatusResponse, error) {

	contractID, method, args := s.getParamsForUpdatePoolStatus(req)
	result := &launchpad.UpdatePoolStatusStatusResponse{
		ContractID: contractID,
		MethodName: method,
		InputData:  args,
		GasLimit:   NearDefaultGas,
		Token:      models.TokenNEAR,
	}

	// Get private key
	contractPKey := lo.If(req.IsMainnet, setting.NearSetting.MainnetLaunchpadPrivateKey).
		Else(setting.NearSetting.TestnetLaunchpadPrivateKey)

	privateKey, err := util.Ed25519PrivateKeyFromString(contractPKey)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", nft.ErrParsePrivateKeyFailed, err)
	}

	// Approve pool
	nodeURLs := s.getNodeURLs(req.IsMainnet)
	txDetails, err := openedu_near_sdk.FunctionCallWithRetry(
		nodeURLs,
		contractID,
		privateKey,
		contractID,
		method,
		args,
		NearDefaultGas,
		*big.NewInt(0),
	)
	if err != nil {
		return result, err
	}

	// Check transaction status
	time.Sleep(NearCheckStatusDelay)
	txDetails, err = openedu_near_sdk.GetTransactionDetailsWithWait(
		nodeURLs,
		txDetails.TransactionOutcome.Id,
		contractID,
		near.TxExecutionStatus_Default,
	)
	if err != nil {
		return result, fmt.Errorf("%w: check transaction status failed: %w", launchpad.ErrInitPoolFailed, err)
	}

	result.Status = models.TxStatusSuccess
	result.BlockHash = txDetails.TransactionOutcome.BlockHash
	result.TxHash = txDetails.TransactionOutcome.Id
	result.GasBurnt = txDetails.TransactionOutcome.Outcome.GasBurnt
	result.Nonce = txDetails.Transaction.Nonce
	result.TxDetails = txDetails
	if txDetails.Status.Failure != nil {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = txDetails.Status.Failure.ActionError.Kind.FunctionCallError.ExecutionError
	}
	return result, nil
}

func (s *NearLaunchpadService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.NearSetting.MainnetURLs).
		Else(setting.NearSetting.TestnetURLs)
}

func (s *NearLaunchpadService) getAccountState(nodeURLs []string, account launchpad.Account) (*openedu_near_sdk.AccountState, error) {
	state, err := openedu_near_sdk.GetAccountStateWithRetry(nodeURLs, account.GetAddress())
	if err == nil {
		return state, nil
	}
	if s.isAccountNotExistError(account, err) {
		return nil, fmt.Errorf("%w: %w", launchpad.ErrAccountNotExists, err)
	}
	return nil, fmt.Errorf("%w: %w", launchpad.ErrGetAccountStateFailed, err)
}

func (s *NearLaunchpadService) isAccountNotExistError(account launchpad.Account, err error) bool {
	return strings.Contains(err.Error(), fmt.Sprintf("account %s does not exist while viewing", account.GetAddress()))
}

func (s *NearLaunchpadService) getPrivateKey(account launchpad.Account) (ed25519.PrivateKey, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return nil, fmt.Errorf("%w: %w", launchpad.ErrGetPrivateKeyFailed, err)
	}

	privateKey, err := util.Ed25519PrivateKeyFromString(util.Ed25519Prefix + privateKeyStr)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", launchpad.ErrParsePrivateKeyFailed, err)
	}

	return privateKey, nil
}

func (s *NearLaunchpadService) getParamsForInitPool(
	account launchpad.Account,
	req *launchpad.InitPoolRequest,
) (string, string, models.JSONB, ed25519.PrivateKey, error) {

	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadInitPoolMethodName
	args := models.JSONB{
		"campaign_id": req.LaunchpadID,
		"token_id":    "",
		//"time_start_pledge": req.FundingStartDate * 1000_000,
		//"time_end_pledge":   req.FundingEndDate * 1000_000,
	}
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadInitPoolMethodName
	}

	tokenID, err := s.token2TokenID(req.Token, req.IsMainnet)
	if err != nil {
		return contractID, methodName, args, nil, nil
	}

	// Get private key from account
	privateKey, err := s.getPrivateKey(account)
	if err != nil {
		return contractID, methodName, args, nil, nil
	}

	nodeURLs := s.getNodeURLs(req.IsMainnet)
	numDecimals, err := s.tokenID2Decimals(nodeURLs, contractID, privateKey, tokenID)
	if err != nil {
		return contractID, methodName, args, privateKey, nil
	}

	args["token_id"] = tokenID
	args["target_funding"] = util.ParseReadableAmount2RawValue(req.TargetFunding, numDecimals)
	args["min_multiple_pledge"] = util.ParseReadableAmount2RawValue(req.MinPledge, numDecimals).BigInt()
	return contractID, methodName, args, privateKey, nil
}

func (s *NearLaunchpadService) token2TokenID(token models.BlockchainToken, isMainnet bool) (string, error) {
	var tokenID string
	switch token {
	case models.TokenUSDT:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetUSDTContractID).Else(setting.NearSetting.TestnetUSDTContractID)

	case models.TokenUSDC:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetUSDCContractID).Else(setting.NearSetting.TestnetUSDCContractID)

	case models.TokenOPENEDU:
		tokenID = lo.If(isMainnet, setting.NearSetting.MainnetOpenEduContractID).Else(setting.NearSetting.TestnetOpenEduContractID)

	}
	if tokenID == "" {
		return "", fmt.Errorf("%w: unsupport token", launchpad.ErrGetTokenIDFailed)
	}
	return tokenID, nil
}

func (s *NearLaunchpadService) parseInitPoolLog(str string) (poolID, tokenID string, stakingAmount decimal.Decimal, err error) {
	re := regexp.MustCompile(NearInitPoolLogRegexp)
	matches := re.FindStringSubmatch(str)
	if len(matches) != 4 {
		return "", "", decimal.Zero, fmt.Errorf("invalid log format")
	}

	poolID = matches[1]
	tokenID = matches[2]
	stakingAmount, err = decimal.NewFromString(matches[3])
	if err != nil {
		return "", "", decimal.Zero, fmt.Errorf("failed to parse staking amount: %v", err)
	}

	return poolID, tokenID, stakingAmount, nil
}

func (s *NearLaunchpadService) parseClaimRefundLog(str string) (poolID, userID string, refundAmount decimal.Decimal, err error) {
	re := regexp.MustCompile(NearClaimRefundLogRegexp)
	matches := re.FindStringSubmatch(str)
	if len(matches) != 4 {
		return "", "", decimal.Zero, fmt.Errorf("invalid log format")
	}

	userID = matches[1]
	refundAmount, err = decimal.NewFromString(matches[2])
	if err != nil {
		return "", "", decimal.Zero, fmt.Errorf("failed to parse refund amount: %v", err)
	}
	poolID = matches[3]

	return poolID, userID, refundAmount, nil
}

func (s *NearLaunchpadService) parseLaunchpadPoolResult(str string) (*NearLaunchpadPoolResult, error) {
	decodedBytes, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return nil, fmt.Errorf("%w: decode base64 string failed: %w", launchpad.ErrDecodeResultFailed, err)
	}

	var result NearLaunchpadPoolResult
	err = json.Unmarshal(decodedBytes, &result)
	if err != nil {
		return nil, fmt.Errorf("%w: unmarshal bytes failed: %w", launchpad.ErrDecodeResultFailed, err)
	}

	return &result, nil
}

func (s *NearLaunchpadService) getParamsForApprovePool(req *launchpad.ApprovePoolRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadApprovePoolMethodName
	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id": poolIdInt,
		"approve": req.IsApproved,
	}
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadApprovePoolMethodName
	}
	return contractID, methodName, args
}

func (s *NearLaunchpadService) tokenID2Decimals(nodeURLs []string, accountID string, privateKey ed25519.PrivateKey, tokenID string) (uint8, error) {
	switch tokenID {
	case setting.NearSetting.MainnetUSDTContractID:
		return uint8(setting.NearSetting.MainnetUSDTDecimals), nil

	case setting.NearSetting.MainnetUSDCContractID:
		return uint8(setting.NearSetting.MainnetUSDCDecimals), nil

	case setting.NearSetting.MainnetOpenEduContractID:
		return uint8(setting.NearSetting.MainnetOpenEduDecimals), nil

	case setting.NearSetting.TestnetUSDTContractID:
		return uint8(setting.NearSetting.TestnetUSDTDecimals), nil

	case setting.NearSetting.TestnetUSDCContractID:
		return uint8(setting.NearSetting.TestnetUSDCDecimals), nil

	case setting.NearSetting.TestnetOpenEduContractID:
		return uint8(setting.NearSetting.TestnetOpenEduDecimals), nil
	}

	ftMetadata, err := openedu_near_sdk.GetFtMetadataWithRetry(nodeURLs, accountID, privateKey, tokenID)
	if err != nil {
		return 0, err
	}

	return ftMetadata.Decimals, nil
}

func (s *NearLaunchpadService) getContractAndMethodForPledgeLaunchpad(req *launchpad.PledgeLaunchpadRequest) (string, string) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadPledgeMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadPledgeMethodName
	}
	return contractID, methodName
}

func (s *NearLaunchpadService) getArgsForPledgeLaunchpad(
	req *launchpad.PledgeLaunchpadRequest,
	contractID string,
	rawAmount decimal.Decimal,
) models.JSONB {
	return models.JSONB{
		"receiver_id": contractID,
		"amount":      rawAmount,
		"msg":         req.PoolID,
	}
}

func (s *NearLaunchpadService) getParamsForUpdatePoolFundingTime(req *launchpad.UpdatePoolFundingTimeRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadUpdatePoolFundingTimeMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadUpdatePoolFundingTimeMethodName
	}

	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id":           poolIdInt,
		"time_start_pledge": req.FundingStartDate * 1000_000,
		"time_end_pledge":   req.FundingEndDate * 1000_000,
	}
	return contractID, methodName, args
}

func (s *NearLaunchpadService) getParamsForCancelPool(req *launchpad.CancelPoolRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.MainnetLaunchpadCancelPoolMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadCancelPoolMethodName
	}

	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id": poolIdInt,
	}
	return contractID, methodName, args
}

func (s *NearLaunchpadService) getParamsForGetVotingPowers(req *launchpad.GetVotingPowersRequest) (string, string) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadGetUserRecordsByPoolMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadGetUserRecordsByPoolMethodName
	}
	return contractID, methodName
}

func (s *NearLaunchpadService) getParamsForGetPoolDetails(req *launchpad.GetPoolDetailsRequest) (string, string) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadGetPoolDetailsMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadGetPoolDetailsMethodName
	}
	return contractID, methodName
}

func (s *NearLaunchpadService) getParamsForCheckFundingResult(req *launchpad.CheckFundingRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadCheckFundingResultMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadCheckFundingResultMethodName
	}

	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id":            poolIdInt,
		"is_waiting_funding": req.IsWaitingFunding,
	}
	return contractID, methodName, args
}

func (s *NearLaunchpadService) getParamsForContinuePartialFund(req *launchpad.ContinueLpPartialFundRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadCreatorAcceptVotingMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadCreatorAcceptVotingMethodName
	}

	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id": poolIdInt,
		"approve": req.IsApproved,
	}
	return contractID, methodName, args
}

func (s *NearLaunchpadService) getParamsForSetPoolFundingTime(req *launchpad.SetPoolFundingTimeRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadSetFundingTimeMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadSetFundingTimeMethodName
	}

	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id":               poolIdInt,
		"time_start_pledge":     req.FundingStartDate * 1000_000,
		"funding_duration_days": req.FundingDurationDays * 24 * 60,
	}
	return contractID, methodName, args
}

func (s *NearLaunchpadService) getContractAndMethodForWithdrawToCreator(req *launchpad.WithdrawToCreatorRequest) (string, string) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadWithdrawToCreatorMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadApprovePoolMethodName
	}
	return contractID, methodName
}

func (s *NearLaunchpadService) getArgsForWithdrawToCreator(
	req *launchpad.WithdrawToCreatorRequest,
	rawAmount decimal.Decimal,
) models.JSONB {
	poolIdInt, _ := strconv.Atoi(req.PoolID)
	return models.JSONB{
		"pool_id": poolIdInt,
		"amount":  rawAmount,
	}
}

func (s *NearLaunchpadService) getParamsForClaimRefund(req *launchpad.ClaimRefundRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadClaimRefundMethodName
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadClaimRefundMethodName
	}

	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id": poolIdInt,
	}
	return contractID, methodName, args
}

func (s *NearLaunchpadService) getParamsForUpdatePoolStatus(req *launchpad.UpdatePoolStatusRequest) (string, string, models.JSONB) {
	contractID := setting.NearSetting.TestnetLaunchpadContractID
	methodName := setting.NearSetting.TestnetLaunchpadUpdatePoolStatusMethodName
	poolIdInt, _ := strconv.Atoi(req.PoolID)
	args := models.JSONB{
		"pool_id": poolIdInt,
		"status":  req.Status,
	}
	if req.IsMainnet {
		contractID = setting.NearSetting.MainnetLaunchpadContractID
		methodName = setting.NearSetting.MainnetLaunchpadUpdatePoolStatusMethodName
	}
	return contractID, methodName, args
}
