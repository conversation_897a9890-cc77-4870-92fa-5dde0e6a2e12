package strategies

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"github.com/ethereum/go-ethereum"
	"math/big"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/openedu_eth_sdk"
	"openedu-blockchain/pkg/openedu_eth_sdk/bindings"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/crypto_payment"
	"openedu-blockchain/services/crypto_transfer"
	"openedu-blockchain/services/launchpad"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

type EVMLaunchpadService struct {
}

func (s *EVMLaunchpadService) ClaimRefund(account launchpad.Account, req *launchpad.ClaimRefundRequest) (*launchpad.ClaimRefundResponse, error) {
	//TODO implement me
	panic("implement me")
}

// InitPool initializes a new launchpad pool
func (s *EVMLaunchpadService) InitPool(
	account launchpad.Account,
	req *launchpad.InitPoolRequest,
) (*launchpad.InitPoolResponse, error) {

	ctx := context.Background()
	result := &launchpad.InitPoolResponse{
		Token: req.Token,
	}

	accountAddr, accountPrivateKey, err := s.getAddressAndPrivateKey(account)
	if err != nil {
		return result, err
	}

	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Get token address
	tokenAddr, err := s.getTokenContractAddress(req.Token, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check token decimals
	tokenDecimals, err := s.getTokenDecimals(ethClient, tokenAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get token decimals: %w", err)
	}

	// Convert goal and min pledge amount to raw values
	goal := util.ParseReadableAmount2RawValue(req.TargetFunding, tokenDecimals)
	minPledgeAmount := util.ParseReadableAmount2RawValue(req.MinPledge, tokenDecimals)

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, accountAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, accountAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Check if balance is enough
	stakeAmount := util.ParseReadableAmount2RawValue(decimal.NewFromFloat(0.01), openedu_eth_sdk.ETHDecimalExp).BigInt()
	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
	totalRequired := new(big.Int).Add(estimatedGasCost, stakeAmount)
	if balance.Cmp(totalRequired) < 0 {
		return result, fmt.Errorf("%w: balance(%s) < required(%s)",
			launchpad.ErrInsufficientBalance, balance.String(), totalRequired.String())
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(accountPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = stakeAmount
	auth.GasLimit = openedu_eth_sdk.DefaultTransferGasLimit
	auth.GasPrice = gasPrice

	// Initialize launchpad
	tx, err := launchpadContract.InitLaunchpad(
		auth,
		req.LaunchpadID,
		accountAddr,
		tokenAddr,
		goal.BigInt(),
		minPledgeAmount.BigInt(),
	)
	if err != nil {
		return result, fmt.Errorf("%w: %w", launchpad.ErrInitPoolFailed, err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit
	result.Deposit = util.ParseRawValue2ReadableAmount(decimal.NewFromBigInt(stakeAmount, 0), openedu_eth_sdk.ETHDecimalExp)
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "initLaunchpad"
	result.InputData = models.JSONB{
		"launchpadId":     req.LaunchpadID,
		"launchpadOwner":  accountAddr.Hex(),
		"token":           tokenAddr.Hex(),
		"goal":            goal,
		"minPledgeAmount": minPledgeAmount,
	}

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed
		result.PoolID = req.LaunchpadID
	} else {
		failureMsg, eErr := openedu_eth_sdk.GetTransactionFailureMsg(ctx, ethClient, tx.Hash(), receipt)
		if eErr != nil || failureMsg == "" {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: status=%d, blockHash=%s, blockNumber=%s",
				receipt.Status, receipt.BlockHash.Hex(), receipt.BlockNumber.String())
		} else {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: %s", failureMsg)
		}

		result.Status = models.TxStatusFailed
		result.ErrorMsg = failureMsg
	}

	return result, nil
}

// ApprovePool approves a launchpad
func (s *EVMLaunchpadService) ApprovePool(
	req *launchpad.ApprovePoolRequest,
) (*launchpad.ApprovePoolStatusResponse, error) {

	ctx := context.Background()
	result := &launchpad.ApprovePoolStatusResponse{
		Token: models.TokenETH,
	}

	// Connect to ethClient
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get owner private key
	ownerPrivateKey, err := s.getLaunchpadOwnerPrivateKey(req.IsMainnet)
	if err != nil {
		return result, err
	}

	ownerAddr := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, ownerAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, ownerAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Check if balance is enough
	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
	if balance.Cmp(estimatedGasCost) < 0 {
		return result, fmt.Errorf("%w: balance(%s) < required(%s)",
			launchpad.ErrInsufficientBalance, balance.String(), estimatedGasCost.String())
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice

	// Update result
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "approveLaunchpad"
	result.InputData = models.JSONB{
		"launchpadId": req.PoolID,
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	// Approve or reject launchpad
	var tx *types.Transaction
	if req.IsApproved {
		tx, err = launchpadContract.ApproveLaunchpad(auth, req.PoolID)
	} else {
		tx, err = launchpadContract.RejectLaunchpad(auth, req.PoolID)
	}
	if err != nil {
		return result, fmt.Errorf("failed to approve/reject launchpad: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed
	} else {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction failed"
	}

	return result, nil
}

func (s *EVMLaunchpadService) UpdatePoolStatus(req *launchpad.UpdatePoolStatusRequest) (*launchpad.UpdatePoolStatusStatusResponse, error) {
	ctx := context.Background()
	result := &launchpad.UpdatePoolStatusStatusResponse{
		Token: models.TokenETH,
	}

	// Convert string status to uint8
	statusMap := map[string]uint8{
		"INIT":       0,
		"REJECTED":   1,
		"CANCELLED":  2,
		"APPROVED":   3,
		"FUNDING":    4,
		"FAILED":     5,
		"WAITING":    6,
		"REFUNDING":  7,
		"REFUNDED":   8,
		"VOTING":     9,
		"SUCCESSFUL": 10,
	}

	statusValue, ok := statusMap[req.Status]
	if !ok {
		return result, fmt.Errorf("invalid status: %s", req.Status)
	}

	// Connect to ethClient
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get owner private key
	ownerPrivateKey, err := s.getLaunchpadOwnerPrivateKey(req.IsMainnet)
	if err != nil {
		return result, err
	}

	ownerAddr := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, ownerAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, ownerAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Check if balance is enough
	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
	if balance.Cmp(estimatedGasCost) < 0 {
		return result, fmt.Errorf("%w: balance(%s) < required(%s)",
			launchpad.ErrInsufficientBalance, balance.String(), estimatedGasCost.String())
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice

	// Update result with transaction info
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "emergencyChangeStatus"
	result.InputData = models.JSONB{
		"launchpadId": req.PoolID,
		"status":      statusValue,
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	// Update pool status
	tx, err := launchpadContract.EmergencyChangeStatus(auth, req.PoolID, statusValue)
	if err != nil {
		return result, fmt.Errorf("failed to update pool status: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed
	} else {
		failureMsg, eErr := openedu_eth_sdk.GetTransactionFailureMsg(ctx, ethClient, tx.Hash(), receipt)
		if eErr != nil || failureMsg == "" {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: status=%d, blockHash=%s, blockNumber=%s",
				receipt.Status, receipt.BlockHash.Hex(), receipt.BlockNumber.String())
		} else {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: %s", failureMsg)
		}

		result.Status = models.TxStatusFailed
		result.ErrorMsg = failureMsg
	}

	return result, nil
}

func (s *EVMLaunchpadService) SetPoolFundingTime(
	account launchpad.Account,
	req *launchpad.SetPoolFundingTimeRequest,
) (*launchpad.SetPoolFundingTimeResponse, error) {

	ctx := context.Background()
	result := &launchpad.SetPoolFundingTimeResponse{
		Token: models.TokenETH,
	}

	accountAddr, accountPrivateKey, err := s.getAddressAndPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Connect to ethClient
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, accountAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, accountAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Check if balance is enough
	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
	if balance.Cmp(estimatedGasCost) < 0 {
		return result, fmt.Errorf("%w: balance(%s) < required(%s)",
			launchpad.ErrInsufficientBalance, balance.String(), estimatedGasCost.String())
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(accountPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice

	// Convert timestamps to big.Int
	startFundingTime := big.NewInt(req.FundingStartDate / 1000) // Unix epoch (seconds)

	durationSeconds := req.FundingDurationDays * 24 * 60 * 60                        // days to seconds
	endFundingTime := big.NewInt(req.FundingStartDate/1000 + int64(durationSeconds)) // Unix epoch (seconds)

	// Update result with transaction info
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "startFunding"
	result.InputData = models.JSONB{
		"launchpadId":      req.PoolID,
		"startFundingTime": startFundingTime.String(),
		"endFundingTime":   endFundingTime.String(),
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	// Set pool funding time
	tx, err := launchpadContract.StartFunding(auth, req.PoolID, startFundingTime, endFundingTime)
	if err != nil {
		return result, fmt.Errorf("failed to set pool funding time: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed
	} else {
		failureMsg, eErr := openedu_eth_sdk.GetTransactionFailureMsg(ctx, ethClient, tx.Hash(), receipt)
		if eErr != nil || failureMsg == "" {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: status=%d, blockHash=%s, blockNumber=%s",
				receipt.Status, receipt.BlockHash.Hex(), receipt.BlockNumber.String())
		} else {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: %s", failureMsg)
		}

		result.Status = models.TxStatusFailed
		result.ErrorMsg = failureMsg
	}

	return result, nil
}

func (s *EVMLaunchpadService) ContinueWithPartialFund(req *launchpad.ContinueLpPartialFundRequest) (*launchpad.ContinueLpPartialFundResponse, error) {
	ctx := context.Background()
	result := &launchpad.ContinueLpPartialFundResponse{
		Token: models.TokenETH,
	}

	// Connect to ethClient
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get owner private key
	ownerPrivateKey, err := s.getLaunchpadOwnerPrivateKey(req.IsMainnet)
	if err != nil {
		return result, err
	}

	ownerAddr := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, ownerAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, ownerAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Check if balance is enough
	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
	if balance.Cmp(estimatedGasCost) < 0 {
		return result, fmt.Errorf("%w: balance(%s) < required(%s)",
			launchpad.ErrInsufficientBalance, balance.String(), estimatedGasCost.String())
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = openedu_eth_sdk.DefaultGasLimit
	auth.GasPrice = gasPrice

	// Update result with transaction info
	result.ContractID = launchpadAddress.Hex()
	result.InputData = models.JSONB{
		"launchpadId": req.PoolID,
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	var tx *types.Transaction
	if req.IsApproved {
		// If approved, accept funding
		result.MethodName = "acceptFunding"
		tx, err = launchpadContract.AcceptFunding(auth, req.PoolID)
	} else {
		// If not approved, cancel launchpad
		result.MethodName = "cancelLaunchpad"
		tx, err = launchpadContract.CancelLaunchpad(auth, req.PoolID)
	}

	if err != nil {
		return result, fmt.Errorf("failed to continue with partial fund: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed

		// Try to get updated pool status
		launchpad, err := launchpadContract.GetLaunchpad(&bind.CallOpts{}, req.PoolID)
		if err == nil {
			switch launchpad.Status {
			case 0:
				result.PoolStatus = "INIT"
			case 1:
				result.PoolStatus = "REJECTED"
			case 2:
				result.PoolStatus = "CANCELLED"
			case 3:
				result.PoolStatus = "APPROVED"
			case 4:
				result.PoolStatus = "FUNDING"
			case 5:
				result.PoolStatus = "FAILED"
			case 6:
				result.PoolStatus = "WAITING"
			case 7:
				result.PoolStatus = "REFUNDING"
			case 8:
				result.PoolStatus = "REFUNDED"
			case 9:
				result.PoolStatus = "VOTING"
			case 10:
				result.PoolStatus = "SUCCESSFUL"
			default:
				result.PoolStatus = fmt.Sprintf("UNKNOWN(%d)", launchpad.Status)
			}
		}
	} else {
		failureMsg, eErr := openedu_eth_sdk.GetTransactionFailureMsg(ctx, ethClient, tx.Hash(), receipt)
		if eErr != nil || failureMsg == "" {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: status=%d, blockHash=%s, blockNumber=%s",
				receipt.Status, receipt.BlockHash.Hex(), receipt.BlockNumber.String())
		} else {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: %s", failureMsg)
		}

		result.Status = models.TxStatusFailed
		result.ErrorMsg = failureMsg
	}

	return result, nil
}

// Pledge adds funds to a launchpad pool
func (s *EVMLaunchpadService) Pledge(
	account launchpad.Account,
	req *launchpad.PledgeLaunchpadRequest,
) (*launchpad.PledgeLaunchpadResponse, error) {

	ctx := context.Background()
	result := &launchpad.PledgeLaunchpadResponse{
		Token:   req.Token,
		Deposit: req.Amount,
	}

	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	accountAddr, accountPrivateKey, err := s.getAddressAndPrivateKey(account)
	if err != nil {
		return result, err
	}

	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Get token address
	tokenAddr, tokenContract, err := s.getTokenInstance(ethClient, req.Token, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, accountAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, accountAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(accountPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice

	// Get token decimals
	tokenDecimals, err := s.getTokenDecimals(ethClient, tokenAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get token decimals: %w", err)
	}

	// Convert amount to raw value
	amount := util.ParseReadableAmount2RawValue(req.Amount, tokenDecimals)

	// Update result
	result.ContractID = launchpadAddress.Hex()
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	var tx *types.Transaction

	// Check if this is a native token (ETH) or ERC20
	if req.Token == models.TokenETH {
		// For native ETH, we use pledgeNative
		result.MethodName = "pledgeNative"
		result.InputData = models.JSONB{
			"launchpadId": req.PoolID,
		}

		// Check balance for native token pledge
		totalRequired := new(big.Int).Add(
			new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit)),
			amount.BigInt(),
		)
		if balance.Cmp(totalRequired) < 0 {
			return result, fmt.Errorf("%w: balance(%s) < required(%s)",
				launchpad.ErrInsufficientBalance, balance.String(), totalRequired.String())
		}

		auth.Value = amount.BigInt()
		tx, err = launchpadContract.PledgeNative(auth, req.PoolID)
	} else {
		// For ERC20 tokens, we need to check allowance and use pledgeERC20
		result.MethodName = "pledgeERC20"
		result.InputData = models.JSONB{
			"launchpadId": req.PoolID,
			"amount":      amount,
		}

		// Check gas fee is enough
		estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
		if balance.Cmp(estimatedGasCost) < 0 {
			return result, fmt.Errorf("%w: insufficient gas fee: balance(%s) gas(%s)",
				launchpad.ErrInsufficientGasFee, balance.String(), estimatedGasCost.String())
		}

		// Check token balance
		tokenBalance, tErr := s.getTokenBalance(ethClient, tokenAddr, accountAddr)
		if tErr != nil {
			return result, fmt.Errorf("failed to get token balance: %w", tErr)
		}

		if tokenBalance.Cmp(amount.BigInt()) < 0 {
			return result, fmt.Errorf("%w: token balance(%s) < amount(%s)",
				launchpad.ErrInsufficientBalance, tokenBalance.String(), amount.String())
		}

		// Check and set allowance if needed
		aErr := s.checkAndSetAllowance(ethClient, accountPrivateKey, tokenContract, launchpadAddress, accountAddr, amount.BigInt(), req.IsMainnet)
		if aErr != nil {
			return result, fmt.Errorf("failed to set token allowance: %w", aErr)
		}

		auth.Value = big.NewInt(0)

		nonce, err = ethClient.PendingNonceAt(ctx, accountAddr)
		if err != nil {
			return result, fmt.Errorf("không thể lấy nonce: %w", err)
		}
		auth.Nonce = big.NewInt(int64(nonce))
		tx, err = launchpadContract.PledgeERC20(auth, req.PoolID, amount.BigInt())
	}

	if err != nil {
		return result, fmt.Errorf("failed to pledge: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed
	} else {
		failureMsg, eErr := openedu_eth_sdk.GetTransactionFailureMsg(ctx, ethClient, tx.Hash(), receipt)
		if eErr != nil || failureMsg == "" {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: status=%d, blockHash=%s, blockNumber=%s",
				receipt.Status, receipt.BlockHash.Hex(), receipt.BlockNumber.String())
		} else {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: %s", failureMsg)
		}

		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction failed"
	}

	return result, nil
}

// UpdatePoolFundingTime updates the funding time window for a launchpad pool
func (s *EVMLaunchpadService) UpdatePoolFundingTime(
	req *launchpad.UpdatePoolFundingTimeRequest,
) (*launchpad.UpdatePoolFundingTimeResponse, error) {

	ctx := context.Background()
	result := &launchpad.UpdatePoolFundingTimeResponse{
		Token: models.TokenETH,
	}

	// Get admin private key
	ownerPrivateKey, err := s.getLaunchpadOwnerPrivateKey(req.IsMainnet)
	if err != nil {
		return result, err
	}
	ownerAddr := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)

	// Connect to client
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, ownerAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = openedu_eth_sdk.DefaultGasLimit
	auth.GasPrice = gasPrice

	// Update result
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "startFunding"
	result.InputData = models.JSONB{
		"launchpadId":      req.PoolID,
		"startFundingTime": big.NewInt(req.FundingStartDate),
		"endFundingTime":   big.NewInt(req.FundingEndDate),
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	// Update funding time
	tx, err := launchpadContract.StartFunding(
		auth,
		req.PoolID,
		big.NewInt(req.FundingStartDate),
		big.NewInt(req.FundingEndDate),
	)
	if err != nil {
		return result, fmt.Errorf("failed to update funding time: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed
	} else {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction failed"
	}

	return result, nil
}

// CancelPool cancels a launchpad pool
func (s *EVMLaunchpadService) CancelPool(
	account launchpad.Account,
	req *launchpad.CancelPoolRequest,
) (*launchpad.CancelPoolResponse, error) {

	ctx := context.Background()
	result := &launchpad.CancelPoolResponse{
		Token: models.TokenETH,
	}

	// Get private key
	accountAddr, accountPrivateKey, err := s.getAddressAndPrivateKey(account)
	if err != nil {
		return result, err
	}

	// Connect to client
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, accountAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, accountAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Check if balance is enough for gas fee
	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
	if balance.Cmp(estimatedGasCost) < 0 {
		return result, fmt.Errorf("%w: balance(%s) < required(%s)",
			launchpad.ErrInsufficientGasFee, balance.String(), estimatedGasCost.String())
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(accountPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice

	// Update result
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "cancelLaunchpad"
	result.InputData = models.JSONB{
		"launchpadId": req.PoolID,
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	// Cancel pool
	tx, err := launchpadContract.CancelLaunchpad(auth, req.PoolID)
	if err != nil {
		return result, fmt.Errorf("failed to cancel launchpad: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed
	} else {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction failed"
	}

	return result, nil
}

// GetVotingPowers gets voting powers for a pool
func (s *EVMLaunchpadService) GetVotingPowers(
	req *launchpad.GetVotingPowersRequest,
) (*launchpad.GetVotingPowersResponse, error) {

	panic("implement me")
	//// Connect to client
	//ethClient, err := s.getETHClient(req.IsMainnet)
	//if err != nil {
	//	return nil, fmt.Errorf("failed to connect to client: %w", err)
	//}
	//
	//// Get launchpad contract address and instance
	//launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	//if err != nil {
	//	return nil, err
	//}
	//
	//// Get pool details
	//poolDetails, err := s.GetPoolDetails(&launchpad.GetPoolDetailsRequest{
	//	PoolID:    req.PoolID,
	//	Network:   req.Network,
	//	IsMainnet: req.IsMainnet,
	//})
	//if err != nil {
	//	return nil, err
	//}
	//
	//return &launchpad.GetVotingPowersResponse{
	//	VotingPowers: []*launchpad.VotingPowerEntry{
	//		{
	//			Address:     poolDetails.CreatorId,
	//			Amount:      decimal.NewFromInt(poolDetails.TotalBalance),
	//			TotalAmount: decimal.NewFromInt(poolDetails.TotalBalance),
	//			VotingPower: 100, // Placeholder, in a real implementation this would be calculated
	//		},
	//	},
	//}, nil
}

// GetPoolDetails gets details of a launchpad pool
func (s *EVMLaunchpadService) GetPoolDetails(
	req *launchpad.GetPoolDetailsRequest,
) (*launchpad.GetPoolDetailsResponse, error) {

	ctx := context.Background()
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to client: %w", err)
	}

	_, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return nil, err
	}

	// Create call options
	callOpts := &bind.CallOpts{
		Context: ctx,
	}

	// Call getLaunchpad method
	launchpadDetails, err := launchpadContract.GetLaunchpad(callOpts, req.PoolID)
	if err != nil {
		return nil, fmt.Errorf("failed to get launchpad details: %w", err)
	}

	poolIdInt, _ := strconv.Atoi(req.PoolID)
	return &launchpad.GetPoolDetailsResponse{
		PoolId:            int64(poolIdInt),
		CreatorId:         launchpadDetails.Owner.Hex(),
		TokenId:           launchpadDetails.Token.Hex(),
		TargetFunding:     launchpadDetails.Goal.Int64(),
		TotalBalance:      launchpadDetails.TotalPledged.Int64(),
		Status:            fmt.Sprintf("%d", launchpadDetails.Status), // TODO
		StakingAmount:     decimal.NewFromInt(launchpadDetails.StakeAmount.Int64()),
		MinMultiplePledge: launchpadDetails.MinPledgeAmount.Int64(),
		TimeStartPledge:   launchpadDetails.StartFundingTime.Int64(),
		TimeEndPledge:     launchpadDetails.EndFundingTime.Int64(),
	}, nil
}

// CheckFundingResult checks the funding result of a pool
func (s *EVMLaunchpadService) CheckFundingResult(
	req *launchpad.CheckFundingRequest,
) (*launchpad.CheckFundingResponse, error) {

	ctx := context.Background()
	result := &launchpad.CheckFundingResponse{
		Token: models.TokenETH,
	}

	// Get owner private key
	ownerPrivateKey, err := s.getLaunchpadOwnerPrivateKey(req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Connect to client
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Get admin address
	adminAddr := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, adminAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice

	// Update result
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "endFundingResult"
	result.InputData = models.JSONB{
		"launchpadId": req.PoolID,
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit

	// End funding result
	tx, err := launchpadContract.EndFundingResult(auth, req.PoolID)
	if err != nil {
		return result, fmt.Errorf("failed to check funding result: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed

		// Get pool details for status
		poolDetails, err := s.GetPoolDetails(&launchpad.GetPoolDetailsRequest{
			PoolID:    req.PoolID,
			IsMainnet: req.IsMainnet,
		})
		if err == nil {
			result.PoolStatus = poolDetails.Status
		}
	} else {
		result.Status = models.TxStatusFailed
		result.ErrorMsg = "Transaction failed"
	}

	return result, nil
}

func (s *EVMLaunchpadService) WithdrawToCreator(req *launchpad.WithdrawToCreatorRequest) (*launchpad.WithdrawToCreatorResponse, error) {
	ctx := context.Background()
	result := &launchpad.WithdrawToCreatorResponse{
		Token: req.Token,
	}

	// Connect to ethClient
	ethClient, err := s.getETHClient(req.IsMainnet)
	if err != nil {
		return result, fmt.Errorf("failed to connect to client: %w", err)
	}

	// Get owner private key
	ownerPrivateKey, err := s.getLaunchpadOwnerPrivateKey(req.IsMainnet)
	if err != nil {
		return result, err
	}

	ownerAddr := crypto.PubkeyToAddress(ownerPrivateKey.PublicKey)

	// Get launchpad contract address and instance
	launchpadAddress, launchpadContract, err := s.getLaunchpadContractInstance(ethClient, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Get token address
	tokenAddr, err := s.getTokenContractAddress(req.Token, req.IsMainnet)
	if err != nil {
		return result, err
	}

	// Check token decimals
	tokenDecimals, err := s.getTokenDecimals(ethClient, tokenAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get token decimals: %w", err)
	}

	// Check nonce and balance
	nonce, err := ethClient.PendingNonceAt(ctx, ownerAddr)
	if err != nil {
		return result, fmt.Errorf("failed to get account nonce: %w", err)
	}

	balance, err := ethClient.BalanceAt(ctx, ownerAddr, nil)
	if err != nil {
		return result, fmt.Errorf("failed to get account balance: %w", err)
	}

	// Get gas price
	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Check if balance is enough for gas
	estimatedGasCost := new(big.Int).Mul(gasPrice, big.NewInt(openedu_eth_sdk.DefaultGasLimit))
	if balance.Cmp(estimatedGasCost) < 0 {
		return result, fmt.Errorf("%w: balance(%s) < required(%s)",
			launchpad.ErrInsufficientBalance, balance.String(), estimatedGasCost.String())
	}

	// Create auth object
	auth, err := bind.NewKeyedTransactorWithChainID(ownerPrivateKey, s.getChainID(req.IsMainnet))
	if err != nil {
		return result, fmt.Errorf("failed to create transactor: %w", err)
	}
	auth.Nonce = big.NewInt(int64(nonce))
	auth.Value = big.NewInt(0)
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice

	// Update result with transaction info
	result.ContractID = launchpadAddress.Hex()
	result.MethodName = "claimFunding"
	result.InputData = models.JSONB{
		"launchpadId": req.PoolID,
	}
	result.GasLimit = openedu_eth_sdk.DefaultGasLimit
	result.Deposit = req.Amount

	// Withdraw to creator
	tx, err := launchpadContract.ClaimFunding(auth, req.PoolID)
	if err != nil {
		return result, fmt.Errorf("failed to withdraw to creator: %w", err)
	}

	// Update result with transaction info
	result.TxHash = tx.Hash().Hex()
	result.Nonce = nonce

	// Wait for transaction receipt
	receiptCtx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Update status based on receipt
	if receipt.Status == types.ReceiptStatusSuccessful {
		result.Status = models.TxStatusSuccess
		result.BlockHash = receipt.BlockHash.Hex()
		result.GasBurnt = receipt.GasUsed

		// Check if there's a FundingAction event for this transaction
		for _, eventLog := range receipt.Logs {
			// Parse FundingAction event if it's from our contract
			if eventLog.Address == launchpadAddress {
				event, err := launchpadContract.ParseFundingAction(*eventLog)
				if err == nil && event.ActionType == "claim" {
					// If we found a claim event for this pool, update the amount
					poolIdHash := crypto.Keccak256Hash([]byte(req.PoolID))
					if event.LaunchpadId == poolIdHash {
						result.Deposit = util.ParseRawValue2ReadableAmount(decimal.NewFromBigInt(event.Amount, 0), tokenDecimals)
						break
					}
				}
			}
		}
	} else {
		failureMsg, eErr := openedu_eth_sdk.GetTransactionFailureMsg(ctx, ethClient, tx.Hash(), receipt)
		if eErr != nil || failureMsg == "" {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: status=%d, blockHash=%s, blockNumber=%s",
				receipt.Status, receipt.BlockHash.Hex(), receipt.BlockNumber.String())
		} else {
			result.ErrorMsg = fmt.Sprintf("transaction execution failed: %s", failureMsg)
		}

		result.Status = models.TxStatusFailed
		result.ErrorMsg = failureMsg
	}

	return result, nil
}

// getETHClient creates a new Ethereum client
func (s *EVMLaunchpadService) getETHClient(isMainnet bool) (*ethclient.Client, error) {
	nodeURLs := s.getNodeURLs(isMainnet)
	client, err := ethclient.Dial(nodeURLs[0])
	if err == nil {
		return client, nil
	}
	return nil, e.Wrap(crypto_payment.ErrConnectRPCFailed, fmt.Errorf("connect to node %s failed: %w", nodeURLs[0], err))
}

// getNodeURLs returns the appropriate node URLs based on network
func (s *EVMLaunchpadService) getNodeURLs(isMainnet bool) []string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetURLs).
		Else(setting.EvmSetting.TestnetURLs)
}

func (s *EVMLaunchpadService) getChainID(isMainnet bool) *big.Int {
	str := lo.If(isMainnet, setting.EvmSetting.MainnetChainID).
		Else(setting.EvmSetting.TestnetChainID)
	bigInt := new(big.Int)
	bigInt, _ = bigInt.SetString(str, 10)
	return bigInt
}

func (s *EVMLaunchpadService) getLaunchpadContractInstance(
	client *ethclient.Client,
	isMainnet bool,
) (common.Address, *bindings.CourseLaunchpad, error) {

	addressHex := s.getLaunchpadContractAddressHex(isMainnet)
	address, err := openedu_eth_sdk.ParseAddressFromHex(addressHex)
	if err != nil {
		return common.Address{}, nil, fmt.Errorf("failed to parse launchpad contract address: %w", err)
	}

	instance, err := bindings.NewCourseLaunchpad(address, client)
	if err != nil {
		return common.Address{}, nil, fmt.Errorf("failed to instantiate contract: %w", err)
	}
	return address, instance, nil
}

func (s *EVMLaunchpadService) getLaunchpadContractAddressHex(isMainnet bool) string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetLaunchpadContractAddress).
		Else(setting.EvmSetting.TestnetLaunchpadContractAddress)
}

func (s *EVMLaunchpadService) getLaunchpadOwnerPrivateKey(isMainnet bool) (*ecdsa.PrivateKey, error) {
	privateKeyStr := lo.If(isMainnet, setting.EvmSetting.MainnetLaunchpadOwnerPrivateKey).
		Else(setting.EvmSetting.TestnetLaunchpadOwnerPrivateKey)

	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("parse owner private key failed: %w", err))
	}

	return privateKey, nil
}

// getAddressAndPrivateKey gets the private key from an account
func (s *EVMLaunchpadService) getAddressAndPrivateKey(account crypto_payment.Account) (common.Address, *ecdsa.PrivateKey, error) {
	privateKeyStr, err := account.GetPrivateKey()
	if err != nil {
		return common.Address{}, nil, e.Wrap(crypto_payment.ErrGetPrivateKeyFailed, err)
	}

	// Remove '0x' prefix if exists
	if len(privateKeyStr) > 2 && privateKeyStr[:2] == "0x" {
		privateKeyStr = privateKeyStr[2:]
	}

	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return common.Address{}, nil, e.Wrap(crypto_transfer.ErrGetAccountStateFailed, fmt.Errorf("parse account private key failed: %w", err))
	}

	accountAddr := common.HexToAddress(account.GetAddress())
	return accountAddr, privateKey, nil
}

func (s *EVMLaunchpadService) getTokenInstance(
	ethClient *ethclient.Client,
	token models.BlockchainToken,
	isMainnet bool,
) (common.Address, openedu_eth_sdk.ERC20TokenContract, error) {

	tokenAddr, err := s.getTokenContractAddress(token, isMainnet)
	if err != nil {
		return common.Address{}, nil, err
	}

	var tokenInstance openedu_eth_sdk.ERC20TokenContract
	switch token {
	case models.TokenUSDC:
		tokenInstance, err = bindings.NewUSDC(tokenAddr, ethClient)
		if err != nil {
			return common.Address{}, nil, fmt.Errorf("instantiate token contract failed: %w", err)
		}

	case models.TokenETH:
		// ETH is native token. So do not have token contract instant

	default:
		return common.Address{}, nil, crypto_payment.ErrUnsupportedToken
	}

	return tokenAddr, tokenInstance, nil
}

// getTokenContractAddress returns the contract address for a given token symbol
func (s *EVMLaunchpadService) getTokenContractAddress(token models.BlockchainToken, isMainnet bool) (common.Address, error) {
	var tokenAddressHex string

	switch token {
	case models.TokenETH:
		tokenAddressHex = openedu_eth_sdk.ETHAddressHex

	case models.TokenUSDC:
		tokenAddressHex = lo.If(isMainnet, setting.EvmSetting.MainnetUSDCContractAddress).Else(setting.EvmSetting.TestnetUSDCContractAddress)
	}

	if tokenAddressHex == "" {
		return common.Address{}, crypto_transfer.ErrUnsupportedToken
	}

	tokenAddress, err := openedu_eth_sdk.ParseAddressFromHex(tokenAddressHex)
	if err != nil {
		return common.Address{}, fmt.Errorf("failed to get token contract address: %w", err)
	}

	return tokenAddress, nil
}

// getTokenDecimals gets the number of decimals for a token
func (s *EVMLaunchpadService) getTokenDecimals(client *ethclient.Client, tokenAddress common.Address) (uint8, error) {
	// Check if tokenAddress is ETH
	if tokenAddress.Hex() == openedu_eth_sdk.ETHAddressHex {
		return openedu_eth_sdk.ETHDecimalExp, nil
	}

	// Get ERC20 token's decimals by
	data := []byte("decimals()")
	hash := crypto.Keccak256(data)[:4]
	result, err := client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &tokenAddress,
		Data: hash,
	}, nil) // nil for latest block
	if err != nil {
		return 0, err
	}

	// Some older tokens might not have the decimals function
	if len(result) == 0 {
		return openedu_eth_sdk.ETHDecimalExp, nil // Default to 18 as most tokens follow ETH
	}

	// Convert the result to uint8
	decimals := uint8(new(big.Int).SetBytes(result).Uint64())
	return decimals, nil
}

func (s *EVMLaunchpadService) getPaymentOwnerPrivateKey(isMainnet bool) string {
	return lo.If(isMainnet, setting.EvmSetting.MainnetLaunchpadOwnerPrivateKey).
		Else(setting.EvmSetting.TestnetLaunchpadOwnerPrivateKey)
}

// getTokenBalance gets the token balance for an address
func (s *EVMLaunchpadService) getTokenBalance(
	client *ethclient.Client,
	tokenAddress common.Address,
	accountAddress common.Address,
) (*big.Int, error) {
	// ERC20 balanceOf(address) function signature
	data := []byte("balanceOf(address)")
	hash := crypto.Keccak256(data)[:4]

	// Pad the address parameter to 32 bytes
	paddedAddress := common.LeftPadBytes(accountAddress.Bytes(), 32)

	// Concatenate function selector and address parameter
	var callData []byte
	callData = append(callData, hash...)
	callData = append(callData, paddedAddress...)

	// Call the balanceOf method on the token contract
	result, err := client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &tokenAddress,
		Data: callData,
	}, nil) // nil for latest block

	if err != nil {
		return nil, err
	}

	// Convert the result to big.Int
	return new(big.Int).SetBytes(result), nil
}

// checkAndSetAllowance checks if the spender has enough allowance to spend tokens
// and approves more if needed
func (s *EVMLaunchpadService) checkAndSetAllowance(
	ethClient *ethclient.Client,
	privateKey *ecdsa.PrivateKey,
	tokenContract openedu_eth_sdk.ERC20TokenContract,
	spenderAddress common.Address,
	ownerAddress common.Address,
	amount *big.Int,
	isMainnet bool,
) error {
	ctx := context.Background()

	// Check if current allowance is enough
	callOpts := &bind.CallOpts{
		Context: ctx,
		From:    ownerAddress,
	}
	currentAllowance, err := tokenContract.Allowance(callOpts, ownerAddress, spenderAddress)
	if err != nil {
		return fmt.Errorf("không thể kiểm tra allowance: %w", err)
	}

	if currentAllowance.Cmp(amount) >= 0 {
		return nil
	}

	// Create auth for allowance transaction
	auth, err := bind.NewKeyedTransactorWithChainID(privateKey, s.getChainID(isMainnet))
	if err != nil {
		return fmt.Errorf("failed to create transactor: %w", err)
	}

	// Get nonce and gas price
	nonce, err := ethClient.PendingNonceAt(ctx, ownerAddress)
	if err != nil {
		return fmt.Errorf("failed to get nonce: %w", err)
	}

	gasPrice, err := ethClient.SuggestGasPrice(ctx)
	if err != nil {
		return fmt.Errorf("failed to get gas price: %w", err)
	}

	auth.Nonce = big.NewInt(int64(nonce))
	auth.GasLimit = uint64(openedu_eth_sdk.DefaultGasLimit)
	auth.GasPrice = gasPrice
	auth.Value = big.NewInt(0) // Always set to 0 for ERC20 approvals

	// Approve maximum uint256 value to avoid frequent approvals
	tx, err := tokenContract.Approve(auth, spenderAddress, amount)
	if err != nil {
		return fmt.Errorf("failed to approve tokens: %w", err)
	}

	// Wait for transaction to be mined
	receiptCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
	defer cancel()
	receipt, err := bind.WaitMined(receiptCtx, ethClient, tx)
	if err != nil {
		return fmt.Errorf("failed to mine approval transaction: %w", err)
	}

	if receipt.Status != types.ReceiptStatusSuccessful {
		failureMsg, eErr := openedu_eth_sdk.GetTransactionFailureMsg(ctx, ethClient, tx.Hash(), receipt)
		if eErr != nil || failureMsg == "" {
			return fmt.Errorf("approval transaction failed: status=%d", receipt.Status)
		} else {
			return fmt.Errorf("approval transaction failed: %s", failureMsg)
		}
	}

	return nil
}