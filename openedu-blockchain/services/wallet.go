package services

import (
	"crypto/ecdsa"
	"crypto/ed25519"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/availproject/avail-go-sdk/src/sdk"
	"github.com/cosmos/go-bip39"
	"github.com/ethereum/go-ethereum/accounts"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/shopspring/decimal"
	"github.com/tyler-smith/go-bip32"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/kms"
	"openedu-blockchain/pkg/openedu_eth_sdk"
	"openedu-blockchain/pkg/openedu_near_sdk"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/crypto_transfer"
)

type WalletService struct {
	transferService *crypto_transfer.TransferService
}

type WalletServiceDeps struct {
	TransferService *crypto_transfer.TransferService
}

func NewWalletService(deps *WalletServiceDeps) *WalletService {
	return &WalletService{
		transferService: deps.TransferService,
	}
}

func (s *WalletService) Create(req *dto.WalletRequest) (*models.Wallet, *e.AppError) {
	userSetting, err := models.Repository.UserSetting.FindOne(&models.UserSettingQuery{
		UserID: &req.UserID,
	}, nil)
	if err != nil && !models.IsRecordNotFound(err) {
		return nil, e.NewError500(e.UserSettingFindFailed, "Find user setting error: "+err.Error())
	}

	if userSetting == nil {
		var appErr *e.AppError
		userSetting, appErr = UserSetting.Create(&models.UserSetting{
			UserID: req.UserID,
		})

		if appErr != nil {
			return nil, appErr
		}
	}

	var wallet *models.Wallet
	var appErr *e.AppError
	switch req.Network {
	case models.NetworkNEAR:
		wallet, appErr = s.createNearWallet(userSetting)

	case models.NetworkAVAIL:
		wallet, appErr = s.createAvailWallet(userSetting)

	case models.NetworkBASE:
		wallet, appErr = s.createEthWallet(userSetting)

	default:
		return nil, e.NewError400(e.InvalidParams, "Invalid blockchain network: "+string(req.Network))
	}

	if appErr != nil {
		return nil, appErr
	}

	wallet.CoreWalletID = req.CoreWalletID
	if err = models.Repository.Wallet.Create(wallet, nil); err != nil {
		return nil, e.NewError500(e.Error, "Create wallet error: "+err.Error())
	}
	return wallet, nil
}

func (s *WalletService) createNearWallet(setting *models.UserSetting) (*models.Wallet, *e.AppError) {
	seedPhrase, secret, err := getSeedPhraseAndSecret(setting)
	if err != nil {
		return nil, e.NewError500(e.Error, "Seed phrase and secret decryption error: "+err.Error())
	}

	nearAccount, err := openedu_near_sdk.CreateImplicitAccount(seedPhrase, secret)
	if err != nil {
		return nil, e.NewError500(e.Error, "Create near implicit account error: "+err.Error())
	}

	encryptedPrivateKey, err := kms.Encrypt(nearAccount.PrivateKey)
	if err != nil {
		return nil, e.NewError500(e.Error, "Encrypt private key error: "+err.Error())
	}

	return &models.Wallet{
		UserID:              setting.UserID,
		Address:             nearAccount.AccountID,
		PublicKey:           nearAccount.PublicKey,
		EncryptedPrivateKey: encryptedPrivateKey,
		Network:             models.NetworkNEAR,
		Status:              models.WalletStatusActive,
	}, nil
}

func (s *WalletService) createAvailWallet(setting *models.UserSetting) (*models.Wallet, *e.AppError) {
	seedPhrase, _, err := getSeedPhraseAndSecret(setting)
	if err != nil {
		return nil, e.NewError500(e.Error, "Seed phrase and secret decryption error: "+err.Error())
	}

	keyringPair, err := sdk.KeyringPairFromSecret(seedPhrase, 42)
	if err != nil {
		return nil, e.NewError500(e.Error, "Generate keyring pair from secret error: "+err.Error())
	}

	// AVAIL SDK uses seed phrase instead of private key
	encryptedPrivateKey, err := kms.Encrypt(seedPhrase)
	if err != nil {
		return nil, e.NewError500(e.Error, "Encrypt private key error: "+err.Error())
	}

	return &models.Wallet{
		UserID:              setting.UserID,
		Address:             keyringPair.Address,
		PublicKey:           hex.EncodeToString(keyringPair.PublicKey),
		EncryptedPrivateKey: encryptedPrivateKey,
		Network:             models.NetworkAVAIL,
		Status:              models.WalletStatusActive,
	}, nil
}

func (s *WalletService) createEthWallet(setting *models.UserSetting) (*models.Wallet, *e.AppError) {
	seedPhrase, _, err := getSeedPhraseAndSecret(setting)
	if err != nil {
		return nil, e.NewError500(e.Error, "Seed phrase and secret decryption error: "+err.Error())
	}

	seed, err := bip39.NewSeedWithErrorChecking(seedPhrase, "")
	if err != nil {
		return nil, e.NewError500(e.Error, "Generate seed error: "+err.Error())
	}

	masterKey, err := bip32.NewMasterKey(seed)
	if err != nil {
		return nil, e.NewError500(e.Error, "Generate master key error: "+err.Error())
	}

	derivationPath, err := accounts.ParseDerivationPath(openedu_eth_sdk.ETHDerivationPath)
	if err != nil {
		return nil, e.NewError500(e.Error, "Parse derivation path error: "+err.Error())
	}

	key := masterKey
	for _, index := range derivationPath {
		childKey, cErr := key.NewChildKey(index)
		if cErr != nil {
			return nil, e.NewError500(e.Error, "Derive child key error: "+cErr.Error())
		}
		key = childKey
	}

	privateKey, err := crypto.ToECDSA(key.Key)
	if err != nil {
		return nil, e.NewError500(e.Error, "Convert to ECDSA error: "+err.Error())
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return nil, e.NewError500(e.Error, "Cannot get public key")
	}

	address := crypto.PubkeyToAddress(*publicKeyECDSA).Hex()

	publicKeyBytes := crypto.FromECDSAPub(publicKeyECDSA)
	publicKeyHex := hexutil.Encode(publicKeyBytes)

	privateKeyBytes := crypto.FromECDSA(privateKey)
	privateKeyHex := hexutil.Encode(privateKeyBytes)

	encryptedPrivateKey, err := kms.Encrypt(privateKeyHex)
	if err != nil {
		return nil, e.NewError500(e.Error, "Encrypt private key error: "+err.Error())
	}

	return &models.Wallet{
		UserID:              setting.UserID,
		Address:             address,
		PublicKey:           publicKeyHex,
		EncryptedPrivateKey: encryptedPrivateKey,
		Network:             models.NetworkBASE,
		Status:              models.WalletStatusActive,
	}, nil
}

func getSeedPhraseAndSecret(setting *models.UserSetting) (string, string, error) {
	secret, err := kms.Decrypt(setting.EncryptedSecret)
	if err != nil {
		return "", "", fmt.Errorf("failed to decrypt secret: %w", err)
	}

	seedPhrase, err := kms.Decrypt(setting.EncryptedSeedPhrase)
	if err != nil {
		return "", "", fmt.Errorf("failed to decrypt seed phrase: %w", err)
	}

	return seedPhrase, secret, nil
}

func (s *WalletService) FindPage(query *models.WalletQuery, options *models.FindPageOptions) ([]*models.Wallet, *models.Pagination, *e.AppError) {
	wallets, pagination, err := models.Repository.Wallet.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Error, "Find wallets error: "+err.Error())
	}
	return wallets, pagination, nil
}

func (s *WalletService) FindByID(id string, options *models.FindOneOptions) (*models.Wallet, *e.AppError) {
	wallet, err := models.Repository.Wallet.FindByID(id, options)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.WalletNotFound, "Wallet not found")
		}
		return nil, e.NewError500(e.Error, "Find wallet by ID error: "+err.Error())
	}
	return wallet, nil
}

func (s *WalletService) GetGasSponsorBalance(req *dto.GetWalletGasSponsorBalanceRequest) (decimal.Decimal, *e.AppError) {
	wallet, err := models.Repository.Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if err != nil {
		if models.IsRecordNotFound(err) {
			return decimal.Zero, e.NewError404(e.WalletNotFound, "Wallet not found")
		}
		return decimal.Zero, e.NewError500(e.WalletGetGasSponsorFailed, "Find the wallet by ID error: "+err.Error())
	}

	switch wallet.Network {
	case models.NetworkNEAR:
		return s.getNearGasSponsorBalance(wallet, req)
	case models.NetworkBASE:
		return s.getBaseGasSponsorBalance(wallet, req)
	default:
		return decimal.Zero, nil
	}
}

func (s *WalletService) getNearGasSponsorBalance(wallet *models.Wallet, req *dto.GetWalletGasSponsorBalanceRequest) (decimal.Decimal, *e.AppError) {
	privateKeyStr, err := kms.Decrypt(wallet.EncryptedPrivateKey)
	if err != nil {
		return decimal.Zero, e.NewError500(e.WalletGetGasSponsorFailed, "Decrypt private key failed: "+err.Error())
	}

	privateKey, err := util.Ed25519PrivateKeyFromString(util.Ed25519Prefix + privateKeyStr)
	if err != nil {
		return decimal.Zero, e.NewError500(e.WalletGetGasSponsorFailed, "Parse ed25519 private key failed: "+err.Error())
	}

	contractID := setting.NearSetting.TestnetNftContractID
	methodName := setting.NearSetting.TestnetNftGetSponsorBalanceMethodName
	nodeURLs := setting.NearSetting.TestnetURLs
	if req.IsMainnet {
		nodeURLs = setting.NearSetting.MainnetURLs
		contractID = setting.NearSetting.MainnetNftContractID
		methodName = setting.NearSetting.MainnetNftGetSponsorBalanceMethodName
	}

	balanceInYoctoNEARS, err := openedu_near_sdk.GetGasSponsorBalanceWithRetry(nodeURLs, privateKey, contractID, methodName, req.CourseCuid, wallet.Address)
	if err != nil {
		return decimal.Zero, e.NewError500(e.WalletGetGasSponsorFailed, "Get the sponsor balance with retry error: "+err.Error())
	}

	return util.ParseNearYocto2Amount(balanceInYoctoNEARS), nil
}

func (s *WalletService) getBaseGasSponsorBalance(wallet *models.Wallet, req *dto.GetWalletGasSponsorBalanceRequest) (decimal.Decimal, *e.AppError) {
	_ = req 
	sponsorWallet, err := models.Repository.SponsorWallet.FindOne(&models.SponsorWalletQuery{
		SponsorID: &wallet.UserID,
		Network:   &wallet.Network,
	}, nil)

	if err != nil {
		if models.IsRecordNotFound(err) {
			return decimal.Zero, e.NewError404(e.WalletNotFound, "Sponsor wallet not found")
		}
		return decimal.Zero, e.NewError500(e.WalletGetGasSponsorFailed, "Find sponsor wallet error: "+err.Error())
	}

	return sponsorWallet.Balance, nil
}

func TokenID2Decimals(nodeURLs []string, accountID string, privateKey ed25519.PrivateKey, tokenID string) (uint8, error) {
	switch tokenID {
	case setting.NearSetting.MainnetUSDTContractID:
		return uint8(setting.NearSetting.MainnetUSDTDecimals), nil

	case setting.NearSetting.MainnetUSDCContractID:
		return uint8(setting.NearSetting.MainnetUSDCDecimals), nil

	case setting.NearSetting.MainnetOpenEduContractID:
		return uint8(setting.NearSetting.MainnetOpenEduDecimals), nil

	case setting.NearSetting.TestnetUSDTContractID:
		return uint8(setting.NearSetting.TestnetUSDTDecimals), nil

	case setting.NearSetting.TestnetUSDCContractID:
		return uint8(setting.NearSetting.TestnetUSDCDecimals), nil

	case setting.NearSetting.TestnetOpenEduContractID:
		return uint8(setting.NearSetting.TestnetOpenEduDecimals), nil
	}

	// TODO implement cache
	ftMetadata, err := openedu_near_sdk.GetFtMetadataWithRetry(nodeURLs, accountID, privateKey, tokenID)
	if err != nil {
		return 0, err
	}

	return ftMetadata.Decimals, nil
}

func (s *WalletService) GetEarningsByUser(req *dto.GetWalletEarningsRequest) ([]*dto.WalletEarningResponse, *e.AppError) {
	var walletEarnings []*dto.WalletEarningResponse

	wallets, wErr := models.Repository.Wallet.FindMany(&models.WalletQuery{
		UserID: &req.UserID,
	}, nil)
	if wErr != nil {
		return nil, e.NewError500(e.Error, "Find wallets error: "+wErr.Error())
	}

	for _, wallet := range wallets {
		if !wallet.IsNetworkNEAR() {
			continue
		}

		privateKeyStr, err := kms.Decrypt(wallet.EncryptedPrivateKey)
		if err != nil {
			return nil, e.NewError500(e.Error, "Encrypt private key failed: "+err.Error())
		}

		privateKey, err := util.Ed25519PrivateKeyFromString(util.Ed25519Prefix + privateKeyStr)
		if err != nil {
			return nil, e.NewError500(e.Error, "Parse ed25519 private key failed: "+err.Error())
		}

		contractID := setting.NearSetting.TestnetPaymentContractID
		methodName := setting.NearSetting.TestnetPaymentGetUserInfoMethod
		nodeURLs := setting.NearSetting.TestnetURLs
		if req.IsMainnet {
			nodeURLs = setting.NearSetting.MainnetURLs
			contractID = setting.NearSetting.MainnetPaymentContractID
			methodName = setting.NearSetting.MainnetPaymentGetUserInfoMethod
		}

		userEarning, err := openedu_near_sdk.GetUserEarningWithRetry(nodeURLs, privateKey, contractID, methodName, wallet.Address)
		if err != nil {
			return nil, e.NewError500(e.Error, "Get user earning with retry error: "+err.Error())
		}

		// TODO implement me as parallel
		for _, deposit := range userEarning.Deposits {
			numDecimals, dErr := TokenID2Decimals(nodeURLs, wallet.Address, privateKey, deposit.TokenID)
			if dErr != nil {
				return nil, e.NewError500(e.Error, "Get user earning with retry error: "+dErr.Error())
			}

			walletEarnings = append(walletEarnings, &dto.WalletEarningResponse{
				BlockchainWalletID: wallet.ID,
				CoreWalletID:       wallet.CoreWalletID,
				Address:            userEarning.UserID,
				Network:            models.NetworkNEAR,
				Token:              models.TokenID2Token(deposit.TokenID),
				TokenID:            deposit.TokenID,
				Amount:             util.ParseRawValue2ReadableAmount(deposit.Amount, numDecimals),
			})
		}
	}

	return walletEarnings, nil
}

func (s *WalletService) GetAccountInfo(req *dto.GetAccountInfoRequest) (*dto.GetAccountInfoResponse, *e.AppError) {
	resp, err := s.transferService.GetAccountInfo(&crypto_transfer.GetAccountInfoRequest{
		Network:   req.Network,
		Address:   req.Address,
		IsMainnet: req.IsMainnet,
	})
	if err == nil {
		return &dto.GetAccountInfoResponse{
			Network:     resp.Network,
			Address:     resp.Address,
			AccountInfo: resp.AccountInfo,
		}, nil
	}

	switch {
	case errors.Is(err, crypto_transfer.ErrInvalidAddress):
		return nil, e.NewError400(e.WalletInvalidAddress, err.Error())

	default:
		return nil, e.NewError500(e.WalletGetAccountInfoFailed, err.Error())
	}
}
