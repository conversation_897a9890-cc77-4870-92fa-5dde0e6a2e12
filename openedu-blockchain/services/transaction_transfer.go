package services

import (
	"errors"
	"github.com/samber/lo"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services/crypto_transfer"
	"strings"
)

func (s *TransactionService) Transfer(req *dto.SingleTransferRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.SenderWalletID,
		ToAddress: req.ToAddress,
		ToNetwork: req.Network,
		Type:      models.TxnTypeTransfer,
		Deposit:   req.Amount,
		Token:     req.Token,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionTransferTokensFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	senderWallet, aErr := Wallet.FindByID(req.SenderWalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.SenderWalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionTransferTokensFailed, msg)
		return
	}

	txn.FromAddress = senderWallet.Address
	txn.FromNetwork = senderWallet.Network
	resp, err := s.transferService.Transfer(senderWallet, &crypto_transfer.TransferRequest{
		ToAddress: req.ToAddress,
		ToNetwork: req.Network,
		Token:     req.Token,
		TokenID:   req.ContractID,
		Amount:    req.Amount,
		IsMainnet: req.IsMainnet,
	})
	if err != nil {
		txn, appErr = s.handleTransferError(txn, err)
		return
	}

	txn.ContractID = resp.ContractID
	txn.MethodName = resp.MethodName
	txn.InputData = resp.InputData
	txn.TxHash = resp.TxHash
	txn.BlockHash = resp.BlockHash
	txn.Nonce = resp.Nonce
	txn.GasLimit = resp.GasLimit
	txn.GasBurnt = resp.GasBurnt
	txn.Status = resp.Status
	switch txn.Status {
	case models.TxStatusSuccess:
		txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

	case models.TxStatusFailed:
		msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
			" on network " + strings.ToUpper(string(senderWallet.Network)) + " failed: " + resp.ErrorMsg
		txn.ErrorCode = e.TransactionTransferTokensFailed
		txn.Response = s.newTxnResponse(e.TransactionTransferTokensFailed, msg, resp.TxDetails)
		appErr = e.NewError500(e.TransactionTransferTokensFailed, msg)
	}
	return
}

func (s *TransactionService) BatchTransfer(req *dto.BatchTransferRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.SenderWalletID,
		Type:      models.TxnTypeTransfer,
		Token:     req.Token,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs:  []string{req.CoreTxID},
			Recipients: req.Recipients,
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionTransferTokensFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	senderWallet, aErr := Wallet.FindByID(req.SenderWalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.SenderWalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionTransferTokensFailed, msg)
		return
	}

	txn.FromAddress = senderWallet.Address
	txn.FromNetwork = senderWallet.Network
	resp, err := s.transferService.BatchTransfer(
		senderWallet,
		&crypto_transfer.BatchTransferRequest{
			ToNetwork: req.Network,
			TokenID:   req.TokenID,
			Token:     req.Token,
			IsMainnet: req.IsMainnet,
			Recipients: lo.Map(
				req.Recipients,
				func(item *dto.BatchTransferRecipient, _ int) *crypto_transfer.TransferRecipient {
					return &crypto_transfer.TransferRecipient{
						Amount:    item.Amount,
						ToAddress: item.Address,
					}
				}),
		})
	if err != nil {
		txn, appErr = s.handleTransferError(txn, err)
		return
	}

	txn.MethodName = resp[0].MethodName
	txn.InputData = resp[0].InputData
	txn.BlockHash = resp[0].BlockHash
	txn.TxHash = resp[0].TxHash
	txn.Status = resp[0].Status
	txn.Nonce = resp[0].Nonce
	txn.GasLimit = resp[0].GasLimit
	txn.GasBurnt = resp[0].GasBurnt
	switch resp[0].Status {
	case models.TxStatusSuccess:
		txn.Response = s.newTxnResponse(e.Success, "Success", resp[0].TxDetails)

	case models.TxStatusFailed:
		msg := "Smart contract call succeeded but the transaction with hash " + resp[0].TxHash +
			" on network " + strings.ToUpper(string(senderWallet.Network)) + " failed: " + resp[0].ErrorMsg
		txn.ErrorCode = e.TransactionTransferTokensFailed
		txn.Response = s.newTxnResponse(e.TransactionTransferTokensFailed, msg, resp[0].TxDetails)
		appErr = e.NewError500(e.TransactionTransferTokensFailed, msg)
	}
	return
}

func (s *TransactionService) handleTransferError(transaction *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return transaction, nil
	}

	switch {
	case errors.Is(err, crypto_transfer.ErrConnectRPCFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = s.newTxnResponse(
			e.TransactionTransferTokensFailed,
			"Connect RPC to validate transfer request failed: "+err.Error(),
			nil,
		)
		return transaction, e.NewError500(e.TransactionTransferTokensFailed, "Connect RPC to validate transfer request failed: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrAccountNotExists):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = s.newTxnResponse(
			e.TransactionInsufficientGasFee,
			"Account does not exist to cover gas fee: "+err.Error(),
			nil,
		)
		return transaction, e.NewError500(e.TransactionInsufficientGasFee, "Account does not exist to cover gas fee: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrGetAccountStateFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionTransferTokensFailed,
			"message": "Get account state to check balance and gas fee failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionTransferTokensFailed, "Get account state to check balance and gas fee failed: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrInvalidAddress):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.WalletInvalidAddress
		transaction.Response = models.JSONB{
			"code":    e.WalletInvalidAddress,
			"message": "Invalid to address: " + err.Error(),
		}
		return transaction, e.NewError400(e.WalletInvalidAddress, "Invalid to address: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrInsufficientGasFee):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to cover gas fee: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to cover gas fee: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrInsufficientBalance):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientBalance
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to transfer: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to transfer: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrGetFtBalanceFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionTransferTokensFailed,
			"message": "Check account balance before transfer failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionTransferTokensFailed, "Check account balance before transfer failed: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrGetStorageBalanceFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionTransferTokensFailed,
			"message": "Check account storage deposit balance failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionTransferTokensFailed, "Check account storage deposit balance failed: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrGetStorageBoundsFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionTransferTokensFailed,
			"message": "Get deposit storage bounds of token failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionTransferTokensFailed, "Get deposit storage bounds of token failed: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrInsufficientBalanceToStorageDeposit):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to storage deposit: "+err.Error())

	case errors.Is(err, crypto_transfer.ErrStorageDepositFailed):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionTransferTokensFailed,
			"message": "Storage deposit for recipient account failed: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionTransferTokensFailed, "Storage deposit for recipient account failed: "+err.Error())
	}

	switch transaction.Type {
	case models.TxnTypeBatchTransfer:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionTransferTokensFailed,
			"message": "Batch transfer tokens to recipient accounts failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionTransferTokensFailed, "Batch transfer tokens to recipient accounts failed: "+err.Error())

	default:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionTransferTokensFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionTransferTokensFailed,
			"message": "Transfer tokens to recipient account failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionTransferTokensFailed, "Transfer tokens to recipient account failed: "+err.Error())
	}
}
