package services

import (
	"errors"
	"fmt"
	"time"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/nft"
	"strings"
)

const (
	GasPayerPlatform = "platform"
	GasPayerLearner  = "learner" 
	GasPayerCreator  = "creator"
	GasPayerPaymaster = "paymaster"
)

func (s *TransactionService) DepositNftSponsorGasFee(req *dto.DepositSponsorGasRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.WalletID,
		Status:    models.TxStatusPending,
		Type:      models.TxnTypeDepositSponsorGas,
		Deposit:   req.Amount,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionDepositSponsorGasFailed,
				"Failed to save the transaction to the database: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionDepositSponsorGasFailed, msg)
		return
	}

	if resp, err := s.nftService.DepositSponsorGas(wallet, &nft.DepositSponsorGasRequest{
		CourseCuid: req.CourseCuid,
		Amount:     req.Amount,
		IsMainnet:  req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.ToAddress = resp.ContractID
			txn.ToNetwork = wallet.Network

			txn.FromAddress = wallet.Address
			txn.FromNetwork = wallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}

		txn, appErr = s.handleDepositSponsorGasError(txn, err)
		return

	} else {

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = wallet.Network

		txn.FromAddress = wallet.Address
		txn.FromNetwork = wallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Deposit sponsor gas to course failed: smart contract call succeeded but the transaction status failed"
			txn.ErrorCode = e.TransactionDepositSponsorGasFailed
			txn.Response = s.newTxnResponse(e.TransactionDepositSponsorGasFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionDepositSponsorGasFailed, msg)
		}
	}
	return
}

func (s *TransactionService) handleDepositSponsorGasError(transaction *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return transaction, nil
	}

	switch {
	case errors.Is(err, nft.ErrAccountNotExists):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientBalance
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientBalance, "Account does not exist to deposit sponsor gas fee: "+err.Error())

	case errors.Is(err, nft.ErrInsufficientGasFee):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientGasFee
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to cover the gas fee: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to cover the gas fee: "+err.Error())

	case errors.Is(err, nft.ErrInsufficientBalance):
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionInsufficientBalance
		transaction.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return transaction, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to deposit sponsor gas fee: "+err.Error())

	default:
		transaction.Status = models.TxStatusFailed
		transaction.ErrorCode = e.TransactionDepositSponsorGasFailed
		transaction.Response = models.JSONB{
			"code":    e.TransactionDepositSponsorGasFailed,
			"message": "Deposit sponsor gas fee to course failed: " + err.Error(),
		}
		return transaction, e.NewError500(e.TransactionDepositSponsorGasFailed, "Deposit sponsor gas fee to course failed: "+err.Error())
	}
}

func (s *TransactionService) WithdrawNftSponsorGasFee(req *dto.WithdrawSponsorGasRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.WalletID,
		Status:    models.TxStatusPending,
		Type:      models.TxnTypeWithdrawSponsorGas,
		Token:     req.Token,
		Deposit:   req.Amount,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionWithdrawSponsorGasFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	wallet, aErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if aErr != nil {
		msg := "Find the wallet ID " + req.WalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionWithdrawSponsorGasFailed, msg)
		return
	}

	if resp, err := s.nftService.WithdrawSponsorGas(wallet, &nft.WithdrawSponsorGasRequest{
		CourseCuid: req.CourseCuid,
		Amount:     req.Amount,
		IsMainnet:  req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.ToAddress = resp.ContractID
			txn.ToNetwork = wallet.Network

			txn.FromAddress = wallet.Address
			txn.FromNetwork = wallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}
		txn, appErr = s.handleWithdrawSponsorGasError(txn, err)
		return

	} else {

		txn.ToAddress = resp.ContractID
		txn.ToNetwork = wallet.Network

		txn.FromAddress = wallet.Address
		txn.FromNetwork = wallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(wallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionWithdrawSponsorGasFailed
			txn.Response = s.newTxnResponse(e.TransactionWithdrawSponsorGasFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionWithdrawSponsorGasFailed, msg)
		}
	}
	return
}

func (s *TransactionService) handleWithdrawSponsorGasError(txn *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return txn, nil
	}

	switch {
	case errors.Is(err, nft.ErrAccountNotExists):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientBalance
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientBalance, "Account does not exist to deposit sponsor gas fee: "+err.Error())

	case errors.Is(err, nft.ErrInsufficientBalance):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientBalance
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to storage deposit: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to deposit sponsor gas fee: "+err.Error())

	default:
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionWithdrawSponsorGasFailed
		txn.Response = models.JSONB{
			"code":    e.TransactionWithdrawSponsorGasFailed,
			"message": "Withdraw sponsor gas fee to course failed: " + err.Error(),
		}
		return txn, e.NewError500(e.TransactionWithdrawSponsorGasFailed, "Withdraw sponsor gas fee to course failed: "+err.Error())
	}
}

func (s *TransactionService) MintNFT(req *dto.MintNFTRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.ReceiverWalletID,
		Status:    models.TxStatusPending,
		Type:      models.TxnTypeMintNFT,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	if req.Network != "" {
		txn.ToNetwork = req.Network
		txn.FromNetwork = req.Network
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionMintNFTFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	receiverWallet, aErr := Wallet.FindByID(req.ReceiverWalletID, nil)
	if aErr != nil {
		msg := "Find the wallet ID " + req.ReceiverWalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionMintNFTFailed, msg)
		return
	}

	tokenID := req.CourseCuid + receiverWallet.UserID + fmt.Sprintf("%d", time.Now().UnixMilli())

	network := req.Network
	if network == "" {
		network = receiverWallet.Network
	}

	txn.ToNetwork = network
	txn.FromNetwork = network

	if (models.Participant(req.GasFeePayer) == models.Learner || models.Participant(req.GasFeePayer) == models.Creator) && (network == models.NetworkBASE) {
		strategy, err := s.nftService.GetStrategy(network)
		if err != nil {
			msg := "Failed to get strategy for network " + string(network) + ": " + err.Error()
			txn.Status = models.TxStatusFailed
			txn.ErrorCode = e.TransactionMintNFTFailed
			txn.Response = models.JSONB{
				"code":    e.TransactionMintNFTFailed,
				"message": msg,
			}
			appErr = e.NewError500(e.TransactionMintNFTFailed, msg)
			return
		}

		eip712Strategy, ok := strategy.(nft.EIP712Strategy)
		if !ok {
			msg := "Network " + string(network) + " does not support EIP-712 permit"
			txn.Status = models.TxStatusFailed
			txn.ErrorCode = e.TransactionMintNFTFailed
			txn.Response = models.JSONB{
				"code":    e.TransactionMintNFTFailed,
				"message": msg,
			}
			appErr = e.NewError500(e.TransactionMintNFTFailed, msg)
			return
		}

		permitReq := &nft.CreatePermitRequest{
			ReceiverAddress: receiverWallet.Address,
			CourseCuid:      req.CourseCuid,
			TokenMetadata: nft.TokenMetadata{
				Title:       req.TokenMetadata.Title,
				Description: req.TokenMetadata.Description,
				MediaURL:    req.TokenMetadata.MediaURL,
			},
			Network:   network,
			IsMainnet: req.IsMainnet,
		}

		// For Creator gas fee payer, need to add sponsor wallet info to permit request
		if models.Participant(req.GasFeePayer) == models.Creator {
			permitReq.CourseOwnerAddress = req.CourseOwnerAddress
			permitReq.GasFeePayer = models.Creator
		} else {
			permitReq.GasFeePayer = models.Learner
		}

		permitResp, err := eip712Strategy.CreatePermit(permitReq)
		if err != nil {
			msg := "Failed to create permit signature: " + err.Error()
			txn.Status = models.TxStatusFailed
			txn.ErrorCode = e.TransactionCreatePermitFailed
			txn.Response = models.JSONB{
				"code":    e.TransactionCreatePermitFailed,
				"message": msg,
			}
			appErr = e.NewError500(e.TransactionCreatePermitFailed, msg)
			return
		}

		if resp, err := eip712Strategy.MintNFTWithPermit(receiverWallet, &nft.MintNftWithPermitRequest{
			CourseCuid:         req.CourseCuid,
			CourseOwnerAddress: req.CourseOwnerAddress,
			GasFeePayer:        models.Participant(req.GasFeePayer),
			TokenID:            tokenID,
			TokenMetadata: nft.TokenMetadata{
				Title:       req.TokenMetadata.Title,
				Description: req.TokenMetadata.Description,
				MediaURL:    req.TokenMetadata.MediaURL,
			},
			Network:           network,
			IsMainnet:         req.IsMainnet,
			SignatureV:        permitResp.SignatureV,
			SignatureR:        permitResp.SignatureR,
			SignatureS:        permitResp.SignatureS,
			SignatureNonce:    permitResp.SignatureNonce,
			SignatureDeadline: permitResp.SignatureDeadline,
		}); err != nil {
			if resp != nil {
				txn.ToAddress = receiverWallet.Address
				txn.ToNetwork = receiverWallet.Network

				txn.FromAddress = resp.ContractID
				txn.FromNetwork = receiverWallet.Network

				txn.MethodName = resp.MethodName
				txn.InputData = resp.InputData
				txn.ContractID = resp.ContractID
			}
			txn, appErr = s.handleMintNftWithPermitError(txn, err)
			return
		} else {
			txn.ToAddress = receiverWallet.Address
			txn.ToNetwork = receiverWallet.Network

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = receiverWallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
			txn.Deposit = resp.Deposit
			txn.Token = resp.Token

			txn.TxHash = resp.TxHash
			txn.BlockHash = resp.BlockHash
			txn.GasLimit = resp.GasLimit
			txn.GasBurnt = resp.GasBurnt
			txn.Nonce = resp.Nonce
			txn.Props.NftTokenID = tokenID
			txn.Props.StorageCost = util.ParseRawValue2ReadableAmount(resp.StorageCost, util.NearNominationExp)
			txn.Props.GasCost = util.ParseRawValue2ReadableAmount(resp.GasCost, util.NearNominationExp)
			txn.Props.TotalGasCost = util.ParseRawValue2ReadableAmount(resp.TotalGasCost, util.NearNominationExp)

			txn.Status = resp.Status
			switch resp.Status {
			case models.TxStatusSuccess:
				txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

			case models.TxStatusFailed:
				msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
					" on network " + strings.ToUpper(string(receiverWallet.Network)) + " failed: " + resp.ErrorMsg
				txn.ErrorCode = e.TransactionMintNFTFailed
				txn.Response = s.newTxnResponse(e.TransactionMintNFTFailed, msg, resp.TxDetails)
				appErr = e.NewError500(e.TransactionMintNFTFailed, msg)
			}
		}
		return
	}

	if resp, err := s.nftService.MintNft(receiverWallet, &nft.MintNftRequest{
		CourseCuid:         req.CourseCuid,
		CourseOwnerAddress: req.CourseOwnerAddress,
		GasFeePayer:        models.Participant(req.GasFeePayer),
		TokenID:            tokenID,
		TokenMetadata: nft.TokenMetadata{
			Title:       req.TokenMetadata.Title,
			Description: req.TokenMetadata.Description,
			MediaURL:    req.TokenMetadata.MediaURL,
		},
		Network:   network,
		IsMainnet: req.IsMainnet,
	}); err != nil {
		if resp != nil {
			txn.ToAddress = receiverWallet.Address
			txn.ToNetwork = receiverWallet.Network

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = receiverWallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}
		txn, appErr = s.handleMintNftError(txn, err)
		return

	} else {

		txn.ToAddress = receiverWallet.Address
		txn.ToNetwork = receiverWallet.Network

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = receiverWallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce
		txn.Props.NftTokenID = tokenID
		txn.Props.StorageCost = util.ParseRawValue2ReadableAmount(resp.StorageCost, util.NearNominationExp)
		txn.Props.GasCost = util.ParseRawValue2ReadableAmount(resp.GasCost, util.NearNominationExp)
		txn.Props.TotalGasCost = util.ParseRawValue2ReadableAmount(resp.TotalGasCost, util.NearNominationExp)

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(receiverWallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionMintNFTFailed
			txn.Response = s.newTxnResponse(e.TransactionMintNFTFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionMintNFTFailed, msg)
		}
	}
	return
}

func (s *TransactionService) handleMintNftError(txn *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return txn, nil
	}

	switch {
	case errors.Is(err, nft.ErrAccountNotExists):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientBalance
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not exist to mint NFT: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientBalance, "Account does not exist to mint NFT: "+err.Error())

	case errors.Is(err, nft.ErrInsufficientBalance):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientBalance
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to mint NFT: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to mint NFT: "+err.Error())

	case errors.Is(err, nft.ErrInsufficientGasFee):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientGasFee
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to cover the gas fee: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to cover the gas fee: "+err.Error())

	default:
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionMintNFTFailed
		txn.Response = models.JSONB{
			"code":    e.TransactionMintNFTFailed,
			"message": "Mint NFT failed: " + err.Error(),
		}
		return txn, e.NewError500(e.TransactionMintNFTFailed, "Mint NFT failed: "+err.Error())
	}
}

func (s *TransactionService) CreatePermit(req *dto.CreatePermitRequest) (*dto.CreatePermitResponse, *e.AppError) {
	nftReq := &nft.CreatePermitRequest{
		ReceiverAddress: req.ReceiverAddress,
		CourseCuid:      req.CourseCuid,
		TokenMetadata: nft.TokenMetadata{
			Title:       req.TokenMetadata.Title,
			Description: req.TokenMetadata.Description,
			MediaURL:    req.TokenMetadata.MediaURL,
		},
		Network:   req.Network,
		IsMainnet: req.IsMainnet,
	}

	resp, err := s.nftService.CreatePermit(nftReq)
	if err != nil {
		return nil, s.handleCreatePermitError(err)
	}

	return &dto.CreatePermitResponse{
		SignatureV:        resp.SignatureV,
		SignatureR:        resp.SignatureR,
		SignatureS:        resp.SignatureS,
		SignatureNonce:    resp.SignatureNonce,
		SignatureDeadline: resp.SignatureDeadline,
	}, nil
}

func (s *TransactionService) handleCreatePermitError(err error) *e.AppError {
	if err == nil {
		return nil
	}

	switch {
	case errors.Is(err, nft.ErrGetPrivateKeyFailed):
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to get private key: "+err.Error())
	case errors.Is(err, nft.ErrParsePrivateKeyFailed):
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to parse private key: "+err.Error())
	case errors.Is(err, nft.ErrGetEthClientFailed):
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to get ETH client: "+err.Error())
	case errors.Is(err, nft.ErrContractNotConfigured):
		return e.NewError500(e.TransactionCreatePermitFailed, "Contract not configured: "+err.Error())
	case errors.Is(err, nft.ErrGetContractFailed):
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to get contract: "+err.Error())
	case errors.Is(err, nft.ErrGetMessageHashFailed):
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to get message hash: "+err.Error())
	case errors.Is(err, nft.ErrGetEIP712DomainFailed):
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to get EIP-712 domain: "+err.Error())
	case errors.Is(err, nft.ErrSignMessageFailed):
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to sign message: "+err.Error())
	default:
		return e.NewError500(e.TransactionCreatePermitFailed, "Failed to create permit: "+err.Error())
	}
}

func (s *TransactionService) MintNFTWithPermit(req *dto.MintNFTWithPermitRequest) (txn *models.Transaction, appErr *e.AppError) {
	txn = &models.Transaction{
		WalletID:  req.ReceiverWalletID,
		Status:    models.TxStatusPending,
		Type:      models.TxnTypeMintNFT,
		IsMainnet: req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	if req.Network != "" {
		txn.ToNetwork = req.Network
		txn.FromNetwork = req.Network
	}

	defer func() {
		if err := models.Repository.Transaction.Create(txn, nil); err != nil {
			appErr = e.NewError500(e.TransactionMintNFTFailed,
				"Create transaction failed: "+err.Error())
			return
		}
	}()

	receiverWallet, aErr := Wallet.FindByID(req.ReceiverWalletID, nil)
	if aErr != nil {
		msg := "Find the wallet ID " + req.ReceiverWalletID + " failed: " + aErr.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = aErr.ErrCode
		txn.Response = models.JSONB{
			"code":    aErr.ErrCode,
			"message": msg,
		}
		aErr = e.NewError500(e.TransactionMintNFTFailed, msg)
		return
	}

	tokenID := req.CourseCuid + receiverWallet.UserID

	network := req.Network
	if network == "" {
		network = receiverWallet.Network
	}

	txn.ToNetwork = network
	txn.FromNetwork = network

	permitReq := &nft.CreatePermitRequest{
		ReceiverAddress: receiverWallet.Address,
		CourseCuid:      req.CourseCuid,
		TokenMetadata: nft.TokenMetadata{
			Title:       req.TokenMetadata.Title,
			Description: req.TokenMetadata.Description,
			MediaURL:    req.TokenMetadata.MediaURL,
		},
		Network:   network,
		IsMainnet: req.IsMainnet,
	}

	permitResp, err := s.nftService.CreatePermit(permitReq)
	if err != nil {
		msg := "Failed to create permit signature: " + err.Error()
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionCreatePermitFailed
		txn.Response = models.JSONB{
			"code":    e.TransactionCreatePermitFailed,
			"message": msg,
		}
		appErr = e.NewError500(e.TransactionCreatePermitFailed, msg)
		return
	}

	if resp, err := s.nftService.MintNftWithPermit(receiverWallet, &nft.MintNftWithPermitRequest{
		CourseCuid:         req.CourseCuid,
		CourseOwnerAddress: receiverWallet.Address,
		TokenID:            tokenID,
		TokenMetadata: nft.TokenMetadata{
			Title:       req.TokenMetadata.Title,
			Description: req.TokenMetadata.Description,
			MediaURL:    req.TokenMetadata.MediaURL,
		},
		Network:           network,
		IsMainnet:         req.IsMainnet,
		SignatureV:        permitResp.SignatureV,
		SignatureR:        permitResp.SignatureR,
		SignatureS:        permitResp.SignatureS,
		SignatureNonce:    permitResp.SignatureNonce,
		SignatureDeadline: permitResp.SignatureDeadline,
	}); err != nil {
		if resp != nil {
			txn.ToAddress = receiverWallet.Address
			txn.ToNetwork = receiverWallet.Network

			txn.FromAddress = resp.ContractID
			txn.FromNetwork = receiverWallet.Network

			txn.MethodName = resp.MethodName
			txn.InputData = resp.InputData
			txn.ContractID = resp.ContractID
		}
		txn, appErr = s.handleMintNftWithPermitError(txn, err)
		return

	} else {

		txn.ToAddress = receiverWallet.Address
		txn.ToNetwork = receiverWallet.Network

		txn.FromAddress = resp.ContractID
		txn.FromNetwork = receiverWallet.Network

		txn.MethodName = resp.MethodName
		txn.InputData = resp.InputData
		txn.ContractID = resp.ContractID
		txn.Deposit = resp.Deposit
		txn.Token = resp.Token

		txn.TxHash = resp.TxHash
		txn.BlockHash = resp.BlockHash
		txn.GasLimit = resp.GasLimit
		txn.GasBurnt = resp.GasBurnt
		txn.Nonce = resp.Nonce
		txn.Props.NftTokenID = tokenID
		txn.Props.StorageCost = util.ParseRawValue2ReadableAmount(resp.StorageCost, util.NearNominationExp)
		txn.Props.GasCost = util.ParseRawValue2ReadableAmount(resp.GasCost, util.NearNominationExp)
		txn.Props.TotalGasCost = util.ParseRawValue2ReadableAmount(resp.TotalGasCost, util.NearNominationExp)

		txn.Status = resp.Status
		switch resp.Status {
		case models.TxStatusSuccess:
			txn.Response = s.newTxnResponse(e.Success, "Success", resp.TxDetails)

		case models.TxStatusFailed:
			msg := "Smart contract call succeeded but the transaction with hash " + resp.TxHash +
				" on network " + strings.ToUpper(string(receiverWallet.Network)) + " failed: " + resp.ErrorMsg
			txn.ErrorCode = e.TransactionMintNFTFailed
			txn.Response = s.newTxnResponse(e.TransactionMintNFTFailed, msg, resp.TxDetails)
			appErr = e.NewError500(e.TransactionMintNFTFailed, msg)
		}
	}
	return
}

func (s *TransactionService) handleMintNftWithPermitError(txn *models.Transaction, err error) (*models.Transaction, *e.AppError) {
	if err == nil {
		return txn, nil
	}

	switch {
	case errors.Is(err, nft.ErrAccountNotExists):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientBalance
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not exist to mint NFT with permit: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientBalance, "Account does not exist to mint NFT with permit: "+err.Error())

	case errors.Is(err, nft.ErrInsufficientBalance):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientBalance
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientBalance,
			"message": "Account does not enough balance to mint NFT with permit: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientBalance, "Account does not enough balance to mint NFT with permit: "+err.Error())

	case errors.Is(err, nft.ErrInsufficientGasFee):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionInsufficientGasFee
		txn.Response = models.JSONB{
			"code":    e.TransactionInsufficientGasFee,
			"message": "Account does not enough balance to cover the gas fee for mint NFT with permit: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionInsufficientGasFee, "Account does not enough balance to cover the gas fee for mint NFT with permit: "+err.Error())

	case errors.Is(err, nft.ErrSignatureExpired):
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionMintNFTFailed
		txn.Response = models.JSONB{
			"code":    e.TransactionMintNFTFailed,
			"message": "Signature expired: " + err.Error(),
		}
		return txn, e.NewError400(e.TransactionMintNFTFailed, "Signature expired: "+err.Error())

	default:
		txn.Status = models.TxStatusFailed
		txn.ErrorCode = e.TransactionMintNFTFailed
		txn.Response = models.JSONB{
			"code":    e.TransactionMintNFTFailed,
			"message": "Mint NFT with permit failed: " + err.Error(),
		}
		return txn, e.NewError500(e.TransactionMintNFTFailed, "Mint NFT with permit failed: "+err.Error())
	}
}
