package services

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/services/crypto_payment"
	paymentstrategies "openedu-blockchain/services/crypto_payment/strategies"
	"openedu-blockchain/services/crypto_transfer"
	transferstrategies "openedu-blockchain/services/crypto_transfer/strategies"
	"openedu-blockchain/services/launchpad"
	launchpadstrategies "openedu-blockchain/services/launchpad/strategies"
	"openedu-blockchain/services/nft"
	nftstrategies "openedu-blockchain/services/nft/strategies"
)

type WalletServiceIface interface {
	Create(req *dto.WalletRequest) (*models.Wallet, *e.AppError)
	FindPage(query *models.WalletQuery, options *models.FindPageOptions) ([]*models.Wallet, *models.Pagination, *e.AppError)
	FindByID(id string, options *models.FindOneOptions) (*models.Wallet, *e.AppError)
	GetEarningsByUser(req *dto.GetWalletEarningsRequest) ([]*dto.WalletEarningResponse, *e.AppError)
	GetGasSponsorBalance(req *dto.GetWalletGasSponsorBalanceRequest) (decimal.Decimal, *e.AppError)
	GetAccountInfo(req *dto.GetAccountInfoRequest) (*dto.GetAccountInfoResponse, *e.AppError)
}

type LaunchpadServiceIface interface {
	GetVotingPowersByPool(req *dto.GetVotingPowersRequest) (*dto.GetVotingPowersResponse, *e.AppError)
}

type UserSettingServiceIface interface {
	Create(setting *models.UserSetting) (*models.UserSetting, *e.AppError)
}

type TransactionServiceIface interface {
	MintNFT(req *dto.MintNFTRequest) (*models.Transaction, *e.AppError)
	CreatePermit(req *dto.CreatePermitRequest) (*dto.CreatePermitResponse, *e.AppError)
	MintNFTWithPermit(req *dto.MintNFTWithPermitRequest) (*models.Transaction, *e.AppError)
	Payment(req *dto.PaymentRequest) (transaction *models.Transaction, appErr *e.AppError)
	ClaimEarning(req *dto.ClaimEarningRequest) (transaction *models.Transaction, appErr *e.AppError)
	Transfer(req *dto.SingleTransferRequest) (*models.Transaction, *e.AppError)
	BatchTransfer(req *dto.BatchTransferRequest) (transaction *models.Transaction, appErr *e.AppError)
	DepositNftSponsorGasFee(req *dto.DepositSponsorGasRequest) (transaction *models.Transaction, appErr *e.AppError)
	WithdrawNftSponsorGasFee(req *dto.WithdrawSponsorGasRequest) (transaction *models.Transaction, appErr *e.AppError)
	InitLaunchpadPool(req *dto.InitLaunchpadPoolRequest) (transaction *models.Transaction, appErr *e.AppError)
	ApproveLaunchpadPool(req *dto.ApproveLaunchpadPoolRequest) (txn *models.Transaction, appErr *e.AppError)
	PledgeLaunchpad(req *dto.PledgeLaunchpadRequest) (txn *models.Transaction, appErr *e.AppError)
	UpdateLpPoolFundingTime(req *dto.UpdateLpPoolFundingTimeRequest) (txn *models.Transaction, appErr *e.AppError)
	CancelLaunchpadPool(req *dto.CancelLpPoolRequest) (txn *models.Transaction, appErr *e.AppError)
	CheckLpFundingResult(req *dto.CheckLpFundingResultRequest) (txn *models.Transaction, appErr *e.AppError)
	ContinueLpPartialFunds(req *dto.ContinueLpPartialFundRequest) (txn *models.Transaction, appErr *e.AppError)
	SetLaunchpadFundingTime(req *dto.SetLpFundingTimeRequest) (txn *models.Transaction, appErr *e.AppError)
	WithdrawLaunchpadFundToCreator(req *dto.WithdrawLaunchpadFundToCreatorRequest) (txn *models.Transaction, appErr *e.AppError)
	ClaimLaunchpadRefund(req *dto.ClaimLaunchpadRefundRequest) (txn *models.Transaction, appErr *e.AppError)
	UpdateLaunchpadPoolStatus(req *dto.UpdateLaunchpadPoolStatusRequest) (txn *models.Transaction, appErr *e.AppError)
}

var Wallet WalletServiceIface
var UserSetting UserSettingServiceIface
var Transaction TransactionServiceIface
var Launchpad LaunchpadServiceIface
var SponsorWallet *SponsorWalletService

func Setup() {
	cryptoTransferSvc := crypto_transfer.NewTransferService()
	if err := cryptoTransferSvc.RegisterStrategy(models.NEAR2NEAR, &transferstrategies.Near2NearTransferService{}); err != nil {
		log.Fatalf("CryptoTransferService::RegisterStrategy register NEAR to NEAR strategy failed: %v", err)
	}
	if err := cryptoTransferSvc.RegisterStrategy(models.AVAIL2AVAIL, &transferstrategies.Avail2AvailTransferService{}); err != nil {
		log.Fatalf("CryptoTransferService::RegisterStrategy register AVAIL to AVAIL strategy failed: %v", err)
	}
	if err := cryptoTransferSvc.RegisterStrategy(models.ETH2ETH, &transferstrategies.Eth2EthTransferService{}); err != nil {
		log.Fatalf("CryptoTransferService::RegisterStrategy register ETH to ETH strategy failed: %v", err)
	}

	nftSvc := nft.NewMintNftService()
	if err := nftSvc.RegisterStrategy(models.NetworkNEAR, &nftstrategies.NearMintNftService{}); err != nil {
		log.Fatalf("MintNFTService::RegisterStrategy register mint NFT strategy on network NEAR failed: %v", err)
	}
	if err := nftSvc.RegisterStrategy(models.NetworkBASE, nftstrategies.NewEVMMintNftService()); err != nil {
		log.Fatalf("MintNFTService::RegisterStrategy register mint NFT strategy on network BASE failed: %v", err)
	}

	launchpadSvc := launchpad.NewLaunchpadService()
	if err := launchpadSvc.RegisterStrategy(models.NetworkNEAR, &launchpadstrategies.NearLaunchpadService{}); err != nil {
		log.Fatalf("LaunchpadService::RegisterStrategy register process launchpad strategy on network NEAR failed: %v", err)
	}

	if err := launchpadSvc.RegisterStrategy(models.NetworkBASE, &launchpadstrategies.EVMLaunchpadService{}); err != nil {
		log.Fatalf("LaunchpadService::RegisterStrategy register process launchpad strategy on network ETH failed: %v", err)
	}

	paymentSvc := crypto_payment.NewPaymentService()
	if err := paymentSvc.RegisterStrategy(models.NetworkBASE, &paymentstrategies.EvmPaymentService{}); err != nil {
		log.Fatalf("PaymentService::RegisterStrategy register process payment strategy on network EVM failed: %v", err)
	}

	if err := paymentSvc.RegisterStrategy(models.NetworkNEAR, &paymentstrategies.NearPaymentService{}); err != nil {
		log.Fatalf("PaymentService::RegisterStrategy register process payment strategy on network NEAR failed: %v", err)
	}

	Wallet = NewWalletService(&WalletServiceDeps{TransferService: cryptoTransferSvc})
	UserSetting = &UserSettingService{}
	SponsorWallet = NewSponsorWalletService(&SponsorWalletServiceDeps{
		TransferService: cryptoTransferSvc,
		NftService:      nftSvc,
	})
	Launchpad = NewLaunchpadService(&LaunchpadServiceDeps{
		LaunchpadService: launchpadSvc,
	})
	Transaction = NewTransactionService(&TransactionServiceDeps{
		TransferService:  cryptoTransferSvc,
		NftService:       nftSvc,
		LaunchpadService: launchpadSvc,
		PaymentService:   paymentSvc,
	})
}
