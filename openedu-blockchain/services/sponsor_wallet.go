package services

import (
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/kms"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/crypto_transfer"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
)

const (
	InitSponsorWalletMethodName = "initSponsorWallet"
	ExistingSponsorWalletTxHash = "existing_sponsor_wallet"
)

type SponsorWalletService struct {
	transferService *crypto_transfer.TransferService
}

type SponsorWalletServiceDeps struct {
	TransferService *crypto_transfer.TransferService
}

func NewSponsorWalletService(deps *SponsorWalletServiceDeps) *SponsorWalletService {
	return &SponsorWalletService{
		transferService: deps.TransferService,
	}
}

func (s *SponsorWalletService) generateWalletCredentials() (address string, encryptedPrivateKey string, appErr *e.AppError) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", e.NewError500(e.Error, "Generate private key error: "+err.Error())
	}

	address = crypto.PubkeyToAddress(privateKey.PublicKey).Hex()

	privateKeyBytes := crypto.FromECDSA(privateKey)
	privateKeyHex := hexutil.Encode(privateKeyBytes)
	encryptedPrivateKey, err = kms.Encrypt(privateKeyHex)
	if err != nil {
		return "", "", e.NewError500(e.Error, "Encrypt private key error: "+err.Error())
	}

	return address, encryptedPrivateKey, nil
}

func (s *SponsorWalletService) InitSponsorWallet(req *dto.InitSponsorWalletRequest) (*models.Transaction, *e.AppError) {
	// Find the wallet
	wallet, appErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if appErr != nil {
		return nil, appErr
	}

	// Check if wallet network is BASE
	if wallet.Network != models.NetworkBASE {
		return nil, e.NewError400(e.InvalidParams, "Only BASE network is supported for sponsor wallets")
	}

	existingSponsor, err := models.Repository.SponsorWallet.FindOne(&models.SponsorWalletQuery{
		SponsorID: &req.SponsorID,
		Network:   &req.Network,
	}, nil)

	if err != nil && !models.IsRecordNotFound(err) {
		return nil, e.NewError500(e.Error, "Find sponsor wallet error: "+err.Error())
	}

	if existingSponsor != nil {
		// Return success for idempotent behavior instead of error
		log.Infof("Sponsor wallet already exists for sponsor_id: %s, network: %s - skipping creation", req.SponsorID, req.Network)

		// Create a mock transaction response for existing wallet
		mockTransaction := &models.Transaction{
			WalletID:    req.WalletID,
			FromAddress: wallet.Address,
			FromNetwork: wallet.Network,
			ToAddress:   wallet.Address,
			ToNetwork:   wallet.Network,
			Status:      models.TxStatusSuccess,
			Type:        models.TxnTypeInitSponsorWallet,
			MethodName:  InitSponsorWalletMethodName,
			TxHash:      ExistingSponsorWalletTxHash,
		}

		return mockTransaction, nil
	}

	// Create transaction record
	transaction := &models.Transaction{
		WalletID:    req.WalletID,
		FromAddress: wallet.Address,
		FromNetwork: wallet.Network,
		ToAddress:   wallet.Address,
		ToNetwork:   wallet.Network,
		Status:      models.TxStatusPending,
		Type:        models.TxnTypeInitSponsorWallet,
		MethodName:  InitSponsorWalletMethodName,
		InputData: models.JSONB{
			"sponsor_id":   req.SponsorID,
			"sponsor_name": req.SponsorName,
			"description":  req.Description,
			"amount":       req.Amount,
		},
		Deposit:    req.Amount,
		Token:      req.Token,
		ContractID: "",
		IsMainnet:  req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	// Save the transaction
	if err := models.Repository.Transaction.Create(transaction, nil); err != nil {
		return nil, e.NewError500(e.Error, "Create transaction error: "+err.Error())
	}

	address, encryptedPrivateKey, appErr := s.generateWalletCredentials()
	if appErr != nil {
		return nil, appErr
	}

	// Create sponsor wallet record
	sponsorWallet := &models.SponsorWallet{
		WalletID:            req.WalletID,
		SponsorID:           req.SponsorID,
		SponsorName:         req.SponsorName,
		Description:         req.Description,
		Balance:             req.Amount,
		Network:             req.Network,
		Status:              models.SponsorWalletStatusActive,
		Address:             address,
		EncryptedPrivateKey: encryptedPrivateKey,
	}

	// Save the sponsor wallet
	if err := models.Repository.SponsorWallet.Create(sponsorWallet, nil); err != nil {
		return nil, e.NewError500(e.Error, "Create sponsor wallet error: "+err.Error())
	}

	transaction.Status = models.TxStatusSuccess
	transaction.TxHash = util.GenerateId()

	if err := models.Repository.Transaction.Update(transaction, nil); err != nil {
		return nil, e.NewError500(e.Error, "Update transaction error: "+err.Error())
	}

	return transaction, nil
}

func (s *SponsorWalletService) FindPage(query *models.SponsorWalletQuery, options *models.FindPageOptions) ([]*models.SponsorWallet, *models.Pagination, *e.AppError) {
	sponsorWallets, pagination, err := models.Repository.SponsorWallet.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Error, "Find sponsor wallets error: "+err.Error())
	}

	return sponsorWallets, pagination, nil
}

func (s *SponsorWalletService) FindByID(id string) (*models.SponsorWallet, *e.AppError) {
	sponsorWallet, err := models.Repository.SponsorWallet.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.WalletNotFound, "Sponsor wallet not found")
		}
		return nil, e.NewError500(e.Error, "Find sponsor wallet error: "+err.Error())
	}

	return sponsorWallet, nil
}

func (s *SponsorWalletService) FindByQuery(query *models.SponsorWalletQuery) (*models.SponsorWallet, *e.AppError) {
	sponsorWallet, err := models.Repository.SponsorWallet.FindOne(query, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.WalletNotFound, "Sponsor wallet not found")
		}
		return nil, e.NewError500(e.Error, "Find sponsor wallet error: "+err.Error())
	}

	return sponsorWallet, nil
}
