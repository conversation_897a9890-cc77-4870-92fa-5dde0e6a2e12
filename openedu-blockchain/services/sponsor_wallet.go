package services

import (
	"context"
	"fmt"
	"math/big"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/pkg/kms"
	"openedu-blockchain/pkg/log"
	"openedu-blockchain/pkg/setting"
	"openedu-blockchain/pkg/util"
	"openedu-blockchain/services/crypto_transfer"
	"openedu-blockchain/services/nft"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
)

const (
	InitSponsorWalletMethodName = "initSponsorWallet"
	ExistingSponsorWalletTxHash = "existing_sponsor_wallet"

	InputDataSponsorID   = "sponsor_id"
	InputDataSponsorName = "sponsor_name"
	InputDataDescription = "description"
	InputDataAmount      = "amount"
)

var (
	ErrGeneratePrivateKey       = "Generate private key error: "
	ErrEncryptPrivateKey        = "Encrypt private key error: "
	ErrOnlyBASENetworkSupported = "Only BASE network is supported for sponsor wallets"
	ErrFindSponsorWallet        = "Find sponsor wallet error: "
	ErrCreateTransaction        = "Create transaction error: "
	ErrCreateSponsorWallet      = "Create sponsor wallet error: "
	ErrUpdateTransaction        = "Update transaction error: "
	ErrFindSponsorWallets       = "Find sponsor wallets error: "
	ErrSponsorWalletNotFound    = "Sponsor wallet not found"
)

type SponsorWalletService struct {
	transferService *crypto_transfer.TransferService
	nftService      *nft.MintNftService
}

type SponsorWalletServiceDeps struct {
	TransferService *crypto_transfer.TransferService
	NftService      *nft.MintNftService
}

func NewSponsorWalletService(deps *SponsorWalletServiceDeps) *SponsorWalletService {
	return &SponsorWalletService{
		transferService: deps.TransferService,
		nftService:      deps.NftService,
	}
}

func (s *SponsorWalletService) generateWalletCredentials() (address string, encryptedPrivateKey string, appErr *e.AppError) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", e.NewError500(e.Error, ErrGeneratePrivateKey+err.Error())
	}

	address = crypto.PubkeyToAddress(privateKey.PublicKey).Hex()

	privateKeyBytes := crypto.FromECDSA(privateKey)
	privateKeyHex := hexutil.Encode(privateKeyBytes)
	encryptedPrivateKey, err = kms.Encrypt(privateKeyHex)
	if err != nil {
		return "", "", e.NewError500(e.Error, ErrEncryptPrivateKey+err.Error())
	}

	return address, encryptedPrivateKey, nil
}

func (s *SponsorWalletService) InitSponsorWallet(req *dto.InitSponsorWalletRequest) (*models.Transaction, *e.AppError) {
	// Find the wallet
	wallet, appErr := Wallet.FindByID(req.WalletID, &models.FindOneOptions{})
	if appErr != nil {
		return nil, appErr
	}

	// Check if wallet network is BASE
	if wallet.Network != models.NetworkBASE {
		return nil, e.NewError400(e.InvalidParams, ErrOnlyBASENetworkSupported)
	}

	existingSponsor, err := models.Repository.SponsorWallet.FindOne(&models.SponsorWalletQuery{
		SponsorID: &req.SponsorID,
		Network:   &req.Network,
	}, nil)

	if err != nil && !models.IsRecordNotFound(err) {
		return nil, e.NewError500(e.Error, ErrFindSponsorWallet+err.Error())
	}

	if existingSponsor != nil {
		// Return success for idempotent behavior instead of error
		log.Infof("Sponsor wallet already exists for sponsor_id: %s, network: %s - skipping creation", req.SponsorID, req.Network)

		// Create a mock transaction response for existing wallet
		mockTransaction := &models.Transaction{
			WalletID:    req.WalletID,
			FromAddress: wallet.Address,
			FromNetwork: wallet.Network,
			ToAddress:   wallet.Address,
			ToNetwork:   wallet.Network,
			Status:      models.TxStatusSuccess,
			Type:        models.TxnTypeInitSponsorWallet,
			MethodName:  InitSponsorWalletMethodName,
			TxHash:      ExistingSponsorWalletTxHash,
		}

		return mockTransaction, nil
	}

	// Create transaction record
	transaction := &models.Transaction{
		WalletID:    req.WalletID,
		FromAddress: wallet.Address,
		FromNetwork: wallet.Network,
		ToAddress:   wallet.Address,
		ToNetwork:   wallet.Network,
		Status:      models.TxStatusPending,
		Type:        models.TxnTypeInitSponsorWallet,
		MethodName:  InitSponsorWalletMethodName,
		InputData: models.JSONB{
			InputDataSponsorID:   req.SponsorID,
			InputDataSponsorName: req.SponsorName,
			InputDataDescription: req.Description,
			InputDataAmount:      req.Amount,
		},
		Deposit:    req.Amount,
		Token:      req.Token,
		ContractID: "",
		IsMainnet:  req.IsMainnet,
		Props: models.TransactionProps{
			CoreTxIDs: []string{req.CoreTxID},
		},
	}

	// Save the transaction
	if err := models.Repository.Transaction.Create(transaction, nil); err != nil {
		return nil, e.NewError500(e.Error, ErrCreateTransaction+err.Error())
	}

	address, encryptedPrivateKey, appErr := s.generateWalletCredentials()
	if appErr != nil {
		return nil, appErr
	}

	// Create sponsor wallet record with 0 balance initially
	sponsorWallet := &models.SponsorWallet{
		Wallet: models.Wallet{
			Address:             address,
			EncryptedPrivateKey: encryptedPrivateKey,
			Network:             req.Network,
			Status:              models.WalletStatusActive,
		},
		WalletID:    req.WalletID,
		SponsorID:   req.SponsorID,
		SponsorName: req.SponsorName,
		Description: req.Description,
		Balance:     decimal.Zero,
		Status:      models.SponsorWalletStatusActive,
	}

	// Save the sponsor wallet
	if err := models.Repository.SponsorWallet.Create(sponsorWallet, nil); err != nil {
		return nil, e.NewError500(e.Error, ErrCreateSponsorWallet+err.Error())
	}

	// If amount > 0, perform actual ETH transfer to sponsor wallet
	if req.Amount.GreaterThan(decimal.Zero) {
		// Check user wallet balance before attempting transfer
		hasEnoughBalance := true
		client, err := s.getEthClient(req.IsMainnet)
		if err == nil {
			userAddress := common.HexToAddress(wallet.Address)
			balance, err := client.BalanceAt(context.Background(), userAddress, nil)
			if err == nil {
				// Convert amount to Wei
				amountInWei := req.Amount.Mul(decimal.NewFromInt(util.ETH_TO_WEI_MULTIPLIER)).BigInt()
				// Estimate gas (approximately 21000 for simple transfer)
				estimatedGasCost := big.NewInt(21000)
				gasPrice, err := client.SuggestGasPrice(context.Background())
				if err == nil {
					estimatedGasCost = estimatedGasCost.Mul(estimatedGasCost, gasPrice)
					totalCost := new(big.Int).Add(amountInWei, estimatedGasCost)

					if balance.Cmp(totalCost) < 0 {
						hasEnoughBalance = false
						balanceEth := decimal.NewFromBigInt(balance, 0).Div(decimal.NewFromInt(util.ETH_TO_WEI_MULTIPLIER))
						requiredEth := decimal.NewFromBigInt(totalCost, 0).Div(decimal.NewFromInt(util.ETH_TO_WEI_MULTIPLIER))

						log.Warnf("User wallet has insufficient funds for sponsor wallet deposit: balance(%s ETH) < required(%s ETH). Sponsor wallet created without initial funding.",
							balanceEth.String(), requiredEth.String())
					}
				}
			}
			client.Close()
		}

		if hasEnoughBalance {
			transferResult, err := s.nftService.DepositSponsorGas(wallet, &nft.DepositSponsorGasRequest{
				Amount:     req.Amount,
				CourseCuid: "",
				IsMainnet:  req.IsMainnet,
			})

			if err != nil {
				log.Errorf("Failed to transfer ETH to sponsor wallet: %v", err)
				transaction.Status = models.TxStatusFailed
				transaction.ErrorCode = e.TransactionDepositSponsorGasFailed
				transaction.Response = models.JSONB{
					"code":    e.TransactionDepositSponsorGasFailed,
					"message": "Failed to transfer ETH to sponsor wallet: " + err.Error(),
				}
			} else {
				transaction.Status = transferResult.Status
				transaction.TxHash = transferResult.TxHash
				transaction.BlockHash = transferResult.BlockHash
				transaction.GasLimit = transferResult.GasLimit
				transaction.GasBurnt = transferResult.GasBurnt
				transaction.Nonce = transferResult.Nonce
				transaction.ToAddress = sponsorWallet.Address
				transaction.ContractID = transferResult.ContractID

				if transferResult.Status == models.TxStatusSuccess {
					transaction.Response = models.JSONB{
						"code":    e.Success,
						"message": "Sponsor wallet created and funded successfully",
					}
					log.Infof("Successfully transferred %s ETH to sponsor wallet %s", req.Amount.String(), sponsorWallet.Address)
				} else {
					transaction.Response = models.JSONB{
						"code":    e.TransactionDepositSponsorGasFailed,
						"message": "ETH transfer failed: " + transferResult.ErrorMsg,
					}
				}
			}
		} else {
			// User doesn't have enough balance, but create sponsor wallet successfully without funding
			transaction.Status = models.TxStatusSuccess
			transaction.TxHash = util.GenerateId()
			transaction.Response = models.JSONB{
				"code":    e.Success,
				"message": "Sponsor wallet created successfully. Note: Initial funding skipped due to insufficient balance in user wallet.",
			}
			log.Infof("Sponsor wallet created without initial funding due to insufficient user wallet balance")
		}
	} else {
		transaction.Status = models.TxStatusSuccess
		transaction.TxHash = util.GenerateId()
		transaction.Response = models.JSONB{
			"code":    e.Success,
			"message": "Sponsor wallet created successfully (no initial funding)",
		}
	}

	if err := models.Repository.Transaction.Update(transaction, nil); err != nil {
		return nil, e.NewError500(e.Error, ErrUpdateTransaction+err.Error())
	}

	return transaction, nil
}

func (s *SponsorWalletService) FindPage(query *models.SponsorWalletQuery, options *models.FindPageOptions) ([]*models.SponsorWallet, *models.Pagination, *e.AppError) {
	sponsorWallets, pagination, err := models.Repository.SponsorWallet.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.Error, ErrFindSponsorWallets+err.Error())
	}

	return sponsorWallets, pagination, nil
}

func (s *SponsorWalletService) FindByID(id string) (*models.SponsorWallet, *e.AppError) {
	sponsorWallet, err := models.Repository.SponsorWallet.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.WalletNotFound, ErrSponsorWalletNotFound)
		}
		return nil, e.NewError500(e.Error, ErrFindSponsorWallet+err.Error())
	}

	return sponsorWallet, nil
}

func (s *SponsorWalletService) FindByQuery(query *models.SponsorWalletQuery) (*models.SponsorWallet, *e.AppError) {
	sponsorWallet, err := models.Repository.SponsorWallet.FindOne(query, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.WalletNotFound, ErrSponsorWalletNotFound)
		}
		return nil, e.NewError500(e.Error, ErrFindSponsorWallet+err.Error())
	}

	return sponsorWallet, nil
}

// getEthClient returns an Ethereum client based on the isMainnet flag
func (s *SponsorWalletService) getEthClient(isMainnet bool) (*ethclient.Client, error) {
	var urls []string
	if isMainnet {
		urls = setting.EvmSetting.MainnetURLs
	} else {
		urls = setting.EvmSetting.TestnetURLs
	}

	if len(urls) == 0 {
		networkName := "mainnet"
		if !isMainnet {
			networkName = "testnet"
		}
		return nil, fmt.Errorf("no RPC URLs configured for %s", networkName)
	}

	client, err := ethclient.Dial(urls[0])
	if err != nil {
		log.Errorf("Failed to connect to ETH client: %v", err)
		return nil, err
	}

	return client, nil
}
