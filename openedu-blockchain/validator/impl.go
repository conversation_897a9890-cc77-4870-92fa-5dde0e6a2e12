package validator

import (
	"openedu-blockchain/pkg/log"
	"reflect"
	"strings"
	"sync"

	"github.com/go-playground/validator/v10"
)

type validatorImpl struct {
	once     sync.Once
	validate *validator.Validate
}

func (v *validatorImpl) ValidateStruct(obj interface{}) error {
	if kindOfData(obj) == reflect.Struct {
		v.lazyinit()
		if err := v.validate.Struct(obj); err != nil {
			return err
		}
	}
	return nil
}

func (v *validatorImpl) Engine() interface{} {
	v.lazyinit()
	return v.validate
}

func (v *validatorImpl) lazyinit() {
	v.once.Do(func() {
		v.validate = validator.New()
		v.validate.SetTagName("binding")

		v.validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
			name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
			if name == "-" {
				return ""
			}
			return name
		})

		for _, validation := range defaultRegistrations {
			if err := v.validate.RegisterValidation(validation.Tag, validation.Func); err != nil {
				log.Fatalf("register validation %s error: %v", validation.Tag, err)
			}
		}
	})
}

func kindOfData(data interface{}) reflect.Kind {
	value := reflect.ValueOf(data)
	valueType := value.Kind()

	if valueType == reflect.Ptr {
		valueType = value.Elem().Kind()
	}
	return valueType
}
