[server]
#debug or release
RunMode = debug
Host = 0.0.0.0
HttpPort = 8003
ReadTimeout = 60
WriteTimeout = 60
BuildName = 1.0.0

[database]
Type = postgres
User = open_user
Password = open_password
Host = 127.0.0.1
Port = 5432
Name = openedu_blockchain
TablePrefix = openedu_
IdSize = 16
SSLMode = disable

[app]
Name = "OpenEdu Chain"

APIKey = dummy

RuntimeRootPath = runtime/

DefaultPerPage = 10

LogMode=debug
LogSavePath = runtime/logs/
LogSaveName = log
LogFileExt = log
LogSaveTimeFormat = 20060102
LogFileMaxSizeInMB = 100
LogFileMaxAgeInDays = 200
LogFileMaxBackups = 30
LogCompressEnabled = true

KeyManagerProvider = aws_kms
KeyManagerKeyID = dummy

CacheService=redis

EnableAPIFailureAlerts = false
MsTeamWebHookURL = dummy
MsTeamMentionedEmails =

[redis]
Host = 127.0.0.1:6379
Password = OpenEdu2024
MaxIdle = 30
MaxActive = 30
IdleTimeout = 200
PrefixChannel = local_

[rabbitmq]
URL = amqp://open_user:open_password@localhost:5672
Prefix = local_

[aws]
AccessKey = dummy
SecretKey = dummy
Region = ap-southeast-1

[openedu-core]
BaseURL = http://localhost:8000
ApiKey = dummy

[near]
MintCertificateNFTsContractID = certificates.openedu-vbi.testnet
MintCertificateNFTsMethod = nft_mint

# Mainnet
MainnetURLs = https://rpc.mainnet.near.org,https://rpc.mainnet.pagoda.co,https://1rpc.io/eW1q8SqNpVJJzrho/near,https://1rpc.io/near,https://near.blockpi.network/v1/rpc/public

MainnetUSDTContractID = usdt.tether-token.near
MainnetUSDTDecimals = 6

MainnetUSDCContractID = 17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1
MainnetUSDCDecimals = 6

# TODO replace me
MainnetOpenEduContractID = openedutoken.testnet
MainnetOpenEduDecimals = 18

MainnetPaymentContractID = payment.openedu-net.near
MainnetPaymentClaimMethod = claim
MainnetPaymentGetUserInfoMethod = get_user_info_by_id

MainnetNftContractID = certificates.openedu-net.near
MainnetNftMintMethodName = nft_mint
MainnetNftMintWithSponsorMethodName = nft_mint_for_sponsor
MainnetNftMintWithSignatureMethodName = nft_mint_with_signature
MainnetNftDepositSponsorMethodName = deposit_sponsor
MainnetNftGetSponsorBalanceMethodName = get_sponsor_balance
MainnetNftWithdrawSponsorMethodName = withdraw_sponsor

# TODO replace me
MainnetLaunchpadContractID = launchpad.openedu-net.near
MainnetLaunchpadInitPoolMethodName = init_pool
MainnetLaunchpadApprovePoolMethodName = admin_set_status_pool_pre_funding
MainnetLaunchpadPledgeMethodName = ft_transfer_call
MainnetLaunchpadUpdatePoolFundingTimeMethodName = change_pool_funding_time
MainnetLaunchpadCancelPoolMethodName = cancel_pool
MainnetLaunchpadGetUserRecordsByPoolMethodName = get_user_records_by_pool_id
MainnetLaunchpadGetPoolDetailsMethodName = get_detail_pool
MainnetLaunchpadCheckFundingResultMethodName = check_funding_result
MainnetLaunchpadWithdrawToCreatorMethodName = withdraw_to_creator
MainnetLaunchpadCreatorAcceptVotingMethodName = creator_accept_voting
MainnetLaunchpadSetFundingTimeMethodName = set_funding_pool_by_creator
MainnetLaunchpadClaimRefundMethodName = claim_refund
MainnetLaunchpadUpdatePoolStatusMethodName = update_pool_status

# Testnet
TestnetURLs = https://rpc.testnet.near.org,https://rpc.testnet.pagoda.co,https://test.rpc.fastnear.com

TestnetUSDTContractID = usdt.openedu-vbi.testnet
TestnetUSDTDecimals = 6

TestnetUSDCContractID = usdc.openedu-vbi.testnet
TestnetUSDCDecimals = 6

TestnetOpenEduContractID = openedutoken.testnet
TestnetOpenEduDecimals = 18

TestnetPaymentContractID = payment.openedu-vbi.testnet
TestnetPaymentClaimMethod = claim
TestnetPaymentGetUserInfoMethod = get_user_info_by_id

TestnetNftContractID = openedu-vbi-certificates.testnet
TestnetNftMintMethodName = nft_mint
TestnetNftMintWithSponsorMethodName = nft_mint_for_sponsor
TestnetNftMintWithSignatureMethodName = nft_mint_with_signature
TestnetNftDepositSponsorMethodName = deposit_sponsor
TestnetNftGetSponsorBalanceMethodName = get_sponsor_balance
TestnetNftWithdrawSponsorMethodName = withdraw_sponsor

TestnetLaunchpadContractID = test-launchpad-8.testnet
TestnetLaunchpadInitPoolMethodName = init_pool
TestnetLaunchpadApprovePoolMethodName = admin_set_status_pool_pre_funding
TestnetLaunchpadPledgeMethodName = ft_transfer_call
TestnetLaunchpadUpdatePoolFundingTimeMethodName = change_pool_funding_time
TestnetLaunchpadCancelPoolMethodName = cancel_pool
TestnetLaunchpadGetUserRecordsByPoolMethodName = get_user_records_by_pool_id
TestnetLaunchpadGetPoolDetailsMethodName = get_detail_pool
TestnetLaunchpadCheckFundingResultMethodName = check_funding_result
TestnetLaunchpadWithdrawToCreatorMethodName = withdraw_to_creator
TestnetLaunchpadCreatorAcceptVotingMethodName = creator_accept_voting
TestnetLaunchpadSetFundingTimeMethodName = set_funding_pool_by_creator
TestnetLaunchpadClaimRefundMethodName = claim_refund
TestnetLaunchpadUpdatePoolStatusMethodName = update_pool_status

[avail]
MainnetURLs = wss://avail-mainnet.public.blastapi.io/,wss://mainnet.avail-rpc.com/
TestnetURLs = wss://turing-rpc.avail.so/ws

[evm]
MainnetURLs = https://mainnet.base.org,https://base-mainnet.public.blastapi.io
MainnetChainID = 8453
MainnetUSDCContractAddress = ******************************************
MainnetPaymentContractAddress =
MainnetVaultContractAddress =

TestnetURLs = https://base-sepolia.g.alchemy.com/v2/********************************
TestnetChainID = 84532
TestnetUSDCContractAddress = ******************************************
TestnetPaymentContractAddress = ******************************************
TestnetNFTCertificateAddress = ******************************************
TestnetVaultContractAddress = ******************************************

[paymaster]
PaymasterURL = https://opendu-paymaster-testing.up.railway.app
PaymasterAPIKey = kP9mRxLw3VbTzY8qN6sFgH2jKpQr7CvXuA4nBtE5yWcZ1iMoUeRlDhGpSaFxJvK3

[pinata]
ApiKey = a8d5b62614ffa41c3166
ApiSecret = 430b721c8c568b199fdf6e6d0ffe2410fdcb1b792260139db00b84d480753df8
JWT = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xkGwDtG9QhuKTAyIISV1JSDIvhwXk9ijs-BE6c2C0UA
BaseURL = https://api.pinata.cloud
Gateway = https://peach-important-centipede-570.mypinata.cloud/ipfs