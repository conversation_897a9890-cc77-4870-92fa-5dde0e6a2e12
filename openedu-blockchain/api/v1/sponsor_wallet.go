package v1

import (
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// GetSponsorWallets gets all sponsor wallets
//
//	@Summary		Get all sponsor wallets
//	@Description	Get all sponsor wallets with pagination
//
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Param			wallet_id	query		string	false	"Filter by wallet ID"
//	@Param			sponsor_id	query		string	false	"Filter by sponsor ID"
//	@Param			network		query		string	false	"Filter by network"
//	@Param			status		query		string	false	"Filter by status"
//	@Param			page		query		int		false	"Page number"
//	@Param			page_size	query		int		false	"Page size"
//	@Success		200			{object}	app.ResponseT[dto.ListSponsorWalletsResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [GET]
func GetSponsorWallets(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.InvalidParams, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.SponsorWalletQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.InvalidParams, "Bind query failed: "+err.Error())
		return
	}

	sponsorWallets, pagination, appErr := services.SponsorWallet.FindPage(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(dto.ListSponsorWalletsResponse{
		Results: lo.Map(sponsorWallets, func(item *models.SponsorWallet, _ int) *models.SanitizedSponsorWallet {
			return item.ToSanitized()
		}),
		Pagination: pagination,
	})
}

// GetSponsorWalletByID gets a sponsor wallet by ID
//
//	@Summary		Get a sponsor wallet by ID
//	@Description	Get a sponsor wallet by ID
//
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Param			id	path		string	true	"Sponsor wallet ID"
//	@Success		200	{object}	app.ResponseT[models.SanitizedSponsorWallet]
//	@Failure		400	{object}	app.ResponseT[app.ErrorData]
//	@Failure		404	{object}	app.ResponseT[app.ErrorData]
//	@Failure		500	{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets/{id} [GET]
func GetSponsorWalletByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	sponsorWallet, appErr := services.SponsorWallet.FindByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(sponsorWallet.ToSanitized())
}
