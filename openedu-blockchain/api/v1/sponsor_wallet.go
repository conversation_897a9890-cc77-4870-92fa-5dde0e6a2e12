package v1

import (
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services"

	"github.com/gin-gonic/gin"
)

// GetSponsorWallets gets all sponsor wallets
//
//	@Summary		Get all sponsor wallets
//	@Description	Get all sponsor wallets with pagination
//
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Param			wallet_id	query		string	false	"Filter by wallet ID"
//	@Param			sponsor_id	query		string	false	"Filter by sponsor ID"
//	@Param			network		query		string	false	"Filter by network"
//	@Param			status		query		string	false	"Filter by status"
//	@Param			page		query		int		false	"Page number"
//	@Param			page_size	query		int		false	"Page size"
//	@Success		200			{object}	app.ResponseT[dto.ListSponsorWalletsResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [GET]
func GetSponsorWallets(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.InvalidParams, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.SponsorWalletQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.InvalidParams, "Bind query failed: "+err.Error())
		return
	}

	sponsorWallets, pagination, appErr := services.SponsorWallet.GetSponsorWallets(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(dto.ListSponsorWalletsResponse{
		Results:    sponsorWallets,
		Pagination: pagination,
	})
}

// GetSponsorWalletByID gets a sponsor wallet by ID
//
//	@Summary		Get a sponsor wallet by ID
//	@Description	Get a sponsor wallet by ID
//
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Param			id	path		string	true	"Sponsor wallet ID"
//	@Success		200	{object}	app.ResponseT[models.SponsorWallet]
//	@Failure		400	{object}	app.ResponseT[app.ErrorData]
//	@Failure		404	{object}	app.ResponseT[app.ErrorData]
//	@Failure		500	{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets/{id} [GET]
func GetSponsorWalletByID(c *gin.Context) {
	appG := app.Gin{C: c}
	id := c.Param("id")

	sponsorWallet, appErr := services.SponsorWallet.GetSponsorWalletByID(id)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(sponsorWallet)
}

// CreateSponsorWallet creates a new sponsor wallet
//
//	@Summary		Create a new sponsor wallet
//	@Description	Create a new sponsor wallet with the provided details
//
//	@Tags			sponsor_wallet
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Param			request	body		dto.InitSponsorWalletRequest	true	"Sponsor wallet creation request"
//	@Success		201		{object}	app.ResponseT[dto.CreateSponsorWalletResponse]
//	@Failure		400		{object}	app.ResponseT[app.ErrorData]
//	@Failure		500		{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/sponsor-wallets [POST]
func CreateSponsorWallet(c *gin.Context) {
	appG := app.Gin{C: c}

	var req dto.InitSponsorWalletRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appG.Response400(e.InvalidParams, "Bind JSON failed: "+err.Error())
		return
	}

	transaction, appErr := services.SponsorWallet.InitSponsorWallet(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	// Get the created sponsor wallet
	sponsorWallet, appErr := services.SponsorWallet.GetSponsorWalletByQuery(&models.SponsorWalletQuery{
		SponsorID: &req.SponsorID,
		Network:   &req.Network,
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	response := dto.CreateSponsorWalletResponse{
		Transaction:   transaction,
		SponsorWallet: sponsorWallet,
	}

	appG.Response201(response)
}
