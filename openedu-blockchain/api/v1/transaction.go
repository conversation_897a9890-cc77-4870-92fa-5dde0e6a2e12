package v1

import (
	"github.com/gin-gonic/gin"
	"openedu-blockchain/dto"
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services"
)

func MintNFT(c *gin.Context) {
	appG := app.Gin{C: c}

	var req dto.MintNFTRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appG.Response400(e.InvalidParams, "Bind JSON failed: "+err.Error())
		return
	}

	resp, appErr := services.Transaction.MintNFT(&req)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(resp)
}
