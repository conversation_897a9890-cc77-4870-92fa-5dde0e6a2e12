package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"openedu-blockchain/dto"
	"openedu-blockchain/models"
	"openedu-blockchain/pkg/app"
	"openedu-blockchain/pkg/e"
	"openedu-blockchain/services"
)

// GetWallets
//
//	@Summary		Get wallets by users
//	@Description	Get wallets by users
//
//	@Tags			user
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Success		200			{object}	app.ResponseT[dto.ListWalletsResponse]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/wallets [GET]
func GetWallets(c *gin.Context) {
	appG := app.Gin{C: c}
	var options models.FindPageOptions
	if err := appG.BindFindPageOptions(&options); err != nil {
		appG.Response400(e.InvalidParams, "Bind find page options failed: "+err.Error())
		return
	}

	var query models.WalletQuery
	if err := appG.C.BindQuery(&query); err != nil {
		appG.Response400(e.InvalidParams, "Bind query failed: "+err.Error())
		return
	}

	wallets, pagination, appErr := services.Wallet.FindPage(&query, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(dto.ListWalletsResponse{
		Results: lo.Map(wallets, func(item *models.Wallet, _ int) *models.SimpleWallet {
			return item.ToSimple()
		}),
		Pagination: pagination,
	})
}

// GetWalletByID
//
//	@Summary		Get wallets by users
//	@Description	Get wallets by users
//
//	@Tags			user
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Success		200			{object}	app.ResponseT[models.SimpleWallet]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/wallets/:id [GET]
func GetWalletByID(c *gin.Context) {
	appG := app.Gin{C: c}
	walletID := c.Param("id")
	var options models.FindOneOptions
	if err := appG.C.BindQuery(&options); err != nil {
		appG.Response400(e.InvalidParams, "Bind find one failed: "+err.Error())
		return
	}

	wallet, appErr := services.Wallet.FindByID(walletID, &options)
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(wallet.ToSimple())
}

// GetEarningsByUser
//
//	@Summary		Get wallets by users
//	@Description	Get wallets by users
//
//	@Tags			user
//	@Accept			json
//	@Produce		json
//	@Security		ApiKeyAuth
//
//	@Success		200			{object}	app.ResponseT[models.SimpleWallet]
//	@Failure		400			{object}	app.ResponseT[app.ErrorData]
//	@Failure		500			{object}	app.ResponseT[app.ErrorData]
//	@Router			/api/v1/users/:id/earnings [GET]
func GetEarningsByUser(c *gin.Context) {
	appG := app.Gin{C: c}
	userID := c.Param("id")
	earnings, appErr := services.Wallet.GetEarningsByUser(&dto.GetWalletEarningsRequest{
		UserID:    userID,
		IsMainnet: c.Query("is_mainnet") == "true",
	})
	if appErr != nil {
		appG.ResponseAppError(appErr)
		return
	}

	appG.Response200(earnings)
}
