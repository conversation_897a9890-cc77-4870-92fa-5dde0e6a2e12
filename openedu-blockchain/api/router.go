package api

import (
	v1 "openedu-blockchain/api/v1"
	middleware "openedu-blockchain/middlewares"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// InitRouter initialize routing information
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(middleware.GinLogger())
	r.Use(middleware.GinRecovery())

	// Health check
	r.GET("/api/chain-v1/health", v1.CheckHealth)

	r.Use(middleware.CORSMiddleware())
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	apiV1 := r.Group("/api/chain-v1")
	apiV1.Use(middleware.BeforeInterceptor())
	{
		// Wallet
		user := apiV1.Group("/users")
		user.GET("/:id", v1.GetEarningsByUser)

		// Wallet
		wallet := apiV1.Group("/wallets")
		wallet.GET("", v1.GetWallets)
		wallet.GET("/:id", v1.GetWalletByID)

		// Sponsor Wallet - All operations via message queue only (like wallet)
		// openedu-core sends messages to openedu-blockchain via RabbitMQ
		// No REST API endpoints - use message queue for all operations

		// Transaction
		transaction := apiV1.Group("/transactions")
		transaction.POST("/mint-nft", v1.MintNFT)

		// Cache
		cache := apiV1.Group("/caches")
		cache.DELETE("", v1.DeleteCaches)
		cache.DELETE("/:key", v1.DeleteCacheByKey)
	}
	return r
}
