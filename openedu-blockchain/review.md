models/sponsor_wallet.go   SponsorWalletStatusInactive = "inactive" ) type SponsorWallet struct { @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Sponsor wallet bản chất cũng là một wallet. Sao em không kế thừa từ models.Wallet mà lại phải tạo một model mới? @pichgfi  Reply... api/v1/sponsor_wallet.go   }   appG.Response200(dto.ListSponsorWalletsResponse{     Results:    sponsorWallets, @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Sanitize (ẩn các field secrect) trước khi trả về giúp anh. Chỉ openedu-blockchain được thấy encrypted_private_key, còn các service khác thì không được xem em. @pichgfi  Reply... api/v1/sponsor_wallet.go     return   }   appG.Response200(sponsorWallet) @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Sanitize (ẩn các field secrect) trước khi trả về giúp anh. Chỉ openedu-blockchain được thấy encrypted_private_key, còn các service khác thì không được xem em. @pichgfi  Reply... api/v1/sponsor_wallet.go   response := dto.CreateSponsorWalletResponse{     Transaction:   transaction,     SponsorWallet: sponsorWallet, @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Sanitize (ẩn các field secrect) trước khi trả về giúp anh. Chỉ openedu-blockchain được thấy encrypted_private_key, còn các service khác thì không được xem em. @pichgfi  Reply... config/app.ini.template ApiKey = a8d5b62614ffa41c3166 ApiSecret = 430b721c8c568b199fdf6e6d0ffe2410fdcb1b792260139db00b84d480753df8 JWT = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xkGwDtG9QhuKTAyIISV1JSDIvhwXk9ijs-BE6c2C0UA @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Tại sao lại cần JWT ở đây em. Về lý thuyết thì JWT có expire time. Nếu em configure thế này thì sẽ có lúc JWT hết hạn và service sẽ lỗi @pichgfi  Reply... services/sponsor_wallet.go       ToNetwork:   wallet.Network,       Status:      models.TxStatusSuccess,       Type:        models.TxnTypeInitSponsorWallet,       MethodName:  "initSponsorWallet", @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Refactor thành constant giúp anh @pichgfi  Reply... services/sponsor_wallet.go     return nil, e.NewError500(e.Error, "Create transaction error: "+err.Error())   }   // Generate new pk and address for sponsor wallet @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Logic tạo sponsor wallet pk và address khác phức tạp. Refactor thành một function giúp anh để dễ maintain @pichgfi  Reply... services/sponsor_wallet.go } // GetSponsorWallets gets all sponsor wallets for a user func (s *SponsorWalletService) GetSponsorWallets(query *models.SponsorWalletQuery, options *models.FindPageOptions) ([]*models.SponsorWallet, *models.Pagination, *e.AppError) { @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Đổi lại giúp anh tên method. Lúc dùng đã có đủ context. Không nhất thiết phải duplicate từ SponsorWallets. Ví dụ services.SponsorWallet.GetSponsorWalletByID(id) -> services.SponsorWallet.FindByID(id). Tương tự cho các methods khác của service.SponsorWallet @pichgfi  Reply... services/wallet.go @@ -270,6 +281,23 @@ func (s *WalletService) GetGasSponsorBalance(req *dto.GetWalletGasSponsorBalance   return util.ParseNearYocto2Amount(balanceInYoctoNEARS), nil } func (s *WalletService) getBaseGasSponsorBalance(wallet *models.Wallet, req *dto.GetWalletGasSponsorBalanceRequest) (decimal.Decimal, *e.AppError) {   _ = req @phuongtt-vbi phuongtt-vbi 2 days ago @pichgfi Tại sao lại cần dòng này vậy em