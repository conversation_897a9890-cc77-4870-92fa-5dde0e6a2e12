package cache_clients

import (
	"fmt"
)

type Caching struct {
	User         UserCacheIface
	Permission   PermissionCacheIface
	System       SystemConfigCacheIface
	Organization OrganizationCacheIface
	Category     CategoryCacheIface
	UserRole     UserRoleCacheIface
	Form         FormCacheIface
	PageAccess   PageAccessCacheIface
	Wallet       WalletCacheIface
	Quiz         QuizCacheIface
	Blog         BlogCacheIface
}

func (c *Caching) Convert(input, output interface{}) error {
	return convertValue(input, output)
}

func makeCacheKey(prefix string, key string, otherKeys ...string) string {
	fullKey := prefix + key
	for _, other := range otherKeys {
		other = "_" + other
		fullKey += other

	}
	return fullKey
}

func (c *Caching) DeleteAll() error {
	var dErr error
	if err := c.User.Flush(); err != nil {
		dErr = fmt.Errorf("delete all user cache: %w", err)
	}
	if err := c.Permission.Flush(); err != nil {
		dErr = fmt.Errorf("delete all permission cache: %w", err)
	}
	if err := c.System.Flush(); err != nil {
		dErr = fmt.Errorf("delete all system cache: %w", err)
	}
	if err := c.Organization.Flush(); err != nil {
		dErr = fmt.Errorf("delete all organization cache: %w", err)
	}
	if err := c.Category.Flush(); err != nil {
		dErr = fmt.Errorf("delete all category cache: %w", err)
	}
	if err := c.UserRole.Flush(); err != nil {
		dErr = fmt.Errorf("delete all user role cache: %w", err)
	}
	if err := c.Form.Flush(); err != nil {
		dErr = fmt.Errorf("delete all form cache: %w", err)
	}
	if err := c.Quiz.Flush(); err != nil {
		dErr = fmt.Errorf("delete all quiz cache: %w", err)
	}
	if err := c.Wallet.DeleteAll(); err != nil {
		dErr = fmt.Errorf("delete all wallet cache: %w", err)
	}
	return dErr
}

type CacheModel struct {
	Prefix string
}

type BlogCache struct {
	CacheModel
}

type UserCache struct {
	CacheModel
}

type PermissionCache struct {
	CacheModel
}

type SystemConfigCache struct {
	CacheModel
}

type OrganizationCache struct {
	CacheModel
}

type CategoryCache struct {
	CacheModel
}

type UserRoleCache struct {
	CacheModel
}

type FormCache struct {
	CacheModel
}

type PageAccessCache struct {
	CacheModel
}

type WalletCache struct {
	CacheModel
}

type QuizCache struct {
	CacheModel
}

type UserCacheIface interface {
	SetUser(id string, user interface{}) error
	GetByUserID(userID string, user interface{}) error
	DeleteByUserID(userID string) error
	DeleteByKey(key string) error
	Flush() error
}

type PermissionCacheIface interface {
	SetAll(permissions []interface{}) error
	FindAll(permissions *[]interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type SystemConfigCacheIface interface {
	Set(systemKey string, system interface{}) error
	Get(cfgKey string, config interface{}) error
	SetAll(cacheKey string, systems []interface{}) error
	GetAll(cacheKey string, systems *[]interface{}) error
	Delete(sType string) error
	DeleteByKey(key string) error
	Flush() error
}

type OrganizationCacheIface interface {
	Set(key string, org interface{}) error
	Get(key string, org interface{}) error
	DeleteByKey(key string) error
	Flush() error
}

type CategoryCacheIface interface {
	GetAllCourseCategories(results []interface{}) error
	GetAllBlogCategories(results []interface{}) error
	GetAllLevels(results []interface{}) error

	SetAllCourseCategories(categories []interface{}) error
	SetAllBlogCategories(categories []interface{}) error
	SetAllLevels(categories []interface{}) error

	DeleteByKey(key string) error
	Flush() error
}

type UserRoleCacheIface interface {
	SetUserRole(userID string, roles []interface{}) error
	GetByUserID(userID string, roles []interface{}) error
	DeleteByUserID(userID string) error
	DeleteByKey(key string) error
	Flush() error
}

type FormCacheIface interface {
	SetFormByID(id string, form interface{}) error
	SetFormBySlug(slug string, form interface{}) error
	SetFormByKey(key string, form interface{}) error

	GetByFormID(formId string, form interface{}) error
	GetByFormSlug(slug string, form interface{}) error
	GetByKey(key string, form interface{}) error

	DeleteByFormID(id string) error
	DeleteByFormSlug(slug string) error
	DeleteByKey(key string) error
	Flush() error
}

type PageAccessCacheIface interface {
	SetAll(results []interface{}) error
	GetAll(results []interface{}) error
	DeleteAll() error
}

type QuizCacheIface interface {
	SetQuizByID(id string, quiz interface{}) error
	GetByQuizID(quizID string, quiz interface{}) error
	DeleteByQuizID(id string) error
	Flush() error
}

type WalletCacheIface interface {
	SetByUser(userID string, wallets []interface{}) error
	GetByUser(userID string, wallets *[]interface{}) error
	DeleteByUserID(userID string) error
	DeleteByKey(key string) error
	DeleteAll() error
}

type BlogCacheIface interface {
	Set(key string, blog interface{}) error
	Get(key string, blog interface{})
	SetManyBlogByCategory(blogByCategory map[string][]interface{}) error
	GetManyBlogByCategory(categoryIDs []string, blogs map[string][]interface{})
	DeleteBlogByManyCategory(categoryIDs []string) error
	DeleteAllBlogByCategory() error
}
