package dto

import (
	"github.com/shopspring/decimal"
	"openedu-blockchain/models"
)

type MintNFTRequest struct {
	GasFee<PERSON>ayer        string                   `json:"gas_fee_payer"`
	CourseCuid         string                   `json:"course_cuid"`
	CourseOwnerAddress string                   `json:"course_owner_address"`
	TokenMetadata      TokenMetadataRequest     `json:"token_metadata"`
	ReceiverWalletID   string                   `json:"receiver_wallet_id"`
	CoreTxID           string                   `json:"core_tx_id"`
	Network            models.BlockchainNetwork `json:"network"`
	IsMainnet          bool                     `json:"is_mainnet"`
}

type TokenMetadataRequest struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	MediaURL    string `json:"media_url"`
}

type SingleTransferRequest struct {
	SenderWalletID string                   `json:"sender_wallet_id"`
	CoreTxID       string                   `json:"core_tx_id"`
	Amount         decimal.Decimal          `json:"amount"`
	Token          models.BlockchainToken   `json:"token"`
	ContractID     string                   `json:"contract_id"`
	ToAddress      string                   `json:"to_address"`
	Network        models.BlockchainNetwork `json:"network"`
	IsMainnet      bool                     `json:"is_mainnet"`
}

type BatchTransferRecipient struct {
	Address string          `json:"address"`
	Amount  decimal.Decimal `json:"amount"`
}

type BatchTransferRequest struct {
	SenderWalletID string                    `json:"sender_wallet_id"`
	CoreTxID       string                    `json:"core_tx_id"`
	Recipients     []*BatchTransferRecipient `json:"recipients"`
	TokenID        string                    `json:"token_id"`
	Token          models.BlockchainToken    `json:"token"`
	Network        models.BlockchainNetwork  `json:"network"`
	IsMainnet      bool                      `json:"is_mainnet"`
}

type ProfitDistribution struct {
	Address string          `json:"address"`
	Amount  decimal.Decimal `json:"amount"`
}

type PaymentRequest struct {
	WalletID            string                 `json:"wallet_id"`
	CourseCUID          string                 `json:"course_cuid"`
	CoreTxID            string                 `json:"core_tx_id"`
	Token               models.BlockchainToken `json:"token"`
	Amount              decimal.Decimal        `json:"amount"`
	Fee                 decimal.Decimal        `json:"fee"`
	ProfitDistributions []*ProfitDistribution  `json:"profit_distributions"`
	IsMainnet           bool                   `json:"is_mainnet"`
}

type ClaimEarningRequest struct {
	WalletID  string                 `json:"wallet_id"`
	CoreTxID  string                 `json:"core_tx_id"`
	Token     models.BlockchainToken `json:"token"`
	Amount    decimal.Decimal        `json:"amount"`
	Fee       decimal.Decimal        `json:"fee"`
	IsMainnet bool                   `json:"is_mainnet"`
}

type CreateTransactionRequest struct {
	Type models.TransactionType `json:"type"`
	Data interface{}            `json:"data"`
}

type DepositSponsorGasRequest struct {
	WalletID   string                   `json:"wallet_id"`
	CoreTxID   string                   `json:"core_tx_id"`
	Amount     decimal.Decimal          `json:"amount"`
	Token      models.BlockchainToken   `json:"token"`
	Network    models.BlockchainNetwork `json:"network"`
	CourseCuid string                   `json:"course_cuid"`
	IsMainnet  bool                     `json:"is_mainnet"`
}

type WithdrawSponsorGasRequest struct {
	WalletID   string                   `json:"wallet_id"`
	CoreTxID   string                   `json:"core_tx_id"`
	Amount     decimal.Decimal          `json:"amount"`
	Token      models.BlockchainToken   `json:"token"`
	Network    models.BlockchainNetwork `json:"network"`
	CourseCuid string                   `json:"course_cuid"`
	IsMainnet  bool                     `json:"is_mainnet"`
}

type InitSponsorWalletRequest struct {
	WalletID    string                   `json:"wallet_id"`
	CoreTxID    string                   `json:"core_tx_id"`
	SponsorID   string                   `json:"sponsor_id"`   
	SponsorName string                   `json:"sponsor_name"` 
	Description string                   `json:"description"`
	Amount      decimal.Decimal          `json:"amount"`
	Token       models.BlockchainToken   `json:"token"`
	Network     models.BlockchainNetwork `json:"network"`
	IsMainnet   bool                     `json:"is_mainnet"`
}

type InitLaunchpadPoolRequest struct {
	WalletID         string                 `json:"wallet_id"`
	CoreTxID         string                 `json:"core_tx_id"`
	LaunchpadID      string                 `json:"launchpad_id"`
	Token            models.BlockchainToken `json:"token"`
	MinPledge        decimal.Decimal        `json:"min_pledge"`
	FundingStartDate int64                  `json:"funding_start_date"`
	FundingEndDate   int64                  `json:"funding_end_date"`
	TargetFunding    decimal.Decimal        `json:"target_funding"`
	IsMainnet        bool                   `json:"is_mainnet"`
}

type ApproveLaunchpadPoolRequest struct {
	PoolID     string                   `json:"pool_id"`
	IsApproved bool                     `json:"is_approved"`
	Network    models.BlockchainNetwork `json:"network"`
	IsMainnet  bool                     `json:"is_mainnet"`
}

type PledgeLaunchpadRequest struct {
	WalletID  string                 `json:"wallet_id"`
	CoreTxID  string                 `json:"core_tx_id"`
	PoolID    string                 `json:"pool_id"`
	Amount    decimal.Decimal        `json:"amount"`
	Token     models.BlockchainToken `json:"token"`
	IsMainnet bool                   `json:"is_mainnet"`
}

type UpdateLpPoolFundingTimeRequest struct {
	PoolID           string                   `json:"pool_id"`
	FundingStartDate int64                    `json:"funding_start_date"`
	FundingEndDate   int64                    `json:"funding_end_date"`
	Network          models.BlockchainNetwork `json:"network"`
	IsMainnet        bool                     `json:"is_mainnet"`
}

type CancelLpPoolRequest struct {
	WalletID  string `json:"wallet_id"`
	PoolID    string `json:"pool_id"`
	IsMainnet bool   `json:"is_mainnet"`
}

type CheckLpFundingResultRequest struct {
	PoolID           string                   `json:"pool_id"`
	IsWaitingFunding bool                     `json:"is_waiting_funding"`
	Network          models.BlockchainNetwork `json:"network"`
	IsMainnet        bool                     `json:"is_mainnet"`
}

type CheckLpFundingResultResponse struct {
	PoolID string `json:"pool_id"`
	Status string `json:"status"`
}

type ContinueLpPartialFundRequest struct {
	PoolID     string                   `json:"pool_id"`
	IsApproved bool                     `json:"is_approved"`
	Network    models.BlockchainNetwork `json:"network"`
	IsMainnet  bool                     `json:"is_mainnet"`
}

type SetLpFundingTimeRequest struct {
	WalletID            string `json:"wallet_id"`
	PoolID              string `json:"pool_id"`
	FundingStartDate    int64  `json:"funding_start_date"`
	FundingDurationDays int    `json:"funding_duration_days"`
	IsMainnet           bool   `json:"is_mainnet"`
}

type WithdrawLaunchpadFundToCreatorRequest struct {
	PoolID    string                   `json:"pool_id"`
	Token     models.BlockchainToken   `json:"token"`
	Amount    decimal.Decimal          `json:"amount"`
	Network   models.BlockchainNetwork `json:"network"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type ClaimLaunchpadRefundRequest struct {
	WalletID  string                 `json:"wallet_id"`
	CoreTxID  string                 `json:"core_tx_id"`
	Token     models.BlockchainToken `json:"token"`
	PoolID    string                 `json:"pool_id"`
	IsMainnet bool                   `json:"is_mainnet"`
}

type UpdateLaunchpadPoolStatusRequest struct {
	PoolID    string                   `json:"pool_id"`
	Status    string                   `json:"status"`
	Network   models.BlockchainNetwork `json:"network"`
	IsMainnet bool                     `json:"is_mainnet"`
}

type CreatePermitRequest struct {
	ReceiverAddress string                   `json:"receiver_address"`
	CourseCuid      string                   `json:"course_cuid"`
	TokenMetadata   TokenMetadataRequest     `json:"token_metadata"`
	Network         models.BlockchainNetwork `json:"network"`
	IsMainnet       bool                     `json:"is_mainnet"`
}

type CreatePermitResponse struct {
	SignatureV        uint8  `json:"signature_v"`
	SignatureR        string `json:"signature_r"`
	SignatureS        string `json:"signature_s"`
	SignatureNonce    int64  `json:"signature_nonce"`
	SignatureDeadline int64  `json:"signature_deadline"`
}

type MintNFTWithPermitRequest struct {
	CourseCuid       string                   `json:"course_cuid"`
	TokenMetadata    TokenMetadataRequest     `json:"token_metadata"`
	ReceiverWalletID string                   `json:"receiver_wallet_id"`
	CoreTxID         string                   `json:"core_tx_id"`
	Network          models.BlockchainNetwork `json:"network"`
	IsMainnet        bool                     `json:"is_mainnet"`
}
