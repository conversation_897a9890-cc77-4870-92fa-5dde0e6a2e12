# OpenEdu Blockchain Deployment Guide

## 📋 Table of Contents
1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Setup](#database-setup)
4. [Service Deployment](#service-deployment)
5. [Configuration](#configuration)
6. [Health Checks](#health-checks)
7. [Monitoring](#monitoring)

## 🔧 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **RAM**: Minimum 8GB, Recommended 16GB
- **CPU**: Minimum 4 cores, Recommended 8 cores
- **Storage**: Minimum 100GB SSD
- **Network**: Stable internet connection

### Required Software
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Go 1.21+
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc

# Install PostgreSQL 14+
sudo apt install postgresql postgresql-contrib -y
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Install RabbitMQ
sudo apt install rabbitmq-server -y
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server

# Install Redis (for caching)
sudo apt install redis-server -y
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Install Nginx (reverse proxy)
sudo apt install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 🌍 Environment Setup

### 1. Create Application User
```bash
# Create openedu user
sudo useradd -m -s /bin/bash openedu
sudo usermod -aG sudo openedu

# Create application directories
sudo mkdir -p /opt/openedu/{core,blockchain}
sudo mkdir -p /var/log/openedu-{core,blockchain}
sudo mkdir -p /etc/openedu/{core,blockchain}

# Set permissions
sudo chown -R openedu:openedu /opt/openedu
sudo chown -R openedu:openedu /var/log/openedu-*
sudo chown -R openedu:openedu /etc/openedu
```

### 2. Clone Repositories
```bash
# Switch to openedu user
sudo su - openedu

# Clone repositories
cd /opt/openedu
git clone https://github.com/your-org/openedu-core.git core
git clone https://github.com/your-org/openedu-blockchain.git blockchain

# Set up Go modules
cd /opt/openedu/core && go mod download
cd /opt/openedu/blockchain && go mod download
```

## 🗄️ Database Setup

### 1. PostgreSQL Configuration
```bash
# Switch to postgres user
sudo su - postgres

# Create databases
createdb openedu_core
createdb openedu_blockchain

# Create database users
psql -c "CREATE USER openedu_core WITH PASSWORD 'secure_password_core';"
psql -c "CREATE USER openedu_blockchain WITH PASSWORD 'secure_password_blockchain';"

# Grant permissions
psql -c "GRANT ALL PRIVILEGES ON DATABASE openedu_core TO openedu_core;"
psql -c "GRANT ALL PRIVILEGES ON DATABASE openedu_blockchain TO openedu_blockchain;"

# Configure PostgreSQL
sudo nano /etc/postgresql/14/main/postgresql.conf
# Set: listen_addresses = 'localhost'
# Set: max_connections = 200

sudo nano /etc/postgresql/14/main/pg_hba.conf
# Add: local   openedu_core     openedu_core                     md5
# Add: local   openedu_blockchain openedu_blockchain             md5

# Restart PostgreSQL
sudo systemctl restart postgresql
```

### 2. Database Migration
```bash
# Run core migrations
cd /opt/openedu/core
go run main.go migrate

# Run blockchain migrations  
cd /opt/openedu/blockchain
go run main.go migrate

# Verify tables created
psql -U openedu_core -d openedu_core -c "\dt"
psql -U openedu_blockchain -d openedu_blockchain -c "\dt"
```

## 🚀 Service Deployment

### 1. Build Applications
```bash
# Build core service
cd /opt/openedu/core
go build -o openedu-core main.go

# Build blockchain service
cd /opt/openedu/blockchain  
go build -o openedu-blockchain main.go

# Make executable
chmod +x /opt/openedu/core/openedu-core
chmod +x /opt/openedu/blockchain/openedu-blockchain
```

### 2. Create Systemd Services

#### Core Service
```bash
sudo nano /etc/systemd/system/openedu-core.service
```

```ini
[Unit]
Description=OpenEdu Core Service
After=network.target postgresql.service rabbitmq-server.service

[Service]
Type=simple
User=openedu
Group=openedu
WorkingDirectory=/opt/openedu/core
ExecStart=/opt/openedu/core/openedu-core
Restart=always
RestartSec=5
Environment=GIN_MODE=release
EnvironmentFile=/etc/openedu/core/.env

# Logging
StandardOutput=append:/var/log/openedu-core/app.log
StandardError=append:/var/log/openedu-core/error.log

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/log/openedu-core

[Install]
WantedBy=multi-user.target
```

#### Blockchain Service
```bash
sudo nano /etc/systemd/system/openedu-blockchain.service
```

```ini
[Unit]
Description=OpenEdu Blockchain Service
After=network.target postgresql.service rabbitmq-server.service

[Service]
Type=simple
User=openedu
Group=openedu
WorkingDirectory=/opt/openedu/blockchain
ExecStart=/opt/openedu/blockchain/openedu-blockchain
Restart=always
RestartSec=5
Environment=GIN_MODE=release
EnvironmentFile=/etc/openedu/blockchain/.env

# Logging
StandardOutput=append:/var/log/openedu-blockchain/app.log
StandardError=append:/var/log/openedu-blockchain/error.log

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/log/openedu-blockchain

[Install]
WantedBy=multi-user.target
```

### 3. Enable and Start Services
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services
sudo systemctl enable openedu-core
sudo systemctl enable openedu-blockchain

# Start services
sudo systemctl start openedu-core
sudo systemctl start openedu-blockchain

# Check status
sudo systemctl status openedu-core
sudo systemctl status openedu-blockchain
```

## ⚙️ Configuration

### 1. Core Service Configuration
```bash
sudo nano /etc/openedu/core/.env
```

```bash
# Server Configuration
PORT=8080
GIN_MODE=release
LOG_LEVEL=info

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=openedu_core
DB_USER=openedu_core
DB_PASSWORD=secure_password_core
DB_SSL_MODE=disable

# RabbitMQ
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_PREFIX=openedu_

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE_HOURS=24

# Blockchain Service
OPENEDU_CHAIN_MAINNET=false
OPENEDU_CHAIN_BASE_URL=http://localhost:8081

# File Storage
UPLOAD_PATH=/var/uploads/openedu-core
MAX_UPLOAD_SIZE=10485760

# Email (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# External APIs
PINATA_API_KEY=your_pinata_api_key
PINATA_SECRET_KEY=your_pinata_secret_key
```

### 2. Blockchain Service Configuration
```bash
sudo nano /etc/openedu/blockchain/.env
```

```bash
# Server Configuration
PORT=8081
GIN_MODE=release
LOG_LEVEL=info

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=openedu_blockchain
DB_USER=openedu_blockchain
DB_PASSWORD=secure_password_blockchain
DB_SSL_MODE=disable

# RabbitMQ
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_PREFIX=openedu_

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# Network Configuration - BASE
BASE_TESTNET_RPC_URL=https://sepolia.base.org
BASE_MAINNET_RPC_URL=https://mainnet.base.org
BASE_NFT_CONTRACT_ADDRESS=0x1234567890abcdef1234567890abcdef12345678

# Network Configuration - NEAR
NEAR_TESTNET_RPC_URL=https://rpc.testnet.near.org
NEAR_MAINNET_RPC_URL=https://rpc.mainnet.near.org
NEAR_NFT_CONTRACT_ID=certificates.openedu.testnet

# Private Keys (KMS Encrypted)
PLATFORM_PRIVATE_KEY_TESTNET=encrypted_private_key_testnet
PLATFORM_PRIVATE_KEY_MAINNET=encrypted_private_key_mainnet

# Gas Configuration
DEFAULT_GAS_LIMIT=21000
DEFAULT_GAS_PRICE=***********
MAX_GAS_PRICE=************

# Coinbase Paymaster (Account Abstraction)
COINBASE_PAYMASTER_URL=https://api.developer.coinbase.com/rpc/v1/base-sepolia
COINBASE_API_KEY=your_coinbase_api_key

# Security
ENCRYPTION_KEY=your_32_byte_encryption_key_here
KMS_KEY_ID=your_kms_key_id
```

### 3. Nginx Configuration
```bash
sudo nano /etc/nginx/sites-available/openedu
```

```nginx
server {
    listen 80;
    server_name api.openedu.com;

    # Core API
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health checks
    location /health {
        proxy_pass http://localhost:8080/health;
    }

    # Blockchain health (internal only)
    location /blockchain/health {
        proxy_pass http://localhost:8081/health;
        allow 127.0.0.1;
        deny all;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/openedu /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🏥 Health Checks

### 1. Service Health Endpoints
```bash
# Core service health
curl http://localhost:8080/health

# Blockchain service health  
curl http://localhost:8081/health

# Expected response
{
  "status": "ok",
  "timestamp": "2025-06-20T10:30:00Z",
  "version": "1.0.0",
  "database": "connected",
  "rabbitmq": "connected"
}
```

### 2. Database Connectivity
```bash
# Test core database
psql -U openedu_core -d openedu_core -c "SELECT 1;"

# Test blockchain database
psql -U openedu_blockchain -d openedu_blockchain -c "SELECT 1;"
```

### 3. Queue Connectivity
```bash
# Check RabbitMQ status
sudo rabbitmqctl status

# List queues
sudo rabbitmqctl list_queues

# Check queue consumers
sudo rabbitmqctl list_consumers
```

## 📊 Monitoring

### 1. Log Monitoring
```bash
# Real-time log monitoring
tail -f /var/log/openedu-core/app.log
tail -f /var/log/openedu-blockchain/app.log

# Error monitoring
tail -f /var/log/openedu-*/error.log

# System logs
journalctl -u openedu-core -f
journalctl -u openedu-blockchain -f
```

### 2. Performance Monitoring
```bash
# System resources
htop
iotop
nethogs

# Database performance
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# Queue performance
sudo rabbitmqctl list_queues name messages consumers
```

### 3. Automated Monitoring Script
```bash
sudo nano /opt/openedu/monitor.sh
```

```bash
#!/bin/bash

# Health check script
check_service() {
    local service=$1
    local url=$2
    
    if curl -f -s $url > /dev/null; then
        echo "✅ $service is healthy"
    else
        echo "❌ $service is down"
        sudo systemctl restart $service
    fi
}

# Check services
check_service "openedu-core" "http://localhost:8080/health"
check_service "openedu-blockchain" "http://localhost:8081/health"

# Check queue lengths
queue_count=$(sudo rabbitmqctl list_queues -q | awk '{sum += $2} END {print sum}')
if [ "$queue_count" -gt 1000 ]; then
    echo "⚠️  High queue count: $queue_count"
fi

# Check disk space
disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$disk_usage" -gt 80 ]; then
    echo "⚠️  High disk usage: $disk_usage%"
fi
```

```bash
chmod +x /opt/openedu/monitor.sh

# Add to crontab
echo "*/5 * * * * /opt/openedu/monitor.sh" | sudo crontab -
```

## 🔄 Deployment Updates

### 1. Zero-Downtime Deployment
```bash
# 1. Build new version
cd /opt/openedu/core
git pull origin main
go build -o openedu-core-new main.go

# 2. Test new binary
./openedu-core-new --version

# 3. Replace binary
sudo systemctl stop openedu-core
mv openedu-core openedu-core-backup
mv openedu-core-new openedu-core
sudo systemctl start openedu-core

# 4. Verify deployment
curl http://localhost:8080/health

# 5. Rollback if needed
# sudo systemctl stop openedu-core
# mv openedu-core-backup openedu-core
# sudo systemctl start openedu-core
```

### 2. Database Migrations
```bash
# Backup before migration
pg_dump -U openedu_core openedu_core > backup_$(date +%Y%m%d_%H%M%S).sql

# Run migration
cd /opt/openedu/core
go run main.go migrate

# Verify migration
psql -U openedu_core -d openedu_core -c "\dt"
```

---

**📝 Important Notes**:
- Always backup databases before deployments
- Test in staging environment first
- Monitor logs during deployment
- Have rollback plan ready

**🔗 Related Documentation**:
- [Architecture Overview](./BLOCKCHAIN_ARCHITECTURE.md)
- [Troubleshooting Guide](./TROUBLESHOOTING_GUIDE.md)
- [API Reference](./API_MESSAGE_REFERENCE.md)
