# OpenEdu Blockchain API & Message Reference

## 📋 Table of Contents
1. [Core API Endpoints](#core-api-endpoints)
2. [Message Queue Formats](#message-queue-formats)
3. [Error Codes](#error-codes)
4. [Data Models](#data-models)

## 🌐 Core API Endpoints

### 1. 🎯 NFT Minting

#### Check NFT Mint Settings
```http
GET /api/v1/courses/{course_id}/nft-mint-settings
```

**Response:**
```json
{
  "data": {
    "mint_nft_enabled": true,
    "gas_fee_payer_in_setting": "creator",
    "actual_gas_fee_payer": "paymaster",
    "estimated_fee": "0.015",
    "sponsor_balance": "0.11",
    "network": "base"
  }
}
```

#### Mint NFT Certificate
```http
POST /api/v1/courses/{course_id}/mint-nft
```

**Request:**
```json
{
  "network": "base"
}
```

**Response:**
```json
{
  "data": {
    "transaction_id": "tx_123",
    "status": "pending",
    "estimated_confirmation_time": "2-5 minutes"
  }
}
```

### 2. 💰 Sponsor Wallet Management

#### Create Sponsor Wallet
```http
POST /api/v1/sponsor-wallets
```

**Request:**
```json
{
  "amount": "0.1",
  "description": "Gas fee sponsoring",
  "network": "base"
}
```

**Response:**
```json
{
  "data": {
    "sponsor_wallet_id": "wallet_123",
    "transaction_id": "tx_456",
    "status": "pending",
    "message": "Sponsor wallet creation request submitted successfully"
  }
}
```

#### Get Sponsor Wallet
```http
GET /api/v1/sponsor-wallets
```

**Response:**
```json
{
  "data": {
    "sponsor_wallet_id": "wallet_123",
    "wallet_address": "0x1234...",
    "network": "base",
    "balance": "0.11",
    "token": "ETH",
    "status": "active",
    "created_at": "2025-06-20T16:30:00Z",
    "last_updated": "2025-06-20T16:35:00Z"
  }
}
```

### 3. 🏦 Wallet Operations

#### Get User Wallets
```http
GET /api/v1/users/me/wallets
```

**Response:**
```json
{
  "data": [
    {
      "id": "wallet_123",
      "currency": "ETH",
      "network": "base",
      "address": "0x1234...",
      "balance": "0.5",
      "status": "active"
    }
  ]
}
```

## 📨 Message Queue Formats

### 1. 🎯 NFT Minting Messages

#### Core → Blockchain: Mint NFT
**Queue:** `transaction_create_queue`

```json
{
  "type": "mint_nft",
  "data": {
    "receiver_wallet_id": "wallet_123",
    "course_cuid": "course_456",
    "gas_fee_payer": "creator",
    "network": "base",
    "is_mainnet": false,
    "metadata": {
      "name": "Course Completion Certificate",
      "description": "Certificate for completing Advanced Blockchain Course",
      "image": "https://ipfs.io/ipfs/QmHash...",
      "attributes": [
        {
          "trait_type": "Course",
          "value": "Advanced Blockchain"
        },
        {
          "trait_type": "Completion Date",
          "value": "2025-06-20"
        }
      ]
    }
  }
}
```

#### Blockchain → Core: Transaction Sync
**Queue:** `transaction_sync_queue`

```json
{
  "id": "blockchain_tx_123",
  "core_tx_id": "core_tx_456",
  "status": "success",
  "blockchain_tx_id": "0xabcdef...",
  "gas_used": "21000",
  "gas_price": "20000000000",
  "block_number": 12345678,
  "confirmation_count": 12
}
```

### 2. 💰 Wallet Messages

#### Core → Blockchain: Create Wallet
**Queue:** `wallet_create_queue`

```json
{
  "user_id": "user_123",
  "currency": "ETH",
  "network": "base",
  "wallet_type": "crypto"
}
```

#### Blockchain → Core: Wallet Sync
**Queue:** `wallet_sync_queue`

```json
{
  "core_wallet_id": "wallet_123",
  "blockchain_wallet_id": "blockchain_wallet_456",
  "address": "0x1234567890abcdef...",
  "network": "base",
  "status": "active"
}
```

### 3. 🏦 Sponsor Wallet Messages

#### Core → Blockchain: Create Sponsor Wallet
**Queue:** `sponsor_wallet_create_queue`

```json
{
  "wallet_id": "wallet_123",
  "core_tx_id": "tx_456",
  "sponsor_id": "user_789",
  "sponsor_name": "John Doe",
  "description": "Gas fee sponsoring for students",
  "amount": "0.1",
  "token": "ETH",
  "network": "base",
  "is_mainnet": false
}
```

### 4. 🚀 Launchpad Messages

#### RPC Query: Get Pool Info
**Queue:** `launchpad_rpc_query`

```json
{
  "type": "get_pool_info",
  "data": {
    "pool_id": "pool_123",
    "network": "base",
    "is_mainnet": false
  }
}
```

**Response:**
```json
{
  "pool_id": "pool_123",
  "total_raised": "1000.5",
  "target_amount": "5000.0",
  "participants_count": 25,
  "status": "active",
  "end_time": "2025-07-20T00:00:00Z"
}
```

## ❌ Error Codes

### Common Error Codes

| Code | Constant | Description |
|------|----------|-------------|
| 400 | `INVALID_PARAMS` | Invalid request parameters |
| 403 | `FORBIDDEN` | User doesn't have permission |
| 404 | `WalletNotFound` | Wallet not found |
| 404 | `TransactionNotFound` | Transaction not found |
| 400 | `WalletNotSynced` | Wallet not synced with blockchain |
| 500 | `WalletCreateFailed` | Failed to create wallet |
| 500 | `TransactionCreateFailed` | Failed to create transaction |
| 500 | `NFTMintFailed` | Failed to mint NFT |

### Blockchain-Specific Errors

| Code | Description | Solution |
|------|-------------|----------|
| `InsufficientBalance` | Not enough balance for transaction | Add funds to wallet |
| `GasEstimationFailed` | Cannot estimate gas fee | Check network status |
| `NetworkUnavailable` | Blockchain network unavailable | Retry later |
| `ContractCallFailed` | Smart contract call failed | Check contract status |
| `InvalidSignature` | Transaction signature invalid | Re-sign transaction |

## 📊 Data Models

### Transaction Model
```go
type Transaction struct {
    ID              string                 `json:"id"`
    UserID          string                 `json:"user_id"`
    WalletID        string                 `json:"wallet_id"`
    Type            TransactionType        `json:"type"`
    Status          TransactionStatus      `json:"status"`
    Amount          decimal.Decimal        `json:"amount"`
    Currency        Currency               `json:"currency"`
    Network         BlockchainNetwork      `json:"network"`
    BlockchainTxID  string                 `json:"blockchain_tx_id"`
    GasUsed         string                 `json:"gas_used"`
    GasPrice        string                 `json:"gas_price"`
    CreatedAt       int64                  `json:"created_at"`
    UpdatedAt       int64                  `json:"updated_at"`
}
```

### Wallet Model
```go
type Wallet struct {
    ID                  string            `json:"id"`
    UserID              string            `json:"user_id"`
    Currency            Currency          `json:"currency"`
    Network             BlockchainNetwork `json:"network"`
    Address             string            `json:"address"`
    BlockchainWalletID  string            `json:"blockchain_wallet_id"`
    Balance             decimal.Decimal   `json:"balance"`
    Status              WalletStatus      `json:"status"`
    CreatedAt           int64             `json:"created_at"`
    UpdatedAt           int64             `json:"updated_at"`
}
```

### Sponsor Wallet Model
```go
type SponsorWallet struct {
    ID          string            `json:"id"`
    SponsorID   string            `json:"sponsor_id"`
    SponsorName string            `json:"sponsor_name"`
    WalletID    string            `json:"wallet_id"`
    Network     BlockchainNetwork `json:"network"`
    Balance     decimal.Decimal   `json:"balance"`
    Status      string            `json:"status"`
    Address     string            `json:"address"`
    CreatedAt   string            `json:"created_at"`
    UpdatedAt   string            `json:"updated_at"`
}
```

### NFT Metadata Model
```go
type NFTMetadata struct {
    Name        string      `json:"name"`
    Description string      `json:"description"`
    Image       string      `json:"image"`
    Attributes  []Attribute `json:"attributes"`
}

type Attribute struct {
    TraitType string `json:"trait_type"`
    Value     string `json:"value"`
}
```

## 🔧 Configuration Examples

### RabbitMQ Queue Configuration
```go
// Queue names with prefix
const (
    WalletCreateQueue     = "wallet_create_queue"
    TransactionCreateQueue = "transaction_create_queue"
    SponsorWalletCreateQueue = "sponsor_wallet_create_queue"
    LaunchpadRPCQuery     = "launchpad_rpc_query"
    WalletRPCQuery        = "wallet_rpc_query"
)

// Worker pool sizes
const (
    WalletCreateWorkerPoolSize     = 10
    TransactionCreateWorkerPoolSize = 20
    SponsorWalletCreateWorkerPoolSize = 5
)
```

### Network Configuration
```go
// Supported networks
const (
    BlockchainNetworkBASE = "base"
    BlockchainNetworkNEAR = "near"
    BlockchainNetworkAVAIL = "avail"
)

// Supported currencies
const (
    CryptoCurrencyETH  = "ETH"
    CryptoCurrencyNEAR = "NEAR"
    CryptoCurrencyUSDT = "USDT"
    CryptoCurrencyUSDC = "USDC"
)
```

---

**📝 Note**: Always validate message formats before processing. Use proper error handling for all queue operations.

**🔗 Related Files**:
- `openedu-core/dto/` - Data Transfer Objects
- `openedu-blockchain/dto/` - Blockchain DTOs
- `models/constant.go` - System constants
