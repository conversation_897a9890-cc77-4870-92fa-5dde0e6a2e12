# OpenEdu Blockchain Architecture Documentation

## 📋 Table of Contents
1. [Overview](#overview)
2. [Service Architecture](#service-architecture)
3. [Communication Flow](#communication-flow)
4. [Blockchain Features](#blockchain-features)
5. [Development Guide](#development-guide)
6. [Troubleshooting](#troubleshooting)

## 🎯 Overview

OpenEdu blockchain system consists of two main services:
- **openedu-core**: Main business logic, API endpoints, user management
- **openedu-blockchain**: Blockchain operations, wallet management, transaction processing

### Key Design Principles
- **Microservice Architecture**: Separated concerns between business logic and blockchain
- **Async Communication**: RabbitMQ message queues for service-to-service communication
- **Multi-Network Support**: NEAR, BASE (Ethereum L2), AVAIL
- **Strategy Pattern**: Easy to add new blockchain networks
- **Security First**: Private key encryption, secure transaction handling

## 🏗️ Service Architecture

```
┌─────────────────┐    RabbitMQ     ┌──────────────────────┐
│   openedu-core  │ ◄──────────────► │ openedu-blockchain   │
│                 │                 │                      │
│ • API Endpoints │                 │ • Wallet Management  │
│ • Business Logic│                 │ • Transaction Proc.  │
│ • User Mgmt     │                 │ • NFT Minting        │
│ • Course Mgmt   │                 │ • Launchpad          │
└─────────────────┘                 └──────────────────────┘
         │                                       │
         ▼                                       ▼
┌─────────────────┐                 ┌──────────────────────┐
│   PostgreSQL    │                 │   PostgreSQL         │
│   (Core DB)     │                 │   (Blockchain DB)    │
└─────────────────┘                 └──────────────────────┘
```

### Database Separation
- **Core DB**: Users, courses, organizations, business data
- **Blockchain DB**: Wallets, transactions, sponsor wallets, blockchain-specific data

## 🔄 Communication Flow

### 1. Message Queue Pattern

**Core → Blockchain:**
```go
// openedu-core/pkg/openedu_chain/
producer.Publish(queueName, message)
```

**Blockchain → Core:**
```go
// openedu-blockchain/pkg/openedu_core/
producer.Publish(queueName, syncMessage)
```

### 2. Queue Types

#### 📤 **Core → Blockchain Queues**
| Queue Name | Purpose | Handler |
|------------|---------|---------|
| `wallet_create_queue` | Create new wallets | `CreateWallet` |
| `sponsor_wallet_create_queue` | Create sponsor wallets | `CreateSponsorWallet` |
| `transaction_create_queue` | Process transactions | `CreateTransaction` |
| `launchpad_rpc_query` | Launchpad operations | `HandleLaunchpadRPCQuery` |
| `wallet_rpc_query` | Wallet queries | `HandleWalletRPCQuery` |

#### 📥 **Blockchain → Core Queues**
| Queue Name | Purpose | Handler |
|------------|---------|---------|
| `wallet_sync_queue` | Sync wallet data | `SyncWallet` |
| `transaction_sync_queue` | Sync transaction status | `SyncTransaction` |
| `wallet_retrieve_get_details_queue` | Wallet details | `HandleRetrieveWalletDetails` |

### 3. RPC vs Fire-and-Forget

**RPC Pattern** (Request-Response):
```go
// For queries that need immediate response
repliedMsg, err := producer.PublishRPC(queueName, message)
```

**Fire-and-Forget Pattern**:
```go
// For async operations
err := producer.Publish(queueName, message)
```

## 🚀 Blockchain Features

### 1. 🎯 NFT Minting System

**Supported Networks:**
- **NEAR**: Native NEAR NFT contracts
- **BASE**: Ethereum L2 with EIP-712 permits

**Gas Fee Payers:**
- `Platform`: OpenEdu pays gas fees
- `Learner`: Student pays gas fees  
- `Creator`: Course creator pays via sponsor wallet
- `Paymaster`: Coinbase Paymaster (Account Abstraction)

**Flow:**
```
1. User completes course
2. Core validates completion
3. Core → Queue: MintNFT request
4. Blockchain processes minting
5. Blockchain → Queue: Transaction sync
6. Core updates certificate status
```

### 2. 💰 Wallet Management

**Wallet Types:**
- **Fiat Wallets**: VND, USD for payments
- **Crypto Wallets**: ETH, NEAR, USDT, USDC, OpenEdu token
- **Point Wallets**: Reward points system
- **Sponsor Wallets**: Gas fee sponsoring (BASE only)

**Multi-Network Support:**
```go
// Strategy pattern for different networks
type WalletStrategy interface {
    CreateWallet(req *CreateWalletRequest) (*CreateWalletResponse, error)
    GetBalance(address string) (decimal.Decimal, error)
}
```

### 3. 🏦 Sponsor Wallet System

**Purpose**: Allow course creators to sponsor gas fees for their students

**Features:**
- **BASE network only**
- **ETH deposits** for gas fee sponsoring
- **Balance tracking** and management
- **Automatic fallback** to paymaster if insufficient balance

**Flow:**
```
1. Creator creates sponsor wallet
2. Creator deposits ETH
3. Student mints NFT
4. Gas fee deducted from sponsor wallet
5. If insufficient → fallback to paymaster
```

### 4. 🚀 Launchpad System

**Purpose**: Crowdfunding platform for educational projects

**Features:**
- **Pool creation** with funding goals
- **Milestone-based** fund release
- **Voting mechanisms** for milestone approval
- **Refund system** if goals not met
- **Multi-network support**

## 🛠️ Development Guide

### Adding New Blockchain Network

1. **Create Strategy Implementation:**
```go
// openedu-blockchain/services/nft/strategies/new_network.go
type NewNetworkMintNftService struct{}

func (s *NewNetworkMintNftService) MintNFT(account Account, req *MintNftRequest) (*MintNftResponse, error) {
    // Implementation
}
```

2. **Register Strategy:**
```go
// openedu-blockchain/services/services.go
nftSvc.RegisterStrategy(models.NetworkNEW, &strategies.NewNetworkMintNftService{})
```

3. **Add Network Constants:**
```go
// models/constant.go
NetworkNEW BlockchainNetwork = "new_network"
```

### Adding New Transaction Type

1. **Define DTO:**
```go
// dto/transaction.go
type NewTransactionRequest struct {
    // Fields
}
```

2. **Implement Service Method:**
```go
// services/transaction.go
func (s *TransactionService) NewTransaction(req *dto.NewTransactionRequest) (*models.Transaction, *e.AppError) {
    // Implementation
}
```

3. **Add Queue Handler:**
```go
// queues/handlers/transaction.go
// Handle new transaction type
```

### Environment Configuration

**Core Service (.env):**
```bash
# RabbitMQ
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_PREFIX=openedu_

# Blockchain Service
OPENEDU_CHAIN_MAINNET=false
```

**Blockchain Service (.env):**
```bash

# Private Keys (KMS Encrypted)
PLATFORM_PRIVATE_KEY_TESTNET=encrypted_key
PLATFORM_PRIVATE_KEY_MAINNET=encrypted_key
```

## 🔧 Troubleshooting

### Common Issues

1. **Queue Connection Failed**
```bash
# Check RabbitMQ status
sudo systemctl status rabbitmq-server

# Check queue consumers
rabbitmqctl list_consumers
```

2. **Transaction Stuck in Pending**
```sql
-- Check transaction status
SELECT * FROM transactions WHERE status = 'pending' ORDER BY created_at DESC;

-- Check blockchain confirmations
SELECT * FROM transactions WHERE blockchain_tx_id IS NOT NULL;
```

3. **Sponsor Wallet Balance Mismatch**
```go
sponsorBalance, err := services.Wallet.GetGasSponsorBalance(&dto.GetWalletGasSponsorBalanceRequest{
    WalletID: walletID,
    CourseCuid: userID,
    IsMainnet: false,
})
```

### Monitoring

**Key Metrics to Monitor:**
- Queue message count and processing rate
- Transaction success/failure rates
- Wallet creation and sync rates
- Gas fee consumption
- Network RPC response times

**Log Locations:**
- Core: `/var/log/openedu-core/`
- Blockchain: `/var/log/openedu-blockchain/`
- RabbitMQ: `/var/log/rabbitmq/`

### Performance Optimization

1. **Queue Workers**: Adjust worker pool sizes based on load
2. **Database Indexing**: Ensure proper indexes on frequently queried fields
3. **RPC Caching**: Cache blockchain RPC responses where appropriate
4. **Batch Processing**: Use batch operations for multiple transactions

