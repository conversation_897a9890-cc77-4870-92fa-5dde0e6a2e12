# OpenEdu Blockchain Troubleshooting Guide

## 📋 Table of Contents
1. [Common Issues](#common-issues)
2. [Queue Problems](#queue-problems)
3. [Transaction Issues](#transaction-issues)
4. [Wallet Problems](#wallet-problems)
5. [Network Issues](#network-issues)
6. [Debugging Tools](#debugging-tools)

## 🚨 Common Issues

### 1. 🔄 Service Communication Problems

#### Issue: "Queue connection failed"
**Symptoms:**
- Services can't communicate
- Messages not being processed
- Connection timeout errors

**Diagnosis:**
```bash
# Check RabbitMQ status
sudo systemctl status rabbitmq-server

# Check if queues exist
rabbitmqctl list_queues

# Check queue consumers
rabbitmqctl list_consumers
```

**Solutions:**
```bash
# Restart RabbitMQ
sudo systemctl restart rabbitmq-server

# Check RabbitMQ logs
sudo tail -f /var/log/rabbitmq/<EMAIL>

# Verify connection string in .env
RABBITMQ_URL=amqp://user:password@localhost:5672
```

#### Issue: "Message processing stuck"
**Symptoms:**
- Messages accumulating in queues
- No processing happening
- Workers not responding

**Diagnosis:**
```bash
# Check queue message count
rabbitmqctl list_queues name messages

# Check worker processes
ps aux | grep openedu

# Check service logs
tail -f /var/log/openedu-*/app.log
```

**Solutions:**
```bash
# Restart blockchain service
sudo systemctl restart openedu-blockchain

# Restart core service  
sudo systemctl restart openedu-core

# Purge stuck messages (CAUTION!)
rabbitmqctl purge_queue queue_name
```

### 2. 🎯 NFT Minting Issues

#### Issue: "Gas fee payer mismatch"
**Symptoms:**
- Setting shows "creator" but actual is "paymaster"
- Sponsor wallet has balance but not being used
- Unexpected gas fee charges

**Diagnosis:**
```sql
-- Check sponsor wallet balance
SELECT * FROM sponsor_wallets WHERE sponsor_id = 'user_id' AND network = 'base';

-- Check transaction gas fee payer
SELECT gas_fee_payer, actual_gas_fee_payer FROM transactions WHERE id = 'tx_id';
```

**Root Cause:**
```go
// In evm.go - fallback logic triggers incorrectly
if sponsorWallet == nil || sponsorWallet.Balance.IsZero() {
    // Falls back to paymaster even when balance exists
    return s.mintNFTWithPaymaster(account, req, result)
}
```

**Solutions:**
1. **Check sponsor wallet query logic**
2. **Verify balance calculation**
3. **Fix fallback conditions**

#### Issue: "NFT minting failed"
**Symptoms:**
- Transaction stuck in pending
- Contract call reverted
- Gas estimation failed

**Diagnosis:**
```bash
# Check blockchain network status
curl -X POST https://sepolia.base.org \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}'

# Check contract address
echo $BASE_NFT_CONTRACT_ADDRESS

# Check transaction on explorer
# https://sepolia.basescan.org/tx/0x...
```

**Solutions:**
1. **Verify contract address is correct**
2. **Check network RPC endpoint**
3. **Increase gas limit**
4. **Retry with higher gas price**

### 3. 💰 Wallet Sync Issues

#### Issue: "Wallet not synced with blockchain"
**Symptoms:**
- `blockchain_wallet_id` is empty
- Cannot perform transactions
- Wallet shows as "pending"

**Diagnosis:**
```sql
-- Check wallet sync status
SELECT id, user_id, blockchain_wallet_id, status 
FROM wallets 
WHERE blockchain_wallet_id = '' OR blockchain_wallet_id IS NULL;

-- Check wallet creation messages
SELECT * FROM queue_messages WHERE queue_name = 'wallet_create_queue';
```

**Solutions:**
```bash
# Manually trigger wallet sync
curl -X POST http://localhost:8080/api/v1/wallets/sync \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"wallet_id": "wallet_123"}'

# Check blockchain service logs
tail -f /var/log/openedu-blockchain/app.log | grep "wallet_create"
```

#### Issue: "Balance mismatch"
**Symptoms:**
- Wallet balance doesn't match blockchain
- Transactions show wrong amounts
- Balance not updating

**Diagnosis:**
```bash
# Check on-chain balance
curl -X POST https://sepolia.base.org \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc":"2.0",
    "method":"eth_getBalance",
    "params":["0x...","latest"],
    "id":1
  }'

# Compare with database
SELECT address, balance FROM wallets WHERE id = 'wallet_123';
```

**Solutions:**
1. **Trigger balance refresh**
2. **Check for pending transactions**
3. **Verify network synchronization**

## 🔧 Queue Problems

### Queue Monitoring Commands

```bash
# List all queues with message counts
rabbitmqctl list_queues name messages consumers

# Check specific queue details
rabbitmqctl list_queue_bindings

# Monitor queue activity
rabbitmqctl list_channels

# Check exchange bindings
rabbitmqctl list_exchanges
```

### Queue Recovery Procedures

```bash
# 1. Stop consumers
sudo systemctl stop openedu-blockchain
sudo systemctl stop openedu-core

# 2. Check queue status
rabbitmqctl list_queues

# 3. Backup messages (if needed)
rabbitmqctl export_definitions /tmp/rabbitmq_backup.json

# 4. Restart services
sudo systemctl start openedu-blockchain
sudo systemctl start openedu-core

# 5. Verify processing resumed
rabbitmqctl list_queues name messages
```

## 🔍 Debugging Tools

### 1. Database Queries

#### Check Transaction Status
```sql
-- Recent transactions
SELECT id, type, status, network, created_at 
FROM transactions 
ORDER BY created_at DESC 
LIMIT 10;

-- Failed transactions
SELECT id, type, status, error_message, created_at
FROM transactions 
WHERE status = 'failed'
ORDER BY created_at DESC;

-- Pending transactions (older than 10 minutes)
SELECT id, type, status, created_at
FROM transactions 
WHERE status = 'pending' 
AND created_at < EXTRACT(EPOCH FROM NOW() - INTERVAL '10 minutes') * 1000;
```

#### Check Wallet Status
```sql
-- Unsynced wallets
SELECT id, user_id, currency, network, status
FROM wallets 
WHERE blockchain_wallet_id = '' OR blockchain_wallet_id IS NULL;

-- Sponsor wallet balances
SELECT sponsor_id, network, balance, status
FROM sponsor_wallets
ORDER BY created_at DESC;
```

### 2. API Testing

#### Test NFT Minting
```bash
# Check mint settings
curl -X GET "http://localhost:8080/api/v1/courses/course_123/nft-mint-settings" \
  -H "Authorization: Bearer $TOKEN"

# Mint NFT
curl -X POST "http://localhost:8080/api/v1/courses/course_123/mint-nft" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"network": "base"}'
```

#### Test Sponsor Wallet
```bash
# Get sponsor wallet
curl -X GET "http://localhost:8080/api/v1/sponsor-wallets" \
  -H "Authorization: Bearer $TOKEN"

# Create sponsor wallet
curl -X POST "http://localhost:8080/api/v1/sponsor-wallets" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "0.1",
    "description": "Test sponsor wallet",
    "network": "base"
  }'
```

### 3. Log Analysis

#### Key Log Patterns
```bash
# NFT minting logs
tail -f /var/log/openedu-blockchain/app.log | grep "MintNFT"

# Transaction processing
tail -f /var/log/openedu-blockchain/app.log | grep "CreateTransaction"

# Queue processing
tail -f /var/log/openedu-*/app.log | grep "worker ID"

# Error patterns
tail -f /var/log/openedu-*/app.log | grep -i "error\|failed\|exception"
```

#### Log Levels
```bash
# Set debug level for detailed logs
export LOG_LEVEL=debug

# Filter by specific components
tail -f /var/log/openedu-blockchain/app.log | grep "handlers/transaction"
```

### 4. Network Testing

#### Test RPC Endpoints
```bash
# BASE testnet
curl -X POST https://sepolia.base.org \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}'

# NEAR testnet  
curl -X POST https://rpc.testnet.near.org \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"status","params":[],"id":1}'
```

#### Check Contract Status
```bash
# Verify contract exists
curl -X POST https://sepolia.base.org \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc":"2.0",
    "method":"eth_getCode",
    "params":["'$BASE_NFT_CONTRACT_ADDRESS'","latest"],
    "id":1
  }'
```

## 🚨 Emergency Procedures

### Service Recovery
```bash
# 1. Stop all services
sudo systemctl stop openedu-blockchain openedu-core

# 2. Check system resources
df -h
free -m
ps aux | head -20

# 3. Clear temporary files
rm -rf /tmp/openedu-*

# 4. Restart dependencies
sudo systemctl restart rabbitmq-server postgresql

# 5. Start services
sudo systemctl start openedu-blockchain
sudo systemctl start openedu-core

# 6. Verify health
curl http://localhost:8080/health
curl http://localhost:8081/health
```

### Data Recovery
```bash
# Database backup
pg_dump openedu_core > /backup/core_$(date +%Y%m%d).sql
pg_dump openedu_blockchain > /backup/blockchain_$(date +%Y%m%d).sql

# Queue backup
rabbitmqctl export_definitions /backup/rabbitmq_$(date +%Y%m%d).json
```

## 📞 Escalation Contacts

### When to Escalate
- **Critical**: Service completely down > 15 minutes
- **High**: Transaction processing stopped > 30 minutes  
- **Medium**: Individual features not working > 1 hour
- **Low**: Performance degradation or minor bugs

### Contact Information
- **DevOps Team**: For infrastructure issues
- **Backend Team**: For service logic problems
- **Blockchain Team**: For smart contract issues
- **Product Team**: For business logic questions

---

**📝 Remember**: Always check logs first, then database state, then network connectivity. Most issues are related to configuration or network problems.

**🔗 Related Documentation**:
- [Architecture Overview](./BLOCKCHAIN_ARCHITECTURE.md)
- [API Reference](./API_MESSAGE_REFERENCE.md)
- [Deployment Guide](./DEPLOYMENT.md)
